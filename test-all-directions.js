// Comprehensive test script to verify all direction overview pages
const fs = require('fs');
const path = require('path');

// Paths to the direction tracker components
const northPath = path.join(
  __dirname,
  'src/features/FinancialCompass/components/North/NorthDirectionTracker.tsx'
);
const eastPath = path.join(
  __dirname,
  'src/features/FinancialCompass/components/East/EastDirectionTracker.tsx'
);
const southPath = path.join(
  __dirname,
  'src/features/FinancialCompass/components/South/SouthDirectionTracker.tsx'
);
const westPath = path.join(
  __dirname,
  'src/features/FinancialCompass/components/West/WestDirectionTracker.tsx'
);

// Function to test a component
function testComponent(componentPath, directionName, dashboardComment, specialElements) {
  try {
    const componentContent = fs.readFileSync(componentPath, 'utf8');

    // Check if the component contains our new UI elements
    const containsDashboardContainer = componentContent.includes('DashboardContainer');
    const containsDashboardComment = componentContent.includes(dashboardComment);
    const containsJourneySection = componentContent.includes('JourneySection');
    const containsActionSection = componentContent.includes('ActionSection');

    // Check for special elements
    const specialElementsPresent = specialElements.map((element) => {
      return {
        name: element,
        present: componentContent.includes(element),
      };
    });

    console.log(`\n${directionName} Direction Component verification results:`);
    console.log('- Contains DashboardContainer:', containsDashboardContainer);
    console.log(`- Contains ${directionName} Dashboard comment:`, containsDashboardComment);
    console.log('- Contains JourneySection:', containsJourneySection);
    console.log('- Contains ActionSection:', containsActionSection);

    specialElementsPresent.forEach((element) => {
      console.log(`- Contains ${element.name}:`, element.present);
    });

    const allElementsPresent =
      containsDashboardContainer &&
      containsDashboardComment &&
      containsJourneySection &&
      containsActionSection &&
      specialElementsPresent.every((element) => element.present);

    if (allElementsPresent) {
      console.log(
        `\nSUCCESS: The ${directionName}DirectionTracker component has been successfully updated with the new UI elements.`
      );
      return true;
    } else {
      console.log(
        `\nFAILURE: Some expected UI elements are missing from the ${directionName}DirectionTracker component.`
      );
      return false;
    }
  } catch (error) {
    console.error(`Error reading ${directionName} component file:`, error);
    return false;
  }
}

// Test all components
const northResult = testComponent(northPath, 'North', 'Financial Dashboard', [
  'DashboardCard',
  'JourneyStep',
  'ActionCard',
]);

const eastResult = testComponent(eastPath, 'East', 'Retirement Dashboard', [
  'IncomeSourcesChart',
  'ReadinessScoreValue',
  'MetricBreakdown',
]);

const southResult = testComponent(southPath, 'South', 'Protection Dashboard', [
  'InsuranceTypesList',
  'ProtectionScoreValue',
  'CoverageInsight',
]);

const westResult = testComponent(westPath, 'West', 'Legacy Dashboard', [
  'DocumentStatusList',
  'TaxStrategyList',
  'CharitableInsight',
]);

// Overall result
console.log('\n=== OVERALL RESULTS ===');
if (northResult && eastResult && southResult && westResult) {
  console.log(
    'SUCCESS: All direction overview pages have been successfully updated with the new UI elements.'
  );
} else {
  console.log('FAILURE: Some direction overview pages are missing expected UI elements.');

  if (!northResult) console.log('- North Direction needs attention');
  if (!eastResult) console.log('- East Direction needs attention');
  if (!southResult) console.log('- South Direction needs attention');
  if (!westResult) console.log('- West Direction needs attention');
}
