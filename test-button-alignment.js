// Script to check for button alignment in all direction tracker components
const fs = require('fs');
const path = require('path');

// Paths to the direction tracker components
const northPath = path.join(
  __dirname,
  'src/features/FinancialCompass/components/North/NorthDirectionTracker.tsx'
);
const eastPath = path.join(
  __dirname,
  'src/features/FinancialCompass/components/East/EastDirectionTracker.tsx'
);
const southPath = path.join(
  __dirname,
  'src/features/FinancialCompass/components/South/SouthDirectionTracker.tsx'
);
const westPath = path.join(
  __dirname,
  'src/features/FinancialCompass/components/West/WestDirectionTracker.tsx'
);

// Function to check for button alignment
function checkButtonAlignment(filePath, fileName) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');

    // Check for the required components for proper button alignment
    const hasActionCard = content.includes('const ActionCard = styled.div');
    const hasFlexDirection = content.includes('flex-direction: column');
    const hasHeight100 = content.includes('height: 100%');
    const hasActionDescription = content.includes('const ActionDescription = styled.p');
    const hasFlex1 = content.includes('flex: 1');
    const hasActionButtonContainer = content.includes('const ActionButtonContainer = styled.div');
    const hasJustifyContent = content.includes('justify-content: flex-end');
    const hasMarginTop = content.includes('margin-top: 16px');
    const hasButtonContainerUsage = content.includes('<ActionButtonContainer>');

    // Report results
    console.log(`\nChecking ${fileName}:`);
    console.log('- ActionCard with flex-direction: column:', hasActionCard && hasFlexDirection);
    console.log('- ActionCard with height: 100%:', hasHeight100);
    console.log('- ActionDescription with flex: 1:', hasActionDescription && hasFlex1);
    console.log(
      '- ActionButtonContainer with justify-content:',
      hasActionButtonContainer && hasJustifyContent
    );
    console.log('- ActionButtonContainer with margin-top:', hasMarginTop);
    console.log('- ActionButtonContainer usage in JSX:', hasButtonContainerUsage);

    const allAlignmentFeatures =
      hasActionCard &&
      hasFlexDirection &&
      hasHeight100 &&
      hasActionDescription &&
      hasFlex1 &&
      hasActionButtonContainer &&
      hasJustifyContent &&
      hasMarginTop &&
      hasButtonContainerUsage;

    if (allAlignmentFeatures) {
      console.log(`\nSUCCESS: The ${fileName} has proper button alignment configuration.`);
      return true;
    } else {
      console.log(`\nFAILURE: The ${fileName} is missing some button alignment features.`);
      return false;
    }
  } catch (error) {
    console.error(`Error reading ${fileName}:`, error);
    return false;
  }
}

// Check all components
const northResult = checkButtonAlignment(northPath, 'NorthDirectionTracker.tsx');
const eastResult = checkButtonAlignment(eastPath, 'EastDirectionTracker.tsx');
const southResult = checkButtonAlignment(southPath, 'SouthDirectionTracker.tsx');
const westResult = checkButtonAlignment(westPath, 'WestDirectionTracker.tsx');

// Overall result
console.log('\n=== OVERALL RESULTS ===');
if (northResult && eastResult && southResult && westResult) {
  console.log(
    'SUCCESS: All direction tracker components have proper button alignment configuration.'
  );
} else {
  console.log('FAILURE: Some direction tracker components are missing button alignment features.');

  if (!northResult) console.log('- North Direction needs attention');
  if (!eastResult) console.log('- East Direction needs attention');
  if (!southResult) console.log('- South Direction needs attention');
  if (!westResult) console.log('- West Direction needs attention');
}
