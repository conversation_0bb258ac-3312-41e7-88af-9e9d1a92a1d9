// Script to check for duplicate component declarations
const fs = require('fs');
const path = require('path');

// Paths to the direction tracker components
const northPath = path.join(
  __dirname,
  'src/features/FinancialCompass/components/North/NorthDirectionTracker.tsx'
);
const eastPath = path.join(
  __dirname,
  'src/features/FinancialCompass/components/East/EastDirectionTracker.tsx'
);
const southPath = path.join(
  __dirname,
  'src/features/FinancialCompass/components/South/SouthDirectionTracker.tsx'
);
const westPath = path.join(
  __dirname,
  'src/features/FinancialCompass/components/West/WestDirectionTracker.tsx'
);

// Function to check for duplicate component declarations
function checkDuplicates(filePath, fileName) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');

    // Extract all component declarations
    const componentDeclarations = [];
    const duplicates = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (line.startsWith('const ') && line.includes(' = styled.')) {
        const componentName = line.split('const ')[1].split(' =')[0].trim();

        if (componentDeclarations.includes(componentName)) {
          duplicates.push({
            component: componentName,
            lineNumber: i + 1,
          });
        } else {
          componentDeclarations.push(componentName);
        }
      }
    }

    // Report results
    console.log(`\nChecking ${fileName}:`);
    console.log(`- Total component declarations: ${componentDeclarations.length}`);

    if (duplicates.length > 0) {
      console.log(`- Found ${duplicates.length} duplicate component declarations:`);
      duplicates.forEach((dup) => {
        console.log(`  * Duplicate '${dup.component}' at line ${dup.lineNumber}`);
      });
    } else {
      console.log('- No duplicate component declarations found.');
    }

    return duplicates.length === 0;
  } catch (error) {
    console.error(`Error reading ${fileName}:`, error);
    return false;
  }
}

// Check all components
const northResult = checkDuplicates(northPath, 'NorthDirectionTracker.tsx');
const eastResult = checkDuplicates(eastPath, 'EastDirectionTracker.tsx');
const southResult = checkDuplicates(southPath, 'SouthDirectionTracker.tsx');
const westResult = checkDuplicates(westPath, 'WestDirectionTracker.tsx');

// Overall result
console.log('\n=== OVERALL RESULTS ===');
if (northResult && eastResult && southResult && westResult) {
  console.log(
    'SUCCESS: No duplicate component declarations found in any direction tracker component.'
  );
} else {
  console.log(
    'FAILURE: Duplicate component declarations found in one or more direction tracker components.'
  );

  if (!northResult) console.log('- North Direction needs attention');
  if (!eastResult) console.log('- East Direction needs attention');
  if (!southResult) console.log('- South Direction needs attention');
  if (!westResult) console.log('- West Direction needs attention');
}
