<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Income Details Preview</title>
    <style>
      body {
        font-family:
          -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell,
          'Open Sans', 'Helvetica Neue', sans-serif;
        background-color: #f5f5f5;
        margin: 0;
        padding: 20px;
      }
      .form-container {
        background-color: white;
        border-radius: 8px;
        padding: 24px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        max-width: 800px;
        margin: 0 auto;
      }
      .form-title {
        color: #333;
        margin-bottom: 16px;
        font-weight: 500;
      }
      .form-description {
        margin-bottom: 24px;
        color: #555;
      }
      .form-section {
        margin-bottom: 32px;
        padding-bottom: 24px;
        border-bottom: 1px solid #eee;
      }
      .section-title {
        color: #333;
        margin-bottom: 16px;
        font-weight: 500;
        font-size: 1.1rem;
      }
      .form-row {
        display: flex;
        gap: 16px;
        margin-bottom: 16px;
      }
      .form-group {
        flex: 1;
      }
      label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #444;
      }
      input,
      select {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background-color: white;
        color: #333;
        font-size: 16px;
      }
      .income-sources-list {
        display: flex;
        flex-direction: column;
        gap: 12px;
        margin-bottom: 24px;
      }
      .income-source-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        background-color: #f9f9f9;
        border-radius: 4px;
        border: 1px solid #eee;
      }
      .income-source-info {
        flex: 1;
      }
      .income-source-amount {
        font-weight: bold;
        color: #2e7d32;
      }
      .add-income-source {
        background-color: #f9f9f9;
        border-radius: 4px;
        padding: 16px;
        border: 1px dashed #ccc;
      }
      .add-button {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px;
        background-color: #2196f3;
        color: white;
        border: none;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s ease-in-out;
        width: 100%;
        margin-top: 16px;
      }
      .summary-container {
        background-color: #f9f9f9;
        border-radius: 4px;
        padding: 16px;
        margin-top: 16px;
      }
      .summary-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
      }
      .summary-label {
        font-weight: 500;
      }
      .summary-value {
        font-weight: bold;
      }
      .total-row {
        border-top: 1px solid #ddd;
        padding-top: 8px;
        margin-top: 8px;
        font-weight: bold;
      }
      .button-container {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-top: 24px;
      }
      .save-indicator {
        margin-right: 16px;
        color: #4caf50;
        display: flex;
        align-items: center;
      }
      .button {
        padding: 10px 20px;
        background-color: #2196f3;
        color: white;
        border: none;
        border-radius: 4px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s ease-in-out;
      }
      .required-fields-note {
        font-size: 14px;
        color: #f44336;
        margin-top: 16px;
        text-align: right;
        font-weight: 500;
      }
      .optional-fields-note {
        font-size: 14px;
        color: #757575;
        margin-top: 8px;
        font-style: italic;
      }
    </style>
  </head>
  <body>
    <div class="form-container">
      <h2 class="form-title">Income Details</h2>
      <p class="form-description">
        Please provide information about your income sources to help us understand your financial
        situation. This information will be used to personalize your financial compass journey.
      </p>

      <form>
        <div class="required-fields-note">
          * Required fields must be completed to save your data
        </div>

        <div class="form-section">
          <h3 class="section-title">Primary Income</h3>

          <div class="form-row">
            <div class="form-group">
              <label for="primaryIncomeSource">Primary Income Source *</label>
              <select id="primaryIncomeSource">
                <option value="">Select an option</option>
                <option value="salary" selected>Salary</option>
                <option value="self-employment">Self-Employment</option>
                <option value="business">Business</option>
                <option value="pension">Pension</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div class="form-group">
              <label for="primaryIncomeAmount">Amount *</label>
              <input type="text" id="primaryIncomeAmount" value="5000" />
            </div>

            <div class="form-group">
              <label for="primaryIncomeFrequency">Frequency *</label>
              <select id="primaryIncomeFrequency">
                <option value="monthly" selected>Monthly</option>
                <option value="bi-weekly">Bi-weekly</option>
                <option value="weekly">Weekly</option>
                <option value="annually">Annually</option>
              </select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="filingStatus">Tax Filing Status</label>
              <select id="filingStatus">
                <option value="">Select an option</option>
                <option value="single" selected>Single</option>
                <option value="married-joint">Married Filing Jointly</option>
                <option value="married-separate">Married Filing Separately</option>
                <option value="head-household">Head of Household</option>
              </select>
            </div>

            <div class="form-group">
              <label for="estimatedTaxRate">Estimated Tax Rate (%)</label>
              <input type="text" id="estimatedTaxRate" value="22" />
            </div>
          </div>
        </div>

        <div class="form-section">
          <h3 class="section-title">Additional Income Sources</h3>

          <div class="income-sources-list">
            <div class="income-source-item">
              <div class="income-source-info">
                <div class="income-source-type">Rental Income</div>
                <div class="income-source-details">$1,200 / Monthly</div>
              </div>
              <div class="income-source-amount">$14,400 / year</div>
              <button
                class="remove-button"
                style="background: none; border: none; color: #f44336; cursor: pointer"
              >
                ✕
              </button>
            </div>

            <div class="income-source-item">
              <div class="income-source-info">
                <div class="income-source-type">Dividends</div>
                <div class="income-source-details">$500 / Quarterly</div>
              </div>
              <div class="income-source-amount">$2,000 / year</div>
              <button
                class="remove-button"
                style="background: none; border: none; color: #f44336; cursor: pointer"
              >
                ✕
              </button>
            </div>
          </div>

          <div class="add-income-source">
            <h4 style="margin-top: 0">Add Income Source (Optional)</h4>
            <div class="optional-fields-note">
              You can proceed without adding additional income sources
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="incomeType">Income Type</label>
                <select id="incomeType">
                  <option value="">Select an option</option>
                  <option value="rental">Rental Income</option>
                  <option value="investment">Investment Income</option>
                  <option value="side-business">Side Business</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div class="form-group">
                <label for="incomeAmount">Amount</label>
                <input type="text" id="incomeAmount" placeholder="0.00" />
              </div>

              <div class="form-group">
                <label for="incomeFrequency">Frequency</label>
                <select id="incomeFrequency">
                  <option value="monthly">Monthly</option>
                  <option value="quarterly">Quarterly</option>
                  <option value="annually">Annually</option>
                  <option value="one-time">One-time</option>
                </select>
              </div>
            </div>

            <button class="add-button">+ Add Income Source</button>
          </div>
        </div>

        <div class="form-section">
          <h3 class="section-title">Income Summary</h3>

          <div class="summary-container">
            <div class="summary-row">
              <div class="summary-label">Primary Income (Annual)</div>
              <div class="summary-value">$60,000</div>
            </div>

            <div class="summary-row">
              <div class="summary-label">Additional Income (Annual)</div>
              <div class="summary-value">$16,400</div>
            </div>

            <div class="summary-row">
              <div class="summary-label">Estimated Tax (22%)</div>
              <div class="summary-value">-$16,808</div>
            </div>

            <div class="summary-row total-row">
              <div class="summary-label">Total Net Annual Income</div>
              <div class="summary-value">$59,592</div>
            </div>

            <div class="summary-row">
              <div class="summary-label">Average Monthly Income</div>
              <div class="summary-value">$4,966</div>
            </div>

            <div class="button-container">
              <div class="save-indicator">✓ Saved</div>
              <button class="button">Save and Continue</button>
            </div>

            <div class="required-fields-note">* Required fields</div>
          </div>
        </div>
      </form>
    </div>
  </body>
</html>
