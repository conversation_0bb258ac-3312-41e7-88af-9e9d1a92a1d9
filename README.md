# LifeCompass

A nature-inspired life journey application that helps users navigate through life's seasons with purpose and harmony.

## Project Philosophy

LifeCompass embraces the **Natural Harmony Principle**: the understanding that human lives follow natural rhythms and patterns similar to those in the natural world. Just as nature moves through seasons with purpose and beauty, our lives unfold in natural cycles of growth, abundance, harvest, and renewal.

The application uses the metaphors of seasons, compass directions, and natural ecosystems to create an intuitive, joyful experience for comprehensive life planning.

## Key Features

- **Living Compass**: A dynamic, nature-inspired visualization of your life journey
- **Seasonal Guidance**: Personalized guidance based on your current life season
- **Financial River Network**: Secure connections to financial institutions with intuitive visualization
- **Purpose Garden**: Tools for discovering and nurturing your life purpose
- **Growth Forest**: Systems for personal development and learning
- **Harvest Fields**: Tools for resource management and security
- **Legacy Mountain**: Systems for wisdom sharing and legacy planning

## Getting Started

### Prerequisites

- Node.js (v16+)
- npm or yarn
- Git

### Installation

1. Clone the repository

   ```
   git clone https://github.com/yourusername/lifecompass.git
   cd lifecompass
   ```

2. Install dependencies

   ```
   npm install
   ```

3. Start the development server

   ```
   npm start
   ```

4. Open Storybook to view components
   ```
   npm run storybook
   ```

## Development Approach

LifeCompass follows these development principles:

1. **Test-Driven Development**: Tests are written before implementing features
2. **Visual-First Development**: Components are developed in Storybook before integration
3. **AI-Assisted Development**: AI code assistants are used to enhance productivity
4. **Accessibility-First**: All components are designed to be fully accessible
5. **Nature-Inspired Design**: UI elements reflect natural patterns and beauty

## Project Structure

```
lifecompass/
├── docs/                 # Documentation
├── public/               # Public assets
├── src/                  # Source code
│   ├── assets/           # Images, fonts, etc.
│   ├── components/       # React components
│   │   ├── common/       # Shared components
│   │   ├── compass/      # Living Compass components
│   │   ├── garden/       # Purpose Garden components
│   │   ├── forest/       # Growth Forest components
│   │   ├── harvest/      # Harvest Fields components
│   │   ├── mountain/     # Legacy Mountain components
│   │   ├── river/        # Financial River components
│   │   └── seasons/      # Season-specific components
│   ├── hooks/            # Custom React hooks
│   ├── pages/            # Page components
│   ├── services/         # API services
│   ├── styles/           # Global styles and themes
│   ├── tests/            # Test utilities
│   └── utils/            # Utility functions
├── backend/              # Backend services
│   ├── api/              # API routes
│   ├── controllers/      # Request handlers
│   ├── models/           # Data models
│   ├── middleware/       # Express middleware
│   ├── utils/            # Utility functions
│   ├── config/           # Configuration
│   └── tests/            # Backend tests
├── scripts/              # Build and utility scripts
└── config/               # Configuration files
```

## Learn More

For more information, see the [System Design Document](./docs/system-design-document.md).

## License

This project is licensed under the MIT License - see the LICENSE file for details.
