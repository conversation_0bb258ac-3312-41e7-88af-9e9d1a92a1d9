# Financial Compass Components Documentation

This document provides an overview of the RetireEz Financial Compass components that will be reused and enhanced in the LifeCompass application.

## 1. Overview

The Financial Compass in RetireEz is organized into four compass directions, each containing multiple sub-features:

- **North**: Where You Are (Current Position)
- **East**: Where You're Going (Retirement Vision)
- **South**: What Could Change (Protection & Risks)
- **West**: What You'll Leave Behind (Legacy Planning)

## 2. North Direction Components

### 2.1 Personal Information

**Location**: `/src/features/FinancialCompass/components/SubFeatures/PersonalInformation`

**Key Components**:

- `PersonalInformationForm`: Main form component for collecting personal information
- `PersonalInformationSummary`: Displays summary of personal information
- `PersonalInformationValidator`: Validates personal information inputs

**Data Structure**:

```typescript
interface PersonalInformation {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: string;
  email: string;
  phone: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
  };
}
```

**Reuse Strategy**: Extract and enhance with improved validation, auto-save, and theme support.

### 2.2 Family Information

**Location**: `/src/features/FinancialCompass/components/SubFeatures/FamilyInformation`

**Key Components**:

- `FamilyInformationForm`: Main form component for collecting family information
- `SpouseInformation`: Collects information about spouse
- `DependentInformation`: Collects information about dependents
- `FamilyInformationSummary`: Displays summary of family information

**Data Structure**:

```typescript
interface FamilyInformation {
  maritalStatus: string;
  spouse?: {
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    gender: string;
    isWorking: boolean;
    isRetired: boolean;
  };
  dependents: Array<{
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    relationship: string;
  }>;
}
```

**Reuse Strategy**: Extract and enhance with improved validation, auto-save, and theme support.

### 2.3 Income Details

**Location**: `/src/features/FinancialCompass/components/SubFeatures/IncomeDetails`

**Key Components**:

- `IncomeDetailsForm`: Main form component for collecting income information
- `IncomeSourceForm`: Form for adding/editing income sources
- `IncomeSummary`: Displays summary of income information
- `TaxEstimator`: Estimates taxes based on income

**Data Structure**:

```typescript
interface IncomeDetails {
  primaryIncome: {
    source: string;
    amount: number;
    frequency: string;
    isRetirementIncome: boolean;
  }[];
  spouseIncome?: {
    source: string;
    amount: number;
    frequency: string;
    isRetirementIncome: boolean;
  }[];
  otherIncome: {
    source: string;
    amount: number;
    frequency: string;
    isRetirementIncome: boolean;
  }[];
  totalAnnualIncome: number;
  estimatedAfterTaxIncome: number;
}
```

**Reuse Strategy**: Extract and enhance with improved validation, auto-save, and theme support.

### 2.4 Expense Details

**Location**: `/src/features/FinancialCompass/components/SubFeatures/ExpenseDetails`

**Key Components**:

- `ExpenseDetailsForm`: Main form component for collecting expense information
- `ExpenseCategoryForm`: Form for adding/editing expense categories
- `ExpenseSummary`: Displays summary of expense information
- `BudgetAnalysis`: Analyzes budget based on income and expenses

**Data Structure**:

```typescript
interface ExpenseDetails {
  housing: {
    mortgage: number;
    rent: number;
    propertyTax: number;
    homeInsurance: number;
    maintenance: number;
    utilities: number;
  };
  transportation: {
    carPayment: number;
    fuel: number;
    insurance: number;
    maintenance: number;
    publicTransport: number;
  };
  food: {
    groceries: number;
    diningOut: number;
  };
  healthcare: {
    insurance: number;
    outOfPocket: number;
    prescriptions: number;
  };
  personal: {
    clothing: number;
    entertainment: number;
    travel: number;
    gifts: number;
    education: number;
  };
  debt: {
    creditCards: number;
    studentLoans: number;
    personalLoans: number;
  };
  other: {
    description: string;
    amount: number;
  }[];
  totalMonthlyExpenses: number;
  totalAnnualExpenses: number;
}
```

**Reuse Strategy**: Extract and enhance with improved validation, auto-save, and theme support.

### 2.5 Assets & Investments

**Location**: `/src/features/FinancialCompass/components/SubFeatures/Assets`

**Key Components**:

- `AssetsForm`: Main form component for collecting asset information
- `AssetItemForm`: Form for adding/editing individual assets
- `AssetsSummary`: Displays summary of asset information
- `AssetAllocationChart`: Visualizes asset allocation

**Data Structure**:

```typescript
interface Assets {
  cashAccounts: {
    type: string;
    institution: string;
    balance: number;
    interestRate?: number;
  }[];
  retirementAccounts: {
    type: string;
    institution: string;
    balance: number;
    annualContribution?: number;
    employerMatch?: number;
  }[];
  investments: {
    type: string;
    description: string;
    value: number;
    annualReturn?: number;
  }[];
  realEstate: {
    type: string;
    description: string;
    value: number;
    mortgageBalance?: number;
  }[];
  otherAssets: {
    description: string;
    value: number;
  }[];
  totalAssets: number;
}
```

**Reuse Strategy**: Extract and enhance with improved validation, auto-save, and theme support.

### 2.6 Liabilities & Debt

**Location**: `/src/features/FinancialCompass/components/SubFeatures/Liabilities`

**Key Components**:

- `LiabilitiesForm`: Main form component for collecting liability information
- `LiabilityItemForm`: Form for adding/editing individual liabilities
- `LiabilitiesSummary`: Displays summary of liability information
- `DebtPayoffCalculator`: Calculates debt payoff strategies

**Data Structure**:

```typescript
interface Liabilities {
  mortgages: {
    description: string;
    balance: number;
    interestRate: number;
    monthlyPayment: number;
    remainingTerm: number;
  }[];
  loans: {
    type: string;
    balance: number;
    interestRate: number;
    monthlyPayment: number;
    remainingTerm: number;
  }[];
  creditCards: {
    issuer: string;
    balance: number;
    interestRate: number;
    minimumPayment: number;
  }[];
  otherDebts: {
    description: string;
    balance: number;
    interestRate?: number;
    monthlyPayment?: number;
  }[];
  totalLiabilities: number;
}
```

**Reuse Strategy**: Extract and enhance with improved validation, auto-save, and theme support.

## 3. East Direction Components

### 3.1 Retirement Goals

**Location**: `/src/features/FinancialCompass/components/SubFeatures/RetirementGoals`

**Key Components**:

- `RetirementGoalsForm`: Main form component for collecting retirement goal information
- `RetirementLifestyleCalculator`: Calculates retirement lifestyle costs
- `RetirementGoalsSummary`: Displays summary of retirement goals
- `RetirementTimeline`: Visualizes retirement timeline

**Data Structure**:

```typescript
interface RetirementGoals {
  desiredRetirementAge: number;
  spouseDesiredRetirementAge?: number;
  desiredMonthlyIncome: number;
  retirementLifestyle: string;
  retirementLocation: string;
  retirementActivities: string[];
  legacyGoals: string;
  priorityRanking: {
    healthcare: number;
    travel: number;
    housing: number;
    family: number;
    charity: number;
  };
}
```

**Reuse Strategy**: Extract and enhance with improved validation, auto-save, and theme support.

### 3.2 Social Security Planning

**Location**: `/src/features/FinancialCompass/components/SubFeatures/SocialSecurityPlanning`

**Key Components**:

- `SocialSecurityPlanningForm`: Main form component for social security planning
- `BenefitEstimator`: Estimates social security benefits
- `ClaimingStrategyAnalyzer`: Analyzes optimal claiming strategies
- `SocialSecuritySummary`: Displays summary of social security planning

**Data Structure**:

```typescript
interface SocialSecurityPlanning {
  primaryBenefitEstimate: {
    at62: number;
    at67: number;
    at70: number;
  };
  spouseBenefitEstimate?: {
    at62: number;
    at67: number;
    at70: number;
  };
  plannedClaimingAge: number;
  spousePlannedClaimingAge?: number;
  optimizationStrategy: string;
  estimatedLifetimeBenefits: number;
}
```

**Reuse Strategy**: Extract and enhance with improved validation, auto-save, and theme support.

### 3.3 Investment Preferences

**Location**: `/src/features/FinancialCompass/components/SubFeatures/Investment`

**Key Components**:

- `InvestmentPreferencesForm`: Main form component for investment preferences
- `RiskToleranceAssessment`: Assesses risk tolerance
- `AssetAllocationRecommendation`: Recommends asset allocation
- `InvestmentPreferencesSummary`: Displays summary of investment preferences

**Data Structure**:

```typescript
interface InvestmentPreferences {
  riskTolerance: number;
  investmentHorizon: number;
  investmentObjectives: string[];
  preferredInvestmentTypes: string[];
  excludedInvestmentTypes: string[];
  desiredPortfolioAllocation: {
    stocks: number;
    bonds: number;
    cash: number;
    realEstate: number;
    alternatives: number;
  };
  rebalancingFrequency: string;
}
```

**Reuse Strategy**: Extract and enhance with improved validation, auto-save, and theme support.

### 3.4 Risk Assessment

**Location**: `/src/features/FinancialCompass/components/SubFeatures/RiskAssessment`

**Key Components**:

- `RiskAssessmentForm`: Main form component for risk assessment
- `RiskQuestionnaireForm`: Questionnaire for assessing risk tolerance
- `RiskProfileSummary`: Displays summary of risk assessment
- `RiskVisualization`: Visualizes risk profile

**Data Structure**:

```typescript
interface RiskAssessment {
  questionnaire: {
    question: string;
    answer: string;
    score: number;
  }[];
  overallRiskScore: number;
  riskProfile: string;
  recommendedAssetAllocation: {
    stocks: number;
    bonds: number;
    cash: number;
    realEstate: number;
    alternatives: number;
  };
}
```

**Reuse Strategy**: Extract and enhance with improved validation, auto-save, and theme support.
