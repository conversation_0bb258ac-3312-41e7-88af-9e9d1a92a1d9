# LifeCompass Testing Guide

## Overview

This document outlines the testing strategy and practices for the LifeCompass project. We follow a test-driven development (TDD) approach, writing tests before implementing features to ensure high-quality, maintainable code.

## Testing Philosophy

Our testing approach is guided by the following principles:

1. **Test-First Development**: Write tests before implementing features
2. **Comprehensive Coverage**: Test all aspects of the application
3. **Realistic Testing**: Tests should reflect real user behavior
4. **Maintainable Tests**: Tests should be easy to understand and maintain
5. **Fast Feedback**: Tests should run quickly to provide immediate feedback

## Testing Levels

### Unit Testing

Unit tests verify that individual components and functions work as expected in isolation.

#### What to Test

- React components (rendering, props, state, events)
- Utility functions
- Custom hooks
- Redux reducers, selectors, and actions
- Service functions

#### Tools

- Jest: Test runner and assertion library
- React Testing Library: Component testing
- Jest-DOM: DOM assertions

#### Example: Component Test

```tsx
// Button.test.tsx
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider } from '../contexts/ThemeContext';
import Button from './Button';

describe('Button', () => {
  test('renders with correct text', () => {
    render(
      <ThemeProvider>
        <Button>Click Me</Button>
      </ThemeProvider>
    );

    expect(screen.getByRole('button')).toHaveTextContent('Click Me');
  });

  test('calls onClick when clicked', () => {
    const handleClick = jest.fn();

    render(
      <ThemeProvider>
        <Button onClick={handleClick}>Click Me</Button>
      </ThemeProvider>
    );

    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

#### Example: Hook Test

```tsx
// useCounter.test.tsx
import { renderHook, act } from '@testing-library/react-hooks';
import useCounter from './useCounter';

describe('useCounter', () => {
  test('should initialize with default value', () => {
    const { result } = renderHook(() => useCounter());
    expect(result.current.count).toBe(0);
  });

  test('should increment counter', () => {
    const { result } = renderHook(() => useCounter());

    act(() => {
      result.current.increment();
    });

    expect(result.current.count).toBe(1);
  });
});
```

### Integration Testing

Integration tests verify that multiple components or systems work together correctly.

#### What to Test

- Component interactions
- Form submissions
- API service integration
- Redux store integration
- Router integration

#### Tools

- Jest: Test runner
- React Testing Library: Component testing
- MSW (Mock Service Worker): API mocking

#### Example: Form Integration Test

```tsx
// LoginForm.test.tsx
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ThemeProvider } from '../contexts/ThemeContext';
import { rest } from 'msw';
import { setupServer } from 'msw/node';
import LoginForm from './LoginForm';

const server = setupServer(
  rest.post('/api/login', (req, res, ctx) => {
    return res(ctx.json({ token: 'fake-token' }));
  })
);

beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

describe('LoginForm', () => {
  test('submits form with user credentials', async () => {
    const onSuccess = jest.fn();

    render(
      <ThemeProvider>
        <LoginForm onSuccess={onSuccess} />
      </ThemeProvider>
    );

    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' },
    });

    fireEvent.change(screen.getByLabelText(/password/i), {
      target: { value: 'password123' },
    });

    fireEvent.click(screen.getByRole('button', { name: /log in/i }));

    await waitFor(() => {
      expect(onSuccess).toHaveBeenCalledWith({ token: 'fake-token' });
    });
  });
});
```

### End-to-End Testing

E2E tests verify that entire user flows work correctly from start to finish.

#### What to Test

- Critical user journeys
- Authentication flows
- Data persistence
- Cross-browser compatibility

#### Tools

- Cypress: E2E testing framework

#### Example: Cypress Test

```js
// cypress/integration/login.spec.js
describe('Login Flow', () => {
  beforeEach(() => {
    cy.visit('/login');
  });

  it('should log in successfully with valid credentials', () => {
    cy.intercept('POST', '/api/login', {
      statusCode: 200,
      body: { token: 'fake-token' },
    }).as('loginRequest');

    cy.get('[data-testid=email-input]').type('<EMAIL>');
    cy.get('[data-testid=password-input]').type('password123');
    cy.get('[data-testid=login-button]').click();

    cy.wait('@loginRequest');
    cy.url().should('include', '/dashboard');
    cy.get('[data-testid=user-greeting]').should('contain', 'Welcome');
  });
});
```

### Visual Testing

Visual tests verify that components render correctly and maintain their appearance.

#### What to Test

- Component appearance
- Responsive behavior
- Theme variations
- Animations and transitions

#### Tools

- Storybook: Component development and documentation
- Chromatic: Visual testing and review
- Percy: Visual regression testing

#### Example: Storybook Story

```tsx
// Button.stories.tsx
import React from 'react';
import { ComponentStory, ComponentMeta } from '@storybook/react';
import { ThemeProvider } from '../contexts/ThemeContext';
import Button from './Button';

export default {
  title: 'Components/Button',
  component: Button,
} as ComponentMeta<typeof Button>;

const Template: ComponentStory<typeof Button> = (args) => (
  <ThemeProvider>
    <Button {...args} />
  </ThemeProvider>
);

export const Primary = Template.bind({});
Primary.args = {
  variant: 'primary',
  children: 'Primary Button',
};

export const Secondary = Template.bind({});
Secondary.args = {
  variant: 'secondary',
  children: 'Secondary Button',
};
```

### Accessibility Testing

Accessibility tests verify that the application is usable by people with disabilities.

#### What to Test

- Keyboard navigation
- Screen reader compatibility
- Color contrast
- Focus management
- ARIA attributes

#### Tools

- axe-core: Automated accessibility testing
- Lighthouse: Accessibility auditing
- NVDA/VoiceOver: Screen reader testing

#### Example: Accessibility Test

```tsx
// Button.test.tsx (with accessibility testing)
import React from 'react';
import { render, screen } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';
import { ThemeProvider } from '../contexts/ThemeContext';
import Button from './Button';

expect.extend(toHaveNoViolations);

describe('Button accessibility', () => {
  test('should not have accessibility violations', async () => {
    const { container } = render(
      <ThemeProvider>
        <Button>Click Me</Button>
      </ThemeProvider>
    );

    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});
```

## Test Organization

### File Structure

Tests should be co-located with the code they test:

```
src/
  components/
    Button/
      Button.tsx
      Button.test.tsx
      Button.stories.tsx
  hooks/
    useCounter.ts
    useCounter.test.ts
  services/
    api.ts
    api.test.ts
  utils/
    formatters.ts
    formatters.test.ts
```

For E2E tests:

```
cypress/
  integration/
    login.spec.js
    dashboard.spec.js
  fixtures/
    users.json
  support/
    commands.js
```

### Naming Conventions

- **Unit Tests**: `[filename].test.tsx` or `[filename].spec.tsx`
- **Integration Tests**: `[feature].integration.test.tsx`
- **E2E Tests**: `[feature].spec.js`
- **Test Files**: Use descriptive names that reflect what's being tested

### Test Structure

Follow the Arrange-Act-Assert (AAA) pattern:

```tsx
test('should increment counter when button is clicked', () => {
  // Arrange
  render(<Counter initialCount={0} />);

  // Act
  fireEvent.click(screen.getByRole('button', { name: /increment/i }));

  // Assert
  expect(screen.getByText(/count: 1/i)).toBeInTheDocument();
});
```

## Best Practices

### General Guidelines

1. **Test Behavior, Not Implementation**: Focus on what the component does, not how it does it
2. **Use Realistic Data**: Test with data that resembles what users will see
3. **Avoid Test Duplication**: Don't repeat the same tests at different levels
4. **Keep Tests Simple**: Each test should verify one specific behavior
5. **Use Descriptive Test Names**: Names should describe the expected behavior

### Component Testing Guidelines

1. **Test Rendering**: Verify that components render correctly
2. **Test Props**: Verify that props affect the component as expected
3. **Test Events**: Verify that events trigger the correct actions
4. **Test Edge Cases**: Test empty states, loading states, error states
5. **Test Accessibility**: Verify that components are accessible

### Redux Testing Guidelines

1. **Test Reducers**: Verify that state changes correctly based on actions
2. **Test Selectors**: Verify that selectors extract the correct data
3. **Test Action Creators**: Verify that actions are created correctly
4. **Test Thunks**: Verify that async actions work correctly
5. **Test Store Integration**: Verify that components connect to the store correctly

### API Testing Guidelines

1. **Mock API Responses**: Use MSW to mock API responses
2. **Test Success Cases**: Verify that successful responses are handled correctly
3. **Test Error Cases**: Verify that error responses are handled correctly
4. **Test Loading States**: Verify that loading states are displayed correctly
5. **Test Retry Logic**: Verify that retry mechanisms work correctly

## Continuous Integration

All tests should run automatically on pull requests and merges to the main branch:

1. **Unit and Integration Tests**: Run on every PR
2. **E2E Tests**: Run on merges to main
3. **Visual Tests**: Run on PRs that affect UI components
4. **Accessibility Tests**: Run on PRs that affect UI components

## Conclusion

By following this testing guide, we ensure that the LifeCompass application is reliable, maintainable, and provides a high-quality user experience. The test-driven development approach helps us catch issues early and build features with confidence.

Remember: Tests are an investment in the future of the project. They may take time to write initially, but they save time and prevent bugs in the long run.
