# LifeCompass Development Guide

## 1. Project Overview

LifeCompass is a nature-inspired life journey application that helps users navigate through life's seasons with purpose and harmony. The application uses the metaphors of seasons, compass directions, and natural ecosystems to create an intuitive, joyful experience for comprehensive life planning.

### 1.1 Vision

LifeCompass will become the premier life journey application, helping users navigate through all of life's seasons with purpose, harmony, and joy. By leveraging the Natural Harmony Principle and creating an intuitive, premium experience, we will transform complex life planning into a natural, organic process that resonates with users' innate connection to nature's wisdom.

### 1.2 Core Principles

1. **Natural Harmony Principle**: Human lives follow natural rhythms and patterns similar to those in the natural world
2. **Seasonal Wisdom**: Each life season has its own wisdom, challenges, and opportunities
3. **Compass Navigation**: Life areas are organized around compass directions for intuitive navigation
4. **Ecosystem Integration**: Life areas form an interconnected ecosystem that works in harmony
5. **Premium Experience**: Every interaction feels refined, thoughtful, and premium

## 2. Technical Architecture

### 2.1 Technology Stack

- **Frontend**: React with TypeScript
- **State Management**: Redux Toolkit
- **Styling**: Styled Components
- **Routing**: React Router
- **API Client**: Axios
- **Testing**: Jest, React Testing Library, Cypress
- **Visualization**: D3.js, React Spring
- **Build Tools**: Webpack, Babel
- **CI/CD**: GitHub Actions

### 2.2 Project Structure

```
lifecompass/
├── docs/                 # Documentation
├── public/               # Public assets
├── src/                  # Source code
│   ├── assets/           # Images, fonts, etc.
│   ├── components/       # React components
│   │   ├── common/       # Shared components
│   │   ├── compass/      # Living Compass components
│   │   ├── garden/       # Purpose Garden components
│   │   ├── forest/       # Growth Forest components
│   │   ├── harvest/      # Harvest Fields components
│   │   ├── mountain/     # Legacy Mountain components
│   │   ├── river/        # Financial River components
│   │   └── seasons/      # Season-specific components
│   ├── contexts/         # React contexts
│   ├── hooks/            # Custom React hooks
│   ├── pages/            # Page components
│   ├── services/         # API services
│   │   ├── auth/         # Authentication services
│   │   ├── financial/    # Financial services
│   │   ├── journey/      # Journey services
│   │   └── wisdom/       # Wisdom services
│   ├── store/            # Redux store
│   ├── styles/           # Global styles and themes
│   ├── tests/            # Test utilities
│   └── utils/            # Utility functions
├── .storybook/           # Storybook configuration
├── cypress/              # Cypress tests
└── scripts/              # Build and utility scripts
```

## 3. Design System

### 3.1 Color System

#### 3.1.1 Seasonal Palettes

**Spring**:

- Primary: #4CAF50 (Fresh Green)
- Secondary: #8BC34A (Light Green)
- Accent: #FFEB3B (Sunshine Yellow)
- Background (Light): #F1F8E9
- Background (Dark): #1B5E20

**Summer**:

- Primary: #2196F3 (Sky Blue)
- Secondary: #03A9F4 (Light Blue)
- Accent: #FF9800 (Warm Orange)
- Background (Light): #E3F2FD
- Background (Dark): #0D47A1

**Autumn**:

- Primary: #FF5722 (Deep Orange)
- Secondary: #FF9800 (Amber)
- Accent: #795548 (Brown)
- Background (Light): #FBE9E7
- Background (Dark): #BF360C

**Winter**:

- Primary: #9C27B0 (Purple)
- Secondary: #673AB7 (Deep Purple)
- Accent: #00BCD4 (Cyan)
- Background (Light): #F3E5F5
- Background (Dark): #4A148C

#### 3.1.2 Neutral Palette

**Light Theme**:

- Background: #FFFFFF
- Surface: #F5F5F5
- Primary Text: #212121
- Secondary Text: #757575
- Divider: #BDBDBD

**Dark Theme**:

- Background: #121212
- Surface: #1E1E1E
- Primary Text: #FFFFFF
- Secondary Text: #B0B0B0
- Divider: #333333

#### 3.1.3 Semantic Colors

- Success: #4CAF50
- Warning: #FFC107
- Error: #F44336
- Info: #2196F3

### 3.2 Typography

#### 3.2.1 Font Families

- Primary: "Playfair Display" (Headings)
- Secondary: "Lato" (Body text)
- Tertiary: "Montserrat" (UI elements)

#### 3.2.2 Font Sizes

**Light Theme**:

- Display: 48px/3rem
- H1: 32px/2rem
- H2: 24px/1.5rem
- H3: 20px/1.25rem
- Body Large: 18px/1.125rem
- Body: 16px/1rem
- Body Small: 14px/0.875rem
- Caption: 12px/0.75rem

**Dark Theme**:

- Display: 56px/3.5rem
- H1: 36px/2.25rem
- H2: 28px/1.75rem
- H3: 22px/1.375rem
- Body Large: 20px/1.25rem
- Body: 18px/1.125rem
- Body Small: 16px/1rem
- Caption: 14px/0.875rem

#### 3.2.3 Font Weights

- Light: 300
- Regular: 400
- Medium: 500
- Bold: 700

#### 3.2.4 Line Heights

- Tight: 1.2
- Normal: 1.5
- Relaxed: 1.8

### 3.3 Spacing

Based on an 8px grid system:

- Micro: 4px (0.25rem)
- Tiny: 8px (0.5rem)
- Small: 16px (1rem)
- Medium: 24px (1.5rem)
- Large: 32px (2rem)
- XLarge: 48px (3rem)
- XXLarge: 64px (4rem)
- Huge: 96px (6rem)

### 3.4 Border Radius

- Small: 4px (0.25rem)
- Medium: 8px (0.5rem)
- Large: 16px (1rem)
- Circular: 50%

### 3.5 Shadows

**Light Theme**:

- Subtle: 0 2px 4px rgba(0,0,0,0.05)
- Medium: 0 4px 8px rgba(0,0,0,0.1)
- Strong: 0 8px 16px rgba(0,0,0,0.15)
- Dramatic: 0 16px 32px rgba(0,0,0,0.2)

**Dark Theme**:

- Subtle: 0 2px 4px rgba(0,0,0,0.2)
- Medium: 0 4px 8px rgba(0,0,0,0.3)
- Strong: 0 8px 16px rgba(0,0,0,0.4)
- Dramatic: 0 16px 32px rgba(0,0,0,0.5)

## 4. Development Workflow

### 4.1 Test-Driven Development

1. **Write Tests First**: Create tests before implementing features
2. **Red-Green-Refactor**: Follow the TDD cycle rigorously
3. **Comprehensive Coverage**: Aim for high test coverage across all components
4. **Automated Verification**: Integrate testing into CI/CD pipeline
5. **Living Documentation**: Tests serve as documentation of expected behavior

### 4.2 Git Workflow

1. **Main Branch Protection**: Require pull requests and passing tests
2. **Feature Branches**: Short-lived branches for individual features
3. **Conventional Commits**: Standardized commit message format
4. **Pull Request Template**: Structured template for consistent PRs
5. **Code Review Process**: Required reviews before merging

### 4.3 Visual-First Development

1. **Design System First**: Establish the design system before feature development
2. **Component Storybook**: Develop and visualize components in isolation
3. **Visual Prototypes**: Create visual prototypes of key user journeys
4. **Regular Design Reviews**: Conduct frequent reviews of visual implementation
5. **User Testing**: Test visual designs with users early and often

### 4.4 AI-Assisted Development

1. **Standardized AI Prompting**: Established patterns for interacting with AI assistants
2. **Code Generation Guidelines**: Standards for AI-generated code review and integration
3. **Documentation Automation**: Using AI to maintain comprehensive documentation
4. **Testing Assistance**: Leveraging AI for test case generation and coverage
5. **Knowledge Sharing**: Using AI to disseminate best practices across the team

## 5. Implementation Plan

### 5.1 Phase 1: Foundation (Weeks 1-4)

#### Week 1: Project Setup & Design System

- Establish project infrastructure
- Create design system foundation
- Set up testing framework
- Implement basic CI/CD pipeline

#### Week 2: Core Components & Living Compass

- Implement core UI components
- Create Living Compass visualization
- Establish component testing patterns
- Develop seasonal variations

#### Week 3: Layout & Navigation

- Implement application layout
- Create navigation system
- Develop page transitions
- Establish routing structure

#### Week 4: State Management & Data Flow

- Implement state management
- Create data models
- Develop API service layer
- Establish data persistence

### 5.2 Phase 2: Core Features (Weeks 5-8)

#### Week 5: User Authentication & Profiles

- Implement user authentication
- Create profile management
- Develop user preferences
- Establish user journey tracking

#### Week 6: Financial Connections & Data Import

- Implement financial institution connections
- Create data import functionality
- Develop data categorization
- Establish data visualization

#### Week 7: Garden of Purpose & Forest of Growth

- Implement Garden of Purpose module
- Create Forest of Growth module
- Develop purpose discovery tools
- Establish growth tracking

#### Week 8: Harvest Fields & Mountain of Legacy

- Implement Harvest Fields module
- Create Mountain of Legacy module
- Develop resource management tools
- Establish legacy planning

### 5.3 Phase 3: Integration & Enhancement (Weeks 9-12)

#### Week 9: Dashboard & Reporting

- Implement main dashboard
- Create reporting system
- Develop insights engine
- Establish notification system

#### Week 10: Guided Journeys & Wizards

- Implement guided journey framework
- Create onboarding wizard
- Develop assessment tools
- Establish decision support system

#### Week 11: Mobile Optimization & Offline Support

- Optimize for mobile devices
- Implement offline functionality
- Develop sync mechanisms
- Establish progressive web app features

#### Week 12: Performance Optimization & Final Polish

- Optimize application performance
- Enhance accessibility
- Implement final visual polish
- Prepare for launch

## 6. Testing Strategy

### 6.1 Unit Testing

- **Component Tests**: Tests for individual UI components
- **Service Tests**: Tests for service functions and methods
- **Utility Tests**: Tests for utility functions and helpers
- **State Tests**: Tests for state management logic
- **Hook Tests**: Tests for custom React hooks

### 6.2 Integration Testing

- **Component Integration**: Tests for component interactions
- **Service Integration**: Tests for service interactions
- **API Integration**: Tests for API endpoints
- **State Integration**: Tests for state management across components
- **Form Validation**: Tests for form validation and submission

### 6.3 End-to-End Testing

- **User Journeys**: Tests for complete user flows
- **Cross-Browser**: Tests across different browsers
- **Responsive Testing**: Tests across different device sizes
- **Accessibility Testing**: Tests for accessibility compliance
- **Performance Testing**: Tests for performance benchmarks

### 6.4 Visual Testing

- **Component Appearance**: Tests for component visual rendering
- **Theme Variations**: Tests for theme switching
- **Responsive Behavior**: Tests for responsive design
- **Animation Testing**: Tests for animations and transitions
- **Visual Regression**: Tests for unexpected visual changes

## 7. Core Components

### 7.1 Living Compass

The central visualization that adapts to the user's life season:

- **Season Renderer**: Dynamically renders appropriate seasonal visuals
- **Compass Navigation**: Interactive compass for navigating life areas
- **Journey Timeline**: Visual representation of life journey
- **Achievement Markers**: Celebration points for milestones
- **Adaptive Animation**: Nature-inspired animations that reflect current season

### 7.2 Natural Ecosystem Components

Components for each life area:

- **Garden of Purpose Module**: Tools for discovering and nurturing purpose
- **Forest of Growth Module**: Systems for personal development and learning
- **Harvest Fields Module**: Tools for resource management and security
- **Mountain of Legacy Module**: Systems for wisdom sharing and legacy

### 7.3 Financial River Network

Components for financial connections and visualization:

- **River Source Connector**: Interface for connecting financial institutions
- **Flow Visualizer**: Dynamic visualization of financial resources
- **Watershed Security**: Components for managing security and permissions
- **Resource Allocation**: Tools for planning resource distribution

### 7.4 Shared UI Components

- **Nature-Inspired Design System**: Consistent visual language based on natural elements
- **Seasonal Theming Engine**: System for adapting UI to current life season
- **Accessibility Layer**: Ensuring all components are fully accessible
- **Responsive Design Framework**: Adaptation to different devices and screen sizes

## 8. AI Code Assistant Guidelines

### 8.1 Effective Prompting

When working with AI code assistants:

1. **Be Specific**: Clearly describe what you need
2. **Provide Context**: Give sufficient background information
3. **Include Examples**: Show examples of desired output
4. **Specify Constraints**: Mention any limitations or requirements
5. **Iterative Refinement**: Use multiple prompts to refine results

### 8.2 Code Generation Best Practices

When generating code with AI assistance:

1. **Start with Tests**: Generate tests before implementation
2. **Follow Design System**: Ensure generated code adheres to the design system
3. **Maintain Consistency**: Keep code consistent with existing patterns
4. **Review Thoroughly**: Always review generated code carefully
5. **Refactor as Needed**: Don't hesitate to refactor generated code

### 8.3 Documentation Generation

When generating documentation with AI assistance:

1. **Clear Structure**: Use consistent documentation structure
2. **Code Examples**: Include relevant code examples
3. **Use Cases**: Describe common use cases
4. **Edge Cases**: Document edge cases and limitations
5. **Maintenance Notes**: Include notes on maintenance considerations

### 8.4 Problem-Solving Approach

When using AI to solve problems:

1. **Define the Problem**: Clearly articulate the problem
2. **Break It Down**: Divide complex problems into smaller parts
3. **Explore Solutions**: Generate multiple potential solutions
4. **Evaluate Options**: Assess the pros and cons of each solution
5. **Implement and Test**: Implement the chosen solution and test thoroughly

## 9. Conclusion

This development guide provides a comprehensive framework for building the LifeCompass application. By following these guidelines, we will create a premium, nature-inspired application that helps users navigate through life's seasons with purpose and harmony.

The key to success is maintaining a consistent approach to development, with a focus on:

- Test-driven development from the start
- Visual-first approach with early UI components
- Modular architecture for maintainability
- Seasonal theming for a unique user experience
- AI-assisted development for productivity and quality

Remember that this guide is a living document and may evolve as the project progresses. Regular reviews will help us identify areas for improvement and refine our approach.
