# Well-Being Components Documentation

This document provides an overview of the RetireEz Well-Being components that will be reused and enhanced in the LifeCompass application as part of the "Seasons of Self" (renamed to "Life Stages Journey") feature.

## 1. Overview

The Well-Being section in RetireEz focuses on non-financial aspects of retirement readiness and overall life satisfaction. These components will be adapted to align with the seasonal metaphors and life stages in the LifeCompass application.

## 2. Well-Being Readiness Components

### 2.1 Well-Being Assessment

**Location**: `/src/features/WellbeingReadiness/components`

**Key Components**:

- `WellbeingAssessment`: Main component for assessing well-being across dimensions
- `WellbeingDimensionForm`: Form for individual well-being dimensions
- `WellbeingScorecard`: Displays well-being scores across dimensions
- `WellbeingVisualization`: Visualizes well-being assessment results

**Data Structure**:

```typescript
interface WellbeingAssessment {
  dimensions: {
    physical: {
      score: number;
      strengths: string[];
      areasForImprovement: string[];
      goals: string[];
    };
    emotional: {
      score: number;
      strengths: string[];
      areasForImprovement: string[];
      goals: string[];
    };
    social: {
      score: number;
      strengths: string[];
      areasForImprovement: string[];
      goals: string[];
    };
    financial: {
      score: number;
      strengths: string[];
      areasForImprovement: string[];
      goals: string[];
    };
    purpose: {
      score: number;
      strengths: string[];
      areasForImprovement: string[];
      goals: string[];
    };
    cognitive: {
      score: number;
      strengths: string[];
      areasForImprovement: string[];
      goals: string[];
    };
  };
  overallScore: number;
  wellbeingProfile: string;
}
```

**Reuse Strategy**: Adapt to the Seasons of Self concept, enhancing with seasonal theming and nature-inspired metaphors.

### 2.2 Happiness Components

**Location**: `/src/features/Happiness/components`

**Key Components**:

- `HappinessAssessment`: Main component for assessing happiness
- `HappinessFactorsForm`: Form for evaluating happiness factors
- `HappinessScorecard`: Displays happiness scores
- `HappinessVisualization`: Visualizes happiness assessment results

**Data Structure**:

```typescript
interface HappinessAssessment {
  factors: {
    relationships: number;
    health: number;
    purpose: number;
    community: number;
    security: number;
    autonomy: number;
    growth: number;
  };
  overallHappinessScore: number;
  happinessProfile: string;
  happinessGoals: string[];
}
```

**Reuse Strategy**: Adapt to the Spring season (Stages 1-2: Pleasure, Happiness) in the Seasons of Self concept.

### 2.3 Joy Components

**Location**: `/src/features/Joy/components`

**Key Components**:

- `JoyAssessment`: Main component for assessing joy
- `JoyActivitiesForm`: Form for identifying joy-inducing activities
- `JoyScorecard`: Displays joy scores
- `JoyVisualization`: Visualizes joy assessment results

**Data Structure**:

```typescript
interface JoyAssessment {
  activities: {
    name: string;
    frequency: string;
    intensity: number;
    category: string;
  }[];
  flowExperiences: {
    activity: string;
    frequency: string;
    duration: string;
  }[];
  overallJoyScore: number;
  joyProfile: string;
  joyGoals: string[];
}
```

**Reuse Strategy**: Adapt to the Summer season (Stages 3-4: Joy, Momentum) in the Seasons of Self concept.

### 2.4 Life Purpose Components

**Location**: `/src/features/LifePurpose/components`

**Key Components**:

- `LifePurposeAssessment`: Main component for assessing life purpose
- `IkigaiForm`: Form for exploring the Ikigai concept (what you love, what you're good at, what the world needs, what you can be paid for)
- `PurposeScorecard`: Displays purpose scores
- `PurposeVisualization`: Visualizes purpose assessment results

**Data Structure**:

```typescript
interface LifePurposeAssessment {
  ikigai: {
    love: string[];
    good: string[];
    paid: string[];
    needed: string[];
  };
  values: string[];
  strengths: string[];
  contributions: string[];
  legacy: string;
  overallPurposeScore: number;
  purposeProfile: string;
  purposeGoals: string[];
}
```

**Reuse Strategy**: Adapt to the Winter season (Stages 7-9: Calling, Purpose, Fulfillment) in the Seasons of Self concept.

### 2.5 Pivot Navigator Components

**Location**: `/src/features/PivotNavigator/components`

**Key Components**:

- `PivotAssessment`: Main component for assessing life transitions
- `TransitionPlanningForm`: Form for planning life transitions
- `PivotScorecard`: Displays transition readiness scores
- `PivotVisualization`: Visualizes transition planning

**Data Structure**:

```typescript
interface PivotAssessment {
  currentSituation: string;
  desiredSituation: string;
  motivations: string[];
  barriers: string[];
  resources: string[];
  supportNetwork: string[];
  timeline: {
    phase: string;
    duration: string;
    milestones: string[];
  }[];
  overallReadinessScore: number;
  pivotProfile: string;
  pivotGoals: string[];
}
```

**Reuse Strategy**: Adapt to the Autumn season (Stages 5-6: Pivot, Goal Seeking) in the Seasons of Self concept.

## 3. Longevity Components

### 3.1 Longevity Assessment

**Location**: `/src/features/Longevity/components`

**Key Components**:

- `LongevityAssessment`: Main component for assessing longevity factors
- `HealthFactorsForm`: Form for evaluating health factors affecting longevity
- `LongevityScorecard`: Displays longevity scores
- `LongevityVisualization`: Visualizes longevity assessment results

**Data Structure**:

```typescript
interface LongevityAssessment {
  healthFactors: {
    nutrition: number;
    exercise: number;
    sleep: number;
    stress: number;
    socialConnections: number;
    purposefulActivity: number;
    genetics: number;
  };
  healthMetrics: {
    bloodPressure: string;
    cholesterol: string;
    bloodSugar: string;
    bmi: number;
  };
  healthBehaviors: {
    smoking: string;
    alcohol: string;
    preventiveCare: string;
  };
  overallLongevityScore: number;
  longevityProfile: string;
  longevityGoals: string[];
}
```

**Reuse Strategy**: Integrate with the Seasons of Self concept to provide health context for each life stage.

## 4. Happiness-Longevity Integration

### 4.1 Happiness-Longevity Dashboard

**Location**: `/src/features/HappinessLongevity/components`

**Key Components**:

- `HappinessLongevityDashboard`: Main dashboard component
- `IntegratedAssessment`: Combined assessment of happiness and longevity
- `LifeBalanceVisualization`: Visualizes balance between happiness and longevity
- `ActionPlanGenerator`: Generates action plans based on assessment results

**Data Structure**:

```typescript
interface HappinessLongevityIntegration {
  happinessAssessment: HappinessAssessment;
  longevityAssessment: LongevityAssessment;
  balanceScore: number;
  integratedProfile: string;
  actionPlans: {
    category: string;
    actions: string[];
    timeframe: string;
    impact: string;
  }[];
}
```

**Reuse Strategy**: Adapt to provide a holistic view across all seasons in the Seasons of Self concept.

## 5. Longitudinal Tracking

### 5.1 Progress Tracking

**Location**: `/src/features/LongitudinalTracking`

**Key Components**:

- `ProgressTracker`: Tracks progress over time
- `MilestoneTracker`: Tracks achievement of milestones
- `ProgressVisualization`: Visualizes progress over time
- `TrendAnalyzer`: Analyzes trends in well-being data

**Data Structure**:

```typescript
interface LongitudinalTracking {
  assessments: {
    date: string;
    type: string;
    scores: Record<string, number>;
    notes: string;
  }[];
  milestones: {
    date: string;
    description: string;
    category: string;
    impact: number;
  }[];
  trends: {
    dimension: string;
    direction: string;
    magnitude: number;
    notes: string;
  }[];
}
```

**Reuse Strategy**: Adapt to track progress through the seasons and stages in the Seasons of Self concept.

## 6. Integration Points with Financial Compass

The Well-Being components will be integrated with the Financial Compass components to provide a holistic view of the user's life journey:

1. **Well-Being Impact on Financial Decisions**: How well-being factors influence financial planning
2. **Financial Impact on Well-Being**: How financial decisions affect overall well-being
3. **Integrated Goal Setting**: Aligning financial and well-being goals
4. **Seasonal Transitions**: Using seasonal metaphors to guide transitions in both financial and well-being domains
5. **Holistic Visualization**: Integrated visualizations that show the relationship between financial and well-being factors
