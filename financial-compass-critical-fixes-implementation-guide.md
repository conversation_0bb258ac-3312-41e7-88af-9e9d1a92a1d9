# Financial Compass Critical Fixes Implementation Guide

This guide provides detailed implementation instructions for the highest priority fixes identified in the Financial Compass Audit Report. These critical fixes focus on ensuring 100% calculation accuracy across all forms and summaries.

## 1. Income Calculation Fixes

### 1.1 Income Frequency Conversion Enhancement

**File**: `src/utils/incomeCalculator.ts`

```typescript
/**
 * Income Calculator Utility
 *
 * This utility provides accurate income conversion functions.
 */

// Constants for precise pay period calculations
export const PAY_PERIODS = {
  WEEKLY: 52.1429, // 365.25/7
  BIWEEKLY: 26.0714, // 365.25/14
  SEMI_MONTHLY: 24,
  MONTHLY: 12,
  QUARTERLY: 4,
  ANNUALLY: 1,
};

/**
 * Convert income to annual amount
 *
 * @param amount - Income amount
 * @param frequency - Income frequency
 * @returns Annual income amount
 */
export const convertToAnnual = (amount: number, frequency: string): number => {
  if (isNaN(amount)) return 0;

  switch (frequency.toLowerCase()) {
    case 'weekly':
      return amount * PAY_PERIODS.WEEKLY;
    case 'biweekly':
      return amount * PAY_PERIODS.BIWEEKLY;
    case 'semi-monthly':
      return amount * PAY_PERIODS.SEMI_MONTHLY;
    case 'monthly':
      return amount * PAY_PERIODS.MONTHLY;
    case 'quarterly':
      return amount * PAY_PERIODS.QUARTERLY;
    case 'annually':
    case 'annual':
      return amount;
    default:
      return amount;
  }
};

/**
 * Convert income to monthly amount
 *
 * @param amount - Income amount
 * @param frequency - Income frequency
 * @returns Monthly income amount
 */
export const convertToMonthly = (amount: number, frequency: string): number => {
  const annual = convertToAnnual(amount, frequency);
  return annual / PAY_PERIODS.MONTHLY;
};
```

**Implementation Steps**:

1. Create the new utility file
2. Update `IncomeDetails.tsx` to use this utility
3. Update all other components that perform income conversions

## 2. Tax Calculation Enhancement

### 2.1 Progressive Tax Bracket Implementation

**File**: `src/utils/taxCalculator.ts`

```typescript
/**
 * Tax Calculator Utility
 *
 * This utility provides accurate tax calculations using progressive brackets.
 */

// 2023 Federal Tax Brackets (example - should be updated annually)
export const FEDERAL_TAX_BRACKETS_2023 = {
  SINGLE: [
    { rate: 0.1, upTo: 11000 },
    { rate: 0.12, upTo: 44725 },
    { rate: 0.22, upTo: 95375 },
    { rate: 0.24, upTo: 182100 },
    { rate: 0.32, upTo: 231250 },
    { rate: 0.35, upTo: 578125 },
    { rate: 0.37, upTo: Infinity },
  ],
  MARRIED_JOINT: [
    { rate: 0.1, upTo: 22000 },
    { rate: 0.12, upTo: 89450 },
    { rate: 0.22, upTo: 190750 },
    { rate: 0.24, upTo: 364200 },
    { rate: 0.32, upTo: 462500 },
    { rate: 0.35, upTo: 693750 },
    { rate: 0.37, upTo: Infinity },
  ],
  HEAD_OF_HOUSEHOLD: [
    { rate: 0.1, upTo: 15700 },
    { rate: 0.12, upTo: 59850 },
    { rate: 0.22, upTo: 95350 },
    { rate: 0.24, upTo: 182100 },
    { rate: 0.32, upTo: 231250 },
    { rate: 0.35, upTo: 578100 },
    { rate: 0.37, upTo: Infinity },
  ],
};

// Standard deductions for 2023
export const STANDARD_DEDUCTION_2023 = {
  SINGLE: 13850,
  MARRIED_JOINT: 27700,
  HEAD_OF_HOUSEHOLD: 20800,
};

/**
 * Calculate federal income tax using progressive brackets
 *
 * @param taxableIncome - Taxable income amount
 * @param filingStatus - Filing status (single, married_joint, head_of_household)
 * @returns Federal income tax amount
 */
export const calculateFederalIncomeTax = (
  taxableIncome: number,
  filingStatus: 'SINGLE' | 'MARRIED_JOINT' | 'HEAD_OF_HOUSEHOLD' = 'SINGLE'
): number => {
  if (isNaN(taxableIncome) || taxableIncome <= 0) return 0;

  const brackets = FEDERAL_TAX_BRACKETS_2023[filingStatus];
  let tax = 0;
  let remainingIncome = taxableIncome;
  let previousBracketLimit = 0;

  for (const bracket of brackets) {
    const bracketIncome = Math.min(remainingIncome, bracket.upTo - previousBracketLimit);
    tax += bracketIncome * bracket.rate;
    remainingIncome -= bracketIncome;
    previousBracketLimit = bracket.upTo;

    if (remainingIncome <= 0) break;
  }

  return tax;
};

/**
 * Calculate taxable income after standard deduction
 *
 * @param grossIncome - Gross income amount
 * @param filingStatus - Filing status
 * @returns Taxable income amount
 */
export const calculateTaxableIncome = (
  grossIncome: number,
  filingStatus: 'SINGLE' | 'MARRIED_JOINT' | 'HEAD_OF_HOUSEHOLD' = 'SINGLE'
): number => {
  if (isNaN(grossIncome) || grossIncome <= 0) return 0;

  const standardDeduction = STANDARD_DEDUCTION_2023[filingStatus];
  return Math.max(0, grossIncome - standardDeduction);
};

/**
 * Calculate after-tax income
 *
 * @param grossIncome - Gross income amount
 * @param filingStatus - Filing status
 * @returns After-tax income amount
 */
export const calculateAfterTaxIncome = (
  grossIncome: number,
  filingStatus: 'SINGLE' | 'MARRIED_JOINT' | 'HEAD_OF_HOUSEHOLD' = 'SINGLE'
): number => {
  if (isNaN(grossIncome) || grossIncome <= 0) return 0;

  const taxableIncome = calculateTaxableIncome(grossIncome, filingStatus);
  const federalTax = calculateFederalIncomeTax(taxableIncome, filingStatus);

  // Approximate FICA taxes (Social Security 6.2% up to limit + Medicare 1.45%)
  const socialSecurityTax = Math.min(grossIncome, 160200) * 0.062;
  const medicareTax = grossIncome * 0.0145;

  return grossIncome - federalTax - socialSecurityTax - medicareTax;
};
```

**Implementation Steps**:

1. Create the new utility file
2. Update `IncomeDetails.tsx` to use this utility
3. Add unit tests to verify tax calculations

## 3. Retirement Savings Calculator

### 3.1 Compound Interest Retirement Calculator

**File**: `src/utils/retirementCalculator.ts`

```typescript
/**
 * Retirement Calculator Utility
 *
 * This utility provides accurate retirement savings calculations with compound interest.
 */

/**
 * Calculate future value with compound interest
 *
 * @param principal - Initial investment amount
 * @param monthlyContribution - Monthly contribution amount
 * @param annualRate - Annual interest rate (decimal)
 * @param years - Number of years
 * @returns Future value
 */
export const calculateFutureValue = (
  principal: number,
  monthlyContribution: number,
  annualRate: number,
  years: number
): number => {
  if (isNaN(principal) || isNaN(monthlyContribution) || isNaN(annualRate) || isNaN(years)) {
    return 0;
  }

  const monthlyRate = annualRate / 12;
  const months = years * 12;

  // Future value of initial principal
  const principalFV = principal * Math.pow(1 + monthlyRate, months);

  // Future value of monthly contributions
  let contributionFV = 0;
  if (monthlyRate > 0) {
    contributionFV = monthlyContribution * ((Math.pow(1 + monthlyRate, months) - 1) / monthlyRate);
  } else {
    contributionFV = monthlyContribution * months;
  }

  return principalFV + contributionFV;
};

/**
 * Calculate required monthly savings to reach a goal
 *
 * @param goal - Target amount
 * @param currentSavings - Current savings amount
 * @param annualRate - Annual interest rate (decimal)
 * @param years - Number of years
 * @returns Required monthly contribution
 */
export const calculateRequiredMonthlySavings = (
  goal: number,
  currentSavings: number,
  annualRate: number,
  years: number
): number => {
  if (isNaN(goal) || isNaN(currentSavings) || isNaN(annualRate) || isNaN(years) || years <= 0) {
    return 0;
  }

  const monthlyRate = annualRate / 12;
  const months = years * 12;

  // Future value of current savings
  const savingsFV = currentSavings * Math.pow(1 + monthlyRate, months);

  // Additional amount needed
  const additionalNeeded = goal - savingsFV;

  if (additionalNeeded <= 0) return 0;

  // Calculate required monthly contribution
  if (monthlyRate > 0) {
    return additionalNeeded / ((Math.pow(1 + monthlyRate, months) - 1) / monthlyRate);
  } else {
    return additionalNeeded / months;
  }
};
```

**Implementation Steps**:

1. Create the new utility file
2. Update `RetirementGoals.tsx` to use this utility
3. Add unit tests to verify retirement calculations

## 4. Social Security Benefit Calculator

### 4.1 Accurate Social Security Benefit Calculation

**File**: `src/utils/socialSecurityCalculator.ts`

```typescript
/**
 * Social Security Calculator Utility
 *
 * This utility provides accurate Social Security benefit calculations.
 */

// 2023 Social Security bend points
const BEND_POINTS_2023 = {
  FIRST: 1115,
  SECOND: 6721,
};

// Replacement rates for PIA calculation
const REPLACEMENT_RATES = {
  FIRST: 0.9, // 90% of AIME up to first bend point
  SECOND: 0.32, // 32% of AIME between first and second bend points
  THIRD: 0.15, // 15% of AIME above second bend point
};

/**
 * Calculate Primary Insurance Amount (PIA) using the three-tier formula
 *
 * @param aime - Average Indexed Monthly Earnings
 * @returns Primary Insurance Amount
 */
export const calculatePIA = (aime: number): number => {
  if (isNaN(aime) || aime < 0) return 0;

  // First tier: 90% of AIME up to first bend point
  const firstTier = Math.min(aime, BEND_POINTS_2023.FIRST) * REPLACEMENT_RATES.FIRST;

  // Second tier: 32% of AIME between first and second bend points
  const secondTier =
    Math.max(
      0,
      Math.min(aime - BEND_POINTS_2023.FIRST, BEND_POINTS_2023.SECOND - BEND_POINTS_2023.FIRST)
    ) * REPLACEMENT_RATES.SECOND;

  // Third tier: 15% of AIME above second bend point
  const thirdTier = Math.max(0, aime - BEND_POINTS_2023.SECOND) * REPLACEMENT_RATES.THIRD;

  // Total PIA is the sum of the three tiers
  return firstTier + secondTier + thirdTier;
};

/**
 * Calculate benefit adjustment for early or delayed claiming
 *
 * @param pia - Primary Insurance Amount
 * @param fullRetirementAge - Full retirement age in years and months (e.g., "67" or "66 and 6 months")
 * @param claimingAge - Claiming age in years and months (e.g., "62" or "70")
 * @returns Adjusted benefit amount
 */
export const calculateAdjustedBenefit = (
  pia: number,
  fullRetirementAge: string,
  claimingAge: string
): number => {
  if (isNaN(pia) || pia <= 0) return 0;

  // Parse full retirement age
  const fraMatch = fullRetirementAge.match(/(\d+)(?:\s+and\s+(\d+)\s+months?)?/);
  if (!fraMatch) return pia;

  const fraYears = parseInt(fraMatch[1]);
  const fraMonths = fraMatch[2] ? parseInt(fraMatch[2]) : 0;
  const fraTotalMonths = fraYears * 12 + fraMonths;

  // Parse claiming age
  const claimingMatch = claimingAge.match(/(\d+)(?:\s+and\s+(\d+)\s+months?)?/);
  if (!claimingMatch) return pia;

  const claimingYears = parseInt(claimingMatch[1]);
  const claimingMonths = claimingMatch[2] ? parseInt(claimingMatch[2]) : 0;
  const claimingTotalMonths = claimingYears * 12 + claimingMonths;

  // Calculate difference in months
  const monthDifference = claimingTotalMonths - fraTotalMonths;

  // Early claiming reduction (5/9% per month for first 36 months, 5/12% for additional months)
  if (monthDifference < 0) {
    const firstPeriodMonths = Math.min(36, Math.abs(monthDifference));
    const secondPeriodMonths = Math.max(0, Math.abs(monthDifference) - 36);

    const firstPeriodReduction = (firstPeriodMonths * (5 / 9)) / 100;
    const secondPeriodReduction = (secondPeriodMonths * (5 / 12)) / 100;

    return pia * (1 - firstPeriodReduction - secondPeriodReduction);
  }

  // Delayed retirement credits (8% per year or 2/3% per month)
  if (monthDifference > 0) {
    const delayedCreditRate = (monthDifference * (2 / 3)) / 100;
    return pia * (1 + delayedCreditRate);
  }

  // Claiming at exactly FRA
  return pia;
};
```

**Implementation Steps**:

1. Create the new utility file
2. Update `SocialSecurityPlanning.tsx` to use this utility
3. Add unit tests to verify Social Security calculations

## 5. Financial Health Score Calculation

### 5.1 Enhanced Financial Health Calculator

**File**: `src/utils/financialHealthCalculator.ts`

```typescript
/**
 * Financial Health Calculator Utility
 *
 * This utility provides functions for calculating financial health scores
 * with adaptive weighting based on user circumstances.
 */

/**
 * Financial health category weights - base values
 */
export const BASE_FINANCIAL_HEALTH_WEIGHTS = {
  CASH_FLOW: 0.2, // 20% - Cash flow and savings rate
  DEBT_MANAGEMENT: 0.2, // 20% - Debt-to-income ratio and debt structure
  EMERGENCY_FUND: 0.15, // 15% - Emergency fund adequacy
  NET_WORTH: 0.15, // 15% - Net worth relative to income
  RETIREMENT: 0.15, // 15% - Retirement readiness
  PROTECTION: 0.1, // 10% - Insurance coverage
  ESTATE_PLANNING: 0.05, // 5%  - Estate planning documents
};

/**
 * Get adaptive weights based on user circumstances
 *
 * @param age - User's age
 * @param hasHighDebt - Whether user has high debt
 * @param isRetirementClose - Whether retirement is within 10 years
 * @param hasDependents - Whether user has dependents
 * @returns Adjusted weights for financial health categories
 */
export const getAdaptiveWeights = (
  age: number,
  hasHighDebt: boolean,
  isRetirementClose: boolean,
  hasDependents: boolean
): typeof BASE_FINANCIAL_HEALTH_WEIGHTS => {
  // Start with base weights
  const weights = { ...BASE_FINANCIAL_HEALTH_WEIGHTS };

  // Adjust weights based on age
  if (age < 30) {
    // Younger users: focus more on cash flow and debt management
    weights.CASH_FLOW += 0.05;
    weights.DEBT_MANAGEMENT += 0.05;
    weights.RETIREMENT -= 0.05;
    weights.ESTATE_PLANNING -= 0.05;
  } else if (age >= 50) {
    // Older users: focus more on retirement and estate planning
    weights.RETIREMENT += 0.05;
    weights.ESTATE_PLANNING += 0.05;
    weights.CASH_FLOW -= 0.05;
    weights.DEBT_MANAGEMENT -= 0.05;
  }

  // Adjust for high debt
  if (hasHighDebt) {
    weights.DEBT_MANAGEMENT += 0.1;
    weights.RETIREMENT -= 0.05;
    weights.ESTATE_PLANNING -= 0.05;
  }

  // Adjust for approaching retirement
  if (isRetirementClose) {
    weights.RETIREMENT += 0.1;
    weights.CASH_FLOW -= 0.05;
    weights.NET_WORTH -= 0.05;
  }

  // Adjust for dependents
  if (hasDependents) {
    weights.PROTECTION += 0.05;
    weights.ESTATE_PLANNING += 0.05;
    weights.NET_WORTH -= 0.05;
    weights.CASH_FLOW -= 0.05;
  }

  // Normalize weights to ensure they sum to 1
  const totalWeight = Object.values(weights).reduce((sum, weight) => sum + weight, 0);

  if (totalWeight !== 1) {
    const normalizationFactor = 1 / totalWeight;
    Object.keys(weights).forEach((key) => {
      weights[key as keyof typeof weights] *= normalizationFactor;
    });
  }

  return weights;
};
```

**Implementation Steps**:

1. Update the existing utility file
2. Modify the financial health score calculation to use adaptive weights
3. Update all components that use the financial health calculator

## Testing Strategy

For each of these critical fixes:

1. Create unit tests to verify the accuracy of calculations
2. Test with edge cases (zero values, negative values, extremely large values)
3. Compare results with known examples from financial planning resources
4. Implement integration tests to ensure components work correctly with the new utilities
5. Conduct end-to-end testing of complete financial planning flows
