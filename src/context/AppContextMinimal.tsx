import React, { ReactNode, createContext, useContext, useState, useEffect } from 'react';
import profileService, { UserProfile } from '../features/ProfileManager/services/profileService';
import { hashPassword, verifyPassword } from '../utils/crypto';

// Define user data type
interface UserData {
  id: string;
  name: string;
  email: string;
  role: 'user' | 'admin';
  preferences: {
    notifications: boolean;
    darkMode: boolean;
  };
}

// Define app context value type
interface AppContextValue {
  userData: UserData | null;
  isLoggedIn: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  register: (name: string, email: string, password: string) => Promise<boolean>;
  logout: () => void;
  updatePreferences: (preferences: Partial<UserData['preferences']>) => void;
  hasRole: (role: 'user' | 'admin') => boolean;
}

// Create context
const AppContextInstance = createContext<AppContextValue | undefined>(undefined);

// Provider props
interface AppContextProviderProps {
  children: ReactNode;
}

// Session key for localStorage
const SESSION_KEY = 'lifecompass_session';

// App context provider component
export const AppContextProvider: React.FC<AppContextProviderProps> = ({ children }) => {
  // State
  const [userData, setUserData] = useState<UserData | null>(null);

  // Load session from localStorage on mount
  useEffect(() => {
    const sessionStr = localStorage.getItem(SESSION_KEY);
    if (sessionStr) {
      const session = JSON.parse(sessionStr);
      // Check expiration
      if (session.expiration && Date.now() < session.expiration) {
        setUserData(session.userData);
      } else {
        localStorage.removeItem(SESSION_KEY);
      }
    }
  }, []);

  // Derived state
  const isLoggedIn = userData !== null;

  // Register a new user
  const register = async (name: string, email: string, password: string): Promise<boolean> => {
    try {
      const passwordHash = await hashPassword(password);
      const newProfile = await profileService.createProfile({
        name,
        email,
        passwordHash,
        role: 'user',
        personalInfo: { firstName: name, email },
      });
      // Set session
      const session = {
        userData: {
          id: newProfile.id,
          name: newProfile.name,
          email: newProfile.email,
          role: newProfile.role,
          preferences: { notifications: true, darkMode: false },
        },
        expiration: Date.now() + 1000 * 60 * 60 * 24, // 24h
      };
      setUserData(session.userData);
      localStorage.setItem(SESSION_KEY, JSON.stringify(session));
      return true;
    } catch (e) {
      return false;
    }
  };

  // Login with email and password
  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      const profiles = await profileService.getAllProfiles();
      const user = profiles.find((p) => p.email === email);
      if (!user) return false;
      const valid = await verifyPassword(password, user.passwordHash);
      if (!valid) return false;
      // Set session
      const session = {
        userData: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          preferences: { notifications: true, darkMode: false },
        },
        expiration: Date.now() + 1000 * 60 * 60 * 24, // 24h
      };
      setUserData(session.userData);
      localStorage.setItem(SESSION_KEY, JSON.stringify(session));
      return true;
    } catch (e) {
      return false;
    }
  };

  // Logout
  const logout = () => {
    setUserData(null);
    localStorage.removeItem(SESSION_KEY);
  };

  // Update preferences
  const updatePreferences = (preferences: Partial<UserData['preferences']>) => {
    if (userData) {
      const updated = {
        ...userData,
        preferences: {
          ...userData.preferences,
          ...preferences,
        },
      };
      setUserData(updated);
      // Update session in localStorage
      const sessionStr = localStorage.getItem(SESSION_KEY);
      if (sessionStr) {
        const session = JSON.parse(sessionStr);
        session.userData = updated;
        localStorage.setItem(SESSION_KEY, JSON.stringify(session));
      }
    }
  };

  // Context value
  const contextValue: AppContextValue = {
    userData,
    isLoggedIn,
    login,
    register,
    logout,
    updatePreferences,
    hasRole: (role: 'user' | 'admin') => userData?.role === role,
  };

  return <AppContextInstance.Provider value={contextValue}>{children}</AppContextInstance.Provider>;
};

// Hook to use app context
export const useAppContext = () => {
  const context = useContext(AppContextInstance);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppContextProvider');
  }
  return context;
};

// Export app context
const AppContextMinimal = {
  Provider: AppContextProvider,
};

export default AppContextMinimal;
