/**
 * Theme Context
 *
 * This context provides theme state and management for the application.
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Theme } from '../types/theme';
import { getTheme } from '../theme/themeUtils';

interface ThemeContextType {
  theme: Theme;
  mode: 'light' | 'dark';
  season: 'spring' | 'summer' | 'autumn' | 'winter';
  setMode: (mode: 'light' | 'dark') => void;
  setSeason: (season: 'spring' | 'summer' | 'autumn' | 'winter') => void;
  toggleMode: () => void;
}

interface ThemeProviderProps {
  children: ReactNode;
  initialMode?: 'light' | 'dark';
  initialSeason?: 'spring' | 'summer' | 'autumn' | 'winter';
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

/**
 * Theme Provider Component
 *
 * Provides theme state and management for the application.
 */
export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  initialMode = 'light',
  initialSeason = 'spring',
}) => {
  // Get stored preferences or use defaults
  const [mode, setMode] = useState<'light' | 'dark'>(() => {
    const storedMode = localStorage.getItem('themeMode');
    return (storedMode as 'light' | 'dark') || initialMode;
  });

  const [season, setSeason] = useState<'spring' | 'summer' | 'autumn' | 'winter'>(() => {
    const storedSeason = localStorage.getItem('themeSeason');
    return (storedSeason as 'spring' | 'summer' | 'autumn' | 'winter') || initialSeason;
  });

  // Generate theme based on current mode and season
  const [theme, setTheme] = useState<Theme>(getTheme(mode, season));

  // Update theme when mode or season changes
  useEffect(() => {
    setTheme(getTheme(mode, season));
    localStorage.setItem('themeMode', mode);
    localStorage.setItem('themeSeason', season);
  }, [mode, season]);

  // Toggle between light and dark mode
  const toggleMode = () => {
    setMode((prevMode) => (prevMode === 'light' ? 'dark' : 'light'));
  };

  const value = {
    theme,
    mode,
    season,
    setMode,
    setSeason,
    toggleMode,
  };

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>;
};

/**
 * Hook to use the theme context
 *
 * @returns The theme context
 * @throws Error if used outside of ThemeProvider
 */
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);

  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }

  return context;
};
