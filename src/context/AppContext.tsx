/**
 * App Context
 *
 * This context combines all the other contexts in the application.
 * Enhanced with improved theme provider and toast notifications.
 */

import React, { ReactNode } from 'react';
import { ToastProvider } from '../components/ui/ToastProvider';
import { FinancialCompassProvider } from '../features/FinancialCompass/context/FinancialCompassContext';
import { SeasonsOfSelfProvider } from '../features/SeasonsOfSelf/context/SeasonsOfSelfContext';
import { DataPortabilityProvider } from '../features/DataPortability/context/DataPortabilityContext';
import { FinancialConnectionsProvider } from '../features/FinancialConnections/context/FinancialConnectionsContext';
import { ProfileProvider } from '../features/ProfileManager/context/ProfileContext';
import { GuidedJourneyProvider } from '../features/GuidedJourney/context/GuidedJourneyContext';
import { AppContextProvider } from '../context/AppContextMinimal';

/**
 * App provider props
 */
interface AppProviderProps {
  children: ReactNode;
}

/**
 * App provider
 *
 * This component combines all the context providers in the application.
 * The order of providers is important - providers higher in the tree
 * can be used by providers lower in the tree.
 */
export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  return (
    <AppContextProvider>
      <ToastProvider>
        <ProfileProvider>
          <DataPortabilityProvider>
            <FinancialConnectionsProvider>
              <FinancialCompassProvider>
                <SeasonsOfSelfProvider>
                  <GuidedJourneyProvider>{children}</GuidedJourneyProvider>
                </SeasonsOfSelfProvider>
              </FinancialCompassProvider>
            </FinancialConnectionsProvider>
          </DataPortabilityProvider>
        </ProfileProvider>
      </ToastProvider>
    </AppContextProvider>
  );
};

/**
 * App context
 *
 * This component provides a convenient way to access all the contexts in the application.
 */
const AppContext = {
  Provider: AppProvider,
};

export default AppContext;
