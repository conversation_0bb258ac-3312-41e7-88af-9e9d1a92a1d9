// Theme configuration for LifeCompass
// This file defines the design tokens used throughout the application

export type Season = 'spring' | 'summer' | 'autumn' | 'winter';
export type ThemeMode = 'light' | 'dark';

interface SeasonColors {
  primary: string;
  secondary: string;
  accent: string;
  background: {
    light: string;
    dark: string;
  };
}

interface NeutralColors {
  background: string;
  surface: string;
  primaryText: string;
  secondaryText: string;
  divider: string;
  card: string;
}

interface SemanticColors {
  success: string;
  warning: string;
  error: string;
  info: string;
}

interface PaletteColor {
  light: string;
  main: string;
  dark: string;
  contrastText: string;
}

interface BackgroundColors {
  default: string;
  paper: string;
  elevated: string;
}

interface TextColors {
  primary: string;
  secondary: string;
  disabled: string;
  hint: string;
}

interface ActionColors {
  active: string;
  hover: string;
  selected: string;
  disabled: string;
  disabledBackground: string;
}

interface Typography {
  fontFamily: {
    primary: string;
    secondary: string;
    tertiary: string;
  };
  fontSize: {
    display: string;
    h1: string;
    h2: string;
    h3: string;
    heading2: string;
    heading3: string;
    bodyLarge: string;
    body: string;
    bodySmall: string;
    caption: string;
  };
  fontWeight: {
    light: number;
    regular: number;
    medium: number;
    bold: number;
  };
  lineHeight: {
    tight: number;
    normal: number;
    relaxed: number;
  };
}

interface Spacing {
  micro: string;
  tiny: string;
  small: string;
  medium: string;
  large: string;
  xLarge: string;
  xxLarge: string;
  huge: string;
}

interface BorderRadius {
  small: string;
  medium: string;
  large: string;
  circular: string;
  sm: string;
  md: string;
  lg: string;
  full: string;
}

interface Shadows {
  subtle: string;
  medium: string;
  strong: string;
  dramatic: string;
  sm: string;
  md: string;
  lg: string;
  large: string;
}

interface ZIndex {
  mobileStepper: number;
  appBar: number;
  drawer: number;
  modal: number;
  snackbar: number;
  tooltip: number;
}

export interface Theme {
  mode: ThemeMode;
  season: Season;
  colors: {
    seasons: Record<Season, SeasonColors>;
    neutral: NeutralColors;
    semantic: SemanticColors;
    primary: PaletteColor;
    secondary: PaletteColor;
    error: PaletteColor;
    warning: PaletteColor;
    info: PaletteColor;
    success: PaletteColor;
    background: BackgroundColors;
    text: TextColors;
    action: ActionColors;
    divider: string;
  };
  typography: Typography;
  spacing: Spacing;
  borderRadius: BorderRadius;
  shadows: Shadows;
  zIndex: ZIndex;
}

// Season colors
const seasonColors: Record<Season, SeasonColors> = {
  spring: {
    primary: '#4CAF50',
    secondary: '#8BC34A',
    accent: '#FFEB3B',
    background: {
      light: '#F1F8E9',
      dark: '#1B5E20',
    },
  },
  summer: {
    primary: '#2196F3',
    secondary: '#03A9F4',
    accent: '#FF9800',
    background: {
      light: '#E3F2FD',
      dark: '#0D47A1',
    },
  },
  autumn: {
    primary: '#FF5722',
    secondary: '#FF9800',
    accent: '#795548',
    background: {
      light: '#FBE9E7',
      dark: '#BF360C',
    },
  },
  winter: {
    primary: '#9C27B0',
    secondary: '#673AB7',
    accent: '#00BCD4',
    background: {
      light: '#F3E5F5',
      dark: '#4A148C',
    },
  },
};

// Neutral colors
const neutralColors: Record<ThemeMode, NeutralColors> = {
  light: {
    background: '#FFFFFF',
    surface: '#F5F5F5',
    primaryText: '#212121',
    secondaryText: '#757575',
    divider: '#BDBDBD',
    card: '#FFFFFF',
  },
  dark: {
    background: '#121212',
    surface: '#1E1E1E',
    primaryText: '#FFFFFF',
    secondaryText: '#B0B0B0',
    divider: '#333333',
    card: '#1E1E1E',
  },
};

// Semantic colors
const semanticColors: SemanticColors = {
  success: '#4CAF50',
  warning: '#FFC107',
  error: '#F44336',
  info: '#2196F3',
};

// Palette colors
const createPaletteColors = (
  mode: ThemeMode,
  season: Season
): {
  primary: PaletteColor;
  secondary: PaletteColor;
  error: PaletteColor;
  warning: PaletteColor;
  info: PaletteColor;
  success: PaletteColor;
} => {
  const seasonColor = seasonColors[season];

  return {
    primary: {
      light:
        mode === 'light'
          ? lightenColor(seasonColor.primary, 0.2)
          : lightenColor(seasonColor.primary, 0.3),
      main: seasonColor.primary,
      dark:
        mode === 'light'
          ? darkenColor(seasonColor.primary, 0.2)
          : darkenColor(seasonColor.primary, 0.3),
      contrastText: mode === 'light' ? '#FFFFFF' : '#000000',
    },
    secondary: {
      light:
        mode === 'light'
          ? lightenColor(seasonColor.secondary, 0.2)
          : lightenColor(seasonColor.secondary, 0.3),
      main: seasonColor.secondary,
      dark:
        mode === 'light'
          ? darkenColor(seasonColor.secondary, 0.2)
          : darkenColor(seasonColor.secondary, 0.3),
      contrastText: mode === 'light' ? '#000000' : '#FFFFFF',
    },
    error: {
      light: '#E57373',
      main: '#F44336',
      dark: '#D32F2F',
      contrastText: '#FFFFFF',
    },
    warning: {
      light: '#FFB74D',
      main: '#FFA726',
      dark: '#F57C00',
      contrastText: '#000000',
    },
    info: {
      light: '#64B5F6',
      main: '#2196F3',
      dark: '#1976D2',
      contrastText: '#FFFFFF',
    },
    success: {
      light: '#81C784',
      main: '#4CAF50',
      dark: '#388E3C',
      contrastText: '#FFFFFF',
    },
  };
};

// Background colors
const backgroundColors: Record<ThemeMode, BackgroundColors> = {
  light: {
    default: '#FAFAFA',
    paper: '#FFFFFF',
    elevated: '#F5F5F5',
  },
  dark: {
    default: '#121212',
    paper: '#1E1E1E',
    elevated: '#2C2C2C',
  },
};

// Text colors
const textColors: Record<ThemeMode, TextColors> = {
  light: {
    primary: '#212121',
    secondary: '#757575',
    disabled: '#9E9E9E',
    hint: '#9E9E9E',
  },
  dark: {
    primary: '#FFFFFF',
    secondary: '#B0B0B0',
    disabled: '#6C6C6C',
    hint: '#6C6C6C',
  },
};

// Action colors
const actionColors: Record<ThemeMode, ActionColors> = {
  light: {
    active: 'rgba(0, 0, 0, 0.54)',
    hover: 'rgba(0, 0, 0, 0.04)',
    selected: 'rgba(0, 0, 0, 0.08)',
    disabled: 'rgba(0, 0, 0, 0.26)',
    disabledBackground: 'rgba(0, 0, 0, 0.12)',
  },
  dark: {
    active: 'rgba(255, 255, 255, 0.7)',
    hover: 'rgba(255, 255, 255, 0.08)',
    selected: 'rgba(255, 255, 255, 0.16)',
    disabled: 'rgba(255, 255, 255, 0.3)',
    disabledBackground: 'rgba(255, 255, 255, 0.12)',
  },
};

// Typography
const typography: Record<ThemeMode, Typography> = {
  light: {
    fontFamily: {
      primary: '"Playfair Display", serif',
      secondary: '"Lato", sans-serif',
      tertiary: '"Montserrat", sans-serif',
    },
    fontSize: {
      display: '3rem', // 48px
      h1: '2rem', // 32px
      h2: '1.5rem', // 24px
      h3: '1.25rem', // 20px
      heading2: '1.5rem', // 24px
      heading3: '1.25rem', // 20px
      bodyLarge: '1.125rem', // 18px
      body: '1rem', // 16px
      bodySmall: '0.875rem', // 14px
      caption: '0.75rem', // 12px
    },
    fontWeight: {
      light: 300,
      regular: 400,
      medium: 500,
      bold: 700,
    },
    lineHeight: {
      tight: 1.2,
      normal: 1.5,
      relaxed: 1.8,
    },
  },
  dark: {
    fontFamily: {
      primary: '"Playfair Display", serif',
      secondary: '"Lato", sans-serif',
      tertiary: '"Montserrat", sans-serif',
    },
    fontSize: {
      display: '3.5rem', // 56px
      h1: '2.25rem', // 36px
      h2: '1.75rem', // 28px
      h3: '1.375rem', // 22px
      heading2: '1.75rem', // 28px
      heading3: '1.375rem', // 22px
      bodyLarge: '1.25rem', // 20px
      body: '1.125rem', // 18px
      bodySmall: '1rem', // 16px
      caption: '0.875rem', // 14px
    },
    fontWeight: {
      light: 300,
      regular: 400,
      medium: 500,
      bold: 700,
    },
    lineHeight: {
      tight: 1.2,
      normal: 1.5,
      relaxed: 1.8,
    },
  },
};

// Spacing
const spacing: Spacing = {
  micro: '0.25rem', // 4px
  tiny: '0.5rem', // 8px
  small: '1rem', // 16px
  medium: '1.5rem', // 24px
  large: '2rem', // 32px
  xLarge: '3rem', // 48px
  xxLarge: '4rem', // 64px
  huge: '6rem', // 96px
};

// Border radius
const borderRadius: BorderRadius = {
  small: '0.25rem', // 4px
  medium: '0.5rem', // 8px
  large: '1rem', // 16px
  circular: '50%',
  sm: '0.25rem', // 4px
  md: '0.5rem', // 8px
  lg: '1rem', // 16px
  full: '9999px',
};

// Shadows
const shadows: Record<ThemeMode, Shadows> = {
  light: {
    subtle: '0 2px 4px rgba(0,0,0,0.05)',
    medium: '0 4px 8px rgba(0,0,0,0.1)',
    strong: '0 8px 16px rgba(0,0,0,0.15)',
    dramatic: '0 16px 32px rgba(0,0,0,0.2)',
    sm: '0 2px 4px rgba(0,0,0,0.05)',
    md: '0 4px 8px rgba(0,0,0,0.1)',
    lg: '0 8px 16px rgba(0,0,0,0.15)',
    large: '0 8px 16px rgba(0,0,0,0.15)',
  },
  dark: {
    subtle: '0 2px 4px rgba(0,0,0,0.2)',
    medium: '0 4px 8px rgba(0,0,0,0.3)',
    strong: '0 8px 16px rgba(0,0,0,0.4)',
    dramatic: '0 16px 32px rgba(0,0,0,0.5)',
    sm: '0 2px 4px rgba(0,0,0,0.2)',
    md: '0 4px 8px rgba(0,0,0,0.3)',
    lg: '0 8px 16px rgba(0,0,0,0.4)',
    large: '0 8px 16px rgba(0,0,0,0.4)',
  },
};

// Z-index
const zIndex: ZIndex = {
  mobileStepper: 1000,
  appBar: 1100,
  drawer: 1200,
  modal: 1300,
  snackbar: 1400,
  tooltip: 1500,
};

// Helper functions for color manipulation
function lightenColor(color: string, amount: number): string {
  // Simple lightening function - in a real app, use a proper color library
  return color; // Placeholder
}

function darkenColor(color: string, amount: number): string {
  // Simple darkening function - in a real app, use a proper color library
  return color; // Placeholder
}

// Create theme
export const createTheme = (mode: ThemeMode = 'light', season: Season = 'spring'): Theme => {
  const paletteColors = createPaletteColors(mode, season);

  return {
    mode,
    season,
    colors: {
      seasons: seasonColors,
      neutral: neutralColors[mode],
      semantic: semanticColors,
      ...paletteColors,
      background: backgroundColors[mode],
      text: textColors[mode],
      action: actionColors[mode],
      divider: neutralColors[mode].divider,
    },
    typography: typography[mode],
    spacing,
    borderRadius,
    shadows: shadows[mode],
    zIndex,
  };
};

// Default theme
export const defaultTheme = createTheme('light', 'spring');

// Theme context will be implemented in a separate file
