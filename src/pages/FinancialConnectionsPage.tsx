/**
 * Financial Connections Page
 *
 * This page displays the Financial Connections Manager, allowing users to
 * connect, manage, and view their financial institution connections.
 */

import React from 'react';
import styled from 'styled-components';
import { FinancialConnectionsManager } from '../features/FinancialConnections';
import Header from '../components/layout/Header';

// Styled Components
const PageContainer = styled.div`
  min-height: 100vh;
  background-color: ${({ theme }) => theme.colors.background.default};
`;

const ContentContainer = styled.div`
  padding-top: 2rem;
  padding-bottom: 4rem;
`;

/**
 * Financial Connections Page Component
 */
const FinancialConnectionsPage: React.FC = () => {
  return (
    <PageContainer>
      <Header />
      <ContentContainer>
        <FinancialConnectionsManager />
      </ContentContainer>
    </PageContainer>
  );
};

export default FinancialConnectionsPage;
