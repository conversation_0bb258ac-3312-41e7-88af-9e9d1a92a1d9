/**
 * West Direction Types
 *
 * This file contains types for the West Direction components of the Financial Compass.
 */

/**
 * Tax Planning Data
 */
export interface TaxPlanningData {
  hasTaxStrategy?: string;
  usesTaxDeferredAccounts?: string;
  usesRothAccounts?: string;
  hasTaxLossHarvesting?: string;
  income?: number;
  filingStatus?: 'single' | 'married' | 'headOfHousehold';
  retirementContribution?: number;
  retirementBalance?: number;
  age?: number;
  capitalGains?: number;
  estateValue?: number;
}

/**
 * Tax Results Data
 */
export interface TaxResultsData {
  currentTax: number;
  retirementSavings: number;
  rmd: number;
  capitalGainsTax: number;
  estateTax: number;
  effectiveTaxRate: number;
}

/**
 * Core Value
 */
export interface CoreValue {
  id: string;
  value: string;
  description: string;
  importance: number;
}

/**
 * Life Goal
 */
export interface LifeGoal {
  id: string;
  goal: string;
  category: string;
  targetDate: string;
  status: 'not_started' | 'in_progress' | 'completed';
  notes: string;
}

/**
 * Values and Goals Data
 */
export interface ValuesGoalsData {
  coreValues: CoreValue[];
  lifeGoals: LifeGoal[];
  personalMission: string;
  valuesNotes: string;
  values?: Array<{
    id: string;
    value: string;
    description: string;
  }>;
}

/**
 * Legacy Goal
 */
export interface LegacyGoal {
  id: string;
  goal: string;
  priority: 'high' | 'medium' | 'low';
  timeframe: 'short_term' | 'medium_term' | 'long_term';
  notes: string;
}

/**
 * Legacy Planning Data
 */
export interface LegacyPlanningData {
  hasLegacyVision: string;
  hasLegacyLetter: string;
  hasLegacyPlan?: boolean;
  legacyGoals?: LegacyGoal[];
  legacyStatement?: string;
  legacyLetter?: string;
  legacyNotes?: string;
}

/**
 * Document
 */
export interface Document {
  id: string;
  type: string;
  status: 'completed' | 'in_progress' | 'not_started';
  location: string;
  notes: string;
  lastUpdated: string;
}

/**
 * Beneficiary
 */
export interface Beneficiary {
  id: string;
  name: string;
  relationship: string;
  assetType: string;
  percentage: number;
  notes: string;
}

/**
 * Estate Planning Data
 */
export interface EstatePlanningData {
  hasEstateStrategy?: string;
  hasSuccessionPlan?: string;
  hasWill?: boolean;
  hasTrust?: boolean;
  hasPowerOfAttorney?: boolean;
  hasHealthcareDirective?: boolean;
  documents?: Document[];
  beneficiaries?: Beneficiary[];
  estateNotes?: string;
}

/**
 * Charity
 */
export interface Charity {
  id: string;
  name: string;
  category: string;
  annualAmount: number;
  frequency: 'one_time' | 'monthly' | 'quarterly' | 'annual';
  taxDeductible: boolean;
  notes: string;
}

/**
 * Charitable Giving Data
 */
export interface CharitableGivingData {
  annualGiving: number;
  plannedBequest: string;
  hasCharitableStrategy: string;
  hasGivingPlan?: boolean;
  annualGivingTarget?: number;
  givingPercentage?: number;
  charities?: Charity[];
  givingVehicles?: {
    directDonations: boolean;
    donorAdvisedFund: boolean;
    charitableTrust: boolean;
    foundationGiving: boolean;
    stockDonations: boolean;
    qcd: boolean;
  };
  givingNotes?: string;
}

/**
 * Estate Documents Data
 */
export interface EstateDocumentsData {
  will?: string;
  trust?: string;
  powerOfAttorney?: string;
  advanceDirective?: string;

  // Detailed document data (optional)
  // Personal Information
  fullName?: string;
  address?: string;
  phone?: string;
  email?: string;
  dateOfBirth?: string;

  // Will Information
  executor?: string;
  alternateExecutor?: string;
  beneficiaries?: Array<{
    name: string;
    relationship: string;
    percentage: string;
  }>;
  guardianForMinors?: string;
  specificBequests?: Array<{
    item: string;
    recipient: string;
  }>;

  // Healthcare Directive Information
  healthcareAgent?: string;
  alternateHealthcareAgent?: string;
  lifeSupportPreferences?: string;
  painManagementPreferences?: string;
  organDonationPreferences?: string;

  // Power of Attorney Information
  attorneyInFact?: string;
  alternateAttorneyInFact?: string;
  powersGranted?: string[];
  effectiveDate?: 'immediate' | 'incapacity';

  // Letter of Intent
  letterContent?: string;
}

/**
 * Legacy Message
 */
export interface MessageData {
  id: string;
  title: string;
  recipient: string;
  textContent?: string;
  audioUrl?: string;
  audioBlob?: Blob;
  duration?: number;
  createdAt: Date | string;
  updatedAt: Date | string;
}

/**
 * West Direction Data
 */
export interface WestDirectionData {
  taxPlanning: TaxPlanningData;
  estatePlanning: EstatePlanningData;
  estateDocuments: EstateDocumentsData;
  charitableGiving: CharitableGivingData;
  legacyPlanning: LegacyPlanningData;
  taxResults?: TaxResultsData;
  valuesGoals?: ValuesGoalsData;
  legacyMessages?: MessageData[];
}
