/**
 * South Direction Types
 *
 * This file contains types for the South Direction components of the Financial Compass.
 */

/**
 * Insurance Coverage Data
 */
export interface InsuranceCoverageData {
  lifeInsurance: {
    coverage: number;
    type?: string;
    policyType?: string;
    beneficiaries?: string[];
    premiumAmount?: number;
    premiumFrequency?: string;
    provider?: string;
    policyNumber?: string;
    notes?: string;
  };
  disabilityInsurance: {
    coverage: number;
    type?: string;
    policyType?: string;
    benefitAmount?: number;
    benefitPeriod?: number;
    eliminationPeriod?: number;
    premiumAmount?: number;
    premiumFrequency?: string;
    provider?: string;
    policyNumber?: string;
    notes?: string;
  };
  healthInsurance: {
    coverage: number;
    type?: string;
    policyType?: string;
    deductible?: number;
    outOfPocketMax?: number;
    premiumAmount?: number;
    premiumFrequency?: string;
    provider?: string;
    policyNumber?: string;
    notes?: string;
  };
  homeownersInsurance?: {
    coverage?: number;
    type?: string;
    policyType?: string;
    deductible?: number;
    premiumAmount?: number;
    premiumFrequency?: string;
    provider?: string;
    policyNumber?: string;
    notes?: string;
  };
  autoInsurance?: {
    coverage?: number;
    type?: string;
    policyType?: string;
    deductible?: number;
    premiumAmount?: number;
    premiumFrequency?: string;
    provider?: string;
    policyNumber?: string;
    notes?: string;
  };
  umbrellaInsurance?: {
    coverage?: number;
    type?: string;
    policyType?: string;
    deductible?: number;
    premiumAmount?: number;
    premiumFrequency?: string;
    provider?: string;
    policyNumber?: string;
    notes?: string;
  };
  liabilityInsurance?: {
    coverage?: number;
    type?: string;
    policyType?: string;
    deductible?: number;
    premiumAmount?: number;
    premiumFrequency?: string;
    provider?: string;
    policyNumber?: string;
    notes?: string;
  };
  // Additional fields for compatibility with existing components
  policies?: any[];
  hasLifeInsurance?: boolean;
  hasHealthInsurance?: boolean;
  hasAutoInsurance?: boolean;
  hasHomeInsurance?: boolean;
  hasDisabilityInsurance?: boolean;
  hasLongTermCareInsurance?: boolean;
  totalAnnualPremium?: number;
}

/**
 * Healthcare Cost Data
 */
export interface HealthcareCostData {
  currentAge: string;
  retirementAge: string;
  lifeExpectancy: string;
  annualInflation: string;
  currentAnnualHealthcareCost: string;
  medicareStartAge: string;
  medicarePartBPremium: string;
  medicarePartDPremium: string;
  medicareSupplementPremium: string;
  outOfPocketCosts: string;
  longTermCareStartAge: string;
  longTermCareCost: string;
  longTermCareDuration: string;
}

/**
 * Healthcare Planning Data
 */
export interface HealthcarePlanningData {
  currentAnnualHealthcareExpenses: string;
  expectedRetirementHealthcareExpenses: string;
  hasMedicareSupplementalPlan: boolean;
  hasHealthSavingsAccount: boolean;
  hsaBalance: string;
  hsaAnnualContribution: string;
  hasLongTermCareInsurance: boolean;
  longTermCareMonthlyBenefit: string;
  longTermCarePremium: string;
  healthcareNotes: string;
  primaryProvider?: string;
}

/**
 * Insurance Policy
 */
export interface InsurancePolicy {
  id: string;
  type: string;
  provider: string;
  policyNumber: string;
  coverageAmount: string;
  premium: string;
  frequency: 'monthly' | 'quarterly' | 'semi-annual' | 'annual';
  deductible: string;
  beneficiaries: string;
  notes: string;
  renewalDate: string;
  coverage?: string; // For backward compatibility
}

/**
 * Calculator Data
 */
export interface CalculatorData {
  // Life Insurance
  annualIncome: number;
  yearsToReplace: number;
  outstandingDebt: number;
  educationFunds: number;
  finalExpenses: number;
  existingLifeInsurance: number;
  liquidAssets: number;

  // Disability Insurance
  monthlyIncome: number;
  monthlyExpenses: number;
  existingDisabilityInsurance: number;
  otherIncomeSources: number;

  // Long-Term Care Insurance
  monthlyCareExpense: number;
  yearsOfCare: number;
  existingLTCInsurance: number;
  monthlyRetirementIncome: number;
  availableAssets: number;
}

/**
 * Coverage Gap
 */
export interface CoverageGap {
  type: string;
  recommendation: string;
  priority: 'high' | 'medium' | 'low';
  hasGap: boolean;
  notes: string;
  id?: string; // For backward compatibility
  currentCoverage?: string; // For backward compatibility
  recommendedCoverage?: string; // For backward compatibility
  gap?: string; // For backward compatibility
}

/**
 * Protection Gap Data
 */
export interface ProtectionGapData {
  coverageGaps: CoverageGap[];
  lifeInsuranceNeeded: number;
  disabilityInsuranceNeeded: number;
  emergencyFundNeeded: number;
  longTermCareNeeded: number;
  additionalNotes: string;
  totalGap?: number;
}

/**
 * Risk Question
 */
export interface RiskQuestion {
  id: string;
  question: string;
  options: {
    value: number;
    text: string;
  }[];
  answer: number | null;
  category?: 'risk_tolerance' | 'risk_capacity' | 'behavioral' | 'knowledge';
}

/**
 * Risk Tolerance Data
 */
export interface RiskToleranceData {
  questions: RiskQuestion[];
  score: number;
  riskProfile:
    | 'conservative'
    | 'moderately_conservative'
    | 'moderate'
    | 'moderately_aggressive'
    | 'aggressive';
  additionalNotes: string;
  // Enhanced risk assessment with separate scores for each category
  riskToleranceScore?: number;
  riskCapacityScore?: number;
  behavioralScore?: number;
  knowledgeScore?: number;
  // Potential risk misalignment (when tolerance and capacity differ significantly)
  hasMisalignment?: boolean;
  misalignmentNotes?: string;
}

/**
 * South Direction Data
 */
export interface SouthDirectionData {
  insuranceCoverage: InsuranceCoverageData;
  healthcareCostProjections: HealthcareCostData;
  healthcarePlanning: HealthcarePlanningData;
  insuranceCalculator: CalculatorData;
  protectionGap: ProtectionGapData;
  riskTolerance: RiskToleranceData;
  insuranceResults?: Record<string, unknown>;
}
