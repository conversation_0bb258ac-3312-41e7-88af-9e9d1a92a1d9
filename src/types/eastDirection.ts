/**
 * East Direction Types
 *
 * This file contains types for the East Direction components of the Financial Compass.
 */

/**
 * Retirement Goals Data
 */
export interface RetirementGoalsData {
  targetRetirementAge: number;
  currentAge?: number;
  lifeExpectancy: number;
  targetMonthlyIncome: number;
  currentRetirementSavings: number;
  annualContribution: number;
  expectedReturnRate: number;
  savingsGoal?: number;
  desiredAnnualIncome?: number;
  retirementLifestyle?: string;
  retirementLocation?: string;
  retirementActivities?: string[];
  priorityGoals?: string[];
}

/**
 * Retirement Income Source
 */
export interface RetirementIncomeSource {
  id: string;
  type: string;
  amount: number;
  frequency: string;
  startAge: number;
  endAge: number;
  notes: string;
}

/**
 * Retirement Income Data
 */
export interface RetirementIncomeData {
  socialSecurity: number;
  pension: number;
  annuities: number;
  investments: number;
  otherIncome: number;
  detailedSocialSecurity?: {
    estimatedMonthlyBenefit: number;
    startAge: number;
  };
  detailedPension?: {
    hasEmployerPension: boolean;
    estimatedMonthlyBenefit: number;
    startAge: number;
  };
  incomeSources?: RetirementIncomeSource[];
}

/**
 * Retirement Expense Category
 */
export interface RetirementExpenseCategory {
  id: string;
  name: string;
  monthlyAmount: number;
  isEssential: boolean;
  notes: string;
}

/**
 * Retirement Expenses Data
 */
export interface RetirementExpensesData {
  housingExpense: number;
  healthcareExpense?: number;
  foodExpense?: number;
  transportationExpense?: number;
  travelExpense?: number;
  entertainmentExpense?: number;
  otherExpense?: number;
  expenseCategories?: RetirementExpenseCategory[];
  housingPlan?: string;
  healthcareCosts?: string;
  travelBudget?: number;
  hobbiesBudget?: number;
  inflationAssumption?: number;
}

/**
 * Timeline Event
 */
export interface TimelineEvent {
  id: string;
  age: number;
  year: number;
  event: string;
  description: string;
  type: string;
}

/**
 * Retirement Timeline Data
 */
export interface RetirementTimelineData {
  events: TimelineEvent[];
}

/**
 * Retirement Accounts Data
 */
export interface RetirementAccountsData {
  totalBalance: number;
  accounts?: Array<{
    id: string;
    name: string;
    type: string;
    balance: number;
    annualContribution: number;
    expectedReturn: number;
  }>;
}

/**
 * Social Security Planning Data
 */
export interface SocialSecurityPlanningData {
  selectedClaimingAge: number;
  birthYear?: number;
  fullRetirementAge?: number;
  earlyClaimingAge?: number;
  delayedClaimingAge?: number;
  estimatedMonthlyBenefitAtFRA?: number;
  earlyClaimingReduction?: number;
  delayedClaimingIncrease?: number;
  spouseBenefit?: number;
  hasWorkingHistory?: boolean;
  yearsOfCoveredEmployment?: number;
  averageIndexedMonthlyEarnings?: number;
  primaryInsuranceAmount?: number;
  maximizationStrategy?: string;
  notes?: string;
}

/**
 * East Direction Data
 */
export interface EastDirectionData {
  retirementGoals: RetirementGoalsData;
  retirementIncome: RetirementIncomeData;
  retirementExpenses: RetirementExpensesData;
  retirementTimeline: RetirementTimelineData;
  retirementAccounts: RetirementAccountsData;
  socialSecurityPlanning?: SocialSecurityPlanningData;
}
