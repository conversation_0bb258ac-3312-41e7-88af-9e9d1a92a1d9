/**
 * North Direction Types
 *
 * This file contains types for the North Direction components of the Financial Compass.
 */

/**
 * Personal Information Data
 */
export interface PersonalInformationData {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  email: string;
  phone: string;
  gender?: string;
  maritalStatus?: string;
  annualIncome?: number;
  age?: number; // Added for age-based calculations
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  occupation: string;
  employmentStatus: 'employed' | 'self-employed' | 'unemployed' | 'retired' | 'student';
  // Additional fields for compatibility with existing components
  fullName?: string;
}

/**
 * Family Member
 */
export interface FamilyMember {
  id: string;
  firstName: string;
  lastName: string;
  relationship: string;
  dateOfBirth?: string;
  isDependent: boolean;
  occupation?: string;
  notes?: string;
}

/**
 * Family Information Data
 */
export interface FamilyInformationData {
  maritalStatus: string;
  familyMembers: FamilyMember[];
  spouseFirstName?: string;
  spouseLastName?: string;
  spouseDateOfBirth?: string;
  spouseOccupation?: string;
}

/**
 * Income Source
 */
export interface IncomeSource {
  id: string;
  type: string;
  amount: number;
  frequency: string;
  description?: string;
  name?: string;
  isPassive?: boolean;
  notes?: string;
}

/**
 * Income Details Data
 */
export interface IncomeDetailsData {
  primaryIncome: number;
  primaryIncomeType: string;
  primaryIncomeFrequency: string;
  taxRate: number;
  totalMonthlyIncome?: number;
  totalAnnualIncome?: number;
  incomeSources: IncomeSource[];
}

/**
 * Expense Category
 */
export interface ExpenseCategory {
  id: string;
  name: string;
  amount: number;
  frequency: 'weekly' | 'biweekly' | 'monthly' | 'quarterly' | 'annually';
  isEssential: boolean;
  notes?: string;
}

/**
 * Expense Details Data
 */
export interface ExpenseDetailsData {
  totalMonthlyExpenses: number;
  totalAnnualExpenses?: number; // Make optional for backward compatibility
  expenseCategories: Record<string, Record<string, number>>;
}

/**
 * Risk Assessment Data
 */
export interface RiskAssessmentData {
  answers: Record<string, string>;
  riskScore: number;
  riskProfile:
    | 'conservative'
    | 'moderate-conservative'
    | 'moderate'
    | 'moderate-aggressive'
    | 'aggressive';
}

/**
 * Net Worth Details
 */
export interface NetWorthDetails {
  totalAssets: number;
  totalLiabilities: number;
}

/**
 * North Direction Data
 */
export interface NorthDirectionData {
  personalInformation: PersonalInformationData;
  personalInfo?: PersonalInformationData; // For backward compatibility
  familyInformation: FamilyInformationData;
  incomeDetails: IncomeDetailsData;
  expenseDetails: ExpenseDetailsData;
  assets: Record<string, Record<string, number>>;
  liabilities: Record<string, Record<string, number>>;
  netWorthDetails: NetWorthDetails;
  riskAssessment: RiskAssessmentData;
  cashFlowAnalysis: Record<string, unknown>;
}
