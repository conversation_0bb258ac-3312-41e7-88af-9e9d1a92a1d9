import React, { useState, useEffect } from 'react';
import { HashRouter as Router, Routes, Route, Link, Navigate } from 'react-router-dom';
import { SimpleThemeProviderMinimal, useThemeMinimal } from './theme/SimpleThemeProviderMinimal';
import AppContextMinimal, { useAppContext } from './context/AppContextMinimal';
import DataConsentModal from './components/privacy/DataConsentModal';
import HeaderIcons from './components/ui/HeaderIcons';

// Login component
const Login = () => {
  const { login } = useAppContext();
  const { theme } = useThemeMinimal();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const success = await login(email, password);
    if (!success) {
      console.error('Lo<PERSON> failed');
    }
  };

  return (
    <div
      style={{
        padding: '20px',
        color: theme.colors.text,
        maxWidth: '400px',
        margin: '0 auto',
        marginTop: '40px',
        backgroundColor: theme.mode === 'light' ? 'white' : '#222',
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
      }}
    >
      <h1>Login</h1>
      <form onSubmit={handleSubmit}>
        <div style={{ marginBottom: '15px' }}>
          <label style={{ display: 'block', marginBottom: '5px' }}>Email:</label>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            style={{
              width: '100%',
              padding: '8px',
              borderRadius: '4px',
              border: '1px solid #ccc',
              backgroundColor: theme.mode === 'light' ? 'white' : '#333',
              color: theme.colors.text,
            }}
            required
          />
        </div>
        <div style={{ marginBottom: '15px' }}>
          <label style={{ display: 'block', marginBottom: '5px' }}>Password:</label>
          <input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            style={{
              width: '100%',
              padding: '8px',
              borderRadius: '4px',
              border: '1px solid #ccc',
              backgroundColor: theme.mode === 'light' ? 'white' : '#333',
              color: theme.colors.text,
            }}
            required
          />
        </div>
        <button
          type="submit"
          style={{
            backgroundColor: theme.colors.primary,
            color: 'white',
            border: 'none',
            padding: '10px 15px',
            borderRadius: '4px',
            cursor: 'pointer',
            width: '100%',
          }}
        >
          Login
        </button>
      </form>
    </div>
  );
};

// Profile component
const Profile = () => {
  const { userData, logout, updatePreferences, hasRole } = useAppContext();
  const { theme } = useThemeMinimal();

  if (!userData) {
    return <div>Loading...</div>;
  }

  return (
    <div
      style={{
        padding: '20px',
        color: theme.colors.text,
        maxWidth: '600px',
        margin: '0 auto',
        marginTop: '20px',
        backgroundColor: theme.mode === 'light' ? 'white' : '#222',
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
      }}
    >
      <h1>Profile</h1>
      <div style={{ marginBottom: '20px' }}>
        <p>
          <strong>Name:</strong> {userData.name}
        </p>
        <p>
          <strong>Email:</strong> {userData.email}
        </p>
        <p>
          <strong>Role:</strong> {userData.role}
        </p>
      </div>

      <h2>Preferences</h2>
      <div style={{ marginBottom: '10px' }}>
        <label style={{ display: 'flex', alignItems: 'center' }}>
          <input
            type="checkbox"
            checked={userData.preferences.notifications}
            onChange={(e) => updatePreferences({ notifications: e.target.checked })}
            style={{ marginRight: '10px' }}
          />
          Enable Notifications
        </label>
      </div>
      <div style={{ marginBottom: '20px' }}>
        <label style={{ display: 'flex', alignItems: 'center' }}>
          <input
            type="checkbox"
            checked={userData.preferences.darkMode}
            onChange={(e) => updatePreferences({ darkMode: e.target.checked })}
            style={{ marginRight: '10px' }}
          />
          Dark Mode
        </label>
      </div>

      <button
        onClick={logout}
        style={{
          backgroundColor: '#f44336',
          color: 'white',
          border: 'none',
          padding: '10px 15px',
          borderRadius: '4px',
          cursor: 'pointer',
        }}
      >
        Logout
      </button>
    </div>
  );
};

// Home component
const Home = () => {
  const { theme } = useThemeMinimal();
  const { isLoggedIn, userData } = useAppContext();

  return (
    <div
      style={{
        padding: '20px',
        color: theme.colors.text,
      }}
    >
      <h1>Welcome{userData ? `, ${userData.name}` : ''} to LifeCompass</h1>
      {!isLoggedIn && <p>Please login or register to continue.</p>}
      {isLoggedIn && (
        <p>Explore the different sections of LifeCompass to navigate your life journey.</p>
      )}
    </div>
  );
};

// Navigation component
const Navigation = () => {
  const { theme, toggleTheme } = useThemeMinimal();
  const { isLoggedIn, userData, hasRole } = useAppContext();

  return (
    <nav
      style={{
        backgroundColor: theme.mode === 'light' ? '#333' : '#111',
        padding: '10px',
        display: 'flex',
        justifyContent: 'space-between',
      }}
    >
      <div>
        <Link to="/" style={{ color: 'white', marginRight: '15px', textDecoration: 'none' }}>
          Home
        </Link>
        {isLoggedIn && (
          <Link to="/profile" style={{ color: 'white', textDecoration: 'none' }}>
            Profile
          </Link>
        )}
        {isLoggedIn && hasRole('admin') && (
          <Link
            to="/data-management"
            style={{ color: 'white', marginLeft: '15px', textDecoration: 'none' }}
          >
            Data Management
          </Link>
        )}
      </div>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <button
          onClick={toggleTheme}
          style={{
            backgroundColor: theme.colors.primary,
            color: 'white',
            border: 'none',
            padding: '5px 10px',
            borderRadius: '4px',
            cursor: 'pointer',
            marginRight: '15px',
          }}
        >
          Toggle Theme
        </button>

        {isLoggedIn ? (
          <span style={{ color: 'white' }}>
            Welcome, {userData?.name} ({userData?.role})
          </span>
        ) : (
          <Link to="/login" style={{ color: 'white', textDecoration: 'none' }}>
            Login
          </Link>
        )}
      </div>
    </nav>
  );
};

// App Content component
const AppContent = () => {
  const { theme } = useThemeMinimal();
  const { isLoggedIn } = useAppContext();
  const [isConsentModalOpen, setIsConsentModalOpen] = useState(true);

  useEffect(() => {
    const consent = localStorage.getItem('lifecompass_data_consent');
    if (consent === 'true') {
      setIsConsentModalOpen(false);
    } else {
      setIsConsentModalOpen(true);
    }
  }, []);

  const handleConsentModalClose = () => {
    setIsConsentModalOpen(false);
  };

  return (
    <div
      style={{
        fontFamily: 'Arial, sans-serif',
        minHeight: '100vh',
        backgroundColor: theme.colors.background,
        transition: 'background-color 0.3s ease, color 0.3s ease',
      }}
    >
      <Navigation />
      <HeaderIcons />
      <div style={{ padding: '20px' }}>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/login" element={isLoggedIn ? <Navigate to="/profile" /> : <Login />} />
          <Route path="/profile" element={isLoggedIn ? <Profile /> : <Navigate to="/login" />} />
          <Route
            path="/data-management"
            element={
              isLoggedIn && useAppContext().hasRole('admin') ? (
                <DataManagementPage />
              ) : (
                <Navigate to="/" />
              )
            }
          />
        </Routes>
      </div>
      <DataConsentModal isOpen={isConsentModalOpen} onClose={handleConsentModalClose} />
    </div>
  );
};

// Main App component with router, theme provider, and app context
const App: React.FC = () => {
  console.log('App.full component rendering');

  return (
    <Router>
      <SimpleThemeProviderMinimal>
        <AppContextMinimal.Provider>
          <AppContent />
        </AppContextMinimal.Provider>
      </SimpleThemeProviderMinimal>
    </Router>
  );
};

export default App;
