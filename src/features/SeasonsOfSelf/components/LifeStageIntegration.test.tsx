/**
 * Life Stage Integration Component Tests
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter } from 'react-router-dom';
import LifeStageIntegration from './LifeStageIntegration';
import { SimpleThemeProvider } from '../../../theme/SimpleThemeProvider';
import { SeasonsOfSelfProvider } from '../context/SeasonsOfSelfContext';
import { FinancialCompassProvider } from '../../FinancialCompass/context/FinancialCompassContext';

// Mock the formatCurrency utility
jest.mock('../../../utils/formatters', () => ({
  formatCurrency: jest.fn((value) => `$${value}`),
}));

describe('LifeStageIntegration Component', () => {
  const renderComponent = (props = {}) => {
    return render(
      <MemoryRouter>
        <SimpleThemeProvider>
          <SeasonsOfSelfProvider>
            <FinancialCompassProvider>
              <LifeStageIntegration {...props} />
            </FinancialCompassProvider>
          </SeasonsOfSelfProvider>
        </SimpleThemeProvider>
      </MemoryRouter>
    );
  };

  it('renders the component with title and description', () => {
    renderComponent();

    expect(screen.getByText('Life Stage Financial Integration')).toBeInTheDocument();
    expect(
      screen.getByText('Align your financial planning with your current life stage and season.')
    ).toBeInTheDocument();
  });

  it('displays season sections', () => {
    renderComponent();

    expect(screen.getByText('Spring: Foundation & Growth')).toBeInTheDocument();
    expect(screen.getByText('Summer: Accumulation & Peak')).toBeInTheDocument();
    expect(screen.getByText('Autumn: Transition & Shift')).toBeInTheDocument();
    expect(screen.getByText('Winter: Purpose & Legacy')).toBeInTheDocument();
  });

  it('displays stage buttons for each season', () => {
    renderComponent();

    // Spring stages
    expect(screen.getByText('Pleasure')).toBeInTheDocument();
    expect(screen.getByText('Happiness')).toBeInTheDocument();

    // Summer stages
    expect(screen.getByText('Joy')).toBeInTheDocument();
    expect(screen.getByText('Momentum')).toBeInTheDocument();

    // Autumn stages
    expect(screen.getByText('Pivot')).toBeInTheDocument();
    expect(screen.getByText('Goal Seeking')).toBeInTheDocument();

    // Winter stages
    expect(screen.getByText('Calling')).toBeInTheDocument();
    expect(screen.getByText('Purpose')).toBeInTheDocument();
    expect(screen.getByText('Fulfillment')).toBeInTheDocument();
  });

  it('shows empty state when no stage is selected', () => {
    renderComponent();

    expect(
      screen.getByText('Select a life stage to see financial priorities and recommendations.')
    ).toBeInTheDocument();
  });

  it('displays stage details when a stage is selected', async () => {
    renderComponent();

    // Click on a stage button
    fireEvent.click(screen.getByText('Joy'));

    // Wait for stage details to appear
    await waitFor(() => {
      expect(screen.getByText('Financial Priorities for This Stage')).toBeInTheDocument();
    });

    // Check for direction cards
    expect(screen.getByText('North: Financial Foundation')).toBeInTheDocument();
    expect(screen.getByText('East: Retirement Vision')).toBeInTheDocument();
    expect(screen.getByText('South: Protection & Risk')).toBeInTheDocument();
    expect(screen.getByText('West: Legacy & Impact')).toBeInTheDocument();
  });

  it('displays financial summary', async () => {
    renderComponent();

    // Click on a stage button
    fireEvent.click(screen.getByText('Joy'));

    // Wait for financial summary to appear
    await waitFor(() => {
      expect(screen.getByText('Your Financial Summary')).toBeInTheDocument();
    });

    // Check for summary items
    expect(screen.getByText('Net Worth')).toBeInTheDocument();
    expect(screen.getByText('Monthly Income')).toBeInTheDocument();
    expect(screen.getByText('Monthly Expenses')).toBeInTheDocument();
    expect(screen.getByText('Retirement Savings')).toBeInTheDocument();
  });

  it('calls onComplete when Continue button is clicked', () => {
    const onCompleteMock = jest.fn();
    renderComponent({ onComplete: onCompleteMock });

    // Click the Continue button
    fireEvent.click(screen.getByText('Continue'));

    // Check that onComplete was called
    expect(onCompleteMock).toHaveBeenCalledTimes(1);
  });

  it('calls onBack when Back button is clicked', () => {
    const onBackMock = jest.fn();
    renderComponent({ onBack: onBackMock });

    // Click the Back button
    fireEvent.click(screen.getByText('Back'));

    // Check that onBack was called
    expect(onBackMock).toHaveBeenCalledTimes(1);
  });
});
