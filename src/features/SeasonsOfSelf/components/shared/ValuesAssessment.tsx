/**
 * Values Assessment Component
 *
 * This component implements a comprehensive values clarification system
 * based on established psychological frameworks (<PERSON><PERSON><PERSON>, <PERSON>).
 * Users identify, rank, and align their core values.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import Card from '../../../../components/ui/Card';
import Button from '../../../../components/ui/Button';
import autoSaveService from '../../../DataPortability/services/autoSaveService';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

// Component props
interface ValuesAssessmentProps {
  onComplete?: (values: ValuesData) => void;
  onBack?: () => void;
  initialData?: ValuesData;
}

// Values data structure
export interface ValuesData {
  id: string;
  selectedValues: string[];
  rankedValues: string[];
  topValues: string[];
  valuesStatement: string;
  completed: boolean;
}

// Core values list based on psychological research
const CORE_VALUES = [
  // Achievement & Success
  { id: 'achievement', name: 'Achievement', category: 'Achievement & Success', description: 'Accomplishing goals and succeeding' },
  { id: 'excellence', name: 'Excellence', category: 'Achievement & Success', description: 'Doing things to the best of your ability' },
  { id: 'recognition', name: 'Recognition', category: 'Achievement & Success', description: 'Being acknowledged for your contributions' },
  { id: 'leadership', name: 'Leadership', category: 'Achievement & Success', description: 'Guiding and inspiring others' },
  { id: 'influence', name: 'Influence', category: 'Achievement & Success', description: 'Having impact on people and situations' },

  // Relationships & Connection
  { id: 'family', name: 'Family', category: 'Relationships & Connection', description: 'Close bonds with family members' },
  { id: 'friendship', name: 'Friendship', category: 'Relationships & Connection', description: 'Deep, meaningful friendships' },
  { id: 'love', name: 'Love', category: 'Relationships & Connection', description: 'Romantic love and intimate relationships' },
  { id: 'community', name: 'Community', category: 'Relationships & Connection', description: 'Belonging to a community' },
  { id: 'collaboration', name: 'Collaboration', category: 'Relationships & Connection', description: 'Working together with others' },

  // Security & Stability
  { id: 'security', name: 'Security', category: 'Security & Stability', description: 'Financial and personal safety' },
  { id: 'stability', name: 'Stability', category: 'Security & Stability', description: 'Predictability and consistency' },
  { id: 'tradition', name: 'Tradition', category: 'Security & Stability', description: 'Honoring customs and heritage' },
  { id: 'order', name: 'Order', category: 'Security & Stability', description: 'Structure and organization' },

  // Adventure & Growth
  { id: 'adventure', name: 'Adventure', category: 'Adventure & Growth', description: 'Exciting and novel experiences' },
  { id: 'freedom', name: 'Freedom', category: 'Adventure & Growth', description: 'Independence and autonomy' },
  { id: 'growth', name: 'Growth', category: 'Adventure & Growth', description: 'Personal development and learning' },
  { id: 'curiosity', name: 'Curiosity', category: 'Adventure & Growth', description: 'Exploring and discovering' },
  { id: 'innovation', name: 'Innovation', category: 'Adventure & Growth', description: 'Creating new ideas and solutions' },

  // Service & Contribution
  { id: 'service', name: 'Service', category: 'Service & Contribution', description: 'Helping and serving others' },
  { id: 'justice', name: 'Justice', category: 'Service & Contribution', description: 'Fairness and equality' },
  { id: 'compassion', name: 'Compassion', category: 'Service & Contribution', description: 'Caring for others\' wellbeing' },
  { id: 'generosity', name: 'Generosity', category: 'Service & Contribution', description: 'Giving freely to others' },

  // Creativity & Expression
  { id: 'creativity', name: 'Creativity', category: 'Creativity & Expression', description: 'Artistic and creative expression' },
  { id: 'beauty', name: 'Beauty', category: 'Creativity & Expression', description: 'Appreciating and creating beauty' },
  { id: 'authenticity', name: 'Authenticity', category: 'Creativity & Expression', description: 'Being true to yourself' },

  // Spirituality & Meaning
  { id: 'spirituality', name: 'Spirituality', category: 'Spirituality & Meaning', description: 'Connection to the sacred' },
  { id: 'purpose', name: 'Purpose', category: 'Spirituality & Meaning', description: 'Having meaning and direction' },
  { id: 'wisdom', name: 'Wisdom', category: 'Spirituality & Meaning', description: 'Deep understanding and insight' },

  // Health & Wellness
  { id: 'health', name: 'Health', category: 'Health & Wellness', description: 'Physical and mental wellbeing' },
  { id: 'balance', name: 'Balance', category: 'Health & Wellness', description: 'Harmony in all life areas' },
  { id: 'peace', name: 'Peace', category: 'Health & Wellness', description: 'Inner calm and tranquility' },
];

/**
 * Values Assessment Component
 */
export const ValuesAssessment: React.FC<ValuesAssessmentProps> = ({
  onComplete,
  onBack,
  initialData
}) => {
  const { theme } = useTheme();

  // Initialize data
  const [valuesData, setValuesData] = useState<ValuesData>(
    initialData || {
      id: `values-${Date.now()}`,
      selectedValues: [],
      rankedValues: [],
      topValues: [],
      valuesStatement: '',
      completed: false,
    }
  );

  const [currentStep, setCurrentStep] = useState(0);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Auto-save setup
  const autoSave = React.useMemo(
    () =>
      autoSaveService.createEnhancedAutoSave({
        key: 'values_assessment',
        onSave: () => {
          setShowSaveIndicator(true);
          setTimeout(() => setShowSaveIndicator(false), 2000);
        },
      }),
    []
  );

  // Update data when values change
  useEffect(() => {
    autoSave.save(valuesData);
  }, [valuesData, autoSave]);

  const steps = [
    {
      title: 'Select Your Values',
      description: 'Choose 15-20 values that resonate with you from the list below.',
      minRequired: 15,
      maxAllowed: 20,
    },
    {
      title: 'Rank Your Values',
      description: 'Arrange your selected values in order of importance to you.',
      minRequired: 0,
      maxAllowed: 0,
    },
    {
      title: 'Identify Top 5',
      description: 'Select your top 5 most important values.',
      minRequired: 5,
      maxAllowed: 5,
    },
    {
      title: 'Values Statement',
      description: 'Write a personal statement about how these values guide your life.',
      minRequired: 0,
      maxAllowed: 0,
    },
  ];

  const currentStepData = steps[currentStep];

  const handleValueToggle = (valueId: string) => {
    setValuesData(prev => {
      const isSelected = prev.selectedValues.includes(valueId);
      let newSelected;

      if (isSelected) {
        newSelected = prev.selectedValues.filter(id => id !== valueId);
      } else {
        if (prev.selectedValues.length >= currentStepData.maxAllowed) {
          return prev; // Don't add if at max
        }
        newSelected = [...prev.selectedValues, valueId];
      }

      return {
        ...prev,
        selectedValues: newSelected,
      };
    });
  };

  const handleRankingChange = (draggedId: string, targetIndex: number) => {
    setValuesData(prev => {
      const newRanked = [...prev.selectedValues];
      const draggedIndex = newRanked.indexOf(draggedId);

      // Remove from old position and insert at new position
      newRanked.splice(draggedIndex, 1);
      newRanked.splice(targetIndex, 0, draggedId);

      return {
        ...prev,
        rankedValues: newRanked,
      };
    });
  };

  const handleTopValuesToggle = (valueId: string) => {
    setValuesData(prev => {
      const isSelected = prev.topValues.includes(valueId);
      let newTop;

      if (isSelected) {
        newTop = prev.topValues.filter(id => id !== valueId);
      } else {
        if (prev.topValues.length >= 5) {
          return prev; // Don't add if at max
        }
        newTop = [...prev.topValues, valueId];
      }

      return {
        ...prev,
        topValues: newTop,
      };
    });
  };

  const handleStatementChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setValuesData(prev => ({
      ...prev,
      valuesStatement: e.target.value,
    }));
  };

  const canProceed = () => {
    switch (currentStep) {
      case 0:
        return valuesData.selectedValues.length >= currentStepData.minRequired;
      case 1:
        return valuesData.rankedValues.length === valuesData.selectedValues.length;
      case 2:
        return valuesData.topValues.length === 5;
      case 3:
        return valuesData.valuesStatement.length > 50;
      default:
        return false;
    }
  };

  const handleNext = () => {
    if (currentStep === 1) {
      // Auto-populate ranking if not done
      if (valuesData.rankedValues.length === 0) {
        setValuesData(prev => ({
          ...prev,
          rankedValues: [...prev.selectedValues],
        }));
      }
    }

    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // Complete assessment
      const completedData = {
        ...valuesData,
        completed: true,
      };
      setValuesData(completedData);
      if (onComplete) {
        onComplete(completedData);
      }
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const getValuesByCategory = () => {
    const categories: Record<string, typeof CORE_VALUES> = {};
    CORE_VALUES.forEach(value => {
      if (!categories[value.category]) {
        categories[value.category] = [];
      }
      categories[value.category].push(value);
    });
    return categories;
  };

  return (
    <Container as={motion.div} variants={containerVariants} initial="hidden" animate="visible">
      <AssessmentCard elevation="medium" as={motion.div} variants={itemVariants}>
        <CardHeader>
          <Title>Values Assessment</Title>
          <Description>
            Discover your core values - the fundamental beliefs that guide your decisions and actions.
          </Description>
          <ProgressBar>
            <ProgressFill progress={(currentStep + 1) / steps.length * 100} />
          </ProgressBar>
          <ProgressText>
            Step {currentStep + 1} of {steps.length}: {currentStepData.title}
          </ProgressText>
        </CardHeader>

        <CardContent>
          <StepContainer>
            <StepHeader>
              <StepTitle>{currentStepData.title}</StepTitle>
              <StepDescription>{currentStepData.description}</StepDescription>
            </StepHeader>

            {currentStep === 0 && (
              <ValuesGrid>
                {Object.entries(getValuesByCategory()).map(([category, values]) => (
                  <CategorySection key={category}>
                    <CategoryTitle>{category}</CategoryTitle>
                    <ValuesInCategory>
                      {values.map(value => (
                        <ValueCard
                          key={value.id}
                          selected={valuesData.selectedValues.includes(value.id)}
                          onClick={() => handleValueToggle(value.id)}
                        >
                          <ValueName>{value.name}</ValueName>
                          <ValueDescription>{value.description}</ValueDescription>
                        </ValueCard>
                      ))}
                    </ValuesInCategory>
                  </CategorySection>
                ))}
              </ValuesGrid>
            )}

            {currentStep === 1 && (
              <RankingContainer>
                <RankingInstructions>
                  Drag and drop to arrange your values in order of importance (most important at top):
                </RankingInstructions>
                <RankingList>
                  {(valuesData.rankedValues.length > 0 ? valuesData.rankedValues : valuesData.selectedValues).map((valueId, index) => {
                    const value = CORE_VALUES.find(v => v.id === valueId);
                    return (
                      <RankingItem key={valueId} rank={index + 1}>
                        <RankNumber>{index + 1}</RankNumber>
                        <RankValueName>{value?.name}</RankValueName>
                        <RankValueDescription>{value?.description}</RankValueDescription>
                      </RankingItem>
                    );
                  })}
                </RankingList>
              </RankingContainer>
            )}

            {currentStep === 2 && (
              <TopValuesContainer>
                <TopValuesInstructions>
                  Select your top 5 most important values from your ranked list:
                </TopValuesInstructions>
                <TopValuesList>
                  {(valuesData.rankedValues.length > 0 ? valuesData.rankedValues : valuesData.selectedValues).slice(0, 10).map((valueId) => {
                    const value = CORE_VALUES.find(v => v.id === valueId);
                    return (
                      <TopValueCard
                        key={valueId}
                        selected={valuesData.topValues.includes(valueId)}
                        onClick={() => handleTopValuesToggle(valueId)}
                      >
                        <ValueName>{value?.name}</ValueName>
                        <ValueDescription>{value?.description}</ValueDescription>
                      </TopValueCard>
                    );
                  })}
                </TopValuesList>
                <SelectedCount>
                  Selected: {valuesData.topValues.length}/5
                </SelectedCount>
              </TopValuesContainer>
            )}

            {currentStep === 3 && (
              <StatementContainer>
                <StatementInstructions>
                  Write a personal statement about how these core values guide your life and decisions:
                </StatementInstructions>
                <TopValuesDisplay>
                  <strong>Your Top 5 Values:</strong>
                  <ValuesList>
                    {valuesData.topValues.map(valueId => {
                      const value = CORE_VALUES.find(v => v.id === valueId);
                      return <ValueTag key={valueId}>{value?.name}</ValueTag>;
                    })}
                  </ValuesList>
                </TopValuesDisplay>
                <StatementTextArea
                  value={valuesData.valuesStatement}
                  onChange={handleStatementChange}
                  placeholder="Example: My core values of integrity, growth, and service guide me to be honest in all my relationships, continuously learn and improve myself, and look for ways to contribute to my community. These values help me make decisions that align with who I want to be..."
                  rows={6}
                />
                <CharacterCount>
                  {valuesData.valuesStatement.length} characters (minimum 50)
                </CharacterCount>
              </StatementContainer>
            )}
          </StepContainer>

          <SaveIndicator visible={showSaveIndicator}>Changes saved automatically</SaveIndicator>
        </CardContent>

        <CardActions>
          {currentStep > 0 && (
            <Button variant="outlined" onClick={handlePrevious}>
              Previous
            </Button>
          )}
          {onBack && currentStep === 0 && (
            <Button variant="outlined" onClick={onBack}>
              Back
            </Button>
          )}
          <div style={{ flex: 1 }} />
          <Button
            variant="primary"
            onClick={handleNext}
            disabled={!canProceed()}
          >
            {currentStep === steps.length - 1 ? 'Complete Assessment' : 'Next'}
          </Button>
        </CardActions>
      </AssessmentCard>
    </Container>
  );
};

// Styled components
const Container = styled.div`
  max-width: 900px;
  margin: 0 auto;
`;

const AssessmentCard = styled(Card)`
  overflow: hidden;
`;

const CardHeader = styled.div`
  padding: 24px;
  background-color: ${({ theme }) => theme.colors?.background || '#ffffff'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const Title = styled.h2`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 8px;
`;

const Description = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 16px;
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 8px;
  background-color: ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
`;

const ProgressFill = styled.div<{ progress: number }>`
  width: ${({ progress }) => progress}%;
  height: 100%;
  background-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  transition: width 0.3s ease;
`;

const ProgressText = styled.p`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  text-align: center;
`;

const CardContent = styled.div`
  padding: 24px;
`;

const CardActions = styled.div`
  padding: 16px 24px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-top: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const StepContainer = styled.div`
  margin-bottom: 24px;
`;

const StepHeader = styled.div`
  margin-bottom: 24px;
`;

const StepTitle = styled.h3`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 8px;
`;

const StepDescription = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  font-size: 0.9rem;
`;

const ValuesGrid = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const CategorySection = styled.div`
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 8px;
  padding: 16px;
`;

const CategoryTitle = styled.h4`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 12px;
  font-size: 1.1rem;
`;

const ValuesInCategory = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
`;

const ValueCard = styled.div<{ selected: boolean }>`
  padding: 12px;
  border: 2px solid ${({ selected, theme }) =>
    selected ? theme.colors?.primary || '#1976d2' : theme.colors?.border || '#e0e0e0'};
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: ${({ selected, theme }) =>
    selected ? `${theme.colors?.primary || '#1976d2'}10` : 'transparent'};

  &:hover {
    border-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
    background-color: ${({ theme }) => `${theme.colors?.primary || '#1976d2'}05`};
  }
`;

const ValueName = styled.div`
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 4px;
`;

const ValueDescription = styled.div`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${({ visible }) => (visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  color: ${({ theme }) => theme.colors?.success || '#4caf50'};
  font-size: 0.875rem;
  text-align: center;
  margin-top: 16px;
`;

// Ranking step styles
const RankingContainer = styled.div`
  margin-top: 16px;
`;

const RankingInstructions = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 16px;
  font-style: italic;
`;

const RankingList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const RankingItem = styled.div<{ rank: number }>`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 8px;
  background-color: ${({ rank, theme }) =>
    rank <= 5 ? `${theme.colors?.primary || '#1976d2'}05` : 'transparent'};
`;

const RankNumber = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.875rem;
`;

const RankValueName = styled.div`
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  flex: 1;
`;

const RankValueDescription = styled.div`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  font-size: 0.875rem;
  flex: 2;
`;

// Top values step styles
const TopValuesContainer = styled.div`
  margin-top: 16px;
`;

const TopValuesInstructions = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 16px;
  font-style: italic;
`;

const TopValuesList = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
`;

const TopValueCard = styled.div<{ selected: boolean }>`
  padding: 16px;
  border: 2px solid ${({ selected, theme }) =>
    selected ? theme.colors?.primary || '#1976d2' : theme.colors?.border || '#e0e0e0'};
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: ${({ selected, theme }) =>
    selected ? `${theme.colors?.primary || '#1976d2'}15` : 'transparent'};

  &:hover {
    border-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
    background-color: ${({ theme }) => `${theme.colors?.primary || '#1976d2'}08`};
  }
`;

const SelectedCount = styled.div`
  text-align: center;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  font-weight: 500;
`;

// Statement step styles
const StatementContainer = styled.div`
  margin-top: 16px;
`;

const StatementInstructions = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 16px;
  font-style: italic;
`;

const TopValuesDisplay = styled.div`
  margin-bottom: 16px;
  padding: 16px;
  background-color: ${({ theme }) => theme.colors?.background || '#ffffff'};
  border-radius: 8px;
`;

const ValuesList = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
`;

const ValueTag = styled.span`
  padding: 4px 12px;
  background-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  color: white;
  border-radius: 16px;
  font-size: 0.875rem;
  font-weight: 500;
`;

const StatementTextArea = styled.textarea`
  width: 100%;
  padding: 16px;
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 8px;
  font-size: 1rem;
  resize: vertical;
  min-height: 150px;
  font-family: inherit;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
    box-shadow: 0 0 0 2px ${({ theme }) => `${theme.colors?.primary || '#1976d2'}40`};
  }
`;

const CharacterCount = styled.div`
  text-align: right;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  font-size: 0.875rem;
  margin-top: 8px;
`;
