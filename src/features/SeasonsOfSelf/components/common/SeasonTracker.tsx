/**
 * Season Tracker Component
 *
 * This component displays the progress of stages within a season.
 * It shows which stages are completed and allows navigation between stages.
 */

import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useSeasonsOfSelf, Season } from '../../context/SeasonsOfSelfContext';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

// Component props
interface SeasonTrackerProps {
  season: Season;
  onStageSelect?: (stageId: string) => void;
}

/**
 * Season Tracker Component
 */
const SeasonTracker: React.FC<SeasonTrackerProps> = ({ season, onStageSelect }) => {
  useTheme(); // Make sure we're using the theme context
  const { stages, activeStage, getSeasonCompletionPercentage } = useSeasonsOfSelf();

  // Get stages for the specified season
  const seasonStages = stages.filter((stage) => stage.season === season);

  // Calculate completion percentage
  const completionPercentage = getSeasonCompletionPercentage(season);

  // Handle stage selection
  const handleStageClick = (stageId: string) => {
    if (onStageSelect) {
      onStageSelect(stageId);
    }
  };

  return (
    <Container
      as={motion.div}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      season={season}
    >
      <Header>
        <Title season={season}>
          {season.charAt(0).toUpperCase() + season.slice(1)} Season Progress
        </Title>
        <ProgressBar>
          <ProgressFill percentage={completionPercentage} season={season} />
          <ProgressText>{completionPercentage}% Complete</ProgressText>
        </ProgressBar>
      </Header>

      <StagesList>
        {seasonStages.map((stage, index) => (
          <StageItem
            key={stage.id}
            as={motion.div}
            variants={itemVariants}
            isCompleted={stage.completed}
            isActive={activeStage === stage.id}
            onClick={() => handleStageClick(stage.id)}
            season={season}
          >
            <StageNumber season={season}>{index + 1}</StageNumber>
            <StageContent>
              <StageName>{stage.name}</StageName>
              <StageDescription>{stage.description}</StageDescription>
            </StageContent>
            <StageStatus isCompleted={stage.completed}>{stage.completed ? '✓' : '○'}</StageStatus>
          </StageItem>
        ))}
      </StagesList>

      {completionPercentage === 100 && (
        <CompletionMessage as={motion.div} variants={itemVariants}>
          <CompletionIcon>🎉</CompletionIcon>
          <CompletionText>
            Congratulations! You&apos;ve completed all stages in the {season} season.
          </CompletionText>
        </CompletionMessage>
      )}
    </Container>
  );
};

// Styled components
const Container = styled.div<{ season: Season }>`
  padding: 24px;
  border-radius: 8px;
  background-color: ${({ theme, season }) => {
    const bgColor =
      theme.mode === 'light'
        ? theme.colors.seasons[season].background.light
        : theme.colors.seasons[season].background.dark;
    // Use less transparency in dark mode for better contrast
    return theme.mode === 'light' ? `${bgColor}40` : `${bgColor}80`;
  }};
  margin-bottom: 24px;
  // Add a subtle border in dark mode for better definition
  border: ${({ theme }) => (theme.mode === 'dark' ? `1px solid ${theme.colors?.border || '#e0e0e0'}` : 'none')};
`;

const Header = styled.div`
  margin-bottom: 16px;
`;

const Title = styled.h3<{ season: Season }>`
  margin-bottom: 8px;
  color: ${({ theme, season }) => theme.colors.seasons[season].primary};
`;

const ProgressBar = styled.div`
  height: 8px;
  background-color: ${({ theme }) => theme.colors?.paper || '#ffffff'};
  border-radius: 4px;
  position: relative;
  overflow: hidden;
`;

const ProgressFill = styled.div<{ percentage: number; season: Season }>`
  height: 100%;
  width: ${(props) => props.percentage}%;
  background-color: ${({ theme, season }) => theme.colors.seasons[season].primary};
  border-radius: 4px;
  transition: width 0.3s ease;
`;

const ProgressText = styled.span`
  position: absolute;
  top: 8px;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 0.75rem;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const StagesList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const StageItem = styled.div<{ isCompleted: boolean; isActive: boolean; season: Season }>`
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 16px;
  border-radius: 8px;
  background-color: ${({ theme, isActive, season }) => {
    // In dark mode, use a slightly lighter background for better contrast
    if (theme.mode === 'dark') {
      return isActive
        ? `${theme.colors.seasons[season].background.dark}CC` // Add some opacity for better contrast
        : theme.colors?.paper || '#ffffff';
    }
    return isActive ? theme.colors.seasons[season].background.light : theme.colors?.paper || '#ffffff';
  }};
  border-left: 4px solid
    ${({ theme, isActive, season }) =>
      isActive ? theme.colors.seasons[season].primary : 'transparent'};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${({ theme, isActive, season }) => {
      if (theme.mode === 'dark') {
        return isActive
          ? `${theme.colors.seasons[season].background.dark}CC`
          : theme.colors.action.hover;
      }
      return isActive ? theme.colors.seasons[season].background.light : theme.colors.action.hover;
    }};
  }
`;

const StageNumber = styled.div<{ season: Season }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: ${({ theme, season }) => theme.colors.seasons[season].primary};
  color: white;
  font-weight: 600;
  flex-shrink: 0;
`;

const StageContent = styled.div`
  flex: 1;
`;

const StageName = styled.h4`
  margin: 0 0 4px;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const StageDescription = styled.p`
  margin: 0;
  font-size: 0.875rem;
  color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : theme.colors?.text || '#000000'};
`;

const StageStatus = styled.div<{ isCompleted: boolean }>`
  color: ${({ theme, isCompleted }) =>
    isCompleted ? theme.colors?.success || '#4caf50' : theme.colors?.text || '#000000'};
  font-size: 1.25rem;
`;

const CompletionMessage = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 24px;
  padding: 16px;
  border-radius: 8px;
  background-color: ${({ theme }) => theme.colors?.paper || '#ffffff'};
`;

const CompletionIcon = styled.span`
  font-size: 1.5rem;
`;

const CompletionText = styled.p`
  margin: 0;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

export default SeasonTracker;
