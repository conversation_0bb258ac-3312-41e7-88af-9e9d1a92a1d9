/**
 * Pivot Stage Component
 *
 * This component represents the first stage of the Autumn season,
 * focusing on reassessing life direction and making necessary pivots.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useSeasonsOfSelf } from '../../context/SeasonsOfSelfContext';
import Card from '../../../../components/ui/Card';
import Button from '../../../../components/ui/Button';
import autoSaveService from '../../../DataPortability/services/autoSaveService';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

// Component props
interface PivotStageProps {
  onComplete?: () => void;
}

// Pivot assessment type
interface PivotAssessment {
  id: string;
  area: string;
  currentState: string;
  desiredState: string;
  gapReason: string;
  pivotAction: string;
  priority: number;
}

// Life areas
const lifeAreas = [
  { id: 'career', name: 'Career & Work' },
  { id: 'relationships', name: 'Relationships' },
  { id: 'health', name: 'Health & Wellbeing' },
  { id: 'finances', name: 'Finances' },
  { id: 'personal_growth', name: 'Personal Growth' },
  { id: 'spirituality', name: 'Spirituality & Purpose' },
  { id: 'lifestyle', name: 'Lifestyle & Environment' },
  { id: 'community', name: 'Community & Contribution' },
];

/**
 * Pivot Stage Component
 */
export const PivotStage: React.FC<PivotStageProps> = ({ onComplete }) => {
  // Theme is used in styled components via ThemeProvider
  useTheme();
  const { data, updateData, stages, updateStageCompletion } = useSeasonsOfSelf();

  // Get saved data or initialize with defaults
  const [assessments, setAssessments] = useState<PivotAssessment[]>(
    data.autumn && data.autumn.pivotAssessments ? data.autumn.pivotAssessments : []
  );

  const [newAssessment, setNewAssessment] = useState<PivotAssessment>({
    id: '',
    area: '',
    currentState: '',
    desiredState: '',
    gapReason: '',
    pivotAction: '',
    priority: 0,
  });

  // Save indicator state
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Create auto-save instance
  const autoSave = React.useMemo(
    () =>
      autoSaveService.createEnhancedAutoSave({
        key: 'autumn_pivot_assessments',
        onSave: () => {
          setShowSaveIndicator(true);
          setTimeout(() => setShowSaveIndicator(false), 2000);
        },
      }),
    []
  );

  // Update context data when assessments change
  useEffect(() => {
    updateData('autumn', 'pivotAssessments', assessments);

    // Auto-save data
    autoSave.save(assessments);

    // Mark stage as completed if at least 3 assessments are added
    const pivotStage = stages.find((stage) => stage.id === 'pivot');
    if (pivotStage && !pivotStage.completed && assessments.length >= 3) {
      updateStageCompletion('pivot', true);
    } else if (pivotStage && pivotStage.completed && assessments.length < 3) {
      updateStageCompletion('pivot', false);
    }
  }, [assessments, updateData, stages, autoSave, updateStageCompletion]);

  // Handle input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setNewAssessment((prev) => ({
      ...prev,
      [name]: name === 'priority' ? parseInt(value) || 0 : value,
    }));
  };

  // Handle adding a new assessment
  const handleAddAssessment = () => {
    if (!newAssessment.area) return;

    const newItem = {
      ...newAssessment,
      id: `pivot-${Date.now()}`,
    };

    setAssessments((prev) => [...prev, newItem]);
    setNewAssessment({
      id: '',
      area: '',
      currentState: '',
      desiredState: '',
      gapReason: '',
      pivotAction: '',
      priority: 0,
    });
  };

  // Handle removing an assessment
  const handleRemoveAssessment = (id: string) => {
    setAssessments((prev) => prev.filter((assessment) => assessment.id !== id));
  };

  // Handle completion
  const handleComplete = () => {
    if (onComplete) {
      onComplete();
    }
  };

  return (
    <Container as={motion.div} variants={containerVariants} initial="hidden" animate="visible">
      <StageCard elevation="medium" as={motion.div} variants={itemVariants}>
        <CardHeader>
          <StageTitle>Pivot Stage</StageTitle>
          <StageDescription>
            The Pivot Stage is about reassessing your life direction and making necessary
            adjustments. Identify areas where you need to pivot to align more closely with your
            authentic self and values.
          </StageDescription>
        </CardHeader>

        <CardContent>
          <Section>
            <SectionTitle>Your Life Pivot Assessment</SectionTitle>
            <SectionDescription>
              Assess different areas of your life where you may need to make pivots or adjustments.
              Consider the gap between your current state and desired state in each area.
            </SectionDescription>

            <AssessmentForm>
              <FormRow>
                <FormGroup>
                  <Label htmlFor="area">Life Area</Label>
                  <Select
                    id="area"
                    name="area"
                    value={newAssessment.area}
                    onChange={handleInputChange}
                  >
                    <option value="">Select life area</option>
                    {lifeAreas.map((area) => (
                      <option key={area.id} value={area.id}>
                        {area.name}
                      </option>
                    ))}
                  </Select>
                </FormGroup>

                <FormGroup>
                  <Label htmlFor="priority">Priority (1-10)</Label>
                  <Input
                    id="priority"
                    name="priority"
                    type="number"
                    min="1"
                    max="10"
                    value={newAssessment.priority || ''}
                    onChange={handleInputChange}
                  />
                </FormGroup>
              </FormRow>

              <FormRow>
                <FormGroup fullWidth>
                  <Label htmlFor="currentState">Current State</Label>
                  <TextArea
                    id="currentState"
                    name="currentState"
                    value={newAssessment.currentState}
                    onChange={handleInputChange}
                    placeholder="Describe your current situation in this area"
                    rows={2}
                  />
                </FormGroup>
              </FormRow>

              <FormRow>
                <FormGroup fullWidth>
                  <Label htmlFor="desiredState">Desired State</Label>
                  <TextArea
                    id="desiredState"
                    name="desiredState"
                    value={newAssessment.desiredState}
                    onChange={handleInputChange}
                    placeholder="Describe your desired situation in this area"
                    rows={2}
                  />
                </FormGroup>
              </FormRow>

              <FormRow>
                <FormGroup fullWidth>
                  <Label htmlFor="gapReason">Reason for Gap</Label>
                  <TextArea
                    id="gapReason"
                    name="gapReason"
                    value={newAssessment.gapReason}
                    onChange={handleInputChange}
                    placeholder="What has caused or maintained this gap?"
                    rows={2}
                  />
                </FormGroup>
              </FormRow>

              <FormRow>
                <FormGroup fullWidth>
                  <Label htmlFor="pivotAction">Pivot Action</Label>
                  <TextArea
                    id="pivotAction"
                    name="pivotAction"
                    value={newAssessment.pivotAction}
                    onChange={handleInputChange}
                    placeholder="What specific action can you take to pivot in this area?"
                    rows={2}
                  />
                </FormGroup>
              </FormRow>

              <AddButton onClick={handleAddAssessment} disabled={!newAssessment.area}>
                Add Pivot Assessment
              </AddButton>
            </AssessmentForm>

            <AssessmentsList>
              {assessments.length === 0 ? (
                <EmptyState>
                  No pivot assessments added yet. Add your first assessment above.
                </EmptyState>
              ) : (
                assessments.map((assessment) => (
                  <AssessmentItem key={assessment.id}>
                    <AssessmentHeader>
                      <AssessmentArea>
                        {lifeAreas.find((area) => area.id === assessment.area)?.name ||
                          assessment.area}
                      </AssessmentArea>
                      <PriorityBadge>Priority: {assessment.priority}/10</PriorityBadge>
                      <RemoveButton onClick={() => handleRemoveAssessment(assessment.id)}>
                        ✕
                      </RemoveButton>
                    </AssessmentHeader>
                    <AssessmentDetails>
                      <StateComparison>
                        <CurrentState>
                          <StateLabel>Current:</StateLabel> {assessment.currentState}
                        </CurrentState>
                        <DesiredState>
                          <StateLabel>Desired:</StateLabel> {assessment.desiredState}
                        </DesiredState>
                      </StateComparison>
                      {assessment.gapReason && (
                        <GapReason>
                          <ReasonLabel>Gap Reason:</ReasonLabel> {assessment.gapReason}
                        </GapReason>
                      )}
                      {assessment.pivotAction && (
                        <PivotAction>
                          <ActionLabel>Pivot Action:</ActionLabel> {assessment.pivotAction}
                        </PivotAction>
                      )}
                    </AssessmentDetails>
                  </AssessmentItem>
                ))
              )}
            </AssessmentsList>
          </Section>

          <SaveIndicator visible={showSaveIndicator}>Changes saved automatically</SaveIndicator>
        </CardContent>

        <CardActions>
          <Button variant="primary" onClick={handleComplete} disabled={assessments.length < 3}>
            {assessments.length < 3
              ? `Add ${3 - assessments.length} more ${assessments.length === 2 ? 'assessment' : 'assessments'} to continue`
              : 'Continue to Goal Seeking Stage'}
          </Button>
        </CardActions>
      </StageCard>
    </Container>
  );
};

// Styled components
const Container = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const StageCard = styled(Card)`
  overflow: hidden;
  border-left: 4px solid ${({ theme }) => theme.colors?.primary || '#1976d2'};
`;

const CardHeader = styled.div`
  padding: 24px;
  background-color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.background || '#ffffff' // Use dark background in dark mode
      : theme.colors?.background || '#ffffff'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const StageTitle = styled.h2`
  color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  margin-bottom: 8px;
`;

const StageDescription = styled.p`
  color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : theme.colors?.text || '#000000'};
`;

const CardContent = styled.div`
  padding: 24px;
`;

const CardActions = styled.div`
  padding: 16px 24px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const Section = styled.div`
  margin-bottom: 24px;
`;

const SectionTitle = styled.h3`
  margin-bottom: 8px;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const SectionDescription = styled.p`
  margin-bottom: 16px;
  color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : theme.colors?.text || '#000000'};
`;

const AssessmentForm = styled.div`
margin - bottom: 24px;
padding: 16px;
background - color: ${ ({ theme }) => theme.colors?.paper || '#ffffff' };
border - radius: 8px;
box - shadow: ${ ({ theme }) => theme.shadows.sm };
`;

const FormRow = styled.div`
display: flex;
gap: 16px;
margin - bottom: 16px;

@media(max - width: 600px) {
  flex - direction: column;
}
`;

const FormGroup = styled.div<{ fullWidth?: boolean }>`
flex: ${ (props) => (props.fullWidth ? 1 : 0.5) };

@media(max - width: 600px) {
  flex: 1;
}
`;

const Label = styled.label`
display: block;
margin - bottom: 8px;
font - weight: 500;
color: ${ ({ theme }) => theme.colors?.text || '#000000' };
`;

const Input = styled.input`
width: 100 %;
padding: 8px 12px;
border: 1px solid ${ ({ theme }) => theme.colors?.border || '#e0e0e0' };
border - radius: 4px;
font - size: 1rem;

  &:focus {
  outline: none;
  border - color: ${ ({ theme }) => theme.colors?.primary || '#1976d2' };
  box - shadow: 0 0 0 2px ${ ({ theme }) => `${theme.colors?.primary || '#1976d2'}40` };
}
`;

const Select = styled.select`
width: 100 %;
padding: 8px 12px;
border: 1px solid ${ ({ theme }) => theme.colors?.border || '#e0e0e0' };
border - radius: 4px;
font - size: 1rem;
background - color: ${ ({ theme }) => theme.colors?.paper || '#ffffff' };

  &:focus {
  outline: none;
  border - color: ${ ({ theme }) => theme.colors?.primary || '#1976d2' };
  box - shadow: 0 0 0 2px ${ ({ theme }) => `${theme.colors?.primary || '#1976d2'}40` };
}
`;

const TextArea = styled.textarea`
width: 100 %;
padding: 8px 12px;
border: 1px solid ${ ({ theme }) => theme.colors?.border || '#e0e0e0' };
border - radius: 4px;
font - size: 1rem;
resize: vertical;

  &:focus {
  outline: none;
  border - color: ${ ({ theme }) => theme.colors?.primary || '#1976d2' };
  box - shadow: 0 0 0 2px ${ ({ theme }) => `${theme.colors?.primary || '#1976d2'}40` };
}
`;

const AddButton = styled(Button)`
margin - top: 8px;
`;

const AssessmentsList = styled.div`
display: flex;
flex - direction: column;
gap: 16px;
`;

const AssessmentItem = styled.div`
padding: 16px;
background - color: ${ ({ theme }) => theme.colors?.paper || '#ffffff' };
border - radius: 8px;
box - shadow: ${ ({ theme }) => theme.shadows.sm };
border - left: 4px solid ${ ({ theme }) => theme.colors?.primary || '#1976d2' };
`;

const AssessmentHeader = styled.div`
display: flex;
align - items: center;
margin - bottom: 12px;
`;

const AssessmentArea = styled.h4`
margin: 0;
flex: 1;
color: ${ ({ theme }) => theme.colors?.text || '#000000' };
`;

const PriorityBadge = styled.span`
margin - right: 16px;
padding: 4px 8px;
background - color: ${
  ({ theme }) =>
    theme.mode === 'dark'
      ? `${theme.colors?.background || '#ffffff'}CC` // Use dark background with opacity in dark mode
      : theme.colors?.background || '#ffffff'
};
border - radius: 4px;
font - size: 0.875rem;
color: ${
  ({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : theme.colors?.primary || '#1976d2'
};
`;

const RemoveButton = styled.button`
background: none;
border: none;
color: ${ ({ theme }) => theme.colors?.text || '#000000' };
cursor: pointer;
font - size: 1rem;
padding: 4px;

  &:hover {
  color: ${ ({ theme }) => theme.colors?.error || '#f44336' };
}
`;

const AssessmentDetails = styled.div`
display: flex;
flex - direction: column;
gap: 8px;
`;

const StateComparison = styled.div`
display: flex;
flex - direction: column;
gap: 8px;
margin - bottom: 8px;
`;

const CurrentState = styled.div`
font - size: 0.875rem;
color: ${ ({ theme }) => theme.colors?.text || '#000000' };
`;

const DesiredState = styled.div`
font - size: 0.875rem;
color: ${ ({ theme }) => theme.colors?.text || '#000000' };
`;

const StateLabel = styled.span`
font - weight: 500;
color: ${ ({ theme }) => theme.colors?.text || '#000000' };
`;

const GapReason = styled.div`
font - size: 0.875rem;
color: ${ ({ theme }) => theme.colors?.text || '#000000' };
`;

const ReasonLabel = styled.span`
font - weight: 500;
color: ${ ({ theme }) => theme.colors?.text || '#000000' };
`;

const PivotAction = styled.div`
font - size: 0.875rem;
color: ${ ({ theme }) => theme.colors?.text || '#000000' };
`;

const ActionLabel = styled.span`
font - weight: 500;
color: ${ ({ theme }) => theme.colors?.text || '#000000' };
`;

const EmptyState = styled.div`
padding: 24px;
text - align: center;
color: ${ ({ theme }) => theme.colors?.text || '#000000' };
background - color: ${ ({ theme }) => theme.colors?.paper || '#ffffff' };
border - radius: 8px;
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
opacity: ${ (props) => (props.visible ? 1 : 0) };
transition: opacity 0.3s ease;
text - align: center;
color: ${ ({ theme }) => theme.colors?.success || '#4caf50' };
margin - top: 16px;
`;
