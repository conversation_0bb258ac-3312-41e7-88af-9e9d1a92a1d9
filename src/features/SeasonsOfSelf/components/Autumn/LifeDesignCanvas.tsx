/**
 * Life Design Canvas Component
 *
 * This component implements life design thinking methodology for the Autumn season.
 * Users can prototype life experiments, iterate on decisions, and design their ideal life.
 * Based on Stanford's Design Thinking approach applied to life decisions.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import Card from '../../../../components/ui/Card';
import Button from '../../../../components/ui/Button';
import autoSaveService from '../../../DataPortability/services/autoSaveService';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

// Component props
interface LifeDesignCanvasProps {
  onComplete?: (data: LifeDesignData) => void;
  onBack?: () => void;
  initialData?: LifeDesignData;
}

// Life Design data structure
export interface LifeDesignData {
  id: string;
  currentSituation: {
    workLife: string;
    relationships: string;
    health: string;
    finances: string;
    personal: string;
  };
  idealVision: {
    workLife: string;
    relationships: string;
    health: string;
    finances: string;
    personal: string;
  };
  experiments: LifeExperiment[];
  prototypes: LifePrototype[];
  insights: string[];
  nextSteps: string[];
  completed: boolean;
}

interface LifeExperiment {
  id: string;
  title: string;
  description: string;
  hypothesis: string;
  duration: string;
  resources: string[];
  successMetrics: string[];
  status: 'planned' | 'active' | 'completed';
  results?: string;
  learnings?: string;
}

interface LifePrototype {
  id: string;
  area: string;
  description: string;
  lowRiskTest: string;
  timeline: string;
  feedback: string;
  iteration: string;
}

/**
 * Life Design Canvas Component
 */
export const LifeDesignCanvas: React.FC<LifeDesignCanvasProps> = ({
  onComplete,
  onBack,
  initialData
}) => {
  const { theme } = useTheme();

  // Initialize data
  const [designData, setDesignData] = useState<LifeDesignData>(
    initialData || {
      id: `life-design-${Date.now()}`,
      currentSituation: {
        workLife: '',
        relationships: '',
        health: '',
        finances: '',
        personal: '',
      },
      idealVision: {
        workLife: '',
        relationships: '',
        health: '',
        finances: '',
        personal: '',
      },
      experiments: [],
      prototypes: [],
      insights: [],
      nextSteps: [],
      completed: false,
    }
  );

  const [currentStep, setCurrentStep] = useState(0);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Auto-save setup
  const autoSave = React.useMemo(
    () =>
      autoSaveService.createEnhancedAutoSave({
        key: 'life_design_canvas',
        onSave: () => {
          setShowSaveIndicator(true);
          setTimeout(() => setShowSaveIndicator(false), 2000);
        },
      }),
    []
  );

  // Update data when design data changes
  useEffect(() => {
    autoSave.save(designData);
  }, [designData, autoSave]);

  const steps = [
    {
      title: 'Current Situation',
      description: 'Assess where you are now in key life areas.',
    },
    {
      title: 'Ideal Vision',
      description: 'Envision your ideal life in each area.',
    },
    {
      title: 'Life Experiments',
      description: 'Design small experiments to test new directions.',
    },
    {
      title: 'Prototypes',
      description: 'Create low-risk prototypes of potential changes.',
    },
    {
      title: 'Insights & Next Steps',
      description: 'Synthesize learnings and plan your next moves.',
    },
  ];

  const lifeAreas = [
    { key: 'workLife', label: 'Work & Career', icon: '💼' },
    { key: 'relationships', label: 'Relationships', icon: '❤️' },
    { key: 'health', label: 'Health & Wellness', icon: '🏃' },
    { key: 'finances', label: 'Finances', icon: '💰' },
    { key: 'personal', label: 'Personal Growth', icon: '🌱' },
  ];

  const handleSituationUpdate = (area: string, value: string) => {
    setDesignData(prev => ({
      ...prev,
      currentSituation: {
        ...prev.currentSituation,
        [area]: value,
      },
    }));
  };

  const handleVisionUpdate = (area: string, value: string) => {
    setDesignData(prev => ({
      ...prev,
      idealVision: {
        ...prev.idealVision,
        [area]: value,
      },
    }));
  };

  const handleExperimentAdd = (experiment: Omit<LifeExperiment, 'id'>) => {
    const newExperiment: LifeExperiment = {
      ...experiment,
      id: `experiment-${Date.now()}`,
    };

    setDesignData(prev => ({
      ...prev,
      experiments: [...prev.experiments, newExperiment],
    }));
  };

  const handlePrototypeAdd = (prototype: Omit<LifePrototype, 'id'>) => {
    const newPrototype: LifePrototype = {
      ...prototype,
      id: `prototype-${Date.now()}`,
    };

    setDesignData(prev => ({
      ...prev,
      prototypes: [...prev.prototypes, newPrototype],
    }));
  };

  const handleInsightAdd = (insight: string) => {
    if (insight.trim()) {
      setDesignData(prev => ({
        ...prev,
        insights: [...prev.insights, insight.trim()],
      }));
    }
  };

  const handleNextStepAdd = (step: string) => {
    if (step.trim()) {
      setDesignData(prev => ({
        ...prev,
        nextSteps: [...prev.nextSteps, step.trim()],
      }));
    }
  };

  const canProceed = () => {
    switch (currentStep) {
      case 0:
        return Object.values(designData.currentSituation).every(value => value.length > 20);
      case 1:
        return Object.values(designData.idealVision).every(value => value.length > 20);
      case 2:
        return designData.experiments.length >= 2;
      case 3:
        return designData.prototypes.length >= 2;
      case 4:
        return designData.insights.length >= 3 && designData.nextSteps.length >= 3;
      default:
        return false;
    }
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // Complete assessment
      const completedData = {
        ...designData,
        completed: true,
      };
      setDesignData(completedData);
      if (onComplete) {
        onComplete(completedData);
      }
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const currentStepData = steps[currentStep];

  return (
    <Container as={motion.div} variants={containerVariants} initial="hidden" animate="visible">
      <CanvasCard elevation="medium" as={motion.div} variants={itemVariants}>
        <CardHeader>
          <Title>Life Design Canvas</Title>
          <Description>
            Use design thinking principles to prototype and iterate on your ideal life.
          </Description>
          <ProgressBar>
            <ProgressFill progress={(currentStep + 1) / steps.length * 100} />
          </ProgressBar>
          <ProgressText>
            Step {currentStep + 1} of {steps.length}: {currentStepData.title}
          </ProgressText>
        </CardHeader>

        <CardContent>
          <StepContainer>
            <StepHeader>
              <StepTitle>{currentStepData.title}</StepTitle>
              <StepDescription>{currentStepData.description}</StepDescription>
            </StepHeader>

            {currentStep === 0 && (
              <SituationAssessment>
                <SectionTitle>Current Situation Assessment</SectionTitle>
                <SectionInstructions>
                  Honestly assess where you are now in each life area. Be specific about what's working and what isn't.
                </SectionInstructions>
                {lifeAreas.map(area => (
                  <AreaContainer key={area.key}>
                    <AreaHeader>
                      <AreaIcon>{area.icon}</AreaIcon>
                      <AreaLabel>{area.label}</AreaLabel>
                    </AreaHeader>
                    <AreaTextArea
                      value={designData.currentSituation[area.key as keyof typeof designData.currentSituation]}
                      onChange={(e) => handleSituationUpdate(area.key, e.target.value)}
                      placeholder={`Describe your current ${area.label.toLowerCase()} situation...`}
                      rows={3}
                    />
                  </AreaContainer>
                ))}
              </SituationAssessment>
            )}

            {currentStep === 1 && (
              <VisionDesign>
                <SectionTitle>Ideal Vision Design</SectionTitle>
                <SectionInstructions>
                  Imagine your ideal life in each area. Be specific and ambitious while staying realistic.
                </SectionInstructions>
                {lifeAreas.map(area => (
                  <AreaContainer key={area.key}>
                    <AreaHeader>
                      <AreaIcon>{area.icon}</AreaIcon>
                      <AreaLabel>{area.label}</AreaLabel>
                    </AreaHeader>
                    <AreaTextArea
                      value={designData.idealVision[area.key as keyof typeof designData.idealVision]}
                      onChange={(e) => handleVisionUpdate(area.key, e.target.value)}
                      placeholder={`Describe your ideal ${area.label.toLowerCase()}...`}
                      rows={3}
                    />
                  </AreaContainer>
                ))}
              </VisionDesign>
            )}

            {currentStep === 2 && (
              <ExperimentsDesign>
                <SectionTitle>Life Experiments</SectionTitle>
                <SectionInstructions>
                  Design small, low-risk experiments to test new directions. Think of these as "life prototypes."
                </SectionInstructions>
                <ExperimentForm onSubmit={handleExperimentAdd} />
                <ExperimentsList>
                  {designData.experiments.map(experiment => (
                    <ExperimentCard key={experiment.id}>
                      <ExperimentTitle>{experiment.title}</ExperimentTitle>
                      <ExperimentDescription>{experiment.description}</ExperimentDescription>
                      <ExperimentHypothesis>Hypothesis: {experiment.hypothesis}</ExperimentHypothesis>
                    </ExperimentCard>
                  ))}
                </ExperimentsList>
              </ExperimentsDesign>
            )}

            {currentStep === 3 && (
              <PrototypesDesign>
                <SectionTitle>Life Prototypes</SectionTitle>
                <SectionInstructions>
                  Create low-risk ways to test potential life changes before committing fully.
                </SectionInstructions>
                <PrototypeForm onSubmit={handlePrototypeAdd} />
                <PrototypesList>
                  {designData.prototypes.map(prototype => (
                    <PrototypeCard key={prototype.id}>
                      <PrototypeArea>{prototype.area}</PrototypeArea>
                      <PrototypeDescription>{prototype.description}</PrototypeDescription>
                      <PrototypeTest>Test: {prototype.lowRiskTest}</PrototypeTest>
                    </PrototypeCard>
                  ))}
                </PrototypesList>
              </PrototypesDesign>
            )}

            {currentStep === 4 && (
              <InsightsAndSteps>
                <SectionTitle>Insights & Next Steps</SectionTitle>
                <SectionInstructions>
                  Synthesize your learnings and create actionable next steps.
                </SectionInstructions>

                <InsightsSection>
                  <SubSectionTitle>Key Insights</SubSectionTitle>
                  <SimpleInput
                    placeholder="What have you learned about yourself and your life?"
                    onSubmit={handleInsightAdd}
                    buttonText="Add Insight"
                  />
                  <ItemsList>
                    {designData.insights.map((insight, index) => (
                      <InsightItem key={index}>{insight}</InsightItem>
                    ))}
                  </ItemsList>
                </InsightsSection>

                <NextStepsSection>
                  <SubSectionTitle>Next Steps</SubSectionTitle>
                  <SimpleInput
                    placeholder="What specific actions will you take?"
                    onSubmit={handleNextStepAdd}
                    buttonText="Add Next Step"
                  />
                  <ItemsList>
                    {designData.nextSteps.map((step, index) => (
                      <NextStepItem key={index}>{step}</NextStepItem>
                    ))}
                  </ItemsList>
                </NextStepsSection>
              </InsightsAndSteps>
            )}

          </StepContainer>

          <SaveIndicator visible={showSaveIndicator}>Changes saved automatically</SaveIndicator>
        </CardContent>

        <CardActions>
          {currentStep > 0 && (
            <Button variant="outlined" onClick={handlePrevious}>
              Previous
            </Button>
          )}
          {onBack && currentStep === 0 && (
            <Button variant="outlined" onClick={onBack}>
              Back
            </Button>
          )}
          <div style={{ flex: 1 }} />
          <Button
            variant="primary"
            onClick={handleNext}
            disabled={!canProceed()}
          >
            {currentStep === steps.length - 1 ? 'Complete Life Design' : 'Next'}
          </Button>
        </CardActions>
      </CanvasCard>
    </Container>
  );
};

// Helper Components
const SimpleInput: React.FC<{
  placeholder: string;
  onSubmit: (value: string) => void;
  buttonText: string;
}> = ({ placeholder, onSubmit, buttonText }) => {
  const [value, setValue] = useState('');

  const handleSubmit = () => {
    if (value.trim()) {
      onSubmit(value.trim());
      setValue('');
    }
  };

  return (
    <InputContainer>
      <Input
        value={value}
        onChange={(e) => setValue(e.target.value)}
        placeholder={placeholder}
        onKeyPress={(e) => e.key === 'Enter' && handleSubmit()}
      />
      <Button variant="primary" onClick={handleSubmit} disabled={!value.trim()}>
        {buttonText}
      </Button>
    </InputContainer>
  );
};

const ExperimentForm: React.FC<{
  onSubmit: (experiment: Omit<LifeExperiment, 'id'>) => void;
}> = ({ onSubmit }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    hypothesis: '',
    duration: '',
  });

  const handleSubmit = () => {
    if (formData.title && formData.description && formData.hypothesis) {
      onSubmit({
        ...formData,
        resources: [],
        successMetrics: [],
        status: 'planned' as const,
      });
      setFormData({ title: '', description: '', hypothesis: '', duration: '' });
    }
  };

  return (
    <FormContainer>
      <FormRow>
        <Input
          placeholder="Experiment title"
          value={formData.title}
          onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
        />
        <Input
          placeholder="Duration (e.g., 2 weeks)"
          value={formData.duration}
          onChange={(e) => setFormData(prev => ({ ...prev, duration: e.target.value }))}
        />
      </FormRow>
      <TextArea
        placeholder="Describe the experiment..."
        value={formData.description}
        onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
        rows={2}
      />
      <TextArea
        placeholder="What hypothesis are you testing?"
        value={formData.hypothesis}
        onChange={(e) => setFormData(prev => ({ ...prev, hypothesis: e.target.value }))}
        rows={2}
      />
      <Button variant="primary" onClick={handleSubmit}>Add Experiment</Button>
    </FormContainer>
  );
};

const PrototypeForm: React.FC<{
  onSubmit: (prototype: Omit<LifePrototype, 'id'>) => void;
}> = ({ onSubmit }) => {
  const [formData, setFormData] = useState({
    area: '',
    description: '',
    lowRiskTest: '',
    timeline: '',
  });

  const handleSubmit = () => {
    if (formData.area && formData.description && formData.lowRiskTest) {
      onSubmit({
        ...formData,
        feedback: '',
        iteration: '',
      });
      setFormData({ area: '', description: '', lowRiskTest: '', timeline: '' });
    }
  };

  return (
    <FormContainer>
      <FormRow>
        <Input
          placeholder="Life area (e.g., Career, Health)"
          value={formData.area}
          onChange={(e) => setFormData(prev => ({ ...prev, area: e.target.value }))}
        />
        <Input
          placeholder="Timeline"
          value={formData.timeline}
          onChange={(e) => setFormData(prev => ({ ...prev, timeline: e.target.value }))}
        />
      </FormRow>
      <TextArea
        placeholder="Describe the prototype..."
        value={formData.description}
        onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
        rows={2}
      />
      <TextArea
        placeholder="How will you test this with low risk?"
        value={formData.lowRiskTest}
        onChange={(e) => setFormData(prev => ({ ...prev, lowRiskTest: e.target.value }))}
        rows={2}
      />
      <Button variant="primary" onClick={handleSubmit}>Add Prototype</Button>
    </FormContainer>
  );
};

// Styled components
const Container = styled.div`
  max-width: 900px;
  margin: 0 auto;
`;

const CanvasCard = styled(Card)`
  overflow: hidden;
  border-left: 4px solid ${({ theme }) => theme.colors?.primary || '#1976d2'};
`;

const CardHeader = styled.div`
  padding: 24px;
  background-color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.background || '#ffffff'
      : theme.colors?.background || '#ffffff'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const Title = styled.h2`
  color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  margin-bottom: 8px;
`;

const Description = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'Secondary};
  margin-bottom: 16px;
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 8px;
  background-color: ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
`;

const ProgressFill = styled.div<{ progress: number }>`
  width: ${({ progress }) => progress}%;
  height: 100%;
  background-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  transition: width 0.3s ease;
`;

const ProgressText = styled.p`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors?.text || '#000000'Secondary};
  text-align: center;
`;

const CardContent = styled.div`
  padding: 24px;
`;

const CardActions = styled.div`
  padding: 16px 24px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-top: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${({ visible }) => (visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  color: ${({ theme }) => theme.colors?.success || '#4caf50'};
  font-size: 0.875rem;
  text-align: center;
  margin-top: 16px;
`;

const StepContainer = styled.div`
  margin-bottom: 24px;
`;

const StepHeader = styled.div`
  margin-bottom: 24px;
`;

const StepTitle = styled.h3`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 8px;
`;

const StepDescription = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'Secondary};
  font-size: 0.9rem;
`;

// Common section styles
const SectionTitle = styled.h4`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 12px;
`;

const SectionInstructions = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'Secondary};
  margin-bottom: 20px;
  font-style: italic;
`;

const SubSectionTitle = styled.h5`
  color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  margin-bottom: 12px;
`;

// Area assessment styles
const SituationAssessment = styled.div``;
const VisionDesign = styled.div``;

const AreaContainer = styled.div`
  margin-bottom: 20px;
  padding: 16px;
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 8px;
`;

const AreaHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
`;

const AreaIcon = styled.span`
  font-size: 1.2rem;
`;

const AreaLabel = styled.h5`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin: 0;
`;

const AreaTextArea = styled.textarea`
  width: 100%;
  padding: 12px;
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 4px;
  resize: vertical;
  font-family: inherit;
`;

// Form styles
const FormContainer = styled.div`
  margin-bottom: 20px;
  padding: 16px;
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 8px;
  background-color: ${({ theme }) => theme.colors?.background || '#ffffff'};
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 12px;
`;

const Input = styled.input`
  padding: 8px 12px;
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 4px;
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: 8px 12px;
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 4px;
  resize: vertical;
  margin-bottom: 8px;
`;

const InputContainer = styled.div`
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
`;

// List styles
const ExperimentsDesign = styled.div``;
const PrototypesDesign = styled.div``;
const InsightsAndSteps = styled.div``;

const ExperimentsList = styled.div`
  display: grid;
  gap: 12px;
`;

const ExperimentCard = styled.div`
  padding: 16px;
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 8px;
`;

const ExperimentTitle = styled.h5`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 8px;
`;

const ExperimentDescription = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'Secondary};
  margin-bottom: 8px;
`;

const ExperimentHypothesis = styled.p`
  color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  font-style: italic;
`;

const PrototypesList = styled.div`
  display: grid;
  gap: 12px;
`;

const PrototypeCard = styled.div`
  padding: 16px;
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 8px;
`;

const PrototypeArea = styled.h5`
  color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  margin-bottom: 8px;
`;

const PrototypeDescription = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'Secondary};
  margin-bottom: 8px;
`;

const PrototypeTest = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  font-weight: 500;
`;

const InsightsSection = styled.div`
  margin-bottom: 24px;
`;

const NextStepsSection = styled.div``;

const ItemsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const InsightItem = styled.div`
  padding: 12px;
  background-color: ${({ theme }) => `${theme.colors?.primary || '#1976d2'}10`};
  border-left: 4px solid ${({ theme }) => theme.colors?.primary || '#1976d2'};
  border-radius: 4px;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const NextStepItem = styled.div`
  padding: 12px;
  background-color: ${({ theme }) => theme.colors?.background || '#ffffff'};
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 4px;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;
