/**
 * Ikigai Assessment Component
 *
 * This component implements the Japanese concept of Ikigai (life's purpose)
 * through an interactive assessment of the four key areas:
 * - What you love (passion)
 * - What you're good at (mission)
 * - What the world needs (vocation)
 * - What you can be paid for (profession)
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useSeasonsOfSelf } from '../../context/SeasonsOfSelfContext';
import Card from '../../../../components/ui/Card';
import Button from '../../../../components/ui/Button';
import autoSaveService from '../../../DataPortability/services/autoSaveService';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

// Component props
interface IkigaiAssessmentProps {
  onComplete?: () => void;
  onBack?: () => void;
}

// Ikigai data structure
export interface IkigaiData {
  id: string;
  whatYouLove: string[];
  whatYoureGoodAt: string[];
  whatTheWorldNeeds: string[];
  whatYouCanBePaidFor: string[];
  ikigaiStatement: string;
  completed: boolean;
}

/**
 * Ikigai Assessment Component
 */
export const IkigaiAssessment: React.FC<IkigaiAssessmentProps> = ({ onComplete, onBack }) => {
  const { theme } = useTheme();
  const { data, updateData, stages, updateStageCompletion } = useSeasonsOfSelf();

  // Get saved data or initialize with defaults
  const [ikigaiData, setIkigaiData] = useState<IkigaiData>(
    data.winter && data.winter.ikigaiData
      ? (data.winter.ikigaiData as IkigaiData)
      : {
        id: `ikigai-${Date.now()}`,
        whatYouLove: [],
        whatYoureGoodAt: [],
        whatTheWorldNeeds: [],
        whatYouCanBePaidFor: [],
        ikigaiStatement: '',
        completed: false,
      }
  );

  const [currentSection, setCurrentSection] = useState(0);
  const [newItem, setNewItem] = useState('');
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Auto-save setup
  const autoSave = React.useMemo(
    () =>
      autoSaveService.createEnhancedAutoSave({
        key: 'winter_ikigai_assessment',
        onSave: () => {
          setShowSaveIndicator(true);
          setTimeout(() => setShowSaveIndicator(false), 2000);
        },
      }),
    []
  );

  // Update context data when ikigai data changes
  useEffect(() => {
    updateData('winter', 'ikigaiData', ikigaiData);

    // Auto-save data
    autoSave.save(ikigaiData);

    // Check completion
    const isComplete =
      ikigaiData.whatYouLove.length >= 3 &&
      ikigaiData.whatYoureGoodAt.length >= 3 &&
      ikigaiData.whatTheWorldNeeds.length >= 3 &&
      ikigaiData.whatYouCanBePaidFor.length >= 3 &&
      ikigaiData.ikigaiStatement.length > 0;

    setIkigaiData(prev => ({ ...prev, completed: isComplete }));

    // Update stage completion
    const callingStage = stages.find((stage) => stage.id === 'calling');
    if (callingStage && !callingStage.completed && isComplete) {
      updateStageCompletion('calling', true);
    } else if (callingStage && callingStage.completed && !isComplete) {
      updateStageCompletion('calling', false);
    }
  }, [ikigaiData, updateData, stages, autoSave, updateStageCompletion]);

  const sections = [
    {
      title: 'What You Love',
      key: 'whatYouLove' as keyof IkigaiData,
      description: 'What activities, subjects, or causes make you feel energized and passionate?',
      placeholder: 'e.g., Teaching others, creating art, solving problems...',
      color: '#FF6B6B',
    },
    {
      title: 'What You\'re Good At',
      key: 'whatYoureGoodAt' as keyof IkigaiData,
      description: 'What are your natural talents, skills, and strengths?',
      placeholder: 'e.g., Communication, analytical thinking, empathy...',
      color: '#4ECDC4',
    },
    {
      title: 'What the World Needs',
      key: 'whatTheWorldNeeds' as keyof IkigaiData,
      description: 'What problems or needs do you see that you could help address?',
      placeholder: 'e.g., Environmental protection, education, healthcare...',
      color: '#45B7D1',
    },
    {
      title: 'What You Can Be Paid For',
      key: 'whatYouCanBePaidFor' as keyof IkigaiData,
      description: 'What skills or services could provide you with income?',
      placeholder: 'e.g., Consulting, writing, design services...',
      color: '#96CEB4',
    },
  ];

  const currentSectionData = sections[currentSection];

  const handleAddItem = () => {
    if (newItem.trim()) {
      const key = currentSectionData.key;
      setIkigaiData(prev => ({
        ...prev,
        [key]: [...(prev[key] as string[]), newItem.trim()],
      }));
      setNewItem('');
    }
  };

  const handleRemoveItem = (sectionKey: keyof IkigaiData, index: number) => {
    setIkigaiData(prev => ({
      ...prev,
      [sectionKey]: (prev[sectionKey] as string[]).filter((_, i) => i !== index),
    }));
  };

  const handleNext = () => {
    if (currentSection < sections.length - 1) {
      setCurrentSection(currentSection + 1);
    } else {
      // Move to Ikigai statement
      setCurrentSection(sections.length);
    }
  };

  const handlePrevious = () => {
    if (currentSection > 0) {
      setCurrentSection(currentSection - 1);
    }
  };

  const handleIkigaiStatementChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setIkigaiData(prev => ({
      ...prev,
      ikigaiStatement: e.target.value,
    }));
  };

  const isCurrentSectionComplete = () => {
    if (currentSection < sections.length) {
      const key = currentSectionData.key;
      return (ikigaiData[key] as string[]).length >= 3;
    }
    return ikigaiData.ikigaiStatement.length > 0;
  };

  const isAssessmentComplete = () => {
    return ikigaiData.whatYouLove.length >= 3 &&
      ikigaiData.whatYoureGoodAt.length >= 3 &&
      ikigaiData.whatTheWorldNeeds.length >= 3 &&
      ikigaiData.whatYouCanBePaidFor.length >= 3 &&
      ikigaiData.ikigaiStatement.length > 0;
  };

  return (
    <Container as={motion.div} variants={containerVariants} initial="hidden" animate="visible">
      <AssessmentCard elevation="medium" as={motion.div} variants={itemVariants}>
        <CardHeader>
          <Title>Ikigai Assessment</Title>
          <Description>
            Discover your Ikigai - your reason for being. Complete each section to find the intersection
            of what you love, what you're good at, what the world needs, and what you can be paid for.
          </Description>
          <ProgressBar>
            <ProgressFill progress={(currentSection + 1) / (sections.length + 1) * 100} />
          </ProgressBar>
          <ProgressText>
            Step {currentSection + 1} of {sections.length + 1}
          </ProgressText>
        </CardHeader>

        <CardContent>
          {currentSection < sections.length ? (
            <SectionContainer>
              <SectionHeader color={currentSectionData.color}>
                <SectionTitle>{currentSectionData.title}</SectionTitle>
                <SectionDescription>{currentSectionData.description}</SectionDescription>
              </SectionHeader>

              <InputContainer>
                <Input
                  type="text"
                  value={newItem}
                  onChange={(e) => setNewItem(e.target.value)}
                  placeholder={currentSectionData.placeholder}
                  onKeyPress={(e) => e.key === 'Enter' && handleAddItem()}
                />
                <AddButton onClick={handleAddItem} disabled={!newItem.trim()}>
                  Add
                </AddButton>
              </InputContainer>

              <ItemsList>
                {(ikigaiData[currentSectionData.key] as string[]).map((item, index) => (
                  <ItemTag key={index} color={currentSectionData.color}>
                    <ItemText>{item}</ItemText>
                    <RemoveButton onClick={() => handleRemoveItem(currentSectionData.key, index)}>
                      ×
                    </RemoveButton>
                  </ItemTag>
                ))}
              </ItemsList>

              <RequirementText>
                Add at least 3 items ({(ikigaiData[currentSectionData.key] as string[]).length}/3)
              </RequirementText>
            </SectionContainer>
          ) : (
            <IkigaiStatementSection>
              <SectionTitle>Your Ikigai Statement</SectionTitle>
              <SectionDescription>
                Based on your responses above, write a statement that captures your Ikigai - your reason for being.
              </SectionDescription>
              <StatementTextArea
                value={ikigaiData.ikigaiStatement}
                onChange={handleIkigaiStatementChange}
                placeholder="My Ikigai is to use my [strengths] to [what you love] in service of [what the world needs] through [what you can be paid for]..."
                rows={4}
              />
            </IkigaiStatementSection>
          )}

          <SaveIndicator visible={showSaveIndicator}>Changes saved automatically</SaveIndicator>
        </CardContent>

        <CardActions>
          {currentSection > 0 && (
            <Button variant="outlined" onClick={handlePrevious}>
              Previous
            </Button>
          )}
          {onBack && currentSection === 0 && (
            <Button variant="outlined" onClick={onBack}>
              Back to Calling
            </Button>
          )}
          <div style={{ flex: 1 }} />
          {currentSection < sections.length ? (
            <Button
              variant="primary"
              onClick={handleNext}
              disabled={!isCurrentSectionComplete()}
            >
              {isCurrentSectionComplete() ? 'Next' : `Add ${3 - (ikigaiData[currentSectionData.key] as string[]).length} more items`}
            </Button>
          ) : (
            <Button
              variant="primary"
              onClick={onComplete}
              disabled={!isAssessmentComplete()}
            >
              {isAssessmentComplete() ? 'Complete Ikigai Assessment' : 'Complete your Ikigai statement'}
            </Button>
          )}
        </CardActions>
      </AssessmentCard>
    </Container>
  );
};

// Styled components
const Container = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const AssessmentCard = styled(Card)`
  overflow: hidden;
  border-left: 4px solid ${({ theme }) => theme.colors?.primary || '#1976d2'};
`;

const CardHeader = styled.div`
  padding: 24px;
  background-color: ${({ theme }) => theme.colors?.background || '#f5f5f5'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const Title = styled.h2`
  color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  margin-bottom: 8px;
`;

const Description = styled.p`
  color: ${({ theme }) => theme.colors?.textSecondary || '#666666'};
  margin-bottom: 16px;
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 8px;
  background-color: ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
`;

const ProgressFill = styled.div<{ progress: number }>`
  width: ${({ progress }) => progress}%;
  height: 100%;
  background-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  transition: width 0.3s ease;
`;

const ProgressText = styled.p`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors?.textSecondary || '#666666'};
  text-align: center;
`;

const CardContent = styled.div`
  padding: 24px;
`;

const CardActions = styled.div`
  padding: 16px 24px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-top: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const SectionContainer = styled.div`
  margin-bottom: 24px;
`;

const SectionHeader = styled.div<{ color: string }>`
  margin-bottom: 20px;
  padding: 16px;
  border-left: 4px solid ${({ color }) => color};
  background-color: ${({ color }) => `${color}10`};
  border-radius: 0 8px 8px 0;
`;

const SectionTitle = styled.h3`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 8px;
`;

const SectionDescription = styled.p`
  color: ${({ theme }) => theme.colors?.textSecondary || '#666666'};
  font-size: 0.9rem;
`;

const InputContainer = styled.div`
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
`;

const Input = styled.input`
  flex: 1;
  padding: 12px;
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 4px;
  font-size: 1rem;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
    box-shadow: 0 0 0 2px ${({ theme }) => `${theme.colors?.primary || '#1976d2'}40`};
  }
`;

const AddButton = styled(Button)`
  min-width: 80px;
`;

const ItemsList = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
  min-height: 40px;
`;

const ItemTag = styled.div<{ color: string }>`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: ${({ color }) => `${color}20`};
  border: 1px solid ${({ color }) => `${color}40`};
  border-radius: 20px;
  font-size: 0.875rem;
`;

const ItemText = styled.span`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const RemoveButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors?.textSecondary || '#666666'};
  cursor: pointer;
  font-size: 1.2rem;
  line-height: 1;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: ${({ theme }) => theme.colors?.error || '#f44336'};
  }
`;

const RequirementText = styled.p`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors?.textSecondary || '#666666'};
  font-style: italic;
`;

const IkigaiStatementSection = styled.div`
  margin-bottom: 24px;
`;

const StatementTextArea = styled.textarea`
  width: 100%;
  padding: 12px;
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 4px;
  font-size: 1rem;
  resize: vertical;
  min-height: 120px;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
    box-shadow: 0 0 0 2px ${({ theme }) => `${theme.colors?.primary || '#1976d2'}40`};
  }
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${({ visible }) => (visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  color: ${({ theme }) => theme.colors?.success || '#4caf50'};
  font-size: 0.875rem;
  text-align: center;
  margin-top: 16px;
`;
