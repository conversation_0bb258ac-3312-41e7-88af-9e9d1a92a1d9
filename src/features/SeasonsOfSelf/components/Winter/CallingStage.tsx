/**
 * Calling Stage Component
 *
 * This component represents the first stage of the Winter season,
 * focusing on discovering one's deeper calling and purpose.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useSeasonsOfSelf } from '../../context/SeasonsOfSelfContext';
import Card from '../../../../components/ui/Card';
import Button from '../../../../components/ui/Button';
import autoSaveService from '../../../DataPortability/services/autoSaveService';
import { IkigaiAssessment } from './IkigaiAssessment';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

// Component props
interface CallingStageProps {
  onComplete?: () => void;
}

// Calling reflection type
interface CallingReflection {
  id: string;
  strengths: string;
  passions: string;
  values: string;
  impact: string;
  legacy: string;
  callingStatement: string;
}

/**
 * Calling Stage Component
 */
export const CallingStage: React.FC<CallingStageProps> = ({ onComplete }) => {
  // Theme is used in styled components via ThemeProvider
  useTheme();
  const { data, updateData, stages, updateStageCompletion } = useSeasonsOfSelf();

  // State for choosing assessment method
  const [assessmentMethod, setAssessmentMethod] = useState<'choose' | 'reflection' | 'ikigai'>(
    'choose'
  );

  // Get saved data or initialize with defaults
  const [reflection, setReflection] = useState<CallingReflection>(
    data.winter && data.winter.callingReflection
      ? data.winter.callingReflection
      : {
          id: `calling-${Date.now()}`,
          strengths: '',
          passions: '',
          values: '',
          impact: '',
          legacy: '',
          callingStatement: '',
        }
  );

  // Save indicator state
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Create auto-save instance
  const autoSave = React.useMemo(
    () =>
      autoSaveService.createEnhancedAutoSave({
        key: 'winter_calling_reflection',
        onSave: () => {
          setShowSaveIndicator(true);
          setTimeout(() => setShowSaveIndicator(false), 2000);
        },
      }),
    []
  );

  // Update context data when reflection changes
  useEffect(() => {
    updateData('winter', 'callingReflection', reflection);

    // Auto-save data
    autoSave.save(reflection);

    // Mark stage as completed if all required fields are filled
    const callingStage = stages.find((stage) => stage.id === 'calling');
    const isComplete = Boolean(
      reflection.strengths &&
        reflection.passions &&
        reflection.values &&
        reflection.impact &&
        reflection.callingStatement
    );

    if (callingStage && !callingStage.completed && isComplete) {
      updateStageCompletion('calling', true);
    } else if (callingStage && callingStage.completed && !isComplete) {
      updateStageCompletion('calling', false);
    }
  }, [reflection, updateData, stages, autoSave, updateStageCompletion]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setReflection((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Check if all required fields are filled
  const isComplete = Boolean(
    reflection.strengths &&
      reflection.passions &&
      reflection.values &&
      reflection.impact &&
      reflection.callingStatement
  );

  // Handle completion
  const handleComplete = () => {
    if (onComplete) {
      onComplete();
    }
  };

  // Check if user has existing data to determine initial method
  useEffect(() => {
    if (data.winter?.ikigaiData) {
      setAssessmentMethod('ikigai');
    } else if (data.winter?.callingReflection && data.winter.callingReflection.callingStatement) {
      setAssessmentMethod('reflection');
    }
  }, [data.winter]);

  // Render method selection
  if (assessmentMethod === 'choose') {
    return (
      <Container as={motion.div} variants={containerVariants} initial="hidden" animate="visible">
        <StageCard elevation="medium" as={motion.div} variants={itemVariants}>
          <CardHeader>
            <StageTitle>Calling Stage</StageTitle>
            <StageDescription>
              Choose your preferred method to discover your deeper purpose and calling in life.
            </StageDescription>
          </CardHeader>

          <CardContent>
            <MethodSelection>
              <MethodOption onClick={() => setAssessmentMethod('ikigai')}>
                <MethodTitle>🎯 Ikigai Assessment</MethodTitle>
                <MethodDescription>
                  Use the Japanese concept of Ikigai to find the intersection of what you love, what
                  you&apos;re good at, what the world needs, and what you can be paid for.
                </MethodDescription>
              </MethodOption>

              <MethodOption onClick={() => setAssessmentMethod('reflection')}>
                <MethodTitle>💭 Guided Reflection</MethodTitle>
                <MethodDescription>
                  Use guided questions to reflect on your strengths, passions, values, and desired
                  impact to articulate your calling.
                </MethodDescription>
              </MethodOption>
            </MethodSelection>
          </CardContent>
        </StageCard>
      </Container>
    );
  }

  // Render Ikigai Assessment
  if (assessmentMethod === 'ikigai') {
    return (
      <IkigaiAssessment onComplete={onComplete} onBack={() => setAssessmentMethod('choose')} />
    );
  }

  // Render traditional reflection (existing code)
  return (
    <Container as={motion.div} variants={containerVariants} initial="hidden" animate="visible">
      <StageCard elevation="medium" as={motion.div} variants={itemVariants}>
        <CardHeader>
          <StageTitle>Calling Stage - Guided Reflection</StageTitle>
          <StageDescription>
            Reflect on your unique strengths, passions, and values to articulate your personal
            calling.
          </StageDescription>
          <div style={{ marginTop: '12px' }}>
            <Button variant="outlined" size="small" onClick={() => setAssessmentMethod('choose')}>
              Switch Method
            </Button>
          </div>
        </CardHeader>

        <CardContent>
          <Section>
            <SectionTitle>Your Calling Reflection</SectionTitle>
            <SectionDescription>
              Reflect deeply on the questions below to help articulate your personal calling. Take
              your time with each question, allowing yourself to explore what truly resonates with
              you.
            </SectionDescription>

            <ReflectionForm>
              <FormGroup>
                <Label htmlFor="strengths">What are your unique strengths and gifts?</Label>
                <TextArea
                  id="strengths"
                  name="strengths"
                  value={reflection.strengths}
                  onChange={handleInputChange}
                  placeholder="What are you naturally good at? What skills have you developed that bring you satisfaction?"
                  rows={3}
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor="passions">What are you passionate about?</Label>
                <TextArea
                  id="passions"
                  name="passions"
                  value={reflection.passions}
                  onChange={handleInputChange}
                  placeholder="What activities or causes energize you? What could you talk about for hours?"
                  rows={3}
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor="values">What are your core values?</Label>
                <TextArea
                  id="values"
                  name="values"
                  value={reflection.values}
                  onChange={handleInputChange}
                  placeholder="What principles guide your decisions? What matters most to you in life?"
                  rows={3}
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor="impact">How do you want to impact others?</Label>
                <TextArea
                  id="impact"
                  name="impact"
                  value={reflection.impact}
                  onChange={handleInputChange}
                  placeholder="How do you want to serve or contribute to others? What difference do you want to make?"
                  rows={3}
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor="legacy">What legacy do you want to leave?</Label>
                <TextArea
                  id="legacy"
                  name="legacy"
                  value={reflection.legacy}
                  onChange={handleInputChange}
                  placeholder="How do you want to be remembered? What mark do you want to leave on the world?"
                  rows={3}
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor="callingStatement">Your Calling Statement</Label>
                <TextArea
                  id="callingStatement"
                  name="callingStatement"
                  value={reflection.callingStatement}
                  onChange={handleInputChange}
                  placeholder="Based on your reflections above, write a statement that captures your sense of calling or purpose."
                  rows={4}
                />
                <HelperText>
                  Example: &quot;I am called to use my creative talents to inspire others to connect
                  with nature and protect our environment.&quot;
                </HelperText>
              </FormGroup>
            </ReflectionForm>
          </Section>

          <SaveIndicator visible={showSaveIndicator}>Changes saved automatically</SaveIndicator>
        </CardContent>

        <CardActions>
          <Button variant="primary" onClick={handleComplete} disabled={!isComplete}>
            {!isComplete
              ? 'Complete all reflection questions to continue'
              : 'Continue to Purpose Stage'}
          </Button>
        </CardActions>
      </StageCard>
    </Container>
  );
};

// Styled components
const Container = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const StageCard = styled(Card)`
  overflow: hidden;
  border-left: 4px solid ${({ theme }) => theme.colors?.primary || '#1976d2'};
`;

const CardHeader = styled.div`
  padding: 24px;
  background-color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.background || '#ffffff' // Use dark background in dark mode
      : theme.colors?.background || '#ffffff'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const StageTitle = styled.h2`
  color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  margin-bottom: 8px;
`;

const StageDescription = styled.p`
  color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : theme.colors?.text || '#000000'};
`;

const CardContent = styled.div`
  padding: 24px;
`;

const CardActions = styled.div`
  padding: 16px 24px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const Section = styled.div`
  margin-bottom: 24px;
`;

const SectionTitle = styled.h3`
  margin-bottom: 8px;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const SectionDescription = styled.p`
  margin-bottom: 16px;
  color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : theme.colors?.text || '#000000'};
`;

const ReflectionForm = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
`;

const Label = styled.label`
  margin-bottom: 8px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: 12px;
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 4px;
  font-size: 1rem;
  resize: vertical;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
    box-shadow: 0 0 0 2px ${({ theme }) => `${theme.colors?.primary || '#1976d2'}40`};
  }
`;

const HelperText = styled.p`
  margin-top: 8px;
  font-size: 0.875rem;
  color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : theme.colors?.text || '#000000'};
  font-style: italic;
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  text-align: center;
  color: ${({ theme }) => theme.colors?.success || '#4caf50'};
  margin-top: 16px;
`;

const MethodSelection = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin: 24px 0;
`;

const MethodOption = styled.div`
  padding: 20px;
  border: 2px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
    background-color: ${({ theme }) => `${theme.colors?.primary || '#1976d2'}10`};
  }
`;

const MethodTitle = styled.h3`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 8px;
  font-size: 1.1rem;
`;

const MethodDescription = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  font-size: 0.9rem;
  line-height: 1.4;
`;
