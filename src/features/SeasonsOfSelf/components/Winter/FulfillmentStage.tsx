/**
 * Fulfillment Stage Component
 *
 * This component represents the third stage of the Winter season,
 * focusing on creating a fulfilling life aligned with one's purpose.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useSeasonsOfSelf } from '../../context/SeasonsOfSelfContext';
import Card from '../../../../components/ui/Card';
import Button from '../../../../components/ui/Button';
import autoSaveService from '../../../DataPortability/services/autoSaveService';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

// Component props
interface FulfillmentStageProps {
  onComplete?: () => void;
  onBack?: () => void;
}

// Fulfillment plan type
interface FulfillmentPlan {
  id: string;
  vision: string;
  dailyPractices: string[];
  relationships: string;
  environment: string;
  contribution: string;
  growth: string;
  balance: string;
  reflection: string;
}

/**
 * Fulfillment Stage Component
 */
export const FulfillmentStage: React.FC<FulfillmentStageProps> = ({ onComplete, onBack }) => {
  // Theme is used in styled components via ThemeProvider
  useTheme();
  const { data, updateData, stages, updateStageCompletion } = useSeasonsOfSelf();

  // Get saved data or initialize with defaults
  const [plan, setPlan] = useState<FulfillmentPlan>(
    data.winter && data.winter.fulfillmentPlan
      ? data.winter.fulfillmentPlan
      : {
          id: `fulfillment-${Date.now()}`,
          vision: '',
          dailyPractices: [],
          relationships: '',
          environment: '',
          contribution: '',
          growth: '',
          balance: '',
          reflection: '',
        }
  );

  const [newPractice, setNewPractice] = useState('');

  // Save indicator state
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Create auto-save instance
  const autoSave = React.useMemo(
    () =>
      autoSaveService.createEnhancedAutoSave({
        key: 'winter_fulfillment_plan',
        onSave: () => {
          setShowSaveIndicator(true);
          setTimeout(() => setShowSaveIndicator(false), 2000);
        },
      }),
    []
  );

  // Update context data when plan changes
  useEffect(() => {
    updateData('winter', 'fulfillmentPlan', plan);

    // Auto-save data
    autoSave.save(plan);

    // Mark stage as completed if required fields are filled
    const fulfillmentStage = stages.find((stage) => stage.id === 'fulfillment');
    const isComplete = Boolean(
      plan.vision &&
        plan.dailyPractices.length >= 3 &&
        plan.relationships &&
        plan.contribution &&
        plan.reflection
    );

    if (fulfillmentStage && !fulfillmentStage.completed && isComplete) {
      updateStageCompletion('fulfillment', true);
    } else if (fulfillmentStage && fulfillmentStage.completed && !isComplete) {
      updateStageCompletion('fulfillment', false);
    }
  }, [plan, updateData, stages, autoSave, updateStageCompletion]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setPlan((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle practice input change
  const handlePracticeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewPractice(e.target.value);
  };

  // Handle adding a practice
  const handleAddPractice = () => {
    if (!newPractice.trim()) return;

    setPlan((prev) => ({
      ...prev,
      dailyPractices: [...prev.dailyPractices, newPractice.trim()],
    }));

    setNewPractice('');
  };

  // Handle removing a practice
  const handleRemovePractice = (index: number) => {
    setPlan((prev) => ({
      ...prev,
      dailyPractices: prev.dailyPractices.filter((_, i) => i !== index),
    }));
  };

  // Check if all required fields are filled
  const isComplete = Boolean(
    plan.vision &&
      plan.dailyPractices.length >= 3 &&
      plan.relationships &&
      plan.contribution &&
      plan.reflection
  );

  // Handle back navigation
  const handleBack = () => {
    if (onBack) {
      onBack();
    }
  };

  // Handle completion
  const handleComplete = () => {
    if (onComplete) {
      onComplete();
    }
  };

  return (
    <Container as={motion.div} variants={containerVariants} initial="hidden" animate="visible">
      <StageCard elevation="medium" as={motion.div} variants={itemVariants}>
        <CardHeader>
          <StageTitle>Fulfillment Stage</StageTitle>
          <StageDescription>
            The Fulfillment Stage is about creating a life of meaning and fulfillment aligned with
            your purpose. Develop a comprehensive plan for living a fulfilling life that embodies
            your calling and purpose.
          </StageDescription>
        </CardHeader>

        <CardContent>
          <Section>
            <SectionTitle>Your Fulfillment Plan</SectionTitle>
            <SectionDescription>
              Create a comprehensive plan for living a fulfilling life that embodies your calling
              and purpose. This plan will serve as your roadmap for creating a life of meaning and
              fulfillment.
            </SectionDescription>

            <PlanForm>
              <FormGroup>
                <Label htmlFor="vision">Your Vision of a Fulfilling Life</Label>
                <TextArea
                  id="vision"
                  name="vision"
                  value={plan.vision}
                  onChange={handleInputChange}
                  placeholder="Describe your vision of a fulfilling life that embodies your purpose"
                  rows={4}
                />
              </FormGroup>

              <FormGroup>
                <Label>Daily Practices for Fulfillment</Label>
                <PracticeInputGroup>
                  <PracticeInput
                    value={newPractice}
                    onChange={handlePracticeChange}
                    placeholder="Add a daily practice that supports your fulfillment"
                  />
                  <PracticeButton onClick={handleAddPractice}>Add</PracticeButton>
                </PracticeInputGroup>

                <PracticesList>
                  {plan.dailyPractices.map((practice, index) => (
                    <PracticeItem key={index}>
                      <PracticeText>{practice}</PracticeText>
                      <PracticeRemove onClick={() => handleRemovePractice(index)}>✕</PracticeRemove>
                    </PracticeItem>
                  ))}
                </PracticesList>
                <HelperText>
                  Add at least 3 daily practices that support your fulfillment and purpose
                </HelperText>
              </FormGroup>

              <FormGroup>
                <Label htmlFor="relationships">Nurturing Relationships</Label>
                <TextArea
                  id="relationships"
                  name="relationships"
                  value={plan.relationships}
                  onChange={handleInputChange}
                  placeholder="How will you cultivate relationships that support your purpose and fulfillment?"
                  rows={3}
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor="environment">Creating a Supportive Environment</Label>
                <TextArea
                  id="environment"
                  name="environment"
                  value={plan.environment}
                  onChange={handleInputChange}
                  placeholder="How will you create an environment that supports your purpose and fulfillment?"
                  rows={3}
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor="contribution">Meaningful Contribution</Label>
                <TextArea
                  id="contribution"
                  name="contribution"
                  value={plan.contribution}
                  onChange={handleInputChange}
                  placeholder="How will you contribute to others and the world in alignment with your purpose?"
                  rows={3}
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor="growth">Continued Growth</Label>
                <TextArea
                  id="growth"
                  name="growth"
                  value={plan.growth}
                  onChange={handleInputChange}
                  placeholder="How will you continue to grow and evolve in alignment with your purpose?"
                  rows={3}
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor="balance">Balance and Wholeness</Label>
                <TextArea
                  id="balance"
                  name="balance"
                  value={plan.balance}
                  onChange={handleInputChange}
                  placeholder="How will you maintain balance and wholeness in your life?"
                  rows={3}
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor="reflection">Reflection and Integration</Label>
                <TextArea
                  id="reflection"
                  name="reflection"
                  value={plan.reflection}
                  onChange={handleInputChange}
                  placeholder="How will you regularly reflect on and integrate your experiences to deepen your fulfillment?"
                  rows={3}
                />
              </FormGroup>
            </PlanForm>
          </Section>

          <SaveIndicator visible={showSaveIndicator}>Changes saved automatically</SaveIndicator>
        </CardContent>

        <CardActions>
          <Button variant="outlined" onClick={handleBack}>
            Back to Purpose Stage
          </Button>
          <Button variant="primary" onClick={handleComplete} disabled={!isComplete}>
            {!isComplete ? 'Complete all required fields to continue' : 'Complete Winter Season'}
          </Button>
        </CardActions>
      </StageCard>
    </Container>
  );
};

// Styled components
const Container = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const StageCard = styled(Card)`
  overflow: hidden;
  border-left: 4px solid ${({ theme }) => theme.colors?.primary || '#1976d2'};
`;

const CardHeader = styled.div`
  padding: 24px;
  background-color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.background || '#ffffff' // Use dark background in dark mode
      : theme.colors?.background || '#ffffff'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const StageTitle = styled.h2`
  color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  margin-bottom: 8px;
`;

const StageDescription = styled.p`
  color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : theme.colors?.text || '#000000'Secondary};
`;

const CardContent = styled.div`
  padding: 24px;
`;

const CardActions = styled.div`
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  border-top: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const Section = styled.div`
  margin-bottom: 24px;
`;

const SectionTitle = styled.h3`
  margin-bottom: 8px;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const SectionDescription = styled.p`
  margin-bottom: 16px;
  color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : theme.colors?.text || '#000000'Secondary};
`;

const PlanForm = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
`;

const Label = styled.label`
  margin-bottom: 8px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: 12px;
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 4px;
  font-size: 1rem;
  resize: vertical;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
    box-shadow: 0 0 0 2px ${({ theme }) => `${theme.colors?.primary || '#1976d2'}40`};
  }
`;

const PracticeInputGroup = styled.div`
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
`;

const PracticeInput = styled.input`
  flex: 1;
  padding: 8px 12px;
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 4px;
  font-size: 1rem;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
    box-shadow: 0 0 0 2px ${({ theme }) => `${theme.colors?.primary || '#1976d2'}40`};
  }
`;

const PracticeButton = styled(Button)`
  padding: 8px 16px;
`;

const PracticesList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 8px;
`;

const PracticeItem = styled.div`
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: ${({ theme }) => theme.colors?.paper || '#ffffff'};
  border-radius: 4px;
`;

const PracticeText = styled.span`
  flex: 1;
  font-size: 0.875rem;
  color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : theme.colors?.text || '#000000'};
`;

const PracticeRemove = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors?.text || '#000000'Secondary};
  cursor: pointer;

  &:hover {
    color: ${({ theme }) => theme.colors?.error || '#f44336'};
  }
`;

const HelperText = styled.p`
  margin-top: 4px;
  font-size: 0.75rem;
  color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : theme.colors?.text || '#000000'Secondary};
  font-style: italic;
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  text-align: center;
  color: ${({ theme }) => theme.colors?.success || '#4caf50'};
  margin-top: 16px;
`;
