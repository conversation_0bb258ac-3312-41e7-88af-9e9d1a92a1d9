/**
 * Life Stages Journey Introduction Component
 *
 * This component provides a clear, intuitive explanation of the Life Stages Journey
 * (formerly "Seasons of Self") concept to help users understand how to use this feature.
 */

import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from '../../../theme/SimpleThemeProvider';

// Types
interface LifeStagesIntroProps {
  onGetStarted: () => void;
}

// Styled Components
const IntroContainer = styled.div`
  max-width: 900px;
  margin: 0 auto;
  padding: 2rem;
  background-color: ${({ theme }) => theme.colors?.paper || '#ffffff'};
  border-radius: ${({ theme }) => theme.borderRadius.large};
  box-shadow: ${({ theme }) => theme.shadows.medium};
`;

const IntroHeader = styled.div`
  text-align: center;
  margin-bottom: 3rem;
`;

const Title = styled.h1`
  font-size: ${({ theme }) => theme.typography.fontSize.heading2};
  color: ${({ theme }) => theme.colors.seasons[theme.season].primary};
  margin-bottom: 1rem;
  font-weight: 600;
`;

const Subtitle = styled.p`
  font-size: ${({ theme }) => theme.typography.fontSize.bodyLarge};
  color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors.neutral.primaryText // Use primary text color in dark mode for better contrast
      : theme.colors.neutral.secondaryText};
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
`;

const StagesContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
`;

const SeasonSection = styled.div<{ season: 'spring' | 'summer' | 'autumn' | 'winter' }>`
  padding: 1.5rem;
  border-radius: ${({ theme }) => theme.borderRadius.medium};
  background-color: ${({ theme, season }) => {
    const bgColor =
      theme.mode === 'light'
        ? theme.colors.seasons[season].background.light
        : theme.colors.seasons[season].background.dark;
    return `${bgColor}80`; // Add transparency
  }};
  border-left: 4px solid ${({ theme, season }) => theme.colors.seasons[season].primary};
`;

const SeasonTitle = styled.h2<{ season: 'spring' | 'summer' | 'autumn' | 'winter' }>`
  font-size: ${({ theme }) => theme.typography.fontSize.heading3};
  color: ${({ theme, season }) => theme.colors.seasons[season].primary};
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const StagesList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;
`;

const StageItem = styled.li`
  margin-bottom: 0.75rem;
  padding-left: 1.5rem;
  position: relative;
  color: ${({ theme }) => theme.colors.neutral.primaryText};

  &:before {
    content: '•';
    position: absolute;
    left: 0;
    color: ${({ theme }) => theme.colors.seasons[theme.season].primary};
  }
`;

const BenefitsSection = styled.div`
  margin-bottom: 3rem;
`;

const BenefitsTitle = styled.h2`
  font-size: ${({ theme }) => theme.typography.fontSize.heading3};
  color: ${({ theme }) => theme.colors.neutral.primaryText};
  margin-bottom: 1.5rem;
  text-align: center;
`;

const BenefitsList = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
`;

const BenefitItem = styled.div`
  padding: 1.5rem;
  background-color: ${({ theme }) =>
    theme.mode === 'light' ? theme.colors?.paper || '#ffffff' : theme.colors?.paper || '#ffffff'};
  border-radius: ${({ theme }) => theme.borderRadius.medium};
  box-shadow: ${({ theme }) => theme.shadows.sm};
`;

const BenefitTitle = styled.h3`
  font-size: ${({ theme }) => theme.typography.fontSize.bodyLarge};
  color: ${({ theme }) => theme.colors.seasons[theme.season].primary};
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const BenefitDescription = styled.p`
  font-size: ${({ theme }) => theme.typography.fontSize.body};
  color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors.neutral.primaryText // Use primary text color in dark mode for better contrast
      : theme.colors.neutral.secondaryText};
  line-height: 1.5;
`;

const GetStartedButton = styled(motion.button)`
  display: block;
  margin: 0 auto;
  padding: 0.75rem 2rem;
  background-color: ${({ theme }) => theme.colors.seasons[theme.season].primary};
  color: white;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius.medium};
  font-size: ${({ theme }) => theme.typography.fontSize.bodyLarge};
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: ${({ theme }) =>
      theme.colors.seasons[theme.season].accent || theme.colors.seasons[theme.season].secondary};
    transform: translateY(-2px);
    box-shadow: ${({ theme }) => theme.shadows.md};
  }

  &:active {
    transform: translateY(0);
  }
`;

/**
 * Life Stages Journey Introduction Component
 */
const LifeStagesIntro: React.FC<LifeStagesIntroProps> = ({ onGetStarted }) => {
  const { theme } = useTheme();

  return (
    <IntroContainer>
      <IntroHeader>
        <Title>Welcome to Your Life Stages Journey</Title>
        <Subtitle>
          Discover where you are in life's natural progression and learn how to thrive in each
          stage. This journey maps your personal growth through four seasons, each with unique
          stages of development.
        </Subtitle>
      </IntroHeader>

      <StagesContainer>
        <SeasonSection season="spring">
          <SeasonTitle season="spring">
            <span>🌱</span> Spring: Beginning
          </SeasonTitle>
          <StagesList>
            <StageItem>Pleasure: Discovering what brings immediate joy</StageItem>
            <StageItem>Happiness: Building positive emotions and experiences</StageItem>
          </StagesList>
        </SeasonSection>

        <SeasonSection season="summer">
          <SeasonTitle season="summer">
            <span>☀️</span> Summer: Growth
          </SeasonTitle>
          <StagesList>
            <StageItem>Joy: Finding deeper meaning in activities and relationships</StageItem>
            <StageItem>Momentum: Building habits and consistent progress</StageItem>
          </StagesList>
        </SeasonSection>

        <SeasonSection season="autumn">
          <SeasonTitle season="autumn">
            <span>🍂</span> Autumn: Transition
          </SeasonTitle>
          <StagesList>
            <StageItem>Pivot: Reassessing direction and making necessary changes</StageItem>
            <StageItem>Goal Seeking: Setting and pursuing meaningful goals</StageItem>
          </StagesList>
        </SeasonSection>

        <SeasonSection season="winter">
          <SeasonTitle season="winter">
            <span>❄️</span> Winter: Wisdom
          </SeasonTitle>
          <StagesList>
            <StageItem>Calling: Discovering your unique vocation</StageItem>
            <StageItem>Purpose: Living with clear intention</StageItem>
            <StageItem>Fulfillment: Experiencing deep satisfaction and legacy</StageItem>
          </StagesList>
        </SeasonSection>
      </StagesContainer>

      <BenefitsSection>
        <BenefitsTitle>How This Journey Helps You</BenefitsTitle>
        <BenefitsList>
          <BenefitItem>
            <BenefitTitle>
              <span>🧭</span> Find Your Current Stage
            </BenefitTitle>
            <BenefitDescription>
              Through guided assessments, discover exactly where you are in your personal
              development journey.
            </BenefitDescription>
          </BenefitItem>

          <BenefitItem>
            <BenefitTitle>
              <span>🌈</span> Understand Natural Transitions
            </BenefitTitle>
            <BenefitDescription>
              Learn how life naturally flows through different stages and how to navigate
              transitions with grace.
            </BenefitDescription>
          </BenefitItem>

          <BenefitItem>
            <BenefitTitle>
              <span>🌟</span> Unlock Growth Opportunities
            </BenefitTitle>
            <BenefitDescription>
              Discover specific activities and practices that help you thrive in your current life
              stage.
            </BenefitDescription>
          </BenefitItem>

          <BenefitItem>
            <BenefitTitle>
              <span>🔄</span> Integrate With Financial Planning
            </BenefitTitle>
            <BenefitDescription>
              See how your life stage connects with your financial needs and goals for truly
              holistic planning.
            </BenefitDescription>
          </BenefitItem>
        </BenefitsList>
      </BenefitsSection>

      <GetStartedButton
        onClick={onGetStarted}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        Begin Your Journey
      </GetStartedButton>
    </IntroContainer>
  );
};

export default LifeStagesIntro;
