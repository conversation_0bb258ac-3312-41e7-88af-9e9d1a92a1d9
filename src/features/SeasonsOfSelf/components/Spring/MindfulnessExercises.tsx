/**
 * Mindfulness Exercises Component
 *
 * This component provides guided mindfulness exercises, gratitude practices,
 * and reflection prompts to enhance present-moment awareness and well-being.
 * Designed for the Spring season to support renewal and growth.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import Card from '../../../../components/ui/Card';
import Button from '../../../../components/ui/Button';
import autoSaveService from '../../../DataPortability/services/autoSaveService';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

// Component props
interface MindfulnessExercisesProps {
  onComplete?: (data: MindfulnessData) => void;
  onBack?: () => void;
  initialData?: MindfulnessData;
}

// Mindfulness data structure
export interface MindfulnessData {
  id: string;
  completedExercises: string[];
  gratitudeEntries: GratitudeEntry[];
  reflectionResponses: Record<string, string>;
  mindfulnessGoals: string[];
  practiceSchedule: PracticeSchedule;
  completed: boolean;
}

interface GratitudeEntry {
  id: string;
  date: string;
  items: string[];
  reflection: string;
}

interface PracticeSchedule {
  dailyMindfulness: boolean;
  gratitudePractice: boolean;
  reflectionTime: string;
  reminderEnabled: boolean;
}

// Mindfulness exercises library
const MINDFULNESS_EXERCISES = [
  {
    id: 'breathing_awareness',
    title: 'Breathing Awareness',
    category: 'Foundation',
    duration: '5-10 minutes',
    description: 'Focus on your natural breath to anchor yourself in the present moment.',
    instructions: [
      'Find a comfortable seated position with your back straight',
      'Close your eyes or soften your gaze downward',
      'Notice your natural breathing without trying to change it',
      'When your mind wanders, gently return attention to your breath',
      'Continue for 5-10 minutes, ending with a few deeper breaths'
    ],
    benefits: ['Reduces stress', 'Improves focus', 'Calms the nervous system']
  },
  {
    id: 'body_scan',
    title: 'Body Scan Meditation',
    category: 'Awareness',
    duration: '10-20 minutes',
    description: 'Systematically notice sensations throughout your body.',
    instructions: [
      'Lie down comfortably or sit with good posture',
      'Start by noticing sensations in your toes',
      'Slowly move your attention up through each part of your body',
      'Notice any tension, warmth, coolness, or other sensations',
      'Send breath and relaxation to any areas of tension',
      'End by noticing your whole body as one complete system'
    ],
    benefits: ['Increases body awareness', 'Promotes relaxation', 'Releases physical tension']
  },
  {
    id: 'loving_kindness',
    title: 'Loving-Kindness Meditation',
    category: 'Heart',
    duration: '10-15 minutes',
    description: 'Cultivate compassion and goodwill toward yourself and others.',
    instructions: [
      'Begin by sending loving wishes to yourself: "May I be happy, may I be healthy, may I be at peace"',
      'Extend these wishes to someone you love',
      'Send loving-kindness to a neutral person (acquaintance)',
      'Include someone you have difficulty with',
      'Finally, extend loving-kindness to all beings everywhere',
      'Rest in the feeling of universal compassion'
    ],
    benefits: ['Increases compassion', 'Reduces negative emotions', 'Improves relationships']
  },
  {
    id: 'mindful_walking',
    title: 'Mindful Walking',
    category: 'Movement',
    duration: '10-30 minutes',
    description: 'Practice mindfulness while walking slowly and deliberately.',
    instructions: [
      'Choose a quiet path 10-20 steps long',
      'Begin walking very slowly, feeling each step',
      'Notice the lifting, moving, and placing of each foot',
      'When you reach the end, pause and turn around mindfully',
      'Continue walking back and forth, staying present with each step',
      'If your mind wanders, stop and reconnect with your feet'
    ],
    benefits: ['Combines movement with mindfulness', 'Grounds you in the present', 'Can be done anywhere']
  },
  {
    id: 'five_senses',
    title: 'Five Senses Grounding',
    category: 'Grounding',
    duration: '5-10 minutes',
    description: 'Use your five senses to anchor yourself in the present moment.',
    instructions: [
      'Notice 5 things you can see around you',
      'Identify 4 things you can touch or feel',
      'Listen for 3 different sounds',
      'Find 2 things you can smell',
      'Notice 1 thing you can taste',
      'Take a moment to appreciate this full sensory experience'
    ],
    benefits: ['Quick grounding technique', 'Reduces anxiety', 'Enhances present-moment awareness']
  },
  {
    id: 'gratitude_meditation',
    title: 'Gratitude Meditation',
    category: 'Heart',
    duration: '10-15 minutes',
    description: 'Cultivate appreciation and thankfulness for your life.',
    instructions: [
      'Sit comfortably and take a few deep breaths',
      'Bring to mind something you\'re grateful for',
      'Really feel the appreciation in your heart',
      'Expand to include more things you\'re thankful for',
      'Include people, experiences, and simple pleasures',
      'End by feeling gratitude for this moment of practice'
    ],
    benefits: ['Increases positive emotions', 'Improves life satisfaction', 'Enhances well-being']
  }
];

// Reflection prompts for different themes
const REFLECTION_PROMPTS = {
  daily: [
    'What am I most grateful for today?',
    'How did I show kindness to myself or others today?',
    'What did I learn about myself today?',
    'What moment today brought me the most joy?',
    'How did I handle challenges today?'
  ],
  growth: [
    'What patterns in my thinking do I notice?',
    'How have I grown in the past month?',
    'What beliefs about myself are no longer serving me?',
    'What would I do if I trusted myself completely?',
    'How can I be more compassionate with myself?'
  ],
  purpose: [
    'What activities make me feel most alive?',
    'How do I want to contribute to the world?',
    'What values guide my daily decisions?',
    'What legacy do I want to leave?',
    'How can I align my actions with my deepest values?'
  ],
  relationships: [
    'How can I be more present in my relationships?',
    'What do I appreciate most about the people in my life?',
    'How do I want to show up for others?',
    'What boundaries do I need to set or maintain?',
    'How can I communicate more authentically?'
  ]
};

/**
 * Mindfulness Exercises Component
 */
export const MindfulnessExercises: React.FC<MindfulnessExercisesProps> = ({
  onComplete,
  onBack,
  initialData
}) => {
  const { theme } = useTheme();

  // Initialize data
  const [mindfulnessData, setMindfulnessData] = useState<MindfulnessData>(
    initialData || {
      id: `mindfulness-${Date.now()}`,
      completedExercises: [],
      gratitudeEntries: [],
      reflectionResponses: {},
      mindfulnessGoals: [],
      practiceSchedule: {
        dailyMindfulness: false,
        gratitudePractice: false,
        reflectionTime: '',
        reminderEnabled: false,
      },
      completed: false,
    }
  );

  const [currentTab, setCurrentTab] = useState<'exercises' | 'gratitude' | 'reflection' | 'schedule'>('exercises');
  const [selectedExercise, setSelectedExercise] = useState<string | null>(null);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Auto-save setup
  const autoSave = React.useMemo(
    () =>
      autoSaveService.createEnhancedAutoSave({
        key: 'mindfulness_practices',
        onSave: () => {
          setShowSaveIndicator(true);
          setTimeout(() => setShowSaveIndicator(false), 2000);
        },
      }),
    []
  );

  // Update data when mindfulness data changes
  useEffect(() => {
    autoSave.save(mindfulnessData);
  }, [mindfulnessData, autoSave]);

  const handleExerciseComplete = (exerciseId: string) => {
    setMindfulnessData(prev => ({
      ...prev,
      completedExercises: prev.completedExercises.includes(exerciseId)
        ? prev.completedExercises
        : [...prev.completedExercises, exerciseId],
    }));
  };

  const handleGratitudeAdd = (items: string[], reflection: string) => {
    const newEntry: GratitudeEntry = {
      id: `gratitude-${Date.now()}`,
      date: new Date().toISOString().split('T')[0],
      items,
      reflection,
    };

    setMindfulnessData(prev => ({
      ...prev,
      gratitudeEntries: [newEntry, ...prev.gratitudeEntries],
    }));
  };

  const handleReflectionResponse = (prompt: string, response: string) => {
    setMindfulnessData(prev => ({
      ...prev,
      reflectionResponses: {
        ...prev.reflectionResponses,
        [prompt]: response,
      },
    }));
  };

  const handleScheduleUpdate = (schedule: Partial<PracticeSchedule>) => {
    setMindfulnessData(prev => ({
      ...prev,
      practiceSchedule: {
        ...prev.practiceSchedule,
        ...schedule,
      },
    }));
  };

  const isComplete = () => {
    return mindfulnessData.completedExercises.length >= 3 &&
      mindfulnessData.gratitudeEntries.length >= 1 &&
      Object.keys(mindfulnessData.reflectionResponses).length >= 3;
  };

  const handleComplete = () => {
    const completedData = {
      ...mindfulnessData,
      completed: true,
    };
    setMindfulnessData(completedData);
    if (onComplete) {
      onComplete(completedData);
    }
  };

  return (
    <Container as={motion.div} variants={containerVariants} initial="hidden" animate="visible">
      <MindfulnessCard elevation="medium" as={motion.div} variants={itemVariants}>
        <CardHeader>
          <Title>Mindfulness & Present-Moment Practices</Title>
          <Description>
            Cultivate awareness, gratitude, and presence through guided exercises and reflection.
          </Description>

          <TabNavigation>
            {[
              { id: 'exercises', label: 'Exercises', icon: '🧘' },
              { id: 'gratitude', label: 'Gratitude', icon: '🙏' },
              { id: 'reflection', label: 'Reflection', icon: '💭' },
              { id: 'schedule', label: 'Practice', icon: '📅' },
            ].map(tab => (
              <TabButton
                key={tab.id}
                active={currentTab === tab.id}
                onClick={() => setCurrentTab(tab.id as any)}
              >
                <span>{tab.icon}</span>
                {tab.label}
              </TabButton>
            ))}
          </TabNavigation>
        </CardHeader>

        <CardContent>
          {currentTab === 'exercises' && (
            <ExercisesTab>
              <ExercisesGrid>
                {MINDFULNESS_EXERCISES.map(exercise => (
                  <ExerciseCard
                    key={exercise.id}
                    completed={mindfulnessData.completedExercises.includes(exercise.id)}
                    onClick={() => setSelectedExercise(exercise.id)}
                  >
                    <ExerciseHeader>
                      <ExerciseTitle>{exercise.title}</ExerciseTitle>
                      <ExerciseCategory>{exercise.category}</ExerciseCategory>
                      <ExerciseDuration>{exercise.duration}</ExerciseDuration>
                    </ExerciseHeader>
                    <ExerciseDescription>{exercise.description}</ExerciseDescription>
                    {mindfulnessData.completedExercises.includes(exercise.id) && (
                      <CompletedBadge>✓ Completed</CompletedBadge>
                    )}
                  </ExerciseCard>
                ))}
              </ExercisesGrid>

              {selectedExercise && (
                <ExerciseDetail>
                  {(() => {
                    const exercise = MINDFULNESS_EXERCISES.find(e => e.id === selectedExercise);
                    return exercise ? (
                      <DetailContent>
                        <DetailHeader>
                          <DetailTitle>{exercise.title}</DetailTitle>
                          <CloseButton onClick={() => setSelectedExercise(null)}>×</CloseButton>
                        </DetailHeader>
                        <DetailDescription>{exercise.description}</DetailDescription>
                        <InstructionsList>
                          <h4>Instructions:</h4>
                          {exercise.instructions.map((instruction, index) => (
                            <InstructionItem key={index}>{instruction}</InstructionItem>
                          ))}
                        </InstructionsList>
                        <BenefitsList>
                          <h4>Benefits:</h4>
                          {exercise.benefits.map((benefit, index) => (
                            <BenefitItem key={index}>{benefit}</BenefitItem>
                          ))}
                        </BenefitsList>
                        <DetailActions>
                          <Button
                            variant="primary"
                            onClick={() => handleExerciseComplete(exercise.id)}
                            disabled={mindfulnessData.completedExercises.includes(exercise.id)}
                          >
                            {mindfulnessData.completedExercises.includes(exercise.id) ? 'Completed' : 'Mark as Completed'}
                          </Button>
                        </DetailActions>
                      </DetailContent>
                    ) : null;
                  })()}
                </ExerciseDetail>
              )}
            </ExercisesTab>
          )}

          {currentTab === 'gratitude' && (
            <GratitudeTab>
              <GratitudeForm>
                <GratitudeTitle>Daily Gratitude Practice</GratitudeTitle>
                <GratitudeInstructions>
                  List 3-5 things you're grateful for today and reflect on why they matter to you.
                </GratitudeInstructions>
                {/* Gratitude form will be simplified for token efficiency */}
                <SimpleGratitudeInput
                  placeholder="What are you grateful for today? (separate with commas)"
                  onSubmit={(items, reflection) => handleGratitudeAdd(items, reflection)}
                />
              </GratitudeForm>

              <GratitudeHistory>
                <HistoryTitle>Your Gratitude Journey</HistoryTitle>
                {mindfulnessData.gratitudeEntries.slice(0, 5).map(entry => (
                  <GratitudeEntry key={entry.id}>
                    <EntryDate>{entry.date}</EntryDate>
                    <EntryItems>
                      {entry.items.map((item, index) => (
                        <GratitudeItem key={index}>{item}</GratitudeItem>
                      ))}
                    </EntryItems>
                    {entry.reflection && <EntryReflection>{entry.reflection}</EntryReflection>}
                  </GratitudeEntry>
                ))}
              </GratitudeHistory>
            </GratitudeTab>
          )}

          {currentTab === 'reflection' && (
            <ReflectionTab>
              <ReflectionTitle>Mindful Reflection</ReflectionTitle>
              <ReflectionInstructions>
                Choose prompts that resonate with you and take time to reflect deeply.
              </ReflectionInstructions>

              {Object.entries(REFLECTION_PROMPTS).map(([category, prompts]) => (
                <ReflectionCategory key={category}>
                  <CategoryTitle>{category.charAt(0).toUpperCase() + category.slice(1)} Reflection</CategoryTitle>
                  {prompts.slice(0, 2).map(prompt => (
                    <ReflectionPrompt key={prompt}>
                      <PromptText>{prompt}</PromptText>
                      <ReflectionTextArea
                        value={mindfulnessData.reflectionResponses[prompt] || ''}
                        onChange={(e) => handleReflectionResponse(prompt, e.target.value)}
                        placeholder="Take your time to reflect deeply on this question..."
                        rows={3}
                      />
                    </ReflectionPrompt>
                  ))}
                </ReflectionCategory>
              ))}
            </ReflectionTab>
          )}

          {currentTab === 'schedule' && (
            <ScheduleTab>
              <ScheduleTitle>Create Your Practice Schedule</ScheduleTitle>
              <ScheduleInstructions>
                Set up a sustainable mindfulness practice that fits your lifestyle.
              </ScheduleInstructions>

              <ScheduleOptions>
                <ScheduleOption>
                  <OptionLabel>
                    <input
                      type="checkbox"
                      checked={mindfulnessData.practiceSchedule.dailyMindfulness}
                      onChange={(e) => handleScheduleUpdate({ dailyMindfulness: e.target.checked })}
                    />
                    Daily Mindfulness Practice (5-10 minutes)
                  </OptionLabel>
                </ScheduleOption>

                <ScheduleOption>
                  <OptionLabel>
                    <input
                      type="checkbox"
                      checked={mindfulnessData.practiceSchedule.gratitudePractice}
                      onChange={(e) => handleScheduleUpdate({ gratitudePractice: e.target.checked })}
                    />
                    Daily Gratitude Practice
                  </OptionLabel>
                </ScheduleOption>

                <ScheduleOption>
                  <OptionLabel>Preferred reflection time:</OptionLabel>
                  <select
                    value={mindfulnessData.practiceSchedule.reflectionTime}
                    onChange={(e) => handleScheduleUpdate({ reflectionTime: e.target.value })}
                  >
                    <option value="">Choose a time</option>
                    <option value="morning">Morning</option>
                    <option value="afternoon">Afternoon</option>
                    <option value="evening">Evening</option>
                  </select>
                </ScheduleOption>
              </ScheduleOptions>
            </ScheduleTab>
          )}

          <SaveIndicator visible={showSaveIndicator}>Changes saved automatically</SaveIndicator>
        </CardContent>

        <CardActions>
          {onBack && (
            <Button variant="outlined" onClick={onBack}>
              Back
            </Button>
          )}
          <div style={{ flex: 1 }} />
          <Button
            variant="primary"
            onClick={handleComplete}
            disabled={!isComplete()}
          >
            {isComplete() ? 'Complete Mindfulness Practice' : `Complete ${3 - mindfulnessData.completedExercises.length} more exercises`}
          </Button>
        </CardActions>
      </MindfulnessCard>
    </Container>
  );
};

// Simple Gratitude Input Component
const SimpleGratitudeInput: React.FC<{
  placeholder: string;
  onSubmit: (items: string[], reflection: string) => void;
}> = ({ placeholder, onSubmit }) => {
  const [input, setInput] = useState('');
  const [reflection, setReflection] = useState('');

  const handleSubmit = () => {
    if (input.trim()) {
      const items = input.split(',').map(item => item.trim()).filter(item => item);
      onSubmit(items, reflection);
      setInput('');
      setReflection('');
    }
  };

  return (
    <GratitudeInputContainer>
      <GratitudeInput
        value={input}
        onChange={(e) => setInput(e.target.value)}
        placeholder={placeholder}
      />
      <GratitudeReflectionInput
        value={reflection}
        onChange={(e) => setReflection(e.target.value)}
        placeholder="Optional: Reflect on why these matter to you..."
        rows={2}
      />
      <Button variant="primary" onClick={handleSubmit} disabled={!input.trim()}>
        Add Gratitude Entry
      </Button>
    </GratitudeInputContainer>
  );
};

// Styled components
const Container = styled.div`
  max-width: 900px;
  margin: 0 auto;
`;

const MindfulnessCard = styled(Card)`
  overflow: hidden;
  border-left: 4px solid ${({ theme }) => theme.colors?.primary || '#1976d2'};
`;

const CardHeader = styled.div`
  padding: 24px;
  background-color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.background || '#ffffff'
      : theme.colors?.background || '#ffffff'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const Title = styled.h2`
  color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  margin-bottom: 8px;
`;

const Description = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 20px;
`;

const TabNavigation = styled.div`
  display: flex;
  gap: 8px;
  margin-top: 16px;
`;

const TabButton = styled.button<{ active: boolean }>`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: 1px solid ${({ active, theme }) =>
    active ? theme.colors?.primary || '#1976d2' : theme.colors?.border || '#e0e0e0'};
  background-color: ${({ active, theme }) =>
    active ? `${theme.colors?.primary || '#1976d2'}15` : 'transparent'};
  color: ${({ active, theme }) =>
    active ? theme.colors?.primary || '#1976d2' : theme.colors?.text || '#000000'};
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;

  &:hover {
    border-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
    background-color: ${({ theme }) => `${theme.colors?.primary || '#1976d2'}10`};
  }
`;

const CardContent = styled.div`
  padding: 24px;
  min-height: 400px;
`;

const CardActions = styled.div`
  padding: 16px 24px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-top: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${({ visible }) => (visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  color: ${({ theme }) => theme.colors?.success || '#4caf50'};
  font-size: 0.875rem;
  text-align: center;
  margin-top: 16px;
`;

// Exercise tab styles
const ExercisesTab = styled.div``;

const ExercisesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
`;

const ExerciseCard = styled.div<{ completed: boolean }>`
  padding: 16px;
  border: 2px solid ${({ completed, theme }) =>
    completed ? theme.colors?.success || '#4caf50' : theme.colors?.border || '#e0e0e0'};
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: ${({ completed, theme }) =>
    completed ? `${theme.colors?.success || '#4caf50'}10` : 'transparent'};

  &:hover {
    border-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
    background-color: ${({ theme }) => `${theme.colors?.primary || '#1976d2'}05`};
  }
`;

const ExerciseHeader = styled.div`
  margin-bottom: 12px;
`;

const ExerciseTitle = styled.h4`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 4px;
`;

const ExerciseCategory = styled.div`
  color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  font-size: 0.875rem;
  font-weight: 500;
`;

const ExerciseDuration = styled.div`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  font-size: 0.875rem;
`;

const ExerciseDescription = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  font-size: 0.9rem;
  line-height: 1.4;
`;

const CompletedBadge = styled.div`
  margin-top: 8px;
  color: ${({ theme }) => theme.colors?.success || '#4caf50'};
  font-weight: 600;
  font-size: 0.875rem;
`;

// Exercise detail styles
const ExerciseDetail = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const DetailContent = styled.div`
  background-color: ${({ theme }) => theme.colors?.background || '#ffffff'.primary};
  border-radius: 8px;
  padding: 24px;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  margin: 20px;
`;

const DetailHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const DetailTitle = styled.h3`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const DetailDescription = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 16px;
`;

const InstructionsList = styled.div`
  margin-bottom: 16px;
  h4 { color: ${({ theme }) => theme.colors?.text || '#000000'}; }
`;

const InstructionItem = styled.div`
  padding: 8px 0;
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const BenefitsList = styled.div`
  margin-bottom: 16px;
  h4 { color: ${({ theme }) => theme.colors?.text || '#000000'}; }
`;

const BenefitItem = styled.div`
  padding: 4px 0;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const DetailActions = styled.div`
  display: flex;
  justify-content: center;
`;

// Gratitude tab styles
const GratitudeTab = styled.div``;
const GratitudeForm = styled.div`
  margin-bottom: 24px;
`;
const GratitudeTitle = styled.h3`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 8px;
`;
const GratitudeInstructions = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 16px;
`;
const GratitudeInputContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;
const GratitudeInput = styled.input`
  padding: 12px;
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 4px;
`;
const GratitudeReflectionInput = styled.textarea`
  padding: 12px;
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 4px;
  resize: vertical;
`;
const GratitudeHistory = styled.div``;
const HistoryTitle = styled.h4`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 16px;
`;
const GratitudeEntry = styled.div`
  padding: 16px;
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 8px;
  margin-bottom: 12px;
`;
const EntryDate = styled.div`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  font-size: 0.875rem;
  margin-bottom: 8px;
`;
const EntryItems = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
`;
const GratitudeItem = styled.span`
  padding: 4px 8px;
  background-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  color: white;
  border-radius: 12px;
  font-size: 0.875rem;
`;
const EntryReflection = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  font-style: italic;
`;

// Reflection tab styles
const ReflectionTab = styled.div``;
const ReflectionTitle = styled.h3`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 8px;
`;
const ReflectionInstructions = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 20px;
`;
const ReflectionCategory = styled.div`
  margin-bottom: 24px;
`;
const CategoryTitle = styled.h4`
  color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  margin-bottom: 12px;
`;
const ReflectionPrompt = styled.div`
  margin-bottom: 16px;
`;
const PromptText = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 8px;
  font-weight: 500;
`;
const ReflectionTextArea = styled.textarea`
  width: 100%;
  padding: 12px;
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 4px;
  resize: vertical;
`;

// Schedule tab styles
const ScheduleTab = styled.div``;
const ScheduleTitle = styled.h3`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 8px;
`;
const ScheduleInstructions = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 20px;
`;
const ScheduleOptions = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;
const ScheduleOption = styled.div`
  padding: 16px;
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 8px;
`;
const OptionLabel = styled.label`
  display: flex;
  align-items: center;
  gap: 8px;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  cursor: pointer;
`;
