/**
 * Life Stage Integration Component
 *
 * This component integrates the Seasons of Self with the Financial Compass,
 * showing how different life stages align with financial planning priorities.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../theme/SimpleThemeProvider';
import { useSeasonsOfSelf } from '../context/SeasonsOfSelfContext';
import { useFinancialCompass } from '../../FinancialCompass/context/FinancialCompassContext';
import { formatCurrency } from '../../../utils/formatters';

interface LifeStageIntegrationProps {
  onComplete?: () => void;
  onBack?: () => void;
}

// Map stages to financial priorities
const stageToPriorities: Record<
  string,
  {
    north: string[];
    east: string[];
    south: string[];
    west: string[];
  }
> = {
  // Spring stages
  pleasure: {
    north: ['Income growth', 'Budgeting basics', 'Debt management'],
    east: ['Starting retirement savings', 'Understanding investment basics'],
    south: ['Basic insurance coverage', 'Emergency fund'],
    west: ['Simple will', 'Beneficiary designations'],
  },
  happiness: {
    north: ['Career development', 'Increasing savings rate', 'Debt reduction'],
    east: ['Retirement account diversification', 'Investment strategy'],
    south: ['Comprehensive insurance review', 'Risk management'],
    west: ['Estate planning basics', 'Charitable giving exploration'],
  },

  // Summer stages
  joy: {
    north: ['Peak earning optimization', 'Advanced budgeting', 'Tax strategy'],
    east: ['Retirement contribution maximization', 'Investment optimization'],
    south: ['Family protection planning', 'Healthcare planning'],
    west: ['Legacy planning', 'Charitable giving strategy'],
  },
  momentum: {
    north: ['Wealth accumulation', 'Financial independence planning', 'Tax optimization'],
    east: ['Retirement timeline refinement', 'Portfolio rebalancing'],
    south: ['Insurance optimization', 'Long-term care planning'],
    west: ['Estate plan updates', 'Legacy vision development'],
  },

  // Autumn stages
  pivot: {
    north: ['Career transition planning', 'Income diversification', 'Expense optimization'],
    east: ['Retirement transition strategy', 'Income distribution planning'],
    south: ['Healthcare coverage transition', 'Risk reassessment'],
    west: ['Estate plan review', 'Legacy implementation beginning'],
  },
  goal_seeking: {
    north: ['Retirement budget finalization', 'Income stream activation'],
    east: ['Retirement withdrawal strategy', 'Portfolio preservation'],
    south: ['Medicare planning', 'Long-term care implementation'],
    west: ['Legacy plan implementation', 'Charitable giving implementation'],
  },

  // Winter stages
  calling: {
    north: ['Sustainable withdrawal refinement', 'Tax-efficient distributions'],
    east: ['Portfolio protection', 'Income security'],
    south: ['Healthcare cost management', 'Long-term care coordination'],
    west: ['Legacy plan execution', 'Family wealth education'],
  },
  purpose: {
    north: ['Longevity planning', 'Simplified financial management'],
    east: ['Conservative portfolio adjustments', 'Required minimum distributions'],
    south: ['Advanced care planning', 'Asset protection'],
    west: ['Legacy monitoring', 'Charitable impact maximization'],
  },
  fulfillment: {
    north: ['Financial simplification', 'Final arrangements funding'],
    east: ['Legacy preservation', 'Generational wealth transfer'],
    south: ['End-of-life care planning', 'Final expense coverage'],
    west: ['Legacy completion', 'Meaningful impact finalization'],
  },
};

const LifeStageIntegration: React.FC<LifeStageIntegrationProps> = ({ onComplete, onBack }) => {
  const { theme } = useTheme();
  const { stages, activeStage, setActiveStage } = useSeasonsOfSelf();
  const { data: compassData } = useFinancialCompass();

  const [selectedStage, setSelectedStage] = useState<string | null>(activeStage);
  const [financialSummary, setFinancialSummary] = useState({
    netWorth: 0,
    monthlyIncome: 0,
    monthlyExpenses: 0,
    retirementSavings: 0,
    insuranceCoverage: 0,
  });

  // Calculate financial summary from compass data
  useEffect(() => {
    const calculateFinancialSummary = () => {
      // Calculate net worth
      const netWorthDetails = compassData.north?.netWorthDetails || {};
      const assets = parseFloat((netWorthDetails as any)?.totalAssets || '0');
      const liabilities = parseFloat((netWorthDetails as any)?.totalLiabilities || '0');
      const netWorth = assets - liabilities;

      // Calculate monthly income
      const incomeDetails = compassData.north?.incomeDetails || {};
      const monthlyIncome = parseFloat((incomeDetails as any)?.totalMonthlyIncome || '0');

      // Calculate monthly expenses
      const expenseDetails = compassData.north?.expenseDetails || {};
      const monthlyExpenses = parseFloat((expenseDetails as any)?.totalMonthlyExpenses || '0');

      // Calculate retirement savings
      const retirementAccounts = compassData.east?.retirementAccounts || {};
      const retirementSavings = parseFloat((retirementAccounts as any)?.totalBalance || '0');

      // Calculate insurance coverage
      const insuranceCoverage = compassData.south?.insuranceCoverage || {};
      const lifeInsurance = parseFloat((insuranceCoverage as any)?.lifeInsurance?.coverage || '0');
      const disabilityInsurance = parseFloat(
        (insuranceCoverage as any)?.disabilityInsurance?.coverage || '0'
      );
      const healthInsurance = parseFloat(
        (insuranceCoverage as any)?.healthInsurance?.coverage || '0'
      );
      const totalInsuranceCoverage = lifeInsurance + disabilityInsurance + healthInsurance;

      setFinancialSummary({
        netWorth,
        monthlyIncome,
        monthlyExpenses,
        retirementSavings,
        insuranceCoverage: totalInsuranceCoverage,
      });
    };

    calculateFinancialSummary();
  }, [compassData]);

  // Handle stage selection
  const handleStageSelect = (stageId: string) => {
    setSelectedStage(stageId);
    setActiveStage(stageId);
  };

  // Get current stage
  const currentStage = stages.find((stage) => stage.id === selectedStage);

  // Get priorities for current stage
  const priorities = selectedStage ? stageToPriorities[selectedStage] : null;

  // Handle completion
  const handleComplete = () => {
    if (onComplete) {
      onComplete();
    }
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Life Stage Financial Integration</Title>
        <Description theme={theme}>
          Align your financial planning with your current life stage and season.
        </Description>
      </Header>

      <ContentContainer>
        <StageSelector theme={theme}>
          <SelectorTitle>Select Your Life Stage</SelectorTitle>

          <SeasonSection>
            <SeasonTitle season="spring">Spring: Foundation & Growth</SeasonTitle>
            <StageButtons>
              {stages
                .filter((stage) => stage.season === 'spring')
                .map((stage) => (
                  <StageButton
                    key={stage.id}
                    isSelected={selectedStage === stage.id}
                    onClick={() => handleStageSelect(stage.id)}
                    theme={theme}
                    season="spring"
                  >
                    {stage.name}
                  </StageButton>
                ))}
            </StageButtons>
          </SeasonSection>

          <SeasonSection>
            <SeasonTitle season="summer">Summer: Accumulation & Peak</SeasonTitle>
            <StageButtons>
              {stages
                .filter((stage) => stage.season === 'summer')
                .map((stage) => (
                  <StageButton
                    key={stage.id}
                    isSelected={selectedStage === stage.id}
                    onClick={() => handleStageSelect(stage.id)}
                    theme={theme}
                    season="summer"
                  >
                    {stage.name}
                  </StageButton>
                ))}
            </StageButtons>
          </SeasonSection>

          <SeasonSection>
            <SeasonTitle season="autumn">Autumn: Transition & Shift</SeasonTitle>
            <StageButtons>
              {stages
                .filter((stage) => stage.season === 'autumn')
                .map((stage) => (
                  <StageButton
                    key={stage.id}
                    isSelected={selectedStage === stage.id}
                    onClick={() => handleStageSelect(stage.id)}
                    theme={theme}
                    season="autumn"
                  >
                    {stage.name}
                  </StageButton>
                ))}
            </StageButtons>
          </SeasonSection>

          <SeasonSection>
            <SeasonTitle season="winter">Winter: Purpose & Legacy</SeasonTitle>
            <StageButtons>
              {stages
                .filter((stage) => stage.season === 'winter')
                .map((stage) => (
                  <StageButton
                    key={stage.id}
                    isSelected={selectedStage === stage.id}
                    onClick={() => handleStageSelect(stage.id)}
                    theme={theme}
                    season="winter"
                  >
                    {stage.name}
                  </StageButton>
                ))}
            </StageButtons>
          </SeasonSection>
        </StageSelector>

        <IntegrationContent theme={theme}>
          {currentStage && priorities ? (
            <>
              <StageHeader season={currentStage.season}>
                <StageName>{currentStage.name}</StageName>
                <StageDescription>{currentStage.description}</StageDescription>
              </StageHeader>

              <FinancialSummary theme={theme}>
                <SummaryTitle>Your Financial Summary</SummaryTitle>
                <SummaryGrid>
                  <SummaryItem>
                    <SummaryLabel>Net Worth</SummaryLabel>
                    <SummaryValue>
                      {formatCurrency(financialSummary.netWorth.toString())}
                    </SummaryValue>
                  </SummaryItem>
                  <SummaryItem>
                    <SummaryLabel>Monthly Income</SummaryLabel>
                    <SummaryValue>
                      {formatCurrency(financialSummary.monthlyIncome.toString())}
                    </SummaryValue>
                  </SummaryItem>
                  <SummaryItem>
                    <SummaryLabel>Monthly Expenses</SummaryLabel>
                    <SummaryValue>
                      {formatCurrency(financialSummary.monthlyExpenses.toString())}
                    </SummaryValue>
                  </SummaryItem>
                  <SummaryItem>
                    <SummaryLabel>Retirement Savings</SummaryLabel>
                    <SummaryValue>
                      {formatCurrency(financialSummary.retirementSavings.toString())}
                    </SummaryValue>
                  </SummaryItem>
                </SummaryGrid>
              </FinancialSummary>

              <PrioritiesContainer>
                <PrioritiesTitle>Financial Priorities for This Stage</PrioritiesTitle>

                <PrioritiesGrid>
                  <PriorityCard direction="north" theme={theme}>
                    <PriorityCardTitle>North: Financial Foundation</PriorityCardTitle>
                    <PriorityList>
                      {priorities.north.map((priority, index) => (
                        <PriorityItem key={index}>{priority}</PriorityItem>
                      ))}
                    </PriorityList>
                  </PriorityCard>

                  <PriorityCard direction="east" theme={theme}>
                    <PriorityCardTitle>East: Retirement Vision</PriorityCardTitle>
                    <PriorityList>
                      {priorities.east.map((priority, index) => (
                        <PriorityItem key={index}>{priority}</PriorityItem>
                      ))}
                    </PriorityList>
                  </PriorityCard>

                  <PriorityCard direction="south" theme={theme}>
                    <PriorityCardTitle>South: Protection & Risk</PriorityCardTitle>
                    <PriorityList>
                      {priorities.south.map((priority, index) => (
                        <PriorityItem key={index}>{priority}</PriorityItem>
                      ))}
                    </PriorityList>
                  </PriorityCard>

                  <PriorityCard direction="west" theme={theme}>
                    <PriorityCardTitle>West: Legacy & Impact</PriorityCardTitle>
                    <PriorityList>
                      {priorities.west.map((priority, index) => (
                        <PriorityItem key={index}>{priority}</PriorityItem>
                      ))}
                    </PriorityList>
                  </PriorityCard>
                </PrioritiesGrid>
              </PrioritiesContainer>
            </>
          ) : (
            <EmptyState>
              <p>Select a life stage to see financial priorities and recommendations.</p>
            </EmptyState>
          )}
        </IntegrationContent>
      </ContentContainer>

      <ButtonContainer>
        {onBack && (
          <BackButton type="button" onClick={onBack} theme={theme}>
            Back
          </BackButton>
        )}

        <ContinueButton type="button" onClick={handleComplete} theme={theme}>
          Continue
        </ContinueButton>
      </ButtonContainer>
    </Container>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  padding: 24px;
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const Title = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors?.text || '#000000'};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const Description = styled.p<{ theme: any }>`
  color: ${(props) =>
    props.theme.mode === 'dark'
      ? props.theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : props.theme.colors?.text || '#000000'Secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const ContentContainer = styled.div`
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 24px;
  margin-bottom: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const StageSelector = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors?.paper || '#ffffff'};
  border-radius: 8px;
  padding: 16px;
`;

const SelectorTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const SeasonSection = styled.div`
  margin-bottom: 16px;
`;

const SeasonTitle = styled.h4<{ season: string }>`
  margin: 0 0 8px 0;
  font-size: 1rem;
  font-weight: 500;
  color: ${(props) => {
    switch (props.season) {
      case 'spring':
        return '#4CAF50'; // Green
      case 'summer':
        return '#FF9800'; // Orange
      case 'autumn':
        return '#F44336'; // Red
      case 'winter':
        return '#2196F3'; // Blue
      default:
        return 'inherit';
    }
  }};
`;

const StageButtons = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const StageButton = styled.button<{ isSelected: boolean; theme: any; season: string }>`
  background-color: ${(props) =>
    props.isSelected ? props.theme.colors?.primary || '#1976d2'.main : props.theme.colors?.background || '#ffffff'};
  color: ${(props) =>
    props.isSelected ? props.theme.colors?.primary || '#1976d2'.contrastText : props.theme.colors?.text || '#000000'};
  border: 1px solid
    ${(props) => (props.isSelected ? props.theme.colors?.primary || '#1976d2'.main : props.theme.colors?.border || '#e0e0e0')};
  border-left: 4px solid
    ${(props) => {
      switch (props.season) {
        case 'spring':
          return '#4CAF50'; // Green
        case 'summer':
          return '#FF9800'; // Orange
        case 'autumn':
          return '#F44336'; // Red
        case 'winter':
          return '#2196F3'; // Blue
        default:
          return props.theme.colors?.border || '#e0e0e0';
      }
    }};
  border-radius: 4px;
  padding: 8px 12px;
  text-align: left;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) =>
      props.isSelected ? props.theme.colors?.primary || '#1976d2'.dark : props.theme.colors.action.hover};
  }
`;

const IntegrationContent = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors?.paper || '#ffffff'};
  border-radius: 8px;
  padding: 24px;
`;

const StageHeader = styled.div<{ season: string }>`
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid
    ${(props) => {
      switch (props.season) {
        case 'spring':
          return '#4CAF50'; // Green
        case 'summer':
          return '#FF9800'; // Orange
        case 'autumn':
          return '#F44336'; // Red
        case 'winter':
          return '#2196F3'; // Blue
        default:
          return '#ccc';
      }
    }};
`;

const StageName = styled.h3`
  margin: 0 0 8px 0;
  font-size: 1.3rem;
  font-weight: 500;
`;

const StageDescription = styled.p`
  margin: 0;
  font-size: 1rem;
  color: ${(props) =>
    props.theme?.mode === 'dark'
      ? props.theme?.colors.text.primary // Use primary text color in dark mode for better contrast
      : '#666'};
`;

const FinancialSummary = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors?.background || '#ffffff'};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
`;

const SummaryTitle = styled.h4`
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 500;
`;

const SummaryGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
`;

const SummaryItem = styled.div`
  display: flex;
  flex-direction: column;
`;

const SummaryLabel = styled.div`
  font-size: 0.9rem;
  color: ${(props) =>
    props.theme?.mode === 'dark'
      ? props.theme?.colors.text.primary // Use primary text color in dark mode for better contrast
      : '#666'};
  margin-bottom: 4px;
`;

const SummaryValue = styled.div`
  font-size: 1.2rem;
  font-weight: 500;
`;

const PrioritiesContainer = styled.div`
  margin-bottom: 24px;
`;

const PrioritiesTitle = styled.h4`
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 500;
`;

const PrioritiesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
`;

const PriorityCard = styled.div<{ direction: string; theme: any }>`
  background-color: ${(props) => props.theme.colors?.background || '#ffffff'};
  border-radius: 8px;
  padding: 16px;
  border-top: 4px solid
    ${(props) => {
      switch (props.direction) {
        case 'north':
          return '#2196F3'; // Blue
        case 'east':
          return '#4CAF50'; // Green
        case 'south':
          return '#F44336'; // Red
        case 'west':
          return '#FF9800'; // Orange
        default:
          return props.theme.colors?.border || '#e0e0e0';
      }
    }};
`;

const PriorityCardTitle = styled.h5`
  margin: 0 0 12px 0;
  font-size: 1rem;
  font-weight: 500;
`;

const PriorityList = styled.ul`
  margin: 0;
  padding: 0 0 0 20px;
`;

const PriorityItem = styled.li`
  margin-bottom: 8px;
  font-size: 0.9rem;
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: ${(props) =>
    props.theme?.mode === 'dark'
      ? props.theme?.colors.text.primary // Use primary text color in dark mode for better contrast
      : '#999'};
  text-align: center;

  p {
    margin: 0;
    line-height: 1.5;
  }
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: 24px;
`;

const BackButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors?.text || '#000000'Secondary};
  border: 1px solid ${(props) => props.theme.colors?.border || '#e0e0e0'};
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors?.background || '#ffffff'};
  }
`;

const ContinueButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors?.primary || '#1976d2'.main};
  color: ${(props) => props.theme.colors?.primary || '#1976d2'.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 24px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors?.primary || '#1976d2'.dark};
  }
`;

export default LifeStageIntegration;
