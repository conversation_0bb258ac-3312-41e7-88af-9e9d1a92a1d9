/**
 * Character Strengths Assessment Component
 *
 * This component implements a simplified VIA Character Strengths assessment
 * based on the 24 character strengths from positive psychology research.
 * Users identify their signature strengths and learn how to apply them.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import Card from '../../../../components/ui/Card';
import Button from '../../../../components/ui/Button';
import autoSaveService from '../../../DataPortability/services/autoSaveService';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

// Component props
interface StrengthsAssessmentProps {
  onComplete?: (strengths: StrengthsData) => void;
  onBack?: () => void;
  initialData?: StrengthsData;
}

// Strengths data structure
export interface StrengthsData {
  id: string;
  strengthsRatings: Record<string, number>;
  signatureStrengths: string[];
  strengthsApplications: Record<string, string>;
  strengthsStatement: string;
  completed: boolean;
}

// VIA Character Strengths (24 strengths organized by 6 virtues)
const CHARACTER_STRENGTHS = [
  // Wisdom and Knowledge
  {
    id: 'creativity',
    name: 'Creativity',
    virtue: 'Wisdom and Knowledge',
    description: 'Thinking of novel and productive ways to conceptualize and do things',
    questions: [
      'I often come up with creative solutions to problems',
      'I enjoy thinking of new ways to do things',
      'Others often ask me for creative ideas'
    ]
  },
  {
    id: 'curiosity',
    name: 'Curiosity',
    virtue: 'Wisdom and Knowledge',
    description: 'Taking an interest in ongoing experience for its own sake',
    questions: [
      'I am always curious about the world around me',
      'I love to learn new things',
      'I ask lots of questions'
    ]
  },
  {
    id: 'judgment',
    name: 'Judgment',
    virtue: 'Wisdom and Knowledge',
    description: 'Thinking things through and examining them from all sides',
    questions: [
      'I think carefully before making decisions',
      'I consider multiple perspectives before forming opinions',
      'Others come to me for advice because I think things through'
    ]
  },
  {
    id: 'love_of_learning',
    name: 'Love of Learning',
    virtue: 'Wisdom and Knowledge',
    description: 'Mastering new skills, topics, and bodies of knowledge',
    questions: [
      'I love learning new skills and knowledge',
      'I enjoy taking classes and reading about new topics',
      'Learning energizes me'
    ]
  },
  {
    id: 'perspective',
    name: 'Perspective',
    virtue: 'Wisdom and Knowledge',
    description: 'Being able to provide wise counsel; having ways of looking at the world that make sense',
    questions: [
      'Others often seek my advice',
      'I can see the big picture in complex situations',
      'I have wisdom that comes from experience'
    ]
  },

  // Courage
  {
    id: 'bravery',
    name: 'Bravery',
    virtue: 'Courage',
    description: 'Not shrinking from threat, challenge, difficulty, or pain',
    questions: [
      'I stand up for what is right even when others disagree',
      'I face my fears and challenges head-on',
      'I speak up when I see something wrong'
    ]
  },
  {
    id: 'perseverance',
    name: 'Perseverance',
    virtue: 'Courage',
    description: 'Persistence in spite of fatigue, frustration, or obstacles',
    questions: [
      'I finish what I begin',
      'I persist even when things get difficult',
      'I rarely give up on important goals'
    ]
  },
  {
    id: 'honesty',
    name: 'Honesty',
    virtue: 'Courage',
    description: 'Speaking the truth but more broadly presenting oneself in a genuine way',
    questions: [
      'I am always honest and authentic',
      'I present myself genuinely to others',
      'People trust me because I am truthful'
    ]
  },
  {
    id: 'zest',
    name: 'Zest',
    virtue: 'Courage',
    description: 'Approaching life with excitement and energy',
    questions: [
      'I approach life with enthusiasm and energy',
      'I feel excited about my activities',
      'My energy is contagious to others'
    ]
  },

  // Humanity
  {
    id: 'love',
    name: 'Love',
    virtue: 'Humanity',
    description: 'Capacity for close relationships; valuing close relations with others',
    questions: [
      'I have deep, meaningful relationships',
      'I value close connections with others',
      'Love is central to my life'
    ]
  },
  {
    id: 'kindness',
    name: 'Kindness',
    virtue: 'Humanity',
    description: 'Doing favors and good deeds for others; helping them; taking care of them',
    questions: [
      'I enjoy helping others',
      'I go out of my way to do good deeds',
      'I am compassionate and caring'
    ]
  },
  {
    id: 'social_intelligence',
    name: 'Social Intelligence',
    virtue: 'Humanity',
    description: 'Understanding the motives and feelings of other people and oneself',
    questions: [
      'I understand what motivates other people',
      'I can read social situations well',
      'I know how to fit in different social settings'
    ]
  },

  // Justice
  {
    id: 'teamwork',
    name: 'Teamwork',
    virtue: 'Justice',
    description: 'Citizenship, social responsibility, loyalty; working well as part of a team',
    questions: [
      'I work well as part of a team',
      'I am loyal to my groups and organizations',
      'I contribute to the common good'
    ]
  },
  {
    id: 'fairness',
    name: 'Fairness',
    virtue: 'Justice',
    description: 'Treating all people the same according to notions of fairness and justice',
    questions: [
      'I treat everyone fairly regardless of who they are',
      'I believe in equal opportunities for all',
      'I stand up against discrimination'
    ]
  },
  {
    id: 'leadership',
    name: 'Leadership',
    virtue: 'Justice',
    description: 'Encouraging a group to get things done while maintaining good relations',
    questions: [
      'I naturally take on leadership roles',
      'I can organize group activities effectively',
      'Others look to me for direction'
    ]
  },

  // Temperance
  {
    id: 'forgiveness',
    name: 'Forgiveness',
    virtue: 'Temperance',
    description: 'Forgiving those who have done wrong; second chances; mercy',
    questions: [
      'I forgive people who have wronged me',
      'I believe in giving second chances',
      'I do not hold grudges'
    ]
  },
  {
    id: 'humility',
    name: 'Humility',
    virtue: 'Temperance',
    description: 'Letting accomplishments speak for themselves; not regarding oneself as special',
    questions: [
      'I am modest about my accomplishments',
      'I do not think I am better than others',
      'I let my actions speak for themselves'
    ]
  },
  {
    id: 'prudence',
    name: 'Prudence',
    virtue: 'Temperance',
    description: 'Careful choices; not taking undue risks; not saying or doing things that might be regretted',
    questions: [
      'I think before I act',
      'I am careful about the risks I take',
      'I rarely say things I later regret'
    ]
  },
  {
    id: 'self_regulation',
    name: 'Self-Regulation',
    virtue: 'Temperance',
    description: 'Regulating what one feels and does; being disciplined; controlling appetites and emotions',
    questions: [
      'I have good self-control',
      'I can regulate my emotions well',
      'I am disciplined in my habits'
    ]
  },

  // Transcendence
  {
    id: 'appreciation_of_beauty',
    name: 'Appreciation of Beauty',
    virtue: 'Transcendence',
    description: 'Noticing and appreciating beauty, excellence, and skilled performance in various domains',
    questions: [
      'I notice and appreciate beauty around me',
      'I am moved by excellence in various forms',
      'I seek out beautiful experiences'
    ]
  },
  {
    id: 'gratitude',
    name: 'Gratitude',
    virtue: 'Transcendence',
    description: 'Being aware of and thankful for good things that happen',
    questions: [
      'I regularly feel grateful for what I have',
      'I express thanks to others often',
      'I focus on the positive aspects of my life'
    ]
  },
  {
    id: 'hope',
    name: 'Hope',
    virtue: 'Transcendence',
    description: 'Optimism, future-mindedness, future orientation',
    questions: [
      'I am optimistic about the future',
      'I believe good things will happen',
      'I maintain hope even in difficult times'
    ]
  },
  {
    id: 'humor',
    name: 'Humor',
    virtue: 'Transcendence',
    description: 'Liking to laugh and tease; bringing smiles to other people; seeing the light side',
    questions: [
      'I enjoy making others laugh',
      'I can find humor in difficult situations',
      'I like to have fun and be playful'
    ]
  },
  {
    id: 'spirituality',
    name: 'Spirituality',
    virtue: 'Transcendence',
    description: 'Having coherent beliefs about the higher purpose and meaning of the universe',
    questions: [
      'I have strong spiritual or religious beliefs',
      'I feel connected to something larger than myself',
      'My beliefs give my life meaning and purpose'
    ]
  },
];

/**
 * Character Strengths Assessment Component
 */
export const StrengthsAssessment: React.FC<StrengthsAssessmentProps> = ({
  onComplete,
  onBack,
  initialData
}) => {
  const { theme } = useTheme();

  // Initialize data
  const [strengthsData, setStrengthsData] = useState<StrengthsData>(
    initialData || {
      id: `strengths-${Date.now()}`,
      strengthsRatings: {},
      signatureStrengths: [],
      strengthsApplications: {},
      strengthsStatement: '',
      completed: false,
    }
  );

  const [currentStep, setCurrentStep] = useState(0);
  const [currentStrengthIndex, setCurrentStrengthIndex] = useState(0);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Auto-save setup
  const autoSave = React.useMemo(
    () =>
      autoSaveService.createEnhancedAutoSave({
        key: 'character_strengths_assessment',
        onSave: () => {
          setShowSaveIndicator(true);
          setTimeout(() => setShowSaveIndicator(false), 2000);
        },
      }),
    []
  );

  // Update data when strengths change
  useEffect(() => {
    autoSave.save(strengthsData);
  }, [strengthsData, autoSave]);

  const steps = [
    {
      title: 'Rate Your Strengths',
      description: 'Rate how well each statement describes you on a scale of 1-5.',
    },
    {
      title: 'Your Signature Strengths',
      description: 'Review your top strengths and select your 5 signature strengths.',
    },
    {
      title: 'Apply Your Strengths',
      description: 'Think about how you can apply your signature strengths in different areas of life.',
    },
    {
      title: 'Strengths Statement',
      description: 'Write a personal statement about how you will use your strengths.',
    },
  ];

  const currentStepData = steps[currentStep];
  const currentStrength = CHARACTER_STRENGTHS[currentStrengthIndex];

  const handleRating = (strengthId: string, rating: number) => {
    setStrengthsData(prev => ({
      ...prev,
      strengthsRatings: {
        ...prev.strengthsRatings,
        [strengthId]: rating,
      },
    }));
  };

  const handleNextStrength = () => {
    if (currentStrengthIndex < CHARACTER_STRENGTHS.length - 1) {
      setCurrentStrengthIndex(currentStrengthIndex + 1);
    } else {
      // Calculate top strengths and move to next step
      calculateTopStrengths();
      setCurrentStep(1);
    }
  };

  const calculateTopStrengths = () => {
    const strengthScores = CHARACTER_STRENGTHS.map(strength => {
      const avgRating = strength.questions.reduce((sum, _, qIndex) => {
        const ratingKey = `${strength.id}_${qIndex}`;
        return sum + (strengthsData.strengthsRatings[ratingKey] || 0);
      }, 0) / strength.questions.length;

      return { id: strength.id, score: avgRating };
    }).sort((a, b) => b.score - a.score);

    const topStrengths = strengthScores.slice(0, 8).map(s => s.id);

    setStrengthsData(prev => ({
      ...prev,
      signatureStrengths: topStrengths.slice(0, 5), // Auto-select top 5
    }));
  };

  const handleSignatureStrengthToggle = (strengthId: string) => {
    setStrengthsData(prev => {
      const isSelected = prev.signatureStrengths.includes(strengthId);
      let newSignature;

      if (isSelected) {
        newSignature = prev.signatureStrengths.filter(id => id !== strengthId);
      } else {
        if (prev.signatureStrengths.length >= 5) {
          return prev; // Don't add if at max
        }
        newSignature = [...prev.signatureStrengths, strengthId];
      }

      return {
        ...prev,
        signatureStrengths: newSignature,
      };
    });
  };

  const handleApplicationChange = (strengthId: string, application: string) => {
    setStrengthsData(prev => ({
      ...prev,
      strengthsApplications: {
        ...prev.strengthsApplications,
        [strengthId]: application,
      },
    }));
  };

  const handleStatementChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setStrengthsData(prev => ({
      ...prev,
      strengthsStatement: e.target.value,
    }));
  };

  const canProceed = () => {
    switch (currentStep) {
      case 0:
        return Object.keys(strengthsData.strengthsRatings).length >= CHARACTER_STRENGTHS.length * 3;
      case 1:
        return strengthsData.signatureStrengths.length === 5;
      case 2:
        return strengthsData.signatureStrengths.every(id =>
          strengthsData.strengthsApplications[id]?.length > 20
        );
      case 3:
        return strengthsData.strengthsStatement.length > 50;
      default:
        return false;
    }
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // Complete assessment
      const completedData = {
        ...strengthsData,
        completed: true,
      };
      setStrengthsData(completedData);
      if (onComplete) {
        onComplete(completedData);
      }
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const getTopStrengths = () => {
    const strengthScores = CHARACTER_STRENGTHS.map(strength => {
      const avgRating = strength.questions.reduce((sum, _, qIndex) => {
        const ratingKey = `${strength.id}_${qIndex}`;
        return sum + (strengthsData.strengthsRatings[ratingKey] || 0);
      }, 0) / strength.questions.length;

      return { ...strength, score: avgRating };
    }).sort((a, b) => b.score - a.score);

    return strengthScores.slice(0, 8);
  };

  return (
    <Container as={motion.div} variants={containerVariants} initial="hidden" animate="visible">
      <AssessmentCard elevation="medium" as={motion.div} variants={itemVariants}>
        <CardHeader>
          <Title>Character Strengths Assessment</Title>
          <Description>
            Discover your signature character strengths based on the VIA Survey of Character Strengths.
          </Description>
          <ProgressBar>
            <ProgressFill progress={
              currentStep === 0
                ? (currentStrengthIndex + 1) / CHARACTER_STRENGTHS.length * 25
                : ((currentStep + 1) / steps.length) * 100
            } />
          </ProgressBar>
          <ProgressText>
            {currentStep === 0
              ? `Strength ${currentStrengthIndex + 1} of ${CHARACTER_STRENGTHS.length}: ${currentStrength?.name}`
              : `Step ${currentStep + 1} of ${steps.length}: ${currentStepData.title}`
            }
          </ProgressText>
        </CardHeader>

        <CardContent>
          {currentStep === 0 && currentStrength && (
            <RatingContainer>
              <StrengthHeader>
                <StrengthName>{currentStrength.name}</StrengthName>
                <StrengthVirtue>{currentStrength.virtue}</StrengthVirtue>
                <StrengthDescription>{currentStrength.description}</StrengthDescription>
              </StrengthHeader>

              <QuestionsContainer>
                <QuestionsTitle>Rate how well each statement describes you:</QuestionsTitle>
                {currentStrength.questions.map((question, qIndex) => {
                  const ratingKey = `${currentStrength.id}_${qIndex}`;
                  const currentRating = strengthsData.strengthsRatings[ratingKey] || 0;

                  return (
                    <QuestionContainer key={qIndex}>
                      <QuestionText>{question}</QuestionText>
                      <RatingScale>
                        {[1, 2, 3, 4, 5].map(rating => (
                          <RatingButton
                            key={rating}
                            selected={currentRating === rating}
                            onClick={() => handleRating(ratingKey, rating)}
                          >
                            {rating}
                          </RatingButton>
                        ))}
                      </RatingScale>
                      <RatingLabels>
                        <span>Not like me</span>
                        <span>Very much like me</span>
                      </RatingLabels>
                    </QuestionContainer>
                  );
                })}
              </QuestionsContainer>
            </RatingContainer>
          )}

          {currentStep === 1 && (
            <SignatureContainer>
              <SignatureInstructions>
                Based on your ratings, here are your top character strengths.
                Your top 5 are automatically selected as your signature strengths, but you can adjust them:
              </SignatureInstructions>
              <TopStrengthsList>
                {getTopStrengths().map((strength, index) => (
                  <StrengthCard
                    key={strength.id}
                    selected={strengthsData.signatureStrengths.includes(strength.id)}
                    rank={index + 1}
                    onClick={() => handleSignatureStrengthToggle(strength.id)}
                  >
                    <StrengthRank>#{index + 1}</StrengthRank>
                    <StrengthCardName>{strength.name}</StrengthCardName>
                    <StrengthCardVirtue>{strength.virtue}</StrengthCardVirtue>
                    <StrengthScore>Score: {strength.score.toFixed(1)}/5</StrengthScore>
                  </StrengthCard>
                ))}
              </TopStrengthsList>
              <SelectedCount>
                Selected: {strengthsData.signatureStrengths.length}/5
              </SelectedCount>
            </SignatureContainer>
          )}

          {currentStep === 2 && (
            <ApplicationContainer>
              <ApplicationInstructions>
                For each of your signature strengths, describe how you can apply it in different areas of your life:
              </ApplicationInstructions>
              {strengthsData.signatureStrengths.map(strengthId => {
                const strength = CHARACTER_STRENGTHS.find(s => s.id === strengthId);
                return (
                  <ApplicationSection key={strengthId}>
                    <ApplicationStrengthName>{strength?.name}</ApplicationStrengthName>
                    <ApplicationTextArea
                      value={strengthsData.strengthsApplications[strengthId] || ''}
                      onChange={(e) => handleApplicationChange(strengthId, e.target.value)}
                      placeholder={`How can you use your ${strength?.name} strength in work, relationships, personal growth, etc.?`}
                      rows={3}
                    />
                  </ApplicationSection>
                );
              })}
            </ApplicationContainer>
          )}

          {currentStep === 3 && (
            <StatementContainer>
              <StatementInstructions>
                Write a personal statement about how you will use your signature strengths to create more joy and fulfillment in your life:
              </StatementInstructions>
              <SignatureStrengthsDisplay>
                <strong>Your Signature Strengths:</strong>
                <StrengthsList>
                  {strengthsData.signatureStrengths.map(strengthId => {
                    const strength = CHARACTER_STRENGTHS.find(s => s.id === strengthId);
                    return <StrengthTag key={strengthId}>{strength?.name}</StrengthTag>;
                  })}
                </StrengthsList>
              </SignatureStrengthsDisplay>
              <StatementTextArea
                value={strengthsData.strengthsStatement}
                onChange={handleStatementChange}
                placeholder="Example: My signature strengths of creativity, perseverance, and kindness help me approach challenges with innovative solutions, persist through difficulties, and build meaningful relationships. I will use these strengths to..."
                rows={6}
              />
              <CharacterCount>
                {strengthsData.strengthsStatement.length} characters (minimum 50)
              </CharacterCount>
            </StatementContainer>
          )}

          <SaveIndicator visible={showSaveIndicator}>Changes saved automatically</SaveIndicator>
        </CardContent>

        <CardActions>
          {currentStep > 0 && (
            <Button variant="outlined" onClick={handlePrevious}>
              Previous
            </Button>
          )}
          {onBack && currentStep === 0 && currentStrengthIndex === 0 && (
            <Button variant="outlined" onClick={onBack}>
              Back
            </Button>
          )}
          <div style={{ flex: 1 }} />
          <Button
            variant="primary"
            onClick={currentStep === 0 ? handleNextStrength : handleNext}
            disabled={currentStep === 0 ? false : !canProceed()}
          >
            {currentStep === 0
              ? (currentStrengthIndex === CHARACTER_STRENGTHS.length - 1 ? 'Calculate Results' : 'Next Strength')
              : (currentStep === steps.length - 1 ? 'Complete Assessment' : 'Next')
            }
          </Button>
        </CardActions>
      </AssessmentCard>
    </Container>
  );
};

// Styled components
const Container = styled.div`
  max-width: 900px;
  margin: 0 auto;
`;

const AssessmentCard = styled(Card)`
  overflow: hidden;
  border-left: 4px solid ${({ theme }) => theme.colors?.primary || '#1976d2'};
`;

const CardHeader = styled.div`
  padding: 24px;
  background-color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.background || '#ffffff'
      : theme.colors?.background || '#ffffff'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const Title = styled.h2`
  color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  margin-bottom: 8px;
`;

const Description = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'Secondary};
  margin-bottom: 16px;
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 8px;
  background-color: ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
`;

const ProgressFill = styled.div<{ progress: number }>`
  width: ${({ progress }) => progress}%;
  height: 100%;
  background-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  transition: width 0.3s ease;
`;

const ProgressText = styled.p`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors?.text || '#000000'Secondary};
  text-align: center;
`;

const CardContent = styled.div`
  padding: 24px;
`;

const CardActions = styled.div`
  padding: 16px 24px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-top: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${({ visible }) => (visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  color: ${({ theme }) => theme.colors?.success || '#4caf50'};
  font-size: 0.875rem;
  text-align: center;
  margin-top: 16px;
`;

// Rating step styles
const RatingContainer = styled.div`
  margin-bottom: 24px;
`;

const StrengthHeader = styled.div`
  margin-bottom: 24px;
  padding: 20px;
  background-color: ${({ theme }) => `${theme.colors?.primary || '#1976d2'}10`};
  border-radius: 8px;
  border-left: 4px solid ${({ theme }) => theme.colors?.primary || '#1976d2'};
`;

const StrengthName = styled.h3`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 8px;
  font-size: 1.5rem;
`;

const StrengthVirtue = styled.div`
  color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  font-weight: 600;
  margin-bottom: 8px;
`;

const StrengthDescription = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'Secondary};
  font-style: italic;
`;

const QuestionsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const QuestionsTitle = styled.h4`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 16px;
`;

const QuestionContainer = styled.div`
  padding: 16px;
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 8px;
`;

const QuestionText = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 12px;
  font-weight: 500;
`;

const RatingScale = styled.div`
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  justify-content: center;
`;

const RatingButton = styled.button<{ selected: boolean }>`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid ${({ selected, theme }) =>
    selected ? theme.colors?.primary || '#1976d2' : theme.colors?.border || '#e0e0e0'};
  background-color: ${({ selected, theme }) =>
    selected ? theme.colors?.primary || '#1976d2' : 'transparent'};
  color: ${({ selected, theme }) =>
    selected ? 'white' : theme.colors?.text || '#000000'};
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
    background-color: ${({ theme }) => `${theme.colors?.primary || '#1976d2'}20`};
  }
`;

const RatingLabels = styled.div`
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors?.text || '#000000'Secondary};
`;

// Signature strengths step styles
const SignatureContainer = styled.div`
  margin-bottom: 24px;
`;

const SignatureInstructions = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'Secondary};
  margin-bottom: 20px;
  font-style: italic;
`;

const TopStrengthsList = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;

const StrengthCard = styled.div<{ selected: boolean; rank: number }>`
  padding: 16px;
  border: 2px solid ${({ selected, theme }) =>
    selected ? theme.colors?.primary || '#1976d2' : theme.colors?.border || '#e0e0e0'};
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: ${({ selected, rank, theme }) => {
    if (selected) return `${theme.colors?.primary || '#1976d2'}15`;
    if (rank <= 5) return `${theme.colors?.primary || '#1976d2'}05`;
    return 'transparent';
  }};

  &:hover {
    border-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
    background-color: ${({ theme }) => `${theme.colors?.primary || '#1976d2'}10`};
  }
`;

const StrengthRank = styled.div`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  font-weight: bold;
  margin-bottom: 8px;
`;

const StrengthCardName = styled.h4`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 4px;
`;

const StrengthCardVirtue = styled.div`
  color: ${({ theme }) => theme.colors?.text || '#000000'Secondary};
  font-size: 0.875rem;
  margin-bottom: 8px;
`;

const StrengthScore = styled.div`
  color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  font-weight: 600;
  font-size: 0.875rem;
`;

const SelectedCount = styled.div`
  text-align: center;
  color: ${({ theme }) => theme.colors?.text || '#000000'Secondary};
  font-weight: 500;
`;

// Application step styles
const ApplicationContainer = styled.div`
  margin-bottom: 24px;
`;

const ApplicationInstructions = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'Secondary};
  margin-bottom: 20px;
  font-style: italic;
`;

const ApplicationSection = styled.div`
  margin-bottom: 20px;
`;

const ApplicationStrengthName = styled.h4`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 8px;
`;

const ApplicationTextArea = styled.textarea`
  width: 100%;
  padding: 12px;
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 8px;
  font-size: 1rem;
  resize: vertical;
  font-family: inherit;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
    box-shadow: 0 0 0 2px ${({ theme }) => `${theme.colors?.primary || '#1976d2'}40`};
  }
`;

// Statement step styles
const StatementContainer = styled.div`
  margin-bottom: 24px;
`;

const StatementInstructions = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'Secondary};
  margin-bottom: 16px;
  font-style: italic;
`;

const SignatureStrengthsDisplay = styled.div`
  margin-bottom: 16px;
  padding: 16px;
  background-color: ${({ theme }) => theme.colors?.background || '#ffffff'};
  border-radius: 8px;
`;

const StrengthsList = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
`;

const StrengthTag = styled.span`
  padding: 4px 12px;
  background-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  color: white;
  border-radius: 16px;
  font-size: 0.875rem;
  font-weight: 500;
`;

const StatementTextArea = styled.textarea`
  width: 100%;
  padding: 16px;
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 8px;
  font-size: 1rem;
  resize: vertical;
  min-height: 150px;
  font-family: inherit;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
    box-shadow: 0 0 0 2px ${({ theme }) => `${theme.colors?.primary || '#1976d2'}40`};
  }
`;

const CharacterCount = styled.div`
  text-align: right;
  color: ${({ theme }) => theme.colors?.text || '#000000'Secondary};
  font-size: 0.875rem;
  margin-top: 8px;
`;
