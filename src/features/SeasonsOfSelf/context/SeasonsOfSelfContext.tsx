/**
 * Seasons of Self Context
 *
 * This context provides state and functions for the Seasons of Self feature.
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import autoSaveService from '../../DataPortability/services/autoSaveService';

/**
 * Season type
 */
export type Season = 'spring' | 'summer' | 'autumn' | 'winter';

/**
 * Stage type
 */
export interface Stage {
  id: string;
  name: string;
  season: Season;
  description: string;
  completed: boolean;
  score: number;
  path: string;
}

/**
 * Seasons of Self data
 */
export interface SeasonsOfSelfData {
  spring: Record<string, unknown>;
  summer: Record<string, unknown>;
  autumn: Record<string, unknown>;
  winter: Record<string, unknown>;
  currentStage: string | null;
  assessmentCompleted: boolean;
}

/**
 * Seasons of Self context type
 */
interface SeasonsOfSelfContextType {
  activeSeason: Season;
  setActiveSeason: (season: Season) => void;
  activeStage: string | null;
  setActiveStage: (stageId: string | null) => void;
  stages: Stage[];
  updateStages: (updatedStages: Stage[]) => void;
  updateStageCompletion: (stageId: string, completed: boolean) => void;
  data: SeasonsOfSelfData;
  updateData: (season: Season, key: string, value: unknown) => void;
  getCompletionPercentage: () => number;
  getSeasonCompletionPercentage: (season: Season) => number;
  getCurrentStage: () => Stage | null;
  resetData: () => void;
}

/**
 * Default stages
 */
const defaultStages: Stage[] = [
  // Spring (Stages 1-2: Pleasure, Happiness)
  {
    id: 'pleasure',
    name: 'Pleasure',
    season: 'spring',
    description: 'The stage of seeking immediate gratification and sensory pleasure.',
    completed: false,
    score: 0,
    path: '/seasons/spring/pleasure',
  },
  {
    id: 'happiness',
    name: 'Happiness',
    season: 'spring',
    description: 'The stage of pursuing happiness through positive emotions and experiences.',
    completed: false,
    score: 0,
    path: '/seasons/spring/happiness',
  },

  // Summer (Stages 3-4: Joy, Momentum)
  {
    id: 'joy',
    name: 'Joy',
    season: 'summer',
    description: 'The stage of finding deeper joy through meaningful activities and relationships.',
    completed: false,
    score: 0,
    path: '/seasons/summer/joy',
  },
  {
    id: 'momentum',
    name: 'Momentum',
    season: 'summer',
    description: 'The stage of building momentum through habits and consistent progress.',
    completed: false,
    score: 0,
    path: '/seasons/summer/momentum',
  },

  // Autumn (Stages 5-6: Pivot, Goal Seeking)
  {
    id: 'pivot',
    name: 'Pivot',
    season: 'autumn',
    description: 'The stage of reassessing direction and making necessary changes.',
    completed: false,
    score: 0,
    path: '/seasons/autumn/pivot',
  },
  {
    id: 'goal_seeking',
    name: 'Goal Seeking',
    season: 'autumn',
    description: 'The stage of setting and pursuing meaningful goals.',
    completed: false,
    score: 0,
    path: '/seasons/autumn/goal-seeking',
  },

  // Winter (Stages 7-9: Calling, Purpose, Fulfillment)
  {
    id: 'calling',
    name: 'Calling',
    season: 'winter',
    description: 'The stage of discovering your unique calling or vocation.',
    completed: false,
    score: 0,
    path: '/seasons/winter/calling',
  },
  {
    id: 'purpose',
    name: 'Purpose',
    season: 'winter',
    description: 'The stage of living with clear purpose and intention.',
    completed: false,
    score: 0,
    path: '/seasons/winter/purpose',
  },
  {
    id: 'fulfillment',
    name: 'Fulfillment',
    season: 'winter',
    description: 'The stage of experiencing deep fulfillment and legacy.',
    completed: false,
    score: 0,
    path: '/seasons/winter/fulfillment',
  },
];

/**
 * Default Seasons of Self data
 */
const defaultData: SeasonsOfSelfData = {
  spring: {
    pleasureActivities: [],
    happinessGoals: [],
  },
  summer: {
    joyActivities: [],
    momentumHabits: [],
  },
  autumn: {
    pivotAssessments: [],
    goals: [],
  },
  winter: {
    callingReflections: [],
    purposeStatements: [],
    fulfillmentMilestones: [],
  },
  currentStage: null,
  assessmentCompleted: false,
};

/**
 * Storage key for Seasons of Self data
 */
const STORAGE_KEY = 'lifecompass_seasons_of_self';

/**
 * Seasons of Self context
 */
const SeasonsOfSelfContext = createContext<SeasonsOfSelfContextType | undefined>(undefined);

/**
 * Seasons of Self Provider props
 */
interface SeasonsOfSelfProviderProps {
  children: ReactNode;
}

/**
 * Seasons of Self Provider
 */
export const SeasonsOfSelfProvider: React.FC<SeasonsOfSelfProviderProps> = ({ children }) => {
  // State
  const [activeSeason, setActiveSeason] = useState<Season>('spring');
  const [activeStage, setActiveStage] = useState<string | null>(null);
  const [stages, setStages] = useState<Stage[]>(defaultStages);
  const [data, setData] = useState<SeasonsOfSelfData>(() => {
    // Load data from localStorage if available
    const storedData = localStorage.getItem(STORAGE_KEY);
    return storedData ? JSON.parse(storedData) : defaultData;
  });

  // Rehydrate stages from localStorage if present (e.g., after import)
  useEffect(() => {
    const stagesStr = localStorage.getItem('lifecompass_stages');
    if (stagesStr) {
      try {
        const importedStages = JSON.parse(stagesStr);
        if (Array.isArray(importedStages) && importedStages.length > 0) {
          setStages(importedStages);
        }
      } catch (e) {
        // Ignore parse errors, fallback to default
      }
    }
  }, []);

  // Set up auto-save
  useEffect(() => {
    const autoSave = autoSaveService.createAutoSave(
      () => ({
        data,
        stages,
      }),
      {
        storageKey: STORAGE_KEY,
        interval: 5000, // 5 seconds
        onAfterSave: () => console.log('Seasons of Self data auto-saved'),
        onError: (error) => console.error('Error auto-saving Seasons of Self data:', error),
      }
    );

    // Start auto-save
    autoSave.start();

    // Clean up on unmount
    return () => {
      autoSave.stop();
    };
  }, [data, stages]);

  /**
   * Update data for a specific season
   */
  const updateData = (season: Season, key: string, value: unknown) => {
    setData((prevData) => ({
      ...prevData,
      [season]: {
        ...prevData[season],
        [key]: value,
      },
    }));

    // Update stage completion status if needed
    if (key === 'completed' && typeof value === 'boolean') {
      setStages((prevStages) =>
        prevStages.map((stage) =>
          stage.id === activeStage ? { ...stage, completed: value } : stage
        )
      );
    }
  };

  /**
   * Get overall completion percentage
   */
  const getCompletionPercentage = (): number => {
    const completedCount = stages.filter((stage) => stage.completed).length;
    return Math.round((completedCount / stages.length) * 100);
  };

  /**
   * Get completion percentage for a specific season
   */
  const getSeasonCompletionPercentage = (season: Season): number => {
    const seasonStages = stages.filter((stage) => stage.season === season);
    const completedCount = seasonStages.filter((stage) => stage.completed).length;
    return Math.round((completedCount / seasonStages.length) * 100);
  };

  /**
   * Get the current stage
   */
  const getCurrentStage = (): Stage | null => {
    if (!data.currentStage) {
      return null;
    }

    return stages.find((stage) => stage.id === data.currentStage) || null;
  };

  /**
   * Update stages
   */
  const updateStages = (updatedStages: Stage[]) => {
    setStages(updatedStages);
  };

  /**
   * Update stage completion status
   */
  const updateStageCompletion = (stageId: string, completed: boolean) => {
    setStages((prevStages) =>
      prevStages.map((stage) => (stage.id === stageId ? { ...stage, completed } : stage))
    );
  };

  /**
   * Reset all data
   */
  const resetData = () => {
    setData(defaultData);
    setStages(defaultStages);
    localStorage.removeItem(STORAGE_KEY);
  };

  // Context value
  const value: SeasonsOfSelfContextType = {
    activeSeason,
    setActiveSeason,
    activeStage,
    setActiveStage,
    stages,
    updateStages,
    updateStageCompletion,
    data,
    updateData,
    getCompletionPercentage,
    getSeasonCompletionPercentage,
    getCurrentStage,
    resetData,
  };

  return <SeasonsOfSelfContext.Provider value={value}>{children}</SeasonsOfSelfContext.Provider>;
};

/**
 * Hook to use the Seasons of Self context
 */
export const useSeasonsOfSelf = (): SeasonsOfSelfContextType => {
  const context = useContext(SeasonsOfSelfContext);

  if (context === undefined) {
    throw new Error('useSeasonsOfSelf must be used within a SeasonsOfSelfProvider');
  }

  return context;
};
