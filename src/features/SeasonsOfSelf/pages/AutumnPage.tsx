/**
 * Autumn Page Component
 *
 * This component serves as the container for the Autumn season stages (Pivot and Goal Seeking).
 * It provides navigation between stages and tracks progress within the Autumn season.
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from '../../../theme/SimpleThemeProvider';
import { useSeasonsOfSelf } from '../context/SeasonsOfSelfContext';
import { PivotStage } from '../components/Autumn/PivotStage';
import { GoalSeekingStage } from '../components/Autumn/GoalSeekingStage';
import SeasonTracker from '../components/common/SeasonTracker';
import Button from '../../../components/ui/Button';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

// Component props
interface AutumnPageProps {
  onComplete?: () => void;
  onBack?: () => void;
}

/**
 * Autumn Page Component
 */
const AutumnPage: React.FC<AutumnPageProps> = ({ onComplete, onBack }) => {
  // Theme is used in styled components via ThemeProvider
  useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const { stages, activeStage, setActiveStage, getSeasonCompletionPercentage } = useSeasonsOfSelf();

  // Get Autumn stages
  const autumnStages = stages.filter((stage) => stage.season === 'autumn');

  // Determine active stage based on URL or context
  const [currentStage, setCurrentStage] = useState<string | null>(null);

  useEffect(() => {
    // Extract stage from URL path
    const pathParts = location.pathname.split('/');
    const stagePath = pathParts[pathParts.length - 1];

    // Find stage by path or use first autumn stage
    const stageFromPath = autumnStages.find((stage) => stage.path.includes(stagePath));

    if (stageFromPath) {
      setCurrentStage(stageFromPath.id);
      setActiveStage(stageFromPath.id);
    } else if (activeStage && autumnStages.some((stage) => stage.id === activeStage)) {
      setCurrentStage(activeStage);
    } else if (autumnStages.length > 0) {
      setCurrentStage(autumnStages[0].id);
      setActiveStage(autumnStages[0].id);
    }
  }, [location.pathname, autumnStages, activeStage, setActiveStage]);

  // Calculate completion percentage
  const completionPercentage = getSeasonCompletionPercentage('autumn');

  // Handle stage navigation
  const handleStageChange = (stageId: string) => {
    setCurrentStage(stageId);
    setActiveStage(stageId);

    // Update URL without full navigation
    const stage = stages.find((s) => s.id === stageId);
    if (stage) {
      navigate(stage.path, { replace: true });
    }
  };

  // Handle back navigation
  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      navigate('/seasons/summer');
    }
  };

  // Handle completion
  const handleComplete = () => {
    if (onComplete) {
      onComplete();
    } else {
      navigate('/seasons/winter');
    }
  };

  // Render the current stage component
  const renderStageComponent = () => {
    switch (currentStage) {
      case 'pivot':
        return <PivotStage onComplete={() => handleStageChange('goal_seeking')} />;
      case 'goal_seeking':
        return (
          <GoalSeekingStage onComplete={handleComplete} onBack={() => handleStageChange('pivot')} />
        );
      default:
        return <div>Select a stage to begin</div>;
    }
  };

  return (
    <Container as={motion.div} variants={containerVariants} initial="hidden" animate="visible">
      <Header as={motion.div} variants={itemVariants}>
        <Title>Autumn: Transformation</Title>
        <Description>
          The Autumn season represents a time of transformation and reassessment in your life
          journey, focusing on making pivots and setting meaningful goals.
        </Description>
      </Header>

      <motion.div variants={itemVariants}>
        <SeasonTracker season="autumn" onStageSelect={handleStageChange} />
      </motion.div>

      <StageContent as={motion.div} variants={itemVariants}>
        {renderStageComponent()}
      </StageContent>

      <Navigation as={motion.div} variants={itemVariants}>
        <Button variant="outlined" onClick={handleBack}>
          Back to Summer Season
        </Button>
        {completionPercentage === 100 && (
          <Button variant="primary" onClick={handleComplete}>
            Continue to Winter Season
          </Button>
        )}
      </Navigation>
    </Container>
  );
};

// Styled components
const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
`;

const Header = styled.div`
  margin-bottom: 24px;
  text-align: center;
`;

const Title = styled.h1`
  color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  margin-bottom: 8px;
`;

const Description = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  max-width: 800px;
  margin: 0 auto;
`;

const StageContent = styled.div`
  margin-bottom: 24px;
`;

const Navigation = styled.div`
  display: flex;
  justify-content: space-between;
  max-width: 800px;
  margin: 0 auto;
`;

export default AutumnPage;
