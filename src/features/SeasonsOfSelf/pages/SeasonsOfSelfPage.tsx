/**
 * Seasons of Self Page Component
 *
 * This component serves as the main entry point for the Life Journey section.
 * It provides an overview of all seasons and allows navigation between them.
 */

import React from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from '../../../theme/SimpleThemeProvider';
import { useSeasonsOfSelf } from '../context/SeasonsOfSelfContext';
import Card from '../../../components/ui/Card';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

/**
 * Seasons of Self Page Component
 */
const SeasonsOfSelfPage: React.FC = () => {
  const { setSeason } = useTheme();
  const navigate = useNavigate();
  const { getCompletionPercentage, getSeasonCompletionPercentage } = useSeasonsOfSelf();

  // Calculate overall completion percentage
  const overallCompletion = getCompletionPercentage();

  // Calculate season completion percentages
  const springCompletion = getSeasonCompletionPercentage('spring');
  const summerCompletion = getSeasonCompletionPercentage('summer');
  const autumnCompletion = getSeasonCompletionPercentage('autumn');
  const winterCompletion = getSeasonCompletionPercentage('winter');

  // Handle season navigation
  const navigateToSeason = (season: string) => {
    // Type assertion to ensure season is a valid Season type
    const validSeason = season as 'spring' | 'summer' | 'autumn' | 'winter';
    setSeason(validSeason);
    navigate(`/seasons/${season}`);
  };

  return (
    <Container as={motion.div} variants={containerVariants} initial="hidden" animate="visible">
      <Header as={motion.div} variants={itemVariants}>
        <Title>Life Journey</Title>
        <Description>
          Explore the seasons of your life journey, from the beginnings of Spring to the wisdom of
          Winter. Each season represents a different stage of growth and development in your life.
        </Description>
      </Header>

      <SeasonsGrid>
        <SeasonCard
          as={motion.div}
          variants={itemVariants}
          season="spring"
          onClick={() => navigateToSeason('spring')}
        >
          <SeasonIcon>🌱</SeasonIcon>
          <SeasonContent>
            <SeasonTitle>Spring: Beginning</SeasonTitle>
            <SeasonDescription>
              Discover what brings you joy and happiness in the early stages of your journey.
            </SeasonDescription>
            <SeasonProgress>
              <SeasonProgressBar>
                <SeasonProgressFill percentage={springCompletion} season="spring" />
              </SeasonProgressBar>
              <SeasonProgressText>{springCompletion}% Complete</SeasonProgressText>
            </SeasonProgress>
          </SeasonContent>
        </SeasonCard>

        <SeasonCard
          as={motion.div}
          variants={itemVariants}
          season="summer"
          onClick={() => navigateToSeason('summer')}
        >
          <SeasonIcon>☀️</SeasonIcon>
          <SeasonContent>
            <SeasonTitle>Summer: Growth</SeasonTitle>
            <SeasonDescription>
              Build momentum and find deeper joy through meaningful activities and relationships.
            </SeasonDescription>
            <SeasonProgress>
              <SeasonProgressBar>
                <SeasonProgressFill percentage={summerCompletion} season="summer" />
              </SeasonProgressBar>
              <SeasonProgressText>{summerCompletion}% Complete</SeasonProgressText>
            </SeasonProgress>
          </SeasonContent>
        </SeasonCard>

        <SeasonCard
          as={motion.div}
          variants={itemVariants}
          season="autumn"
          onClick={() => navigateToSeason('autumn')}
        >
          <SeasonIcon>🍂</SeasonIcon>
          <SeasonContent>
            <SeasonTitle>Autumn: Transformation</SeasonTitle>
            <SeasonDescription>
              Reassess your direction and set meaningful goals aligned with your values.
            </SeasonDescription>
            <SeasonProgress>
              <SeasonProgressBar>
                <SeasonProgressFill percentage={autumnCompletion} season="autumn" />
              </SeasonProgressBar>
              <SeasonProgressText>{autumnCompletion}% Complete</SeasonProgressText>
            </SeasonProgress>
          </SeasonContent>
        </SeasonCard>

        <SeasonCard
          as={motion.div}
          variants={itemVariants}
          season="winter"
          onClick={() => navigateToSeason('winter')}
        >
          <SeasonIcon>❄️</SeasonIcon>
          <SeasonContent>
            <SeasonTitle>Winter: Wisdom</SeasonTitle>
            <SeasonDescription>
              Discover your calling, align with your purpose, and create a fulfilling life.
            </SeasonDescription>
            <SeasonProgress>
              <SeasonProgressBar>
                <SeasonProgressFill percentage={winterCompletion} season="winter" />
              </SeasonProgressBar>
              <SeasonProgressText>{winterCompletion}% Complete</SeasonProgressText>
            </SeasonProgress>
          </SeasonContent>
        </SeasonCard>
      </SeasonsGrid>

      {overallCompletion === 100 && (
        <CompletionMessage as={motion.div} variants={itemVariants}>
          <CompletionIcon>🎉</CompletionIcon>
          <CompletionText>
            Congratulations! You&apos;ve completed all stages of your life journey. Continue to
            revisit and refine your journey as you grow and evolve.
          </CompletionText>
        </CompletionMessage>
      )}
    </Container>
  );
};

// Styled components
const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
`;

const Header = styled.div`
  margin-bottom: 32px;
  text-align: center;
`;

const Title = styled.h1`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 16px;
`;

const Description = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  max-width: 800px;
  margin: 0 auto;
  font-size: 1.1rem;
`;

const SeasonsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
  gap: 24px;
  margin-bottom: 32px;

  @media (max-width: 600px) {
    grid-template-columns: 1fr;
  }
`;

const SeasonCard = styled(Card)<{ season: string }>`
  display: flex;
  padding: 24px;
  cursor: pointer;
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
  border-left: 4px solid
    ${({ theme, season }) => {
      // Type assertion to ensure season is a valid key
      const validSeason = season as 'spring' | 'summer' | 'autumn' | 'winter';
      return theme.colors.seasons[validSeason].primary;
    }};

  &:hover {
    transform: translateY(-4px);
    box-shadow: ${({ theme }) => theme.shadows.lg};
  }
`;

const SeasonIcon = styled.div`
  font-size: 2.5rem;
  margin-right: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const SeasonContent = styled.div`
  flex: 1;
`;

const SeasonTitle = styled.h3`
  margin-bottom: 8px;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const SeasonDescription = styled.p`
  margin-bottom: 16px;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const SeasonProgress = styled.div`
  position: relative;
`;

const SeasonProgressBar = styled.div`
  height: 8px;
  background-color: ${({ theme }) => theme.colors?.paper || '#ffffff'};
  border-radius: 4px;
  overflow: hidden;
`;

const SeasonProgressFill = styled.div<{ percentage: number; season: string }>`
  height: 100%;
  width: ${(props) => props.percentage}%;
  background-color: ${({ theme, season }) => {
    // Type assertion to ensure season is a valid key
    const validSeason = season as 'spring' | 'summer' | 'autumn' | 'winter';
    return theme.colors.seasons[validSeason].primary;
  }};
  border-radius: 4px;
  transition: width 0.3s ease;
`;

const SeasonProgressText = styled.span`
  font-size: 0.75rem;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-top: 4px;
  display: block;
`;

const CompletionMessage = styled(Card)`
  display: flex;
  align-items: center;
  padding: 24px;
  margin-top: 32px;
  background-color: ${({ theme }) => `${theme.colors?.success || '#4caf50'.light}40`};
  border: 1px solid ${({ theme }) => theme.colors?.success || '#4caf50'};
`;

const CompletionIcon = styled.div`
  font-size: 2rem;
  margin-right: 16px;
`;

const CompletionText = styled.p`
  margin: 0;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  font-size: 1.1rem;
`;

export default SeasonsOfSelfPage;
