/**
 * Spring Page Component
 *
 * This component serves as the container for the Spring season stages (Pleasure and Happiness).
 * It provides navigation between stages and tracks progress within the Spring season.
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from '../../../theme/SimpleThemeProvider';
import { useSeasonsOfSelf } from '../context/SeasonsOfSelfContext';
import { PleasureStage } from '../components/Spring/PleasureStage';
import { HappinessStage } from '../components/Spring/HappinessStage';
import SeasonTracker from '../components/common/SeasonTracker';
import Button from '../../../components/ui/Button';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

// Component props
interface SpringPageProps {
  onComplete?: () => void;
  onBack?: () => void;
}

/**
 * Spring Page Component
 */
const SpringPage: React.FC<SpringPageProps> = ({ onComplete, onBack }) => {
  // Theme is used in styled components
  useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const { stages, activeStage, setActiveStage, getSeasonCompletionPercentage } = useSeasonsOfSelf();

  // Get Spring stages
  const springStages = stages.filter((stage) => stage.season === 'spring');

  // Determine active stage based on URL or context
  const [currentStage, setCurrentStage] = useState<string | null>(null);

  useEffect(() => {
    // Extract stage from URL if present
    const pathParts = location.pathname.split('/');
    const stagePath = pathParts[pathParts.length - 1];

    if (stagePath && stagePath !== 'spring') {
      // If URL has a specific stage, use it
      setCurrentStage(stagePath);
      setActiveStage(stagePath);
    } else if (activeStage && springStages.some((stage) => stage.id === activeStage)) {
      // If active stage is set and is a spring stage, use it
      setCurrentStage(activeStage);
    } else {
      // Default to first spring stage
      const firstStage = springStages[0]?.id || null;
      setCurrentStage(firstStage);
      setActiveStage(firstStage);
    }
  }, [location.pathname, activeStage, springStages, setActiveStage]);

  // Calculate completion percentage
  const completionPercentage = getSeasonCompletionPercentage('spring');

  // Handle stage navigation
  const handleStageChange = (stageId: string) => {
    setCurrentStage(stageId);
    setActiveStage(stageId);

    // Update URL without full navigation
    const stage = stages.find((s) => s.id === stageId);
    if (stage) {
      navigate(stage.path, { replace: true });
    }
  };

  // Handle back navigation
  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      navigate('/');
    }
  };

  // Render the current stage component
  const renderStageComponent = () => {
    switch (currentStage) {
      case 'pleasure':
        return <PleasureStage onComplete={() => handleStageChange('happiness')} />;
      case 'happiness':
        return (
          <HappinessStage onComplete={onComplete} onBack={() => handleStageChange('pleasure')} />
        );
      default:
        return <div>Select a stage to begin</div>;
    }
  };

  return (
    <Container as={motion.div} variants={containerVariants} initial="hidden" animate="visible">
      <Header as={motion.div} variants={itemVariants}>
        <Title>Spring: Beginning</Title>
        <Description>
          The Spring season represents the beginning of your life journey, focusing on discovering
          what brings you joy and building positive experiences.
        </Description>
      </Header>

      <motion.div variants={itemVariants}>
        <SeasonTracker season="spring" onStageSelect={handleStageChange} />
      </motion.div>

      <StageContent as={motion.div} variants={itemVariants}>
        {renderStageComponent()}
      </StageContent>

      <NavigationButtons as={motion.div} variants={itemVariants}>
        <Button variant="outlined" onClick={handleBack}>
          Back
        </Button>
        {completionPercentage === 100 && (
          <Button variant="primary" onClick={onComplete}>
            Complete Spring Season
          </Button>
        )}
      </NavigationButtons>
    </Container>
  );
};

// Styled components
const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 16px;
`;

const Title = styled.h1`
  color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  margin-bottom: 8px;
  font-size: 2rem;
`;

const Description = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'Secondary};
  margin-bottom: 16px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
`;

const StageContent = styled.div`
  flex: 1;
`;

const NavigationButtons = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: 24px;
`;

export default SpringPage;
