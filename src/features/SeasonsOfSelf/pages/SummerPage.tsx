/**
 * Summer Page Component
 *
 * This component serves as the container for the Summer season stages (Joy and Momentum).
 * It provides navigation between stages and tracks progress within the Summer season.
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from '../../../theme/SimpleThemeProvider';
import { useSeasonsOfSelf } from '../context/SeasonsOfSelfContext';
import { JoyStage } from '../components/Summer/JoyStage';
import { MomentumStage } from '../components/Summer/MomentumStage';
import SeasonTracker from '../components/common/SeasonTracker';
import Button from '../../../components/ui/Button';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

// Component props
interface SummerPageProps {
  onComplete?: () => void;
  onBack?: () => void;
}

/**
 * Summer Page Component
 */
const SummerPage: React.FC<SummerPageProps> = ({ onComplete, onBack }) => {
  // Theme is used in styled components via ThemeProvider
  useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const { stages, activeStage, setActiveStage, getSeasonCompletionPercentage } = useSeasonsOfSelf();

  // Get Summer stages
  const summerStages = stages.filter((stage) => stage.season === 'summer');

  // Determine active stage based on URL or context
  const [currentStage, setCurrentStage] = useState<string | null>(null);

  useEffect(() => {
    // Extract stage from URL path
    const pathParts = location.pathname.split('/');
    const stagePath = pathParts[pathParts.length - 1];

    // Find stage by path or use first summer stage
    const stageFromPath = summerStages.find((stage) => stage.path.includes(stagePath));

    if (stageFromPath) {
      setCurrentStage(stageFromPath.id);
      setActiveStage(stageFromPath.id);
    } else if (activeStage && summerStages.some((stage) => stage.id === activeStage)) {
      setCurrentStage(activeStage);
    } else if (summerStages.length > 0) {
      setCurrentStage(summerStages[0].id);
      setActiveStage(summerStages[0].id);
    }
  }, [location.pathname, summerStages, activeStage, setActiveStage]);

  // Calculate completion percentage
  const completionPercentage = getSeasonCompletionPercentage('summer');

  // Handle stage navigation
  const handleStageChange = (stageId: string) => {
    setCurrentStage(stageId);
    setActiveStage(stageId);

    // Update URL without full navigation
    const stage = stages.find((s) => s.id === stageId);
    if (stage) {
      navigate(stage.path, { replace: true });
    }
  };

  // Handle back navigation
  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      navigate('/seasons/spring');
    }
  };

  // Handle completion
  const handleComplete = () => {
    if (onComplete) {
      onComplete();
    } else {
      navigate('/seasons/autumn');
    }
  };

  // Render the current stage component
  const renderStageComponent = () => {
    switch (currentStage) {
      case 'joy':
        return <JoyStage onComplete={() => handleStageChange('momentum')} />;
      case 'momentum':
        return (
          <MomentumStage onComplete={handleComplete} onBack={() => handleStageChange('joy')} />
        );
      default:
        return <div>Select a stage to begin</div>;
    }
  };

  return (
    <Container as={motion.div} variants={containerVariants} initial="hidden" animate="visible">
      <Header as={motion.div} variants={itemVariants}>
        <Title>Summer: Growth</Title>
        <Description>
          The Summer season represents a time of growth and expansion in your life journey, focusing
          on finding deeper joy and building momentum.
        </Description>
      </Header>

      <motion.div variants={itemVariants}>
        <SeasonTracker season="summer" onStageSelect={handleStageChange} />
      </motion.div>

      <StageContent as={motion.div} variants={itemVariants}>
        {renderStageComponent()}
      </StageContent>

      <Navigation as={motion.div} variants={itemVariants}>
        <Button variant="outlined" onClick={handleBack}>
          Back to Spring Season
        </Button>
        {completionPercentage === 100 && (
          <Button variant="primary" onClick={handleComplete}>
            Continue to Autumn Season
          </Button>
        )}
      </Navigation>
    </Container>
  );
};

// Styled components
const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
`;

const Header = styled.div`
  margin-bottom: 24px;
  text-align: center;
`;

const Title = styled.h1`
  color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  margin-bottom: 8px;
`;

const Description = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'Secondary};
  max-width: 800px;
  margin: 0 auto;
`;

const StageContent = styled.div`
  margin-bottom: 24px;
`;

const Navigation = styled.div`
  display: flex;
  justify-content: space-between;
  max-width: 800px;
  margin: 0 auto;
`;

export default SummerPage;
