/**
 * Notification Provider
 *
 * This provider integrates the NotificationService with the app.
 */

import React, { ReactNode, useEffect } from 'react';
import { useToast } from '../../../components/ui/ToastProvider';
import { useUserPreferences } from '../../UserPreferences/context/UserPreferencesContext';
import NotificationService from '../../../services/NotificationService';

interface NotificationProviderProps {
  children: ReactNode;
}

const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const { showToast } = useToast();
  const { isNotificationAllowed } = useUserPreferences();

  // Initialize notification service
  useEffect(() => {
    const notificationService = NotificationService.getInstance();

    // Set toast function
    notificationService.setToastFunction(showToast);

    // Set notification permission function
    notificationService.setNotificationPermissionFunction(isNotificationAllowed);

    // Create a welcome notification on first load
    const hasShownWelcome = localStorage.getItem('lifecompass_welcome_notification');
    if (!hasShownWelcome) {
      notificationService.createNotification(
        'Welcome to LifeCompass! Explore the app to discover all its features.',
        {
          title: 'Welcome',
          type: 'info',
          icon: '👋',
          toast: true,
          toastOptions: {
            duration: 8000,
          },
        }
      );
      localStorage.setItem('lifecompass_welcome_notification', 'true');
    }
  }, [showToast, isNotificationAllowed]);

  return <>{children}</>;
};

export default NotificationProvider;
