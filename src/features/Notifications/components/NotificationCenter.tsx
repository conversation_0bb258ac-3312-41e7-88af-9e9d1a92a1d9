/**
 * Notification Center Component
 *
 * This component displays a list of notifications and provides controls
 * for managing them (mark as read, delete, etc.).
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '../../../context/ThemeContext';
import NotificationService, { Notification } from '../../../services/NotificationService';

interface NotificationCenterProps {
  onClose: () => void;
}

const NotificationCenter: React.FC<NotificationCenterProps> = ({ onClose }) => {
  const { theme } = useTheme();
  const notificationService = NotificationService.getInstance();

  // State
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [activeTab, setActiveTab] = useState<'all' | 'unread'>('all');
  const [isVisible, setIsVisible] = useState(true);

  // Load notifications on mount
  useEffect(() => {
    const loadNotifications = () => {
      const allNotifications = notificationService.getNotifications();
      setNotifications(allNotifications);
    };

    loadNotifications();

    // Set up interval to refresh notifications
    const intervalId = setInterval(loadNotifications, 30000);

    return () => clearInterval(intervalId);
  }, []);

  // Filter notifications based on active tab
  const filteredNotifications =
    activeTab === 'all'
      ? notifications
      : notifications.filter((notification) => !notification.read);

  // Handle mark as read
  const handleMarkAsRead = (id: string) => {
    notificationService.markAsRead(id);
    setNotifications(notificationService.getNotifications());
  };

  // Handle mark all as read
  const handleMarkAllAsRead = () => {
    notificationService.markAllAsRead();
    setNotifications(notificationService.getNotifications());
  };

  // Handle delete notification
  const handleDeleteNotification = (id: string) => {
    notificationService.deleteNotification(id);
    setNotifications(notificationService.getNotifications());
  };

  // Handle delete all notifications
  const handleDeleteAllNotifications = () => {
    notificationService.deleteAllNotifications();
    setNotifications([]);
  };

  // Handle delete read notifications
  const handleDeleteReadNotifications = () => {
    notificationService.deleteReadNotifications();
    setNotifications(notificationService.getNotifications());
  };

  // Handle close
  const handleClose = () => {
    setIsVisible(false);
    setTimeout(onClose, 300); // Wait for animation to complete
  };

  // Get icon for notification type
  const getNotificationIcon = (notification: Notification) => {
    if (notification.icon) {
      return notification.icon;
    }

    switch (notification.type) {
      case 'milestone':
        return '🏆';
      case 'update':
        return '🔄';
      case 'reminder':
        return '⏰';
      case 'system':
        return '🔧';
      case 'error':
        return '❌';
      case 'success':
        return '✅';
      case 'warning':
        return '⚠️';
      case 'info':
      default:
        return 'ℹ️';
    }
  };

  // Format timestamp
  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);

    if (diffSec < 60) {
      return 'Just now';
    } else if (diffMin < 60) {
      return `${diffMin} minute${diffMin === 1 ? '' : 's'} ago`;
    } else if (diffHour < 24) {
      return `${diffHour} hour${diffHour === 1 ? '' : 's'} ago`;
    } else if (diffDay < 7) {
      return `${diffDay} day${diffDay === 1 ? '' : 's'} ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <Overlay
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={handleClose}
        >
          <Container
            theme={theme}
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            onClick={(e) => e.stopPropagation()}
          >
            <Header theme={theme}>
              <Title theme={theme}>Notifications</Title>
              <CloseButton onClick={handleClose} theme={theme}>
                ×
              </CloseButton>
            </Header>

            <TabContainer theme={theme}>
              <Tab isActive={activeTab === 'all'} onClick={() => setActiveTab('all')} theme={theme}>
                All
              </Tab>
              <Tab
                isActive={activeTab === 'unread'}
                onClick={() => setActiveTab('unread')}
                theme={theme}
              >
                Unread
                {notifications.filter((n) => !n.read).length > 0 && (
                  <Badge theme={theme}>{notifications.filter((n) => !n.read).length}</Badge>
                )}
              </Tab>
            </TabContainer>

            <ActionBar theme={theme}>
              <ActionButton
                onClick={handleMarkAllAsRead}
                disabled={!notifications.some((n) => !n.read)}
                theme={theme}
              >
                Mark All Read
              </ActionButton>

              <ActionButton
                onClick={
                  activeTab === 'all' ? handleDeleteAllNotifications : handleDeleteReadNotifications
                }
                disabled={filteredNotifications.length === 0}
                theme={theme}
              >
                {activeTab === 'all' ? 'Clear All' : 'Clear Read'}
              </ActionButton>
            </ActionBar>

            <NotificationList theme={theme}>
              {filteredNotifications.length === 0 ? (
                <EmptyState theme={theme}>
                  <EmptyIcon>🔔</EmptyIcon>
                  <EmptyText>No notifications to display</EmptyText>
                </EmptyState>
              ) : (
                filteredNotifications.map((notification) => (
                  <NotificationItem key={notification.id} isRead={notification.read} theme={theme}>
                    <NotificationIcon>{getNotificationIcon(notification)}</NotificationIcon>

                    <NotificationContent>
                      {notification.title && (
                        <NotificationTitle isRead={notification.read}>
                          {notification.title}
                        </NotificationTitle>
                      )}

                      <NotificationMessage>{notification.message}</NotificationMessage>

                      <NotificationTimestamp>
                        {formatTimestamp(notification.timestamp)}
                      </NotificationTimestamp>

                      {notification.actionUrl && notification.actionLabel && (
                        <NotificationAction href={notification.actionUrl} theme={theme}>
                          {notification.actionLabel}
                        </NotificationAction>
                      )}
                    </NotificationContent>

                    <NotificationActions>
                      {!notification.read && (
                        <NotificationActionButton
                          onClick={() => handleMarkAsRead(notification.id)}
                          title="Mark as read"
                          theme={theme}
                        >
                          ✓
                        </NotificationActionButton>
                      )}

                      {notification.dismissible && (
                        <NotificationActionButton
                          onClick={() => handleDeleteNotification(notification.id)}
                          title="Delete notification"
                          theme={theme}
                        >
                          ×
                        </NotificationActionButton>
                      )}
                    </NotificationActions>
                  </NotificationItem>
                ))
              )}
            </NotificationList>
          </Container>
        </Overlay>
      )}
    </AnimatePresence>
  );
};

// Styled components
const Overlay = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const Container = styled(motion.div)<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

const Header = styled.div<{ theme: any }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
`;

const Title = styled.h2<{ theme: any }>`
  margin: 0;
  font-size: 1.25rem;
  font-weight: 500;
  color: ${(props) => props.theme.colors.primary.contrastText};
`;

const CloseButton = styled.button<{ theme: any }>`
  background: none;
  border: none;
  color: ${(props) => props.theme.colors.primary.contrastText};
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;

const TabContainer = styled.div<{ theme: any }>`
  display: flex;
  border-bottom: 1px solid ${(props) => props.theme.colors.divider};
`;

const Tab = styled.button<{ isActive: boolean; theme: any }>`
  padding: 12px 16px;
  background-color: transparent;
  color: ${(props) =>
    props.isActive ? props.theme.colors.primary.main : props.theme.colors.text.secondary};
  border: none;
  border-bottom: 2px solid
    ${(props) => (props.isActive ? props.theme.colors.primary.main : 'transparent')};
  cursor: pointer;
  font-weight: ${(props) => (props.isActive ? '500' : '400')};
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
    color: ${(props) => props.theme.colors.primary.main};
  }
`;

const Badge = styled.span<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  font-size: 0.75rem;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 20px;
  text-align: center;
`;

const ActionBar = styled.div<{ theme: any }>`
  display: flex;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid ${(props) => props.theme.colors.divider};
`;

const ActionButton = styled.button<{ theme: any; disabled?: boolean }>`
  background-color: transparent;
  color: ${(props) =>
    props.disabled ? props.theme.colors.text.disabled : props.theme.colors.primary.main};
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;

  &:hover:not(:disabled) {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const NotificationList = styled.div<{ theme: any }>`
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
`;

const NotificationItem = styled.div<{ isRead: boolean; theme: any }>`
  display: flex;
  padding: 12px 16px;
  border-bottom: 1px solid ${(props) => props.theme.colors.divider};
  background-color: ${(props) =>
    props.isRead ? 'transparent' : props.theme.colors.background.default};
  transition: background-color 0.2s;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: ${(props) => props.theme.colors.background.hover};
  }
`;

const NotificationIcon = styled.div`
  font-size: 1.25rem;
  margin-right: 12px;
  display: flex;
  align-items: flex-start;
`;

const NotificationContent = styled.div`
  flex: 1;
`;

const NotificationTitle = styled.h3<{ isRead: boolean }>`
  margin: 0 0 4px 0;
  font-size: 1rem;
  font-weight: ${(props) => (props.isRead ? '400' : '500')};
`;

const NotificationMessage = styled.p`
  margin: 0 0 8px 0;
  font-size: 0.875rem;
  line-height: 1.5;
`;

const NotificationTimestamp = styled.div`
  font-size: 0.75rem;
  color: #666;
  margin-bottom: 8px;
`;

const NotificationAction = styled.a<{ theme: any }>`
  display: inline-block;
  font-size: 0.875rem;
  color: ${(props) => props.theme.colors.primary.main};
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
`;

const NotificationActions = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-left: 12px;
`;

const NotificationActionButton = styled.button<{ theme: any }>`
  background: none;
  border: none;
  color: ${(props) => props.theme.colors.text.secondary};
  font-size: 1rem;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
    color: ${(props) => props.theme.colors.primary.main};
  }
`;

const EmptyState = styled.div<{ theme: any }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const EmptyIcon = styled.div`
  font-size: 2rem;
  margin-bottom: 16px;
`;

const EmptyText = styled.p`
  margin: 0;
  font-size: 1rem;
`;

export default NotificationCenter;
