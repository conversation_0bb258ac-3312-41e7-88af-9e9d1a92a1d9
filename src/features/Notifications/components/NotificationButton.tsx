/**
 * Notification Button Component
 *
 * This component displays a button that shows the number of unread notifications
 * and opens the notification center when clicked.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '../../../context/ThemeContext';
import NotificationService from '../../../services/NotificationService';
import NotificationCenter from './NotificationCenter';

const NotificationButton: React.FC = () => {
  const { theme } = useTheme();
  const notificationService = NotificationService.getInstance();

  // State
  const [unreadCount, setUnreadCount] = useState(0);
  const [showNotificationCenter, setShowNotificationCenter] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  // Update unread count
  useEffect(() => {
    const updateUnreadCount = () => {
      const unreadNotifications = notificationService.getUnreadNotifications();
      setUnreadCount(unreadNotifications.length);

      // Animate the button if there are new notifications
      if (unreadNotifications.length > 0 && !isAnimating) {
        setIsAnimating(true);
        setTimeout(() => setIsAnimating(false), 1000);
      }
    };

    // Update immediately
    updateUnreadCount();

    // Set up interval to refresh unread count
    const intervalId = setInterval(updateUnreadCount, 30000);

    return () => clearInterval(intervalId);
  }, []);

  // Toggle notification center
  const toggleNotificationCenter = () => {
    setShowNotificationCenter((prev) => !prev);
  };

  // Close notification center
  const closeNotificationCenter = () => {
    setShowNotificationCenter(false);
  };

  return (
    <>
      <ButtonContainer>
        <Button
          onClick={toggleNotificationCenter}
          theme={theme}
          aria-label={`${unreadCount} unread notifications`}
        >
          <BellIcon>🔔</BellIcon>

          <AnimatePresence>
            {unreadCount > 0 && (
              <Badge
                theme={theme}
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                exit={{ scale: 0 }}
              >
                {unreadCount > 99 ? '99+' : unreadCount}
              </Badge>
            )}
          </AnimatePresence>
        </Button>

        {isAnimating && (
          <AnimationRing
            theme={theme}
            animate={{
              scale: [1, 1.5, 1],
              opacity: [0.5, 0, 0],
            }}
            transition={{
              duration: 1,
              ease: 'easeOut',
            }}
          />
        )}
      </ButtonContainer>

      {showNotificationCenter && <NotificationCenter onClose={closeNotificationCenter} />}
    </>
  );
};

// Styled components
const ButtonContainer = styled.div`
  position: relative;
  display: inline-block;
`;

const Button = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  color: ${(props) => props.theme.colors.text.primary};
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }
`;

const BellIcon = styled.span`
  font-size: 1.25rem;
`;

const Badge = styled(motion.div)<{ theme: any }>`
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: ${(props) => props.theme.colors.error.main};
  color: ${(props) => props.theme.colors.error.contrastText};
  font-size: 0.75rem;
  font-weight: 500;
  min-width: 18px;
  height: 18px;
  border-radius: 9px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
`;

const AnimationRing = styled(motion.div)<{ theme: any }>`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  border: 2px solid ${(props) => props.theme.colors.primary.main};
  pointer-events: none;
`;

export default NotificationButton;
