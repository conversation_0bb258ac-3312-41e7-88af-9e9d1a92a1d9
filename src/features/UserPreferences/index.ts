/**
 * User Preferences Feature Index
 *
 * This file exports all components and utilities related to user preferences.
 */

// Export components
export { default as UserPreferencesUI } from './components/UserPreferencesUI';
export { default as UserPreferencesPage } from './pages/UserPreferencesPage';

// Export context
export {
  UserPreferencesProvider,
  useUserPreferences,
  defaultPreferences,
} from './context/UserPreferencesContext';
export type {
  NotificationPreferences,
  PrivacyPreferences,
  AccessibilityPreferences,
  DisplayPreferences,
  UserPreferences,
} from './context/UserPreferencesContext';
