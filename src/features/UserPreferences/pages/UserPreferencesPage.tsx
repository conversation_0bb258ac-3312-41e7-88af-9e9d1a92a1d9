/**
 * User Preferences Page
 *
 * This page displays the user preferences UI.
 */

import React from 'react';
import styled from 'styled-components';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '../../../context/ThemeContext';
import UserPreferencesUI from '../components/UserPreferencesUI';

const UserPreferencesPage: React.FC = () => {
  const { theme } = useTheme();
  const navigate = useNavigate();

  // Handle close
  const handleClose = () => {
    navigate(-1);
  };

  return (
    <Container theme={theme}>
      <PageHeader theme={theme}>
        <BackButton onClick={handleClose} theme={theme}>
          ← Back
        </BackButton>
        <PageTitle theme={theme}>User Preferences</PageTitle>
      </PageHeader>

      <Content>
        <UserPreferencesUI onClose={handleClose} />
      </Content>
    </Container>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  padding: 24px;
  min-height: 100vh;
  background-color: ${(props) => props.theme.colors.background.default};
`;

const PageHeader = styled.div<{ theme: any }>`
  display: flex;
  align-items: center;
  margin-bottom: 24px;
`;

const BackButton = styled.button<{ theme: any }>`
  background: none;
  border: none;
  color: ${(props) => props.theme.colors.primary.main};
  font-size: 1rem;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.hover};
  }
`;

const PageTitle = styled.h1<{ theme: any }>`
  margin: 0 0 0 16px;
  font-size: 1.5rem;
  font-weight: 500;
  color: ${(props) => props.theme.colors.text.primary};
`;

const Content = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

export default UserPreferencesPage;
