/**
 * User Preferences UI Component
 *
 * This component provides a user interface for managing user preferences.
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../context/ThemeContext';
import { useUserPreferences } from '../context/UserPreferencesContext';

interface UserPreferencesUIProps {
  onClose?: () => void;
}

const UserPreferencesUI: React.FC<UserPreferencesUIProps> = ({ onClose }) => {
  const { theme } = useTheme();
  const {
    preferences,
    updateNotificationPreferences,
    updatePrivacyPreferences,
    updateAccessibilityPreferences,
    updateDisplayPreferences,
    resetPreferences,
  } = useUserPreferences();

  // State for active tab
  const [activeTab, setActiveTab] = useState<
    'notifications' | 'privacy' | 'accessibility' | 'display'
  >('notifications');

  // State for confirmation dialog
  const [showConfirmation, setShowConfirmation] = useState(false);

  // Handle reset preferences
  const handleResetPreferences = () => {
    setShowConfirmation(true);
  };

  // Handle confirm reset
  const handleConfirmReset = () => {
    resetPreferences();
    setShowConfirmation(false);
  };

  // Handle cancel reset
  const handleCancelReset = () => {
    setShowConfirmation(false);
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>User Preferences</Title>
        {onClose && (
          <CloseButton onClick={onClose} theme={theme}>
            ×
          </CloseButton>
        )}
      </Header>

      <Content>
        <Sidebar theme={theme}>
          <TabButton
            isActive={activeTab === 'notifications'}
            onClick={() => setActiveTab('notifications')}
            theme={theme}
          >
            <TabIcon>🔔</TabIcon>
            <TabLabel>Notifications</TabLabel>
          </TabButton>

          <TabButton
            isActive={activeTab === 'privacy'}
            onClick={() => setActiveTab('privacy')}
            theme={theme}
          >
            <TabIcon>🔒</TabIcon>
            <TabLabel>Privacy</TabLabel>
          </TabButton>

          <TabButton
            isActive={activeTab === 'accessibility'}
            onClick={() => setActiveTab('accessibility')}
            theme={theme}
          >
            <TabIcon>♿</TabIcon>
            <TabLabel>Accessibility</TabLabel>
          </TabButton>

          <TabButton
            isActive={activeTab === 'display'}
            onClick={() => setActiveTab('display')}
            theme={theme}
          >
            <TabIcon>🖥️</TabIcon>
            <TabLabel>Display</TabLabel>
          </TabButton>

          <ResetButton onClick={handleResetPreferences} theme={theme}>
            Reset All Preferences
          </ResetButton>
        </Sidebar>

        <TabContent theme={theme}>
          {activeTab === 'notifications' && (
            <TabPanel>
              <SectionTitle>Notification Settings</SectionTitle>

              <PreferenceGroup>
                <PreferenceItem>
                  <PreferenceLabel>Enable Notifications</PreferenceLabel>
                  <Toggle
                    isActive={preferences.notifications.enableNotifications}
                    onClick={() =>
                      updateNotificationPreferences({
                        enableNotifications: !preferences.notifications.enableNotifications,
                      })
                    }
                    theme={theme}
                  />
                </PreferenceItem>

                <PreferenceItem>
                  <PreferenceLabel>Email Notifications</PreferenceLabel>
                  <Toggle
                    isActive={preferences.notifications.emailNotifications}
                    onClick={() =>
                      updateNotificationPreferences({
                        emailNotifications: !preferences.notifications.emailNotifications,
                      })
                    }
                    disabled={!preferences.notifications.enableNotifications}
                    theme={theme}
                  />
                </PreferenceItem>

                <PreferenceItem>
                  <PreferenceLabel>App Notifications</PreferenceLabel>
                  <Toggle
                    isActive={preferences.notifications.appNotifications}
                    onClick={() =>
                      updateNotificationPreferences({
                        appNotifications: !preferences.notifications.appNotifications,
                      })
                    }
                    disabled={!preferences.notifications.enableNotifications}
                    theme={theme}
                  />
                </PreferenceItem>
              </PreferenceGroup>

              <PreferenceGroup>
                <PreferenceItem>
                  <PreferenceLabel>Reminder Frequency</PreferenceLabel>
                  <Select
                    value={preferences.notifications.reminderFrequency}
                    onChange={(e) =>
                      updateNotificationPreferences({
                        reminderFrequency: e.target.value as
                          | 'daily'
                          | 'weekly'
                          | 'monthly'
                          | 'never',
                      })
                    }
                    disabled={!preferences.notifications.enableNotifications}
                    theme={theme}
                  >
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                    <option value="never">Never</option>
                  </Select>
                </PreferenceItem>

                <PreferenceItem>
                  <PreferenceLabel>Notify on Milestones</PreferenceLabel>
                  <Toggle
                    isActive={preferences.notifications.notifyOnMilestones}
                    onClick={() =>
                      updateNotificationPreferences({
                        notifyOnMilestones: !preferences.notifications.notifyOnMilestones,
                      })
                    }
                    disabled={!preferences.notifications.enableNotifications}
                    theme={theme}
                  />
                </PreferenceItem>

                <PreferenceItem>
                  <PreferenceLabel>Notify on Updates</PreferenceLabel>
                  <Toggle
                    isActive={preferences.notifications.notifyOnUpdates}
                    onClick={() =>
                      updateNotificationPreferences({
                        notifyOnUpdates: !preferences.notifications.notifyOnUpdates,
                      })
                    }
                    disabled={!preferences.notifications.enableNotifications}
                    theme={theme}
                  />
                </PreferenceItem>
              </PreferenceGroup>

              <PreferenceGroup>
                <PreferenceItem>
                  <PreferenceLabel>Enable Quiet Hours</PreferenceLabel>
                  <Toggle
                    isActive={preferences.notifications.enableQuietHours}
                    onClick={() =>
                      updateNotificationPreferences({
                        enableQuietHours: !preferences.notifications.enableQuietHours,
                      })
                    }
                    disabled={!preferences.notifications.enableNotifications}
                    theme={theme}
                  />
                </PreferenceItem>

                <PreferenceItem>
                  <PreferenceLabel>Quiet Hours Start</PreferenceLabel>
                  <TimeInput
                    type="time"
                    value={preferences.notifications.quietHoursStart}
                    onChange={(e) =>
                      updateNotificationPreferences({
                        quietHoursStart: e.target.value,
                      })
                    }
                    disabled={
                      !preferences.notifications.enableNotifications ||
                      !preferences.notifications.enableQuietHours
                    }
                    theme={theme}
                  />
                </PreferenceItem>

                <PreferenceItem>
                  <PreferenceLabel>Quiet Hours End</PreferenceLabel>
                  <TimeInput
                    type="time"
                    value={preferences.notifications.quietHoursEnd}
                    onChange={(e) =>
                      updateNotificationPreferences({
                        quietHoursEnd: e.target.value,
                      })
                    }
                    disabled={
                      !preferences.notifications.enableNotifications ||
                      !preferences.notifications.enableQuietHours
                    }
                    theme={theme}
                  />
                </PreferenceItem>
              </PreferenceGroup>
            </TabPanel>
          )}

          {activeTab === 'privacy' && (
            <TabPanel>
              <SectionTitle>Privacy Settings</SectionTitle>

              <PreferenceGroup>
                <PreferenceItem>
                  <PreferenceLabel>Share Usage Data</PreferenceLabel>
                  <Toggle
                    isActive={preferences.privacy.shareUsageData}
                    onClick={() =>
                      updatePrivacyPreferences({
                        shareUsageData: !preferences.privacy.shareUsageData,
                      })
                    }
                    theme={theme}
                  />
                </PreferenceItem>

                <PreferenceItem>
                  <PreferenceLabel>Store Data Locally</PreferenceLabel>
                  <Toggle
                    isActive={preferences.privacy.storeDataLocally}
                    onClick={() =>
                      updatePrivacyPreferences({
                        storeDataLocally: !preferences.privacy.storeDataLocally,
                      })
                    }
                    theme={theme}
                  />
                </PreferenceItem>

                <PreferenceItem>
                  <PreferenceLabel>Enable Backups</PreferenceLabel>
                  <Toggle
                    isActive={preferences.privacy.enableBackups}
                    onClick={() =>
                      updatePrivacyPreferences({
                        enableBackups: !preferences.privacy.enableBackups,
                      })
                    }
                    theme={theme}
                  />
                </PreferenceItem>

                <PreferenceItem>
                  <PreferenceLabel>Encrypt Data</PreferenceLabel>
                  <Toggle
                    isActive={preferences.privacy.encryptData}
                    onClick={() =>
                      updatePrivacyPreferences({
                        encryptData: !preferences.privacy.encryptData,
                      })
                    }
                    theme={theme}
                  />
                </PreferenceItem>
              </PreferenceGroup>

              <PreferenceGroup>
                <PreferenceItem>
                  <PreferenceLabel>Data Retention Period</PreferenceLabel>
                  <Select
                    value={preferences.privacy.dataRetentionPeriod}
                    onChange={(e) =>
                      updatePrivacyPreferences({
                        dataRetentionPeriod: e.target.value as
                          | 'forever'
                          | '1year'
                          | '6months'
                          | '3months',
                      })
                    }
                    theme={theme}
                  >
                    <option value="forever">Forever</option>
                    <option value="1year">1 Year</option>
                    <option value="6months">6 Months</option>
                    <option value="3months">3 Months</option>
                  </Select>
                </PreferenceItem>
              </PreferenceGroup>
            </TabPanel>
          )}

          {activeTab === 'accessibility' && (
            <TabPanel>
              <SectionTitle>Accessibility Settings</SectionTitle>

              <PreferenceGroup>
                <PreferenceItem>
                  <PreferenceLabel>Font Size</PreferenceLabel>
                  <Select
                    value={preferences.accessibility.fontSize}
                    onChange={(e) =>
                      updateAccessibilityPreferences({
                        fontSize: e.target.value as 'small' | 'medium' | 'large' | 'x-large',
                      })
                    }
                    theme={theme}
                  >
                    <option value="small">Small</option>
                    <option value="medium">Medium</option>
                    <option value="large">Large</option>
                    <option value="x-large">Extra Large</option>
                  </Select>
                </PreferenceItem>

                <PreferenceItem>
                  <PreferenceLabel>High Contrast</PreferenceLabel>
                  <Toggle
                    isActive={preferences.accessibility.highContrast}
                    onClick={() =>
                      updateAccessibilityPreferences({
                        highContrast: !preferences.accessibility.highContrast,
                      })
                    }
                    theme={theme}
                  />
                </PreferenceItem>

                <PreferenceItem>
                  <PreferenceLabel>Reduce Motion</PreferenceLabel>
                  <Toggle
                    isActive={preferences.accessibility.reduceMotion}
                    onClick={() =>
                      updateAccessibilityPreferences({
                        reduceMotion: !preferences.accessibility.reduceMotion,
                      })
                    }
                    theme={theme}
                  />
                </PreferenceItem>

                <PreferenceItem>
                  <PreferenceLabel>Enable Screen Reader</PreferenceLabel>
                  <Toggle
                    isActive={preferences.accessibility.enableScreenReader}
                    onClick={() =>
                      updateAccessibilityPreferences({
                        enableScreenReader: !preferences.accessibility.enableScreenReader,
                      })
                    }
                    theme={theme}
                  />
                </PreferenceItem>
              </PreferenceGroup>

              <PreferenceGroup>
                <PreferenceItem>
                  <PreferenceLabel>Color Blind Mode</PreferenceLabel>
                  <Select
                    value={preferences.accessibility.colorBlindMode}
                    onChange={(e) =>
                      updateAccessibilityPreferences({
                        colorBlindMode: e.target.value as
                          | 'none'
                          | 'protanopia'
                          | 'deuteranopia'
                          | 'tritanopia',
                      })
                    }
                    theme={theme}
                  >
                    <option value="none">None</option>
                    <option value="protanopia">Protanopia (Red-Blind)</option>
                    <option value="deuteranopia">Deuteranopia (Green-Blind)</option>
                    <option value="tritanopia">Tritanopia (Blue-Blind)</option>
                  </Select>
                </PreferenceItem>
              </PreferenceGroup>
            </TabPanel>
          )}

          {activeTab === 'display' && (
            <TabPanel>
              <SectionTitle>Display Settings</SectionTitle>

              <PreferenceGroup>
                <PreferenceItem>
                  <PreferenceLabel>Default View</PreferenceLabel>
                  <Select
                    value={preferences.display.defaultView}
                    onChange={(e) =>
                      updateDisplayPreferences({
                        defaultView: e.target.value as 'compass' | 'journey' | 'dashboard',
                      })
                    }
                    theme={theme}
                  >
                    <option value="compass">Compass</option>
                    <option value="journey">Journey</option>
                    <option value="dashboard">Dashboard</option>
                  </Select>
                </PreferenceItem>

                <PreferenceItem>
                  <PreferenceLabel>Show Tips</PreferenceLabel>
                  <Toggle
                    isActive={preferences.display.showTips}
                    onClick={() =>
                      updateDisplayPreferences({
                        showTips: !preferences.display.showTips,
                      })
                    }
                    theme={theme}
                  />
                </PreferenceItem>

                <PreferenceItem>
                  <PreferenceLabel>Compact Mode</PreferenceLabel>
                  <Toggle
                    isActive={preferences.display.compactMode}
                    onClick={() =>
                      updateDisplayPreferences({
                        compactMode: !preferences.display.compactMode,
                      })
                    }
                    theme={theme}
                  />
                </PreferenceItem>

                <PreferenceItem>
                  <PreferenceLabel>Show Progress Bars</PreferenceLabel>
                  <Toggle
                    isActive={preferences.display.showProgressBars}
                    onClick={() =>
                      updateDisplayPreferences({
                        showProgressBars: !preferences.display.showProgressBars,
                      })
                    }
                    theme={theme}
                  />
                </PreferenceItem>
              </PreferenceGroup>

              <PreferenceGroup>
                <PreferenceItem>
                  <PreferenceLabel>Date Format</PreferenceLabel>
                  <Select
                    value={preferences.display.dateFormat}
                    onChange={(e) =>
                      updateDisplayPreferences({
                        dateFormat: e.target.value as 'MM/DD/YYYY' | 'DD/MM/YYYY' | 'YYYY-MM-DD',
                      })
                    }
                    theme={theme}
                  >
                    <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                    <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                    <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                  </Select>
                </PreferenceItem>

                <PreferenceItem>
                  <PreferenceLabel>Time Format</PreferenceLabel>
                  <Select
                    value={preferences.display.timeFormat}
                    onChange={(e) =>
                      updateDisplayPreferences({
                        timeFormat: e.target.value as '12h' | '24h',
                      })
                    }
                    theme={theme}
                  >
                    <option value="12h">12-hour (AM/PM)</option>
                    <option value="24h">24-hour</option>
                  </Select>
                </PreferenceItem>
              </PreferenceGroup>
            </TabPanel>
          )}
        </TabContent>
      </Content>

      {showConfirmation && (
        <ConfirmationOverlay>
          <ConfirmationDialog theme={theme}>
            <ConfirmationTitle>Reset Preferences</ConfirmationTitle>
            <ConfirmationMessage>
              Are you sure you want to reset all preferences to their default values? This action
              cannot be undone.
            </ConfirmationMessage>
            <ConfirmationButtons>
              <CancelButton onClick={handleCancelReset} theme={theme}>
                Cancel
              </CancelButton>
              <ConfirmButton onClick={handleConfirmReset} theme={theme}>
                Reset
              </ConfirmButton>
            </ConfirmationButtons>
          </ConfirmationDialog>
        </ConfirmationOverlay>
      )}
    </Container>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
  overflow: hidden;
`;

const Header = styled.div<{ theme: any }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
`;

const Title = styled.h2<{ theme: any }>`
  margin: 0;
  font-size: 1.5rem;
  font-weight: 500;
  color: ${(props) => props.theme.colors.primary.contrastText};
`;

const CloseButton = styled.button<{ theme: any }>`
  background: none;
  border: none;
  color: ${(props) => props.theme.colors.primary.contrastText};
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;

const Content = styled.div`
  display: flex;
  height: 500px;
`;

const Sidebar = styled.div<{ theme: any }>`
  width: 200px;
  background-color: ${(props) => props.theme.colors.background.default};
  border-right: 1px solid ${(props) => props.theme.colors.divider};
  padding: 16px 0;
  display: flex;
  flex-direction: column;
`;

const TabButton = styled.button<{ isActive: boolean; theme: any }>`
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: ${(props) =>
    props.isActive ? props.theme.colors.background.paper : 'transparent'};
  color: ${(props) =>
    props.isActive ? props.theme.colors.primary.main : props.theme.colors.text.primary};
  border: none;
  border-left: 3px solid
    ${(props) => (props.isActive ? props.theme.colors.primary.main : 'transparent')};
  cursor: pointer;
  text-align: left;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) =>
      props.isActive ? props.theme.colors.background.paper : props.theme.colors.background.hover};
  }
`;

const TabIcon = styled.span`
  margin-right: 12px;
  font-size: 1.25rem;
`;

const TabLabel = styled.span`
  font-size: 0.9rem;
  font-weight: 500;
`;

const ResetButton = styled.button<{ theme: any }>`
  margin-top: auto;
  margin-left: 16px;
  margin-right: 16px;
  padding: 8px 12px;
  background-color: ${(props) => props.theme.colors.error.main};
  color: ${(props) => props.theme.colors.error.contrastText};
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.error.dark};
  }
`;

const TabContent = styled.div<{ theme: any }>`
  flex: 1;
  padding: 24px;
  overflow-y: auto;
`;

const TabPanel = styled.div`
  height: 100%;
`;

const SectionTitle = styled.h3`
  margin: 0 0 24px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const PreferenceGroup = styled.div`
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #eee;

  &:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }
`;

const PreferenceItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
`;

const PreferenceLabel = styled.label`
  font-size: 0.9rem;
  font-weight: 500;
`;

const Toggle = styled.button<{ isActive: boolean; disabled?: boolean; theme: any }>`
  width: 48px;
  height: 24px;
  border-radius: 12px;
  background-color: ${(props) =>
    props.isActive
      ? props.theme.colors.primary.main
      : props.disabled
        ? props.theme.colors.background.default
        : '#ccc'};
  position: relative;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  border: none;
  transition: all 0.2s;
  opacity: ${(props) => (props.disabled ? 0.5 : 1)};

  &:before {
    content: '';
    position: absolute;
    top: 2px;
    left: ${(props) => (props.isActive ? '26px' : '2px')};
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: white;
    transition: all 0.2s;
  }
`;

const Select = styled.select<{ theme: any; disabled?: boolean }>`
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid ${(props) => props.theme.colors.divider};
  background-color: ${(props) => props.theme.colors.background.paper};
  color: ${(props) =>
    props.disabled ? props.theme.colors.text.disabled : props.theme.colors.text.primary};
  font-size: 0.9rem;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  opacity: ${(props) => (props.disabled ? 0.5 : 1)};

  &:focus {
    outline: none;
    border-color: ${(props) => props.theme.colors.primary.main};
  }
`;

const TimeInput = styled.input<{ theme: any; disabled?: boolean }>`
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid ${(props) => props.theme.colors.divider};
  background-color: ${(props) => props.theme.colors.background.paper};
  color: ${(props) =>
    props.disabled ? props.theme.colors.text.disabled : props.theme.colors.text.primary};
  font-size: 0.9rem;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'text')};
  opacity: ${(props) => (props.disabled ? 0.5 : 1)};

  &:focus {
    outline: none;
    border-color: ${(props) => props.theme.colors.primary.main};
  }
`;

const ConfirmationOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ConfirmationDialog = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 24px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
`;

const ConfirmationTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const ConfirmationMessage = styled.p`
  margin: 0 0 24px 0;
  line-height: 1.5;
`;

const ConfirmationButtons = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 12px;
`;

const CancelButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.text.secondary};
  border: 1px solid ${(props) => props.theme.colors.divider};
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const ConfirmButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.error.main};
  color: ${(props) => props.theme.colors.error.contrastText};
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.error.dark};
  }
`;

export default UserPreferencesUI;
