/**
 * User Preferences Context
 *
 * This context provides user preferences state and management for the application.
 * It handles preferences like notifications, privacy, accessibility, and more.
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useToast } from '../../../components/ui/ToastProvider';

// Types
export interface NotificationPreferences {
  enableNotifications: boolean;
  emailNotifications: boolean;
  appNotifications: boolean;
  reminderFrequency: 'daily' | 'weekly' | 'monthly' | 'never';
  notifyOnMilestones: boolean;
  notifyOnUpdates: boolean;
  quietHoursStart: string; // Format: "HH:MM"
  quietHoursEnd: string; // Format: "HH:MM"
  enableQuietHours: boolean;
}

export interface PrivacyPreferences {
  shareUsageData: boolean;
  storeDataLocally: boolean;
  enableBackups: boolean;
  encryptData: boolean;
  dataRetentionPeriod: 'forever' | '1year' | '6months' | '3months';
}

export interface AccessibilityPreferences {
  fontSize: 'small' | 'medium' | 'large' | 'x-large';
  highContrast: boolean;
  reduceMotion: boolean;
  enableScreenReader: boolean;
  colorBlindMode: 'none' | 'protanopia' | 'deuteranopia' | 'tritanopia';
}

export interface DisplayPreferences {
  defaultView: 'compass' | 'journey' | 'dashboard';
  showTips: boolean;
  compactMode: boolean;
  showProgressBars: boolean;
  dateFormat: 'MM/DD/YYYY' | 'DD/MM/YYYY' | 'YYYY-MM-DD';
  timeFormat: '12h' | '24h';
}

export interface UserPreferences {
  notifications: NotificationPreferences;
  privacy: PrivacyPreferences;
  accessibility: AccessibilityPreferences;
  display: DisplayPreferences;
}

// Default preferences
export const defaultPreferences: UserPreferences = {
  notifications: {
    enableNotifications: true,
    emailNotifications: false,
    appNotifications: true,
    reminderFrequency: 'weekly',
    notifyOnMilestones: true,
    notifyOnUpdates: true,
    quietHoursStart: '22:00',
    quietHoursEnd: '08:00',
    enableQuietHours: false,
  },
  privacy: {
    shareUsageData: false,
    storeDataLocally: true,
    enableBackups: true,
    encryptData: false,
    dataRetentionPeriod: 'forever',
  },
  accessibility: {
    fontSize: 'medium',
    highContrast: false,
    reduceMotion: false,
    enableScreenReader: false,
    colorBlindMode: 'none',
  },
  display: {
    defaultView: 'compass',
    showTips: true,
    compactMode: false,
    showProgressBars: true,
    dateFormat: 'MM/DD/YYYY',
    timeFormat: '12h',
  },
};

// Storage key for user preferences
const STORAGE_KEY = 'lifecompass_user_preferences';

// Context type
interface UserPreferencesContextType {
  preferences: UserPreferences;
  updateNotificationPreferences: (preferences: Partial<NotificationPreferences>) => void;
  updatePrivacyPreferences: (preferences: Partial<PrivacyPreferences>) => void;
  updateAccessibilityPreferences: (preferences: Partial<AccessibilityPreferences>) => void;
  updateDisplayPreferences: (preferences: Partial<DisplayPreferences>) => void;
  resetPreferences: () => void;
  isNotificationAllowed: (type: 'milestone' | 'update' | 'reminder') => boolean;
}

// Create context
const UserPreferencesContext = createContext<UserPreferencesContextType | undefined>(undefined);

// Provider props
interface UserPreferencesProviderProps {
  children: ReactNode;
}

/**
 * User Preferences Provider
 */
export const UserPreferencesProvider: React.FC<UserPreferencesProviderProps> = ({ children }) => {
  const { showToast } = useToast();

  // Initialize state from localStorage or defaults
  const [preferences, setPreferences] = useState<UserPreferences>(() => {
    try {
      const storedPreferences = localStorage.getItem(STORAGE_KEY);
      if (storedPreferences) {
        return JSON.parse(storedPreferences);
      }
      return defaultPreferences;
    } catch (error) {
      console.error('Error loading preferences from localStorage:', error);
      return defaultPreferences;
    }
  });

  // Save preferences to localStorage when they change
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(preferences));
    } catch (error) {
      console.error('Error saving preferences to localStorage:', error);
      showToast('Failed to save preferences', { type: 'error' });
    }
  }, [preferences, showToast]);

  // Update notification preferences
  const updateNotificationPreferences = (newPreferences: Partial<NotificationPreferences>) => {
    setPreferences((prev) => ({
      ...prev,
      notifications: {
        ...prev.notifications,
        ...newPreferences,
      },
    }));
    showToast('Notification preferences updated', { type: 'success' });
  };

  // Update privacy preferences
  const updatePrivacyPreferences = (newPreferences: Partial<PrivacyPreferences>) => {
    setPreferences((prev) => ({
      ...prev,
      privacy: {
        ...prev.privacy,
        ...newPreferences,
      },
    }));
    showToast('Privacy preferences updated', { type: 'success' });
  };

  // Update accessibility preferences
  const updateAccessibilityPreferences = (newPreferences: Partial<AccessibilityPreferences>) => {
    setPreferences((prev) => ({
      ...prev,
      accessibility: {
        ...prev.accessibility,
        ...newPreferences,
      },
    }));
    showToast('Accessibility preferences updated', { type: 'success' });
  };

  // Update display preferences
  const updateDisplayPreferences = (newPreferences: Partial<DisplayPreferences>) => {
    setPreferences((prev) => ({
      ...prev,
      display: {
        ...prev.display,
        ...newPreferences,
      },
    }));
    showToast('Display preferences updated', { type: 'success' });
  };

  // Reset preferences to defaults
  const resetPreferences = () => {
    setPreferences(defaultPreferences);
    showToast('Preferences reset to defaults', { type: 'info' });
  };

  // Check if a notification is allowed based on user preferences
  const isNotificationAllowed = (type: 'milestone' | 'update' | 'reminder'): boolean => {
    const { notifications } = preferences;

    // If notifications are disabled, no notifications are allowed
    if (!notifications.enableNotifications) {
      return false;
    }

    // Check if we're in quiet hours
    if (notifications.enableQuietHours) {
      const now = new Date();
      const currentHour = now.getHours();
      const currentMinute = now.getMinutes();
      const currentTime = currentHour * 60 + currentMinute;

      const [startHour, startMinute] = notifications.quietHoursStart.split(':').map(Number);
      const [endHour, endMinute] = notifications.quietHoursEnd.split(':').map(Number);

      const startTime = startHour * 60 + startMinute;
      const endTime = endHour * 60 + endMinute;

      // Check if current time is within quiet hours
      if (startTime < endTime) {
        // Simple case: start time is before end time (e.g., 22:00 to 08:00)
        if (currentTime >= startTime && currentTime <= endTime) {
          return false;
        }
      } else {
        // Complex case: start time is after end time (e.g., 22:00 to 08:00)
        if (currentTime >= startTime || currentTime <= endTime) {
          return false;
        }
      }
    }

    // Check specific notification types
    switch (type) {
      case 'milestone':
        return notifications.notifyOnMilestones;
      case 'update':
        return notifications.notifyOnUpdates;
      case 'reminder':
        return notifications.reminderFrequency !== 'never';
      default:
        return true;
    }
  };

  // Context value
  const value: UserPreferencesContextType = {
    preferences,
    updateNotificationPreferences,
    updatePrivacyPreferences,
    updateAccessibilityPreferences,
    updateDisplayPreferences,
    resetPreferences,
    isNotificationAllowed,
  };

  return (
    <UserPreferencesContext.Provider value={value}>{children}</UserPreferencesContext.Provider>
  );
};

/**
 * Hook to use the user preferences context
 */
export const useUserPreferences = (): UserPreferencesContextType => {
  const context = useContext(UserPreferencesContext);
  if (context === undefined) {
    throw new Error('useUserPreferences must be used within a UserPreferencesProvider');
  }
  return context;
};

export default UserPreferencesContext;
