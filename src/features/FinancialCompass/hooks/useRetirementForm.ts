import { useState, useEffect, useCallback } from 'react';
import { RetirementFormData, RetirementFormState } from '../types/retirementTypes';
import { generateRetirementPlan } from '../../../utils/retirementPlanningUtils';
import { retirementFormSchema } from '../validation/retirementSchema';

export const useRetirementForm = (initialData?: Partial<RetirementFormData>) => {
  const [state, setState] = useState<RetirementFormState>({
    data: {
      currentAge: initialData?.currentAge ?? 30,
      retirementAge: initialData?.retirementAge ?? 65,
      lifeExpectancy: initialData?.lifeExpectancy ?? 85,
      currentIncome: initialData?.currentIncome ?? 80000,
      currentSavings: initialData?.currentSavings ?? 100000,
      monthlyContribution: initialData?.monthlyContribution ?? 1000,
      expectedReturnRate: initialData?.expectedReturnRate ?? 6,
      desiredAnnualIncome: initialData?.desiredAnnualIncome ?? 60000,
      inflationRate: initialData?.inflationRate ?? 2.5,
      socialSecurityBenefit: initialData?.socialSecurityBenefit ?? 25000,
      pensionIncome: initialData?.pensionIncome ?? 0,
      otherIncome: initialData?.otherIncome ?? 0,
      retirementLocation: initialData?.retirementLocation ?? '',
      riskTolerance: initialData?.riskTolerance ?? 'moderate',
      investmentStrategy: initialData?.investmentStrategy ?? 'balanced',
      retirementLifestyle: initialData?.retirementLifestyle ?? 'comfortable',
      retirementActivities: initialData?.retirementActivities ?? [],
      priorityGoals: initialData?.priorityGoals ?? [],
    },
    errors: {} as Record<keyof RetirementFormData, string>,
    isSubmitting: false,
    retirementPlan: null,
  });

  // Generate retirement plan when form data changes
  useEffect(() => {
    const calculateRetirement = async () => {
      try {
        await retirementFormSchema.validate(state.data, { abortEarly: false });

        const plan = generateRetirementPlan({
          currentAge: state.data.currentAge,
          retirementAge: state.data.retirementAge,
          lifeExpectancy: state.data.lifeExpectancy,
          currentIncome: state.data.currentIncome,
          currentSavings: state.data.currentSavings,
          monthlyContribution: state.data.monthlyContribution,
          expectedReturnRate: state.data.expectedReturnRate / 100, // Convert to decimal
          inflationRate: state.data.inflationRate / 100,
          socialSecurityEarnings: Array(35).fill(state.data.currentIncome),
          filingStatus: 'single', // Can be made configurable
        });

        setState((prev) => ({
          ...prev,
          retirementPlan: plan,
        }));
      } catch (error) {
        // Validation errors are handled by the form
        if ((error as Error).name !== 'ValidationError') {
          console.error('Error calculating retirement plan:', error);
        }
      }
    };

    calculateRetirement();
  }, [state.data]);

  // Update form field
  const updateField = useCallback((field: keyof RetirementFormData, value: any) => {
    setState((prev) => ({
      ...prev,
      data: {
        ...prev.data,
        [field]: value,
      },
      errors: {
        ...prev.errors,
        [field]: '',
      },
    }));
  }, []);

  // Validate form
  const validateForm = useCallback(async () => {
    try {
      await retirementFormSchema.validate(state.data, { abortEarly: false });
      setState((prev) => ({
        ...prev,
        errors: {} as Record<keyof RetirementFormData, string>,
      }));
      return true;
    } catch (error: any) {
      if (error.name === 'ValidationError') {
        const validationErrors = {} as Record<keyof RetirementFormData, string>;
        error.inner.forEach((err: any) => {
          if (err.path) {
            validationErrors[err.path as keyof RetirementFormData] = err.message;
          }
        });
        setState((prev) => ({
          ...prev,
          errors: validationErrors,
        }));
      }
      return false;
    }
  }, [state.data]);

  // Handle form submission
  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();
      const isValid = await validateForm();

      if (isValid) {
        setState((prev) => ({ ...prev, isSubmitting: true }));
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1000));
        setState((prev) => ({ ...prev, isSubmitting: false }));
        return true;
      }
      return false;
    },
    [validateForm]
  );

  return {
    ...state,
    updateField,
    validateForm,
    handleSubmit,
  };
};
