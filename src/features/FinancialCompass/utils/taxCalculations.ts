// Federal tax brackets for 2023 (single filers)
const FEDERAL_TAX_BRACKETS = [
  { min: 0, max: 10275, rate: 0.1 },
  { min: 10276, max: 41775, rate: 0.12 },
  { min: 41776, max: 89075, rate: 0.22 },
  { min: 89076, max: 170050, rate: 0.24 },
  { min: 170051, max: 215950, rate: 0.32 },
  { min: 215951, max: 539900, rate: 0.35 },
  { min: 539901, max: Infinity, rate: 0.37 },
];

// State tax rates (simplified for example)
const STATE_TAX_RATES: Record<string, number> = {
  CA: 0.093,
  NY: 0.0882,
  TX: 0,
  FL: 0,
  IL: 0.0495,
  PA: 0.0307,
  OH: 0.0297,
  GA: 0.0575,
  NC: 0.0525,
  MI: 0.0425,
};

// FICA tax rates (Social Security + Medicare)
const SOCIAL_SECURITY_RATE = 0.062; // 6.2% for Social Security
const MEDICARE_RATE = 0.0145; // 1.45% for Medicare
const SOCIAL_SECURITY_WAGE_BASE = 160200; // 2023 wage base limit

export const calculateFederalTax = (taxableIncome: number): number => {
  if (taxableIncome <= 0) return 0;

  let tax = 0;
  let previousBracketMax = 0;

  for (const bracket of FEDERAL_TAX_BRACKETS) {
    if (taxableIncome <= previousBracketMax) break;

    const taxableAmount = Math.min(
      taxableIncome - previousBracketMax,
      bracket.max - previousBracketMax
    );

    tax += taxableAmount * bracket.rate;
    previousBracketMax = bracket.max;
  }

  return Math.round(tax * 100) / 100; // Round to nearest cent
};

export const calculateStateTax = (taxableIncome: number, state: string): number => {
  if (!state || !STATE_TAX_RATES[state] || taxableIncome <= 0) return 0;
  return Math.round(taxableIncome * STATE_TAX_RATES[state] * 100) / 100;
};

export const calculateFICATax = (income: number): number => {
  if (income <= 0) return 0;

  // Social Security tax (capped at wage base)
  const socialSecurityTax = Math.min(income, SOCIAL_SECURITY_WAGE_BASE) * SOCIAL_SECURITY_RATE;

  // Medicare tax (no cap)
  const medicareTax = income * MEDICARE_RATE;

  // Additional Medicare tax for high earners (not implemented here)

  return Math.round((socialSecurityTax + medicareTax) * 100) / 100;
};

export const calculateTaxLiability = (
  income: number,
  state: string,
  filingStatus: string,
  preTaxDeductions: number = 0,
  taxCredits: number = 0
) => {
  const taxableIncome = Math.max(0, income - (preTaxDeductions || 0));

  const federal = calculateFederalTax(taxableIncome);
  const stateTax = calculateStateTax(taxableIncome, state);
  const fica = calculateFICATax(income); // FICA is on gross income, not taxable income

  // Apply tax credits (simplified)
  const totalTax = Math.max(0, federal + stateTax + fica - (taxCredits || 0));

  return {
    federal,
    state: stateTax,
    fica,
    total: totalTax,
    effectiveRate: income > 0 ? totalTax / income : 0,
  };
};
