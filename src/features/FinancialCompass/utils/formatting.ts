/**
 * Format a number as currency
 * @param amount - The amount to format
 * @param decimals - Number of decimal places (default: 2)
 * @returns Formatted currency string
 */
export const formatCurrency = (amount: number, decimals: number = 2): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: decimals,
  }).format(amount);
};

/**
 * Parse a currency string into a number
 * @param value - The currency string to parse
 * @returns Parsed number or 0 if invalid
 */
export const parseCurrencyInput = (value: string): number => {
  // Remove all non-numeric characters except decimal point
  const numericValue = value.replace(/[^0-9.]/g, '');
  const num = parseFloat(numericValue);
  return isNaN(num) ? 0 : num;
};

/**
 * Format a number as a percentage
 * @param value - The decimal value to format (e.g., 0.15 for 15%)
 * @param decimals - Number of decimal places (default: 1)
 * @returns Formatted percentage string
 */
export const formatPercentage = (value: number, decimals: number = 1): string => {
  return (value * 100).toFixed(decimals) + '%';
};

/**
 * Format a large number with K, M, B suffixes
 * @param num - The number to format
 * @param decimals - Number of decimal places (default: 1)
 * @returns Formatted string (e.g., 1.5K, 2.3M)
 */
export const formatLargeNumber = (num: number, decimals: number = 1): string => {
  if (num >= 1000000000) {
    return (num / 1000000000).toFixed(decimals) + 'B';
  }
  if (num >= 1000000) {
    return (num / 1000000).toFixed(decimals) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(decimals) + 'K';
  }
  return num.toString();
};

/**
 * Format a date string to a more readable format
 * @param dateString - ISO date string
 * @param format - Date format (default: 'MM/dd/yyyy')
 * @returns Formatted date string
 */
export const formatDate = (dateString: string, format: string = 'MM/dd/yyyy'): string => {
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';

    const pad = (num: number) => num.toString().padStart(2, '0');

    return format
      .replace('yyyy', date.getFullYear().toString())
      .replace('MM', pad(date.getMonth() + 1))
      .replace('dd', pad(date.getDate()))
      .replace('HH', pad(date.getHours()))
      .replace('mm', pad(date.getMinutes()));
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
};
