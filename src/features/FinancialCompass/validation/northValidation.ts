import { z } from 'zod';
import {
  dateSchema,
  currencySchema,
  percentageSchema,
  validationMessages,
} from '../../schemas/base';
import { ValidationUtils } from '../../utils/validationUtils';

// Personal Information Schema
export const personalInfoSchema = z.object({
  firstName: z.string().min(1, validationMessages.required),
  lastName: z.string().min(1, validationMessages.required),
  dateOfBirth: dateSchema.refine(
    (date) => {
      const today = new Date();
      const birthDate = new Date(date);
      const age = today.getFullYear() - birthDate.getFullYear();
      return age >= 18;
    },
    { message: 'You must be at least 18 years old' }
  ),
  email: z.string().email(validationMessages.invalidEmail),
  phone: z.string().min(10, validationMessages.invalidPhone),
  address: z.object({
    street: z.string().min(1, validationMessages.required),
    city: z.string().min(1, validationMessages.required),
    state: z.string().min(2, 'Please enter a valid state code'),
    zipCode: z.string().regex(/^\d{5}(-\d{4})?$/, validationMessages.invalidZipCode),
  }),
  maritalStatus: z.enum(['single', 'married', 'divorced', 'widowed', 'separated']),
  dependents: z.number().int().min(0).default(0),
});

// Income Schema
export const incomeSchema = z.object({
  primaryIncome: currencySchema.min(0, 'Income cannot be negative'),
  primaryIncomeType: z.enum([
    'salary',
    'hourly',
    'self-employed',
    'pension',
    'social-security',
    'other',
  ]),
  primaryIncomeFrequency: z.enum(['weekly', 'bi-weekly', 'monthly', 'annually']),
  secondaryIncome: currencySchema.optional(),
  secondaryIncomeType: z.enum(['part-time', 'rental', 'investments', 'other']).optional(),
  secondaryIncomeFrequency: z.enum(['weekly', 'bi-weekly', 'monthly', 'annually']).optional(),
  otherIncome: currencySchema.optional(),
  otherIncomeDescription: z.string().optional(),
  taxRate: percentageSchema.optional(),
  taxFilingStatus: z
    .enum([
      'single',
      'married-filing-jointly',
      'married-filing-separately',
      'head-of-household',
      'qualifying-widow(er)',
    ])
    .optional(),
});

// Expense Schema
export const expenseSchema = z.object({
  housing: currencySchema.min(0, 'Amount cannot be negative'),
  utilities: currencySchema.min(0, 'Amount cannot be negative'),
  food: currencySchema.min(0, 'Amount cannot be negative'),
  transportation: currencySchema.min(0, 'Amount cannot be negative'),
  healthcare: currencySchema.min(0, 'Amount cannot be negative'),
  insurance: currencySchema.min(0, 'Amount cannot be negative'),
  debtPayments: currencySchema.min(0, 'Amount cannot be negative'),
  entertainment: currencySchema.min(0, 'Amount cannot be negative'),
  education: currencySchema.min(0, 'Amount cannot be negative'),
  savings: currencySchema.min(0, 'Amount cannot be negative'),
  other: currencySchema.min(0, 'Amount cannot be negative'),
  frequency: z.enum(['weekly', 'bi-weekly', 'monthly', 'annually']).default('monthly'),
});

// Asset Schema
export const assetSchema = z.object({
  cash: currencySchema.min(0, 'Amount cannot be negative'),
  checkingAccounts: currencySchema.min(0, 'Amount cannot be negative'),
  savingsAccounts: currencySchema.min(0, 'Amount cannot be negative'),
  investmentAccounts: currencySchema.min(0, 'Amount cannot be negative'),
  retirementAccounts: currencySchema.min(0, 'Amount cannot be negative'),
  realEstate: currencySchema.min(0, 'Amount cannot be negative'),
  vehicles: currencySchema.min(0, 'Amount cannot be negative'),
  otherAssets: currencySchema.min(0, 'Amount cannot be negative'),
  otherAssetsDescription: z.string().optional(),
});

// Liability Schema
export const liabilitySchema = z.object({
  creditCards: currencySchema.min(0, 'Amount cannot be negative'),
  studentLoans: currencySchema.min(0, 'Amount cannot be negative'),
  mortgages: currencySchema.min(0, 'Amount cannot be negative'),
  carLoans: currencySchema.min(0, 'Amount cannot be negative'),
  personalLoans: currencySchema.min(0, 'Amount cannot be negative'),
  medicalDebt: currencySchema.min(0, 'Amount cannot be negative'),
  otherDebt: currencySchema.min(0, 'Amount cannot be negative'),
  otherDebtDescription: z.string().optional(),
});

// North Direction Schema
export const northDirectionSchema = z.object({
  personalInfo: personalInfoSchema,
  income: incomeSchema,
  expenses: expenseSchema,
  assets: assetSchema,
  liabilities: liabilitySchema,
  lastUpdated: dateSchema.optional(),
});

// Type exports
export type PersonalInfo = z.infer<typeof personalInfoSchema>;
export type Income = z.infer<typeof incomeSchema>;
export type Expense = z.infer<typeof expenseSchema>;
export type Asset = z.infer<typeof assetSchema>;
export type Liability = z.infer<typeof liabilitySchema>;
export type NorthDirectionData = z.infer<typeof northDirectionSchema>;

// Validation hooks
export const useNorthValidation = () => {
  const validatePersonalInfo = (data: Partial<PersonalInfo>) => {
    return ValidationUtils.validateFormData(data, personalInfoSchema.partial());
  };

  const validateIncome = (data: Partial<Income>) => {
    return ValidationUtils.validateFormData(data, incomeSchema.partial());
  };

  const validateExpenses = (data: Partial<Expense>) => {
    return ValidationUtils.validateFormData(data, expenseSchema.partial());
  };

  const validateAssets = (data: Partial<Asset>) => {
    return ValidationUtils.validateFormData(data, assetSchema.partial());
  };

  const validateLiabilities = (data: Partial<Liability>) => {
    return ValidationUtils.validateFormData(data, liabilitySchema.partial());
  };

  const validateNorthForm = (data: Partial<NorthDirectionData>) => {
    return ValidationUtils.validateFormData(data, northDirectionSchema.partial());
  };

  // Field-level validation functions
  const validateField = <T>(
    section: keyof NorthDirectionData,
    field: string,
    value: any,
    schema: z.ZodSchema<T>
  ) => {
    try {
      schema.parse(value);
      return '';
    } catch (error) {
      if (error instanceof z.ZodError) {
        return error.errors[0]?.message || 'Invalid value';
      }
      return 'Validation error';
    }
  };

  // Helper function to get field schema
  const getFieldSchema = (section: string, field: string) => {
    const schemas: Record<string, any> = {
      personalInfo: personalInfoSchema,
      income: incomeSchema,
      expenses: expenseSchema,
      assets: assetSchema,
      liabilities: liabilitySchema,
    };

    const schema = schemas[section];
    if (!schema) return null;

    return schema.shape[field as keyof typeof schema.shape];
  };

  return {
    validatePersonalInfo,
    validateIncome,
    validateExpenses,
    validateAssets,
    validateLiabilities,
    validateNorthForm,
    validateField,
    getFieldSchema,
    schemas: {
      personalInfo: personalInfoSchema,
      income: incomeSchema,
      expenses: expenseSchema,
      assets: assetSchema,
      liabilities: liabilitySchema,
      north: northDirectionSchema,
    },
  };
};

// Custom validation rules
export const northValidationRules = {
  required: (value: any) => (value ? undefined : 'This field is required'),
  minValue: (min: number) => (value: number) =>
    value >= min ? undefined : `Must be at least ${min}`,
  maxValue: (max: number) => (value: number) =>
    value <= max ? undefined : `Must be at most ${max}`,
  minLength: (min: number) => (value: string) =>
    value.length >= min ? undefined : `Must be at least ${min} characters`,
  maxLength: (max: number) => (value: string) =>
    value.length <= max ? undefined : `Must be at most ${max} characters`,
  email: (value: string) =>
    /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(value) ? undefined : 'Invalid email address',
  phone: (value: string) =>
    /^\+?[\d\s-()]{10,}$/.test(value) ? undefined : 'Invalid phone number',
  zipCode: (value: string) => (/^\d{5}(-\d{4})?$/.test(value) ? undefined : 'Invalid ZIP code'),
  ssn: (value: string) =>
    /^\d{3}-\d{2}-\d{4}$/.test(value) ? undefined : 'Invalid SSN format (XXX-XX-XXXX)',
  url: (value: string) =>
    /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([\/\w .-]*)*\/?$/.test(value)
      ? undefined
      : 'Invalid URL',
  match: (field: string) => (value: any, allValues: any) =>
    value === allValues[field] ? undefined : 'Values do not match',
  positive: (value: number) => (value > 0 ? undefined : 'Must be positive'),
  nonNegative: (value: number) => (value >= 0 ? undefined : 'Cannot be negative'),
  percentage: (value: number) =>
    value >= 0 && value <= 100 ? undefined : 'Must be between 0 and 100',
  dateInPast: (value: Date) => (value < new Date() ? undefined : 'Date must be in the past'),
  dateInFuture: (value: Date) => (value > new Date() ? undefined : 'Date must be in the future'),
};
