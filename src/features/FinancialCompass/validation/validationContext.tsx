import React, { createContext, useContext, useReducer, useCallback, useMemo } from 'react';
import { z, Zod<PERSON><PERSON><PERSON>, ZodSchema, ZodTypeAny } from 'zod';
import { ERROR_MESSAGES } from './baseSchemas';

// Type for objects with string keys
type StringKeyObject = { [key: string]: any };

// Type for validation errors
export type ValidationErrors<T> = Partial<Record<keyof T, string>>;

// Type for field validation result
type FieldValidationResult = {
  validate: () => boolean;
};

// Type for form field props
type FormFieldProps<T> = {
  value: T;
  onChange: (value: T) => void;
  onBlur: () => void;
  error?: string;
};

interface ValidationState<T extends Record<string, any>> {
  values: T;
  errors: ValidationErrors<T>;
  isDirty: boolean;
  isValid: boolean;
  isSubmitting: boolean;
  submitCount: number;
}

type ValidationAction<T extends Record<string, any>> =
  | { type: 'SET_FIELD_VALUE'; field: keyof T; value: T[keyof T] }
  | { type: 'SET_FIELD_ERROR'; field: keyof T; error: string | undefined }
  | { type: 'SET_ERRORS'; errors: ValidationErrors<T> }
  | { type: 'SET_IS_DIRTY'; isDirty: boolean }
  | { type: 'SET_IS_VALID'; isValid: boolean }
  | { type: 'SET_IS_SUBMITTING'; isSubmitting: boolean }
  | { type: 'INCREMENT_SUBMIT_COUNT' }
  | { type: 'RESET'; values: T };

function validationReducer<T extends Record<string, any>>(
  state: ValidationState<T>,
  action: ValidationAction<T>
): ValidationState<T> {
  switch (action.type) {
    case 'SET_FIELD_VALUE':
      return {
        ...state,
        values: {
          ...state.values,
          [action.field]: action.value,
        },
        isDirty: true,
      };
    case 'SET_FIELD_ERROR':
      return {
        ...state,
        errors: {
          ...state.errors,
          [action.field]: action.error,
        },
      };
    case 'SET_ERRORS':
      return {
        ...state,
        errors: action.errors,
      };
    case 'SET_IS_DIRTY':
      return {
        ...state,
        isDirty: action.isDirty,
      };
    case 'SET_IS_VALID':
      return {
        ...state,
        isValid: action.isValid,
      };
    case 'SET_IS_SUBMITTING':
      return {
        ...state,
        isSubmitting: action.isSubmitting,
      };
    case 'INCREMENT_SUBMIT_COUNT':
      return {
        ...state,
        submitCount: state.submitCount + 1,
      };
    case 'RESET':
      return {
        values: action.values,
        errors: {},
        isDirty: false,
        isValid: false,
        isSubmitting: false,
        submitCount: 0,
      };
    default:
      return state;
  }
}

// Type for objects with string keys
type StringKeyObject = { [key: string]: any };

interface ValidationContextValue<T extends StringKeyObject> {
  // State
  values: T;
  errors: ValidationErrors<T>;
  isDirty: boolean;
  isValid: boolean;
  isSubmitting: boolean;
  submitCount: number;

  // Methods
  setFieldValue: (field: string & keyof T, value: T[keyof T]) => void;
  setFieldError: (field: string & keyof T, error: string | undefined) => void;
  validateField: (field: string & keyof T, schema: ZodTypeAny) => boolean;
  validateForm: (schema: ZodTypeAny) => boolean;
  resetForm: (newValues?: Partial<T>) => void;
  handleSubmit: (
    onSubmit: (values: T) => Promise<void> | void
  ) => (e?: React.FormEvent) => Promise<void>;
  getFieldProps: (
    field: string & keyof T,
    schema: ZodTypeAny
  ) => {
    value: T[keyof T];
    onChange: (value: T[keyof T]) => void;
    onBlur: () => void;
    error: string | undefined;
  };
}

const ValidationContext = createContext<ValidationContextValue<any> | undefined>(undefined);

interface ValidationProviderProps<T> {
  initialValues: T;
  onSubmit?: (values: T) => Promise<void> | void;
  children: React.ReactNode;
  enableReinitialize?: boolean;
}

export function ValidationProvider<T extends StringKeyObject>({
  initialValues,
  onSubmit,
  children,
  enableReinitialize = false,
}: ValidationProviderProps<T>) {
  const [state, dispatch] = useReducer(
    validationReducer as React.Reducer<ValidationState<T>, ValidationAction<T>>,
    {
      values: initialValues,
      errors: {},
      isDirty: false,
      isValid: false,
      isSubmitting: false,
      submitCount: 0,
    } as ValidationState<T>
  );

  // Reset form when initialValues change and enableReinitialize is true
  React.useEffect(() => {
    if (enableReinitialize) {
      dispatch({ type: 'RESET', values: initialValues });
    }
  }, [initialValues, enableReinitialize]);

  const setFieldValue = useCallback(<K extends keyof T>(field: K, value: T[K]) => {
    dispatch({
      type: 'SET_FIELD_VALUE',
      field: field as keyof T,
      value: value as T[keyof T],
    });
  }, []);

  const setFieldError = useCallback(<K extends keyof T>(field: K, error: string | undefined) => {
    dispatch({
      type: 'SET_FIELD_ERROR',
      field: field as keyof T,
      error,
    });
  }, []);

  const validateField = useCallback(
    <K extends keyof T>(field: K, schema: ZodTypeAny): boolean => {
      try {
        schema.parse(state.values[field]);
        setFieldError(field, undefined);
        return true;
      } catch (error) {
        if (error instanceof z.ZodError) {
          setFieldError(field, error.errors[0]?.message || ERROR_MESSAGES.REQUIRED);
        } else {
          setFieldError(field, 'Invalid value');
        }
        return false;
      }
    },
    [state.values, setFieldError]
  );

  const validateForm = useCallback(
    (schema: ZodSchema<any>): boolean => {
      try {
        schema.parse(state.values);
        dispatch({ type: 'SET_ERRORS', errors: {} });
        return true;
      } catch (error) {
        if (error instanceof z.ZodError) {
          const errors = error.errors.reduce<ValidationErrors<T>>((acc, curr) => {
            const field = curr.path[0] as keyof T;
            if (field) {
              acc[field] = curr.message;
            }
            return acc;
          }, {} as ValidationErrors<T>);
          dispatch({ type: 'SET_ERRORS', errors });
        }
        return false;
      }
    },
    [state.values]
  );

  const resetForm = useCallback(
    (newValues: Partial<T> = {}) => {
      const resetValues = { ...initialValues, ...newValues } as T;
      dispatch({ type: 'RESET', values: resetValues });
    },
    [initialValues]
  );

  const handleSubmit = useCallback(
    (onSubmit: (values: T) => Promise<void> | void) => {
      return async (e?: React.FormEvent) => {
        if (e) {
          e.preventDefault();
        }

        dispatch({ type: 'SET_IS_SUBMITTING', isSubmitting: true });
        dispatch({ type: 'INCREMENT_SUBMIT_COUNT' });

        try {
          await onSubmit(state.values as T);
          dispatch({ type: 'SET_IS_DIRTY', isDirty: false });
        } catch (error) {
          console.error('Form submission error:', error);
        } finally {
          dispatch({ type: 'SET_IS_SUBMITTING', isSubmitting: false });
        }
      };
    },
    [state.values]
  );

  const getFieldProps = useCallback(
    <K extends keyof T>(field: K, schema: ZodTypeAny) => ({
      value: state.values[field] as T[K],
      onChange: (value: T[K]) => setFieldValue(field, value),
      onBlur: () => validateField(field, schema),
      error: state.errors[field],
    }),
    [state.values, state.errors, setFieldValue, validateField]
  );

  const contextValue = useMemo(
    () => ({
      ...state,
      setFieldValue,
      setFieldError,
      validateField,
      validateForm,
      resetForm,
      handleSubmit,
      getFieldProps,
    }),
    [
      state,
      setFieldValue,
      setFieldError,
      validateField,
      validateForm,
      resetForm,
      handleSubmit,
      getFieldProps,
    ]
  );

  return <ValidationContext.Provider value={contextValue}>{children}</ValidationContext.Provider>;
}

// Custom hook to use the validation context
export function useValidation<T extends object>(): ValidationContextValue<T> {
  const context = useContext(
    ValidationContext as React.Context<ValidationContextValue<T> | undefined>
  );
  if (context === undefined) {
    throw new Error('useValidation must be used within a ValidationProvider');
  }
  return context as unknown as ValidationContextValue<T>;
}

// Helper hook for field-level validation
export function useFieldValidation<T>(
  field: string & keyof T,
  schema: ZodTypeAny
): FieldValidationResult {
  const { validateField } = useValidation<T>();
  const validate = useCallback((): boolean => {
    return validateField(field, schema);
  }, [field, schema, validateField]);

  return { validate };
}

// Utility function to create form field props
export function useFormField<T>(field: string & keyof T, schema: ZodTypeAny): FormFieldProps<any> {
  const { values, errors, setFieldValue, validateField } = useValidation<T>();

  return {
    value: values[field],
    onChange: (value: any) => setFieldValue(field, value),
    onBlur: () => validateField(field, schema),
    error: errors[field],
  };
}
