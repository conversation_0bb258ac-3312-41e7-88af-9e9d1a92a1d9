import { validationRules } from '../../../utils/formValidation';

type ValidationRule = {
  required?: boolean;
  message?: string | Record<string, any>;
  pattern?: RegExp;
  min?: number;
  max?: number;
  minLength?: number;
  maxLength?: number;
  validate?: (value: any, formData?: any) => string | null | Promise<string | null>;
};

export interface IncomeValidationSchema {
  [key: string]: ValidationRule;
}

export const incomeValidationSchema: IncomeValidationSchema = {
  primaryIncome: {
    required: true,
    message: 'Primary income is required',
    validate: (value: string | number) => {
      const numValue = Number(value);
      if (isNaN(numValue)) return 'Please enter a valid number';
      if (numValue < 0) return 'Income cannot be negative';
      if (numValue > 10000000) return 'Income seems unusually high. Please verify.';
      return null;
    },
  },
  primaryIncomeType: {
    required: true,
    message: 'Income type is required',
  },
  primaryIncomeFrequency: {
    required: true,
    message: 'Income frequency is required',
  },
  taxRate: {
    required: true,
    message: 'Tax rate is required',
    validate: (value: string | number, formData: Record<string, any>) => {
      const numValue = Number(value);
      if (isNaN(numValue)) return 'Please enter a valid number';
      if (numValue < 0) return 'Tax rate cannot be negative';
      if (numValue > 100) return 'Tax rate cannot exceed 100%';
      return null;
    },
  },
  // Add validation for other fields
};

// Cross-field validation function
export const validateIncomeForm = (formData: any): Record<string, string> => {
  const errors: Record<string, string> = {};

  // Example cross-field validation
  if (formData.primaryIncome && formData.taxRate) {
    const income = Number(formData.primaryIncome);
    const taxRate = Number(formData.taxRate);
    const taxAmount = (income * taxRate) / 100;

    if (taxAmount > 0 && taxAmount > income * 0.5) {
      errors.taxRate = 'Tax amount seems unusually high compared to income';
    }
  }

  return errors;
};
