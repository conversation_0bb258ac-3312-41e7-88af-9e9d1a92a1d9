import React from 'react';
import { useFieldValidation } from './validationContext';
import {
  FormControl,
  FormLabel,
  FormErrorMessage,
  Input,
  Select,
  Textarea,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
} from '@chakra-ui/react';
import { CurrencyInput } from '../../components/CurrencyInput';
import { PercentageInput } from '../../components/PercentageInput';

interface BaseFieldProps<T, K extends keyof T> {
  name: K;
  label: string;
  placeholder?: string;
  isRequired?: boolean;
  schema: any; // ZodSchema type causes issues with generics
  children?: React.ReactNode;
  helperText?: string;
  isDisabled?: boolean;
}

export function FormInput<T extends object, K extends keyof T>({
  name,
  label,
  placeholder,
  isRequired = false,
  schema,
  helperText,
  isDisabled = false,
  ...props
}: BaseFieldProps<T, K> & React.InputHTMLAttributes<HTMLInputElement>) {
  const { value, error, onChange, onBlur } = useFieldValidation<T, K>(name, schema);

  return (
    <FormControl isInvalid={!!error} isRequired={isRequired} mb={4}>
      <FormLabel htmlFor={name as string}>{label}</FormLabel>
      <Input
        id={name as string}
        value={(value as any) || ''}
        onChange={(e) => onChange(e.target.value as any)}
        onBlur={onBlur}
        placeholder={placeholder}
        isDisabled={isDisabled}
        {...props}
      />
      {helperText && !error && <FormHelperText>{helperText}</FormHelperText>}
      <FormErrorMessage>{error}</FormErrorMessage>
    </FormControl>
  );
}

export function FormTextarea<T extends object, K extends keyof T>({
  name,
  label,
  placeholder,
  isRequired = false,
  schema,
  helperText,
  isDisabled = false,
  ...props
}: BaseFieldProps<T, K> & React.TextareaHTMLAttributes<HTMLTextAreaElement>) {
  const { value, error, onChange, onBlur } = useFieldValidation<T, K>(name, schema);

  return (
    <FormControl isInvalid={!!error} isRequired={isRequired} mb={4}>
      <FormLabel htmlFor={name as string}>{label}</FormLabel>
      <Textarea
        id={name as string}
        value={(value as any) || ''}
        onChange={(e) => onChange(e.target.value as any)}
        onBlur={onBlur}
        placeholder={placeholder}
        isDisabled={isDisabled}
        {...props}
      />
      {helperText && !error && <FormHelperText>{helperText}</FormHelperText>}
      <FormErrorMessage>{error}</FormErrorMessage>
    </FormControl>
  );
}

export function FormNumberInput<T extends object, K extends keyof T>({
  name,
  label,
  placeholder,
  isRequired = false,
  schema,
  helperText,
  isDisabled = false,
  min,
  max,
  precision = 0,
  ...props
}: BaseFieldProps<T, K> & { min?: number; max?: number; precision?: number } & Omit<
    React.InputHTMLAttributes<HTMLInputElement>,
    'min' | 'max'
  >) {
  const { value, error, onChange, onBlur } = useFieldValidation<T, K>(name, schema);

  return (
    <FormControl isInvalid={!!error} isRequired={isRequired} mb={4}>
      <FormLabel htmlFor={name as string}>{label}</FormLabel>
      <NumberInput
        value={(value as any) || ''}
        onChange={(_, valueAsNumber) => onChange(valueAsNumber as any)}
        onBlur={onBlur}
        min={min}
        max={max}
        precision={precision}
        isDisabled={isDisabled}
      >
        <NumberInputField id={name as string} placeholder={placeholder} />
        <NumberInputStepper>
          <NumberIncrementStepper />
          <NumberDecrementStepper />
        </NumberInputStepper>
      </NumberInput>
      {helperText && !error && <FormHelperText>{helperText}</FormHelperText>}
      <FormErrorMessage>{error}</FormErrorMessage>
    </FormControl>
  );
}

export function FormCurrencyInput<T extends object, K extends keyof T>({
  name,
  label,
  placeholder,
  isRequired = false,
  schema,
  helperText,
  isDisabled = false,
  ...props
}: BaseFieldProps<T, K> & Omit<React.InputHTMLAttributes<HTMLInputElement>, 'value' | 'onChange'>) {
  const { value, error, onChange, onBlur } = useFieldValidation<T, K>(name, schema);

  return (
    <FormControl isInvalid={!!error} isRequired={isRequired} mb={4}>
      <FormLabel htmlFor={name as string}>{label}</FormLabel>
      <CurrencyInput
        id={name as string}
        value={(value as any) || 0}
        onChange={(value) => onChange(value as any)}
        onBlur={onBlur}
        placeholder={placeholder}
        isDisabled={isDisabled}
        {...props}
      />
      {helperText && !error && <FormHelperText>{helperText}</FormHelperText>}
      <FormErrorMessage>{error}</FormErrorMessage>
    </FormControl>
  );
}

export function FormPercentageInput<T extends object, K extends keyof T>({
  name,
  label,
  placeholder,
  isRequired = false,
  schema,
  helperText,
  isDisabled = false,
  ...props
}: BaseFieldProps<T, K> & Omit<React.InputHTMLAttributes<HTMLInputElement>, 'value' | 'onChange'>) {
  const { value, error, onChange, onBlur } = useFieldValidation<T, K>(name, schema);

  return (
    <FormControl isInvalid={!!error} isRequired={isRequired} mb={4}>
      <FormLabel htmlFor={name as string}>{label}</FormLabel>
      <PercentageInput
        id={name as string}
        value={(value as any) || 0}
        onChange={(value) => onChange(value as any)}
        onBlur={onBlur}
        placeholder={placeholder}
        isDisabled={isDisabled}
        {...props}
      />
      {helperText && !error && <FormHelperText>{helperText}</FormHelperText>}
      <FormErrorMessage>{error}</FormErrorMessage>
    </FormControl>
  );
}

export function FormSelect<T extends object, K extends keyof T>({
  name,
  label,
  placeholder,
  isRequired = false,
  schema,
  helperText,
  isDisabled = false,
  children,
  ...props
}: BaseFieldProps<T, K> & React.SelectHTMLAttributes<HTMLSelectElement>) {
  const { value, error, onChange, onBlur } = useFieldValidation<T, K>(name, schema);

  return (
    <FormControl isInvalid={!!error} isRequired={isRequired} mb={4}>
      <FormLabel htmlFor={name as string}>{label}</FormLabel>
      <Select
        id={name as string}
        value={(value as any) || ''}
        onChange={(e) => onChange(e.target.value as any)}
        onBlur={onBlur}
        placeholder={placeholder}
        isDisabled={isDisabled}
        {...props}
      >
        {children}
      </Select>
      {helperText && !error && <FormHelperText>{helperText}</FormHelperText>}
      <FormErrorMessage>{error}</FormErrorMessage>
    </FormControl>
  );
}

// Helper component for form submission state
export function FormActions({
  isSubmitting,
  onCancel,
  submitLabel = 'Save',
  cancelLabel = 'Cancel',
  isDisabled = false,
}: {
  isSubmitting: boolean;
  onCancel?: () => void;
  submitLabel?: string;
  cancelLabel?: string;
  isDisabled?: boolean;
}) {
  return (
    <div className="flex justify-end space-x-3 mt-6">
      {onCancel && (
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          isDisabled={isSubmitting || isDisabled}
        >
          {cancelLabel}
        </Button>
      )}
      <Button
        type="submit"
        colorScheme="blue"
        isLoading={isSubmitting}
        loadingText="Saving..."
        isDisabled={isDisabled}
      >
        {submitLabel}
      </Button>
    </div>
  );
}

// Helper component for form error summary
export function FormErrorSummary({
  errors,
  title = 'Please fix the following errors',
  className = '',
}: {
  errors: Record<string, string>;
  title?: string;
  className?: string;
}) {
  const errorCount = Object.keys(errors).length;

  if (errorCount === 0) return null;

  return (
    <div className={`bg-red-50 border-l-4 border-red-400 p-4 mb-6 ${className}`}>
      <div className="flex">
        <div className="flex-shrink-0">
          <ExclamationCircleIcon className="h-5 w-5 text-red-400" aria-hidden="true" />
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-red-800">
            {title} ({errorCount} {errorCount === 1 ? 'error' : 'errors'})
          </h3>
          <div className="mt-2 text-sm text-red-700">
            <ul className="list-disc pl-5 space-y-1">
              {Object.entries(errors).map(([field, message]) => (
                <li key={field}>
                  <span className="font-medium">{field}:</span> {message}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

// Helper component for form section
export function FormSection({
  title,
  description,
  children,
  className = '',
}: {
  title: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <div className={`bg-white shadow overflow-hidden sm:rounded-lg mb-8 ${className}`}>
      <div className="px-4 py-5 sm:px-6 border-b border-gray-200">
        <h3 className="text-lg leading-6 font-medium text-gray-900">{title}</h3>
        {description && <p className="mt-1 max-w-2xl text-sm text-gray-500">{description}</p>}
      </div>
      <div className="px-4 py-5 sm:p-6">
        <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">{children}</div>
      </div>
    </div>
  );
}
