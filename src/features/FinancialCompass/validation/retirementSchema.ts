import * as yup from 'yup';
import { RetirementFormData } from '../types/retirementTypes';

export const retirementFormSchema = yup.object().shape<Record<keyof RetirementFormData, any>>({
  currentAge: yup
    .number()
    .required('Current age is required')
    .min(18, 'You must be at least 18 years old')
    .max(100, 'Please enter a valid age'),

  retirementAge: yup
    .number()
    .required('Retirement age is required')
    .min(yup.ref('currentAge'), 'Retirement age must be greater than current age')
    .max(100, 'Please enter a valid retirement age'),

  lifeExpectancy: yup
    .number()
    .required('Life expectancy is required')
    .min(yup.ref('retirementAge'), 'Life expectancy must be greater than retirement age')
    .max(120, 'Please enter a valid life expectancy'),

  currentIncome: yup
    .number()
    .required('Current income is required')
    .min(0, 'Income cannot be negative'),

  currentSavings: yup
    .number()
    .required('Current savings is required')
    .min(0, 'Savings cannot be negative'),

  monthlyContribution: yup
    .number()
    .required('Monthly contribution is required')
    .min(0, 'Contribution cannot be negative'),

  expectedReturnRate: yup
    .number()
    .required('Expected return rate is required')
    .min(0, 'Rate cannot be negative')
    .max(20, 'Rate seems too high'),

  desiredAnnualIncome: yup
    .number()
    .required('Desired annual income is required')
    .min(0, 'Income cannot be negative'),

  inflationRate: yup
    .number()
    .required('Inflation rate is required')
    .min(0, 'Rate cannot be negative')
    .max(10, 'Rate seems too high'),

  socialSecurityBenefit: yup.number().min(0, 'Amount cannot be negative').default(0),

  pensionIncome: yup.number().min(0, 'Amount cannot be negative').default(0),

  otherIncome: yup.number().min(0, 'Amount cannot be negative').default(0),

  retirementLocation: yup.string().default(''),

  riskTolerance: yup
    .mixed()
    .oneOf(['conservative', 'moderate', 'aggressive'], 'Please select a valid risk tolerance')
    .required('Risk tolerance is required'),

  investmentStrategy: yup
    .mixed()
    .oneOf(['preservation', 'balanced', 'growth'], 'Please select a valid investment strategy')
    .required('Investment strategy is required'),

  retirementLifestyle: yup
    .mixed()
    .oneOf(
      ['minimal', 'modest', 'comfortable', 'luxurious'],
      'Please select a valid lifestyle option'
    )
    .required('Retirement lifestyle is required'),

  retirementActivities: yup.array().of(yup.string()).default([]),

  priorityGoals: yup
    .array()
    .of(yup.string())
    .min(1, 'Please select at least one priority goal')
    .required('Priority goals are required'),
});

export const defaultRetirementFormValues: RetirementFormData = {
  currentAge: 30,
  retirementAge: 65,
  lifeExpectancy: 85,
  currentIncome: 80000,
  currentSavings: 100000,
  monthlyContribution: 1000,
  expectedReturnRate: 6,
  desiredAnnualIncome: 60000,
  inflationRate: 2.5,
  socialSecurityBenefit: 25000,
  pensionIncome: 0,
  otherIncome: 0,
  retirementLocation: '',
  riskTolerance: 'moderate',
  investmentStrategy: 'balanced',
  retirementLifestyle: 'comfortable',
  retirementActivities: [],
  priorityGoals: [],
};
