import { z } from 'zod';
import { currencySchema, percentageSchema, validationMessages } from '../../../schemas/base';

// Retirement Goals Schema
export const retirementGoalsSchema = z
  .object({
    retirementAge: z.preprocess(
      (val) => Number(val),
      z
        .number()
        .min(18, 'Retirement age must be at least 18')
        .max(100, 'Retirement age must be 100 or less')
    ),
    currentAge: z.preprocess(
      (val) => Number(val),
      z
        .number()
        .min(18, 'Current age must be at least 18')
        .max(100, 'Current age must be 100 or less')
    ),
    lifeExpectancy: z.preprocess(
      (val) => Number(val),
      z
        .number()
        .min(65, 'Life expectancy must be at least 65')
        .max(120, 'Life expectancy must be 120 or less')
    ),
    savingsGoal: currencySchema.min(0, 'Savings goal must be a positive number'),
    desiredAnnualIncome: currencySchema.min(0, 'Desired annual income must be a positive number'),
    retirementLifestyle: z.enum(['minimal', 'modest', 'comfortable', 'luxurious']),
    retirementLocation: z.string().min(1, 'Retirement location is required'),
    retirementActivities: z.array(z.string()).min(1, 'Select at least one retirement activity'),
    priorityGoals: z.array(z.string()).min(1, 'Select at least one priority goal'),
  })
  .refine((data) => Number(data.currentAge) < Number(data.retirementAge), {
    message: 'Retirement age must be greater than current age',
    path: ['retirementAge'],
  })
  .refine((data) => Number(data.retirementAge) < Number(data.lifeExpectancy), {
    message: 'Life expectancy must be greater than retirement age',
    path: ['lifeExpectancy'],
  });

// Type exports
export type RetirementGoalsFormData = z.infer<typeof retirementGoalsSchema>;

// Default values for the form
export const defaultRetirementGoals: RetirementGoalsFormData = {
  retirementAge: 65,
  currentAge: 30,
  lifeExpectancy: 90,
  savingsGoal: '1000000',
  desiredAnnualIncome: '80000',
  retirementLifestyle: 'comfortable',
  retirementLocation: '',
  retirementActivities: [],
  priorityGoals: [],
};
