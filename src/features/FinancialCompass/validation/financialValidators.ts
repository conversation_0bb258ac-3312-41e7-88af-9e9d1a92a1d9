import { z } from 'zod';
import { currencySchema, percentageSchema } from './baseSchemas';

/**
 * Validates that income is greater than expenses
 */
export const validateIncomeGreaterThanExpenses = (income: number, expenses: number) => {
  return income >= expenses || 'Income must be greater than or equal to expenses';
};

/**
 * Validates debt-to-income ratio
 */
export const validateDebtToIncomeRatio = (debt: number, income: number, maxRatio = 0.43) => {
  if (income <= 0) return 'Income must be greater than zero';
  const ratio = debt / income;
  return (
    ratio <= maxRatio ||
    `Debt-to-income ratio (${(ratio * 100).toFixed(1)}%) exceeds maximum allowed (${maxRatio * 100}%)`
  );
};

/**
 * Validates loan-to-value ratio
 */
export const validateLoanToValue = (loanAmount: number, assetValue: number, maxRatio = 0.8) => {
  if (assetValue <= 0) return 'Asset value must be greater than zero';
  const ratio = loanAmount / assetValue;
  return (
    ratio <= maxRatio ||
    `Loan-to-value ratio (${(ratio * 100).toFixed(1)}%) exceeds maximum allowed (${maxRatio * 100}%)`
  );
};

/**
 * Validates retirement savings rate
 */
export const validateRetirementSavingsRate = (
  savingsRate: number,
  minRate = 0.1,
  maxRate = 0.9
) => {
  return (
    (savingsRate >= minRate && savingsRate <= maxRate) ||
    `Savings rate should be between ${minRate * 100}% and ${maxRate * 100}% of income`
  );
};

/**
 * Validates emergency fund amount (3-6 months of expenses)
 */
export const validateEmergencyFund = (
  fundAmount: number,
  monthlyExpenses: number,
  minMonths = 3,
  maxMonths = 6
) => {
  const minAmount = monthlyExpenses * minMonths;
  const maxAmount = monthlyExpenses * maxMonths;

  if (fundAmount < minAmount) {
    return `Emergency fund should cover at least ${minMonths} months of expenses ($${minAmount.toLocaleString()})`;
  }

  if (fundAmount > maxAmount * 1.5) {
    return `Consider investing amounts over ${maxMonths} months of expenses ($${maxAmount.toLocaleString()})`;
  }

  return true;
};

/**
 * Schema for financial account information
 */
export const financialAccountSchema = z.object({
  accountType: z.enum(['CHECKING', 'SAVINGS', 'INVESTMENT', 'RETIREMENT', 'OTHER']),
  institution: z.string().min(2, 'Institution name is required'),
  accountNumber: z.string().optional(),
  balance: currencySchema,
  interestRate: percentageSchema.optional(),
  isPrimary: z.boolean().default(false),
  lastUpdated: z.date().optional(),
});

/**
 * Schema for loan information
 */
export const loanSchema = z.object({
  loanType: z.enum(['MORTGAGE', 'AUTO', 'STUDENT', 'PERSONAL', 'CREDIT_CARD', 'OTHER']),
  lender: z.string().min(2, 'Lender name is required'),
  originalAmount: currencySchema,
  currentBalance: currencySchema,
  interestRate: percentageSchema,
  termMonths: z.number().int().positive('Term must be positive'),
  startDate: z.date(),
  isActive: z.boolean().default(true),
});

/**
 * Schema for investment account
 */
export const investmentAccountSchema = z.object({
  accountType: z.enum([
    'BROKERAGE',
    'ROTH_IRA',
    'TRADITIONAL_IRA',
    '401K',
    '403B',
    'SEP_IRA',
    'OTHER',
  ]),
  provider: z.string().min(2, 'Provider name is required'),
  accountNumber: z.string().optional(),
  currentValue: currencySchema,
  annualReturnRate: percentageSchema.optional(),
  riskLevel: z.enum(['LOW', 'MODERATE', 'HIGH', 'VERY_HIGH']).optional(),
  isTaxAdvantaged: z.boolean().default(false),
});

/**
 * Schema for insurance policy
 */
export const insurancePolicySchema = z.object({
  policyType: z.enum([
    'HEALTH',
    'LIFE',
    'AUTO',
    'HOMEOWNERS',
    'RENTERS',
    'DISABILITY',
    'LIABILITY',
    'OTHER',
  ]),
  provider: z.string().min(2, 'Provider name is required'),
  policyNumber: z.string().optional(),
  coverageAmount: currencySchema.optional(),
  premium: currencySchema.optional(),
  premiumFrequency: z.enum(['MONTHLY', 'QUARTERLY', 'SEMI_ANNUALLY', 'ANNUALLY']).optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  isActive: z.boolean().default(true),
});
