import { z } from 'zod';

/**
 * Common validation schemas for financial data
 */

export const currencySchema = z
  .number()
  .min(0, 'Amount must be positive')
  .max(1_000_000_000, 'Amount is too large')
  .transform((val) => Number(val.toFixed(2)));

export const percentageSchema = z
  .number()
  .min(0, 'Percentage must be at least 0%')
  .max(100, 'Percentage cannot exceed 100%')
  .transform((val) => Number(val.toFixed(2)));

export const dateSchema = z.preprocess(
  (arg) => {
    if (typeof arg === 'string' || arg instanceof Date) return new Date(arg);
    return undefined;
  },
  z.date({
    required_error: 'Date is required',
    invalid_type_error: 'Invalid date format',
  })
);

export const positiveIntegerSchema = z
  .number()
  .int('Must be a whole number')
  .min(0, 'Must be a positive number')
  .max(999, 'Number is too large');

export const emailSchema = z.string().email('Invalid email address').toLowerCase().trim();

export const phoneSchema = z
  .string()
  .regex(/^\+?[\d\s-]{10,}$/, 'Please enter a valid phone number (at least 10 digits)')
  .transform((val) => val.replace(/[\s-]/g, ''));

// Common financial validators
export const validatePositive = (val: number) => val >= 0 || 'Must be positive';
export const validateFutureDate = (date: Date) => date > new Date() || 'Date must be in the future';

export const validateAge = (min: number, max: number) => (val: number) =>
  (val >= min && val <= 120) || `Age must be between ${min} and 120`;

// Common error messages
export const ERROR_MESSAGES = {
  REQUIRED: 'This field is required',
  INVALID_EMAIL: 'Please enter a valid email',
  INVALID_PHONE: 'Please enter a valid phone number',
  INVALID_PERCENTAGE: 'Please enter a value between 0 and 100',
  INVALID_CURRENCY: 'Please enter a valid amount',
  FUTURE_DATE: 'Date must be in the future',
  PAST_DATE: 'Date must be in the past',
} as const;

// Type helpers
export type Currency = z.infer<typeof currencySchema>;
export type Percentage = z.infer<typeof percentageSchema>;

/**
 * Creates a field schema with common options
 */
export const createFieldSchema = <T extends z.ZodTypeAny>(
  schema: T,
  options: {
    required?: boolean;
    min?: number;
    max?: number;
    label?: string;
  } = {}
) => {
  let fieldSchema = schema;
  const { required = true, min, max, label } = options;

  if (required) {
    fieldSchema = fieldSchema.nonempty({
      message: label ? `${label} is required` : ERROR_MESSAGES.REQUIRED,
    });
  }

  if (min !== undefined) {
    fieldSchema = fieldSchema.min(min, {
      message: label ? `${label} must be at least ${min}` : `Must be at least ${min}`,
    });
  }

  if (max !== undefined) {
    fieldSchema = fieldSchema.max(max, {
      message: label ? `${label} cannot exceed ${max}` : `Cannot exceed ${max}`,
    });
  }

  return fieldSchema;
};
