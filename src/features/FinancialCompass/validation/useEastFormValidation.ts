import { useState, useCallback } from 'react';
import { ZodError } from 'zod';
import { retirementGoalsSchema, RetirementGoalsFormData } from './eastSchemas';

type ValidationErrors<T> = Partial<Record<keyof T, string>>;

export const useEastFormValidation = () => {
  const [errors, setErrors] = useState<ValidationErrors<RetirementGoalsFormData>>({});
  const [isValid, setIsValid] = useState<boolean>(false);

  const validateForm = useCallback((formData: Partial<RetirementGoalsFormData>) => {
    try {
      // Validate against the schema
      retirementGoalsSchema.parse(formData);
      setErrors({});
      setIsValid(true);
      return { isValid: true, errors: {} };
    } catch (error) {
      if (error instanceof ZodError) {
        // Convert Zod errors to a more usable format
        const validationErrors: ValidationErrors<RetirementGoalsFormData> = {};

        error.issues.forEach((issue) => {
          const path = issue.path[0] as keyof RetirementGoalsFormData;
          if (path) {
            validationErrors[path] = issue.message;
          }
        });

        setErrors(validationErrors);
        setIsValid(false);
        return { isValid: false, errors: validationErrors };
      }

      // For unexpected errors
      console.error('Validation error:', error);
      return { isValid: false, errors: { _general: 'An unexpected validation error occurred' } };
    }
  }, []);

  const validateField = useCallback((field: keyof RetirementGoalsFormData, value: any) => {
    try {
      // Create a temporary object with just the field we want to validate
      const tempData = { [field]: value };

      // Validate just this field against the schema
      retirementGoalsSchema.pick({ [field]: true }).parse(tempData);

      // If validation passes, clear any existing error for this field
      setErrors((prev) => ({
        ...prev,
        [field]: undefined,
      }));

      return { isValid: true, error: undefined };
    } catch (error) {
      if (error instanceof ZodError) {
        const errorMessage = error.errors[0]?.message || 'Invalid value';

        // Update the errors state with the new error
        setErrors((prev) => ({
          ...prev,
          [field]: errorMessage,
        }));

        return { isValid: false, error: errorMessage };
      }

      return { isValid: false, error: 'Validation error' };
    }
  }, []);

  const resetValidation = useCallback(() => {
    setErrors({});
    setIsValid(false);
  }, []);

  return {
    errors,
    isValid,
    validateForm,
    validateField,
    resetValidation,
  };
};

export default useEastFormValidation;
