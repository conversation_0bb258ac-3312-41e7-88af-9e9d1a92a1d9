import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../context/FinancialCompassContext';
import { useGuidedJourney } from '../../GuidedJourney/context/GuidedJourneyContext';
import GuidedFlowNavigator from '../../GuidedJourney/components/GuidedFlowNavigator';
import NorthDirectionTracker from '../components/North/NorthDirectionTracker';
import PersonalInformation from '../components/North/PersonalInformation';
import FamilyInformation from '../components/North/FamilyInformation';
import IncomeDetails from '../components/North/IncomeDetails';
import ExpenseDetails from '../components/North/ExpenseDetails';
import Assets from '../components/North/Assets';
import Liabilities from '../components/North/Liabilities';
import SummarySection from '../components/North/SummarySection';

interface NorthDirectionPageProps {
  onComplete?: () => void;
}

const NorthDirectionPage: React.FC<NorthDirectionPageProps> = ({ onComplete }) => {
  const { theme } = useTheme();
  const {
    northSections,
    updateNorthSectionStatus,
    updateNorthProgress,
    activeSubFeature,
    setActiveSubFeature,
  } = useFinancialCompass();
  const { updateActiveDirection } = useGuidedJourney();

  // Current step state
  const [currentStep, setCurrentStep] = useState<string>('overview');

  // Set active direction to North
  useEffect(() => {
    if (updateActiveDirection) {
      updateActiveDirection('north');
    }
  }, [updateActiveDirection]);

  // Steps for the guided flow
  const steps = [
    {
      id: 'overview',
      label: 'Overview',
      description: 'North Direction: Where You Are',
      isCompleted: true,
    },
    {
      id: 'personal_information',
      label: 'Personal Information',
      description: 'Basic personal details',
      isCompleted: northSections.find((s) => s.id === 'personal_information')?.isCompleted || false,
    },
    {
      id: 'family_information',
      label: 'Family Information',
      description: 'Information about your family',
      isCompleted: northSections.find((s) => s.id === 'family_information')?.isCompleted || false,
    },
    {
      id: 'income_details',
      label: 'Income Details',
      description: 'Your income sources',
      isCompleted: northSections.find((s) => s.id === 'income_details')?.isCompleted || false,
    },
    {
      id: 'expense_details',
      label: 'Expense Details',
      description: 'Your expenses',
      isCompleted: northSections.find((s) => s.id === 'expense_details')?.isCompleted || false,
    },
    {
      id: 'assets_investments',
      label: 'Assets & Investments',
      description: 'What you own',
      isCompleted: northSections.find((s) => s.id === 'assets_investments')?.isCompleted || false,
    },
    {
      id: 'liabilities_debt',
      label: 'Liabilities & Debt',
      description: 'What you owe',
      isCompleted: northSections.find((s) => s.id === 'liabilities_debt')?.isCompleted || false,
    },
    {
      id: 'summary',
      label: 'Summary',
      description: 'Review your financial position',
      isCompleted: false,
    },
  ];

  // Handle step change
  const handleStepChange = (stepId: string) => {
    setCurrentStep(stepId);

    // Set active sub-feature if it's not the overview or summary
    if (stepId !== 'overview' && stepId !== 'summary') {
      setActiveSubFeature(stepId);

      // Update section status to active
      updateNorthSectionStatus(
        stepId,
        northSections.find((s) => s.id === stepId)?.isCompleted || false,
        true
      );
    } else {
      setActiveSubFeature(null);
    }
  };

  // Handle section completion
  const handleSectionComplete = (sectionId: string, isCompleted: boolean = true) => {
    updateNorthSectionStatus(sectionId, isCompleted, true);

    // Calculate progress
    const completedSections = northSections.filter((s) =>
      s.id === sectionId ? isCompleted : s.isCompleted
    ).length;
    const progress = (completedSections / northSections.length) * 100;
    updateNorthProgress(progress);

    // Move to next step if completed
    if (isCompleted) {
      const currentIndex = steps.findIndex((step) => step.id === currentStep);
      if (currentIndex < steps.length - 1) {
        setCurrentStep(steps[currentIndex + 1].id);
      }
    }
  };

  // Handle section selection from tracker
  const handleSectionSelect = (sectionId: string) => {
    setCurrentStep(sectionId);
    setActiveSubFeature(sectionId);

    // Update section status to active
    updateNorthSectionStatus(
      sectionId,
      northSections.find((s) => s.id === sectionId)?.isCompleted || false,
      true
    );
  };

  // Render current step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 'overview':
        return <NorthDirectionTracker onSectionSelect={handleSectionSelect} />;
      case 'personal_information':
        return (
          <PersonalInformation
            onComplete={() => handleSectionComplete('personal_information')}
            onBack={() => handleStepChange('overview')}
          />
        );
      case 'family_information':
        return (
          <FamilyInformation
            onComplete={() => handleSectionComplete('family_information')}
            onBack={() => handleStepChange('personal_information')}
          />
        );
      case 'income_details':
        return (
          <IncomeDetails
            onComplete={() => handleSectionComplete('income_details')}
            onBack={() => handleStepChange('family_information')}
          />
        );
      case 'expense_details':
        return (
          <ExpenseDetails
            onComplete={() => handleSectionComplete('expense_details')}
            onBack={() => handleStepChange('income_details')}
          />
        );
      case 'assets_investments':
        return (
          <Assets
            onComplete={() => handleSectionComplete('assets_investments')}
            onBack={() => handleStepChange('expense_details')}
          />
        );
      case 'liabilities_debt':
        return (
          <Liabilities
            onComplete={() => handleSectionComplete('liabilities_debt')}
            onBack={() => handleStepChange('assets_investments')}
          />
        );
      case 'summary':
        return (
          <SummarySection
            onComplete={onComplete}
            onBack={() => handleStepChange('liabilities_debt')}
          />
        );
      default:
        return <div>Unknown step</div>;
    }
  };

  return (
    <PageContainer theme={theme}>
      <PageHeader theme={theme}>
        <PageTitle theme={theme}>North Direction: Where You Are</PageTitle>
        <PageDescription theme={theme}>
          The North direction helps you understand your current financial position. Complete each
          section to build a comprehensive picture of your finances.
        </PageDescription>
      </PageHeader>

      <GuidedFlowNavigator
        steps={steps}
        currentStepId={currentStep}
        onStepChange={handleStepChange}
        direction="horizontal"
        allowSkip={false}
        showProgress={true}
      />

      <ContentContainer theme={theme}>{renderStepContent()}</ContentContainer>
    </PageContainer>
  );
};

// Placeholder component for sections not yet implemented
const PlaceholderSection: React.FC<{
  title: string;
  description: string;
  onComplete: () => void;
  onBack: () => void;
}> = ({ title, description, onComplete, onBack }) => {
  const { theme } = useTheme();

  return (
    <SectionContainer theme={theme}>
      <SectionHeader theme={theme}>
        <SectionTitle theme={theme}>{title}</SectionTitle>
        <SectionDescription theme={theme}>{description}</SectionDescription>
      </SectionHeader>

      <PlaceholderContent theme={theme}>
        <PlaceholderIcon>🚧</PlaceholderIcon>
        <PlaceholderText theme={theme}>
          This section is under construction. Check back soon!
        </PlaceholderText>
      </PlaceholderContent>

      <ButtonContainer>
        <BackButton onClick={onBack} theme={theme}>
          Back
        </BackButton>
        <CompleteButton onClick={onComplete} theme={theme}>
          Mark as Complete
        </CompleteButton>
      </ButtonContainer>
    </SectionContainer>
  );
};

// We now use the standalone SummarySection component from '../components/North/SummarySection'

// Styled components
const PageContainer = styled.div<{ theme: any }>`
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
`;

const PageHeader = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const PageTitle = styled.h1<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 16px 0;
  font-size: 2rem;
`;

const PageDescription = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  font-size: 1.1rem;
  line-height: 1.5;
  margin: 0;
`;

const ContentContainer = styled.div<{ theme: any }>`
  margin-top: 24px;
`;

const SectionContainer = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.secondary};
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const SectionHeader = styled.div<{ theme: any }>`
  margin-bottom: 24px;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 16px;
`;

const SectionTitle = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const SectionDescription = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const PlaceholderContent = styled.div<{ theme: any }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-radius: 8px;
  margin-bottom: 24px;
`;

const PlaceholderIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 16px;
`;

const PlaceholderText = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  font-size: 1.2rem;
  text-align: center;
  margin: 0;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: 24px;
`;

const BackButton = styled.button<{ theme: any }>`
  padding: 12px 24px;
  border-radius: 4px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
  color: ${(props) => props.theme.colors.text.primary};
  border: none;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.hover};
  }
`;

const CompleteButton = styled.button<{ theme: any }>`
  padding: 12px 24px;
  border-radius: 4px;
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.text.onPrimary};
  border: none;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

const SummaryContent = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const SummaryStatus = styled.div<{ theme: any }>`
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-radius: 8px;
`;

const StatusIcon = styled.div`
  font-size: 2rem;
  margin-right: 16px;
`;

const StatusText = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  font-size: 1.2rem;
  margin: 0;
`;

const SectionsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const SectionItem = styled.div<{ isCompleted: boolean; theme: any }>`
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 4px;
  background-color: ${(props) =>
    props.isCompleted ? props.theme.colors.success.light : props.theme.colors.background.tertiary};
`;

const SectionItemIcon = styled.div<{ isCompleted: boolean; theme: any }>`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: ${(props) =>
    props.isCompleted ? props.theme.colors.success.main : props.theme.colors.background.main};
  color: ${(props) =>
    props.isCompleted ? props.theme.colors.text.onPrimary : props.theme.colors.text.secondary};
  margin-right: 12px;
  font-size: 0.8rem;
`;

const SectionItemTitle = styled.div`
  font-weight: 500;
`;

const FinancialSummary = styled.div<{ theme: any }>`
  margin-top: 24px;
  padding: 16px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-radius: 8px;
`;

const FinancialSummaryTitle = styled.h4<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 8px;
`;

const FinancialMetrics = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
`;

const FinancialMetric = styled.div<{ theme: any }>`
  padding: 12px;
  background-color: ${(props) => props.theme.colors.background.main};
  border-radius: 8px;
`;

const MetricLabel = styled.div`
  font-size: 0.9rem;
  color: ${(props) => props.theme.colors.text.secondary};
  margin-bottom: 8px;
`;

const MetricValue = styled.div<{ isPositive?: boolean }>`
  font-size: 1.2rem;
  font-weight: 700;
  color: ${(props) =>
    props.isPositive !== undefined
      ? props.isPositive
        ? props.theme.colors.success.dark
        : props.theme.colors.error.main
      : props.theme.colors.text.primary};
`;

const MetricDescription = styled.div`
  font-size: 12px;
  color: ${({ theme }) => theme.colors.text.secondary};
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 4px;
`;

const HealthIndicator = styled.span<{ status: string; color: string }>`
  display: inline-block;
  margin-left: 8px;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
  background-color: ${({ color }) => color};
  color: white;
  &:before {
    content: '${({ status }) => status}';
  }
`;

const RecommendationsSection = styled.div<{ theme: any }>`
  margin-top: 24px;
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const RecommendationsTitle = styled.h3<{ theme: any }>`
  font-size: 18px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: 16px;
`;

const RecommendationsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const RecommendationItem = styled.div<{ priority: string; theme: any }>`
  padding: 12px;
  border-radius: 6px;
  background-color: ${({ theme }) => theme.colors.background.default};
  border-left: 4px solid
    ${({ priority, theme }) =>
      priority === 'High'
        ? theme.colors.error.main
        : priority === 'Medium'
          ? theme.colors.warning.main
          : theme.colors.success.main};
`;

const RecommendationHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
`;

const RecommendationTitle = styled.h4`
  font-size: 16px;
  font-weight: 600;
  margin: 0;
`;

const RecommendationDescription = styled.p`
  font-size: 14px;
  margin: 0;
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const PriorityBadge = styled.span<{ priority: string; theme: any }>`
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  background-color: ${({ priority, theme }) =>
    priority === 'High'
      ? theme.colors.error.light
      : priority === 'Medium'
        ? theme.colors.warning.light
        : theme.colors.success.light};
  color: ${({ priority, theme }) =>
    priority === 'High'
      ? theme.colors.error.dark
      : priority === 'Medium'
        ? theme.colors.warning.dark
        : theme.colors.success.dark};
`;

const ButtonGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
`;

const AnalysisButton = styled.button<{ theme: any }>`
  padding: 12px 24px;
  border-radius: 4px;
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.text.onPrimary};
  border: none;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s ease;
  width: 100%;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

const ExportButton = styled.button<{ theme: any }>`
  padding: 12px 24px;
  border-radius: 4px;
  background-color: ${(props) => props.theme.colors.success.main};
  color: ${(props) => props.theme.colors.text.onPrimary};
  border: none;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s ease;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  &:hover {
    background-color: ${(props) => props.theme.colors.success.dark};
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
`;

const Modal = styled.div<{ theme: any }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24px;
  z-index: 1000;
  overflow-y: auto;
`;

export default NorthDirectionPage;
