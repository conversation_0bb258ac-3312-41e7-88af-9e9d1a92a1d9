import React from 'react';
import { render, screen } from '@testing-library/react';
import EastDirectionPage from './EastDirectionPage';
import { ThemeProvider } from '../../../context/ThemeContext';
import { FinancialCompassProvider } from '../context/FinancialCompassContext';
import { GuidedJourneyProvider } from '../../GuidedJourney/context/GuidedJourneyContext';

// Mock the PDF export utility
jest.mock('../../../utils/pdfExport', () => ({
  downloadFinancialSummaryPDF: jest.fn(),
  generateFinancialSummaryPDF: jest.fn(),
}));

describe('EastDirectionPage', () => {
  const mockOnComplete = jest.fn();

  const renderComponent = () => {
    return render(
      <ThemeProvider>
        <GuidedJourneyProvider>
          <FinancialCompassProvider>
            <EastDirectionPage onComplete={mockOnComplete} />
          </FinancialCompassProvider>
        </GuidedJourneyProvider>
      </ThemeProvider>
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the East Direction page with title and description', () => {
    renderComponent();

    // Check if the page title is rendered
    expect(screen.getByText("East Direction: Where You're Going")).toBeInTheDocument();

    // Check if the page description is rendered
    expect(
      screen.getByText(/The East direction helps you plan for your retirement future/)
    ).toBeInTheDocument();
  });

  it('renders the GuidedFlowNavigator with correct steps', () => {
    renderComponent();

    // Check if the steps are rendered
    expect(screen.getByText('Overview')).toBeInTheDocument();
    expect(screen.getByText('Retirement Goals')).toBeInTheDocument();
    expect(screen.getByText('Retirement Income')).toBeInTheDocument();
    expect(screen.getByText('Retirement Expenses')).toBeInTheDocument();
    expect(screen.getByText('Retirement Timeline')).toBeInTheDocument();
    expect(screen.getByText('Summary')).toBeInTheDocument();
  });

  it('renders the EastDirectionTracker by default', () => {
    renderComponent();

    // The tracker should be rendered initially
    expect(screen.getByText("East: Where You're Going")).toBeInTheDocument();
  });
});
