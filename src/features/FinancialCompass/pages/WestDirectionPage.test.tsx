import React from 'react';
import { render, screen } from '@testing-library/react';
import WestDirectionPage from './WestDirectionPage';
import { ThemeProvider } from '../../../context/ThemeContext';
import { FinancialCompassProvider } from '../context/FinancialCompassContext';
import { GuidedJourneyProvider } from '../../GuidedJourney/context/GuidedJourneyContext';

// Mock the PDF export utility
jest.mock('../../../utils/pdfExport', () => ({
  downloadFinancialSummaryPDF: jest.fn(),
  generateFinancialSummaryPDF: jest.fn(),
}));

describe('WestDirectionPage', () => {
  const mockOnComplete = jest.fn();

  const renderComponent = () => {
    return render(
      <ThemeProvider>
        <GuidedJourneyProvider>
          <FinancialCompassProvider>
            <WestDirectionPage onComplete={mockOnComplete} />
          </FinancialCompassProvider>
        </GuidedJourneyProvider>
      </ThemeProvider>
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the West Direction page with title and description', () => {
    renderComponent();

    // Check if the page title is rendered
    expect(screen.getByText('West Direction: Your Legacy & Impact')).toBeInTheDocument();

    // Check if the page description is rendered
    expect(
      screen.getByText(/The West direction helps you plan for your legacy and lasting impact/)
    ).toBeInTheDocument();
  });

  it('renders the GuidedFlowNavigator with correct steps', () => {
    renderComponent();

    // Check if the steps are rendered
    expect(screen.getByText('Overview')).toBeInTheDocument();
    expect(screen.getByText('Estate Planning')).toBeInTheDocument();
    expect(screen.getByText('Charitable Giving')).toBeInTheDocument();
    expect(screen.getByText('Legacy Planning')).toBeInTheDocument();
    expect(screen.getByText('Values & Goals')).toBeInTheDocument();
    expect(screen.getByText('Summary')).toBeInTheDocument();
  });

  it('renders the WestDirectionTracker by default', () => {
    renderComponent();

    // The tracker should be rendered initially
    expect(screen.getByText('West: Your Legacy & Impact')).toBeInTheDocument();
  });
});
