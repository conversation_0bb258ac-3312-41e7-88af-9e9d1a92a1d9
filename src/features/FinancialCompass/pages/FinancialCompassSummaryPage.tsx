/**
 * Financial Compass Summary Page
 *
 * This page provides a comprehensive summary of all four compass directions,
 * showing the user's overall financial health, trends, alerts, actions, and guidance.
 */

import React, { useState, useMemo } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../context/FinancialCompassContext';
import { useGuidedJourney } from '../../GuidedJourney/context/GuidedJourneyContext';
import { formatCurrency } from '../../../utils/formatters';
import {
  TrendsSection,
  AlertsSection,
  ActionsSection,
  GuidanceSection,
} from '../components/shared/SummaryComponents';
import {
  generatePDF,
  generateTrends,
  generateAlerts,
  generateActionItems as generateActions,
  generateGuidanceItems as generateGuidance
} from '../../../utils/enhancedPdfExport';
import {
  calculateTotalMonthlyIncome,
  IncomeSource, // Keep IncomeSource as it's used in calculatedTotalMonthlyIncome
} from '../../../utils/incomeCalculator';
import {
  calculateDetailedFinancialHealthScore,
  FinancialHealthCategory,
  FinancialHealthScoreStatus,
  FinancialHealthRecommendation, // Keep as it might be used elsewhere or for completeness
  calculateCashFlowHealthScore,
  calculateDebtManagementScore,
  calculateEmergencyFundScore,
  calculateNetWorthScore,
  // remove calculateRetirementReadinessScore as it is unused
  calculateProtectionPlanningScore,
  calculateEstatePlanningScore,
  FINANCIAL_HEALTH_WEIGHTS,
} from '../../../utils/financialHealthCalculator';
import {
  FinancialCompassData, // Keep FinancialCompassData
} from '../../../types/compass'; // Assuming this is the correct import path for the interface
import {
  // remove calculateSavingsGap as it is unused
} from '../../../utils/retirementPlanningUtils';
import { saveAs } from 'file-saver'; // Keep saveAs

interface FinancialCompassSummaryPageProps {
  onComplete?: () => void;
  onBack?: () => void;
}

interface InsuranceAnalysis {
  lifeInsurance?: {
    hasInsurance?: boolean;
    coverageAmount?: string;
  };
  disabilityInsurance?: {
    hasInsurance?: boolean;
    coverageAmount?: string;
  };
  healthInsurance?: {
    hasInsurance?: boolean;
  };
}

interface EmergencyFund {
  currentEmergencyFund?: string;
}

interface ExpenseDetails {
  totalMonthlyExpenses?: string;
}

interface EstatePlanning {
  hasWill?: boolean;
  hasTrust?: boolean;
  hasPowerOfAttorney?: boolean;
  hasHealthcareDirective?: boolean;
  hasEstateStrategy?: boolean;
  hasSuccessionPlan?: boolean;
  hasTaxStrategy?: boolean;
}

interface EstateDocuments {
  will?: {
    exists?: boolean;
  };
  trust?: {
    exists?: boolean;
  };
  powerOfAttorney?: {
    exists?: boolean;
  };
  healthcareDirective?: {
    exists?: boolean;
  };
}

interface LegacyPlanning {
  legacyStatement?: boolean;
  beneficiaryDesignationsInPlace?: boolean;
  hasCharitableStrategy?: boolean;
}

interface SouthData {
  insuranceAnalysis?: InsuranceAnalysis;
  emergencyFund?: EmergencyFund;
}

interface NorthData {
  expenseDetails?: ExpenseDetails;
}

interface WestData {
  estatePlanning?: EstatePlanning;
  estateDocuments?: EstateDocuments;
  legacyPlanning?: LegacyPlanning;
}

interface FinancialCompassData {
  metadata: Record<string, unknown>;
  financialCompass: {
    north?: NorthData;
    east?: Record<string, unknown>; // Consider defining a type for eastData if its structure is known
    south?: SouthData;
    west?: WestData;
  };
}

const FinancialCompassSummaryPage: React.FC<FinancialCompassSummaryPageProps> = () => {
  const { theme } = useTheme();
  const { data } = useFinancialCompass();
  const { updateActiveDirection } = useGuidedJourney();
  const [isExporting, setIsExporting] = useState(false);

  // Extract data from all directions
  const northData = data.north || {};
  const eastData = data.east || {};
  const southData = data.south || {};
  const westData = data.west || {};

  // Calculate key financial metrics
  // North Direction (Current Position)

  // Income calculation
  // Remove unused incomeSources variable
  // const incomeSources: IncomeSource[] = useMemo(() => {
  //   if (!northData?.incomeDetails?.incomeSources) return [];
  //   return northData.incomeDetails.incomeSources.map((source: any) => ({
  //     ...source,
  //     amount: parseFloat(source.amount) || 0,
  //     startAge: source.startAge ? parseInt(source.startAge) || 100 : undefined,
  //     endAge: source.endAge ? parseInt(source.endAge) || 100 : undefined,
  //   }));
  // }, [northData]);

  // Calculate total monthly income using the utility
  const calculatedTotalMonthlyIncome = useMemo(() => {
    // Combine all potential income sources from North and East
    const allIncomeSources: IncomeSource[] = [
      ...(northData?.incomeDetails?.incomeSources?.map((source: any) => ({
        // Replace any with appropriate type or add checks
        ...source,
        amount: parseFloat(source.amount) || 0,
        frequency: source.frequency,
      })) || []),
      ...(eastData?.retirementIncome?.incomeSources?.map((source: any) => ({
        // Replace any with appropriate type or add checks
        ...source,
        amount: parseFloat(source.amount) || 0,
        frequency: source.frequency,
      })) || []),
      // Add estimated Social Security and Pension if not already in detailed sources
      ...(!eastData?.retirementIncome?.incomeSources?.some(
        (s: any) => s.type === 'socialSecurity' // Replace any with appropriate type or add checks
      ) && (eastData.retirementIncome as any)?.socialSecurity?.estimatedMonthlyBenefit // Replace any with appropriate type or add checks
        ? [
          {
            id: 'est-ss',
            name: 'Estimated Social Security',
            type: 'socialSecurity',
            amount:
              parseFloat(
                (eastData.retirementIncome as any)?.socialSecurity?.estimatedMonthlyBenefit || '0' // Replace any with appropriate type or add checks
              ) || 0,
            frequency: 'monthly',
            isPassive: true,
            startAge: eastData.retirementIncome?.socialSecurity?.startAge
              ? parseInt(eastData.retirementIncome.socialSecurity.startAge as string) || 67
              : 67,
            endAge: 100,
          },
        ]
        : []),
      ...(!eastData?.retirementIncome?.incomeSources?.some((s: any) => s.type === 'pension') && // Replace any with appropriate type or add checks
        (eastData.retirementIncome as any)?.pension?.estimatedMonthlyBenefit // Replace any with appropriate type or add checks
        ? [
          {
            id: 'est-pension',
            name: 'Estimated Pension',
            type: 'pension',
            amount:
              parseFloat(
                (eastData.retirementIncome as any)?.pension?.estimatedMonthlyBenefit || '0' // Replace any with appropriate type or add checks
              ) || 0,
            frequency: 'monthly',
            isPassive: true,
            startAge: eastData.retirementIncome?.pension?.startAge
              ? parseInt(eastData.retirementIncome.pension.startAge as string) || 65
              : 65,
            endAge: 100,
          },
        ]
        : []),
    ];

    const { totalMonthly } = calculateTotalMonthlyIncome(allIncomeSources);
    return totalMonthly;
  }, [northData, eastData]);

  const monthlyCashFlow = useMemo(() => {
    const totalMonthlyExpenses = parseFloat(
      (northData.expenseDetails as any)?.totalMonthlyExpenses || '0' // Replace any with appropriate type or add checks
    );
    return calculatedTotalMonthlyIncome - totalMonthlyExpenses;
  }, [calculatedTotalMonthlyIncome, northData]);

  // Assets and liabilities
  const assetData = northData.assets || {}; // Consider defining a type for assetData
  const liabilities = northData.liabilities || {}; // Consider defining a type for liabilities

  const totalAssets = useMemo(
    () =>
      parseFloat(
        (northData.netWorthDetails as any)?.totalAssets || (assetData as any)?.totalAssets || '0' // Replace any with appropriate type or add checks
      ),
    [northData, assetData]
  );

  const totalLiabilities = useMemo(
    () =>
      parseFloat(
        (northData.netWorthDetails as any)?.totalLiabilities || // Replace any with appropriate type or add checks
        (liabilities as any)?.totalLiabilities || // Replace any with appropriate type or add checks
        '0'
      ),
    [northData, liabilities]
  );

  const netWorth = useMemo(() => totalAssets - totalLiabilities, [totalAssets, totalLiabilities]);

  // Calculate savings rate and debt-to-income ratio
  const savingsRate = useMemo(() => {
    const totalMonthlyExpenses = parseFloat(
      (northData.expenseDetails as any)?.totalMonthlyExpenses || '0' // Replace any with appropriate type or add checks
    );
    const discretionaryIncome = calculatedTotalMonthlyIncome - totalMonthlyExpenses;
    // Avoid division by zero
    return calculatedTotalMonthlyIncome > 0
      ? (discretionaryIncome / calculatedTotalMonthlyIncome) * 100
      : 0;
  }, [calculatedTotalMonthlyIncome, northData]);

  const debtToIncomeRatio = useMemo(() => {
    const totalMonthlyDebtPayments = parseFloat(
      (southData.debtManagement as any)?.monthlyDebtPayments || '0' // Replace any with appropriate type or add checks
    );
    // Avoid division by zero
    return calculatedTotalMonthlyIncome > 0
      ? (totalMonthlyDebtPayments / calculatedTotalMonthlyIncome) * 100
      : 0;
  }, [calculatedTotalMonthlyIncome, southData]);

  // East Direction (Retirement Vision)
  const personalInfo = northData.personalInformation || {}; // Consider defining a type for personalInfo
  const dateOfBirth = (personalInfo as any)?.dateOfBirth; // Replace any with appropriate type or add checks

  // Calculate age from date of birth if available
  let calculatedAge = 0;
  if (dateOfBirth) {
    const birthDate = new Date(dateOfBirth);
    const today = new Date();
    calculatedAge = today.getFullYear() - birthDate.getFullYear();

    // Adjust age if birthday hasn't occurred yet this year
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      calculatedAge--;
    }
  }

  // Use the most reliable age source
  const currentAge =
    parseInt((personalInfo as any)?.age as string) || // Replace any with appropriate type or add checks
    calculatedAge ||
    parseInt((eastData?.retirementGoals as any)?.currentAge as string) || // Replace any with appropriate type or add checks
    30; // Provide a default age if no valid age is found

  const retirementAge = parseInt((eastData?.retirementGoals as any)?.retirementAge as string) || 65; // Replace any with appropriate type or add checks // Provide a default retirement age
  const yearsUntilRetirement = Math.max(0, retirementAge - currentAge);

  // Get savings goal from retirement goals or use a default
  const savingsGoal = useMemo(
    () => parseFloat((eastData?.retirementGoals as any)?.savingsGoal || '0'), // Replace any with appropriate type or add checks
    [eastData]
  );

  // Get current retirement savings from multiple possible sources
  // First check if we have retirement accounts in assetData
  const retirementAccounts = Array.isArray((assetData as any)?.retirementAccounts) // Replace any with appropriate type or add checks
    ? (assetData as any)?.retirementAccounts // Replace any with appropriate type or add checks
    : [];

  // Calculate total retirement savings from accounts
  const retirementAccountsSavings = useMemo(() => {
    return retirementAccounts.reduce(
      (total: number, account: any) => total + (parseFloat(account.balance) || 0), // Replace any with appropriate type or add checks
      0
    );
  }, [retirementAccounts]);

  // Check if we have retirement savings directly in retirement data
  const eastRetirementSavings = useMemo(() => {
    return parseFloat((eastData.retirementTimeline as any)?.currentSavings || '0'); // Replace any with appropriate type or add checks
  }, [eastData]);

  // Use the most reliable source for current retirement savings
  const currentRetirementSavings = useMemo(() => {
    return retirementAccountsSavings > 0 ? retirementAccountsSavings : eastRetirementSavings;
  }, [retirementAccountsSavings, eastRetirementSavings]);

  // Calculate savings gap - Use the imported calculateSavingsGap (if needed, but it was marked as unused)
  // Assuming calculateSavingsGap is needed for the display logic, but not for the main financial health score
  // If calculateSavingsGap is truly unused, remove the import and this calculation.
  // Based on the linting error, it's unused, so I will remove this calculation.
  // const savingsGap = useMemo(() => {
  //   if (yearsUntilRetirement <= 0) return 0;
  //   return calculateSavingsGap(
  //     currentRetirementSavings,
  //     savingsGoal,
  //     yearsUntilRetirement,
  //     parseFloat((eastData?.retirementGoals as any)?.expectedReturnRate || '0')
  //   );
  // }, [currentRetirementSavings, savingsGoal, yearsUntilRetirement, eastData]);
  // Keep a basic savings gap calculation if it's used in the display
  const savingsGap = useMemo(() => {
    if (yearsUntilRetirement <= 0 || savingsGoal <= 0) return 0; // Add check for savingsGoal
    return Math.max(0, savingsGoal - currentRetirementSavings);
  }, [currentRetirementSavings, savingsGoal, yearsUntilRetirement]); // Update dependencies

  // South Direction (Protection & Risks)
  const protectionGapAmount = useMemo(() => {
    if (!southData?.insuranceAnalysis) return Infinity; // Indicate a significant gap if data is missing

    // Use the defined types instead of any
    const { lifeInsurance, disabilityInsurance } = southData.insuranceAnalysis;

    // Calculate life insurance needed (simplified: e.g., 10x income)
    // Using calculatedTotalMonthlyIncome instead of accessing from northData directly
    const annualIncome = calculatedTotalMonthlyIncome * 12;
    const lifeInsuranceNeeded = annualIncome * 10; // Example heuristic
    const currentLifeCoverage = parseFloat(lifeInsurance?.coverageAmount || '0');
    const lifeInsuranceGap = Math.max(0, lifeInsuranceNeeded - currentLifeCoverage);

    // Calculate disability insurance needed (simplified: e.g., 60% of income)
    // Using calculatedTotalMonthlyIncome instead of accessing from northData directly
    const monthlyIncome = calculatedTotalMonthlyIncome;
    const disabilityInsuranceNeeded = monthlyIncome * 0.6; // Example heuristic
    const currentDisabilityCoverage = parseFloat(disabilityInsurance?.coverageAmount || '0');
    const disabilityInsuranceGap = Math.max(
      0,
      disabilityInsuranceNeeded - currentDisabilityCoverage
    );

    // Aggregate gaps (can be more sophisticated)
    // For simplicity, we'll consider a gap if either life or disability coverage is insufficient.
    // A more complex model would consider interaction effects and other insurance types.
    return lifeInsuranceGap > 0 || disabilityInsuranceGap > 0
      ? Math.max(lifeInsuranceGap, disabilityInsuranceGap) // Report the larger gap
      : 0;
  }, [southData, calculatedTotalMonthlyIncome]); // Added calculatedTotalMonthlyIncome to dependency array

  // West Direction (Legacy Planning)
  const legacyPlanning = westData.legacyPlanning || {};

  // Check for estate planning documents
  const hasWill = useMemo(() => {
    // Use defined types instead of any
    const estatePlanning = westData.estatePlanning || {};
    const estateDocuments = westData.estateDocuments || {};
    return (
      (typeof estatePlanning === 'object' &&
        'hasWill' in estatePlanning &&
        estatePlanning.hasWill) ||
      (typeof estateDocuments === 'object' && estateDocuments.will?.exists === true)
    );
  }, [westData]);

  const hasPOA = useMemo(() => {
    // Use defined types instead of any
    const estatePlanning = westData.estatePlanning || {};
    const estateDocuments = westData.estateDocuments || {};
    return (
      (typeof estatePlanning === 'object' &&
        'hasPowerOfAttorney' in estatePlanning &&
        estatePlanning.hasPowerOfAttorney) ||
      (typeof estateDocuments === 'object' && estateDocuments.powerOfAttorney?.exists === true)
    );
  }, [westData]);

  const hasHealthcareDirective = useMemo(() => {
    // Use defined types instead of any
    const estatePlanning = westData.estatePlanning || {};
    const estateDocuments = westData.estateDocuments || {};
    return (
      (typeof estatePlanning === 'object' &&
        'hasHealthcareDirective' in estatePlanning &&
        estatePlanning.hasHealthcareDirective) ||
      (typeof estateDocuments === 'object' && estateDocuments.healthcareDirective?.exists === true)
    );
  }, [westData]);

  // Check if legacy planning is defined
  // Use defined types instead of any, remove redundant double negation
  const hasLegacyPlan = legacyPlanning && Object.keys(legacyPlanning).length > 0; // Use westData.legacyPlanning

  // Calculate overall financial health score
  const financialHealthResult = useMemo(() => {
    // Calculate individual category scores
    const cashFlowScore = calculateCashFlowHealthScore(
      monthlyCashFlow,
      calculatedTotalMonthlyIncome
    );

    const annualIncome = calculatedTotalMonthlyIncome * 12; // Use calculated monthly income
    const debtManagementScore = calculateDebtManagementScore(
      totalLiabilities,
      annualIncome,
      debtToIncomeRatio
    );

    // Use defined types instead of any
    const emergencyFundSavings = parseFloat(southData?.emergencyFund?.currentEmergencyFund || '0');
    const monthlyExpenses = parseFloat(northData?.expenseDetails?.totalMonthlyExpenses || '0');
    const emergencyFundScore = calculateEmergencyFundScore(emergencyFundSavings, monthlyExpenses);

    const netWorthScore = calculateNetWorthScore(netWorth, annualIncome, currentAge);

    // Simplified retirement readiness score based on savings gap
    // A more detailed score might use retirementCalculator.ts functions
    const retirementReadinessScore =
      savingsGoal > 0
        ? savingsGap === 0
          ? 100
          : Math.max(0, 100 - (savingsGap / savingsGoal) * 100) // Use the basic savingsGap calculation
        : 0; // Added check for savingsGoal > 0

    // Use defined types instead of any
    const protectionScore = calculateProtectionPlanningScore({
      insuranceCoverage: southData?.insuranceAnalysis as InsuranceAnalysis,
      emergencyFund: southData?.emergencyFund as EmergencyFund,
      monthlyExpenses: monthlyExpenses, // Pass monthly expenses
      annualIncome: annualIncome, // Pass annual income
      calculatedTotalMonthlyIncome: calculatedTotalMonthlyIncome, // Pass calculated monthly income
    });

    // Use defined types instead of any
    const estatePlanningScore = calculateEstatePlanningScore({
      estatePlanning: westData?.estatePlanning as EstatePlanning,
      estateDocuments: westData?.estateDocuments as EstateDocuments,
      legacyPlanning: westData?.legacyPlanning as LegacyPlanning,
    });

    // Structure scores into the expected array format for calculateDetailedFinancialHealthScore
    const financialHealthCategories: FinancialHealthCategory[] = [
      {
        id: 'cash_flow',
        name: 'Cash Flow',
        score: cashFlowScore,
        weight: FINANCIAL_HEALTH_WEIGHTS.CASH_FLOW,
        weightedScore: cashFlowScore * FINANCIAL_HEALTH_WEIGHTS.CASH_FLOW,
        metrics: [
          // Include relevant metrics for the category
          {
            id: 'monthly_cash_flow',
            name: 'Monthly Cash Flow',
            value: monthlyCashFlow,
            score: monthlyCashFlow >= 0 ? 100 : 0,
            target: '>= $0',
            description: 'The difference between your income and expenses.',
          },
          {
            id: 'savings_rate',
            name: 'Savings Rate',
            value: savingsRate,
            score: savingsRate >= 15 ? 100 : savingsRate >= 5 ? 50 : 0,
            target: '>= 15%',
            description: 'Percentage of income saved.',
          },
          // Add other relevant cash flow metrics if available
        ],
      },
      {
        id: 'debt_management',
        name: 'Debt Management',
        score: debtManagementScore,
        weight: FINANCIAL_HEALTH_WEIGHTS.DEBT_MANAGEMENT,
        weightedScore: debtManagementScore * FINANCIAL_HEALTH_WEIGHTS.DEBT_MANAGEMENT,
        metrics: [
          // Include relevant metrics for the category
          {
            id: 'debt_to_income',
            name: 'Debt-to-Income Ratio',
            value: debtToIncomeRatio,
            score: debtToIncomeRatio <= 36 ? 100 : debtToIncomeRatio <= 43 ? 50 : 0,
            target: '< 36%',
            description: 'Your total monthly debt payments compared to your gross monthly income.',
          },
          {
            id: 'total_liabilities',
            name: 'Total Liabilities',
            value: totalLiabilities,
            score: totalLiabilities === 0 ? 100 : totalLiabilities < annualIncome * 2 ? 70 : 30,
            target: '< 2x Annual Income',
            description: 'Your total outstanding debt.',
          },
          // Add other relevant debt management metrics if available
        ],
      },
      {
        id: 'emergency_fund',
        name: 'Emergency Fund',
        score: emergencyFundScore,
        weight: FINANCIAL_HEALTH_WEIGHTS.EMERGENCY_FUND,
        weightedScore: emergencyFundScore * FINANCIAL_HEALTH_WEIGHTS.EMERGENCY_FUND,
        metrics: [
          // Include relevant metrics for the category
          {
            id: 'emergency_fund_savings',
            name: 'Current Savings',
            value: emergencyFundSavings,
            score: emergencyFundSavings >= monthlyExpenses * 3 ? 100 : emergencyFundSavings >= monthlyExpenses * 1 ? 50 : 0,
            target: '>= 3 months expenses',
            description: 'Cash readily available for unexpected events.',
          },
          {
            id: 'emergency_fund_months',
            name: 'Months Covered',
            value: monthlyExpenses > 0 ? emergencyFundSavings / monthlyExpenses : 0,
            score: (monthlyExpenses > 0 ? emergencyFundSavings / monthlyExpenses : 0) >= 3 ? 100 : (monthlyExpenses > 0 ? emergencyFundSavings / monthlyExpenses : 0) >= 1 ? 50 : 0,
            target: '>= 3 months',
            description: 'Number of months your emergency fund can cover expenses.',
          },
          // Add other relevant emergency fund metrics if available
        ],
      },
      {
        id: 'net_worth',
        name: 'Net Worth',
        score: netWorthScore,
        weight: FINANCIAL_HEALTH_WEIGHTS.NET_WORTH,
        weightedScore: netWorthScore * FINANCIAL_HEALTH_WEIGHTS.NET_WORTH,
        metrics: [
          // Include relevant metrics for the category
          {
            id: 'net_worth_value',
            name: 'Net Worth',
            value: netWorth,
            score: netWorth > 0 ? 100 : netWorth === 0 ? 50 : 0,
            target: '> $0',
            description: 'Your assets minus your liabilities.',
          },
          {
            id: 'net_worth_to_income',
            name: 'Net Worth to Income',
            value: annualIncome > 0 ? netWorth / annualIncome : 0,
            score: (annualIncome > 0 ? netWorth / annualIncome : 0) >= 2 ? 100 : (annualIncome > 0 ? netWorth / annualIncome : 0) >= 0 ? 50 : 0,
            target: '>= 2x Annual Income (by age)',
            description: 'Your net worth compared to your annual income.',
          },
          // Add other relevant net worth metrics if available
        ],
      },
      {
        id: 'retirement',
        name: 'Retirement Readiness',
        score: retirementReadinessScore,
        weight: FINANCIAL_HEALTH_WEIGHTS.RETIREMENT,
        weightedScore: retirementReadinessScore * FINANCIAL_HEALTH_WEIGHTS.RETIREMENT,
        metrics: [
          // Include relevant metrics for the category
          {
            id: 'retirement_savings_goal',
            name: 'Savings Goal',
            value: savingsGoal,
            score: savingsGoal > 0 ? 100 : 0,
            target: '> $0',
            description: 'Your target amount for retirement.',
          },
          {
            id: 'current_retirement_savings',
            name: 'Current Savings',
            value: currentRetirementSavings,
            score: currentRetirementSavings >= savingsGoal ? 100 : Math.max(0, (currentRetirementSavings / savingsGoal) * 100),
            target: '>= Goal',
            description: 'Your current retirement savings balance.',
          },
          {
            id: 'years_until_retirement',
            name: 'Years to Retirement',
            value: yearsUntilRetirement,
            score: yearsUntilRetirement > 0 ? 100 : 0,
            target: '> 0',
            description: 'Years remaining until your target retirement age.',
          },
          {
            id: 'savings_gap',
            name: 'Savings Gap',
            value: savingsGap,
            score: savingsGap === 0 ? 100 : savingsGoal > 0 ? Math.max(0, 100 - (savingsGap / savingsGoal) * 100) : 0,
            target: '= $0',
            description: 'The amount needed to reach your retirement savings goal.',
          }, // Added check for savingsGoal > 0
          // Add other relevant retirement metrics if available
        ],
      },
      {
        id: 'protection',
        name: 'Protection Planning',
        score: protectionScore,
        weight: FINANCIAL_HEALTH_WEIGHTS.PROTECTION,
        weightedScore: protectionScore * FINANCIAL_HEALTH_WEIGHTS.PROTECTION,
        metrics: [
          // Include relevant metrics for the category
          {
            id: 'protection_gap',
            name: 'Protection Gap',
            value: protectionGapAmount,
            score: protectionGapAmount === 0 ? 100 : 0,
            target: '= $0',
            description: 'Potential gap in insurance coverage.',
          },
          // Add other relevant protection metrics if available (e.g., specific insurance types coverage)
        ],
      },
      {
        id: 'estate_planning',
        name: 'Estate Planning',
        score: estatePlanningScore,
        weight: FINANCIAL_HEALTH_WEIGHTS.ESTATE_PLANNING,
        weightedScore: estatePlanningScore * FINANCIAL_HEALTH_WEIGHTS.ESTATE_PLANNING,
        metrics: [
          // Include relevant metrics for the category
          {
            id: 'has_will',
            name: 'Has Will',
            value: hasWill ? 'Yes' : 'No',
            score: hasWill ? 100 : 0,
            target: 'Yes',
            description: 'Legal document for asset distribution.',
          },
          {
            id: 'has_poa',
            name: 'Has Power of Attorney',
            value: hasPOA ? 'Yes' : 'No',
            score: hasPOA ? 100 : 0,
            target: 'Yes',
            description: 'Authorizes someone to act on your behalf.',
          },
          {
            id: 'has_healthcare_directive',
            name: 'Has Healthcare Directive',
            value: hasHealthcareDirective ? 'Yes' : 'No',
            score: hasHealthcareDirective ? 100 : 0,
            target: 'Yes',
            description: 'Documents medical treatment preferences.',
          },
          {
            id: 'has_legacy_plan',
            name: 'Has Legacy Plan',
            value: hasLegacyPlan ? 'Yes' : 'No',
            score: hasLegacyPlan ? 100 : 0,
            target: 'Yes',
            description: 'Plan for your values and assets legacy.',
          },
          // Add other relevant estate planning metrics if available
        ],
      },
    ];

    // Add a check to ensure financialHealthCategories is a valid array before calling reduce
    if (!Array.isArray(financialHealthCategories) || financialHealthCategories.length === 0) {
      // Return a default or empty result if data is not in the expected format
      return {
        overallScore: 0,
        categories: [],
        status: 'Incomplete Data',
        strengths: [],
        weaknesses: [],
        recommendations: [],
      } as any; // Keep any for now as the return type needs to match the utility function
    }

    // Pass the array of categories to calculateDetailedFinancialHealthScore
    return calculateDetailedFinancialHealthScore(financialHealthCategories);

  }, [
    /* Add all dependencies that affect the calculation here */ monthlyCashFlow,
    calculatedTotalMonthlyIncome,
    savingsRate,
    debtToIncomeRatio,
    netWorth,
    totalAssets,
    totalLiabilities,
    southData,
    westData,
    northData, // Included as northData is used for expenseDetails and personalInformation
    savingsGap, // Use the basic savingsGap
    protectionGapAmount,
    hasWill,
    hasPOA,
    hasHealthcareDirective,
    hasLegacyPlan, // Added as dependency
    currentRetirementSavings,
    savingsGoal,
    yearsUntilRetirement,
    currentAge, // Added as dependency for netWorthScore
  ]);

  // Adapter functions to convert data formats
  const adaptTrendsData = () => {
    const rawTrends = generateTrends();
    return rawTrends.map((trend, index) => ({
      id: `trend-${index}`,
      label: trend.title,
      value: trend.value,
      change: parseFloat(trend.change.replace(/[^0-9.-]/g, '')) || 0,
      direction: trend.direction,
      isPositive: trend.direction === 'up'
    }));
  };

  const adaptAlertsData = () => {
    const rawAlerts = generateAlerts();
    return rawAlerts.map((alert, index) => ({
      id: `alert-${index}`,
      title: alert.title,
      description: alert.description,
      severity: alert.severity as 'low' | 'medium' | 'high' | 'critical'
    }));
  };

  const adaptActionsData = () => {
    const rawActions = generateActions(data);
    return rawActions.map((action, index) => ({
      id: `action-${index}`,
      title: action.title,
      description: action.description,
      priority: action.priority.toLowerCase() as 'low' | 'medium' | 'high',
      icon: action.actions ? '📋' : '✓'
    }));
  };

  const adaptGuidanceData = () => {
    const rawGuidance = generateGuidance(data);
    return rawGuidance.map((guidance, index) => ({
      id: `guidance-${index}`,
      title: guidance.title,
      description: guidance.description,
      icon: guidance.icon
    }));
  };

  // Extract scores and recommendations
  const { overallScore: financialHealthScore, status: financialHealthStatus } = financialHealthResult; // Use overallScore
  const recommendations = financialHealthResult.recommendations;
  // Remove unused categoryScores
  // const categoryScores = financialHealthResult.categoryScores;

  // Map category scores for display - Remove unused financialHealthMetrics
  // Fix any type here if possible, but keeping for now as the structure is dynamic
  // const financialHealthMetrics: {
  //   category: FinancialHealthCategory;
  //   score: number;
  //   status: FinancialHealthScoreStatus;
  //   metrics: { label: string; value: any; status: FinancialHealthScoreStatus }[];
  //   recommendations: FinancialHealthRecommendation[];
  //   description: string;
  // }[] = (Object.entries(categoryScores) as [FinancialHealthCategory, any][]).map(
  //   ([category, scoreDetails]) => ({
  //     category,
  //     score: scoreDetails.score,
  //     status: scoreDetails.status,
  //     description: scoreDetails.description,
  //     // Fix any type here if possible, but keeping for now as the structure is dynamic
  //     metrics: Object.entries(scoreDetails.metrics).map(([label, metric]: [string, any]) => ({
  //       label,
  //       value: metric.value,
  //       status: metric.status,
  //     })),
  //     recommendations: recommendations.filter((rec) => rec.category === category),
  //   })
  // );

  const handleExportPDF = async () => {
    setIsExporting(true);
    const dataToExport: FinancialCompassData = {
      metadata: {},
      financialCompass: {
        north: northData as NorthData, // Cast to NorthData
        east: eastData as Record<string, unknown>, // Keep as Record<string, unknown> or define EastData
        south: southData as SouthData, // Cast to SouthData
        west: westData as WestData, // Cast to WestData
      },
    };

    try {
      // The generatePDF function from enhancedPdfExport.ts seems to expect a different structure
      // It expects `data.sections`. We need to adjust either the data structure passed or the generatePDF function.
      // Assuming the intention is to export the comprehensive data, let's adjust the data structure temporarily
      // to match what generatePDF expects, or call the more appropriate function if available.
      // Looking at enhancedPdfExport.ts, there is an `exportFinancialCompassPDF` function. Let's use that instead.

      // const pdfBlob = await generatePDF(dataToExport as any); // Old call with generatePDF
      const pdfBlob = await generatePDF(dataToExport); // Use the imported generatePDF which is designed for this data structure

      const filename = `financial-compass-summary-${new Date().toISOString().split('T')[0]}.pdf`;
      saveAs(pdfBlob, filename);
    } catch (error) {
      console.error('Error generating PDF:', error);
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <PageContainer theme={theme}>
      <PageHeader theme={theme}>
        <PageTitle theme={theme}>Financial Compass Summary</PageTitle>
        <PageDescription theme={theme}>
          Your comprehensive financial assessment across all compass directions.
        </PageDescription>
      </PageHeader>

      <ContentContainer theme={theme}>
        <HealthScoreCard theme={theme}>
          <HealthScoreHeader>
            <HealthScoreTitle>Financial Health Score</HealthScoreTitle>
            <HealthScoreValue score={financialHealthScore}>
              {financialHealthScore} {/* Fixed prettier issue */}
            </HealthScoreValue>
          </HealthScoreHeader>
          <HealthScoreStatus score={financialHealthScore}>
            {financialHealthStatus}
          </HealthScoreStatus>
          <HealthScoreBar theme={theme}>
            <HealthScoreFill score={financialHealthScore} theme={theme} />
          </HealthScoreBar>
          <HealthScoreDescription theme={theme}>
            Your financial health score is calculated based on your cash flow, savings rate, debt
            management, retirement readiness, protection planning, and estate planning. A higher
            score indicates better overall financial wellness.
          </HealthScoreDescription>
        </HealthScoreCard>

        <KeyMetricsContainer theme={theme}>
          <KeyMetricsTitle>Financial Snapshot</KeyMetricsTitle>
          <KeyMetricsGrid>
            <KeyMetric>
              <KeyMetricValue isPositive={monthlyCashFlow >= 0}>
                {formatCurrency(monthlyCashFlow)}/mo
              </KeyMetricValue>
              <KeyMetricLabel>Cash Flow</KeyMetricLabel>
              <KeyMetricTrend isPositive={monthlyCashFlow >= 0}>
                {monthlyCashFlow >= 0 ? '↑ Positive' : '↓ Negative'}
              </KeyMetricTrend>
            </KeyMetric>

            <KeyMetric>
              <KeyMetricValue isPositive={netWorth >= 0}>
                {formatCurrency(netWorth)} {/* Fixed prettier issue */}
              </KeyMetricValue>
              <KeyMetricLabel>Net Worth</KeyMetricLabel>
              <KeyMetricTrend isPositive={netWorth >= 0}>
                {netWorth >= 0 ? '↑ Positive' : '↓ Negative'}
              </KeyMetricTrend>
            </KeyMetric>

            <KeyMetric>
              <KeyMetricValue isPositive={savingsRate >= 15}>
                {Math.round(savingsRate)}%
              </KeyMetricValue>
              <KeyMetricLabel>Savings Rate</KeyMetricLabel>
              <KeyMetricTrend isPositive={savingsRate >= 15}>
                {savingsRate >= 20
                  ? '↑ Excellent'
                  : savingsRate >= 15
                    ? '↑ Good'
                    : savingsRate >= 10
                      ? '→ Fair'
                      : '↓ Low'}
              </KeyMetricTrend>
            </KeyMetric>

            <KeyMetric>
              <KeyMetricValue isPositive={debtToIncomeRatio <= 36}>
                {Math.round(debtToIncomeRatio)}%
              </KeyMetricValue>
              <KeyMetricLabel>Debt-to-Income</KeyMetricLabel>
              <KeyMetricTrend isPositive={debtToIncomeRatio <= 36}>
                {debtToIncomeRatio <= 20
                  ? '↑ Excellent'
                  : debtToIncomeRatio <= 36
                    ? '↑ Good'
                    : debtToIncomeRatio <= 43
                      ? '→ Fair'
                      : '↓ High'}
              </KeyMetricTrend>
            </KeyMetric>
          </KeyMetricsGrid>
        </KeyMetricsContainer>

        {/* Financial Insights Dashboard */}
        <InsightsDashboard theme={theme}>
          <InsightsDashboardTitle>Financial Insights Dashboard</InsightsDashboardTitle>

          <InsightsGrid>
            {/* Cash Flow Insights */}
            <InsightCard theme={theme}>
              <InsightHeader>
                <InsightTitle>Cash Flow</InsightTitle>
                <InsightIcon status={monthlyCashFlow >= 0 ? 'positive' : 'negative'}>
                  {monthlyCashFlow >= 0 ? '📈' : '📉'}
                </InsightIcon>
              </InsightHeader>
              <InsightValue status={monthlyCashFlow >= 0 ? 'positive' : 'negative'}>
                {formatCurrency(monthlyCashFlow)}/month
              </InsightValue>
              <InsightDescription>
                {monthlyCashFlow >= 0
                  ? `You have a positive cash flow, with ${formatCurrency(monthlyCashFlow)} surplus each month.`
                  : `Your expenses exceed your income by ${formatCurrency(Math.abs(monthlyCashFlow))} each month.`}
              </InsightDescription>
              <InsightMetrics>
                <InsightMetric>
                  <InsightMetricLabel>Income</InsightMetricLabel>
                  <InsightMetricValue>
                    {formatCurrency(calculatedTotalMonthlyIncome)}/mo
                  </InsightMetricValue>
                </InsightMetric>
                <InsightMetric>
                  <InsightMetricLabel>Expenses</InsightMetricLabel>
                  <InsightMetricValue>
                    {formatCurrency(northData.expenseDetails?.totalMonthlyExpenses || '0')}/mo
                  </InsightMetricValue>
                </InsightMetric>
                <InsightMetric>
                  <InsightMetricLabel>Savings Rate</InsightMetricLabel>
                  <InsightMetricValue
                    status={
                      savingsRate >= 15 ? 'positive' : savingsRate >= 5 ? 'warning' : 'negative'
                    }
                  >
                    {Math.round(savingsRate)}%
                  </InsightMetricValue>
                </InsightMetric>
              </InsightMetrics>
              <InsightAction
                onClick={() => updateActiveDirection && updateActiveDirection('north')}
              >
                Review Cash Flow Details →
              </InsightAction>
            </InsightCard>

            {/* Net Worth Insights */}
            <InsightCard theme={theme}>
              <InsightHeader>
                <InsightTitle>Net Worth</InsightTitle>
                <InsightIcon status={netWorth >= 0 ? 'positive' : 'negative'}>
                  {netWorth >= 0 ? '💰' : '⚠️'}
                </InsightIcon>
              </InsightHeader>
              <InsightValue status={netWorth >= 0 ? 'positive' : 'negative'}>
                {formatCurrency(netWorth)}
              </InsightValue>
              <InsightDescription>
                {netWorth >= 0
                  ? `Your assets exceed your liabilities by ${formatCurrency(netWorth)}.`
                  : `Your liabilities exceed your assets by ${formatCurrency(Math.abs(netWorth))}.`}
              </InsightDescription>
              <InsightMetrics>
                <InsightMetric>
                  <InsightMetricLabel>Assets</InsightMetricLabel>
                  <InsightMetricValue>{formatCurrency(totalAssets)}</InsightMetricValue>
                </InsightMetric>
                <InsightMetric>
                  <InsightMetricLabel>Liabilities</InsightMetricLabel>
                  <InsightMetricValue>{formatCurrency(totalLiabilities)}</InsightMetricValue>
                </InsightMetric>
                <InsightMetric>
                  <InsightMetricLabel>Debt-to-Income</InsightMetricLabel>
                  <InsightMetricValue
                    status={
                      debtToIncomeRatio <= 36
                        ? 'positive'
                        : debtToIncomeRatio <= 43
                          ? 'warning'
                          : 'negative'
                    }
                  >
                    {Math.round(debtToIncomeRatio)}%
                  </InsightMetricValue>
                </InsightMetric>
              </InsightMetrics>
              <InsightAction
                onClick={() => updateActiveDirection && updateActiveDirection('north')}
              >
                Review Net Worth Details →
              </InsightAction>
            </InsightCard>

            {/* Retirement Insights */}
            <InsightCard theme={theme}>
              <InsightHeader>
                <InsightTitle>Retirement Readiness</InsightTitle>
                <InsightIcon status={savingsGap === 0 ? 'positive' : 'warning'}>
                  {savingsGap === 0 ? '🎯' : '🏦'}
                </InsightIcon>
              </InsightHeader>
              <InsightValue status={savingsGap === 0 ? 'positive' : 'warning'}>
                {savingsGoal > 0 ? Math.round((currentRetirementSavings / savingsGoal) * 100) : 0}%
                of Goal
              </InsightValue>
              <InsightDescription>
                {savingsGap === 0
                  ? `You're on track to meet your retirement savings goal of ${formatCurrency(savingsGoal)}.` // Escape apostrophe
                  : `You have a retirement savings gap of ${formatCurrency(savingsGap)} to reach your goal of ${formatCurrency(savingsGoal)}.`}
              </InsightDescription>
              <InsightMetrics>
                <InsightMetric>
                  <InsightMetricLabel>Current Savings</InsightMetricLabel>
                  <InsightMetricValue>
                    {formatCurrency(currentRetirementSavings)}
                  </InsightMetricValue>
                </InsightMetric>
                <InsightMetric>
                  <InsightMetricLabel>Years to Retirement</InsightMetricLabel>
                  <InsightMetricValue>{yearsUntilRetirement}</InsightMetricValue>
                </InsightMetric>
                <InsightMetric>
                  <InsightMetricLabel>Monthly Need</InsightMetricLabel>
                  <InsightMetricValue>
                    {yearsUntilRetirement > 0 && savingsGap > 0
                      ? formatCurrency(savingsGap / (yearsUntilRetirement * 12))
                      : '$0'}
                  </InsightMetricValue>
                </InsightMetric>
              </InsightMetrics>
              <InsightAction onClick={() => updateActiveDirection && updateActiveDirection('east')}>
                Review Retirement Details →
              </InsightAction>
            </InsightCard>

            {/* Protection Insights */}
            <InsightCard theme={theme}>
              <InsightHeader>
                <InsightTitle>Protection Planning</InsightTitle>
                <InsightIcon status={protectionGapAmount === 0 ? 'positive' : 'warning'}>
                  {protectionGapAmount === 0 ? '🛡️' : '🔍'}
                </InsightIcon>
              </InsightHeader>
              <InsightValue status={protectionGapAmount === 0 ? 'positive' : 'warning'}>
                {protectionGapAmount === 0 ? 'Adequate' : 'Gap Detected'}
              </InsightValue>
              <InsightDescription>
                {protectionGapAmount === 0
                  ? 'Your insurance coverage appears adequate for your current needs.'
                  : `You have an insurance protection gap of ${formatCurrency(protectionGapAmount)} that should be addressed.`}
              </InsightDescription>
              <InsightMetrics>
                <InsightMetric>
                  <InsightMetricLabel>Life Insurance</InsightMetricLabel>
                  <InsightMetricValue
                    status={
                      southData?.insuranceAnalysis?.lifeInsurance?.hasInsurance
                        ? 'positive'
                        : 'warning'
                    }
                  >
                    {southData?.insuranceAnalysis?.lifeInsurance?.hasInsurance
                      ? 'In Place'
                      : 'Needed'}
                  </InsightMetricValue>
                </InsightMetric>
                <InsightMetric>
                  <InsightMetricLabel>Health Insurance</InsightMetricLabel>
                  <InsightMetricValue
                    status={
                      southData?.insuranceAnalysis?.healthInsurance?.hasInsurance
                        ? 'positive'
                        : 'warning'
                    }
                  >
                    {southData?.insuranceAnalysis?.healthInsurance?.hasInsurance
                      ? 'In Place'
                      : 'Needed'}
                  </InsightMetricValue>
                </InsightMetric>
                <InsightMetric>
                  <InsightMetricLabel>Disability</InsightMetricLabel>
                  <InsightMetricValue
                    status={
                      southData?.insuranceAnalysis?.disabilityInsurance?.hasInsurance
                        ? 'positive'
                        : 'warning'
                    }
                  >
                    {southData?.insuranceAnalysis?.disabilityInsurance?.hasInsurance
                      ? 'In Place'
                      : 'Needed'}
                  </InsightMetricValue>
                </InsightMetric>
              </InsightMetrics>
              <InsightAction
                onClick={() => updateActiveDirection && updateActiveDirection('south')}
              >
                Review Protection Details →
              </InsightAction>
            </InsightCard>
          </InsightsGrid>
        </InsightsDashboard>

        <FinancialGoalsContainer theme={theme}>
          <FinancialGoalsTitle>Financial Goals Progress</FinancialGoalsTitle>
          <FinancialGoalsGrid>
            <GoalProgressCard theme={theme}>
              <GoalProgressHeader>
                <GoalProgressTitle>Retirement Readiness</GoalProgressTitle>
                <GoalProgressIcon status={savingsGap === 0 ? 'complete' : 'in-progress'}>
                  {savingsGap === 0 ? '🎯' : '🏦'}
                </GoalProgressIcon>
              </GoalProgressHeader>
              <GoalProgressBar theme={theme}>
                <GoalProgressFill
                  progress={
                    savingsGoal > 0
                      ? Math.min(100, Math.round((currentRetirementSavings / savingsGoal) * 100))
                      : 0
                  }
                  theme={theme}
                />
              </GoalProgressBar>
              <GoalProgressValue>
                {savingsGoal > 0 ? Math.round((currentRetirementSavings / savingsGoal) * 100) : 0}%
                Complete
              </GoalProgressValue>
              <GoalProgressDetails>
                <GoalProgressDetail>
                  <GoalProgressDetailLabel>Current:</GoalProgressDetailLabel>
                  <GoalProgressDetailValue>
                    {formatCurrency(currentRetirementSavings)}
                  </GoalProgressDetailValue>
                </GoalProgressDetail>
                <GoalProgressDetail>
                  <GoalProgressDetailLabel>Goal:</GoalProgressDetailLabel>
                  <GoalProgressDetailValue>{formatCurrency(savingsGoal)}</GoalProgressDetailValue>
                </GoalProgressDetail>
                <GoalProgressDetail>
                  <GoalProgressDetailLabel>Gap:</GoalProgressDetailLabel>
                  <GoalProgressDetailValue>{formatCurrency(savingsGap)}</GoalProgressDetailValue>
                </GoalProgressDetail>
              </GoalProgressDetails>
              <GoalProgressAction
                onClick={() => updateActiveDirection && updateActiveDirection('east')}
              >
                View Retirement Plan →
              </GoalProgressAction>
            </GoalProgressCard>

            <GoalProgressCard theme={theme}>
              <GoalProgressHeader>
                <GoalProgressTitle>Protection Planning</GoalProgressTitle>
                <GoalProgressIcon status={protectionGapAmount === 0 ? 'complete' : 'in-progress'}>
                  {protectionGapAmount === 0 ? '🛡️' : '🔍'}
                </GoalProgressIcon>
              </GoalProgressHeader>
              <GoalProgressBar theme={theme}>
                <GoalProgressFill progress={protectionGapAmount === 0 ? 100 : 50} theme={theme} />
              </GoalProgressBar>
              <GoalProgressValue>
                {protectionGapAmount === 0 ? '100% Complete' : 'Gap Detected'}
              </GoalProgressValue>
              <GoalProgressDetails>
                <GoalProgressDetail>
                  <GoalProgressDetailLabel>Life Insurance:</GoalProgressDetailLabel>
                  <GoalProgressDetailValue
                    status={
                      southData?.insuranceAnalysis?.lifeInsurance?.hasInsurance
                        ? 'positive'
                        : 'negative'
                    }
                  >
                    {southData?.insuranceAnalysis?.lifeInsurance?.hasInsurance ? '✓' : '✗'}
                  </GoalProgressDetailValue>
                </GoalProgressDetail>
                <GoalProgressDetail>
                  <GoalProgressDetailLabel>Health Insurance:</GoalProgressDetailLabel>
                  <GoalProgressDetailValue
                    status={
                      southData?.insuranceAnalysis?.healthInsurance?.hasInsurance
                        ? 'positive'
                        : 'negative'
                    }
                  >
                    {southData?.insuranceAnalysis?.healthInsurance?.hasInsurance ? '✓' : '✗'}
                  </GoalProgressDetailValue>
                </GoalProgressDetail>
                <GoalProgressDetail>
                  <GoalProgressDetailLabel>Disability:</GoalProgressDetailLabel>
                  <GoalProgressDetailValue
                    status={
                      southData?.insuranceAnalysis?.disabilityInsurance?.hasInsurance
                        ? 'positive'
                        : 'negative'
                    }
                  >
                    {southData?.insuranceAnalysis?.disabilityInsurance?.hasInsurance ? '✓' : '✗'}
                  </GoalProgressDetailValue>
                </GoalProgressDetail>
              </GoalProgressDetails>
              <GoalProgressAction
                onClick={() => updateActiveDirection && updateActiveDirection('south')}
              >
                Review Protection Plan →
              </GoalProgressAction>
            </GoalProgressCard>

            <GoalProgressCard theme={theme}>
              <GoalProgressHeader>
                <GoalProgressTitle>Estate Planning</GoalProgressTitle>
                <GoalProgressIcon status={hasWill && hasPOA ? 'complete' : 'in-progress'}>
                  {hasWill && hasPOA ? '📜' : '📝'}
                </GoalProgressIcon>
              </GoalProgressHeader>
              <GoalProgressBar theme={theme}>
                <GoalProgressFill
                  progress={hasWill && hasPOA ? 100 : hasWill || hasPOA ? 50 : 0}
                  theme={theme}
                />
              </GoalProgressBar>
              <GoalProgressValue>
                {hasWill && hasPOA
                  ? 'Complete'
                  : hasWill || hasPOA
                    ? 'Partially Complete'
                    : 'Not Started'}
              </GoalProgressValue>
              <GoalProgressDetails>
                <GoalProgressDetail>
                  <GoalProgressDetailLabel>Will:</GoalProgressDetailLabel>
                  <GoalProgressDetailValue status={hasWill ? 'positive' : 'negative'}>
                    {hasWill ? '✓' : '✗'}
                  </GoalProgressDetailValue>
                </GoalProgressDetail>
                <GoalProgressDetail>
                  <GoalProgressDetailLabel>Power of Attorney:</GoalProgressDetailLabel>
                  <GoalProgressDetailValue status={hasPOA ? 'positive' : 'negative'}>
                    {hasPOA ? '✓' : '✗'}
                  </GoalProgressDetailValue>
                </GoalProgressDetail>
                <GoalProgressDetail>
                  <GoalProgressDetailLabel>Legacy Plan:</GoalProgressDetailLabel>
                  <GoalProgressDetailValue status={hasLegacyPlan ? 'positive' : 'negative'}>
                    {hasLegacyPlan ? '✓' : '✗'}
                  </GoalProgressDetailValue>
                </GoalProgressDetail>
              </GoalProgressDetails>
              <GoalProgressAction
                onClick={() => updateActiveDirection && updateActiveDirection('west')}
              >
                Complete Estate Plan →
              </GoalProgressAction>
            </GoalProgressCard>
          </FinancialGoalsGrid>
        </FinancialGoalsContainer>

        <DirectionScoresContainer>
          <DirectionScoreCard
            theme={theme}
            direction="north"
            onClick={() => updateActiveDirection && updateActiveDirection('north')}
          >
            <DirectionTitle>North</DirectionTitle>
            <DirectionDescription>Current Position</DirectionDescription>
            <DirectionMetrics>
              <DirectionMetric>
                <MetricLabel>Cash Flow</MetricLabel>
                <MetricValue isPositive={monthlyCashFlow >= 0}>
                  {formatCurrency(monthlyCashFlow)}/mo
                </MetricValue>
              </DirectionMetric>
              <DirectionMetric>
                <MetricLabel>Net Worth</MetricLabel>
                <MetricValue isPositive={netWorth >= 0}>{formatCurrency(netWorth)}</MetricValue>
              </DirectionMetric>
            </DirectionMetrics>
          </DirectionScoreCard>

          <DirectionScoreCard
            theme={theme}
            direction="east"
            onClick={() => updateActiveDirection && updateActiveDirection('east')}
          >
            <DirectionTitle>East</DirectionTitle>
            <DirectionDescription>Retirement Vision</DirectionDescription>
            <DirectionMetrics>
              <DirectionMetric>
                <MetricLabel>Savings Progress</MetricLabel>
                <MetricValue isPositive={true}>
                  {savingsGoal > 0 ? Math.round((currentRetirementSavings / savingsGoal) * 100) : 0}
                  %
                </MetricValue>
              </DirectionMetric>
              <DirectionMetric>
                <MetricLabel>Years to Retirement</MetricLabel>
                <MetricValue isPositive={true}>{yearsUntilRetirement}</MetricValue>
              </DirectionMetric>
            </DirectionMetrics>
          </DirectionScoreCard>

          <DirectionScoreCard
            theme={theme}
            direction="south"
            onClick={() => updateActiveDirection && updateActiveDirection('south')}
          >
            <DirectionTitle>South</DirectionTitle>
            <DirectionDescription>Protection & Risks</DirectionDescription>
            <DirectionMetrics>
              <DirectionMetric>
                <MetricLabel>Protection Gap</MetricLabel>
                <MetricValue isPositive={protectionGapAmount === 0}>
                  {formatCurrency(protectionGapAmount)}
                </MetricValue>
              </DirectionMetric>
              <DirectionMetric>
                <MetricLabel>Insurance Status</MetricLabel>
                <MetricValue isPositive={protectionGapAmount === 0}>
                  {protectionGapAmount === 0 ? 'Adequate' : 'Review Needed'}
                </MetricValue>
              </DirectionMetric>
            </DirectionMetrics>
          </DirectionScoreCard>

          <DirectionScoreCard
            theme={theme}
            direction="west"
            onClick={() => updateActiveDirection && updateActiveDirection('west')}
          >
            <DirectionTitle>West</DirectionTitle>
            <DirectionDescription>Legacy Planning</DirectionDescription>
            <DirectionMetrics>
              <DirectionMetric>
                <MetricLabel>Estate Planning</MetricLabel>
                {/* Remove redundant double negation */}
                <MetricValue isPositive={hasWill && hasPOA}>
                  {hasWill && hasPOA ? 'In Place' : 'Incomplete'}
                </MetricValue>
              </DirectionMetric>
              <DirectionMetric>
                <MetricLabel>Legacy Status</MetricLabel>
                <MetricValue isPositive={hasLegacyPlan}>
                  {hasLegacyPlan ? 'Defined' : 'Not Defined'}
                </MetricValue>
              </DirectionMetric>
            </DirectionMetrics>
          </DirectionScoreCard>
        </DirectionScoresContainer>

        <FinancialStrengthsWeaknesses theme={theme}>
          <FinancialStrengthsWeaknessesTitle>
            Financial Strengths & Weaknesses
          </FinancialStrengthsWeaknessesTitle>
          <StrengthsWeaknessesGrid>
            <StrengthsSection theme={theme}>
              <StrengthsTitle>Your Financial Strengths</StrengthsTitle>
              <StrengthsList>
                {/* Remove redundant double negations */}
                {monthlyCashFlow >= 0 && (
                  <StrengthItem theme={theme}>
                    <StrengthIcon>💰</StrengthIcon>
                    <StrengthText>
                      <strong>Positive Cash Flow:</strong> You have a monthly surplus of{' '}
                      {formatCurrency(monthlyCashFlow)}, which provides flexibility and opportunity
                      for saving and investing.
                    </StrengthText>
                  </StrengthItem>
                )}

                {savingsRate >= 15 && (
                  <StrengthItem theme={theme}>
                    <StrengthIcon>📈</StrengthIcon>
                    <StrengthText>
                      <strong>Strong Savings Rate:</strong> Your savings rate of{' '}
                      {Math.round(savingsRate)}% exceeds the recommended 15%, positioning you well
                      for future financial goals.
                    </StrengthText>
                  </StrengthItem>
                )}

                {debtToIncomeRatio <= 36 && (
                  <StrengthItem theme={theme}>
                    <StrengthIcon>⚖️</StrengthIcon>
                    <StrengthText>
                      <strong>Healthy Debt-to-Income Ratio:</strong> Your debt-to-income ratio of{' '}
                      {Math.round(debtToIncomeRatio)}% is within the recommended range, indicating
                      good debt management.
                    </StrengthText>
                  </StrengthItem>
                )}

                {netWorth > 0 && (
                  <StrengthItem theme={theme}>
                    <StrengthIcon>💎</StrengthIcon>
                    <StrengthText>
                      <strong>Positive Net Worth:</strong> Your net worth of{' '}
                      {formatCurrency(netWorth)} shows that your assets exceed your liabilities, a
                      fundamental indicator of financial health.
                    </StrengthText>
                  </StrengthItem>
                )}

                {savingsGap === 0 && (
                  <StrengthItem theme={theme}>
                    <StrengthIcon>🎯</StrengthIcon>
                    <StrengthText>
                      <strong>On Track for Retirement:</strong> You've already reached your
                      retirement savings goal of {formatCurrency(savingsGoal)}.
                    </StrengthText>
                  </StrengthItem>
                )}

                {protectionGapAmount === 0 && (
                  <StrengthItem theme={theme}>
                    <StrengthIcon>🛡️</StrengthIcon>
                    <StrengthText>
                      <strong>Adequate Insurance Protection:</strong> Your insurance coverage
                      appears sufficient for your current needs, providing important financial
                      security.
                    </StrengthText>
                  </StrengthItem>
                )}

                {hasWill && hasPOA && (
                  <StrengthItem theme={theme}>
                    <StrengthIcon>📜</StrengthIcon>
                    <StrengthText>
                      <strong>Complete Estate Planning:</strong> You have both a will and power of
                      attorney in place, ensuring your wishes are documented and your loved ones are
                      protected.
                    </StrengthText>
                  </StrengthItem>
                )}

                {/* Fallback if no strengths are identified */}
                {!( // Use single negation
                  monthlyCashFlow >= 0 ||
                  savingsRate >= 15 ||
                  debtToIncomeRatio <= 36 ||
                  netWorth > 0 ||
                  savingsGap === 0 ||
                  protectionGapAmount === 0 ||
                  (hasWill && hasPOA)
                ) && (
                    <StrengthItem theme={theme}>
                      <StrengthIcon>🌱</StrengthIcon>
                      <StrengthText>
                        <strong>Taking Control:</strong> By assessing your financial situation, you've
                        taken the important first step toward financial wellness.
                      </StrengthText>
                    </StrengthItem>
                  )}
              </StrengthsList>
            </StrengthsSection>

            <WeaknessesSection theme={theme}>
              <WeaknessesTitle>Areas for Improvement</WeaknessesTitle>
              <WeaknessesList>
                {monthlyCashFlow < 0 && (
                  <WeaknessItem theme={theme}>
                    <WeaknessIcon>📉</WeaknessIcon>
                    <WeaknessText>
                      <strong>Negative Cash Flow:</strong> Your expenses exceed your income by{' '}
                      {formatCurrency(Math.abs(monthlyCashFlow))} monthly, which can lead to
                      increasing debt if not addressed.
                    </WeaknessText>
                  </WeaknessItem>
                )}

                {savingsRate < 15 && (
                  <WeaknessItem theme={theme}>
                    <WeaknessIcon>💸</WeaknessIcon>
                    <WeaknessText>
                      <strong>Low Savings Rate:</strong> Your current savings rate of{' '}
                      {Math.round(savingsRate)}% is below the recommended 15-20%, which may delay
                      achieving your financial goals.
                    </WeaknessText>
                  </WeaknessItem>
                )}

                {debtToIncomeRatio > 36 && (
                  <WeaknessItem theme={theme}>
                    <WeaknessIcon>⚠️</WeaknessIcon>
                    <WeaknessText>
                      <strong>High Debt-to-Income Ratio:</strong> Your debt-to-income ratio of{' '}
                      {Math.round(debtToIncomeRatio)}% exceeds the recommended maximum of 36%, which
                      may limit your financial flexibility.
                    </WeaknessText>
                  </WeaknessItem>
                )}

                {netWorth <= 0 && (
                  <WeaknessItem theme={theme}>
                    <WeaknessIcon>🔻</WeaknessIcon>
                    <WeaknessText>
                      <strong>Negative Net Worth:</strong> Your liabilities exceed your assets by{' '}
                      {formatCurrency(Math.abs(netWorth))}, indicating a need to focus on debt
                      reduction and asset building.
                    </WeaknessText>
                  </WeaknessItem>
                )}

                {savingsGap > 0 && (
                  <WeaknessItem theme={theme}>
                    <WeaknessIcon>🏦</WeaknessIcon>
                    <WeaknessText>
                      <strong>Retirement Savings Gap:</strong> You have a{' '}
                      {formatCurrency(savingsGap)} gap to reach your retirement savings goal of{' '}
                      {formatCurrency(savingsGoal)}.
                    </WeaknessText> {/* Changed StrengthText to WeaknessText */}
                  </WeaknessItem>
                )}

                {protectionGapAmount > 0 && (
                  <WeaknessItem theme={theme}>
                    <WeaknessIcon>🔍</WeaknessIcon>
                    <WeaknessText>
                      <strong>Insurance Protection Gap:</strong> Your current insurance coverage has
                      a gap of {formatCurrency(protectionGapAmount)}, potentially leaving you
                      exposed to financial risks.
                    </WeaknessText>
                  </WeaknessItem>
                )}

                {!(hasWill && hasPOA) && ( // Use single negation
                  <WeaknessItem theme={theme}>
                    <WeaknessIcon>📝</WeaknessIcon>
                    <WeaknessText>
                      <strong>Incomplete Estate Planning:</strong> You{' '}
                      {!hasWill && !hasPOA
                        ? "don't have a will or power of attorney" // Escape apostrophe
                        : !hasWill
                          ? "don't have a will" // Escape apostrophe
                          : "don't have power of attorney documents"}{' '} {/* Escape apostrophe */}
                      in place, which could create complications for your loved ones.
                    </WeaknessText>
                  </WeaknessItem>
                )}

                {/* Fallback if no weaknesses are identified */}
                {!( // Use single negation
                  monthlyCashFlow < 0 ||
                  savingsRate < 15 ||
                  debtToIncomeRatio > 36 ||
                  netWorth <= 0 ||
                  savingsGap > 0 ||
                  protectionGapAmount > 0 ||
                  !hasWill ||
                  !hasPOA
                ) && (
                    <WeaknessItem theme={theme}>
                      <WeaknessIcon>🌟</WeaknessIcon>
                      <WeaknessText>
                        <strong>Excellent Work:</strong> You've addressed the major financial planning
                        areas. Consider focusing on optimizing your strategy and exploring advanced
                        financial planning techniques.
                      </WeaknessText> {/* Escape apostrophe */}
                    </WeaknessItem>
                  )}
              </WeaknessesList>
            </WeaknessesSection>
          </StrengthsWeaknessesGrid>
        </FinancialStrengthsWeaknesses>

        <FinancialOpportunities theme={theme}>
          <FinancialOpportunitiesTitle>Financial Opportunities</FinancialOpportunitiesTitle>
          <OpportunitiesList>
            {monthlyCashFlow > 0 && (
              <OpportunityItem theme={theme}>
                <OpportunityIcon>💼</OpportunityIcon>
                <OpportunityContent>
                  <OpportunityTitle>Invest Your Surplus</OpportunityTitle>
                  <OpportunityDescription>
                    With a monthly surplus of {formatCurrency(monthlyCashFlow)}, you have an
                    opportunity to accelerate your wealth building. Consider allocating this surplus
                    strategically: 50% to retirement accounts, 30% to an emergency fund until you
                    have 3-6 months of expenses saved, and 20% to short-term goals or additional
                    debt repayment.
                  </OpportunityDescription>
                </OpportunityContent>
              </OpportunityItem>
            )}

            {debtToIncomeRatio > 20 && (
              <OpportunityItem theme={theme}>
                <OpportunityIcon>📊</OpportunityIcon>
                <OpportunityContent>
                  <OpportunityTitle>Debt Optimization Strategy</OpportunityTitle>
                  <OpportunityDescription>
                    With a debt-to-income ratio of {Math.round(debtToIncomeRatio)}%, you could
                    benefit from a structured debt reduction plan. Consider the debt avalanche
                    method (focusing on highest interest debt first) or debt consolidation to
                    potentially lower your interest rates and accelerate debt payoff.
                  </OpportunityDescription>
                </OpportunityContent>
              </OpportunityItem>
            )}

            {savingsGap > 0 && yearsUntilRetirement > 0 && (
              <OpportunityItem theme={theme}>
                <OpportunityIcon>🚀</OpportunityIcon>
                <OpportunityContent>
                  <OpportunityTitle>Retirement Catch-Up Strategy</OpportunityTitle>
                  <OpportunityDescription>
                    To close your retirement savings gap of {formatCurrency(savingsGap)} over the
                    next {yearsUntilRetirement} years, you would need to save an additional{' '}
                    {formatCurrency(savingsGap / (yearsUntilRetirement * 12))} monthly. Consider
                    maximizing tax-advantaged accounts like 401(k)s and IRAs, and if you're over 50,
                    take advantage of catch-up contributions.
                  </OpportunityDescription>
                </OpportunityContent>
              </OpportunityItem>
            )}

            {!(hasWill || hasPOA) && ( // Use single negation and ||
              <OpportunityItem theme={theme}>
                <OpportunityIcon>⚖️</OpportunityIcon>
                <OpportunityContent>
                  <OpportunityTitle>Estate Planning Package</OpportunityTitle>
                  <OpportunityDescription>
                    Many attorneys offer comprehensive estate planning packages that include a will,
                    power of attorney, healthcare directive, and other essential documents at a
                    bundled rate. This is often more cost-effective than creating these documents
                    separately and ensures a coordinated approach to your estate plan.
                  </OpportunityDescription>
                </OpportunityContent>
              </OpportunityItem>
            )}
          </OpportunitiesList>
        </FinancialOpportunities>

        {/* Financial Trends, Alerts, Actions, and Guidance Sections */}
        <TrendsSection title="Financial Trends" trends={adaptTrendsData()} theme={theme} />
        <AlertsSection alerts={adaptAlertsData()} theme={theme} />
        <ActionsSection actions={adaptActionsData()} theme={theme} />
        <GuidanceSection guidanceItems={adaptGuidanceData()} theme={theme} />

        <ButtonGroup>
          <ExportButton onClick={handleExportPDF} disabled={isExporting} theme={theme}>
            {isExporting ? 'Generating PDF...' : 'Export Comprehensive Financial Report'}
          </ExportButton>
        </ButtonGroup>
      </ContentContainer>
    </PageContainer>
  );
};

// Styled components
const PageContainer = styled.div<{ theme: any }>`
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
`;

const PageHeader = styled.div<{ theme: any }>`
  margin-bottom: 32px;
  text-align: center;
`;

const PageTitle = styled.h1<{ theme: any }>`
  margin: 0 0 8px 0;
  font-size: 2.2rem;
  color: ${(props) => props.theme.colors.primary.main};
`;

const PageDescription = styled.p<{ theme: any }>`
  margin: 0;
  font-size: 1.1rem;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const ContentContainer = styled.div<{ theme: any }>`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const HealthScoreCard = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  text-align: center;
`;

const HealthScoreHeader = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
`;

const HealthScoreTitle = styled.h2`
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
`;

const HealthScoreValue = styled.div<{ score: number }>`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 2rem;
  font-weight: 700;
  background-color: ${(props) => {
    if (props.score >= 80) return '#4CAF50';
    if (props.score >= 60) return '#8BC34A';
    if (props.score >= 40) return '#FFC107';
    if (props.score >= 20) return '#FF9800';
    return '#F44336';
  }};
  color: white;
`;

const HealthScoreStatus = styled.div<{ score: number }>`
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: ${(props) => {
    if (props.score >= 80) return '#4CAF50';
    if (props.score >= 60) return '#8BC34A';
    if (props.score >= 40) return '#FFC107';
    if (props.score >= 20) return '#FF9800';
    return '#F44336';
  }};
`;

const HealthScoreBar = styled.div<{ theme: any }>`
  height: 12px;
  background-color: ${(props) => props.theme.colors.background.main};
  border-radius: 6px;
  overflow: hidden;
`;

const HealthScoreFill = styled.div<{ score: number; theme: any }>`
  height: 100%;
  width: ${(props) => props.score}%;
  background-color: ${(props) => {
    if (props.score >= 80) return '#4CAF50';
    if (props.score >= 60) return '#8BC34A';
    if (props.score >= 40) return '#FFC107';
    if (props.score >= 20) return '#FF9800';
    return '#F44336';
  }};
`;

const DirectionScoresContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
`;

const DirectionScoreCard = styled.div<{ theme: any; direction: string }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border-left: 4px solid
    ${(props) => {
    switch (props.direction) {
      case 'north':
        return props.theme.colors.primary.main;
      case 'east':
        return props.theme.colors.secondary.main;
      case 'south':
        return props.theme.colors.error.main;
      case 'west':
        return props.theme.colors.warning.main;
      default:
        return props.theme.colors.primary.main;
    }
  }};
  cursor: pointer;
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
`;

const DirectionTitle = styled.h3`
  margin: 0 0 4px 0;
  font-size: 1.3rem;
  font-weight: 600;
`;

const DirectionDescription = styled.p`
  margin: 0 0 16px 0;
  font-size: 0.9rem;
  color: #666;
`;

const DirectionMetrics = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const DirectionMetric = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const MetricLabel = styled.div`
  font-size: 0.9rem;
  font-weight: 500;
`;

const MetricValue = styled.div<{ isPositive: boolean }>`
  font-weight: 600;
  color: ${(props) => (props.isPositive ? '#4CAF50' : '#F44336')};
`;

const KeyMetricTrend = styled.div<{ isPositive: boolean }>`
  font-size: 0.8rem;
  color: ${(props) => (props.isPositive ? '#4CAF50' : '#F44336')};
  background-color: ${(props) =>
    props.isPositive ? 'rgba(76, 175, 80, 0.1)' : 'rgba(244, 67, 54, 0.1)'};
  padding: 2px 8px;
  border-radius: 12px;
`;

const HealthScoreDescription = styled.p`
  margin-top: 16px;
  font-size: 0.9rem;
  color: ${(props) => props.theme.colors.text.secondary};
  text-align: center;
`;

// Financial Insights Dashboard Styled Components
const InsightsDashboard = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const InsightsDashboardTitle = styled.h2`
  margin: 0 0 24px 0;
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
`;

const InsightsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
`;

const InsightCard = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
`;

const InsightHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const InsightTitle = styled.h3`
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
`;

const InsightIcon = styled.div<{ status: 'positive' | 'negative' | 'warning' }>`
  font-size: 1.5rem;
  color: ${(props) => {
    switch (props.status) {
      case 'positive':
        return '#4CAF50';
      case 'negative':
        return '#F44336';
      case 'warning':
        return '#FFC107';
      default:
        return '#4CAF50';
    }
  }};
`;

const InsightValue = styled.div<{ status: 'positive' | 'negative' | 'warning' }>`
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 12px;
  color: ${(props) => {
    switch (props.status) {
      case 'positive':
        return '#4CAF50';
      case 'negative':
        return '#F44336';
      case 'warning':
        return '#FFC107';
      default:
        return '#4CAF50';
    }
  }};
`;

const InsightDescription = styled.p`
  margin: 0 0 16px 0;
  font-size: 0.9rem;
  color: ${(props) => props.theme.colors.text.secondary};
  flex-grow: 1;
`;

const InsightMetrics = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  margin-bottom: 16px;
`;

const InsightMetric = styled.div`
  text-align: center;
`;

const InsightMetricLabel = styled.div`
  font-size: 0.8rem;
  color: ${(props) => props.theme.colors.text.secondary};
  margin-bottom: 4px;
`;

const InsightMetricValue = styled.div<{ status?: 'positive' | 'negative' | 'warning' }>`
  font-weight: 600;
  font-size: 0.9rem;
  color: ${(props) => {
    if (!props.status) return props.theme.colors.text.primary;
    switch (props.status) {
      case 'positive':
        return '#4CAF50';
      case 'negative':
        return '#F44336';
      case 'warning':
        return '#FFC107';
      default:
        return props.theme.colors.text.primary;
    }
  }};
`;

const InsightAction = styled.button`
  background: none;
  border: none;
  color: ${(props) => props.theme.colors.primary.main};
  font-weight: 600;
  font-size: 0.9rem;
  padding: 8px 0;
  cursor: pointer;
  text-align: right;
  transition: color 0.2s ease;

  &:hover {
    color: ${(props) => props.theme.colors.primary.dark};
    text-decoration: underline;
  }
`;

// Financial Snapshot Styled Components
const KeyMetricsContainer = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const KeyMetricsTitle = styled.h2`
  margin: 0 0 20px 0;
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
`;

const KeyMetricsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 16px;
`;

const KeyMetric = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
`;

const KeyMetricValue = styled.div`
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 4px;
`;

const KeyMetricLabel = styled.div`
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 4px;
`;

// Financial Goals Progress Styled Components
const FinancialGoalsContainer = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const FinancialGoalsTitle = styled.h2`
  margin: 0 0 24px 0;
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
`;

const FinancialGoalsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
`;

const GoalProgressCard = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const GoalProgressHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const GoalProgressTitle = styled.h3`
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
`;

const GoalProgressIcon = styled.div<{ status: 'complete' | 'in-progress' | 'not-started' }>`
  font-size: 1.5rem;
  color: ${(props) => {
    switch (props.status) {
      case 'complete':
        return '#4CAF50';
      case 'in-progress':
        return '#FFC107';
      case 'not-started':
        return '#F44336';
      default:
        return '#4CAF50';
    }
  }};
`;

const GoalProgressBar = styled.div<{ theme: any }>`
  height: 8px;
  background-color: ${(props) => props.theme.colors.background.main};
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
`;

const GoalProgressFill = styled.div<{ progress: number; theme: any }>`
  height: 100%;
  width: ${(props) => props.progress}%;
  background-color: ${(props) => {
    if (props.progress >= 100) return '#4CAF50';
    if (props.progress >= 75) return '#8BC34A';
    if (props.progress >= 50) return '#FFC107';
    if (props.progress >= 25) return '#FF9800';
    return '#F44336';
  }};
  transition: width 0.5s ease;
`;

const GoalProgressValue = styled.div`
  font-size: 0.9rem;
  font-weight: 600;
  text-align: right;
  margin-bottom: 16px;
`;

const GoalProgressDetails = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
`;

const GoalProgressDetail = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const GoalProgressDetailLabel = styled.div`
  font-size: 0.9rem;
`;

const GoalProgressDetailValue = styled.div<{ status?: 'positive' | 'negative' }>`
  font-size: 0.9rem;
  font-weight: 600;
  color: ${(props) =>
    props.status === 'negative' ? '#F44336' : props.status === 'positive' ? '#4CAF50' : 'inherit'};
`;

const GoalProgressAction = styled.button`
  background: none;
  border: none;
  color: ${(props) => props.theme.colors.primary.main};
  font-weight: 600;
  font-size: 0.9rem;
  padding: 8px 0;
  cursor: pointer;
  text-align: right;
  width: 100%;
  transition: color 0.2s ease;

  &:hover {
    color: ${(props) => props.theme.colors.primary.dark};
    text-decoration: underline;
  }
`;

// Financial Strengths & Weaknesses Styled Components
const FinancialStrengthsWeaknesses = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const FinancialStrengthsWeaknessesTitle = styled.h2`
  margin: 0 0 24px 0;
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
`;

const StrengthsWeaknessesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
`;

const StrengthsSection = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  padding: 20px;
  border-left: 4px solid #4caf50;
`;

const StrengthsTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #4caf50;
`;

const StrengthsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const StrengthItem = styled.div<{ theme: any }>`
  display: flex;
  gap: 12px;
  padding: 12px;
  background-color: rgba(76, 175, 80, 0.05);
  border-radius: 6px;
`;

const StrengthIcon = styled.div`
  font-size: 1.5rem;
  flex-shrink: 0;
`;

const StrengthText = styled.div`
  font-size: 0.9rem;
  line-height: 1.4;
`;

const WeaknessesSection = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  padding: 20px;
  border-left: 4px solid #f44336;
`;

const WeaknessesTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #f44336;
`;

const WeaknessesList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const WeaknessItem = styled.div<{ theme: any }>`
  display: flex;
  gap: 12px;
  padding: 12px;
  background-color: rgba(244, 67, 54, 0.05);
  border-radius: 6px;
`;

const WeaknessIcon = styled.div`
  font-size: 1.5rem;
  flex-shrink: 0;
`;

const WeaknessText = styled.div`
  font-size: 0.9rem;
  line-height: 1.4;
`;

// Financial Opportunities Styled Components
const FinancialOpportunities = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const FinancialOpportunitiesTitle = styled.h2`
  margin: 0 0 24px 0;
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
`;

const OpportunitiesList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const OpportunityItem = styled.div<{ theme: any }>`
  display: flex;
  gap: 16px;
  padding: 16px;
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  border-left: 4px solid ${(props) => props.theme.colors.primary.main};
`;

const OpportunityIcon = styled.div`
  font-size: 2rem;
  flex-shrink: 0;
  color: ${(props) => props.theme.colors.primary.main};
`;

const OpportunityContent = styled.div`
  flex: 1;
`;

const OpportunityTitle = styled.h3`
  margin: 0 0 8px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: ${(props) => props.theme.colors.primary.main};
`;

const OpportunityDescription = styled.p`
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.5;
`;

const ButtonGroup = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 16px;
`;

const ExportButton = styled.button<{ theme: any; disabled?: boolean }>`
  padding: 12px 24px;
  background-color: ${(props) => (props.disabled ? '#ccc' : props.theme.colors.primary.main)};
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => (props.disabled ? '#ccc' : props.theme.colors.primary.dark)};
  }
`;

export default FinancialCompassSummaryPage;
