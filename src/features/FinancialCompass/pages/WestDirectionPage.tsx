/**
 * West Direction Page
 *
 * This page manages the West direction of the Financial Compass ("Your Legacy & Impact").
 * It includes components for estate planning, charitable giving, legacy planning, and values & goals.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../context/FinancialCompassContext';
import { useGuidedJourney } from '../../GuidedJourney/context/GuidedJourneyContext';
import GuidedFlowNavigator from '../../GuidedJourney/components/GuidedFlowNavigator';
import WestDirectionTracker from '../components/West/WestDirectionTracker';
import EstatePlanning from '../components/West/EstatePlanning';
import CharitableGiving from '../components/West/CharitableGiving';
import LegacyPlanning from '../components/West/LegacyPlanning';
import ValuesGoals from '../components/West/ValuesGoals';
import TaxPlanning from '../components/West/TaxPlanning';
import EstatePlanningDocumentGenerator from '../components/West/EstatePlanningDocumentGenerator';
import LegacyMessageRecording from '../components/West/LegacyMessageRecording';
import SummarySection from '../components/West/SummarySection';
import { formatCurrency } from '../../../utils/formatters';
import { downloadComprehensivePDF } from '../../../utils/enhancedPdfExport';

interface WestDirectionPageProps {
  onComplete?: () => void;
}

const WestDirectionPage: React.FC<WestDirectionPageProps> = ({ onComplete }) => {
  const { theme } = useTheme();
  const {
    westSections,
    updateWestSectionStatus,
    updateWestProgress,
    activeSubFeature,
    setActiveSubFeature,
  } = useFinancialCompass();
  const { updateActiveDirection } = useGuidedJourney();

  // Current step state
  const [currentStep, setCurrentStep] = useState<string>('overview');

  // Set active direction to West
  useEffect(() => {
    if (updateActiveDirection) {
      updateActiveDirection('west');
    }
  }, [updateActiveDirection]);

  // Define steps for the guided flow navigator
  const steps = [
    { id: 'overview', label: 'Overview', isCompleted: true },
    {
      id: 'tax_planning',
      label: 'Tax Planning',
      isCompleted: westSections.find((s) => s.id === 'tax_planning')?.isCompleted || false,
    },
    {
      id: 'estate_planning',
      label: 'Estate Planning',
      isCompleted: westSections.find((s) => s.id === 'estate_planning')?.isCompleted || false,
    },
    {
      id: 'estate_documents',
      label: 'Estate Documents',
      isCompleted: westSections.find((s) => s.id === 'estate_documents')?.isCompleted || false,
    },
    {
      id: 'charitable_giving',
      label: 'Charitable Giving',
      isCompleted: westSections.find((s) => s.id === 'charitable_giving')?.isCompleted || false,
    },
    {
      id: 'legacy_planning',
      label: 'Legacy Planning',
      isCompleted: westSections.find((s) => s.id === 'legacy_planning')?.isCompleted || false,
    },
    {
      id: 'legacy_messages',
      label: 'Legacy Messages',
      isCompleted: westSections.find((s) => s.id === 'legacy_messages')?.isCompleted || false,
    },
    {
      id: 'values_goals',
      label: 'Values & Goals',
      isCompleted: westSections.find((s) => s.id === 'values_goals')?.isCompleted || false,
    },
    { id: 'summary', label: 'Summary', isCompleted: false },
  ];

  // Handle step change
  const handleStepChange = (stepId: string) => {
    setCurrentStep(stepId);
  };

  // Handle section selection from tracker
  const handleSectionSelect = (sectionId: string) => {
    handleStepChange(sectionId);
  };

  // Handle section completion
  const handleSectionComplete = (sectionId: string) => {
    updateWestSectionStatus(sectionId, true);

    // Navigate to next step
    const currentIndex = steps.findIndex((step) => step.id === sectionId);
    if (currentIndex < steps.length - 1) {
      handleStepChange(steps[currentIndex + 1].id);
    }
  };

  // Render current step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 'overview':
        return <WestDirectionTracker onSectionSelect={handleSectionSelect} />;
      case 'tax_planning':
        return (
          <TaxPlanning
            onComplete={() => handleSectionComplete('tax_planning')}
            onBack={() => handleStepChange('overview')}
          />
        );
      case 'estate_planning':
        return (
          <EstatePlanning
            onComplete={() => handleSectionComplete('estate_planning')}
            onBack={() => handleStepChange('tax_planning')}
          />
        );
      case 'estate_documents':
        return (
          <EstatePlanningDocumentGenerator
            onComplete={() => handleSectionComplete('estate_documents')}
            onBack={() => handleStepChange('estate_planning')}
          />
        );
      case 'charitable_giving':
        return (
          <CharitableGiving
            onComplete={() => handleSectionComplete('charitable_giving')}
            onBack={() => handleStepChange('estate_documents')}
          />
        );
      case 'legacy_planning':
        return (
          <LegacyPlanning
            onComplete={() => handleSectionComplete('legacy_planning')}
            onBack={() => handleStepChange('charitable_giving')}
          />
        );
      case 'legacy_messages':
        return (
          <LegacyMessageRecording
            onComplete={() => handleSectionComplete('legacy_messages')}
            onBack={() => handleStepChange('legacy_planning')}
          />
        );
      case 'values_goals':
        return (
          <ValuesGoals
            onComplete={() => handleSectionComplete('values_goals')}
            onBack={() => handleStepChange('legacy_messages')}
          />
        );
      case 'summary':
        return (
          <SummarySection onComplete={onComplete} onBack={() => handleStepChange('values_goals')} />
        );
      default:
        return <div>Unknown step</div>;
    }
  };

  return (
    <PageContainer theme={theme}>
      <PageHeader theme={theme}>
        <PageTitle theme={theme}>West Direction: Your Legacy & Impact</PageTitle>
        <PageDescription theme={theme}>
          The West direction helps you plan for your legacy and lasting impact. Complete each
          section to build a comprehensive legacy plan.
        </PageDescription>
      </PageHeader>

      <GuidedFlowNavigator
        steps={steps}
        currentStepId={currentStep}
        onStepChange={handleStepChange}
        direction="horizontal"
        allowSkip={false}
        showProgress={true}
      />

      <ContentContainer theme={theme}>{renderStepContent()}</ContentContainer>
    </PageContainer>
  );
};

// Placeholder component for sections that are not yet implemented
const PlaceholderSection: React.FC<{
  title: string;
  description: string;
  onComplete?: () => void;
  onBack: () => void;
}> = ({ title, description, onComplete, onBack }) => {
  const { theme } = useTheme();

  return (
    <SectionContainer theme={theme}>
      <SectionHeader theme={theme}>
        <SectionTitle theme={theme}>{title}</SectionTitle>
        <SectionDescription theme={theme}>{description}</SectionDescription>
      </SectionHeader>

      <PlaceholderContent theme={theme}>
        <PlaceholderIcon>🚧</PlaceholderIcon>
        <PlaceholderText theme={theme}>
          This section is under construction. Check back soon!
        </PlaceholderText>
      </PlaceholderContent>

      <ButtonContainer>
        <BackButton onClick={onBack} theme={theme}>
          Back
        </BackButton>
        <CompleteButton onClick={onComplete} theme={theme}>
          Mark as Complete
        </CompleteButton>
      </ButtonContainer>
    </SectionContainer>
  );
};

// Styled components
const PageContainer = styled.div<{ theme: any }>`
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
`;

const PageHeader = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const PageTitle = styled.h1<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 16px 0;
  font-size: 2rem;
`;

const PageDescription = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const ContentContainer = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 24px;
  overflow: hidden;
`;

// Section components
const SectionContainer = styled.div<{ theme: any }>`
  padding: 24px;
`;

const SectionHeader = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const SectionTitle = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const SectionDescription = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

// Placeholder components
const PlaceholderContent = styled.div<{ theme: any }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  margin: 24px 0;
`;

const PlaceholderIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
`;

const PlaceholderText = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  text-align: center;
  font-size: 1.2rem;
`;

// Button components
const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: 24px;
`;

const BackButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.text.secondary};
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const CompleteButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

export default WestDirectionPage;
