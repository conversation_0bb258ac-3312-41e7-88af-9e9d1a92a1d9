/**
 * East Direction Page
 *
 * This page manages the East direction of the Financial Compass ("Where You're Going").
 * It includes components for retirement planning and investment preferences.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../context/FinancialCompassContext';
import { useGuidedJourney } from '../../GuidedJourney/context/GuidedJourneyContext';
import GuidedFlowNavigator from '../../GuidedJourney/components/GuidedFlowNavigator';
import EastDirectionTracker from '../components/East/EastDirectionTracker';
import RetirementGoals from '../components/East/RetirementGoals';
import RetirementIncome from '../components/East/RetirementIncome';
import RetirementExpenses from '../components/East/RetirementExpenses';
import RetirementTimeline from '../components/East/RetirementTimeline';
import SocialSecurityPlanning from '../components/East/SocialSecurityPlanning';
import SummarySection from '../components/East/SummarySection';
import { formatCurrency } from '../../../utils/formatters';
import { downloadComprehensivePDF } from '../../../utils/enhancedPdfExport';

interface EastDirectionPageProps {
  onComplete?: () => void;
}

const EastDirectionPage: React.FC<EastDirectionPageProps> = ({ onComplete }) => {
  const { theme } = useTheme();
  const { eastSections, updateEastSectionStatus } = useFinancialCompass();
  const { updateActiveDirection } = useGuidedJourney();

  // Current step state
  const [currentStep, setCurrentStep] = useState<string>('overview');

  // Set active direction to East
  useEffect(() => {
    if (updateActiveDirection) {
      updateActiveDirection('east');
    }
  }, [updateActiveDirection]);

  // Define steps for the guided flow navigator
  const steps = [
    { id: 'overview', label: 'Overview', isCompleted: true },
    {
      id: 'retirement_goals',
      label: 'Retirement Goals',
      isCompleted: eastSections.find((s) => s.id === 'retirement_goals')?.isCompleted || false,
    },
    {
      id: 'retirement_income',
      label: 'Retirement Income',
      isCompleted: eastSections.find((s) => s.id === 'retirement_income')?.isCompleted || false,
    },
    {
      id: 'social_security',
      label: 'Social Security Planning',
      isCompleted: eastSections.find((s) => s.id === 'social_security')?.isCompleted || false,
    },
    {
      id: 'retirement_expenses',
      label: 'Retirement Expenses',
      isCompleted: eastSections.find((s) => s.id === 'retirement_expenses')?.isCompleted || false,
    },
    {
      id: 'investment_preferences',
      label: 'Investment Preferences',
      isCompleted:
        eastSections.find((s) => s.id === 'investment_preferences')?.isCompleted || false,
    },
    {
      id: 'risk_assessment',
      label: 'Risk Assessment',
      isCompleted: eastSections.find((s) => s.id === 'risk_assessment')?.isCompleted || false,
    },
    {
      id: 'retirement_timeline',
      label: 'Retirement Timeline',
      isCompleted: eastSections.find((s) => s.id === 'retirement_timeline')?.isCompleted || false,
    },
    { id: 'summary', label: 'Summary', isCompleted: false },
  ];

  // Handle step change
  const handleStepChange = (stepId: string) => {
    setCurrentStep(stepId);
  };

  // Handle section selection from tracker
  const handleSectionSelect = (sectionId: string) => {
    handleStepChange(sectionId);
  };

  // Handle section completion
  const handleSectionComplete = (sectionId: string) => {
    updateEastSectionStatus(sectionId, true);

    // Navigate to next step
    const currentIndex = steps.findIndex((step) => step.id === sectionId);
    if (currentIndex < steps.length - 1) {
      handleStepChange(steps[currentIndex + 1].id);
    }
  };

  // Render current step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 'overview':
        return <EastDirectionTracker onSectionSelect={handleSectionSelect} />;
      case 'retirement_goals':
        return (
          <RetirementGoals
            onComplete={() => handleSectionComplete('retirement_goals')}
            onBack={() => handleStepChange('overview')}
          />
        );
      case 'retirement_income':
        return (
          <RetirementIncome
            onComplete={() => handleSectionComplete('retirement_income')}
            onBack={() => handleStepChange('retirement_goals')}
          />
        );
      case 'social_security':
        return (
          <SocialSecurityPlanning
            onComplete={() => handleSectionComplete('social_security')}
            onBack={() => handleStepChange('retirement_income')}
          />
        );
      case 'retirement_expenses':
        return (
          <RetirementExpenses
            onComplete={() => handleSectionComplete('retirement_expenses')}
            onBack={() => handleStepChange('social_security')}
          />
        );
      case 'investment_preferences':
        return (
          <PlaceholderSection
            title="Investment Preferences"
            description="Define your investment preferences and strategy for retirement."
            onComplete={() => handleSectionComplete('investment_preferences')}
            onBack={() => handleStepChange('retirement_expenses')}
          />
        );
      case 'risk_assessment':
        return (
          <PlaceholderSection
            title="Risk Assessment"
            description="Assess your risk tolerance for retirement investments."
            onComplete={() => handleSectionComplete('risk_assessment')}
            onBack={() => handleStepChange('investment_preferences')}
          />
        );
      case 'retirement_timeline':
        return (
          <RetirementTimeline
            onComplete={() => handleSectionComplete('retirement_timeline')}
            onBack={() => handleStepChange('risk_assessment')}
          />
        );
      case 'summary':
        return (
          <SummarySection
            onComplete={onComplete}
            onBack={() => handleStepChange('retirement_timeline')}
          />
        );
      default:
        return <div>Unknown step</div>;
    }
  };

  return (
    <PageContainer theme={theme}>
      <PageHeader theme={theme}>
        <PageTitle theme={theme}>East Direction: Where You&apos;re Going</PageTitle>
        <PageDescription theme={theme}>
          The East direction helps you plan for your retirement future. Complete each section to
          build a comprehensive retirement vision.
        </PageDescription>
      </PageHeader>

      <GuidedFlowNavigator
        steps={steps}
        currentStepId={currentStep}
        onStepChange={handleStepChange}
        direction="horizontal"
        allowSkip={false}
        showProgress={true}
      />

      <ContentContainer theme={theme}>{renderStepContent()}</ContentContainer>
    </PageContainer>
  );
};

// Placeholder component for sections that are not yet implemented
const PlaceholderSection: React.FC<{
  title: string;
  description: string;
  onComplete?: () => void;
  onBack: () => void;
}> = ({ title, description, onComplete, onBack }) => {
  const { theme } = useTheme();

  return (
    <SectionContainer theme={theme}>
      <SectionHeader theme={theme}>
        <SectionTitle theme={theme}>{title}</SectionTitle>
        <SectionDescription theme={theme}>{description}</SectionDescription>
      </SectionHeader>

      <PlaceholderContent theme={theme}>
        <PlaceholderIcon>🚧</PlaceholderIcon>
        <PlaceholderText theme={theme}>
          This section is under construction. Check back soon!
        </PlaceholderText>
      </PlaceholderContent>

      <ButtonContainer>
        <BackButton onClick={onBack} theme={theme}>
          Back
        </BackButton>
        <CompleteButton onClick={onComplete} theme={theme}>
          Mark as Complete
        </CompleteButton>
      </ButtonContainer>
    </SectionContainer>
  );
};

// Styled components
const PageContainer = styled.div<{ theme: any }>`
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
`;

const PageHeader = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const PageTitle = styled.h1<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 16px 0;
  font-size: 2rem;
`;

const PageDescription = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const ContentContainer = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 24px;
  overflow: hidden;
`;

// Section components
const SectionContainer = styled.div<{ theme: any }>`
  padding: 24px;
`;

const SectionHeader = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const SectionTitle = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const SectionDescription = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

// Placeholder components
const PlaceholderContent = styled.div<{ theme: any }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  margin: 24px 0;
`;

const PlaceholderIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
`;

const PlaceholderText = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  text-align: center;
  font-size: 1.2rem;
`;

// Button components
const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: 24px;
`;

const BackButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.text.secondary};
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const CompleteButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

export default EastDirectionPage;
