import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import NorthDirectionPage from './NorthDirectionPage';
import { ThemeProvider } from '../../../context/ThemeContext';
import { FinancialCompassProvider } from '../context/FinancialCompassContext';
import { GuidedJourneyProvider } from '../../GuidedJourney/context/GuidedJourneyContext';

// Mock the PDF export utility
jest.mock('../../../utils/pdfExport', () => ({
  downloadFinancialSummaryPDF: jest.fn(),
  generateFinancialSummaryPDF: jest.fn(),
}));

describe('NorthDirectionPage Integration Tests', () => {
  const mockOnComplete = jest.fn();

  const renderComponent = () => {
    return render(
      <ThemeProvider>
        <GuidedJourneyProvider>
          <FinancialCompassProvider>
            <NorthDirectionPage onComplete={mockOnComplete} />
          </FinancialCompassProvider>
        </GuidedJourneyProvider>
      </ThemeProvider>
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock localStorage
    const localStorageMock = (() => {
      let store: Record<string, string> = {};
      return {
        getItem: (key: string) => store[key] || null,
        setItem: (key: string, value: string) => {
          store[key] = value.toString();
        },
        removeItem: (key: string) => {
          delete store[key];
        },
        clear: () => {
          store = {};
        },
      };
    })();

    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
    });
  });

  it('renders the North Direction page with tracker and first step', () => {
    renderComponent();

    // Check if the page title is rendered
    expect(screen.getByText('North Direction: Financial Foundations')).toBeInTheDocument();

    // Check if the tracker is rendered
    expect(screen.getByText('North Direction Progress')).toBeInTheDocument();

    // Check if the first step (Personal Information) is rendered
    expect(screen.getByText('Personal Information')).toBeInTheDocument();
  });

  it('navigates through all steps in the North Direction journey', async () => {
    renderComponent();

    // Step 1: Personal Information
    expect(screen.getByText('Personal Information')).toBeInTheDocument();

    // Fill out personal information form
    fireEvent.change(screen.getByLabelText('First Name'), { target: { value: 'John' } });
    fireEvent.change(screen.getByLabelText('Last Name'), { target: { value: 'Doe' } });
    fireEvent.change(screen.getByLabelText('Email'), { target: { value: '<EMAIL>' } });

    // Click Save & Continue
    fireEvent.click(screen.getByText('Save & Continue'));

    // Wait for navigation to Family Information
    await waitFor(() => {
      expect(screen.getByText('Family Information')).toBeInTheDocument();
    });

    // Step 2: Family Information
    // Add spouse
    fireEvent.click(screen.getByText('Add Spouse'));
    fireEvent.change(screen.getByLabelText('Spouse First Name'), { target: { value: 'Jane' } });
    fireEvent.change(screen.getByLabelText('Spouse Last Name'), { target: { value: 'Doe' } });

    // Click Save & Continue
    fireEvent.click(screen.getByText('Save & Continue'));

    // Wait for navigation to Income Details
    await waitFor(() => {
      expect(screen.getByText('Income Details')).toBeInTheDocument();
    });

    // Step 3: Income Details
    // Add income source
    fireEvent.click(screen.getByText('Add Income Source'));
    fireEvent.change(screen.getByLabelText('Income Source'), { target: { value: 'Salary' } });
    fireEvent.change(screen.getByLabelText('Monthly Amount'), { target: { value: '5000' } });

    // Click Save & Continue
    fireEvent.click(screen.getByText('Save & Continue'));

    // Wait for navigation to Expense Details
    await waitFor(() => {
      expect(screen.getByText('Expense Details')).toBeInTheDocument();
    });

    // Step 4: Expense Details
    // Add expense
    const mortgageInput = screen.getByLabelText('Mortgage/Rent');
    fireEvent.change(mortgageInput, { target: { value: '1500' } });

    // Click Save & Continue
    fireEvent.click(screen.getByText('Save & Continue'));

    // Wait for navigation to Assets & Investments
    await waitFor(() => {
      expect(screen.getByText('Assets & Investments')).toBeInTheDocument();
    });

    // Step 5: Assets & Investments
    // Add asset
    const checkingInput = screen.getByLabelText('Checking Accounts');
    fireEvent.change(checkingInput, { target: { value: '10000' } });

    // Click Save & Continue
    fireEvent.click(screen.getByText('Save & Continue'));

    // Wait for navigation to Liabilities & Debt
    await waitFor(() => {
      expect(screen.getByText('Liabilities & Debt')).toBeInTheDocument();
    });

    // Step 6: Liabilities & Debt
    // Add liability
    const autoLoanInput = screen.getByLabelText('Auto Loan');
    fireEvent.change(autoLoanInput, { target: { value: '15000' } });

    // Click Save & Continue
    fireEvent.click(screen.getByText('Save & Continue'));

    // Wait for navigation to Summary
    await waitFor(() => {
      expect(screen.getByText('North Direction Summary')).toBeInTheDocument();
    });

    // Step 7: Summary
    // Check if the summary shows the correct completion status
    expect(
      screen.getByText('Congratulations! You have completed all sections in the North direction.')
    ).toBeInTheDocument();

    // Check if the financial summary is displayed
    expect(screen.getByText('Financial Position Summary')).toBeInTheDocument();
    expect(screen.getByText('Monthly Income')).toBeInTheDocument();
    expect(screen.getByText('Monthly Expenses')).toBeInTheDocument();
    expect(screen.getByText('Monthly Cash Flow')).toBeInTheDocument();
    expect(screen.getByText('Total Assets')).toBeInTheDocument();
    expect(screen.getByText('Total Liabilities')).toBeInTheDocument();
    expect(screen.getByText('Net Worth')).toBeInTheDocument();

    // Check if the buttons are displayed
    expect(screen.getByText('View Detailed Cash Flow Analysis')).toBeInTheDocument();
    expect(screen.getByText('Export Financial Summary PDF')).toBeInTheDocument();

    // Click Continue to East Direction
    fireEvent.click(screen.getByText('Continue to East Direction'));

    // Check if onComplete was called
    expect(mockOnComplete).toHaveBeenCalled();
  });

  it('allows navigation back to previous steps', async () => {
    renderComponent();

    // Step 1: Personal Information
    expect(screen.getByText('Personal Information')).toBeInTheDocument();

    // Fill out personal information form
    fireEvent.change(screen.getByLabelText('First Name'), { target: { value: 'John' } });
    fireEvent.change(screen.getByLabelText('Last Name'), { target: { value: 'Doe' } });
    fireEvent.change(screen.getByLabelText('Email'), { target: { value: '<EMAIL>' } });

    // Click Save & Continue
    fireEvent.click(screen.getByText('Save & Continue'));

    // Wait for navigation to Family Information
    await waitFor(() => {
      expect(screen.getByText('Family Information')).toBeInTheDocument();
    });

    // Click Back
    fireEvent.click(screen.getByText('Back'));

    // Wait for navigation back to Personal Information
    await waitFor(() => {
      expect(screen.getByText('Personal Information')).toBeInTheDocument();
    });

    // Check if the form values are preserved
    expect(screen.getByLabelText('First Name')).toHaveValue('John');
    expect(screen.getByLabelText('Last Name')).toHaveValue('Doe');
    expect(screen.getByLabelText('Email')).toHaveValue('<EMAIL>');
  });

  it('exports PDF when clicking the export button', async () => {
    const { downloadFinancialSummaryPDF } = require('../../../utils/pdfExport');

    // Mock the complete journey to get to the summary page
    const mockFinancialCompassContext = {
      data: {
        north: {
          personalInfo: {
            firstName: 'John',
            lastName: 'Doe',
          },
          totalMonthlyIncome: 5000,
          totalMonthlyExpenses: 3000,
          totalAnnualIncome: 60000,
          totalAnnualExpenses: 36000,
          totalAssets: 100000,
          totalLiabilities: 50000,
        },
      },
      northSections: [
        { id: 'personal_information', title: 'Personal Information', isCompleted: true },
        { id: 'family_information', title: 'Family Information', isCompleted: true },
        { id: 'income_details', title: 'Income Details', isCompleted: true },
        { id: 'expense_details', title: 'Expense Details', isCompleted: true },
        { id: 'assets_investments', title: 'Assets & Investments', isCompleted: true },
        { id: 'liabilities_debt', title: 'Liabilities & Debt', isCompleted: true },
      ],
      updateNorthSectionStatus: jest.fn(),
      updateData: jest.fn(),
    };

    jest.mock('../context/FinancialCompassContext', () => ({
      ...jest.requireActual('../context/FinancialCompassContext'),
      useFinancialCompass: () => mockFinancialCompassContext,
    }));

    renderComponent();

    // Navigate to the summary page
    fireEvent.click(screen.getByText('Summary'));

    // Wait for the summary page to load
    await waitFor(() => {
      expect(screen.getByText('North Direction Summary')).toBeInTheDocument();
    });

    // Click the export button
    fireEvent.click(screen.getByText('Export Financial Summary PDF'));

    // Check if the PDF export function was called
    expect(downloadFinancialSummaryPDF).toHaveBeenCalledWith(
      mockFinancialCompassContext.data,
      'John Doe'
    );
  });
});
