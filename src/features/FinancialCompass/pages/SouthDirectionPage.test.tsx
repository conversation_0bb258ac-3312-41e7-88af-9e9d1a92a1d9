import React from 'react';
import { render, screen } from '@testing-library/react';
import SouthDirectionPage from './SouthDirectionPage';
import { ThemeProvider } from '../../../context/ThemeContext';
import { FinancialCompassProvider } from '../context/FinancialCompassContext';
import { GuidedJourneyProvider } from '../../GuidedJourney/context/GuidedJourneyContext';

// Mock the PDF export utility
jest.mock('../../../utils/pdfExport', () => ({
  downloadFinancialSummaryPDF: jest.fn(),
  generateFinancialSummaryPDF: jest.fn(),
}));

describe('SouthDirectionPage', () => {
  const mockOnComplete = jest.fn();

  const renderComponent = () => {
    return render(
      <ThemeProvider>
        <GuidedJourneyProvider>
          <FinancialCompassProvider>
            <SouthDirectionPage onComplete={mockOnComplete} />
          </FinancialCompassProvider>
        </GuidedJourneyProvider>
      </ThemeProvider>
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the South Direction page with title and description', () => {
    renderComponent();

    // Check if the page title is rendered
    expect(screen.getByText('South Direction: What Protects You')).toBeInTheDocument();

    // Check if the page description is rendered
    expect(
      screen.getByText(
        /The South direction helps you assess and strengthen your protection against risks/
      )
    ).toBeInTheDocument();
  });

  it('renders the GuidedFlowNavigator with correct steps', () => {
    renderComponent();

    // Check if the steps are rendered
    expect(screen.getByText('Overview')).toBeInTheDocument();
    expect(screen.getByText('Insurance Coverage')).toBeInTheDocument();
    expect(screen.getByText('Healthcare Planning')).toBeInTheDocument();
    expect(screen.getByText('Risk Tolerance')).toBeInTheDocument();
    expect(screen.getByText('Protection Gap')).toBeInTheDocument();
    expect(screen.getByText('Summary')).toBeInTheDocument();
  });

  it('renders the SouthDirectionTracker by default', () => {
    renderComponent();

    // The tracker should be rendered initially
    expect(screen.getByText('South: What Protects You')).toBeInTheDocument();
  });
});
