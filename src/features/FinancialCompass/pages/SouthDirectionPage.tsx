/**
 * South Direction Page
 *
 * This page manages the South direction of the Financial Compass ("What Protects You").
 * It includes components for insurance coverage, healthcare planning, risk tolerance, and protection gap analysis.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../context/FinancialCompassContext';
import { useGuidedJourney } from '../../GuidedJourney/context/GuidedJourneyContext';
import GuidedFlowNavigator from '../../GuidedJourney/components/GuidedFlowNavigator';
import SouthDirectionTracker from '../components/South/SouthDirectionTracker';
import InsuranceCoverage from '../components/South/InsuranceCoverage';
import HealthcarePlanning from '../components/South/HealthcarePlanning';
import RiskTolerance from '../components/South/RiskTolerance';
import ProtectionGap from '../components/South/ProtectionGap';
import InsuranceNeedsCalculator from '../components/South/InsuranceNeedsCalculator';
import HealthcareCostProjections from '../components/South/HealthcareCostProjections';
import SummarySection from '../components/South/SummarySection';
import { formatCurrency } from '../../../utils/formatters';
import { downloadComprehensivePDF } from '../../../utils/enhancedPdfExport';

interface SouthDirectionPageProps {
  onComplete?: () => void;
}

const SouthDirectionPage: React.FC<SouthDirectionPageProps> = ({ onComplete }) => {
  const { theme } = useTheme();
  const {
    southSections,
    updateSouthSectionStatus,
    updateSouthProgress,
    activeSubFeature,
    setActiveSubFeature,
    updateData,
    data,
  } = useFinancialCompass();
  const { updateActiveDirection } = useGuidedJourney();

  // Current step state
  const [currentStep, setCurrentStep] = useState<string>('overview');

  // Set active direction to South
  useEffect(() => {
    if (updateActiveDirection) {
      updateActiveDirection('south');
    }
  }, [updateActiveDirection]);

  // Define steps for the guided flow navigator
  const steps = [
    { id: 'overview', label: 'Overview', isCompleted: true },
    {
      id: 'insurance_coverage',
      label: 'Insurance Coverage',
      isCompleted: southSections.find((s) => s.id === 'insurance_coverage')?.isCompleted || false,
    },
    {
      id: 'insurance_calculator',
      label: 'Insurance Calculator',
      isCompleted: southSections.find((s) => s.id === 'insurance_calculator')?.isCompleted || false,
    },
    {
      id: 'healthcare_planning',
      label: 'Healthcare Planning',
      isCompleted: southSections.find((s) => s.id === 'healthcare_planning')?.isCompleted || false,
    },
    {
      id: 'healthcare_projections',
      label: 'Healthcare Projections',
      isCompleted:
        southSections.find((s) => s.id === 'healthcare_projections')?.isCompleted || false,
    },
    {
      id: 'risk_tolerance',
      label: 'Risk Tolerance',
      isCompleted: southSections.find((s) => s.id === 'risk_tolerance')?.isCompleted || false,
    },
    {
      id: 'protection_gap',
      label: 'Protection Gap',
      isCompleted: southSections.find((s) => s.id === 'protection_gap')?.isCompleted || false,
    },
    { id: 'summary', label: 'Summary', isCompleted: false },
  ];

  // Handle step change
  const handleStepChange = (stepId: string) => {
    console.log('Changing step to:', stepId);
    setCurrentStep(stepId);

    // Update active section in context
    southSections.forEach((section) => {
      updateSouthSectionStatus(section.id, section.isCompleted, section.id === stepId);
    });
  };

  // Handle section selection from tracker
  const handleSectionSelect = (sectionId: string) => {
    console.log('Section selected:', sectionId);
    handleStepChange(sectionId);
  };

  // Handle section completion
  const handleSectionComplete = (sectionId: string) => {
    console.log('Section completed:', sectionId);
    updateSouthSectionStatus(sectionId, true, true);

    // Save completion date
    const now = new Date().toISOString();
    updateData('south', `${sectionId}_completed_date`, now);

    // Navigate to next step
    const currentIndex = steps.findIndex((step) => step.id === sectionId);
    if (currentIndex < steps.length - 1) {
      handleStepChange(steps[currentIndex + 1].id);
    }
  };

  // Render current step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 'overview':
        return <SouthDirectionTracker onSectionSelect={handleSectionSelect} />;
      case 'insurance_coverage':
        return (
          <InsuranceCoverage
            onComplete={() => handleSectionComplete('insurance_coverage')}
            onBack={() => handleStepChange('overview')}
          />
        );
      case 'insurance_calculator':
        return (
          <InsuranceNeedsCalculator
            onComplete={() => handleSectionComplete('insurance_calculator')}
            onBack={() => handleStepChange('insurance_coverage')}
          />
        );
      case 'healthcare_planning':
        return (
          <HealthcarePlanning
            onComplete={() => handleSectionComplete('healthcare_planning')}
            onBack={() => handleStepChange('insurance_calculator')}
          />
        );
      case 'healthcare_projections':
        return (
          <HealthcareCostProjections
            onComplete={() => handleSectionComplete('healthcare_projections')}
            onBack={() => handleStepChange('healthcare_planning')}
          />
        );
      case 'risk_tolerance':
        return (
          <RiskTolerance
            onComplete={() => handleSectionComplete('risk_tolerance')}
            onBack={() => handleStepChange('healthcare_projections')}
          />
        );
      case 'protection_gap':
        return (
          <ProtectionGap
            onComplete={() => handleSectionComplete('protection_gap')}
            onBack={() => handleStepChange('risk_tolerance')}
          />
        );
      case 'summary':
        return (
          <SummarySection
            onComplete={onComplete}
            onBack={() => handleStepChange('protection_gap')}
          />
        );
      default:
        return <div>Unknown step</div>;
    }
  };

  return (
    <PageContainer theme={theme}>
      <PageHeader theme={theme}>
        <PageTitle theme={theme}>South Direction: What Protects You</PageTitle>
        <PageDescription theme={theme}>
          The South direction helps you assess and strengthen your protection against risks.
          Complete each section to build a comprehensive protection plan.
        </PageDescription>
      </PageHeader>

      <GuidedFlowNavigator
        steps={steps}
        currentStepId={currentStep}
        onStepChange={handleStepChange}
        direction="horizontal"
        allowSkip={false}
        showProgress={true}
      />

      <ContentContainer theme={theme}>{renderStepContent()}</ContentContainer>
    </PageContainer>
  );
};

// Placeholder component for sections that are not yet implemented
const PlaceholderSection: React.FC<{
  title: string;
  description: string;
  onComplete?: () => void;
  onBack: () => void;
}> = ({ title, description, onComplete, onBack }) => {
  const { theme } = useTheme();

  return (
    <SectionContainer theme={theme}>
      <SectionHeader theme={theme}>
        <SectionTitle theme={theme}>{title}</SectionTitle>
        <SectionDescription theme={theme}>{description}</SectionDescription>
      </SectionHeader>

      <PlaceholderContent theme={theme}>
        <PlaceholderIcon>🚧</PlaceholderIcon>
        <PlaceholderText theme={theme}>
          This section is under construction. Check back soon!
        </PlaceholderText>
      </PlaceholderContent>

      <ButtonContainer>
        <BackButton onClick={onBack} theme={theme}>
          Back
        </BackButton>
        <CompleteButton onClick={onComplete} theme={theme}>
          Mark as Complete
        </CompleteButton>
      </ButtonContainer>
    </SectionContainer>
  );
};

// Styled components
const PageContainer = styled.div<{ theme: any }>`
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
`;

const PageHeader = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const PageTitle = styled.h1<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 16px 0;
  font-size: 2rem;
`;

const PageDescription = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const ContentContainer = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 24px;
  overflow: hidden;
`;

// Section components
const SectionContainer = styled.div<{ theme: any }>`
  padding: 24px;
`;

const SectionHeader = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const SectionTitle = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const SectionDescription = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

// Placeholder components
const PlaceholderContent = styled.div<{ theme: any }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  margin: 24px 0;
`;

const PlaceholderIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
`;

const PlaceholderText = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  text-align: center;
  font-size: 1.2rem;
`;

// Button components
const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: 24px;
`;

const BackButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.text.secondary};
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const CompleteButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

export default SouthDirectionPage;
