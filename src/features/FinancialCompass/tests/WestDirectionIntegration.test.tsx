/**
 * West Direction Integration Tests
 *
 * This file contains integration tests for the West Direction journey,
 * testing the flow between components and data persistence.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from '../../../context/ThemeContext';
import { FinancialCompassProvider } from '../context/FinancialCompassContext';
import { GuidedJourneyProvider } from '../../GuidedJourney/context/GuidedJourneyContext';
import { SeasonsOfSelfProvider } from '../../SeasonsOfSelf/context/SeasonsOfSelfContext';
import WestDirectionPage from '../pages/WestDirectionPage';

// Mock the components
jest.mock('../components/West/WestDirectionTracker', () => {
  return function MockWestDirectionTracker({
    onSectionSelect,
  }: {
    onSectionSelect: (id: string) => void;
  }) {
    return (
      <div data-testid="west-direction-tracker">
        <button onClick={() => onSectionSelect('tax_planning')}>Go to Tax Planning</button>
        <button onClick={() => onSectionSelect('estate_planning')}>Go to Estate Planning</button>
        <button onClick={() => onSectionSelect('estate_documents')}>Go to Estate Documents</button>
        <button onClick={() => onSectionSelect('charitable_giving')}>
          Go to Charitable Giving
        </button>
        <button onClick={() => onSectionSelect('legacy_planning')}>Go to Legacy Planning</button>
        <button onClick={() => onSectionSelect('legacy_messages')}>Go to Legacy Messages</button>
        <button onClick={() => onSectionSelect('values_goals')}>Go to Values & Goals</button>
      </div>
    );
  };
});

jest.mock('../components/West/TaxPlanning', () => {
  return function MockTaxPlanning({
    onComplete,
    onBack,
  }: {
    onComplete?: () => void;
    onBack?: () => void;
  }) {
    return (
      <div data-testid="tax-planning">
        <h2>Tax Planning</h2>
        <button onClick={onBack}>Back</button>
        <button onClick={onComplete}>Complete</button>
      </div>
    );
  };
});

jest.mock('../components/West/EstatePlanning', () => {
  return function MockEstatePlanning({
    onComplete,
    onBack,
  }: {
    onComplete?: () => void;
    onBack?: () => void;
  }) {
    return (
      <div data-testid="estate-planning">
        <h2>Estate Planning</h2>
        <button onClick={onBack}>Back</button>
        <button onClick={onComplete}>Complete</button>
      </div>
    );
  };
});

jest.mock('../components/West/EstatePlanningDocumentGenerator', () => {
  return function MockEstatePlanningDocumentGenerator({
    onComplete,
    onBack,
  }: {
    onComplete?: () => void;
    onBack?: () => void;
  }) {
    return (
      <div data-testid="estate-documents">
        <h2>Estate Planning Document Generator</h2>
        <button onClick={onBack}>Back</button>
        <button onClick={onComplete}>Complete</button>
      </div>
    );
  };
});

jest.mock('../components/West/CharitableGiving', () => {
  return function MockCharitableGiving({
    onComplete,
    onBack,
  }: {
    onComplete?: () => void;
    onBack?: () => void;
  }) {
    return (
      <div data-testid="charitable-giving">
        <h2>Charitable Giving</h2>
        <button onClick={onBack}>Back</button>
        <button onClick={onComplete}>Complete</button>
      </div>
    );
  };
});

jest.mock('../components/West/LegacyPlanning', () => {
  return function MockLegacyPlanning({
    onComplete,
    onBack,
  }: {
    onComplete?: () => void;
    onBack?: () => void;
  }) {
    return (
      <div data-testid="legacy-planning">
        <h2>Legacy Planning</h2>
        <button onClick={onBack}>Back</button>
        <button onClick={onComplete}>Complete</button>
      </div>
    );
  };
});

jest.mock('../components/West/LegacyMessageRecording', () => {
  return function MockLegacyMessageRecording({
    onComplete,
    onBack,
  }: {
    onComplete?: () => void;
    onBack?: () => void;
  }) {
    return (
      <div data-testid="legacy-messages">
        <h2>Legacy Message Recording</h2>
        <button onClick={onBack}>Back</button>
        <button onClick={onComplete}>Complete</button>
      </div>
    );
  };
});

jest.mock('../components/West/ValuesGoals', () => {
  return function MockValuesGoals({
    onComplete,
    onBack,
  }: {
    onComplete?: () => void;
    onBack?: () => void;
  }) {
    return (
      <div data-testid="values-goals">
        <h2>Values & Goals</h2>
        <button onClick={onBack}>Back</button>
        <button onClick={onComplete}>Complete</button>
      </div>
    );
  };
});

jest.mock('../components/West/SummarySection', () => {
  return function MockSummarySection({
    onComplete,
    onBack,
  }: {
    onComplete?: () => void;
    onBack?: () => void;
  }) {
    return (
      <div data-testid="summary-section">
        <h2>Summary</h2>
        <button onClick={onBack}>Back</button>
        <button onClick={onComplete}>Complete</button>
      </div>
    );
  };
});

// Mock the formatCurrency utility
jest.mock('../../../utils/formatters', () => ({
  formatCurrency: jest.fn((value) => `$${value}`),
}));

describe('West Direction Integration', () => {
  const renderWestDirectionPage = () => {
    return render(
      <MemoryRouter initialEntries={['/compass/west']}>
        <ThemeProvider>
          <SeasonsOfSelfProvider>
            <FinancialCompassProvider>
              <GuidedJourneyProvider>
                <Routes>
                  <Route path="/compass/west" element={<WestDirectionPage />} />
                </Routes>
              </GuidedJourneyProvider>
            </FinancialCompassProvider>
          </SeasonsOfSelfProvider>
        </ThemeProvider>
      </MemoryRouter>
    );
  };

  it('renders the West Direction Tracker initially', () => {
    renderWestDirectionPage();
    expect(screen.getByTestId('west-direction-tracker')).toBeInTheDocument();
  });

  it('navigates through the West Direction journey', async () => {
    renderWestDirectionPage();

    // Start at the tracker
    expect(screen.getByTestId('west-direction-tracker')).toBeInTheDocument();

    // Go to Tax Planning
    fireEvent.click(screen.getByText('Go to Tax Planning'));
    await waitFor(() => {
      expect(screen.getByTestId('tax-planning')).toBeInTheDocument();
    });

    // Complete Tax Planning and go to Estate Planning
    fireEvent.click(screen.getByText('Complete'));
    await waitFor(() => {
      expect(screen.getByTestId('estate-planning')).toBeInTheDocument();
    });

    // Complete Estate Planning and go to Estate Documents
    fireEvent.click(screen.getByText('Complete'));
    await waitFor(() => {
      expect(screen.getByTestId('estate-documents')).toBeInTheDocument();
    });

    // Complete Estate Documents and go to Charitable Giving
    fireEvent.click(screen.getByText('Complete'));
    await waitFor(() => {
      expect(screen.getByTestId('charitable-giving')).toBeInTheDocument();
    });

    // Complete Charitable Giving and go to Legacy Planning
    fireEvent.click(screen.getByText('Complete'));
    await waitFor(() => {
      expect(screen.getByTestId('legacy-planning')).toBeInTheDocument();
    });

    // Complete Legacy Planning and go to Legacy Messages
    fireEvent.click(screen.getByText('Complete'));
    await waitFor(() => {
      expect(screen.getByTestId('legacy-messages')).toBeInTheDocument();
    });

    // Complete Legacy Messages and go to Values & Goals
    fireEvent.click(screen.getByText('Complete'));
    await waitFor(() => {
      expect(screen.getByTestId('values-goals')).toBeInTheDocument();
    });

    // Complete Values & Goals and go to Summary
    fireEvent.click(screen.getByText('Complete'));
    await waitFor(() => {
      expect(screen.getByTestId('summary-section')).toBeInTheDocument();
    });
  });

  it('allows navigation back through the journey', async () => {
    renderWestDirectionPage();

    // Navigate to Values & Goals
    fireEvent.click(screen.getByText('Go to Values & Goals'));
    await waitFor(() => {
      expect(screen.getByTestId('values-goals')).toBeInTheDocument();
    });

    // Go back to Legacy Messages
    fireEvent.click(screen.getByText('Back'));
    await waitFor(() => {
      expect(screen.getByTestId('legacy-messages')).toBeInTheDocument();
    });

    // Go back to Legacy Planning
    fireEvent.click(screen.getByText('Back'));
    await waitFor(() => {
      expect(screen.getByTestId('legacy-planning')).toBeInTheDocument();
    });

    // Go back to Charitable Giving
    fireEvent.click(screen.getByText('Back'));
    await waitFor(() => {
      expect(screen.getByTestId('charitable-giving')).toBeInTheDocument();
    });

    // Go back to Estate Documents
    fireEvent.click(screen.getByText('Back'));
    await waitFor(() => {
      expect(screen.getByTestId('estate-documents')).toBeInTheDocument();
    });

    // Go back to Estate Planning
    fireEvent.click(screen.getByText('Back'));
    await waitFor(() => {
      expect(screen.getByTestId('estate-planning')).toBeInTheDocument();
    });

    // Go back to Tax Planning
    fireEvent.click(screen.getByText('Back'));
    await waitFor(() => {
      expect(screen.getByTestId('tax-planning')).toBeInTheDocument();
    });

    // Go back to Overview
    fireEvent.click(screen.getByText('Back'));
    await waitFor(() => {
      expect(screen.getByTestId('west-direction-tracker')).toBeInTheDocument();
    });
  });
});
