/**
 * South Direction Integration Tests
 *
 * This file contains integration tests for the South Direction journey,
 * testing the flow between components and data persistence.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from '../../../context/ThemeContext';
import { FinancialCompassProvider } from '../context/FinancialCompassContext';
import { GuidedJourneyProvider } from '../../GuidedJourney/context/GuidedJourneyContext';
import { SeasonsOfSelfProvider } from '../../SeasonsOfSelf/context/SeasonsOfSelfContext';
import SouthDirectionPage from '../pages/SouthDirectionPage';

// Mock the components
jest.mock('../components/South/SouthDirectionTracker', () => {
  return function MockSouthDirectionTracker({
    onSectionSelect,
  }: {
    onSectionSelect: (id: string) => void;
  }) {
    return (
      <div data-testid="south-direction-tracker">
        <button onClick={() => onSectionSelect('insurance_coverage')}>
          Go to Insurance Coverage
        </button>
        <button onClick={() => onSectionSelect('insurance_calculator')}>
          Go to Insurance Calculator
        </button>
        <button onClick={() => onSectionSelect('healthcare_planning')}>
          Go to Healthcare Planning
        </button>
        <button onClick={() => onSectionSelect('healthcare_projections')}>
          Go to Healthcare Projections
        </button>
        <button onClick={() => onSectionSelect('risk_tolerance')}>Go to Risk Tolerance</button>
        <button onClick={() => onSectionSelect('protection_gap')}>Go to Protection Gap</button>
      </div>
    );
  };
});

jest.mock('../components/South/InsuranceCoverage', () => {
  return function MockInsuranceCoverage({
    onComplete,
    onBack,
  }: {
    onComplete?: () => void;
    onBack?: () => void;
  }) {
    return (
      <div data-testid="insurance-coverage">
        <h2>Insurance Coverage</h2>
        <button onClick={onBack}>Back</button>
        <button onClick={onComplete}>Complete</button>
      </div>
    );
  };
});

jest.mock('../components/South/InsuranceNeedsCalculator', () => {
  return function MockInsuranceNeedsCalculator({
    onComplete,
    onBack,
  }: {
    onComplete?: () => void;
    onBack?: () => void;
  }) {
    return (
      <div data-testid="insurance-calculator">
        <h2>Insurance Needs Calculator</h2>
        <button onClick={onBack}>Back</button>
        <button onClick={onComplete}>Complete</button>
      </div>
    );
  };
});

jest.mock('../components/South/HealthcarePlanning', () => {
  return function MockHealthcarePlanning({
    onComplete,
    onBack,
  }: {
    onComplete?: () => void;
    onBack?: () => void;
  }) {
    return (
      <div data-testid="healthcare-planning">
        <h2>Healthcare Planning</h2>
        <button onClick={onBack}>Back</button>
        <button onClick={onComplete}>Complete</button>
      </div>
    );
  };
});

jest.mock('../components/South/HealthcareCostProjections', () => {
  return function MockHealthcareCostProjections({
    onComplete,
    onBack,
  }: {
    onComplete?: () => void;
    onBack?: () => void;
  }) {
    return (
      <div data-testid="healthcare-projections">
        <h2>Healthcare Cost Projections</h2>
        <button onClick={onBack}>Back</button>
        <button onClick={onComplete}>Complete</button>
      </div>
    );
  };
});

jest.mock('../components/South/RiskTolerance', () => {
  return function MockRiskTolerance({
    onComplete,
    onBack,
  }: {
    onComplete?: () => void;
    onBack?: () => void;
  }) {
    return (
      <div data-testid="risk-tolerance">
        <h2>Risk Tolerance</h2>
        <button onClick={onBack}>Back</button>
        <button onClick={onComplete}>Complete</button>
      </div>
    );
  };
});

jest.mock('../components/South/ProtectionGap', () => {
  return function MockProtectionGap({
    onComplete,
    onBack,
  }: {
    onComplete?: () => void;
    onBack?: () => void;
  }) {
    return (
      <div data-testid="protection-gap">
        <h2>Protection Gap</h2>
        <button onClick={onBack}>Back</button>
        <button onClick={onComplete}>Complete</button>
      </div>
    );
  };
});

jest.mock('../components/South/SummarySection', () => {
  return function MockSummarySection({
    onComplete,
    onBack,
  }: {
    onComplete?: () => void;
    onBack?: () => void;
  }) {
    return (
      <div data-testid="summary-section">
        <h2>Summary</h2>
        <button onClick={onBack}>Back</button>
        <button onClick={onComplete}>Complete</button>
      </div>
    );
  };
});

// Mock the formatCurrency utility
jest.mock('../../../utils/formatters', () => ({
  formatCurrency: jest.fn((value) => `$${value}`),
}));

describe('South Direction Integration', () => {
  const renderSouthDirectionPage = () => {
    return render(
      <MemoryRouter initialEntries={['/compass/south']}>
        <ThemeProvider>
          <SeasonsOfSelfProvider>
            <FinancialCompassProvider>
              <GuidedJourneyProvider>
                <Routes>
                  <Route path="/compass/south" element={<SouthDirectionPage />} />
                </Routes>
              </GuidedJourneyProvider>
            </FinancialCompassProvider>
          </SeasonsOfSelfProvider>
        </ThemeProvider>
      </MemoryRouter>
    );
  };

  it('renders the South Direction Tracker initially', () => {
    renderSouthDirectionPage();
    expect(screen.getByTestId('south-direction-tracker')).toBeInTheDocument();
  });

  it('navigates through the South Direction journey', async () => {
    renderSouthDirectionPage();

    // Start at the tracker
    expect(screen.getByTestId('south-direction-tracker')).toBeInTheDocument();

    // Go to Insurance Coverage
    fireEvent.click(screen.getByText('Go to Insurance Coverage'));
    await waitFor(() => {
      expect(screen.getByTestId('insurance-coverage')).toBeInTheDocument();
    });

    // Complete Insurance Coverage and go to Insurance Calculator
    fireEvent.click(screen.getByText('Complete'));
    await waitFor(() => {
      expect(screen.getByTestId('insurance-calculator')).toBeInTheDocument();
    });

    // Complete Insurance Calculator and go to Healthcare Planning
    fireEvent.click(screen.getByText('Complete'));
    await waitFor(() => {
      expect(screen.getByTestId('healthcare-planning')).toBeInTheDocument();
    });

    // Complete Healthcare Planning and go to Healthcare Projections
    fireEvent.click(screen.getByText('Complete'));
    await waitFor(() => {
      expect(screen.getByTestId('healthcare-projections')).toBeInTheDocument();
    });

    // Complete Healthcare Projections and go to Risk Tolerance
    fireEvent.click(screen.getByText('Complete'));
    await waitFor(() => {
      expect(screen.getByTestId('risk-tolerance')).toBeInTheDocument();
    });

    // Complete Risk Tolerance and go to Protection Gap
    fireEvent.click(screen.getByText('Complete'));
    await waitFor(() => {
      expect(screen.getByTestId('protection-gap')).toBeInTheDocument();
    });

    // Complete Protection Gap and go to Summary
    fireEvent.click(screen.getByText('Complete'));
    await waitFor(() => {
      expect(screen.getByTestId('summary-section')).toBeInTheDocument();
    });
  });

  it('allows navigation back through the journey', async () => {
    renderSouthDirectionPage();

    // Navigate to Protection Gap
    fireEvent.click(screen.getByText('Go to Protection Gap'));
    await waitFor(() => {
      expect(screen.getByTestId('protection-gap')).toBeInTheDocument();
    });

    // Go back to Risk Tolerance
    fireEvent.click(screen.getByText('Back'));
    await waitFor(() => {
      expect(screen.getByTestId('risk-tolerance')).toBeInTheDocument();
    });

    // Go back to Healthcare Projections
    fireEvent.click(screen.getByText('Back'));
    await waitFor(() => {
      expect(screen.getByTestId('healthcare-projections')).toBeInTheDocument();
    });

    // Go back to Healthcare Planning
    fireEvent.click(screen.getByText('Back'));
    await waitFor(() => {
      expect(screen.getByTestId('healthcare-planning')).toBeInTheDocument();
    });

    // Go back to Insurance Calculator
    fireEvent.click(screen.getByText('Back'));
    await waitFor(() => {
      expect(screen.getByTestId('insurance-calculator')).toBeInTheDocument();
    });

    // Go back to Insurance Coverage
    fireEvent.click(screen.getByText('Back'));
    await waitFor(() => {
      expect(screen.getByTestId('insurance-coverage')).toBeInTheDocument();
    });

    // Go back to Overview
    fireEvent.click(screen.getByText('Back'));
    await waitFor(() => {
      expect(screen.getByTestId('south-direction-tracker')).toBeInTheDocument();
    });
  });
});
