/**
 * Monte Carlo Simulation Component
 *
 * This component displays retirement projection results using Monte Carlo simulation
 * to show the probability of retirement success.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { formatCurrency } from '../../../../utils/formatters';
import {
  RetirementProjectionOptions,
  runMonteCarloSimulation,
  MonteCarloSimulationResult,
  RetirementAccount,
  RetirementIncomeSource,
  RetirementExpense,
} from '../../../../utils/retirementProjections';

interface MonteCarloSimulationProps {
  onClose?: () => void;
}

const MonteCarloSimulation: React.FC<MonteCarloSimulationProps> = ({ onClose }) => {
  const { theme } = useTheme();
  const { data } = useFinancialCompass();
  const [isLoading, setIsLoading] = useState(true);
  const [simulationResult, setSimulationResult] = useState<MonteCarloSimulationResult | null>(null);
  const [simulationCount, setSimulationCount] = useState(1000);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [customOptions, setCustomOptions] = useState({
    inflationRate: 2.5,
    taxRate: 25,
    simulationCount: 1000,
  });

  // Extract data from context
  const personalInfo = data.north?.personalInformation || {};
  const retirementGoals = data.east?.retirementGoals || {};
  const retirementIncome = data.east?.retirementIncome || {};
  const retirementExpenses = data.east?.retirementExpenses || {};
  const assets = data.north?.assets || {};

  // Run simulation when component mounts
  useEffect(() => {
    runSimulation();
  }, []);

  // Handle running the simulation
  const runSimulation = () => {
    setIsLoading(true);

    // Extract data for simulation
    const currentAge = parseInt((personalInfo as any)?.age || '30');
    const retirementAge = parseInt((retirementGoals as any)?.retirementAge || '65');
    const lifeExpectancy = parseInt((retirementGoals as any)?.lifeExpectancy || '90');

    // Create retirement accounts from assets
    const retirementAccounts: RetirementAccount[] = [];

    // 401(k) account
    if ((assets as any)?.retirement?.['401k']) {
      retirementAccounts.push({
        id: '401k',
        name: '401(k)',
        balance: parseFloat((assets as any)?.retirement?.['401k'] || '0'),
        type: 'traditional',
        annualContribution: 19500, // Default max contribution
        annualReturn: 0.07, // 7% average return
        fees: 0.005, // 0.5% fees
      });
    }

    // IRA account
    if ((assets as any)?.retirement?.ira) {
      retirementAccounts.push({
        id: 'ira',
        name: 'IRA',
        balance: parseFloat((assets as any)?.retirement?.ira || '0'),
        type: 'traditional',
        annualContribution: 6000, // Default max contribution
        annualReturn: 0.07, // 7% average return
        fees: 0.003, // 0.3% fees
      });
    }

    // Roth IRA account
    if ((assets as any)?.retirement?.rothIra) {
      retirementAccounts.push({
        id: 'rothIra',
        name: 'Roth IRA',
        balance: parseFloat((assets as any)?.retirement?.rothIra || '0'),
        type: 'roth',
        annualContribution: 6000, // Default max contribution
        annualReturn: 0.07, // 7% average return
        fees: 0.003, // 0.3% fees
      });
    }

    // Taxable investment account
    if ((assets as any)?.investments?.taxable) {
      retirementAccounts.push({
        id: 'taxable',
        name: 'Taxable Investments',
        balance: parseFloat((assets as any)?.investments?.taxable || '0'),
        type: 'taxable',
        annualContribution: 12000, // Default annual contribution
        annualReturn: 0.07, // 7% average return
        fees: 0.002, // 0.2% fees
      });
    }

    // Create income sources
    const incomeSources: RetirementIncomeSource[] = [];

    // Social Security
    const socialSecurityStartAge = parseInt(
      (retirementIncome as any)?.socialSecurity?.startAge || '67'
    );
    const socialSecurityMonthlyBenefit = parseFloat(
      (retirementIncome as any)?.socialSecurity?.estimatedMonthlyBenefit || '1500'
    );

    incomeSources.push({
      id: 'socialSecurity',
      name: 'Social Security',
      monthlyAmount: socialSecurityMonthlyBenefit,
      startAge: socialSecurityStartAge,
      inflationAdjusted: true,
    });

    // Pension if applicable
    if ((retirementIncome as any)?.pension?.hasEmployerPension) {
      incomeSources.push({
        id: 'pension',
        name: 'Pension',
        monthlyAmount: parseFloat(
          (retirementIncome as any)?.pension?.estimatedMonthlyBenefit || '0'
        ),
        startAge: parseInt((retirementIncome as any)?.pension?.startAge || '65'),
        inflationAdjusted: false,
      });
    }

    // Add other income sources
    const otherIncomeSources = Array.isArray((retirementIncome as any)?.incomeSources)
      ? (retirementIncome as any)?.incomeSources
      : [];

    otherIncomeSources.forEach((source: any, index: number) => {
      incomeSources.push({
        id: `other_${index}`,
        name: source.type || `Income Source ${index + 1}`,
        monthlyAmount: parseFloat(source.amount || '0'),
        startAge: parseInt(source.startAge || retirementAge.toString()),
        endAge: source.endAge ? parseInt(source.endAge) : undefined,
        inflationAdjusted: source.inflationAdjusted || false,
      });
    });

    // Create expenses
    const expenses: RetirementExpense[] = [];

    // Essential expenses
    expenses.push({
      id: 'essential',
      name: 'Essential Expenses',
      monthlyAmount: parseFloat((retirementExpenses as any)?.essentialExpenses || '4000'),
      startAge: retirementAge,
      isEssential: true,
      inflationAdjusted: true,
    });

    // Discretionary expenses
    expenses.push({
      id: 'discretionary',
      name: 'Discretionary Expenses',
      monthlyAmount: parseFloat((retirementExpenses as any)?.discretionaryExpenses || '2000'),
      startAge: retirementAge,
      isEssential: false,
      inflationAdjusted: true,
    });

    // Healthcare expenses
    expenses.push({
      id: 'healthcare',
      name: 'Healthcare Expenses',
      monthlyAmount: parseFloat((retirementExpenses as any)?.healthcareCosts || '800'),
      startAge: retirementAge,
      isEssential: true,
      inflationAdjusted: true,
    });

    // Create projection options
    const projectionOptions: RetirementProjectionOptions = {
      currentAge,
      retirementAge,
      lifeExpectancy,
      accounts: retirementAccounts,
      incomeSources,
      expenses,
      inflationRate: customOptions.inflationRate / 100,
      taxRate: customOptions.taxRate / 100,
      socialSecurityStartAge,
      socialSecurityMonthlyBenefit,
    };

    // Run Monte Carlo simulation
    setTimeout(() => {
      try {
        const result = runMonteCarloSimulation(projectionOptions, customOptions.simulationCount);
        setSimulationResult(result);
      } catch (error) {
        console.error('Error running Monte Carlo simulation:', error);
      } finally {
        setIsLoading(false);
      }
    }, 100); // Small timeout to allow UI to update
  };

  // Handle changes to custom options
  const handleOptionChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCustomOptions((prev) => ({
      ...prev,
      [name]: parseFloat(value),
    }));
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Retirement Monte Carlo Simulation</Title>
        <Description theme={theme}>
          This simulation runs {customOptions.simulationCount} different scenarios to estimate the
          probability of your retirement plan succeeding.
        </Description>
      </Header>

      {isLoading ? (
        <LoadingContainer theme={theme}>
          <LoadingSpinner />
          <LoadingText>Running {customOptions.simulationCount} simulations...</LoadingText>
        </LoadingContainer>
      ) : simulationResult ? (
        <ResultsContainer theme={theme}>
          <ProbabilityCard theme={theme} probability={simulationResult.successProbability}>
            <ProbabilityValue>{Math.round(simulationResult.successProbability)}%</ProbabilityValue>
            <ProbabilityLabel>Success Probability</ProbabilityLabel>
            <ProbabilityDescription>
              {simulationResult.successProbability >= 85
                ? 'Excellent chance of retirement success!'
                : simulationResult.successProbability >= 70
                  ? 'Good chance of retirement success, but consider some adjustments.'
                  : simulationResult.successProbability >= 50
                    ? 'Moderate chance of success. Consider significant adjustments to your plan.'
                    : 'Low probability of success. Your retirement plan needs major adjustments.'}
            </ProbabilityDescription>
          </ProbabilityCard>

          <ResultsGrid>
            <ResultCard theme={theme}>
              <ResultLabel>Median Ending Balance</ResultLabel>
              <ResultValue>{formatCurrency(simulationResult.medianEndingBalance)}</ResultValue>
            </ResultCard>

            <ResultCard theme={theme}>
              <ResultLabel>Worst Case Ending Balance</ResultLabel>
              <ResultValue negative={simulationResult.worstCaseEndingBalance < 0}>
                {formatCurrency(simulationResult.worstCaseEndingBalance)}
              </ResultValue>
            </ResultCard>

            <ResultCard theme={theme}>
              <ResultLabel>Best Case Ending Balance</ResultLabel>
              <ResultValue>{formatCurrency(simulationResult.bestCaseEndingBalance)}</ResultValue>
            </ResultCard>

            <ResultCard theme={theme}>
              <ResultLabel>Median Portfolio Survival Age</ResultLabel>
              <ResultValue>{simulationResult.medianPortfolioSurvivalAge}</ResultValue>
            </ResultCard>
          </ResultsGrid>

          <AdvancedOptionsToggle onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}>
            {showAdvancedOptions ? 'Hide Advanced Options' : 'Show Advanced Options'}
          </AdvancedOptionsToggle>

          {showAdvancedOptions && (
            <AdvancedOptions theme={theme}>
              <OptionGroup>
                <OptionLabel htmlFor="inflationRate">Inflation Rate (%)</OptionLabel>
                <OptionInput
                  type="number"
                  id="inflationRate"
                  name="inflationRate"
                  min="0"
                  max="10"
                  step="0.1"
                  value={customOptions.inflationRate}
                  onChange={handleOptionChange}
                />
              </OptionGroup>

              <OptionGroup>
                <OptionLabel htmlFor="taxRate">Tax Rate (%)</OptionLabel>
                <OptionInput
                  type="number"
                  id="taxRate"
                  name="taxRate"
                  min="0"
                  max="50"
                  step="1"
                  value={customOptions.taxRate}
                  onChange={handleOptionChange}
                />
              </OptionGroup>

              <OptionGroup>
                <OptionLabel htmlFor="simulationCount">Simulation Count</OptionLabel>
                <OptionInput
                  type="number"
                  id="simulationCount"
                  name="simulationCount"
                  min="100"
                  max="10000"
                  step="100"
                  value={customOptions.simulationCount}
                  onChange={handleOptionChange}
                />
              </OptionGroup>

              <RunButton onClick={runSimulation} theme={theme}>
                Run Simulation
              </RunButton>
            </AdvancedOptions>
          )}
        </ResultsContainer>
      ) : (
        <ErrorMessage theme={theme}>
          Unable to run simulation. Please check your retirement data and try again.
        </ErrorMessage>
      )}

      <ButtonContainer>
        {onClose && (
          <CloseButton onClick={onClose} theme={theme}>
            Close
          </CloseButton>
        )}
      </ButtonContainer>
    </Container>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  padding: 24px;
  max-width: 800px;
  margin: 0 auto;
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 24px;
  text-align: center;
`;

const Title = styled.h2<{ theme: any }>`
  margin: 0 0 8px 0;
  font-size: 1.8rem;
  color: ${(props) => props.theme.colors.primary.main};
`;

const Description = styled.p<{ theme: any }>`
  margin: 0;
  font-size: 1rem;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const LoadingContainer = styled.div<{ theme: any }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const LoadingSpinner = styled.div`
  width: 48px;
  height: 48px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3f51b5;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
`;

const LoadingText = styled.p`
  font-size: 1rem;
  color: #666;
`;

const ResultsContainer = styled.div<{ theme: any }>`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const ProbabilityCard = styled.div<{ theme: any; probability: number }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border-left: 6px solid
    ${(props) => {
      if (props.probability >= 85) return '#4CAF50';
      if (props.probability >= 70) return '#8BC34A';
      if (props.probability >= 50) return '#FFC107';
      return '#F44336';
    }};
`;

const ProbabilityValue = styled.div`
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 8px;
`;

const ProbabilityLabel = styled.div`
  font-size: 1.2rem;
  font-weight: 500;
  margin-bottom: 16px;
`;

const ProbabilityDescription = styled.p`
  font-size: 1rem;
  color: #666;
  margin: 0;
`;

const ResultsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
`;

const ResultCard = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const ResultLabel = styled.div`
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 8px;
`;

const ResultValue = styled.div<{ negative?: boolean }>`
  font-size: 1.4rem;
  font-weight: 600;
  color: ${(props) => (props.negative ? '#F44336' : 'inherit')};
`;

const AdvancedOptionsToggle = styled.button`
  background: none;
  border: none;
  color: #3f51b5;
  font-size: 0.9rem;
  cursor: pointer;
  padding: 8px;
  text-decoration: underline;
  align-self: center;

  &:hover {
    color: #303f9f;
  }
`;

const AdvancedOptions = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 16px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const OptionGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const OptionLabel = styled.label`
  font-size: 0.9rem;
  color: #666;
`;

const OptionInput = styled.input`
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
`;

const RunButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  grid-column: 1 / -1;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

const ErrorMessage = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.error.light};
  color: ${(props) => props.theme.colors.error.main};
  padding: 16px;
  border-radius: 8px;
  text-align: center;
  margin: 24px 0;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 24px;
`;

const CloseButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.secondary.main};
  color: white;
  border: none;
  border-radius: 4px;
  padding: 12px 24px;
  font-size: 1rem;
  cursor: pointer;

  &:hover {
    background-color: ${(props) => props.theme.colors.secondary.dark};
  }
`;

export default MonteCarloSimulation;
