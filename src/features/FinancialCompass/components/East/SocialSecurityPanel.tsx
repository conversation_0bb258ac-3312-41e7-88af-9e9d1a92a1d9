import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Typography, Box, Divider, TextField, Button } from '@mui/material';

const calculateLifetimeBenefits = (monthly: number, age: number) => {
  // Assume benefits last from claiming age to 85
  const years = 85 - age;
  return monthly * 12 * years;
};

const SocialSecurityPanel: React.FC = () => {
  const [monthlyBenefit, setMonthlyBenefit] = useState('');
  const [claimingAge, setClaimingAge] = useState('67');
  const [errors, setErrors] = useState<{ monthlyBenefit?: string; claimingAge?: string }>({});
  const [submitted, setSubmitted] = useState(false);
  const [lifetime, setLifetime] = useState<number | null>(null);
  const [recommendation, setRecommendation] = useState<string | null>(null);

  const validate = () => {
    const newErrors: { monthlyBenefit?: string; claimingAge?: string } = {};
    if (!monthlyBenefit || isNaN(Number(monthlyBenefit)) || Number(monthlyBenefit) < 0) {
      newErrors.monthlyBenefit = 'Monthly benefit must be a positive number';
    }
    if (
      !claimingAge ||
      isNaN(Number(claimingAge)) ||
      Number(claimingAge) < 62 ||
      Number(claimingAge) > 70
    ) {
      newErrors.claimingAge = 'Claiming age must be between 62 and 70';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitted(true);
    if (validate()) {
      const age = Number(claimingAge);
      const monthly = Number(monthlyBenefit);
      const total = calculateLifetimeBenefits(monthly, age);
      setLifetime(total);
      // Simple recommendation: later claiming = higher total if you live to 85
      if (age < 67) {
        setRecommendation(
          'Delaying claiming may increase your total lifetime benefits if you expect to live past 80.'
        );
      } else if (age > 67) {
        setRecommendation(
          'You are maximizing your monthly benefit by delaying, but consider your health and income needs.'
        );
      } else {
        setRecommendation('Claiming at full retirement age provides a balanced benefit.');
      }
    }
  };

  return (
    <Card variant="outlined">
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Social Security Planning
        </Typography>
        <Divider sx={{ mb: 2 }} />
        <Box mb={2}>
          <Typography variant="body2" color="textSecondary">
            Enter your estimated monthly benefit and planned claiming age to see a lifetime
            projection.
          </Typography>
        </Box>
        <form onSubmit={handleSubmit} noValidate>
          <Box display="flex" gap={2} mb={2}>
            <TextField
              label="Estimated Monthly Benefit ($)"
              value={monthlyBenefit}
              onChange={(e) => setMonthlyBenefit(e.target.value)}
              error={!!errors.monthlyBenefit && submitted}
              helperText={submitted && errors.monthlyBenefit}
              type="number"
              inputProps={{ min: 0 }}
            />
            <TextField
              label="Claiming Age"
              value={claimingAge}
              onChange={(e) => setClaimingAge(e.target.value)}
              error={!!errors.claimingAge && submitted}
              helperText={submitted && errors.claimingAge}
              type="number"
              inputProps={{ min: 62, max: 70 }}
            />
            <Button type="submit" variant="contained" color="primary">
              Analyze
            </Button>
          </Box>
        </form>
        {lifetime !== null && (
          <Box mt={3}>
            <Typography variant="subtitle1" gutterBottom>
              Estimated Lifetime Benefits:
            </Typography>
            <Typography variant="h5" color="primary" gutterBottom>
              ${lifetime.toLocaleString(undefined, { maximumFractionDigits: 0 })}
            </Typography>
            {recommendation && (
              <Box mt={2}>
                <Typography variant="body2" color="textSecondary">
                  {recommendation}
                </Typography>
              </Box>
            )}
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default SocialSecurityPanel;
