/**
 * East Direction Components Tests
 *
 * This file contains tests for the East direction components.
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import {
  RetirementGoals,
  RetirementIncome,
  RetirementExpenses,
  RetirementTimeline,
  EastDirectionTracker,
} from './index';

// Mock the context providers
jest.mock('../../../../context/ThemeContext', () => ({
  useTheme: () => ({
    theme: {
      colors: {
        primary: { main: '#1976d2', light: '#42a5f5', dark: '#0d47a1', contrastText: '#ffffff' },
        secondary: { main: '#dc004e', light: '#ff5c8d', dark: '#9a0036', contrastText: '#ffffff' },
        text: { primary: '#000000', secondary: '#666666' },
        background: { default: '#f5f5f5', paper: '#ffffff' },
        border: '#e0e0e0',
      },
    },
  }),
}));

jest.mock('../../context/FinancialCompassContext', () => ({
  useFinancialCompass: () => ({
    data: {
      east: {},
    },
    updateData: jest.fn(),
  }),
}));

jest.mock('../../../GuidedJourney/context/GuidedJourneyContext', () => ({
  useGuidedJourney: () => ({
    askQuestion: jest.fn(),
  }),
}));

// Simple test to verify components are exported correctly
describe('East Direction Components', () => {
  test('RetirementGoals component renders correctly', () => {
    render(<RetirementGoals />);
    expect(screen.getByText('Retirement Goals')).toBeInTheDocument();
  });

  test('RetirementIncome component renders correctly', () => {
    render(<RetirementIncome />);
    expect(screen.getByText('Retirement Income')).toBeInTheDocument();
  });

  test('RetirementExpenses component renders correctly', () => {
    render(<RetirementExpenses />);
    expect(screen.getByText('Retirement Expenses')).toBeInTheDocument();
  });

  test('RetirementTimeline component renders correctly', () => {
    render(<RetirementTimeline />);
    expect(screen.getByText('Retirement Timeline')).toBeInTheDocument();
  });

  test('EastDirectionTracker component renders correctly', () => {
    render(<EastDirectionTracker />);
    expect(screen.getByText('East Direction Progress')).toBeInTheDocument();
  });
});
