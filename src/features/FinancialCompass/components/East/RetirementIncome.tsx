/**
 * Retirement Income Component
 *
 * This component collects and displays the user's retirement income sources as part of the
 * East direction of the Financial Compass ("Where You're Going").
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { useGuidedJourney } from '../../../GuidedJourney/context/GuidedJourneyContext';
import { formatCurrency } from '../../../../utils/formatters';

interface RetirementIncomeProps {
  onComplete?: () => void;
  onBack?: () => void;
}

interface IncomeSource {
  id: string;
  type: string;
  amount: string;
  frequency: 'monthly' | 'annual' | 'one-time';
  startAge: string;
  endAge: string;
  notes: string;
}

interface RetirementIncomeData {
  socialSecurity: {
    estimatedMonthlyBenefit: string;
    startAge: string;
  };
  pension: {
    hasEmployerPension: boolean;
    estimatedMonthlyBenefit: string;
    startAge: string;
  };
  incomeSources: IncomeSource[];
}

const RetirementIncome: React.FC<RetirementIncomeProps> = ({ onComplete, onBack }) => {
  const { theme } = useTheme();
  const { data, updateData } = useFinancialCompass();
  const { askQuestion } = useGuidedJourney();

  const [isSaving, setIsSaving] = useState(false);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Initialize form data from context or with defaults
  const [formData, setFormData] = useState<RetirementIncomeData>(
    (data.east?.retirementIncome as RetirementIncomeData) || {
      socialSecurity: {
        estimatedMonthlyBenefit: '1500',
        startAge: '67',
      },
      pension: {
        hasEmployerPension: false,
        estimatedMonthlyBenefit: '0',
        startAge: '65',
      },
      incomeSources: [],
    }
  );

  // New income source form
  const [newIncomeSource, setNewIncomeSource] = useState<Omit<IncomeSource, 'id'>>({
    type: '',
    amount: '',
    frequency: 'monthly',
    startAge: '',
    endAge: '',
    notes: '',
  });

  // Income source type options
  const incomeSourceTypes = [
    'Part-time work',
    'Rental income',
    'Investment dividends',
    'Annuity',
    'Business income',
    'Royalties',
    'Other',
  ];

  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (name.startsWith('socialSecurity.')) {
      const field = name.split('.')[1];
      setFormData((prev) => ({
        ...prev,
        socialSecurity: {
          ...prev.socialSecurity,
          [field]: value,
        },
      }));
    } else if (name.startsWith('pension.')) {
      const field = name.split('.')[1];

      // Handle checkbox separately
      if (type === 'checkbox') {
        const isChecked = (e.target as HTMLInputElement).checked;
        setFormData((prev) => ({
          ...prev,
          pension: {
            ...prev.pension,
            [field]: isChecked,
          },
        }));
      } else {
        setFormData((prev) => ({
          ...prev,
          pension: {
            ...prev.pension,
            [field]: value,
          },
        }));
      }
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  // Handle new income source field changes
  const handleNewIncomeSourceChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setNewIncomeSource((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Add new income source
  const handleAddIncomeSource = () => {
    if (!newIncomeSource.type || !newIncomeSource.amount) return;

    const newSource: IncomeSource = {
      ...newIncomeSource,
      id: `income-${Date.now()}`,
    };

    setFormData((prev) => ({
      ...prev,
      incomeSources: [...prev.incomeSources, newSource],
    }));

    // Reset new income source form
    setNewIncomeSource({
      type: '',
      amount: '',
      frequency: 'monthly',
      startAge: '',
      endAge: '',
      notes: '',
    });
  };

  // Remove income source
  const handleRemoveIncomeSource = (id: string) => {
    setFormData((prev) => ({
      ...prev,
      incomeSources: prev.incomeSources.filter((source) => source.id !== id),
    }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSave();

    if (onComplete) {
      onComplete();
    }
  };

  // Handle saving data
  const handleSave = () => {
    setIsSaving(true);

    // Calculate total monthly income
    const monthlyIncome = calculateTotalMonthlyIncome();

    // Update data in context
    updateData('east', 'retirementIncome', {
      ...formData,
      monthlyIncome,
    });

    // Show save indicator
    setShowSaveIndicator(true);

    // Hide save indicator after 2 seconds
    setTimeout(() => {
      setIsSaving(false);
      setShowSaveIndicator(false);
    }, 2000);
  };

  // Calculate total monthly income
  const calculateTotalMonthlyIncome = () => {
    let total = 0;

    // Add Social Security
    total += parseFloat(formData.socialSecurity.estimatedMonthlyBenefit) || 0;

    // Add pension if applicable
    if (formData.pension.hasEmployerPension) {
      total += parseFloat(formData.pension.estimatedMonthlyBenefit) || 0;
    }

    // Add other income sources (converted to monthly)
    formData.incomeSources.forEach((source) => {
      const amount = parseFloat(source.amount) || 0;

      if (source.frequency === 'monthly') {
        total += amount;
      } else if (source.frequency === 'annual') {
        total += amount / 12;
      }
      // One-time sources are not included in monthly calculations
    });

    return total;
  };

  // Calculate annual income
  const calculateAnnualIncome = () => {
    return calculateTotalMonthlyIncome() * 12;
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Retirement Income Sources</Title>
        <Description theme={theme}>
          Identify and estimate your income sources during retirement.
        </Description>
      </Header>

      <form onSubmit={handleSubmit}>
        <FormSection>
          <SectionTitle>Social Security Benefits</SectionTitle>
          <FormRow>
            <FormField>
              <Label htmlFor="socialSecurity.estimatedMonthlyBenefit">
                Estimated Monthly Benefit
              </Label>
              <Input
                type="number"
                id="socialSecurity.estimatedMonthlyBenefit"
                name="socialSecurity.estimatedMonthlyBenefit"
                value={formData.socialSecurity.estimatedMonthlyBenefit}
                onChange={handleChange}
                min="0"
                step="10"
                required
              />
              <FieldHint>
                <a
                  href="https://www.ssa.gov/benefits/retirement/estimator.html"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Get an estimate from SSA.gov
                </a>
              </FieldHint>
            </FormField>

            <FormField>
              <Label htmlFor="socialSecurity.startAge">Benefit Start Age</Label>
              <Select
                id="socialSecurity.startAge"
                name="socialSecurity.startAge"
                value={formData.socialSecurity.startAge}
                onChange={handleChange}
                required
              >
                <option value="62">62 (Early)</option>
                <option value="67">67 (Full Retirement Age)</option>
                <option value="70">70 (Maximum Benefit)</option>
              </Select>
              <FieldHint>Age when you plan to start receiving benefits</FieldHint>
            </FormField>
          </FormRow>
        </FormSection>

        <FormSection>
          <SectionTitle>Pension</SectionTitle>
          <CheckboxField>
            <Checkbox
              type="checkbox"
              id="pension.hasEmployerPension"
              name="pension.hasEmployerPension"
              checked={formData.pension.hasEmployerPension}
              onChange={handleChange}
            />
            <CheckboxLabel htmlFor="pension.hasEmployerPension">
              I have an employer pension plan
            </CheckboxLabel>
          </CheckboxField>

          {formData.pension.hasEmployerPension && (
            <FormRow>
              <FormField>
                <Label htmlFor="pension.estimatedMonthlyBenefit">Estimated Monthly Benefit</Label>
                <Input
                  type="number"
                  id="pension.estimatedMonthlyBenefit"
                  name="pension.estimatedMonthlyBenefit"
                  value={formData.pension.estimatedMonthlyBenefit}
                  onChange={handleChange}
                  min="0"
                  step="10"
                  required={formData.pension.hasEmployerPension}
                />
              </FormField>

              <FormField>
                <Label htmlFor="pension.startAge">Benefit Start Age</Label>
                <Input
                  type="number"
                  id="pension.startAge"
                  name="pension.startAge"
                  value={formData.pension.startAge}
                  onChange={handleChange}
                  min="50"
                  max="90"
                  required={formData.pension.hasEmployerPension}
                />
              </FormField>
            </FormRow>
          )}
        </FormSection>

        <FormSection>
          <SectionTitle>Additional Income Sources</SectionTitle>

          {formData.incomeSources.length > 0 && (
            <IncomeSourcesList>
              {formData.incomeSources.map((source) => (
                <IncomeSourceItem key={source.id} theme={theme}>
                  <IncomeSourceHeader>
                    <IncomeSourceType>{source.type}</IncomeSourceType>
                    <RemoveButton onClick={() => handleRemoveIncomeSource(source.id)} theme={theme}>
                      Remove
                    </RemoveButton>
                  </IncomeSourceHeader>

                  <IncomeSourceDetails>
                    <IncomeSourceAmount>
                      {formatCurrency(Number(source.amount) || 0)}
                      <IncomeSourceFrequency>({source.frequency})</IncomeSourceFrequency>
                    </IncomeSourceAmount>

                    <IncomeSourceTiming>
                      Ages {source.startAge || '?'} to {source.endAge || 'end of life'}
                    </IncomeSourceTiming>

                    {source.notes && <IncomeSourceNotes>{source.notes}</IncomeSourceNotes>}
                  </IncomeSourceDetails>
                </IncomeSourceItem>
              ))}
            </IncomeSourcesList>
          )}

          <AddIncomeSourceForm theme={theme}>
            <AddIncomeSourceTitle>Add Income Source</AddIncomeSourceTitle>

            <FormRow>
              <FormField>
                <Label htmlFor="type">Income Type</Label>
                <Select
                  id="type"
                  name="type"
                  value={newIncomeSource.type}
                  onChange={handleNewIncomeSourceChange}
                >
                  <option value="">Select type...</option>
                  {incomeSourceTypes.map((type) => (
                    <option key={type} value={type}>
                      {type}
                    </option>
                  ))}
                </Select>
              </FormField>

              <FormField>
                <Label htmlFor="amount">Amount</Label>
                <Input
                  type="number"
                  id="amount"
                  name="amount"
                  value={newIncomeSource.amount}
                  onChange={handleNewIncomeSourceChange}
                  min="0"
                  step="10"
                />
              </FormField>

              <FormField>
                <Label htmlFor="frequency">Frequency</Label>
                <Select
                  id="frequency"
                  name="frequency"
                  value={newIncomeSource.frequency}
                  onChange={handleNewIncomeSourceChange}
                >
                  <option value="monthly">Monthly</option>
                  <option value="annual">Annual</option>
                  <option value="one-time">One-time</option>
                </Select>
              </FormField>
            </FormRow>

            <FormRow>
              <FormField>
                <Label htmlFor="startAge">Start Age</Label>
                <Input
                  type="number"
                  id="startAge"
                  name="startAge"
                  value={newIncomeSource.startAge}
                  onChange={handleNewIncomeSourceChange}
                  min="50"
                  max="100"
                  placeholder="e.g., 65"
                />
              </FormField>

              <FormField>
                <Label htmlFor="endAge">End Age (optional)</Label>
                <Input
                  type="number"
                  id="endAge"
                  name="endAge"
                  value={newIncomeSource.endAge}
                  onChange={handleNewIncomeSourceChange}
                  min="50"
                  max="120"
                  placeholder="Leave blank if lifelong"
                />
              </FormField>
            </FormRow>

            <FormField>
              <Label htmlFor="notes">Notes (optional)</Label>
              <Textarea
                id="notes"
                name="notes"
                value={newIncomeSource.notes}
                onChange={handleNewIncomeSourceChange}
                placeholder="Additional details about this income source"
              />
            </FormField>

            <AddButton
              type="button"
              onClick={handleAddIncomeSource}
              disabled={!newIncomeSource.type || !newIncomeSource.amount}
              theme={theme}
            >
              Add Income Source
            </AddButton>
          </AddIncomeSourceForm>
        </FormSection>

        <FormSection>
          <SectionTitle>Income Summary</SectionTitle>

          <SummaryContainer theme={theme}>
            <SummaryRow>
              <SummaryLabel>Estimated Monthly Retirement Income:</SummaryLabel>
              <SummaryValue>{formatCurrency(calculateTotalMonthlyIncome())}</SummaryValue>
            </SummaryRow>

            <SummaryRow>
              <SummaryLabel>Estimated Annual Retirement Income:</SummaryLabel>
              <SummaryValue highlight>{formatCurrency(calculateAnnualIncome())}</SummaryValue>
            </SummaryRow>
          </SummaryContainer>
        </FormSection>

        <ButtonContainer>
          {onBack && (
            <BackButton type="button" onClick={onBack} theme={theme}>
              Back
            </BackButton>
          )}

          <SaveIndicator visible={showSaveIndicator}>✓ Saved</SaveIndicator>

          <SubmitButton type="submit" theme={theme}>
            {isSaving ? 'Saving...' : 'Save & Continue'}
          </SubmitButton>
        </ButtonContainer>
      </form>
    </Container>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  padding: 24px;
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const Title = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const Description = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const FormSection = styled.div`
  margin-bottom: 32px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;

const FormField = styled.div`
  margin-bottom: 16px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
`;

const Input = styled.input`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
`;

const Select = styled.select`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
`;

const Textarea = styled.textarea`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  min-height: 80px;
  resize: vertical;
`;

const FieldHint = styled.div`
  font-size: 0.8rem;
  color: #666;
  margin-top: 4px;
`;

const CheckboxField = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 16px;
`;

const Checkbox = styled.input`
  margin-right: 8px;
`;

const CheckboxLabel = styled.label`
  font-size: 1rem;
`;

const IncomeSourcesList = styled.div`
  margin-bottom: 24px;
`;

const IncomeSourceItem = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 12px;
`;

const IncomeSourceHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
`;

const IncomeSourceType = styled.div`
  font-weight: 600;
  font-size: 1.1rem;
`;

const RemoveButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.error.main};
  border: none;
  font-size: 0.9rem;
  cursor: pointer;
  padding: 4px 8px;

  &:hover {
    text-decoration: underline;
  }
`;

const IncomeSourceDetails = styled.div`
  font-size: 0.9rem;
`;

const IncomeSourceAmount = styled.div`
  font-weight: 500;
  margin-bottom: 4px;
`;

const IncomeSourceFrequency = styled.span`
  font-weight: normal;
  margin-left: 4px;
`;

const IncomeSourceTiming = styled.div`
  color: #666;
  margin-bottom: 4px;
`;

const IncomeSourceNotes = styled.div`
  font-style: italic;
  margin-top: 8px;
`;

const AddIncomeSourceForm = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 4px;
  padding: 16px;
`;

const AddIncomeSourceTitle = styled.h4`
  margin: 0 0 16px 0;
  font-size: 1.1rem;
`;

const AddButton = styled.button<{ theme: any; disabled?: boolean }>`
  background-color: ${(props) =>
    props.disabled
      ? props.theme.colors.action.disabledBackground
      : props.theme.colors.primary.main};
  color: ${(props) =>
    props.disabled ? props.theme.colors.action.disabled : props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) =>
      props.disabled
        ? props.theme.colors.action.disabledBackground
        : props.theme.colors.primary.dark};
  }
`;

const SummaryContainer = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  padding: 16px;
  border-radius: 4px;
`;

const SummaryRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
    padding-top: 8px;
    border-top: 1px solid #ddd;
  }
`;

const SummaryLabel = styled.div`
  font-weight: 500;
`;

const SummaryValue = styled.div<{ highlight?: boolean }>`
  font-weight: ${(props) => (props.highlight ? 'bold' : 'normal')};
  font-size: ${(props) => (props.highlight ? '1.2rem' : '1rem')};
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
`;

const BackButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.text.secondary};
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const SubmitButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 24px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  color: green;
  margin-right: 16px;
`;

export default RetirementIncome;
