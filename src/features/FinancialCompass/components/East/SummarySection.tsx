/**
 * East Direction Summary Section
 *
 * This component displays a summary of the East Direction journey,
 * showing the user's retirement readiness, trends, alerts, actions, and guidance.
 * Enhanced with Monte Carlo simulation for retirement success probability.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { formatCurrency } from '../../../../utils/formatters';
import {
  TrendsSection,
  AlertsSection,
  ActionsSection,
  GuidanceSection,
  TrendItem,
  AlertItem,
  ActionItem,
  GuidanceItem,
} from '../shared/SummaryComponents';
import MonteCarloSimulation from './MonteCarloSimulation';

interface SummarySectionProps {
  onComplete?: () => void;
  onBack?: () => void;
}

const SummarySection: React.FC<SummarySectionProps> = ({ onComplete, onBack }) => {
  const { theme } = useTheme();
  const { data } = useFinancialCompass();
  const [isExporting, setIsExporting] = useState(false);
  const [showMonteCarloSimulation, setShowMonteCarloSimulation] = useState(false);

  // Extract relevant data from the context
  const retirementGoals = data.east?.retirementGoals || {};
  const retirementIncome = data.east?.retirementIncome || {};
  const retirementExpenses = data.east?.retirementExpenses || {};
  const retirementTimeline = data.east?.retirementTimeline || {};
  const socialSecurityPlanning = data.east?.socialSecurityPlanning || {};
  const personalInfo = data.north?.personalInformation || {};
  const assets = data.north?.assets || {};

  // Calculate retirement metrics
  const currentAge =
    parseInt((personalInfo as any)?.age) || parseInt((retirementGoals as any)?.currentAge) || 30;
  const retirementAge = parseInt((retirementGoals as any)?.retirementAge) || 65;
  const lifeExpectancy = parseInt((retirementGoals as any)?.lifeExpectancy) || 90;
  const yearsUntilRetirement = Math.max(0, retirementAge - currentAge);
  const retirementDuration = Math.max(0, lifeExpectancy - retirementAge);

  // Calculate savings and income
  const savingsGoal = parseFloat((retirementGoals as any)?.savingsGoal) || 1000000;
  const desiredAnnualIncome = parseFloat((retirementGoals as any)?.desiredAnnualIncome) || 60000;
  const desiredMonthlyIncome = desiredAnnualIncome / 12;

  // Get current retirement savings
  const retirementAccounts = Array.isArray((assets as any)?.retirementAccounts)
    ? (assets as any)?.retirementAccounts
    : [];

  const currentRetirementSavings = retirementAccounts.reduce(
    (total: number, account: any) => total + (parseFloat(account.balance) || 0),
    0
  );

  // Calculate savings gap
  const savingsGap = Math.max(0, savingsGoal - currentRetirementSavings);

  // Calculate monthly savings needed
  const monthsUntilRetirement = yearsUntilRetirement * 12;
  const monthlySavingsNeeded = monthsUntilRetirement > 0 ? savingsGap / monthsUntilRetirement : 0;

  // Calculate estimated monthly retirement income
  const estimatedMonthlyIncome =
    typeof retirementIncome === 'object' &&
    typeof (retirementIncome as any).calculateTotalMonthlyIncome === 'function'
      ? (retirementIncome as any).calculateTotalMonthlyIncome()
      : parseFloat((retirementIncome as any)?.totalMonthlyIncome) || 0;

  // Calculate income replacement ratio
  const currentMonthlyIncome =
    parseFloat((data.north?.incomeDetails as any)?.totalMonthlyIncome) || 0;
  const incomeReplacementRatio =
    currentMonthlyIncome > 0 ? (estimatedMonthlyIncome / currentMonthlyIncome) * 100 : 0;

  // Calculate retirement readiness score (0-100)
  const calculateRetirementReadinessScore = () => {
    let score = 0;

    // Factor 1: Savings progress (0-40 points)
    const savingsProgress = currentRetirementSavings / savingsGoal;
    score += Math.min(40, Math.round(savingsProgress * 40));

    // Factor 2: Income replacement (0-30 points)
    const targetReplacement = 0.8; // 80% income replacement is typically recommended
    const replacementProgress = Math.min(1, incomeReplacementRatio / 100 / targetReplacement);
    score += Math.min(30, Math.round(replacementProgress * 30));

    // Factor 3: Time horizon (0-15 points)
    if (yearsUntilRetirement >= 20) {
      score += 15; // Plenty of time to prepare
    } else if (yearsUntilRetirement >= 10) {
      score += 10; // Moderate time to prepare
    } else if (yearsUntilRetirement >= 5) {
      score += 5; // Limited time to prepare
    }

    // Factor 4: Planning completeness (0-20 points)
    let planningScore = 0;
    if (retirementGoals && Object.keys(retirementGoals).length > 0) planningScore += 5;
    if (retirementIncome && Object.keys(retirementIncome).length > 0) planningScore += 5;
    if (socialSecurityPlanning && Object.keys(socialSecurityPlanning).length > 0)
      planningScore += 5;
    if (retirementExpenses && Object.keys(retirementExpenses).length > 0) planningScore += 5;
    score += planningScore;

    return Math.min(100, score);
  };

  const retirementReadinessScore = calculateRetirementReadinessScore();

  // Get retirement readiness status
  const getRetirementReadinessStatus = () => {
    if (retirementReadinessScore >= 80) return 'Excellent';
    if (retirementReadinessScore >= 60) return 'Good';
    if (retirementReadinessScore >= 40) return 'Fair';
    if (retirementReadinessScore >= 20) return 'Needs Attention';
    return 'Critical';
  };

  // Generate trends
  const generateTrends = (): TrendItem[] => {
    return [
      {
        id: 'savings_progress',
        label: 'Retirement Savings Progress',
        value: `${Math.round((currentRetirementSavings / savingsGoal) * 100)}%`,
        change: 5, // This would ideally be calculated from historical data
        direction: 'up',
        isPositive: true,
      },
      {
        id: 'income_replacement',
        label: 'Income Replacement Ratio',
        value: `${Math.round(incomeReplacementRatio)}%`,
        change: 2, // This would ideally be calculated from historical data
        direction: 'up',
        isPositive: true,
      },
      {
        id: 'time_horizon',
        label: 'Years Until Retirement',
        value: yearsUntilRetirement,
        change: 0,
        direction: 'neutral',
        isPositive: true,
      },
      {
        id: 'retirement_duration',
        label: 'Expected Retirement Duration',
        value: `${retirementDuration} years`,
        change: 0,
        direction: 'neutral',
        isPositive: true,
      },
    ];
  };

  // Generate alerts
  const generateAlerts = (): AlertItem[] => {
    const alerts: AlertItem[] = [];

    if (savingsGap > 0) {
      const severityLevel =
        savingsGap > savingsGoal * 0.7
          ? 'critical'
          : savingsGap > savingsGoal * 0.5
            ? 'high'
            : savingsGap > savingsGoal * 0.3
              ? 'medium'
              : 'low';

      alerts.push({
        id: 'savings_gap',
        title: 'Retirement Savings Gap',
        description: `You currently have a retirement savings gap of ${formatCurrency(savingsGap)}. Consider increasing your retirement contributions.`,
        severity: severityLevel,
      });
    }

    if (incomeReplacementRatio < 70) {
      const severityLevel =
        incomeReplacementRatio < 40
          ? 'critical'
          : incomeReplacementRatio < 50
            ? 'high'
            : incomeReplacementRatio < 60
              ? 'medium'
              : 'low';

      alerts.push({
        id: 'income_replacement',
        title: 'Low Income Replacement Ratio',
        description: `Your projected retirement income will replace only ${Math.round(incomeReplacementRatio)}% of your current income. Aim for at least 70-80%.`,
        severity: severityLevel,
      });
    }

    if (yearsUntilRetirement < 10 && retirementReadinessScore < 60) {
      alerts.push({
        id: 'retirement_readiness',
        title: 'Retirement Readiness Concern',
        description: `With only ${yearsUntilRetirement} years until retirement, your readiness score of ${retirementReadinessScore} indicates you may need to adjust your retirement plans.`,
        severity: 'high',
      });
    }

    return alerts;
  };

  // Generate actions
  const generateActions = (): ActionItem[] => {
    const actions: ActionItem[] = [];

    if (savingsGap > 0) {
      actions.push({
        id: 'increase_contributions',
        title: 'Increase Retirement Contributions',
        description: `Consider saving an additional ${formatCurrency(monthlySavingsNeeded)} per month to reach your retirement savings goal.`,
        priority: monthlySavingsNeeded > currentMonthlyIncome * 0.2 ? 'high' : 'medium',
        icon: '💰',
      });
    }

    if (incomeReplacementRatio < 70) {
      actions.push({
        id: 'diversify_income',
        title: 'Diversify Retirement Income Sources',
        description:
          'Add additional income sources for retirement such as part-time work, rental income, or annuities.',
        priority: 'medium',
        icon: '📊',
      });
    }

    if (retirementReadinessScore < 60) {
      actions.push({
        id: 'adjust_retirement_age',
        title: 'Consider Adjusting Retirement Age',
        description:
          'Delaying retirement by a few years can significantly improve your financial security in retirement.',
        priority: 'medium',
        icon: '⏱️',
      });
    }

    if (!retirementExpenses || Object.keys(retirementExpenses).length === 0) {
      actions.push({
        id: 'complete_expenses',
        title: 'Complete Retirement Expenses Section',
        description:
          'Understanding your expected retirement expenses is crucial for accurate retirement planning.',
        priority: 'high',
        icon: '📝',
      });
    }

    if (!socialSecurityPlanning || Object.keys(socialSecurityPlanning).length === 0) {
      actions.push({
        id: 'complete_social_security',
        title: 'Complete Social Security Planning',
        description:
          'Optimizing your Social Security claiming strategy can significantly impact your retirement income.',
        priority: 'high',
        icon: '🏛️',
      });
    }

    return actions;
  };

  // Generate guidance
  const generateGuidance = (): GuidanceItem[] => {
    return [
      {
        id: 'savings_strategy',
        title: 'Retirement Savings Strategy',
        description: `With ${yearsUntilRetirement} years until retirement, consider a ${yearsUntilRetirement > 15 ? 'growth-oriented' : yearsUntilRetirement > 5 ? 'balanced' : 'conservative'} investment approach for your retirement accounts.`,
        icon: '📈',
      },
      {
        id: 'social_security',
        title: 'Social Security Optimization',
        description:
          socialSecurityPlanning && Object.keys(socialSecurityPlanning).length > 0
            ? `Based on your selected claiming age of ${(socialSecurityPlanning as any).selectedClaimingAge}, your estimated monthly benefit will be ${formatCurrency((socialSecurityPlanning as any).estimatedMonthlyBenefitAtFRA)}. ${(socialSecurityPlanning as any).maximizationStrategy === 'delay' ? 'Delaying benefits increases your monthly amount significantly.' : (socialSecurityPlanning as any).maximizationStrategy === 'early' ? 'Claiming early provides income sooner but reduces your benefit amount.' : 'Your claiming strategy balances early income with benefit amount.'}`
            : 'Delaying Social Security benefits until age 70 can increase your monthly benefit by up to 32% compared to starting at full retirement age.',
        icon: '🏛️',
      },
      {
        id: 'tax_efficiency',
        title: 'Tax-Efficient Retirement Planning',
        description:
          'Balance contributions between traditional and Roth accounts to manage your tax liability both now and in retirement.',
        icon: '📊',
      },
      {
        id: 'healthcare_planning',
        title: 'Healthcare in Retirement',
        description:
          'Medicare begins at age 65, but consider budgeting for supplemental insurance and out-of-pocket healthcare costs in retirement.',
        icon: '🏥',
      },
    ];
  };

  const trends = generateTrends();
  const alerts = generateAlerts();
  const actions = generateActions();
  const guidanceItems = generateGuidance();

  return (
    <Container theme={theme} data-testid="summary-section">
      <Header theme={theme}>
        <Title theme={theme}>East Direction Summary</Title>
        <Description theme={theme}>
          Review your retirement readiness and planning progress.
        </Description>
      </Header>

      <ReadinessCard theme={theme}>
        <ReadinessHeader>
          <ReadinessTitle>Retirement Readiness Score</ReadinessTitle>
          <ReadinessScore score={retirementReadinessScore}>
            {retirementReadinessScore}
          </ReadinessScore>
        </ReadinessHeader>
        <ReadinessStatus score={retirementReadinessScore}>
          {getRetirementReadinessStatus()}
        </ReadinessStatus>
        <ReadinessBar theme={theme}>
          <ReadinessFill score={retirementReadinessScore} theme={theme} />
        </ReadinessBar>

        <SimulationButton onClick={() => setShowMonteCarloSimulation(true)} theme={theme}>
          Run Monte Carlo Simulation
        </SimulationButton>
        <SimulationDescription theme={theme}>
          Analyze your retirement plan with 1,000+ market scenarios to determine success
          probability.
        </SimulationDescription>
      </ReadinessCard>

      {/* Monte Carlo Simulation Modal */}
      {showMonteCarloSimulation && (
        <ModalOverlay onClick={() => setShowMonteCarloSimulation(false)}>
          <ModalContent onClick={(e) => e.stopPropagation()} theme={theme}>
            <MonteCarloSimulation onClose={() => setShowMonteCarloSimulation(false)} />
          </ModalContent>
        </ModalOverlay>
      )}

      <SummaryCard theme={theme}>
        <SummaryTitle>Retirement Overview</SummaryTitle>
        <SummaryContent>
          <SummaryItem>
            <SummaryLabel>Current Retirement Savings:</SummaryLabel>
            <SummaryValue>{formatCurrency(currentRetirementSavings)}</SummaryValue>
          </SummaryItem>
          <SummaryItem>
            <SummaryLabel>Retirement Savings Goal:</SummaryLabel>
            <SummaryValue>{formatCurrency(savingsGoal)}</SummaryValue>
          </SummaryItem>
          <SummaryItem>
            <SummaryLabel>Savings Gap:</SummaryLabel>
            <SummaryValue highlight={savingsGap > 0}>{formatCurrency(savingsGap)}</SummaryValue>
          </SummaryItem>
          <SummaryItem>
            <SummaryLabel>Monthly Savings Needed:</SummaryLabel>
            <SummaryValue>{formatCurrency(monthlySavingsNeeded)}</SummaryValue>
          </SummaryItem>
        </SummaryContent>
      </SummaryCard>

      <SummaryCard theme={theme}>
        <SummaryTitle>Retirement Income</SummaryTitle>
        <SummaryContent>
          <SummaryItem>
            <SummaryLabel>Projected Monthly Income:</SummaryLabel>
            <SummaryValue>{formatCurrency(estimatedMonthlyIncome)}</SummaryValue>
          </SummaryItem>
          <SummaryItem>
            <SummaryLabel>Desired Monthly Income:</SummaryLabel>
            <SummaryValue>{formatCurrency(desiredMonthlyIncome)}</SummaryValue>
          </SummaryItem>
          <SummaryItem>
            <SummaryLabel>Income Replacement Ratio:</SummaryLabel>
            <SummaryValue highlight={incomeReplacementRatio < 70}>
              {Math.round(incomeReplacementRatio)}%
            </SummaryValue>
          </SummaryItem>
        </SummaryContent>
      </SummaryCard>

      {socialSecurityPlanning && Object.keys(socialSecurityPlanning).length > 0 && (
        <SummaryCard theme={theme}>
          <SummaryTitle>Social Security Planning</SummaryTitle>
          <SummaryContent>
            <SummaryItem>
              <SummaryLabel>Full Retirement Age:</SummaryLabel>
              <SummaryValue>{(socialSecurityPlanning as any).fullRetirementAge}</SummaryValue>
            </SummaryItem>
            <SummaryItem>
              <SummaryLabel>Selected Claiming Age:</SummaryLabel>
              <SummaryValue>{(socialSecurityPlanning as any).selectedClaimingAge}</SummaryValue>
            </SummaryItem>
            <SummaryItem>
              <SummaryLabel>Monthly Benefit at Selected Age:</SummaryLabel>
              <SummaryValue>
                {formatCurrency((socialSecurityPlanning as any).estimatedMonthlyBenefitAtFRA)}
              </SummaryValue>
            </SummaryItem>
            <SummaryItem>
              <SummaryLabel>Claiming Strategy:</SummaryLabel>
              <SummaryValue>
                {(socialSecurityPlanning as any).maximizationStrategy === 'delay'
                  ? 'Delay Benefits'
                  : (socialSecurityPlanning as any).maximizationStrategy === 'early'
                    ? 'Claim Early'
                    : (socialSecurityPlanning as any).maximizationStrategy === 'fra'
                      ? 'Claim at Full Retirement Age'
                      : 'Coordinate with Spouse'}
              </SummaryValue>
            </SummaryItem>
          </SummaryContent>
        </SummaryCard>
      )}

      <TrendsSection title="Retirement Trends" trends={trends} theme={theme} />
      <AlertsSection alerts={alerts} theme={theme} />
      <ActionsSection actions={actions} theme={theme} />
      <GuidanceSection guidanceItems={guidanceItems} theme={theme} />

      <ButtonContainer>
        {onBack && (
          <BackButton type="button" onClick={onBack} theme={theme}>
            Back
          </BackButton>
        )}

        <CompleteButton type="button" onClick={onComplete} theme={theme}>
          Complete East Direction
        </CompleteButton>
      </ButtonContainer>
    </Container>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  padding: 24px;
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const Title = styled.h2<{ theme: any }>`
  margin: 0 0 8px 0;
  font-size: 1.8rem;
  color: ${(props) => props.theme.colors.primary.main};
`;

const Description = styled.p<{ theme: any }>`
  margin: 0;
  font-size: 1rem;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const ReadinessCard = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  text-align: center;
`;

const ReadinessHeader = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
`;

const ReadinessTitle = styled.h3`
  margin: 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const ReadinessScore = styled.div<{ score: number }>`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.5rem;
  font-weight: 700;
  background-color: ${(props) => {
    if (props.score >= 80) return '#4CAF50';
    if (props.score >= 60) return '#8BC34A';
    if (props.score >= 40) return '#FFC107';
    if (props.score >= 20) return '#FF9800';
    return '#F44336';
  }};
  color: white;
`;

const ReadinessStatus = styled.div<{ score: number }>`
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: ${(props) => {
    if (props.score >= 80) return '#4CAF50';
    if (props.score >= 60) return '#8BC34A';
    if (props.score >= 40) return '#FFC107';
    if (props.score >= 20) return '#FF9800';
    return '#F44336';
  }};
`;

const ReadinessBar = styled.div<{ theme: any }>`
  height: 8px;
  background-color: ${(props) => props.theme.colors.background.main};
  border-radius: 4px;
  overflow: hidden;
`;

const ReadinessFill = styled.div<{ score: number; theme: any }>`
  height: 100%;
  width: ${(props) => props.score}%;
  background-color: ${(props) => {
    if (props.score >= 80) return '#4CAF50';
    if (props.score >= 60) return '#8BC34A';
    if (props.score >= 40) return '#FFC107';
    if (props.score >= 20) return '#FF9800';
    return '#F44336';
  }};
`;

const SummaryCard = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const SummaryTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const SummaryContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const SummaryItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
`;

const SummaryLabel = styled.div`
  font-weight: 500;
`;

const SummaryValue = styled.div<{ highlight?: boolean }>`
  font-weight: 600;
  color: ${(props) => (props.highlight ? '#F44336' : '#2196F3')};
`;

const SimulationButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.secondary.main};
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: pointer;
  margin-top: 24px;

  &:hover {
    background-color: ${(props) => props.theme.colors.secondary.dark};
  }
`;

const SimulationDescription = styled.p<{ theme: any }>`
  font-size: 0.9rem;
  color: ${(props) => props.theme.colors.text.secondary};
  margin-top: 8px;
`;

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContent = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  max-width: 90%;
  max-height: 90%;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: 32px;
`;

const BackButton = styled.button<{ theme: any }>`
  padding: 12px 24px;
  background-color: transparent;
  color: ${(props) => props.theme.colors.primary.main};
  border: 1px solid ${(props) => props.theme.colors.primary.main};
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.light};
  }
`;

const CompleteButton = styled.button<{ theme: any }>`
  padding: 12px 24px;
  background-color: ${(props) => props.theme.colors.primary.main};
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

export default SummarySection;
