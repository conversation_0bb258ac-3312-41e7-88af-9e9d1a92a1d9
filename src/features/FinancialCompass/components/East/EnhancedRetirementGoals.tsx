/**
 * Enhanced Retirement Goals Component
 *
 * A comprehensive retirement planning form with real-time calculations and validation.
 * Implements the East direction of the Financial Compass, focusing on future financial planning.
 */
import React, { useMemo } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import styled from '@emotion/styled';
import { formatCurrency } from '../../../../utils/formatters';

// Define form data interface
interface RetirementFormData {
  currentAge: number;
  retirementAge: number;
  lifeExpectancy: number;
  currentIncome: number;
  currentSavings: number;
  monthlyContribution: number;
  expectedReturnRate: number;
  desiredAnnualIncome: number;
  inflationRate: number;
  socialSecurityBenefit: number;
  pensionIncome: number;
  otherIncome: number;
  retirementLocation: string;
  riskTolerance: string;
  investmentStrategy: string;
  lifestyle: string;
  activities: string[];
  priorityGoals: string[];
}

// Form props interface
interface EnhancedRetirementGoalsProps {
  initialValues?: Partial<RetirementFormData>;
  onSubmit: (data: RetirementFormData) => void;
}

// Activity options for retirement
const ACTIVITY_OPTIONS = [
  { value: 'travel', label: 'Travel' },
  { value: 'hobbies', label: 'Hobbies' },
  { value: 'volunteering', label: 'Volunteering' },
  { value: 'part_time', label: 'Part-time work' },
];

// Priority goal options
const PRIORITY_GOALS = [
  { value: 'max_income', label: 'Maximize retirement income' },
  { value: 'min_risk', label: 'Minimize risk' },
  { value: 'early_retirement', label: 'Early retirement' },
  { value: 'inheritance', label: 'Leave inheritance' },
  { value: 'healthcare', label: 'Healthcare coverage' },
];

// Risk tolerance options
const RISK_TOLERANCE_OPTIONS = [
  { value: 'conservative', label: 'Conservative' },
  { value: 'moderate', label: 'Moderate' },
  { value: 'aggressive', label: 'Aggressive' },
];

// Investment strategy options
const INVESTMENT_STRATEGY_OPTIONS = [
  { value: 'preservation', label: 'Capital Preservation' },
  { value: 'income', label: 'Income Generation' },
  { value: 'growth', label: 'Growth Oriented' },
  { value: 'balanced', label: 'Balanced' },
];

// Retirement lifestyle options
const LIFESTYLE_OPTIONS = [
  { value: 'minimal', label: 'Minimal (Basic needs met)' },
  { value: 'modest', label: 'Modest (Comfortable but simple)' },
  { value: 'comfortable', label: 'Comfortable (Maintain current lifestyle)' },
  { value: 'luxurious', label: 'Luxurious (High-end lifestyle)' },
];

// Helper function to parse currency input
const parseCurrency = (value: string): number => {
  return parseFloat(value.replace(/[^0-9.-]+/g, '')) || 0;
};

// Helper function to format currency input (for user-typed input only)
const formatCurrencyInput = (value: string): string => {
  if (!value) return '';
  const num = parseFloat(value.replace(/[^0-9.-]+/g, ''));
  return isNaN(num)
    ? ''
    : num.toLocaleString('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      });
};

// Calculate retirement metrics
const calculateRetirementMetrics = (data: RetirementFormData) => {
  const yearsToRetirement = data.retirementAge - data.currentAge;
  const retirementDuration = data.lifeExpectancy - data.retirementAge;

  // Simple future value calculation (simplified)
  const futureValue =
    data.currentSavings * Math.pow(1 + data.expectedReturnRate / 100, yearsToRetirement) +
    (data.monthlyContribution *
      12 *
      (Math.pow(1 + data.expectedReturnRate / 100, yearsToRetirement) - 1)) /
      (data.expectedReturnRate / 100);

  return {
    yearsToRetirement,
    retirementDuration,
    estimatedRetirementSavings: futureValue,
    annualRetirementIncome: futureValue * 0.04, // 4% safe withdrawal rate
  };
};

const EnhancedRetirementGoals: React.FC<EnhancedRetirementGoalsProps> = ({
  initialValues = {},
  onSubmit: onSubmitProp,
}) => {
  const formRef = useRef<HTMLFormElement>(null);

  const scrollToForm = () => {
    if (formRef.current) {
      formRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };
  // Initialize form with default values
  const defaultValues: RetirementFormData = {
    currentAge: 30,
    retirementAge: 65,
    lifeExpectancy: 85,
    currentIncome: 80000,
    currentSavings: 100000,
    monthlyContribution: 1000,
    expectedReturnRate: 6,
    desiredAnnualIncome: 60000,
    inflationRate: 2.5,
    socialSecurityBenefit: 0,
    pensionIncome: 0,
    otherIncome: 0,
    retirementLocation: '',
    riskTolerance: 'moderate',
    investmentStrategy: 'balanced',
    lifestyle: 'comfortable',
    activities: [],
    priorityGoals: [],
  };

  // Form validation schema
  const retirementSchema = yup.object().shape({
    currentAge: yup
      .number()
      .required('Current age is required')
      .min(18, 'Must be at least 18 years old')
      .max(100, 'Must be less than 100 years old'),
    retirementAge: yup
      .number()
      .required('Retirement age is required')
      .min(yup.ref('currentAge'), 'Retirement age must be greater than current age')
      .max(100, 'Must be less than 100 years old'),
    lifeExpectancy: yup
      .number()
      .required('Life expectancy is required')
      .min(yup.ref('retirementAge'), 'Life expectancy must be greater than retirement age')
      .max(120, 'Must be less than 120 years old'),
    currentIncome: yup
      .number()
      .required('Current income is required')
      .min(0, 'Income cannot be negative'),
    currentSavings: yup
      .number()
      .required('Current savings is required')
      .min(0, 'Savings cannot be negative'),
    monthlyContribution: yup
      .number()
      .required('Monthly contribution is required')
      .min(0, 'Contribution cannot be negative'),
    expectedReturnRate: yup
      .number()
      .required('Expected return rate is required')
      .min(0, 'Rate cannot be negative')
      .max(20, 'Rate seems too high'),
    desiredAnnualIncome: yup
      .number()
      .required('Desired annual income is required')
      .min(0, 'Income cannot be negative'),
    inflationRate: yup
      .number()
      .required('Inflation rate is required')
      .min(0, 'Rate cannot be negative')
      .max(20, 'Rate seems too high'),
    lifestyle: yup.string().required('Lifestyle selection is required'),
    riskTolerance: yup.string().required('Risk tolerance is required'),
    investmentStrategy: yup.string().required('Investment strategy is required'),
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<RetirementFormData>({
    resolver: yupResolver(retirementSchema),
    defaultValues: {
      ...defaultValues,
      ...initialValues,
    },
  });

  const onSubmit: SubmitHandler<RetirementFormData> = (data) => {
    try {
      console.log('Form submitted:', data);
      onSubmitProp(data);
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  // Calculate retirement metrics
  const metrics = useMemo(() => {
    if (!formValues.currentAge || !formValues.retirementAge || !formValues.lifeExpectancy) {
      return null;
    }

    const yearsToRetirement = formValues.retirementAge - formValues.currentAge;
    const retirementDuration = formValues.lifeExpectancy - formValues.retirementAge;

    // Simple future value calculation (simplified)
    const futureValue =
      formValues.currentSavings *
        Math.pow(1 + formValues.expectedReturnRate / 100, yearsToRetirement) +
      (formValues.monthlyContribution *
        12 *
        (Math.pow(1 + formValues.expectedReturnRate / 100, yearsToRetirement) - 1)) /
        (formValues.expectedReturnRate / 100);

    return {
      yearsToRetirement,
      retirementDuration,
      estimatedRetirementSavings: futureValue,
      annualRetirementIncome: futureValue * 0.04, // 4% safe withdrawal rate
    };
  }, [formValues]);

  // Get form values
  const formValues = watch();
  const metrics = calculateRetirementMetrics(formValues);

  // Render form sections
  const renderPersonalInfoSection = () => (
    <Card>
      <SectionTitle>Personal Information</SectionTitle>
      <FormRow>
        <FormGroup>
          <Label>Current Age</Label>
          <Input type="number" hasError={!!errors.currentAge} {...register('currentAge')} />
          {errors.currentAge && <ErrorText>{errors.currentAge.message}</ErrorText>}
        </FormGroup>
        <FormGroup>
          <Label>Retirement Age</Label>
          <Input type="number" hasError={!!errors.retirementAge} {...register('retirementAge')} />
          {errors.retirementAge && <ErrorText>{errors.retirementAge.message}</ErrorText>}
        </FormGroup>
        <FormGroup>
          <Label>Life Expectancy</Label>
          <Input type="number" hasError={!!errors.lifeExpectancy} {...register('lifeExpectancy')} />
          {errors.lifeExpectancy && <ErrorText>{errors.lifeExpectancy.message}</ErrorText>}
        </FormGroup>
      </FormRow>
    </Card>
  );

  const renderFinancialInfoSection = () => (
    <Card>
      <SectionTitle>Financial Information</SectionTitle>
      <FormRow>
        <FormGroup>
          <Label>Current Income</Label>
          <Input type="number" hasError={!!errors.currentIncome} {...register('currentIncome')} />
          {errors.currentIncome && <ErrorText>{errors.currentIncome.message}</ErrorText>}
        </FormGroup>
        <FormGroup>
          <Label>Current Savings</Label>
          <Input type="number" hasError={!!errors.currentSavings} {...register('currentSavings')} />
          {errors.currentSavings && <ErrorText>{errors.currentSavings.message}</ErrorText>}
        </FormGroup>
        <FormGroup>
          <Label>Monthly Contribution</Label>
          <Input
            type="number"
            hasError={!!errors.monthlyContribution}
            {...register('monthlyContribution')}
          />
          {errors.monthlyContribution && (
            <ErrorText>{errors.monthlyContribution.message}</ErrorText>
          )}
        </FormGroup>
      </FormRow>
    </Card>
  );

  const renderRetirementStrategySection = () => (
    <Card>
      <SectionTitle>Retirement Strategy</SectionTitle>
      <FormRow>
        <FormGroup>
          <Label>Risk Tolerance</Label>
          <Select {...register('riskTolerance')}>
            {RISK_TOLERANCE_OPTIONS.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </Select>
          {errors.riskTolerance && <ErrorText>{errors.riskTolerance.message}</ErrorText>}
        </FormGroup>
        <FormGroup>
          <Label>Investment Strategy</Label>
          <Select {...register('investmentStrategy')}>
            {INVESTMENT_STRATEGY_OPTIONS.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </Select>
          {errors.investmentStrategy && <ErrorText>{errors.investmentStrategy.message}</ErrorText>}
        </FormGroup>
        <FormGroup>
          <Label>Lifestyle</Label>
          <Select {...register('lifestyle')}>
            {LIFESTYLE_OPTIONS.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </Select>
          {errors.lifestyle && <ErrorText>{errors.lifestyle.message}</ErrorText>}
        </FormGroup>
      </FormRow>
    </Card>
  );

  return (
    <FormContainer>
      <h1 style={{ fontSize: '1.875rem', fontWeight: 600, color: '#111827', marginBottom: '1rem' }}>
        Retirement Planning
      </h1>
      <p style={{ color: '#6b7280', marginBottom: '2rem' }}>
        Plan your ideal retirement by setting your goals and expectations. Adjust the values below
        to see how they impact your retirement readiness.
      </p>

      <form onSubmit={handleSubmit(onSubmit)}>
        {/* Retirement Readiness Summary */}
        {renderPersonalInfoSection()}
        {renderFinancialInfoSection()}
        {renderRetirementStrategySection()}

        {/* Form Actions */}
        <Card>
          <Button type="submit">Save & Continue</Button>
        </Card>
      </form>
    </FormContainer>
  );
};

// Styled components
const FormContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
`;

const SectionTitle = styled.h2`
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: #2c3e50;
`;

const FormGroup = styled.div`
  margin-bottom: 1.5rem;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #34495e;
`;

const Input = styled.input<{ hasError?: boolean }>`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid ${(props) => (props.hasError ? '#e74c3c' : '#ddd')};
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
  }
`;

const Select = styled.select<{ hasError?: boolean }>`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid ${(props) => (props.hasError ? '#e74c3c' : '#ddd')};
  border-radius: 4px;
  font-size: 1rem;
  background-color: white;
  cursor: pointer;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
  }
`;

const Button = styled.button`
  background-color: #3498db;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 1rem;

  &:hover {
    background-color: #2980b9;
  }

  &:disabled {
    background-color: #95a5a6;
    cursor: not-allowed;
  }
`;

const Card = styled.div`
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 2rem;
  margin-bottom: 2rem;
`;

const ErrorText = styled.span`
  display: block;
  color: #e74c3c;
  font-size: 0.875rem;
  margin-top: 0.5rem;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
`;

const FormActions = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
`;

export default EnhancedRetirementGoals;
