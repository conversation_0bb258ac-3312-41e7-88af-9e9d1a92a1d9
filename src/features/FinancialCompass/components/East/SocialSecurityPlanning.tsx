/**
 * Social Security Planning Component
 *
 * This component helps users plan their Social Security benefits claiming strategy
 * to maximize their retirement income.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { formatCurrency } from '../../../../utils/formatters';
import { SocialSecurityPlanningData } from '../../../../types/eastDirection';

interface SocialSecurityPlanningProps {
  onComplete?: () => void;
  onBack?: () => void;
}

const SocialSecurityPlanning: React.FC<SocialSecurityPlanningProps> = ({ onComplete, onBack }) => {
  const { theme } = useTheme();
  const { data, updateData } = useFinancialCompass();

  const [isSaving, setIsSaving] = useState(false);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Get personal information from North direction
  const personalInfo = data.north?.personalInformation || {};
  const birthYear =
    typeof personalInfo === 'object' && 'dateOfBirth' in personalInfo && personalInfo.dateOfBirth
      ? new Date(personalInfo.dateOfBirth as string).getFullYear().toString()
      : '1970';

  // Initialize form data from context or with defaults
  const [formData, setFormData] = useState<SocialSecurityPlanningData>(
    data.east?.socialSecurityPlanning || {
      birthYear: birthYear,
      fullRetirementAge: calculateFullRetirementAge(birthYear),
      earlyClaimingAge: '62',
      delayedClaimingAge: '70',
      estimatedMonthlyBenefitAtFRA: '2000',
      earlyClaimingReduction: calculateEarlyClaimingReduction(birthYear),
      delayedClaimingIncrease: '8',
      selectedClaimingAge: calculateFullRetirementAge(birthYear),
      spouseBenefit: '0',
      hasWorkingHistory: true,
      yearsOfCoveredEmployment: '35',
      averageIndexedMonthlyEarnings: '5000',
      primaryInsuranceAmount: '2000',
      maximizationStrategy: 'delay',
      notes: '',
    }
  );

  // Calculate full retirement age based on birth year
  function calculateFullRetirementAge(birthYear: string): string {
    const year = parseInt(birthYear, 10);

    if (year <= 1937) return '65';
    if (year >= 1960) return '67';

    if (year <= 1942) return `65 and ${(year - 1937) * 2} months`;
    if (year <= 1954) return '66';

    return `66 and ${(year - 1954) * 2} months`;
  }

  // Calculate early claiming reduction percentage
  function calculateEarlyClaimingReduction(birthYear: string): string {
    const year = parseInt(birthYear, 10);

    // For those born in 1960 or later, the reduction is 30% at age 62
    if (year >= 1960) return '30';

    // For those born before 1960, the reduction varies
    return '25';
  }

  // Calculate estimated benefit at different claiming ages
  const calculateBenefitAtAge = (age: string): number => {
    const fra = formData.fullRetirementAge;
    const fraAge = parseInt(fra.split(' ')[0], 10);
    const selectedAge = parseInt(age, 10);
    const baseAmount = parseFloat(formData.estimatedMonthlyBenefitAtFRA) || 0;

    // Early claiming (before FRA)
    if (selectedAge < fraAge) {
      const reductionPerYear = parseFloat(formData.earlyClaimingReduction) / (fraAge - 62);
      const yearsEarly = fraAge - selectedAge;
      const reductionPercent = reductionPerYear * yearsEarly;
      return baseAmount * (1 - reductionPercent / 100);
    }

    // Delayed claiming (after FRA)
    if (selectedAge > fraAge) {
      const increasePerYear = parseFloat(formData.delayedClaimingIncrease) || 8;
      const yearsDelayed = selectedAge - fraAge;
      const increasePercent = increasePerYear * yearsDelayed;
      return baseAmount * (1 + increasePercent / 100);
    }

    // At FRA
    return baseAmount;
  };

  // Handle input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData((prev) => ({
        ...prev,
        [name]: checked,
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  // Update benefit estimates when relevant fields change
  useEffect(() => {
    // Update primary insurance amount when AIME changes
    if (formData.averageIndexedMonthlyEarnings) {
      const aime = parseFloat(formData.averageIndexedMonthlyEarnings);
      // Simplified PIA calculation (actual calculation is more complex)
      const estimatedPIA = Math.min(aime * 0.4, 3000);

      setFormData((prev) => ({
        ...prev,
        primaryInsuranceAmount: estimatedPIA.toFixed(2),
        estimatedMonthlyBenefitAtFRA: estimatedPIA.toFixed(2),
      }));
    }
  }, [formData.averageIndexedMonthlyEarnings]);

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSave();

    if (onComplete) {
      onComplete();
    }
  };

  // Handle saving data
  const handleSave = () => {
    setIsSaving(true);

    // Update data in context
    updateData('east', 'socialSecurityPlanning', formData);

    // Show save indicator
    setShowSaveIndicator(true);

    // Hide save indicator after 2 seconds
    setTimeout(() => {
      setIsSaving(false);
      setShowSaveIndicator(false);
    }, 2000);
  };

  // Calculate benefits at different ages
  const benefitAt62 = calculateBenefitAtAge('62');
  const benefitAtFRA = parseFloat(formData.estimatedMonthlyBenefitAtFRA) || 0;
  const benefitAt70 = calculateBenefitAtAge('70');
  const selectedBenefit = calculateBenefitAtAge(formData.selectedClaimingAge.split(' ')[0]);

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Social Security Planning</Title>
        <Description theme={theme}>
          Optimize your Social Security benefits by planning your claiming strategy.
        </Description>
      </Header>

      <form onSubmit={handleSubmit}>
        <FormSection>
          <SectionTitle>Personal Information</SectionTitle>

          <FormRow>
            <FormField>
              <Label htmlFor="birthYear">Birth Year</Label>
              <Input
                theme={theme}
                type="text"
                id="birthYear"
                name="birthYear"
                value={formData.birthYear}
                onChange={handleInputChange}
                placeholder="1960"
              />
            </FormField>

            <FormField>
              <Label htmlFor="fullRetirementAge">Full Retirement Age</Label>
              <Input
                theme={theme}
                type="text"
                id="fullRetirementAge"
                name="fullRetirementAge"
                value={formData.fullRetirementAge}
                onChange={handleInputChange}
                placeholder="67"
                disabled
              />
              <FieldHint theme={theme}>Based on your birth year</FieldHint>
            </FormField>
          </FormRow>

          <FormRow>
            <FormField>
              <Label htmlFor="hasWorkingHistory">
                <Checkbox
                  type="checkbox"
                  id="hasWorkingHistory"
                  name="hasWorkingHistory"
                  checked={formData.hasWorkingHistory}
                  onChange={handleInputChange}
                />
                I have a working history in the U.S.
              </Label>
            </FormField>
          </FormRow>

          {formData.hasWorkingHistory && (
            <FormRow>
              <FormField>
                <Label htmlFor="yearsOfCoveredEmployment">Years of Covered Employment</Label>
                <Input
                  theme={theme}
                  type="text"
                  id="yearsOfCoveredEmployment"
                  name="yearsOfCoveredEmployment"
                  value={formData.yearsOfCoveredEmployment}
                  onChange={handleInputChange}
                  placeholder="35"
                />
                <FieldHint theme={theme}>
                  Social Security benefits are based on your 35 highest-earning years
                </FieldHint>
              </FormField>

              <FormField>
                <Label htmlFor="averageIndexedMonthlyEarnings">
                  Average Indexed Monthly Earnings (AIME)
                </Label>
                <InputWithPrefix>
                  <Prefix theme={theme}>$</Prefix>
                  <Input
                    theme={theme}
                    type="text"
                    id="averageIndexedMonthlyEarnings"
                    name="averageIndexedMonthlyEarnings"
                    value={formData.averageIndexedMonthlyEarnings}
                    onChange={handleInputChange}
                    placeholder="5000"
                  />
                </InputWithPrefix>
                <FieldHint theme={theme}>
                  Your average monthly earnings over your 35 highest-earning years, adjusted for
                  inflation
                </FieldHint>
              </FormField>
            </FormRow>
          )}
        </FormSection>

        <FormSection>
          <SectionTitle>Benefit Estimates</SectionTitle>

          <FormRow>
            <FormField>
              <Label htmlFor="primaryInsuranceAmount">Primary Insurance Amount (PIA)</Label>
              <InputWithPrefix>
                <Prefix theme={theme}>$</Prefix>
                <Input
                  theme={theme}
                  type="text"
                  id="primaryInsuranceAmount"
                  name="primaryInsuranceAmount"
                  value={formData.primaryInsuranceAmount}
                  onChange={handleInputChange}
                  placeholder="2000"
                />
              </InputWithPrefix>
              <FieldHint theme={theme}>Your benefit amount at full retirement age</FieldHint>
            </FormField>

            <FormField>
              <Label htmlFor="estimatedMonthlyBenefitAtFRA">Estimated Monthly Benefit at FRA</Label>
              <InputWithPrefix>
                <Prefix theme={theme}>$</Prefix>
                <Input
                  theme={theme}
                  type="text"
                  id="estimatedMonthlyBenefitAtFRA"
                  name="estimatedMonthlyBenefitAtFRA"
                  value={formData.estimatedMonthlyBenefitAtFRA}
                  onChange={handleInputChange}
                  placeholder="2000"
                />
              </InputWithPrefix>
            </FormField>
          </FormRow>

          <FormRow>
            <FormField>
              <Label htmlFor="earlyClaimingReduction">Early Claiming Reduction (%)</Label>
              <InputWithSuffix>
                <Input
                  theme={theme}
                  type="text"
                  id="earlyClaimingReduction"
                  name="earlyClaimingReduction"
                  value={formData.earlyClaimingReduction}
                  onChange={handleInputChange}
                  placeholder="30"
                />
                <Suffix theme={theme}>%</Suffix>
              </InputWithSuffix>
              <FieldHint theme={theme}>Reduction if claimed at age 62</FieldHint>
            </FormField>

            <FormField>
              <Label htmlFor="delayedClaimingIncrease">Delayed Claiming Increase (%)</Label>
              <InputWithSuffix>
                <Input
                  theme={theme}
                  type="text"
                  id="delayedClaimingIncrease"
                  name="delayedClaimingIncrease"
                  value={formData.delayedClaimingIncrease}
                  onChange={handleInputChange}
                  placeholder="8"
                />
                <Suffix theme={theme}>%</Suffix>
              </InputWithSuffix>
              <FieldHint theme={theme}>Annual increase for each year delayed after FRA</FieldHint>
            </FormField>
          </FormRow>
        </FormSection>

        <FormSection>
          <SectionTitle>Claiming Strategy</SectionTitle>

          <FormRow>
            <FormField>
              <Label htmlFor="selectedClaimingAge">When do you plan to claim benefits?</Label>
              <Select
                theme={theme}
                id="selectedClaimingAge"
                name="selectedClaimingAge"
                value={formData.selectedClaimingAge}
                onChange={handleInputChange}
              >
                <option value="62">Age 62 (Earliest)</option>
                <option value="63">Age 63</option>
                <option value="64">Age 64</option>
                <option value="65">Age 65</option>
                <option value="66">Age 66</option>
                <option value="67">Age 67</option>
                <option value="68">Age 68</option>
                <option value="69">Age 69</option>
                <option value="70">Age 70 (Maximum)</option>
              </Select>
            </FormField>

            <FormField>
              <Label htmlFor="maximizationStrategy">Benefit Maximization Strategy</Label>
              <Select
                theme={theme}
                id="maximizationStrategy"
                name="maximizationStrategy"
                value={formData.maximizationStrategy}
                onChange={handleInputChange}
              >
                <option value="early">Claim Early</option>
                <option value="fra">Claim at Full Retirement Age</option>
                <option value="delay">Delay Claiming</option>
                <option value="spouse">Coordinate with Spouse</option>
              </Select>
            </FormField>
          </FormRow>

          <FormRow>
            <FormField>
              <Label htmlFor="spouseBenefit">Spouse Benefit (if applicable)</Label>
              <InputWithPrefix>
                <Prefix theme={theme}>$</Prefix>
                <Input
                  theme={theme}
                  type="text"
                  id="spouseBenefit"
                  name="spouseBenefit"
                  value={formData.spouseBenefit}
                  onChange={handleInputChange}
                  placeholder="0"
                />
              </InputWithPrefix>
            </FormField>
          </FormRow>

          <FormRow>
            <FormField fullWidth>
              <Label htmlFor="notes">Notes</Label>
              <TextArea
                theme={theme}
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                placeholder="Add any additional notes about your Social Security planning strategy..."
                rows={4}
              />
            </FormField>
          </FormRow>
        </FormSection>

        <BenefitComparison theme={theme}>
          <SectionTitle>Benefit Comparison</SectionTitle>

          <BenefitCards>
            <BenefitCard theme={theme} isSelected={formData.selectedClaimingAge === '62'}>
              <BenefitAge>Age 62</BenefitAge>
              <BenefitAmount theme={theme}>
                {formatCurrency(benefitAt62.toString())}/month
              </BenefitAmount>
              <BenefitDescription theme={theme}>
                Earliest claiming age
                <br />
                {Math.round((benefitAt62 / benefitAtFRA - 1) * 100)}% of FRA benefit
              </BenefitDescription>
            </BenefitCard>

            <BenefitCard
              theme={theme}
              isSelected={formData.selectedClaimingAge === formData.fullRetirementAge}
            >
              <BenefitAge>Full Retirement Age ({formData.fullRetirementAge})</BenefitAge>
              <BenefitAmount theme={theme}>
                {formatCurrency(benefitAtFRA.toString())}/month
              </BenefitAmount>
              <BenefitDescription theme={theme}>100% of your benefit</BenefitDescription>
            </BenefitCard>

            <BenefitCard theme={theme} isSelected={formData.selectedClaimingAge === '70'}>
              <BenefitAge>Age 70</BenefitAge>
              <BenefitAmount theme={theme}>
                {formatCurrency(benefitAt70.toString())}/month
              </BenefitAmount>
              <BenefitDescription theme={theme}>
                Maximum claiming age
                <br />
                {Math.round((benefitAt70 / benefitAtFRA - 1) * 100)}% increase from FRA
              </BenefitDescription>
            </BenefitCard>
          </BenefitCards>

          <SelectedBenefit theme={theme}>
            <SelectedBenefitLabel>
              Your Selected Claiming Age: {formData.selectedClaimingAge}
            </SelectedBenefitLabel>
            <SelectedBenefitAmount>
              {formatCurrency(selectedBenefit.toString())}/month
            </SelectedBenefitAmount>
            <SelectedBenefitAnnual theme={theme}>
              ({formatCurrency((selectedBenefit * 12).toString())}/year)
            </SelectedBenefitAnnual>
          </SelectedBenefit>

          <BenefitTips theme={theme}>
            <TipIcon>💡</TipIcon>
            <TipContent>
              <TipTitle>Claiming Strategy Tips</TipTitle>
              <TipText>
                {formData.maximizationStrategy === 'delay' &&
                  'Delaying benefits increases your monthly amount by about 8% per year after your full retirement age, up to age 70. This strategy works well if you have other income sources and expect to live longer than average.'}
                {formData.maximizationStrategy === 'early' &&
                  'Claiming early provides income sooner but permanently reduces your benefit. This may make sense if you need the income immediately or have health concerns that might limit your lifespan.'}
                {formData.maximizationStrategy === 'fra' &&
                  'Claiming at your full retirement age provides your full benefit amount. This balanced approach works well for many retirees.'}
                {formData.maximizationStrategy === 'spouse' &&
                  'Coordinating with your spouse can maximize household benefits. Consider strategies where one spouse claims early while the higher earner delays to maximize survivor benefits.'}
              </TipText>
            </TipContent>
          </BenefitTips>
        </BenefitComparison>

        <ButtonContainer>
          {onBack && (
            <BackButton type="button" onClick={onBack} theme={theme}>
              Back
            </BackButton>
          )}

          <SaveIndicator visible={showSaveIndicator}>✓ Saved</SaveIndicator>

          <SubmitButton type="submit" theme={theme}>
            {isSaving ? 'Saving...' : 'Save & Continue'}
          </SubmitButton>
        </ButtonContainer>
      </form>
    </Container>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  padding: 24px;
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const Title = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const Description = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const FormSection = styled.div`
  margin-bottom: 32px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;

const FormField = styled.div<{ fullWidth?: boolean }>`
  margin-bottom: 16px;
  grid-column: ${(props) => (props.fullWidth ? '1 / -1' : 'auto')};
`;

const Label = styled.label`
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 500;
`;

const Input = styled.input<{ theme: any }>`
  width: 100%;
  padding: 8px 12px;
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  font-size: 1rem;
  background-color: ${(props) => props.theme.colors.background.paper};
  color: ${(props) => props.theme.colors.text.primary};

  &:disabled {
    background-color: ${(props) => props.theme.colors.background.default};
    cursor: not-allowed;
    color: ${(props) => props.theme.colors.text.disabled};
  }
`;

const TextArea = styled.textarea<{ theme: any }>`
  width: 100%;
  padding: 8px 12px;
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  font-size: 1rem;
  resize: vertical;
  background-color: ${(props) => props.theme.colors.background.paper};
  color: ${(props) => props.theme.colors.text.primary};
`;

const Select = styled.select<{ theme: any }>`
  width: 100%;
  padding: 8px 12px;
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  font-size: 1rem;
  background-color: ${(props) => props.theme.colors.background.paper};
  color: ${(props) => props.theme.colors.text.primary};
`;

const Checkbox = styled.input`
  margin-right: 8px;
`;

const FieldHint = styled.div<{ theme: any }>`
  font-size: 0.8rem;
  color: ${(props) => props.theme.colors.text.secondary};
  margin-top: 4px;
`;

const InputWithPrefix = styled.div`
  display: flex;
  align-items: center;
  position: relative;
`;

const InputWithSuffix = styled.div`
  display: flex;
  align-items: center;
  position: relative;
`;

const Prefix = styled.div<{ theme: any }>`
  position: absolute;
  left: 12px;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const Suffix = styled.div<{ theme: any }>`
  position: absolute;
  right: 12px;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const BenefitComparison = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 32px;
`;

const BenefitCards = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
`;

const BenefitCard = styled.div<{ theme: any; isSelected: boolean }>`
  background-color: ${(props) =>
    props.isSelected ? props.theme.colors.primary.light : props.theme.colors.background.paper};
  border: 2px solid
    ${(props) => (props.isSelected ? props.theme.colors.primary.main : 'transparent')};
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
`;

const BenefitAge = styled.div`
  font-weight: 600;
  margin-bottom: 8px;
`;

const BenefitAmount = styled.div<{ theme: any }>`
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 8px;
  color: ${(props) => props.theme.colors.primary.main};
`;

const BenefitDescription = styled.div<{ theme: any }>`
  font-size: 0.9rem;
  color: ${(props) => props.theme.colors.text.secondary};
  line-height: 1.4;
`;

const SelectedBenefit = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.light};
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  margin-bottom: 24px;
`;

const SelectedBenefitLabel = styled.div`
  font-weight: 600;
  margin-bottom: 8px;
`;

const SelectedBenefitAmount = styled.div`
  font-size: 1.8rem;
  font-weight: 700;
  color: ${(props) => props.theme.colors.primary.main};
`;

const SelectedBenefitAnnual = styled.div<{ theme: any }>`
  font-size: 1rem;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const BenefitTips = styled.div<{ theme: any }>`
  display: flex;
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
`;

const TipIcon = styled.div`
  font-size: 24px;
  margin-right: 16px;
`;

const TipContent = styled.div`
  flex: 1;
`;

const TipTitle = styled.div`
  font-weight: 600;
  margin-bottom: 8px;
`;

const TipText = styled.div`
  font-size: 0.9rem;
  line-height: 1.5;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
`;

const BackButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.text.secondary};
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const SubmitButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  color: #4caf50;
  font-weight: 500;
`;

export default SocialSecurityPlanning;
