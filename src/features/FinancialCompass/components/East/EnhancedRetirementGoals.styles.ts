import styled from 'styled-components';

export const FormContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
`;

export const FormSection = styled.div`
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #fff;
  border-radius: 0.5rem;
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
`;

export const SectionTitle = styled.h3`
  font-size: 1.25rem;
  color: #111827;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #e5e7eb;
`;

export const FormGroup = styled.div`
  margin-bottom: 1.25rem;
`;

export const Label = styled.label`
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
`;

export const Input = styled.input<{ $error?: boolean }>`
  width: 100%;
  padding: 0.625rem 0.875rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #111827;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid ${(props) => (props.$error ? '#ef4444' : '#d1d5db')};
  border-radius: 0.375rem;
  transition:
    border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;

  &:focus {
    border-color: #3b82f6;
    outline: 0;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
  }

  &::placeholder {
    color: #9ca3af;
  }

  &:disabled {
    background-color: #f3f4f6;
    opacity: 1;
  }
`;

export const Select = styled.select<{ $error?: boolean }>`
  width: 100%;
  padding: 0.625rem 2.5rem 0.625rem 0.875rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #111827;
  background-color: #fff;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.5rem center;
  background-size: 1.5em 1.5em;
  border: 1px solid ${(props) => (props.$error ? '#ef4444' : '#d1d5db')};
  border-radius: 0.375rem;
  appearance: none;
  transition:
    border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;

  &:focus {
    border-color: #3b82f6;
    outline: 0;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
  }
`;

export const Button = styled.button<{ variant?: 'primary' | 'secondary' }>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.625rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.25rem;
  border-radius: 0.375rem;
  transition: all 0.15s ease-in-out;
  cursor: pointer;
  border: 1px solid transparent;

  ${({ variant = 'primary' }) =>
    variant === 'primary'
      ? `
        background-color: #3b82f6;
        color: #fff;
        &:hover {
          background-color: #2563eb;
        }
        &:disabled {
          background-color: #bfdbfe;
          cursor: not-allowed;
        }
      `
      : `
        background-color: #fff;
        color: #374151;
        border-color: #d1d5db;
        &:hover {
          background-color: #f9fafb;
        }
        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      `}

  & + & {
    margin-left: 0.75rem;
  }
`;

export const ErrorText = styled.p`
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #ef4444;
`;

export const FormActions = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;

  @media (max-width: 640px) {
    flex-direction: column;
    gap: 0.75rem;

    & > * {
      width: 100%;
    }
  }
`;

export const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  z-index: 10;
`;

export const Card = styled.div`
  position: relative;
  background: #fff;
  border-radius: 0.5rem;
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
`;

export const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1rem;
  margin-bottom: 1rem;

  @media (min-width: 640px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (min-width: 1024px) {
    grid-template-columns: repeat(3, 1fr);
  }
`;

export const ReadinessScore = styled.div<{ $score: number }>`
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin: 1rem 0;
  color: ${
    ({ $score }) =>
      $score >= 80
        ? '#10b981' // green-500
        : $score >= 60
          ? '#3b82f6' // blue-500
          : $score >= 40
            ? '#f59e0b' // yellow-500
            : '#ef4444' // red-500
  };
`;

export const ProgressBar = styled.div<{ $percentage: number }>`
  height: 0.75rem;
  background-color: #e5e7eb;
  border-radius: 0.375rem;
  overflow: hidden;
  margin: 1rem 0;

  &::after {
    content: '';
    display: block;
    height: 100%;
    width: ${({ $percentage }) => Math.min(100, Math.max(0, $percentage))}%;
    background-color: ${
      ({ $percentage }) =>
        $percentage >= 80
          ? '#10b981' // green-500
          : $percentage >= 60
            ? '#3b82f6' // blue-500
            : $percentage >= 40
              ? '#f59e0b' // yellow-500
              : '#ef4444' // red-500
    };
    transition: width 0.5s ease-in-out;
  }
`;

export const StatCard = styled.div`
  background: #f9fafb;
  border-radius: 0.375rem;
  padding: 1rem;
  text-align: center;
`;

export const StatValue = styled.div`
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.25rem;
`;

export const StatLabel = styled.div`
  font-size: 0.75rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
`;
