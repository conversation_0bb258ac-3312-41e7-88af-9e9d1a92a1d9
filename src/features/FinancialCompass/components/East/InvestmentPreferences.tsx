/**
 * Investment Preferences Component
 *
 * This component helps users define their investment preferences and strategy for retirement.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { formatCurrency } from '../../../../utils/formatters';

interface InvestmentPreferencesProps {
  onComplete?: () => void;
  onBack?: () => void;
}

interface InvestmentPreferencesData {
  riskTolerance: 'conservative' | 'moderate' | 'aggressive';
  investmentHorizon: string;
  preferredAssetAllocation: {
    stocks: number;
    bonds: number;
    cash: number;
    alternatives: number;
  };
  investmentGoals: string[];
  monthlyInvestmentAmount: string;
  preferredInvestmentTypes: string[];
  esgPreferences: boolean;
  rebalancingFrequency: string;
  notes: string;
}

const InvestmentPreferences: React.FC<InvestmentPreferencesProps> = ({ onComplete, onBack }) => {
  const { theme } = useTheme();
  const { data, updateData } = useFinancialCompass();

  const [isSaving, setIsSaving] = useState(false);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Initialize form data from context or with defaults
  const [formData, setFormData] = useState<InvestmentPreferencesData>(
    data.east?.investmentPreferences || {
      riskTolerance: 'moderate',
      investmentHorizon: '20',
      preferredAssetAllocation: {
        stocks: 60,
        bonds: 30,
        cash: 5,
        alternatives: 5,
      },
      investmentGoals: [],
      monthlyInvestmentAmount: '',
      preferredInvestmentTypes: [],
      esgPreferences: false,
      rebalancingFrequency: 'annually',
      notes: '',
    }
  );

  // Auto-save functionality
  useEffect(() => {
    const saveData = async () => {
      setIsSaving(true);
      try {
        updateData('east', 'investmentPreferences', formData);
        setShowSaveIndicator(true);
        setTimeout(() => setShowSaveIndicator(false), 2000);
      } catch (error) {
        console.error('Error saving investment preferences:', error);
      } finally {
        setIsSaving(false);
      }
    };

    const timeoutId = setTimeout(saveData, 1000);
    return () => clearTimeout(timeoutId);
  }, [formData, updateData]);

  // Handle form field changes
  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle asset allocation changes
  const handleAllocationChange = (asset: string, value: number) => {
    setFormData(prev => ({
      ...prev,
      preferredAssetAllocation: {
        ...prev.preferredAssetAllocation,
        [asset]: value,
      },
    }));
  };

  // Handle array field changes (goals, investment types)
  const handleArrayFieldChange = (field: string, value: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: checked
        ? [...(prev[field as keyof InvestmentPreferencesData] as string[]), value]
        : (prev[field as keyof InvestmentPreferencesData] as string[]).filter(item => item !== value),
    }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (onComplete) {
      onComplete();
    }
  };

  // Calculate total allocation percentage
  const totalAllocation = Object.values(formData.preferredAssetAllocation).reduce((sum, value) => sum + value, 0);

  // Investment goals options
  const investmentGoalsOptions = [
    'Retirement Income',
    'Wealth Preservation',
    'Capital Growth',
    'Tax Efficiency',
    'Inflation Protection',
    'Legacy Planning',
  ];

  // Investment types options
  const investmentTypesOptions = [
    'Index Funds',
    'Mutual Funds',
    'ETFs',
    'Individual Stocks',
    'Bonds',
    'Real Estate',
    'Commodities',
    'Target-Date Funds',
  ];

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Investment Preferences</Title>
        <Description theme={theme}>
          Define your investment strategy and preferences for retirement planning.
        </Description>
      </Header>

      <form onSubmit={handleSubmit}>
        {/* Risk Tolerance */}
        <Section>
          <SectionTitle theme={theme}>Risk Tolerance</SectionTitle>
          <RiskToleranceGrid>
            {[
              { value: 'conservative', label: 'Conservative', description: 'Lower risk, steady returns' },
              { value: 'moderate', label: 'Moderate', description: 'Balanced risk and return' },
              { value: 'aggressive', label: 'Aggressive', description: 'Higher risk, higher potential returns' },
            ].map((option) => (
              <RiskOption
                key={option.value}
                selected={formData.riskTolerance === option.value}
                onClick={() => handleInputChange('riskTolerance', option.value)}
                theme={theme}
              >
                <RiskLabel>{option.label}</RiskLabel>
                <RiskDescription>{option.description}</RiskDescription>
              </RiskOption>
            ))}
          </RiskToleranceGrid>
        </Section>

        {/* Investment Horizon */}
        <Section>
          <SectionTitle theme={theme}>Investment Horizon</SectionTitle>
          <InputGroup>
            <Label theme={theme}>Years until retirement</Label>
            <Input
              type="number"
              value={formData.investmentHorizon}
              onChange={(e) => handleInputChange('investmentHorizon', e.target.value)}
              min="1"
              max="50"
              theme={theme}
            />
          </InputGroup>
        </Section>

        {/* Asset Allocation */}
        <Section>
          <SectionTitle theme={theme}>Preferred Asset Allocation</SectionTitle>
          <AllocationGrid>
            {Object.entries(formData.preferredAssetAllocation).map(([asset, value]) => (
              <AllocationItem key={asset}>
                <AllocationLabel theme={theme}>
                  {asset.charAt(0).toUpperCase() + asset.slice(1)}
                </AllocationLabel>
                <AllocationSlider
                  type="range"
                  min="0"
                  max="100"
                  value={value}
                  onChange={(e) => handleAllocationChange(asset, parseInt(e.target.value))}
                  theme={theme}
                />
                <AllocationValue theme={theme}>{value}%</AllocationValue>
              </AllocationItem>
            ))}
          </AllocationGrid>
          <AllocationTotal
            theme={theme}
            isValid={totalAllocation === 100}
          >
            Total: {totalAllocation}% {totalAllocation !== 100 && '(Should equal 100%)'}
          </AllocationTotal>
        </Section>

        {/* Investment Goals */}
        <Section>
          <SectionTitle theme={theme}>Investment Goals</SectionTitle>
          <CheckboxGrid>
            {investmentGoalsOptions.map((goal) => (
              <CheckboxItem key={goal}>
                <Checkbox
                  type="checkbox"
                  checked={formData.investmentGoals.includes(goal)}
                  onChange={(e) => handleArrayFieldChange('investmentGoals', goal, e.target.checked)}
                />
                <CheckboxLabel theme={theme}>{goal}</CheckboxLabel>
              </CheckboxItem>
            ))}
          </CheckboxGrid>
        </Section>

        {/* Monthly Investment Amount */}
        <Section>
          <SectionTitle theme={theme}>Monthly Investment Amount</SectionTitle>
          <InputGroup>
            <Label theme={theme}>How much can you invest monthly?</Label>
            <Input
              type="number"
              value={formData.monthlyInvestmentAmount}
              onChange={(e) => handleInputChange('monthlyInvestmentAmount', e.target.value)}
              placeholder="Enter amount"
              min="0"
              theme={theme}
            />
          </InputGroup>
        </Section>

        {/* Preferred Investment Types */}
        <Section>
          <SectionTitle theme={theme}>Preferred Investment Types</SectionTitle>
          <CheckboxGrid>
            {investmentTypesOptions.map((type) => (
              <CheckboxItem key={type}>
                <Checkbox
                  type="checkbox"
                  checked={formData.preferredInvestmentTypes.includes(type)}
                  onChange={(e) => handleArrayFieldChange('preferredInvestmentTypes', type, e.target.checked)}
                />
                <CheckboxLabel theme={theme}>{type}</CheckboxLabel>
              </CheckboxItem>
            ))}
          </CheckboxGrid>
        </Section>

        {/* ESG Preferences */}
        <Section>
          <SectionTitle theme={theme}>ESG Investing</SectionTitle>
          <CheckboxItem>
            <Checkbox
              type="checkbox"
              checked={formData.esgPreferences}
              onChange={(e) => handleInputChange('esgPreferences', e.target.checked)}
            />
            <CheckboxLabel theme={theme}>
              I prefer Environmental, Social, and Governance (ESG) investments
            </CheckboxLabel>
          </CheckboxItem>
        </Section>

        {/* Rebalancing Frequency */}
        <Section>
          <SectionTitle theme={theme}>Rebalancing Frequency</SectionTitle>
          <Select
            value={formData.rebalancingFrequency}
            onChange={(e) => handleInputChange('rebalancingFrequency', e.target.value)}
            theme={theme}
          >
            <option value="monthly">Monthly</option>
            <option value="quarterly">Quarterly</option>
            <option value="annually">Annually</option>
            <option value="asNeeded">As Needed</option>
          </Select>
        </Section>

        {/* Notes */}
        <Section>
          <SectionTitle theme={theme}>Additional Notes</SectionTitle>
          <TextArea
            value={formData.notes}
            onChange={(e) => handleInputChange('notes', e.target.value)}
            placeholder="Any additional investment preferences or considerations..."
            rows={4}
            theme={theme}
          />
        </Section>

        <ButtonContainer>
          {onBack && (
            <BackButton type="button" onClick={onBack} theme={theme}>
              Back
            </BackButton>
          )}

          <SaveIndicator visible={showSaveIndicator}>✓ Saved</SaveIndicator>

          <SubmitButton type="submit" theme={theme}>
            {isSaving ? 'Saving...' : 'Save & Continue'}
          </SubmitButton>
        </ButtonContainer>
      </form>
    </Container>
  );
};

// Styled Components
const Container = styled.div<{ theme: any }>`
  padding: 24px;
  max-width: 800px;
  margin: 0 auto;
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 32px;
  text-align: center;
`;

const Title = styled.h2<{ theme: any }>`
  color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
`;

const Description = styled.p<{ theme: any }>`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin: 0;
  font-size: 16px;
  opacity: 0.8;
`;

const Section = styled.div`
  margin-bottom: 32px;
`;

const SectionTitle = styled.h3<{ theme: any }>`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
`;

const RiskToleranceGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
`;

const RiskOption = styled.div<{ selected: boolean; theme: any }>`
  padding: 20px;
  border: 2px solid ${({ selected, theme }) =>
    selected
      ? theme.colors?.primary || '#1976d2'
      : theme.colors?.border || '#e0e0e0'
  };
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: ${({ selected, theme }) =>
    selected
      ? theme.colors?.primary ? `${theme.colors.primary}10` : '#1976d210'
      : theme.colors?.background || '#ffffff'
  };

  &:hover {
    border-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  }
`;

const RiskLabel = styled.div`
  font-weight: 600;
  margin-bottom: 4px;
`;

const RiskDescription = styled.div`
  font-size: 14px;
  opacity: 0.8;
`;

const InputGroup = styled.div`
  margin-bottom: 16px;
`;

const Label = styled.label<{ theme: any }>`
  display: block;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  font-weight: 500;
  margin-bottom: 8px;
`;

const Input = styled.input<{ theme: any }>`
  width: 100%;
  padding: 12px;
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 4px;
  font-size: 16px;
  background: ${({ theme }) => theme.colors?.background || '#ffffff'};
  color: ${({ theme }) => theme.colors?.text || '#000000'};

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  }
`;

const AllocationGrid = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const AllocationItem = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const AllocationLabel = styled.div<{ theme: any }>`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  font-weight: 500;
  min-width: 100px;
`;

const AllocationSlider = styled.input<{ theme: any }>`
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  outline: none;

  &::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: ${({ theme }) => theme.colors?.primary || '#1976d2'};
    cursor: pointer;
  }
`;

const AllocationValue = styled.div<{ theme: any }>`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  font-weight: 600;
  min-width: 50px;
  text-align: right;
`;

const AllocationTotal = styled.div<{ theme: any; isValid: boolean }>`
  color: ${({ isValid, theme }) =>
    isValid
      ? theme.colors?.success || '#4caf50'
      : theme.colors?.error || '#f44336'
  };
  font-weight: 600;
  margin-top: 8px;
  text-align: center;
`;

const CheckboxGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
`;

const CheckboxItem = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const Checkbox = styled.input`
  width: 18px;
  height: 18px;
`;

const CheckboxLabel = styled.label<{ theme: any }>`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  font-size: 14px;
  cursor: pointer;
`;

const Select = styled.select<{ theme: any }>`
  width: 100%;
  padding: 12px;
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 4px;
  font-size: 16px;
  background: ${({ theme }) => theme.colors?.background || '#ffffff'};
  color: ${({ theme }) => theme.colors?.text || '#000000'};

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  }
`;

const TextArea = styled.textarea<{ theme: any }>`
  width: 100%;
  padding: 12px;
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 4px;
  font-size: 16px;
  background: ${({ theme }) => theme.colors?.background || '#ffffff'};
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  resize: vertical;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  }
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
`;

const BackButton = styled.button<{ theme: any }>`
  background: transparent;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 4px;
  padding: 12px 24px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${({ theme }) => theme.colors?.background || '#f5f5f5'};
  }
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  color: #4caf50;
  font-weight: 500;
  opacity: ${({ visible }) => (visible ? 1 : 0)};
  transition: opacity 0.3s ease;
`;

const SubmitButton = styled.button<{ theme: any }>`
  background: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  color: white;
  border: none;
  border-radius: 4px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${({ theme }) => theme.colors?.primaryDark || '#1565c0'};
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

export default InvestmentPreferences;
