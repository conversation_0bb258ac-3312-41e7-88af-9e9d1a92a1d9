/**
 * Investment Preferences Component
 *
 * This component helps users define their investment preferences and strategy for retirement.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { formatCurrency } from '../../../../utils/formatters';

interface InvestmentPreferencesProps {
  onComplete?: () => void;
  onBack?: () => void;
}

interface InvestmentPreferencesData {
  riskTolerance: 'conservative' | 'moderate' | 'aggressive';
  investmentHorizon: string;
  preferredAssetAllocation: {
    stocks: number;
    bonds: number;
    cash: number;
    alternatives: number;
  };
  investmentGoals: string[];
  monthlyInvestmentAmount: string;
  preferredInvestmentTypes: string[];
  esgPreferences: boolean;
  rebalancingFrequency: string;
  notes: string;
}

const InvestmentPreferences: React.FC<InvestmentPreferencesProps> = ({ onComplete, onBack }) => {
  const { theme } = useTheme();
  const { data, updateData } = useFinancialCompass();

  const [isSaving, setIsSaving] = useState(false);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Initialize form data from context or with defaults
  const [formData, setFormData] = useState<InvestmentPreferencesData>(
    data.east?.investmentPreferences || {
      riskTolerance: 'moderate',
      investmentHorizon: '20',
      preferredAssetAllocation: {
        stocks: 60,
        bonds: 30,
        cash: 5,
        alternatives: 5,
      },
      investmentGoals: [],
      monthlyInvestmentAmount: '',
      preferredInvestmentTypes: [],
      esgPreferences: false,
      rebalancingFrequency: 'annually',
      notes: '',
    }
  );

  // Auto-save functionality
  useEffect(() => {
    const saveData = async () => {
      setIsSaving(true);
      try {
        updateData('east', 'investmentPreferences', formData);
        setShowSaveIndicator(true);
        setTimeout(() => setShowSaveIndicator(false), 2000);
      } catch (error) {
        console.error('Error saving investment preferences:', error);
      } finally {
        setIsSaving(false);
      }
    };

    const timeoutId = setTimeout(saveData, 1000);
    return () => clearTimeout(timeoutId);
  }, [formData, updateData]);

  // Handle form field changes
  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle asset allocation changes
  const handleAllocationChange = (asset: string, value: number) => {
    setFormData(prev => ({
      ...prev,
      preferredAssetAllocation: {
        ...prev.preferredAssetAllocation,
        [asset]: value,
      },
    }));
  };

  // Handle array field changes (goals, investment types)
  const handleArrayFieldChange = (field: string, value: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: checked
        ? [...(prev[field as keyof InvestmentPreferencesData] as string[]), value]
        : (prev[field as keyof InvestmentPreferencesData] as string[]).filter(item => item !== value),
    }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (onComplete) {
      onComplete();
    }
  };

  // Calculate total allocation percentage
  const totalAllocation = Object.values(formData.preferredAssetAllocation).reduce((sum, value) => sum + value, 0);

  // Investment goals options
  const investmentGoalsOptions = [
    'Retirement Income',
    'Wealth Preservation',
    'Capital Growth',
    'Tax Efficiency',
    'Inflation Protection',
    'Legacy Planning',
  ];

  // Investment types options
  const investmentTypesOptions = [
    'Index Funds',
    'Mutual Funds',
    'ETFs',
    'Individual Stocks',
    'Bonds',
    'Real Estate',
    'Commodities',
    'Target-Date Funds',
  ];

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Investment Preferences</Title>
        <Description theme={theme}>
          Define your investment strategy and preferences for retirement planning.
        </Description>
      </Header>

      <form onSubmit={handleSubmit}>
        {/* Risk Tolerance */}
        <Section>
          <SectionTitle theme={theme}>Risk Tolerance</SectionTitle>
          <RiskToleranceGrid>
            {[
              { value: 'conservative', label: 'Conservative', description: 'Lower risk, steady returns' },
              { value: 'moderate', label: 'Moderate', description: 'Balanced risk and return' },
              { value: 'aggressive', label: 'Aggressive', description: 'Higher risk, higher potential returns' },
            ].map((option) => (
              <RiskOption
                key={option.value}
                selected={formData.riskTolerance === option.value}
                onClick={() => handleInputChange('riskTolerance', option.value)}
                theme={theme}
              >
                <RiskLabel>{option.label}</RiskLabel>
                <RiskDescription>{option.description}</RiskDescription>
              </RiskOption>
            ))}
          </RiskToleranceGrid>
        </Section>

        {/* Investment Horizon */}
        <Section>
          <SectionTitle theme={theme}>Investment Horizon</SectionTitle>
          <InputGroup>
            <Label theme={theme}>Years until retirement</Label>
            <Input
              type="number"
              value={formData.investmentHorizon}
              onChange={(e) => handleInputChange('investmentHorizon', e.target.value)}
              min="1"
              max="50"
              theme={theme}
            />
          </InputGroup>
        </Section>

        {/* Asset Allocation */}
        <Section>
          <SectionTitle theme={theme}>Preferred Asset Allocation</SectionTitle>
          <AllocationGrid>
            {Object.entries(formData.preferredAssetAllocation).map(([asset, value]) => (
              <AllocationItem key={asset}>
                <AllocationLabel theme={theme}>
                  {asset.charAt(0).toUpperCase() + asset.slice(1)}
                </AllocationLabel>
                <AllocationSlider
                  type="range"
                  min="0"
                  max="100"
                  value={value}
                  onChange={(e) => handleAllocationChange(asset, parseInt(e.target.value))}
                  theme={theme}
                />
                <AllocationValue theme={theme}>{value}%</AllocationValue>
              </AllocationItem>
            ))}
          </AllocationGrid>
          <AllocationTotal 
            theme={theme} 
            isValid={totalAllocation === 100}
          >
            Total: {totalAllocation}% {totalAllocation !== 100 && '(Should equal 100%)'}
          </AllocationTotal>
        </Section>

        {/* Investment Goals */}
        <Section>
          <SectionTitle theme={theme}>Investment Goals</SectionTitle>
          <CheckboxGrid>
            {investmentGoalsOptions.map((goal) => (
              <CheckboxItem key={goal}>
                <Checkbox
                  type="checkbox"
                  checked={formData.investmentGoals.includes(goal)}
                  onChange={(e) => handleArrayFieldChange('investmentGoals', goal, e.target.checked)}
                />
                <CheckboxLabel theme={theme}>{goal}</CheckboxLabel>
              </CheckboxItem>
            ))}
          </CheckboxGrid>
        </Section>

        {/* Monthly Investment Amount */}
        <Section>
          <SectionTitle theme={theme}>Monthly Investment Amount</SectionTitle>
          <InputGroup>
            <Label theme={theme}>How much can you invest monthly?</Label>
            <Input
              type="number"
              value={formData.monthlyInvestmentAmount}
              onChange={(e) => handleInputChange('monthlyInvestmentAmount', e.target.value)}
              placeholder="Enter amount"
              min="0"
              theme={theme}
            />
          </InputGroup>
        </Section>

        {/* Preferred Investment Types */}
        <Section>
          <SectionTitle theme={theme}>Preferred Investment Types</SectionTitle>
          <CheckboxGrid>
            {investmentTypesOptions.map((type) => (
              <CheckboxItem key={type}>
                <Checkbox
                  type="checkbox"
                  checked={formData.preferredInvestmentTypes.includes(type)}
                  onChange={(e) => handleArrayFieldChange('preferredInvestmentTypes', type, e.target.checked)}
                />
                <CheckboxLabel theme={theme}>{type}</CheckboxLabel>
              </CheckboxItem>
            ))}
          </CheckboxGrid>
        </Section>

        {/* ESG Preferences */}
        <Section>
          <SectionTitle theme={theme}>ESG Investing</SectionTitle>
          <CheckboxItem>
            <Checkbox
              type="checkbox"
              checked={formData.esgPreferences}
              onChange={(e) => handleInputChange('esgPreferences', e.target.checked)}
            />
            <CheckboxLabel theme={theme}>
              I prefer Environmental, Social, and Governance (ESG) investments
            </CheckboxLabel>
          </CheckboxItem>
        </Section>

        {/* Rebalancing Frequency */}
        <Section>
          <SectionTitle theme={theme}>Rebalancing Frequency</SectionTitle>
          <Select
            value={formData.rebalancingFrequency}
            onChange={(e) => handleInputChange('rebalancingFrequency', e.target.value)}
            theme={theme}
          >
            <option value="monthly">Monthly</option>
            <option value="quarterly">Quarterly</option>
            <option value="annually">Annually</option>
            <option value="asNeeded">As Needed</option>
          </Select>
        </Section>

        {/* Notes */}
        <Section>
          <SectionTitle theme={theme}>Additional Notes</SectionTitle>
          <TextArea
            value={formData.notes}
            onChange={(e) => handleInputChange('notes', e.target.value)}
            placeholder="Any additional investment preferences or considerations..."
            rows={4}
            theme={theme}
          />
        </Section>

        <ButtonContainer>
          {onBack && (
            <BackButton type="button" onClick={onBack} theme={theme}>
              Back
            </BackButton>
          )}

          <SaveIndicator visible={showSaveIndicator}>✓ Saved</SaveIndicator>

          <SubmitButton type="submit" theme={theme}>
            {isSaving ? 'Saving...' : 'Save & Continue'}
          </SubmitButton>
        </ButtonContainer>
      </form>
    </Container>
  );
};
