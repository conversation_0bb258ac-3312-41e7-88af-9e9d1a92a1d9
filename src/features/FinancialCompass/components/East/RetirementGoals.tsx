/**
 * Retirement Goals Component
 *
 * This component collects and displays the user's retirement goals as part of the
 * East direction of the Financial Compass ("Where You're Going").
 * Enhanced with Monte Carlo simulation integration and improved validation.
 */

import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { useGuidedJourney } from '../../../GuidedJourney/context/GuidedJourneyContext';
import { formatCurrency } from '../../../../utils/formatters';
import MonteCarloSimulation from './MonteCarloSimulation';

interface RetirementGoalsProps {
  onComplete?: () => void;
  onBack?: () => void;
}

interface RetirementGoalsData {
  retirementAge: string;
  currentAge: string;
  lifeExpectancy: string;
  savingsGoal: string;
  desiredAnnualIncome: string;
  retirementLifestyle: 'minimal' | 'modest' | 'comfortable' | 'luxurious';
  retirementLocation: string;
  retirementActivities: string[];
  priorityGoals: string[];
}

const RetirementGoals: React.FC<RetirementGoalsProps> = ({ onComplete, onBack }) => {
  const { theme } = useTheme();
  const { data, updateData } = useFinancialCompass();
  const { askQuestion } = useGuidedJourney();

  const [isSaving, setIsSaving] = useState(false);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);
  const [showMonteCarloSimulation, setShowMonteCarloSimulation] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const skipNextUpdate = useRef(false);

  // Initialize form data from context or with defaults
  const [formData, setFormData] = useState<RetirementGoalsData>(
    (data.east?.retirementGoals as RetirementGoalsData) || {
      retirementAge: '65',
      currentAge: '30',
      lifeExpectancy: '90',
      savingsGoal: '1000000',
      desiredAnnualIncome: '60000',
      retirementLifestyle: 'comfortable',
      retirementLocation: '',
      retirementActivities: [],
      priorityGoals: [],
    }
  );

  // Activity options
  const activityOptions = [
    'Travel',
    'Hobbies',
    'Volunteering',
    'Part-time work',
    'Education',
    'Family time',
    'Relocation',
    'Starting a business',
  ];

  // Priority goal options
  const priorityGoalOptions = [
    'Financial security',
    'Health and wellness',
    'Travel and experiences',
    'Family legacy',
    'Charitable giving',
    'Continued learning',
    'Second career',
    'Homeownership',
  ];

  // Handle form field changes
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle checkbox changes for activities
  const handleActivityChange = (activity: string) => {
    setFormData((prev) => {
      const activities = [...prev.retirementActivities];

      if (activities.includes(activity)) {
        return {
          ...prev,
          retirementActivities: activities.filter((a) => a !== activity),
        };
      } else {
        return {
          ...prev,
          retirementActivities: [...activities, activity],
        };
      }
    });
  };

  // Handle checkbox changes for priority goals
  const handlePriorityGoalChange = (goal: string) => {
    setFormData((prev) => {
      const goals = [...prev.priorityGoals];

      if (goals.includes(goal)) {
        return {
          ...prev,
          priorityGoals: goals.filter((g) => g !== goal),
        };
      } else {
        return {
          ...prev,
          priorityGoals: [...goals, goal],
        };
      }
    });
  };

  // Validate form with enhanced validation
  const validateForm = (): boolean => {
    const errors: string[] = [];

    // Validate current age
    const currentAge = parseInt(formData.currentAge);
    if (isNaN(currentAge) || currentAge < 18 || currentAge > 100) {
      errors.push('Current age must be between 18 and 100.');
    }

    // Validate retirement age
    const retirementAge = parseInt(formData.retirementAge);
    if (isNaN(retirementAge) || retirementAge < 40 || retirementAge > 100) {
      errors.push('Retirement age must be between 40 and 100.');
    }

    // Validate retirement age is greater than current age
    if (!isNaN(currentAge) && !isNaN(retirementAge) && retirementAge <= currentAge) {
      errors.push('Retirement age must be greater than current age.');
    }

    // Validate life expectancy
    const lifeExpectancy = parseInt(formData.lifeExpectancy);
    if (isNaN(lifeExpectancy) || lifeExpectancy < 60 || lifeExpectancy > 120) {
      errors.push('Life expectancy must be between 60 and 120.');
    }

    // Validate life expectancy is greater than retirement age
    if (!isNaN(retirementAge) && !isNaN(lifeExpectancy) && lifeExpectancy <= retirementAge) {
      errors.push('Life expectancy must be greater than retirement age.');
    }

    // Validate savings goal
    const savingsGoal = parseFloat(formData.savingsGoal);
    if (isNaN(savingsGoal) || savingsGoal < 0) {
      errors.push('Savings goal must be a positive number.');
    }

    // Validate desired annual income
    const desiredAnnualIncome = parseFloat(formData.desiredAnnualIncome);
    if (isNaN(desiredAnnualIncome) || desiredAnnualIncome < 0) {
      errors.push('Desired annual income must be a positive number.');
    }

    // Update validation errors state
    setValidationErrors(errors);

    return errors.length === 0;
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      handleSave();

      if (onComplete) {
        onComplete();
      }
    } else {
      // Display validation errors
      alert('Please correct the following errors:\n' + validationErrors.join('\n'));
    }
  };

  // Enhanced handle saving data with multiple persistence mechanisms
  const handleSave = () => {
    setIsSaving(true);

    // Create complete data object with timestamp
    const completeData = {
      ...formData,
      lastUpdated: new Date().toISOString(), // Add timestamp for tracking changes
    };

    // Set flag to prevent auto-save from triggering an infinite loop
    skipNextUpdate.current = true;

    // 1. Update data in context
    updateData('east', 'retirementGoals', completeData);

    // 2. Also save to localStorage directly to ensure persistence
    const storageKey = 'lifecompass_financial_compass';
    try {
      const storedData = localStorage.getItem(storageKey);
      if (storedData) {
        const parsedData = JSON.parse(storedData);

        // Make sure we're saving to the correct structure
        let updatedData;

        // Check if the data structure has a nested 'data' property
        if (parsedData.data) {
          updatedData = {
            ...parsedData,
            data: {
              ...parsedData.data,
              east: {
                ...parsedData.data.east,
                retirementGoals: completeData,
              },
            },
          };
        } else {
          // Direct structure without nested 'data'
          updatedData = {
            ...parsedData,
            east: {
              ...parsedData.east,
              retirementGoals: completeData,
            },
          };
        }

        localStorage.setItem(storageKey, JSON.stringify(updatedData));
        console.log('Retirement goals saved to localStorage');
      } else {
        // If no data exists yet, create a new structure
        const newData = {
          north: {},
          east: {
            retirementGoals: completeData,
          },
          south: {},
          west: {},
        };
        localStorage.setItem(storageKey, JSON.stringify(newData));
        console.log('Created new data in localStorage with retirement goals');
      }
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }

    // 3. Create a backup in sessionStorage as an additional safety measure
    try {
      sessionStorage.setItem('retirement_goals_backup', JSON.stringify(completeData));
      console.log('Retirement goals backup saved to sessionStorage');
    } catch (error) {
      console.error('Error saving backup to sessionStorage:', error);
    }

    // Show save indicator
    setShowSaveIndicator(true);

    // Hide save indicator after 2 seconds
    setTimeout(() => {
      setIsSaving(false);
      setShowSaveIndicator(false);
    }, 2000);
  };

  // Calculate years until retirement
  const calculateYearsUntilRetirement = () => {
    const currentAge = parseInt(formData.currentAge) || 30;
    const retirementAge = parseInt(formData.retirementAge) || 65;
    return Math.max(0, retirementAge - currentAge);
  };

  // Calculate retirement duration
  const calculateRetirementDuration = () => {
    const retirementAge = parseInt(formData.retirementAge) || 65;
    const lifeExpectancy = parseInt(formData.lifeExpectancy) || 90;
    return Math.max(0, lifeExpectancy - retirementAge);
  };

  // Calculate monthly savings needed
  const calculateMonthlySavingsNeeded = () => {
    const yearsUntilRetirement = calculateYearsUntilRetirement();
    const savingsGoal = parseFloat(formData.savingsGoal) || 1000000;

    if (yearsUntilRetirement <= 0) return 0;

    const monthsUntilRetirement = yearsUntilRetirement * 12;
    return savingsGoal / monthsUntilRetirement;
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Retirement Goals</Title>
        <Description theme={theme}>
          Define your retirement vision and set clear goals for your future.
        </Description>
      </Header>

      <form onSubmit={handleSubmit}>
        <FormSection>
          <SectionTitle>Retirement Timeline</SectionTitle>
          <FormRow>
            <FormField>
              <Label htmlFor="currentAge">Current Age</Label>
              <Input
                type="number"
                id="currentAge"
                name="currentAge"
                value={formData.currentAge}
                onChange={handleChange}
                min="18"
                max="100"
                required
              />
            </FormField>

            <FormField>
              <Label htmlFor="retirementAge">Target Retirement Age</Label>
              <Input
                type="number"
                id="retirementAge"
                name="retirementAge"
                value={formData.retirementAge}
                onChange={handleChange}
                min="40"
                max="100"
                required
              />
            </FormField>

            <FormField>
              <Label htmlFor="lifeExpectancy">Life Expectancy</Label>
              <Input
                type="number"
                id="lifeExpectancy"
                name="lifeExpectancy"
                value={formData.lifeExpectancy}
                onChange={handleChange}
                min="60"
                max="120"
                required
              />
            </FormField>
          </FormRow>

          <TimelineVisualization theme={theme}>
            <TimelineSegment
              width={calculateYearsUntilRetirement()}
              color={theme.colors.primary.light}
              theme={theme}
            >
              <TimelineLabel>Working Years: {calculateYearsUntilRetirement()}</TimelineLabel>
            </TimelineSegment>
            <TimelineSegment
              width={calculateRetirementDuration()}
              color={theme.colors.secondary.light}
              theme={theme}
            >
              <TimelineLabel>Retirement Years: {calculateRetirementDuration()}</TimelineLabel>
            </TimelineSegment>
          </TimelineVisualization>
        </FormSection>

        <FormSection>
          <SectionTitle>Financial Goals</SectionTitle>
          <FormRow>
            <FormField>
              <Label htmlFor="savingsGoal">Retirement Savings Goal</Label>
              <Input
                type="number"
                id="savingsGoal"
                name="savingsGoal"
                value={formData.savingsGoal}
                onChange={handleChange}
                min="0"
                step="10000"
                required
              />
              <FieldHint>Total amount you aim to save for retirement</FieldHint>
            </FormField>

            <FormField>
              <Label htmlFor="desiredAnnualIncome">Desired Annual Income</Label>
              <Input
                type="number"
                id="desiredAnnualIncome"
                name="desiredAnnualIncome"
                value={formData.desiredAnnualIncome}
                onChange={handleChange}
                min="0"
                step="1000"
                required
              />
              <FieldHint>Annual income needed during retirement</FieldHint>
            </FormField>
          </FormRow>

          <SavingsCalculation theme={theme}>
            <SavingsLabel>Estimated Monthly Savings Needed:</SavingsLabel>
            <SavingsAmount>
              {formatCurrency(calculateMonthlySavingsNeeded().toString())}
            </SavingsAmount>
            <SavingsNote>
              Based on your current age, target retirement age, and savings goal
            </SavingsNote>

            <SimulationButton
              type="button"
              onClick={() => setShowMonteCarloSimulation(true)}
              theme={theme}
            >
              Run Monte Carlo Simulation
            </SimulationButton>
            <SimulationDescription theme={theme}>
              Analyze your retirement plan with 1,000+ market scenarios to determine success
              probability.
            </SimulationDescription>
          </SavingsCalculation>

          {/* Monte Carlo Simulation Modal */}
          {showMonteCarloSimulation && (
            <ModalOverlay onClick={() => setShowMonteCarloSimulation(false)}>
              <ModalContent onClick={(e) => e.stopPropagation()} theme={theme}>
                <MonteCarloSimulation onClose={() => setShowMonteCarloSimulation(false)} />
              </ModalContent>
            </ModalOverlay>
          )}
        </FormSection>

        <FormSection>
          <SectionTitle>Lifestyle & Priorities</SectionTitle>
          <FormRow>
            <FormField>
              <Label htmlFor="retirementLifestyle">Desired Retirement Lifestyle</Label>
              <Select
                id="retirementLifestyle"
                name="retirementLifestyle"
                value={formData.retirementLifestyle}
                onChange={handleChange}
                required
              >
                <option value="minimal">Minimal - Basic needs only</option>
                <option value="modest">Modest - Comfortable but frugal</option>
                <option value="comfortable">Comfortable - Some luxuries</option>
                <option value="luxurious">Luxurious - Premium lifestyle</option>
              </Select>
            </FormField>

            <FormField>
              <Label htmlFor="retirementLocation">Planned Retirement Location</Label>
              <Input
                type="text"
                id="retirementLocation"
                name="retirementLocation"
                value={formData.retirementLocation}
                onChange={handleChange}
                placeholder="City, State, or Country"
              />
            </FormField>
          </FormRow>

          <CheckboxGroup>
            <CheckboxGroupLabel>Retirement Activities</CheckboxGroupLabel>
            <CheckboxOptions>
              {activityOptions.map((activity) => (
                <CheckboxOption key={activity}>
                  <Checkbox
                    type="checkbox"
                    id={`activity-${activity}`}
                    checked={formData.retirementActivities.includes(activity)}
                    onChange={() => handleActivityChange(activity)}
                  />
                  <CheckboxLabel htmlFor={`activity-${activity}`}>{activity}</CheckboxLabel>
                </CheckboxOption>
              ))}
            </CheckboxOptions>
          </CheckboxGroup>

          <CheckboxGroup>
            <CheckboxGroupLabel>Priority Goals</CheckboxGroupLabel>
            <CheckboxOptions>
              {priorityGoalOptions.map((goal) => (
                <CheckboxOption key={goal}>
                  <Checkbox
                    type="checkbox"
                    id={`goal-${goal}`}
                    checked={formData.priorityGoals.includes(goal)}
                    onChange={() => handlePriorityGoalChange(goal)}
                  />
                  <CheckboxLabel htmlFor={`goal-${goal}`}>{goal}</CheckboxLabel>
                </CheckboxOption>
              ))}
            </CheckboxOptions>
          </CheckboxGroup>
        </FormSection>

        <ButtonContainer>
          {onBack && (
            <BackButton type="button" onClick={onBack} theme={theme}>
              Back
            </BackButton>
          )}

          <SaveIndicator visible={showSaveIndicator}>✓ Saved</SaveIndicator>

          <SubmitButton type="submit" theme={theme}>
            {isSaving ? 'Saving...' : 'Save & Continue'}
          </SubmitButton>
        </ButtonContainer>
      </form>
    </Container>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  padding: 24px;
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const Title = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const Description = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const FormSection = styled.div`
  margin-bottom: 32px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;

const FormField = styled.div`
  margin-bottom: 16px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
`;

const Input = styled.input`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
`;

const Select = styled.select`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
`;

const FieldHint = styled.div`
  font-size: 0.8rem;
  color: #666;
  margin-top: 4px;
`;

const TimelineVisualization = styled.div<{ theme: any }>`
  display: flex;
  margin-top: 24px;
  height: 40px;
  border-radius: 4px;
  overflow: hidden;
`;

const TimelineSegment = styled.div<{ width: number; color: string; theme: any }>`
  flex: ${(props) => props.width};
  background-color: ${(props) => props.color};
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  position: relative;
`;

const TimelineLabel = styled.span`
  font-size: 0.8rem;
  font-weight: 500;
  white-space: nowrap;
`;

const SavingsCalculation = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  padding: 16px;
  border-radius: 4px;
  margin-top: 16px;
`;

const SavingsLabel = styled.div`
  font-weight: 500;
  margin-bottom: 8px;
`;

const SavingsAmount = styled.div`
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 8px;
`;

const SavingsNote = styled.div`
  font-size: 0.8rem;
  color: #666;
`;

const CheckboxGroup = styled.div`
  margin-bottom: 24px;
`;

const CheckboxGroupLabel = styled.div`
  font-weight: 500;
  margin-bottom: 12px;
`;

const CheckboxOptions = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
`;

const CheckboxOption = styled.div`
  display: flex;
  align-items: center;
`;

const Checkbox = styled.input`
  margin-right: 8px;
`;

const CheckboxLabel = styled.label`
  font-size: 0.9rem;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
`;

const BackButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.text.secondary};
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const SubmitButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 24px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  color: green;
  margin-right: 16px;
`;

const SimulationButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.secondary.main};
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: pointer;
  margin-top: 16px;

  &:hover {
    background-color: ${(props) => props.theme.colors.secondary.dark};
  }
`;

const SimulationDescription = styled.p<{ theme: any }>`
  font-size: 0.9rem;
  color: ${(props) => props.theme.colors.text.secondary};
  margin-top: 8px;
`;

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContent = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  max-width: 90%;
  max-height: 90%;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
`;

export default RetirementGoals;
