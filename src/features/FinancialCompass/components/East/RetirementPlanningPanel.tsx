import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Divider,
  TextField,
  Button,
  LinearProgress,
} from '@mui/material';

const calculateProjection = (current: number, years: number, annualReturn = 0.06) => {
  // Compound interest formula: FV = PV * (1 + r)^n
  return current * Math.pow(1 + annualReturn, years);
};

const RetirementPlanningPanel: React.FC = () => {
  const [goalAge, setGoalAge] = useState('65');
  const [savings, setSavings] = useState('');
  const [errors, setErrors] = useState<{ goalAge?: string; savings?: string }>({});
  const [submitted, setSubmitted] = useState(false);
  const [projection, setProjection] = useState<number | null>(null);
  const [years, setYears] = useState(30); // Assume default 35 now, 65 goal

  const validate = () => {
    const newErrors: { goalAge?: string; savings?: string } = {};
    if (!goalAge || isNaN(Number(goalAge)) || Number(goalAge) < 50 || Number(goalAge) > 80) {
      newErrors.goalAge = 'Goal age must be a number between 50 and 80';
    }
    if (!savings || isNaN(Number(savings)) || Number(savings) < 0) {
      newErrors.savings = 'Savings must be a positive number';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitted(true);
    if (validate()) {
      const currentAge = 35; // For demo, assume 35
      const targetAge = Number(goalAge);
      const yearsToGrow = targetAge - currentAge;
      setYears(yearsToGrow);
      const proj = calculateProjection(Number(savings), yearsToGrow);
      setProjection(proj);
    }
  };

  return (
    <Card variant="outlined">
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Retirement Planning
        </Typography>
        <Divider sx={{ mb: 2 }} />
        <Box mb={2}>
          <Typography variant="body2" color="textSecondary">
            Enter your current retirement savings and goal age to see a projection.
          </Typography>
        </Box>
        <form onSubmit={handleSubmit} noValidate>
          <Box display="flex" gap={2} mb={2}>
            <TextField
              label="Goal Retirement Age"
              value={goalAge}
              onChange={(e) => setGoalAge(e.target.value)}
              error={!!errors.goalAge && submitted}
              helperText={submitted && errors.goalAge}
              type="number"
              inputProps={{ min: 50, max: 80 }}
            />
            <TextField
              label="Current Retirement Savings ($)"
              value={savings}
              onChange={(e) => setSavings(e.target.value)}
              error={!!errors.savings && submitted}
              helperText={submitted && errors.savings}
              type="number"
              inputProps={{ min: 0 }}
            />
            <Button type="submit" variant="contained" color="primary">
              Calculate
            </Button>
          </Box>
        </form>
        {projection !== null && (
          <Box mt={3}>
            <Typography variant="subtitle1" gutterBottom>
              Projected Savings at Age {goalAge}:
            </Typography>
            <Typography variant="h5" color="primary" gutterBottom>
              ${projection.toLocaleString(undefined, { maximumFractionDigits: 0 })}
            </Typography>
            <Box mt={2}>
              <Typography variant="body2" color="textSecondary">
                (Assumes {years} years of 6% annual growth, no additional contributions)
              </Typography>
              <LinearProgress
                variant="determinate"
                value={Math.min(100, (projection / 1000000) * 100)}
                sx={{ height: 10, borderRadius: 5, mt: 1 }}
              />
              <Box display="flex" justifyContent="space-between">
                <Typography variant="caption">$0</Typography>
                <Typography variant="caption">$1,000,000</Typography>
              </Box>
            </Box>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default RetirementPlanningPanel;
