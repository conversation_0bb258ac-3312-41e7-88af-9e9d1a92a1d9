/**
 * East Direction Tracker Component
 *
 * This component displays the progress of the East direction sections,
 * provides retirement planning insights, and allows navigation to individual sections.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { useGuidedJourney } from '../../../GuidedJourney/context/GuidedJourneyContext';
import { formatCurrency, formatPercentage } from '../../../../utils/formatters';

interface EastDirectionTrackerProps {
  onSectionSelect?: (sectionId: string) => void;
}

const EastDirectionTracker: React.FC<EastDirectionTrackerProps> = ({ onSectionSelect }) => {
  const { theme } = useTheme();
  const { updateActiveDirection } = useGuidedJourney();
  const { eastSections, updateEastProgress, data } = useFinancialCompass();
  const [completedSections, setCompletedSections] = useState<string[]>([]);
  const [showRetirementInsights, setShowRetirementInsights] = useState(false);

  // Calculate overall progress for East direction
  const totalSections = eastSections?.length || 0;
  const completedCount = completedSections.length;
  const progressPercentage = totalSections > 0 ? (completedCount / totalSections) * 100 : 0;

  // Extract retirement data
  const retirementGoals = data.east?.retirementGoals || {
    targetRetirementAge: '65',
    lifeExpectancy: '90',
    targetMonthlyIncome: '0',
    currentRetirementSavings: '0',
    annualContribution: '0',
    expectedReturnRate: '0',
  };
  const retirementIncome = data.east?.retirementIncome || {
    socialSecurity: '0',
    pension: '0',
    annuities: '0',
    investments: '0',
    otherIncome: '0',
  };
  const retirementExpenses = data.east?.retirementExpenses || {
    housingExpense: '0',
  };
  const socialSecurityPlanning = data.east?.socialSecurityPlanning || {
    selectedClaimingAge: '',
  };

  // Extract personal information
  const personalInfo = data.north?.personalInformation || {
    age: '30',
  };
  const currentAge = parseInt(personalInfo.age || '0');

  // Calculate key retirement metrics
  const targetRetirementAge = parseInt(retirementGoals.targetRetirementAge || '65');
  const lifeExpectancy = parseInt(retirementGoals.lifeExpectancy || '90');
  const yearsUntilRetirement = Math.max(0, targetRetirementAge - currentAge);
  const retirementDuration = Math.max(0, lifeExpectancy - targetRetirementAge);

  const targetRetirementIncome = parseFloat(retirementGoals.targetMonthlyIncome || '0') * 12;
  const currentSavings = parseFloat(retirementGoals.currentRetirementSavings || '0');
  const annualContribution = parseFloat(retirementGoals.annualContribution || '0');
  const expectedReturnRate = parseFloat(retirementGoals.expectedReturnRate || '0') / 100;

  // Calculate projected retirement savings
  const calculateProjectedSavings = () => {
    let projectedSavings = currentSavings;

    for (let i = 0; i < yearsUntilRetirement; i++) {
      projectedSavings = projectedSavings * (1 + expectedReturnRate) + annualContribution;
    }

    return projectedSavings;
  };

  const projectedSavings = calculateProjectedSavings();

  // Calculate safe withdrawal amount (using 4% rule)
  const safeWithdrawalRate = 0.04;
  const annualSafeWithdrawal = projectedSavings * safeWithdrawalRate;
  const monthlySafeWithdrawal = annualSafeWithdrawal / 12;

  // Calculate retirement income gap
  const monthlyTargetIncome = targetRetirementIncome / 12;
  const monthlyIncomeGap = monthlyTargetIncome - monthlySafeWithdrawal;
  const incomeGapPercentage =
    monthlyTargetIncome > 0 ? (monthlyIncomeGap / monthlyTargetIncome) * 100 : 0;

  // Calculate retirement readiness score
  const calculateReadinessScore = () => {
    if (monthlyTargetIncome <= 0) return 0;

    // Base score on income coverage percentage
    const coveragePercentage = Math.min(100, Math.max(0, 100 - incomeGapPercentage));

    // Adjust score based on other factors
    let adjustedScore = coveragePercentage;

    // Adjust for retirement timeline
    if (yearsUntilRetirement < 5 && coveragePercentage < 80) {
      adjustedScore *= 0.8; // Urgent timeline with insufficient savings
    } else if (yearsUntilRetirement > 20) {
      adjustedScore *= 1.1; // Long timeline provides flexibility (cap at 100)
    }

    // Adjust for diversification of income sources
    const hasMultipleIncomeSources =
      parseFloat(retirementIncome.socialSecurity || '0') > 0 &&
      (parseFloat(retirementIncome.pension || '0') > 0 ||
        parseFloat(retirementIncome.annuities || '0') > 0);

    if (hasMultipleIncomeSources) {
      adjustedScore *= 1.1; // Diversified income sources (cap at 100)
    }

    return Math.min(100, Math.max(0, adjustedScore));
  };

  const readinessScore = calculateReadinessScore();

  // Determine retirement readiness status
  const getReadinessStatus = () => {
    if (readinessScore >= 80) return 'On Track';
    if (readinessScore >= 60) return 'Mostly on Track';
    if (readinessScore >= 40) return 'Needs Attention';
    if (readinessScore >= 20) return 'Off Track';
    return 'Critical';
  };

  // Get color based on score
  const getScoreColor = (score: number) => {
    if (score >= 80) return theme.colors.success.main;
    if (score >= 60) return theme.colors.success.light;
    if (score >= 40) return theme.colors.warning.main;
    if (score >= 20) return theme.colors.warning.dark;
    return theme.colors.error.main;
  };

  // Get recommendations based on retirement readiness
  const getRecommendations = () => {
    const recommendations = [];

    // Income gap recommendations
    if (incomeGapPercentage > 10) {
      recommendations.push({
        title: 'Close Your Retirement Income Gap',
        description: `You're projected to have a ${formatPercentage(incomeGapPercentage)}% income gap in retirement. Consider increasing your savings rate or adjusting your retirement lifestyle expectations.`,
        priority: incomeGapPercentage > 30 ? 'High' : 'Medium',
        action: 'retirement_goals',
      });
    }

    // Savings rate recommendations
    const currentIncome = parseFloat(data.north?.incomeDetails?.primaryIncome || '0') * 12;
    const savingsRate = currentIncome > 0 ? (annualContribution / currentIncome) * 100 : 0;

    if (savingsRate < 15 && yearsUntilRetirement < 20) {
      recommendations.push({
        title: 'Increase Your Savings Rate',
        description: `You're currently saving ${formatPercentage(savingsRate)}% of your income for retirement. Consider increasing to at least 15-20% to ensure a secure retirement.`,
        priority: savingsRate < 10 ? 'High' : 'Medium',
        action: 'retirement_goals',
      });
    }

    // Social Security optimization
    if (!socialSecurityPlanning.selectedClaimingAge) {
      recommendations.push({
        title: 'Optimize Social Security Benefits',
        description:
          "You haven't completed your Social Security planning. Optimizing when you claim benefits can significantly impact your retirement income.",
        priority: 'Medium',
        action: 'social_security_planning',
      });
    }

    // Retirement expenses planning
    if (!retirementExpenses.housingExpense) {
      recommendations.push({
        title: 'Plan Your Retirement Expenses',
        description:
          'Complete your retirement expenses planning to get a more accurate picture of your retirement needs.',
        priority: 'Medium',
        action: 'retirement_expenses',
      });
    }

    // If all looks good, provide positive reinforcement
    if (recommendations.length === 0) {
      recommendations.push({
        title: 'Stay on Course',
        description:
          'Your retirement plan is on track. Continue your current savings strategy and review annually to ensure continued progress.',
        priority: 'Low',
        action: 'retirement_goals',
      });
    }

    return recommendations;
  };

  useEffect(() => {
    // Set active direction to East
    if (updateActiveDirection) {
      updateActiveDirection('east');
    }

    // Update progress in Financial Compass context
    if (updateEastProgress) {
      updateEastProgress(progressPercentage);
    }

    // Check which sections are completed
    if (eastSections) {
      const completed = eastSections
        .filter((section) => section.isCompleted)
        .map((section) => section.id);
      setCompletedSections(completed);
    }
  }, [eastSections, progressPercentage, updateActiveDirection, updateEastProgress]);

  const handleSectionClick = (sectionId: string) => {
    if (onSectionSelect) {
      onSectionSelect(sectionId);
    }
  };

  // Toggle retirement insights visibility
  const toggleRetirementInsights = () => {
    setShowRetirementInsights(!showRetirementInsights);
  };

  return (
    <TrackerContainer theme={theme}>
      <TrackerHeader theme={theme}>
        <DirectionTitle theme={theme}>East: Where You're Going</DirectionTitle>
        <DirectionDescription theme={theme}>
          The East direction helps you plan for your retirement future - setting goals, estimating
          expenses, and creating a sustainable income plan.
        </DirectionDescription>
      </TrackerHeader>

      {/* Retirement Dashboard */}
      <DashboardContainer>
        {/* Retirement Readiness */}
        <DashboardSection>
          <DashboardCard theme={theme}>
            <CardHeader>
              <CardTitle theme={theme}>Retirement Readiness</CardTitle>
              <ReadinessScoreValue score={readinessScore}>
                {Math.round(readinessScore)}
              </ReadinessScoreValue>
            </CardHeader>
            <ReadinessScoreStatus score={readinessScore}>
              {getReadinessStatus()}
            </ReadinessScoreStatus>
            <ReadinessScoreBar theme={theme}>
              <ReadinessScoreFill score={readinessScore} theme={theme} />
            </ReadinessScoreBar>

            <MetricBreakdown>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Current Age</MetricBreakdownLabel>
                <MetricBreakdownValue>{currentAge}</MetricBreakdownValue>
              </MetricBreakdownItem>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Retirement Age</MetricBreakdownLabel>
                <MetricBreakdownValue>{targetRetirementAge}</MetricBreakdownValue>
              </MetricBreakdownItem>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Years to Retirement</MetricBreakdownLabel>
                <MetricBreakdownValue>{yearsUntilRetirement}</MetricBreakdownValue>
              </MetricBreakdownItem>
            </MetricBreakdown>

            <ReadinessInsight theme={theme}>
              {readinessScore >= 80
                ? "You're well on track for retirement. Continue your current savings strategy and review annually."
                : readinessScore >= 60
                  ? "You're making good progress toward retirement. Consider increasing your savings rate to improve your readiness."
                  : readinessScore >= 40
                    ? 'Your retirement plan needs attention. Focus on increasing savings and optimizing your investment strategy.'
                    : 'Your retirement readiness is critical. Create a plan to significantly increase savings and reduce the income gap.'}
            </ReadinessInsight>
          </DashboardCard>
        </DashboardSection>

        {/* Retirement Savings */}
        <DashboardSection>
          <DashboardCard theme={theme}>
            <CardHeader>
              <CardTitle theme={theme}>Retirement Savings</CardTitle>
              <CardIcon positive={projectedSavings >= targetRetirementIncome * 25}>💰</CardIcon>
            </CardHeader>
            <MetricHighlight positive={projectedSavings >= targetRetirementIncome * 25}>
              {formatCurrency(projectedSavings)}
            </MetricHighlight>
            <MetricDescription>Projected savings at age {targetRetirementAge}</MetricDescription>

            <MetricBreakdown>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Current Savings</MetricBreakdownLabel>
                <MetricBreakdownValue>{formatCurrency(currentSavings)}</MetricBreakdownValue>
              </MetricBreakdownItem>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Annual Contribution</MetricBreakdownLabel>
                <MetricBreakdownValue>{formatCurrency(annualContribution)}</MetricBreakdownValue>
              </MetricBreakdownItem>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Expected Return</MetricBreakdownLabel>
                <MetricBreakdownValue>
                  {formatPercentage(expectedReturnRate * 100)}
                </MetricBreakdownValue>
              </MetricBreakdownItem>
            </MetricBreakdown>

            <SavingsInsight theme={theme}>
              {projectedSavings >= targetRetirementIncome * 25
                ? 'Your projected savings should provide a sustainable retirement income based on the 4% rule.'
                : projectedSavings >= targetRetirementIncome * 20
                  ? 'Your savings are on track, but consider increasing contributions to build a stronger safety margin.'
                  : projectedSavings >= targetRetirementIncome * 15
                    ? 'Your savings may be sufficient but leave little room for unexpected expenses. Consider increasing contributions.'
                    : 'Your projected savings may not be sufficient for your retirement needs. Increase contributions or adjust expectations.'}
            </SavingsInsight>
          </DashboardCard>
        </DashboardSection>

        {/* Retirement Income */}
        <DashboardSection>
          <DashboardCard theme={theme}>
            <CardHeader>
              <CardTitle theme={theme}>Retirement Income</CardTitle>
              <CardIcon positive={incomeGapPercentage <= 0}>💵</CardIcon>
            </CardHeader>
            <MetricHighlight positive={incomeGapPercentage <= 0}>
              {formatCurrency(monthlySafeWithdrawal)}/month
            </MetricHighlight>
            <MetricDescription>Sustainable monthly withdrawal (4% rule)</MetricDescription>

            <MetricBreakdown>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Target Income</MetricBreakdownLabel>
                <MetricBreakdownValue>
                  {formatCurrency(monthlyTargetIncome)}/month
                </MetricBreakdownValue>
              </MetricBreakdownItem>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Income Gap</MetricBreakdownLabel>
                <MetricBreakdownValue>
                  {incomeGapPercentage <= 0
                    ? 'No Gap'
                    : formatCurrency(monthlyIncomeGap) + '/month'}
                </MetricBreakdownValue>
              </MetricBreakdownItem>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Gap Percentage</MetricBreakdownLabel>
                <MetricBreakdownValue>
                  {incomeGapPercentage <= 0 ? '0%' : formatPercentage(incomeGapPercentage)}
                </MetricBreakdownValue>
              </MetricBreakdownItem>
            </MetricBreakdown>

            <IncomeInsight theme={theme}>
              {incomeGapPercentage <= 0
                ? "Your projected retirement income exceeds your target. You're well-positioned for a comfortable retirement."
                : incomeGapPercentage <= 10
                  ? 'Your income gap is small and can likely be closed with minor adjustments to your savings or expenses.'
                  : incomeGapPercentage <= 25
                    ? 'You have a moderate income gap. Consider increasing savings or adjusting your retirement lifestyle expectations.'
                    : 'Your income gap is significant. Create a plan to increase savings, delay retirement, or reduce retirement expenses.'}
            </IncomeInsight>
          </DashboardCard>
        </DashboardSection>

        {/* Income Sources */}
        <DashboardSection>
          <DashboardCard theme={theme}>
            <CardHeader>
              <CardTitle theme={theme}>Income Sources</CardTitle>
              <CardIcon positive={true}>📊</CardIcon>
            </CardHeader>

            <IncomeSourcesChart theme={theme}>
              {parseFloat(retirementIncome.socialSecurity || '0') > 0 && (
                <IncomeSourceBar
                  width={
                    (parseFloat(retirementIncome.socialSecurity || '0') / monthlyTargetIncome) * 100
                  }
                  color="#4f46e5"
                  theme={theme}
                >
                  <IncomeSourceLabel>Social Security</IncomeSourceLabel>
                  <IncomeSourceValue>
                    {formatCurrency(parseFloat(retirementIncome.socialSecurity || '0'))}/mo
                  </IncomeSourceValue>
                </IncomeSourceBar>
              )}

              {parseFloat(retirementIncome.pension || '0') > 0 && (
                <IncomeSourceBar
                  width={(parseFloat(retirementIncome.pension || '0') / monthlyTargetIncome) * 100}
                  color="#0ea5e9"
                  theme={theme}
                >
                  <IncomeSourceLabel>Pension</IncomeSourceLabel>
                  <IncomeSourceValue>
                    {formatCurrency(parseFloat(retirementIncome.pension || '0'))}/mo
                  </IncomeSourceValue>
                </IncomeSourceBar>
              )}

              {parseFloat(retirementIncome.annuities || '0') > 0 && (
                <IncomeSourceBar
                  width={
                    (parseFloat(retirementIncome.annuities || '0') / monthlyTargetIncome) * 100
                  }
                  color="#10b981"
                  theme={theme}
                >
                  <IncomeSourceLabel>Annuities</IncomeSourceLabel>
                  <IncomeSourceValue>
                    {formatCurrency(parseFloat(retirementIncome.annuities || '0'))}/mo
                  </IncomeSourceValue>
                </IncomeSourceBar>
              )}

              {monthlySafeWithdrawal > 0 && (
                <IncomeSourceBar
                  width={(monthlySafeWithdrawal / monthlyTargetIncome) * 100}
                  color="#ec4899"
                  theme={theme}
                >
                  <IncomeSourceLabel>Portfolio</IncomeSourceLabel>
                  <IncomeSourceValue>{formatCurrency(monthlySafeWithdrawal)}/mo</IncomeSourceValue>
                </IncomeSourceBar>
              )}

              {incomeGapPercentage > 0 && (
                <IncomeSourceBar width={incomeGapPercentage} color="#ef4444" theme={theme}>
                  <IncomeSourceLabel>Gap</IncomeSourceLabel>
                  <IncomeSourceValue>{formatCurrency(monthlyIncomeGap)}/mo</IncomeSourceValue>
                </IncomeSourceBar>
              )}
            </IncomeSourcesChart>

            <DiversificationInsight theme={theme}>
              {parseFloat(retirementIncome.socialSecurity || '0') > 0 &&
              (parseFloat(retirementIncome.pension || '0') > 0 ||
                parseFloat(retirementIncome.annuities || '0') > 0)
                ? 'Your retirement income is well-diversified with multiple guaranteed income sources, providing stability.'
                : parseFloat(retirementIncome.socialSecurity || '0') > 0
                  ? 'Your retirement income relies heavily on Social Security and portfolio withdrawals. Consider adding more guaranteed income sources.'
                  : 'Your retirement income lacks diversification. Consider adding guaranteed income sources like Social Security optimization or annuities.'}
            </DiversificationInsight>
          </DashboardCard>
        </DashboardSection>
      </DashboardContainer>

      {/* Priority Actions */}
      <ActionSection theme={theme}>
        <ActionSectionTitle theme={theme}>Priority Actions</ActionSectionTitle>
        <ActionCards>
          {getRecommendations().map((recommendation, index) => (
            <ActionCard
              key={index}
              priority={recommendation.priority}
              theme={theme}
              onClick={() => recommendation.action && handleSectionClick(recommendation.action)}
            >
              <ActionHeader>
                <ActionTitle>{recommendation.title}</ActionTitle>
                <PriorityBadge priority={recommendation.priority} theme={theme}>
                  {recommendation.priority}
                </PriorityBadge>
              </ActionHeader>
              <ActionDescription>{recommendation.description}</ActionDescription>
              <ActionButtonContainer>
                <ActionButton theme={theme}>Take Action</ActionButton>
              </ActionButtonContainer>
            </ActionCard>
          ))}
        </ActionCards>
      </ActionSection>

      {/* Retirement Journey */}
      <JourneySection theme={theme}>
        <JourneySectionTitle theme={theme}>Your Retirement Journey</JourneySectionTitle>
        <JourneyDescription theme={theme}>
          Complete these sections to build a comprehensive retirement plan for your future.
        </JourneyDescription>

        <JourneySteps>
          {eastSections?.map((section) => (
            <JourneyStep
              key={section.id}
              onClick={() => handleSectionClick(section.id)}
              theme={theme}
              isActive={section.isActive}
            >
              <JourneyStepIcon theme={theme}>{section.icon || '○'}</JourneyStepIcon>
              <JourneyStepContent>
                <JourneyStepTitle theme={theme}>{section.title}</JourneyStepTitle>
                {section.description && (
                  <JourneyStepDescription theme={theme}>
                    {section.description}
                  </JourneyStepDescription>
                )}
              </JourneyStepContent>
              <JourneyStepArrow theme={theme}>→</JourneyStepArrow>
            </JourneyStep>
          ))}
        </JourneySteps>
      </JourneySection>
    </TrackerContainer>
  );
};

// Styled components
const TrackerContainer = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.secondary};
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const TrackerHeader = styled.div<{ theme: any }>`
  margin-bottom: 24px;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 16px;
  position: relative;
`;

const DirectionTitle = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
  font-weight: bold;
`;

const DirectionDescription = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0 0 16px 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const InsightsButton = styled.button<{ theme: any; isActive: boolean }>`
  padding: 8px 16px;
  border-radius: 4px;
  background-color: ${(props) =>
    props.isActive ? props.theme.colors.primary.main : props.theme.colors.background.tertiary};
  color: ${(props) =>
    props.isActive ? props.theme.colors.text.onPrimary : props.theme.colors.text.primary};
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 16px;

  &:hover {
    background-color: ${(props) =>
      props.isActive ? props.theme.colors.primary.dark : props.theme.colors.background.hover};
  }
`;

const ProgressIndicator = styled.div`
  margin-top: 16px;
`;

const ProgressText = styled.div<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin-bottom: 8px;
  font-size: 0.9rem;
`;

const ProgressBar = styled.div<{ theme: any }>`
  height: 8px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-radius: 4px;
  overflow: hidden;
`;

const ProgressFill = styled.div<{ progress: number; theme: any }>`
  height: 100%;
  width: ${(props) => props.progress}%;
  background-color: ${(props) => props.theme.colors.primary.main};
  transition: width 0.3s ease;
`;

// Retirement Insights Styled Components
const RetirementInsights = styled.div<{ theme: any }>`
  margin: 0 0 24px 0;
  padding: 20px;
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
`;

const InsightsTitle = styled.h3<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 20px 0;
  font-size: 1.3rem;
  font-weight: 600;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 12px;
`;

// Readiness Score Card
const ReadinessScoreCard = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
`;

const ReadinessScoreHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
`;

const ReadinessScoreTitle = styled.h4`
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
`;

const ReadinessScoreValue = styled.div<{ score: number }>`
  font-size: 1.8rem;
  font-weight: 700;
  color: ${(props) => {
    if (props.score >= 80) return '#10b981'; // success
    if (props.score >= 60) return '#22c55e'; // success light
    if (props.score >= 40) return '#f59e0b'; // warning
    if (props.score >= 20) return '#d97706'; // warning dark
    return '#ef4444'; // error
  }};
`;

const ReadinessScoreStatus = styled.div<{ score: number }>`
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: ${(props) => {
    if (props.score >= 80) return '#10b981'; // success
    if (props.score >= 60) return '#22c55e'; // success light
    if (props.score >= 40) return '#f59e0b'; // warning
    if (props.score >= 20) return '#d97706'; // warning dark
    return '#ef4444'; // error
  }};
`;

const ReadinessScoreBar = styled.div<{ theme: any }>`
  height: 8px;
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 4px;
  overflow: hidden;
`;

const ReadinessScoreFill = styled.div<{ score: number; theme: any }>`
  height: 100%;
  width: ${(props) => props.score}%;
  background-color: ${(props) => {
    if (props.score >= 80) return props.theme.colors.success.main;
    if (props.score >= 60) return props.theme.colors.success.light;
    if (props.score >= 40) return props.theme.colors.warning.main;
    if (props.score >= 20) return props.theme.colors.warning.dark;
    return props.theme.colors.error.main;
  }};
  transition: width 0.5s ease;
`;

// Metrics Grid
const MetricsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
`;

const MetricCard = styled.div<{ theme: any }>`
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-radius: 8px;
`;

const MetricIcon = styled.div<{ positive?: boolean }>`
  font-size: 1.8rem;
  margin-right: 16px;
  color: ${(props) =>
    props.positive !== undefined ? (props.positive ? '#10b981' : '#ef4444') : '#4f46e5'};
`;

const MetricContent = styled.div`
  flex: 1;
`;

const MetricLabel = styled.div`
  font-size: 0.9rem;
  color: ${(props) => props.theme.colors.text.secondary};
  margin-bottom: 4px;
`;

const MetricValue = styled.div<{ positive?: boolean }>`
  font-size: 1.2rem;
  font-weight: 700;
  color: ${(props) =>
    props.positive !== undefined
      ? props.positive
        ? '#10b981'
        : '#ef4444'
      : props.theme.colors.text.primary};
  margin-bottom: 4px;
`;

const MetricDescription = styled.div`
  font-size: 0.8rem;
  color: ${(props) => props.theme.colors.text.tertiary};
`;

// Income Breakdown
const IncomeBreakdown = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
`;

const IncomeBreakdownTitle = styled.h4<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 8px;
`;

const IncomeBreakdownChart = styled.div<{ theme: any }>`
  height: 24px;
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  margin-bottom: 12px;
`;

const IncomeSource = styled.div<{ percentage: number; color: string; theme: any; tooltip: string }>`
  height: 100%;
  width: ${(props) => props.percentage}%;
  background-color: ${(props) => props.color};
  position: relative;

  &:hover::after {
    content: '${(props) => props.tooltip}';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    z-index: 10;
  }
`;

const IncomeBreakdownLegend = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
`;

const LegendItem = styled.div<{ color: string; theme: any }>`
  display: flex;
  align-items: center;
  font-size: 0.8rem;
  color: ${(props) => props.theme.colors.text.secondary};

  &::before {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 4px;
    background-color: ${(props) => props.color};
    border-radius: 2px;
  }
`;

// Recommendations Section
const RecommendationsSection = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-radius: 8px;
  padding: 16px;
`;

const RecommendationsTitle = styled.h4<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 8px;
`;

const RecommendationsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const RecommendationItem = styled.div<{ priority: string; theme: any }>`
  padding: 12px;
  border-radius: 6px;
  background-color: ${(props) => props.theme.colors.background.paper};
  border-left: 4px solid
    ${(props) =>
      props.priority === 'High'
        ? props.theme.colors.error.main
        : props.priority === 'Medium'
          ? props.theme.colors.warning.main
          : props.theme.colors.success.main};
  cursor: pointer;
  transition: transform 0.2s ease;

  &:hover {
    transform: translateX(4px);
  }
`;

const RecommendationHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
`;

const RecommendationTitle = styled.h5`
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
`;

const RecommendationDescription = styled.p`
  margin: 0;
  font-size: 0.9rem;
  color: ${(props) => props.theme.colors.text.secondary};
  line-height: 1.4;
`;

const PriorityBadge = styled.span<{ priority: string; theme: any }>`
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
  background-color: ${(props) =>
    props.priority === 'High'
      ? props.theme.colors.error.light
      : props.priority === 'Medium'
        ? props.theme.colors.warning.light
        : props.theme.colors.success.light};
  color: ${(props) =>
    props.priority === 'High'
      ? props.theme.colors.error.dark
      : props.priority === 'Medium'
        ? props.theme.colors.warning.dark
        : props.theme.colors.success.dark};
`;

// Sections List
const SectionsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const SectionItem = styled.div<{ isCompleted: boolean; isActive: boolean; theme: any }>`
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  background-color: ${(props) =>
    props.isActive ? props.theme.colors.background.highlight : props.theme.colors.background.main};
  border-left: 4px solid
    ${(props) =>
      props.isCompleted
        ? props.theme.colors.success.main
        : props.isActive
          ? props.theme.colors.primary.main
          : props.theme.colors.border.main};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.hover};
    transform: translateX(4px);
  }
`;

const SectionIcon = styled.div<{ isCompleted: boolean; theme: any }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: ${(props) =>
    props.isCompleted ? props.theme.colors.success.main : props.theme.colors.background.tertiary};
  color: ${(props) =>
    props.isCompleted ? props.theme.colors.text.onPrimary : props.theme.colors.text.secondary};
  margin-right: 16px;
  font-size: 16px;
  flex-shrink: 0;
`;

const SectionContent = styled.div`
  flex: 1;
`;

const SectionTitle = styled.h3<{ isCompleted: boolean; isActive: boolean; theme: any }>`
  margin: 0 0 4px 0;
  font-size: 1.1rem;
  color: ${(props) =>
    props.isCompleted
      ? props.theme.colors.success.dark
      : props.isActive
        ? props.theme.colors.primary.dark
        : props.theme.colors.text.primary};
`;

const SectionDescription = styled.p<{ theme: any }>`
  margin: 0;
  font-size: 0.9rem;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const CompletionDate = styled.div<{ theme: any }>`
  font-size: 0.8rem;
  color: ${(props) => props.theme.colors.text.tertiary};
  margin-top: 4px;
`;

const SectionStatus = styled.div<{ isCompleted: boolean; theme: any }>`
  font-size: 0.8rem;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: ${(props) =>
    props.isCompleted ? props.theme.colors.success.light : props.theme.colors.background.tertiary};
  color: ${(props) =>
    props.isCompleted ? props.theme.colors.success.dark : props.theme.colors.text.secondary};
  margin-left: 16px;
`;

const CompletionMessage = styled.div<{ theme: any }>`
  margin-top: 32px;
  padding: 24px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.success.light};
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const CompletionIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
`;

const CompletionText = styled.p`
  color: ${(props) => props.theme.colors.text.primary};
  font-size: 1.1rem;
  line-height: 1.5;
  margin: 0 0 24px 0;
`;

const NextDirectionButton = styled.button<{ theme: any }>`
  padding: 12px 24px;
  border-radius: 4px;
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.text.onPrimary};
  border: none;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${(props) => props.theme.colors.primary.light};
  }
`;

// Dashboard Components
const DashboardContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
`;

const DashboardSection = styled.div`
  display: flex;
  flex-direction: column;
`;

const DashboardCard = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  height: 100%;
  display: flex;
  flex-direction: column;
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }
`;

const CardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const CardTitle = styled.h3<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
`;

const CardIcon = styled.div<{ positive: boolean }>`
  font-size: 1.8rem;
  color: ${(props) => (props.positive ? '#10b981' : '#ef4444')};
`;

const MetricHighlight = styled.div<{ positive: boolean }>`
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 8px;
  color: ${(props) => (props.positive ? '#10b981' : '#ef4444')};
`;

const MetricBreakdown = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
  padding: 12px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-radius: 8px;
`;

const MetricBreakdownItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const MetricBreakdownLabel = styled.div`
  font-size: 0.85rem;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const MetricBreakdownValue = styled.div`
  font-size: 0.9rem;
  font-weight: 600;
  color: ${(props) => props.theme.colors.text.primary};
`;

// Income Sources Chart
const IncomeSourcesChart = styled.div<{ theme: any }>`
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
`;

const IncomeSourceBar = styled.div<{ width: number; color: string; theme: any }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: ${(props) => props.color};
  color: white;
  border-radius: 4px;
  width: ${(props) => Math.min(100, Math.max(20, props.width))}%;
  min-width: 150px;
  transition: width 0.3s ease;
`;

const IncomeSourceLabel = styled.div`
  font-size: 0.85rem;
  font-weight: 600;
`;

const IncomeSourceValue = styled.div`
  font-size: 0.85rem;
  font-weight: 600;
`;

// Insight Components
const ReadinessInsight = styled.div<{ theme: any }>`
  font-size: 0.9rem;
  line-height: 1.5;
  color: ${(props) => props.theme.colors.text.primary};
  margin-top: auto;
  padding-top: 12px;
  border-top: 1px solid ${(props) => props.theme.colors.border.main};
`;

const SavingsInsight = styled.div<{ theme: any }>`
  font-size: 0.9rem;
  line-height: 1.5;
  color: ${(props) => props.theme.colors.text.primary};
  margin-top: auto;
  padding-top: 12px;
  border-top: 1px solid ${(props) => props.theme.colors.border.main};
`;

const IncomeInsight = styled.div<{ theme: any }>`
  font-size: 0.9rem;
  line-height: 1.5;
  color: ${(props) => props.theme.colors.text.primary};
  margin-top: auto;
  padding-top: 12px;
  border-top: 1px solid ${(props) => props.theme.colors.border.main};
`;

const DiversificationInsight = styled.div<{ theme: any }>`
  font-size: 0.9rem;
  line-height: 1.5;
  color: ${(props) => props.theme.colors.text.primary};
  margin-top: auto;
  padding-top: 12px;
  border-top: 1px solid ${(props) => props.theme.colors.border.main};
`;

// Action Section
const ActionSection = styled.div<{ theme: any }>`
  margin-bottom: 32px;
  padding: 20px;
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
`;

const ActionSectionTitle = styled.h3<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 20px 0;
  font-size: 1.3rem;
  font-weight: 600;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 12px;
`;

const ActionCards = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
`;

const ActionCard = styled.div<{ priority: string; theme: any }>`
  padding: 16px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-left: 4px solid
    ${(props) =>
      props.priority === 'High'
        ? props.theme.colors.error.main
        : props.priority === 'Medium'
          ? props.theme.colors.warning.main
          : props.theme.colors.success.main};
  cursor: pointer;
  transition: transform 0.2s ease;
  display: flex;
  flex-direction: column;
  height: 100%;

  &:hover {
    transform: translateY(-4px);
  }
`;

const ActionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const ActionTitle = styled.h4`
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
`;

const ActionDescription = styled.p`
  margin: 0;
  font-size: 0.9rem;
  color: ${(props) => props.theme.colors.text.secondary};
  line-height: 1.5;
  flex: 1;
`;

const ActionButtonContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  align-items: center;
`;

const ActionButton = styled.div<{ theme: any }>`
  display: inline-block;
  padding: 8px 16px;
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.text.onPrimary};
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

// Journey Section
const JourneySection = styled.div<{ theme: any }>`
  padding: 20px;
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
`;

const JourneySectionTitle = styled.h3<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.3rem;
  font-weight: 600;
`;

const JourneyDescription = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0 0 20px 0;
  font-size: 0.9rem;
  line-height: 1.5;
`;

const JourneySteps = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const JourneyStep = styled.div<{ theme: any; isActive: boolean }>`
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  background-color: ${(props) =>
    props.isActive
      ? props.theme.colors.background.highlight
      : props.theme.colors.background.tertiary};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.hover};
    transform: translateX(8px);
  }
`;

const JourneyStepIcon = styled.div<{ theme: any }>`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.text.onPrimary};
  margin-right: 16px;
  font-size: 1.2rem;
`;

const JourneyStepContent = styled.div`
  flex: 1;
`;

const JourneyStepTitle = styled.h4<{ theme: any }>`
  margin: 0 0 4px 0;
  font-size: 1.1rem;
  color: ${(props) => props.theme.colors.text.primary};
`;

const JourneyStepDescription = styled.p<{ theme: any }>`
  margin: 0;
  font-size: 0.85rem;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const JourneyStepArrow = styled.div<{ theme: any }>`
  font-size: 1.5rem;
  color: ${(props) => props.theme.colors.text.secondary};
  margin-left: 16px;
`;

export default EastDirectionTracker;
