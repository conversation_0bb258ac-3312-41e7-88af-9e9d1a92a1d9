/**
 * Retirement Expenses Component
 *
 * This component collects and displays the user's estimated retirement expenses as part of the
 * East direction of the Financial Compass ("Where You're Going").
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { useGuidedJourney } from '../../../GuidedJourney/context/GuidedJourneyContext';
import { formatCurrency } from '../../../../utils/formatters';

interface RetirementExpensesProps {
  onComplete?: () => void;
  onBack?: () => void;
}

interface ExpenseCategory {
  id: string;
  name: string;
  monthlyAmount: string;
  isEssential: boolean;
  notes: string;
}

interface RetirementExpensesData {
  expenseCategories: ExpenseCategory[];
  housingPlan: 'stay_current' | 'downsize' | 'relocate' | 'retirement_community';
  healthcareCosts: string;
  travelBudget: string;
  hobbiesBudget: string;
  inflationAssumption: string;
}

const RetirementExpenses: React.FC<RetirementExpensesProps> = ({ onComplete, onBack }) => {
  const { theme } = useTheme();
  const { data, updateData } = useFinancialCompass();
  const { askQuestion } = useGuidedJourney();

  const [isSaving, setIsSaving] = useState(false);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Default expense categories
  const defaultExpenseCategories: ExpenseCategory[] = [
    {
      id: 'housing',
      name: 'Housing',
      monthlyAmount: '1500',
      isEssential: true,
      notes: 'Mortgage/rent, property taxes, insurance, utilities, maintenance',
    },
    {
      id: 'food',
      name: 'Food & Groceries',
      monthlyAmount: '600',
      isEssential: true,
      notes: 'Groceries, dining out',
    },
    {
      id: 'healthcare',
      name: 'Healthcare',
      monthlyAmount: '800',
      isEssential: true,
      notes: 'Insurance premiums, out-of-pocket expenses, prescriptions',
    },
    {
      id: 'transportation',
      name: 'Transportation',
      monthlyAmount: '400',
      isEssential: true,
      notes: 'Car payment, insurance, gas, maintenance, public transit',
    },
    {
      id: 'insurance',
      name: 'Insurance',
      monthlyAmount: '300',
      isEssential: true,
      notes: 'Life, disability, long-term care',
    },
    {
      id: 'entertainment',
      name: 'Entertainment',
      monthlyAmount: '400',
      isEssential: false,
      notes: 'Movies, events, subscriptions',
    },
    {
      id: 'travel',
      name: 'Travel',
      monthlyAmount: '500',
      isEssential: false,
      notes: 'Vacations, trips to visit family',
    },
    {
      id: 'gifts',
      name: 'Gifts & Donations',
      monthlyAmount: '200',
      isEssential: false,
      notes: 'Charitable giving, gifts to family',
    },
    {
      id: 'personal',
      name: 'Personal Care',
      monthlyAmount: '150',
      isEssential: false,
      notes: 'Haircuts, gym, clothing',
    },
    {
      id: 'other',
      name: 'Other',
      monthlyAmount: '200',
      isEssential: false,
      notes: 'Miscellaneous expenses',
    },
  ];

  // Initialize form data from context or with defaults
  const [formData, setFormData] = useState<RetirementExpensesData>(
    (data.east?.retirementExpenses as RetirementExpensesData) || {
      expenseCategories: defaultExpenseCategories,
      housingPlan: 'stay_current',
      healthcareCosts: '800',
      travelBudget: '500',
      hobbiesBudget: '300',
      inflationAssumption: '2.5',
    }
  );

  // New expense category form
  const [newCategory, setNewCategory] = useState<Omit<ExpenseCategory, 'id'>>({
    name: '',
    monthlyAmount: '',
    isEssential: false,
    notes: '',
  });

  // Handle form field changes
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle expense category changes
  const handleCategoryChange = (
    id: string,
    field: keyof ExpenseCategory,
    value: string | boolean
  ) => {
    setFormData((prev) => ({
      ...prev,
      expenseCategories: prev.expenseCategories.map((category) =>
        category.id === id ? { ...category, [field]: value } : category
      ),
    }));
  };

  // Handle new category field changes
  const handleNewCategoryChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target;
    // Handle checkbox separately
    if (type === 'checkbox') {
      const isChecked = (e.target as HTMLInputElement).checked;
      setNewCategory((prev) => ({
        ...prev,
        [name]: isChecked,
      }));
    } else {
      setNewCategory((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  // Add new expense category
  const handleAddCategory = () => {
    if (!newCategory.name || !newCategory.monthlyAmount) return;

    const newCat: ExpenseCategory = {
      ...newCategory,
      id: `expense-${Date.now()}`,
    };

    setFormData((prev) => ({
      ...prev,
      expenseCategories: [...prev.expenseCategories, newCat],
    }));

    // Reset new category form
    setNewCategory({
      name: '',
      monthlyAmount: '',
      isEssential: false,
      notes: '',
    });
  };

  // Remove expense category
  const handleRemoveCategory = (id: string) => {
    setFormData((prev) => ({
      ...prev,
      expenseCategories: prev.expenseCategories.filter((category) => category.id !== id),
    }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSave();

    if (onComplete) {
      onComplete();
    }
  };

  // Handle saving data
  const handleSave = () => {
    setIsSaving(true);

    // Calculate total monthly expenses
    const monthlyExpenses = calculateTotalMonthlyExpenses();

    // Update data in context
    updateData('east', 'retirementExpenses', {
      ...formData,
      monthlyExpenses,
    });

    // Show save indicator
    setShowSaveIndicator(true);

    // Hide save indicator after 2 seconds
    setTimeout(() => {
      setIsSaving(false);
      setShowSaveIndicator(false);
    }, 2000);
  };

  // Calculate total monthly expenses
  const calculateTotalMonthlyExpenses = () => {
    return formData.expenseCategories.reduce((total, category) => {
      return total + (parseFloat(category.monthlyAmount) || 0);
    }, 0);
  };

  // Calculate total annual expenses
  const calculateTotalAnnualExpenses = () => {
    return calculateTotalMonthlyExpenses() * 12;
  };

  // Calculate essential expenses
  const calculateEssentialExpenses = () => {
    return formData.expenseCategories
      .filter((category) => category.isEssential)
      .reduce((total, category) => {
        return total + (parseFloat(category.monthlyAmount) || 0);
      }, 0);
  };

  // Calculate discretionary expenses
  const calculateDiscretionaryExpenses = () => {
    return formData.expenseCategories
      .filter((category) => !category.isEssential)
      .reduce((total, category) => {
        return total + (parseFloat(category.monthlyAmount) || 0);
      }, 0);
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Retirement Expenses</Title>
        <Description theme={theme}>Estimate your monthly expenses during retirement.</Description>
      </Header>

      <form onSubmit={handleSubmit}>
        <FormSection>
          <SectionTitle>Expense Categories</SectionTitle>

          <ExpenseCategoriesTable theme={theme}>
            <TableHeader theme={theme}>
              <HeaderCell width="30%">Category</HeaderCell>
              <HeaderCell width="20%">Monthly Amount</HeaderCell>
              <HeaderCell width="15%">Essential?</HeaderCell>
              <HeaderCell width="25%">Notes</HeaderCell>
              <HeaderCell width="10%">Actions</HeaderCell>
            </TableHeader>

            <TableBody>
              {formData.expenseCategories.map((category) => (
                <TableRow key={category.id} theme={theme}>
                  <TableCell>
                    <Input
                      type="text"
                      value={category.name}
                      onChange={(e) => handleCategoryChange(category.id, 'name', e.target.value)}
                      required
                    />
                  </TableCell>
                  <TableCell>
                    <Input
                      type="number"
                      value={category.monthlyAmount}
                      onChange={(e) =>
                        handleCategoryChange(category.id, 'monthlyAmount', e.target.value)
                      }
                      min="0"
                      step="10"
                      required
                    />
                  </TableCell>
                  <TableCell>
                    <Checkbox
                      type="checkbox"
                      checked={category.isEssential}
                      onChange={(e) =>
                        handleCategoryChange(category.id, 'isEssential', e.target.checked)
                      }
                    />
                  </TableCell>
                  <TableCell>
                    <Input
                      type="text"
                      value={category.notes}
                      onChange={(e) => handleCategoryChange(category.id, 'notes', e.target.value)}
                    />
                  </TableCell>
                  <TableCell>
                    <RemoveButton onClick={() => handleRemoveCategory(category.id)} theme={theme}>
                      Remove
                    </RemoveButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </ExpenseCategoriesTable>

          <AddCategoryForm theme={theme}>
            <AddCategoryTitle>Add Expense Category</AddCategoryTitle>

            <FormRow>
              <FormField>
                <Label htmlFor="name">Category Name</Label>
                <Input
                  type="text"
                  id="name"
                  name="name"
                  value={newCategory.name}
                  onChange={handleNewCategoryChange}
                  placeholder="e.g., Hobbies"
                />
              </FormField>

              <FormField>
                <Label htmlFor="monthlyAmount">Monthly Amount</Label>
                <Input
                  type="number"
                  id="monthlyAmount"
                  name="monthlyAmount"
                  value={newCategory.monthlyAmount}
                  onChange={handleNewCategoryChange}
                  min="0"
                  step="10"
                  placeholder="e.g., 200"
                />
              </FormField>

              <FormField>
                <CheckboxField>
                  <Checkbox
                    type="checkbox"
                    id="isEssential"
                    name="isEssential"
                    checked={newCategory.isEssential}
                    onChange={handleNewCategoryChange}
                  />
                  <CheckboxLabel htmlFor="isEssential">Essential Expense</CheckboxLabel>
                </CheckboxField>
              </FormField>
            </FormRow>

            <FormField>
              <Label htmlFor="notes">Notes (optional)</Label>
              <Textarea
                id="notes"
                name="notes"
                value={newCategory.notes}
                onChange={handleNewCategoryChange}
                placeholder="Additional details about this expense category"
              />
            </FormField>

            <AddButton
              type="button"
              onClick={handleAddCategory}
              disabled={!newCategory.name || !newCategory.monthlyAmount}
              theme={theme}
            >
              Add Category
            </AddButton>
          </AddCategoryForm>
        </FormSection>

        <FormSection>
          <SectionTitle>Housing Plans</SectionTitle>
          <FormRow>
            <FormField>
              <Label htmlFor="housingPlan">Retirement Housing Plan</Label>
              <Select
                id="housingPlan"
                name="housingPlan"
                value={formData.housingPlan}
                onChange={handleChange}
                required
              >
                <option value="stay_current">Stay in current home</option>
                <option value="downsize">Downsize to smaller home</option>
                <option value="relocate">Relocate to different area</option>
                <option value="retirement_community">Move to retirement community</option>
              </Select>
            </FormField>
          </FormRow>
        </FormSection>

        <FormSection>
          <SectionTitle>Additional Considerations</SectionTitle>
          <FormRow>
            <FormField>
              <Label htmlFor="healthcareCosts">Healthcare Costs (monthly)</Label>
              <Input
                type="number"
                id="healthcareCosts"
                name="healthcareCosts"
                value={formData.healthcareCosts}
                onChange={handleChange}
                min="0"
                step="10"
                required
              />
              <FieldHint>
                Include insurance premiums, out-of-pocket expenses, prescriptions
              </FieldHint>
            </FormField>

            <FormField>
              <Label htmlFor="travelBudget">Travel Budget (monthly)</Label>
              <Input
                type="number"
                id="travelBudget"
                name="travelBudget"
                value={formData.travelBudget}
                onChange={handleChange}
                min="0"
                step="10"
                required
              />
              <FieldHint>Average monthly amount for travel and vacations</FieldHint>
            </FormField>

            <FormField>
              <Label htmlFor="hobbiesBudget">Hobbies & Recreation (monthly)</Label>
              <Input
                type="number"
                id="hobbiesBudget"
                name="hobbiesBudget"
                value={formData.hobbiesBudget}
                onChange={handleChange}
                min="0"
                step="10"
                required
              />
              <FieldHint>Budget for leisure activities and hobbies</FieldHint>
            </FormField>
          </FormRow>

          <FormRow>
            <FormField>
              <Label htmlFor="inflationAssumption">Inflation Assumption (%)</Label>
              <Input
                type="number"
                id="inflationAssumption"
                name="inflationAssumption"
                value={formData.inflationAssumption}
                onChange={handleChange}
                min="0"
                max="10"
                step="0.1"
                required
              />
              <FieldHint>Expected annual inflation rate for retirement planning</FieldHint>
            </FormField>
          </FormRow>
        </FormSection>

        <FormSection>
          <SectionTitle>Expense Summary</SectionTitle>

          <SummaryContainer theme={theme}>
            <SummaryRow>
              <SummaryLabel>Essential Monthly Expenses:</SummaryLabel>
              <SummaryValue>{formatCurrency(calculateEssentialExpenses())}</SummaryValue>
            </SummaryRow>

            <SummaryRow>
              <SummaryLabel>Discretionary Monthly Expenses:</SummaryLabel>
              <SummaryValue>{formatCurrency(calculateDiscretionaryExpenses())}</SummaryValue>
            </SummaryRow>

            <SummaryRow>
              <SummaryLabel>Total Monthly Expenses:</SummaryLabel>
              <SummaryValue>{formatCurrency(calculateTotalMonthlyExpenses())}</SummaryValue>
            </SummaryRow>

            <SummaryRow>
              <SummaryLabel>Total Annual Expenses:</SummaryLabel>
              <SummaryValue highlight>
                {formatCurrency(calculateTotalAnnualExpenses())}
              </SummaryValue>
            </SummaryRow>
          </SummaryContainer>
        </FormSection>

        <ButtonContainer>
          {onBack && (
            <BackButton type="button" onClick={onBack} theme={theme}>
              Back
            </BackButton>
          )}

          <SaveIndicator visible={showSaveIndicator}>✓ Saved</SaveIndicator>

          <SubmitButton type="submit" theme={theme}>
            {isSaving ? 'Saving...' : 'Save & Continue'}
          </SubmitButton>
        </ButtonContainer>
      </form>
    </Container>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  padding: 24px;
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const Title = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const Description = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const FormSection = styled.div`
  margin-bottom: 32px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;

const FormField = styled.div`
  margin-bottom: 16px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
`;

const Input = styled.input`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
`;

const Select = styled.select`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
`;

const Textarea = styled.textarea`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  min-height: 80px;
  resize: vertical;
`;

const FieldHint = styled.div`
  font-size: 0.8rem;
  color: #666;
  margin-top: 4px;
`;

const CheckboxField = styled.div`
  display: flex;
  align-items: center;
  margin-top: 24px;
`;

const Checkbox = styled.input`
  margin-right: 8px;
`;

const CheckboxLabel = styled.label`
  font-size: 1rem;
`;

const ExpenseCategoriesTable = styled.div<{ theme: any }>`
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 24px;
`;

const TableHeader = styled.div<{ theme: any }>`
  display: flex;
  background-color: ${(props) => props.theme.colors.background.default};
  padding: 12px;
  font-weight: 600;
`;

const HeaderCell = styled.div<{ width: string }>`
  width: ${(props) => props.width};
  padding: 0 8px;
`;

const TableBody = styled.div`
  max-height: 400px;
  overflow-y: auto;
`;

const TableRow = styled.div<{ theme: any }>`
  display: flex;
  padding: 8px 12px;
  border-top: 1px solid ${(props) => props.theme.colors.border};

  &:nth-child(even) {
    background-color: ${(props) => props.theme.colors.background.paper};
  }

  &:nth-child(odd) {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const TableCell = styled.div`
  padding: 0 8px;
  display: flex;
  align-items: center;

  &:nth-child(1) {
    width: 30%;
  }

  &:nth-child(2) {
    width: 20%;
  }

  &:nth-child(3) {
    width: 15%;
    justify-content: center;
  }

  &:nth-child(4) {
    width: 25%;
  }

  &:nth-child(5) {
    width: 10%;
    justify-content: center;
  }
`;

const RemoveButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.error.main};
  border: none;
  font-size: 0.9rem;
  cursor: pointer;
  padding: 4px 8px;

  &:hover {
    text-decoration: underline;
  }
`;

const AddCategoryForm = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 4px;
  padding: 16px;
`;

const AddCategoryTitle = styled.h4`
  margin: 0 0 16px 0;
  font-size: 1.1rem;
`;

const AddButton = styled.button<{ theme: any; disabled?: boolean }>`
  background-color: ${(props) =>
    props.disabled
      ? props.theme.colors.action.disabledBackground
      : props.theme.colors.primary.main};
  color: ${(props) =>
    props.disabled ? props.theme.colors.action.disabled : props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) =>
      props.disabled
        ? props.theme.colors.action.disabledBackground
        : props.theme.colors.primary.dark};
  }
`;

const SummaryContainer = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  padding: 16px;
  border-radius: 4px;
`;

const SummaryRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
    padding-top: 8px;
    border-top: 1px solid #ddd;
  }
`;

const SummaryLabel = styled.div`
  font-weight: 500;
`;

const SummaryValue = styled.div<{ highlight?: boolean }>`
  font-weight: ${(props) => (props.highlight ? 'bold' : 'normal')};
  font-size: ${(props) => (props.highlight ? '1.2rem' : '1rem')};
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
`;

const BackButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.text.secondary};
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const SubmitButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 24px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  color: green;
  margin-right: 16px;
`;

export default RetirementExpenses;
