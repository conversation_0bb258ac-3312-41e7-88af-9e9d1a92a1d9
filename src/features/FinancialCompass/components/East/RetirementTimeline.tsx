/**
 * Retirement Timeline Component
 *
 * This component visualizes the user's retirement timeline as part of the
 * East direction of the Financial Compass ("Where You're Going").
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { formatCurrency } from '../../../../utils/formatters';

interface RetirementTimelineProps {
  onComplete?: () => void;
  onBack?: () => void;
}

interface TimelineEvent {
  id: string;
  age: number;
  year: number;
  event: string;
  description: string;
  type: 'milestone' | 'income' | 'expense' | 'other';
}

const RetirementTimeline: React.FC<RetirementTimelineProps> = ({ onComplete, onBack }) => {
  const { theme } = useTheme();
  const { data, updateData } = useFinancialCompass();

  const [isSaving, setIsSaving] = useState(false);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Get retirement data from context
  const retirementGoals = data.east?.retirementGoals || {};
  const currentAge = parseInt((retirementGoals as any)?.currentAge) || 30;
  const retirementAge = parseInt((retirementGoals as any)?.retirementAge) || 65;
  const lifeExpectancy = parseInt((retirementGoals as any)?.lifeExpectancy) || 90;
  const currentYear = new Date().getFullYear();

  // Calculate key years
  const retirementYear = currentYear + (retirementAge - currentAge);
  const endYear = currentYear + (lifeExpectancy - currentAge);

  // Generate timeline events
  const generateDefaultEvents = (): TimelineEvent[] => {
    const events: TimelineEvent[] = [];

    // Add current age milestone
    events.push({
      id: 'current',
      age: currentAge,
      year: currentYear,
      event: 'Current Age',
      description: 'Your current position in your financial journey',
      type: 'milestone',
    });

    // Add Social Security milestone if applicable
    const retirementIncome = data.east?.retirementIncome || {};
    const socialSecurity = (retirementIncome as any)?.socialSecurity || {};
    const ssStartAge = parseInt(socialSecurity?.startAge) || 67;
    if (ssStartAge >= currentAge) {
      events.push({
        id: 'social-security',
        age: ssStartAge,
        year: currentYear + (ssStartAge - currentAge),
        event: 'Social Security Benefits Begin',
        description: `Monthly benefit: ${formatCurrency(socialSecurity?.estimatedMonthlyBenefit || '0')}`,
        type: 'income',
      });
    }

    // Add pension milestone if applicable
    const pension = (retirementIncome as any)?.pension || {};
    if (pension?.hasEmployerPension) {
      const pensionStartAge = parseInt(pension?.startAge) || 65;
      if (pensionStartAge >= currentAge) {
        events.push({
          id: 'pension',
          age: pensionStartAge,
          year: currentYear + (pensionStartAge - currentAge),
          event: 'Pension Benefits Begin',
          description: `Monthly benefit: ${formatCurrency(pension?.estimatedMonthlyBenefit || '0')}`,
          type: 'income',
        });
      }
    }

    // Add retirement milestone
    events.push({
      id: 'retirement',
      age: retirementAge,
      year: retirementYear,
      event: 'Retirement',
      description: 'Your planned retirement date',
      type: 'milestone',
    });

    // Add Required Minimum Distributions milestone
    const rmdAge = 72;
    if (rmdAge > currentAge) {
      events.push({
        id: 'rmd',
        age: rmdAge,
        year: currentYear + (rmdAge - currentAge),
        event: 'Required Minimum Distributions Begin',
        description: 'You must start taking distributions from tax-advantaged retirement accounts',
        type: 'milestone',
      });
    }

    // Add Medicare milestone
    const medicareAge = 65;
    if (medicareAge > currentAge) {
      events.push({
        id: 'medicare',
        age: medicareAge,
        year: currentYear + (medicareAge - currentAge),
        event: 'Medicare Eligibility',
        description: 'You become eligible for Medicare health coverage',
        type: 'milestone',
      });
    }

    // Add life expectancy milestone
    events.push({
      id: 'life-expectancy',
      age: lifeExpectancy,
      year: endYear,
      event: 'Life Expectancy',
      description: 'Your estimated life expectancy for planning purposes',
      type: 'milestone',
    });

    // Sort events by age
    return events.sort((a, b) => a.age - b.age);
  };

  // Initialize timeline events from context or with defaults
  const [timelineEvents, setTimelineEvents] = useState<TimelineEvent[]>(
    ((data.east?.retirementTimeline as any)?.events as TimelineEvent[]) || generateDefaultEvents()
  );

  // New event form
  const [newEvent, setNewEvent] = useState<Omit<TimelineEvent, 'id'>>({
    age: retirementAge,
    year: retirementYear,
    event: '',
    description: '',
    type: 'milestone',
  });

  // Update year when age changes and vice versa
  useEffect(() => {
    if (newEvent.age !== retirementAge) {
      setNewEvent((prev) => ({
        ...prev,
        year: currentYear + (prev.age - currentAge),
      }));
    }
  }, [newEvent.age, currentAge, currentYear, retirementAge]);

  useEffect(() => {
    if (newEvent.year !== retirementYear) {
      setNewEvent((prev) => ({
        ...prev,
        age: currentAge + (prev.year - currentYear),
      }));
    }
  }, [newEvent.year, currentAge, currentYear, retirementYear]);

  // Handle new event field changes
  const handleNewEventChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setNewEvent((prev) => ({
      ...prev,
      [name]: name === 'age' || name === 'year' ? parseInt(value) : value,
    }));
  };

  // Add new event
  const handleAddEvent = () => {
    if (!newEvent.event) return;

    const newTimelineEvent: TimelineEvent = {
      ...newEvent,
      id: `event-${Date.now()}`,
    };

    setTimelineEvents((prev) => {
      const updated = [...prev, newTimelineEvent].sort((a, b) => a.age - b.age);
      return updated;
    });

    // Reset new event form
    setNewEvent({
      age: retirementAge,
      year: retirementYear,
      event: '',
      description: '',
      type: 'milestone',
    });
  };

  // Remove event
  const handleRemoveEvent = (id: string) => {
    setTimelineEvents((prev) => prev.filter((event) => event.id !== id));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSave();

    if (onComplete) {
      onComplete();
    }
  };

  // Handle saving data
  const handleSave = () => {
    setIsSaving(true);

    // Update data in context
    updateData('east', 'retirementTimeline', {
      events: timelineEvents,
      currentAge,
      retirementAge,
      lifeExpectancy,
      currentYear,
      retirementYear,
      endYear,
    });

    // Show save indicator
    setShowSaveIndicator(true);

    // Hide save indicator after 2 seconds
    setTimeout(() => {
      setIsSaving(false);
      setShowSaveIndicator(false);
    }, 2000);
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Retirement Timeline</Title>
        <Description theme={theme}>
          Visualize your retirement journey with key milestones and events.
        </Description>
      </Header>

      <form onSubmit={handleSubmit}>
        <FormSection>
          <SectionTitle>Your Retirement Timeline</SectionTitle>

          <TimelineContainer theme={theme}>
            <TimelineHeader theme={theme}>
              <TimelineYears>
                <YearMarker>Current ({currentYear})</YearMarker>
                <YearMarker>Retirement ({retirementYear})</YearMarker>
                <YearMarker>End ({endYear})</YearMarker>
              </TimelineYears>

              <TimelineLine theme={theme}>
                <TimelineProgress progress={0} color={theme.colors.primary.main} theme={theme} />

                <TimelineMarker position={0} type="current" theme={theme}>
                  <MarkerLabel position="bottom">Now</MarkerLabel>
                </TimelineMarker>

                <TimelineMarker
                  position={((retirementYear - currentYear) / (endYear - currentYear)) * 100}
                  type="retirement"
                  theme={theme}
                >
                  <MarkerLabel position="top">Retirement</MarkerLabel>
                </TimelineMarker>

                <TimelineMarker position={100} type="end" theme={theme}>
                  <MarkerLabel position="bottom">Life Expectancy</MarkerLabel>
                </TimelineMarker>
              </TimelineLine>
            </TimelineHeader>

            <TimelineEvents>
              {timelineEvents.map((event) => {
                const position = Math.min(
                  100,
                  Math.max(0, ((event.year - currentYear) / (endYear - currentYear)) * 100)
                );

                return (
                  <TimelineEvent key={event.id} position={position} type={event.type} theme={theme}>
                    <EventContent theme={theme}>
                      <EventHeader>
                        <EventTitle>{event.event}</EventTitle>
                        <EventAge>
                          Age {event.age} ({event.year})
                        </EventAge>
                      </EventHeader>

                      <EventDescription>{event.description}</EventDescription>

                      <EventActions>
                        <RemoveButton onClick={() => handleRemoveEvent(event.id)} theme={theme}>
                          Remove
                        </RemoveButton>
                      </EventActions>
                    </EventContent>
                  </TimelineEvent>
                );
              })}
            </TimelineEvents>
          </TimelineContainer>
        </FormSection>

        <FormSection>
          <SectionTitle>Add Timeline Event</SectionTitle>

          <AddEventForm theme={theme}>
            <FormRow>
              <FormField>
                <Label htmlFor="event">Event Name</Label>
                <Input
                  type="text"
                  id="event"
                  name="event"
                  value={newEvent.event}
                  onChange={handleNewEventChange}
                  placeholder="e.g., Downsize Home"
                  required
                />
              </FormField>

              <FormField>
                <Label htmlFor="type">Event Type</Label>
                <Select id="type" name="type" value={newEvent.type} onChange={handleNewEventChange}>
                  <option value="milestone">Milestone</option>
                  <option value="income">Income</option>
                  <option value="expense">Expense</option>
                  <option value="other">Other</option>
                </Select>
              </FormField>
            </FormRow>

            <FormRow>
              <FormField>
                <Label htmlFor="age">Age</Label>
                <Input
                  type="number"
                  id="age"
                  name="age"
                  value={newEvent.age}
                  onChange={handleNewEventChange}
                  min={currentAge}
                  max={lifeExpectancy}
                  required
                />
              </FormField>

              <FormField>
                <Label htmlFor="year">Year</Label>
                <Input
                  type="number"
                  id="year"
                  name="year"
                  value={newEvent.year}
                  onChange={handleNewEventChange}
                  min={currentYear}
                  max={endYear}
                  required
                />
              </FormField>
            </FormRow>

            <FormField>
              <Label htmlFor="description">Description (optional)</Label>
              <Textarea
                id="description"
                name="description"
                value={newEvent.description}
                onChange={handleNewEventChange}
                placeholder="Additional details about this event"
              />
            </FormField>

            <AddButton
              type="button"
              onClick={handleAddEvent}
              disabled={!newEvent.event}
              theme={theme}
            >
              Add Event
            </AddButton>
          </AddEventForm>
        </FormSection>

        <ButtonContainer>
          {onBack && (
            <BackButton type="button" onClick={onBack} theme={theme}>
              Back
            </BackButton>
          )}

          <SaveIndicator visible={showSaveIndicator}>✓ Saved</SaveIndicator>

          <SubmitButton type="submit" theme={theme}>
            {isSaving ? 'Saving...' : 'Save & Continue'}
          </SubmitButton>
        </ButtonContainer>
      </form>
    </Container>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  padding: 24px;
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const Title = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const Description = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const FormSection = styled.div`
  margin-bottom: 32px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const TimelineContainer = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
`;

const TimelineHeader = styled.div<{ theme: any }>`
  margin-bottom: 40px;
`;

const TimelineYears = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
`;

const YearMarker = styled.div`
  font-size: 0.9rem;
  font-weight: 500;
`;

const TimelineLine = styled.div<{ theme: any }>`
  position: relative;
  height: 8px;
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 4px;
`;

const TimelineProgress = styled.div<{ progress: number; color: string; theme: any }>`
  position: absolute;
  height: 100%;
  width: ${(props) => props.progress}%;
  background-color: ${(props) => props.color};
  border-radius: 4px;
`;

const TimelineMarker = styled.div<{ position: number; type: string; theme: any }>`
  position: absolute;
  top: 50%;
  left: ${(props) => props.position}%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: ${(props) =>
    props.type === 'current'
      ? props.theme.colors.primary.main
      : props.type === 'retirement'
        ? props.theme.colors.secondary.main
        : props.theme.colors.text.disabled};
  border: 2px solid ${(props) => props.theme.colors.background.paper};
  z-index: 2;
`;

const MarkerLabel = styled.div<{ position: 'top' | 'bottom' }>`
  position: absolute;
  ${(props) => (props.position === 'top' ? 'bottom: 24px;' : 'top: 24px;')}
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
  font-size: 0.8rem;
  font-weight: 500;
`;

const TimelineEvents = styled.div`
  position: relative;
  min-height: 200px;
  margin-top: 60px;
`;

const TimelineEvent = styled.div<{ position: number; type: string; theme: any }>`
  position: absolute;
  top: 0;
  left: ${(props) => props.position}%;
  transform: translateX(-50%);
  margin-bottom: 16px;

  &:nth-child(even) {
    top: 120px;
  }
`;

const EventContent = styled.div<{ theme: any }>`
  width: 200px;
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 4px;
  padding: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const EventHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
`;

const EventTitle = styled.div`
  font-weight: 600;
  font-size: 0.9rem;
`;

const EventAge = styled.div`
  font-size: 0.8rem;
  color: #666;
`;

const EventDescription = styled.div`
  font-size: 0.8rem;
  margin-bottom: 8px;
`;

const EventActions = styled.div`
  display: flex;
  justify-content: flex-end;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;

const FormField = styled.div`
  margin-bottom: 16px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
`;

const Input = styled.input`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
`;

const Select = styled.select`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
`;

const Textarea = styled.textarea`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  min-height: 80px;
  resize: vertical;
`;

const AddEventForm = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 4px;
  padding: 16px;
`;

const AddButton = styled.button<{ theme: any; disabled?: boolean }>`
  background-color: ${(props) =>
    props.disabled
      ? props.theme.colors.action.disabledBackground
      : props.theme.colors.primary.main};
  color: ${(props) =>
    props.disabled ? props.theme.colors.action.disabled : props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) =>
      props.disabled
        ? props.theme.colors.action.disabledBackground
        : props.theme.colors.primary.dark};
  }
`;

const RemoveButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.error.main};
  border: none;
  font-size: 0.8rem;
  cursor: pointer;
  padding: 4px 8px;

  &:hover {
    text-decoration: underline;
  }
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
`;

const BackButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.text.secondary};
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const SubmitButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 24px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  color: green;
  margin-right: 16px;
`;

export default RetirementTimeline;
