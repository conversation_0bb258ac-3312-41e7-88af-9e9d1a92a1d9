import React from 'react';
import { Alert, Box } from '@mui/material';

interface CrossFormValidationBannerProps {
  errors: Record<string, string>;
}

const CrossFormValidationBanner: React.FC<CrossFormValidationBannerProps> = ({ errors }) => {
  if (!errors || Object.keys(errors).length === 0) return null;
  return (
    <Box mb={2}>
      {Object.entries(errors).map(([field, message]) => (
        <Alert key={field} severity="warning" sx={{ mb: 1 }}>
          {message}
        </Alert>
      ))}
    </Box>
  );
};

export default CrossFormValidationBanner;
