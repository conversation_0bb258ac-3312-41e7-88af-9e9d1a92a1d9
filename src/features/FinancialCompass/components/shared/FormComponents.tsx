import styled from 'styled-components';
import React from 'react';

export const FormContainer = styled.div`
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
`;

export const FormTitle = styled.h2`
  font-size: 1.5rem;
  margin-bottom: 1rem;
`;

export const FormDescription = styled.p`
  margin-bottom: 1.5rem;
`;

export const FormSection = styled.div`
  margin-bottom: 2rem;
`;

export const SectionTitle = styled.h3`
  font-size: 1.25rem;
  margin-bottom: 1rem;
`;

export const FormRow = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
`;

export const FormGroup = styled.div`
  flex: 1;
`;

export const Label = styled.label`
  display: block;
  margin-bottom: 0.5rem;
`;

export const Input = styled.input`
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
`;

export const Select = styled.select`
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
`;

export const RequiredFieldsNote = styled.p`
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.5rem;
`;

export const OptionalFieldsNote = styled.p`
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.5rem;
  font-style: italic;
`;
