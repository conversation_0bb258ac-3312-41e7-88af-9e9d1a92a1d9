/**
 * Shared Summary Components
 *
 * This file contains reusable components for summary sections across all compass directions.
 * These components provide consistent UI for trends, alerts, actions, and guidance.
 */

import React from 'react';
import styled from 'styled-components';

// Types
export interface TrendItem {
  id: string;
  label: string;
  value: string | number;
  change: number; // Percentage change
  direction: 'up' | 'down' | 'neutral';
  isPositive: boolean; // Whether the trend is positive (up can be good or bad depending on context)
}

export interface AlertItem {
  id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  icon?: string;
}

export interface ActionItem {
  id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  icon?: string;
  completed?: boolean;
}

export interface GuidanceItem {
  id: string;
  title: string;
  description: string;
  icon?: string;
}

// Props for components
interface TrendsSectionProps {
  title?: string;
  trends: TrendItem[];
  theme: any;
}

interface AlertsSectionProps {
  title?: string;
  alerts: AlertItem[];
  theme: any;
}

interface ActionsSectionProps {
  title?: string;
  actions: ActionItem[];
  theme: any;
  onActionComplete?: (id: string) => void;
}

interface GuidanceSectionProps {
  title?: string;
  guidanceItems: GuidanceItem[];
  theme: any;
}

// Trends Section Component
export const TrendsSection: React.FC<TrendsSectionProps> = ({
  title = 'Trends',
  trends,
  theme,
}) => {
  if (trends.length === 0) return null;

  return (
    <SectionContainer theme={theme}>
      <SectionTitle theme={theme}>{title}</SectionTitle>
      <TrendsList>
        {trends.map((trend) => (
          <TrendItem key={trend.id} theme={theme}>
            <TrendLabel>{trend.label}</TrendLabel>
            <TrendValueContainer>
              <TrendValue>{trend.value}</TrendValue>
              <TrendChange isPositive={trend.isPositive} theme={theme}>
                {trend.direction === 'up' && '↑'}
                {trend.direction === 'down' && '↓'}
                {trend.direction === 'neutral' && '→'} {Math.abs(trend.change)}%
              </TrendChange>
            </TrendValueContainer>
          </TrendItem>
        ))}
      </TrendsList>
    </SectionContainer>
  );
};

// Alerts Section Component
export const AlertsSection: React.FC<AlertsSectionProps> = ({
  title = 'Alerts',
  alerts,
  theme,
}) => {
  if (alerts.length === 0) return null;

  const getAlertIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return '🚨';
      case 'high':
        return '⚠️';
      case 'medium':
        return '⚠';
      case 'low':
        return 'ℹ️';
      default:
        return 'ℹ️';
    }
  };

  return (
    <SectionContainer theme={theme}>
      <SectionTitle theme={theme}>{title}</SectionTitle>
      <AlertsList>
        {alerts.map((alert) => (
          <AlertItem key={alert.id} severity={alert.severity} theme={theme}>
            <AlertIcon>{alert.icon || getAlertIcon(alert.severity)}</AlertIcon>
            <AlertContent>
              <AlertTitle severity={alert.severity}>{alert.title}</AlertTitle>
              <AlertDescription>{alert.description}</AlertDescription>
            </AlertContent>
          </AlertItem>
        ))}
      </AlertsList>
    </SectionContainer>
  );
};

// Actions Section Component
export const ActionsSection: React.FC<ActionsSectionProps> = ({
  title = 'Recommended Actions',
  actions,
  theme,
  onActionComplete,
}) => {
  if (actions.length === 0) return null;

  const getActionIcon = (priority: string, icon?: string) => {
    if (icon) return icon;
    switch (priority) {
      case 'high':
        return '🔥';
      case 'medium':
        return '⚡';
      case 'low':
        return '✓';
      default:
        return '✓';
    }
  };

  return (
    <SectionContainer theme={theme}>
      <SectionTitle theme={theme}>{title}</SectionTitle>
      <ActionsList>
        {actions.map((action) => (
          <ActionItem
            key={action.id}
            priority={action.priority}
            theme={theme}
            completed={action.completed}
          >
            <ActionIcon>{getActionIcon(action.priority, action.icon)}</ActionIcon>
            <ActionContent>
              <ActionTitle>{action.title}</ActionTitle>
              <ActionDescription>{action.description}</ActionDescription>
            </ActionContent>
            {onActionComplete && (
              <ActionCheckbox
                type="checkbox"
                checked={action.completed}
                onChange={() => onActionComplete(action.id)}
              />
            )}
          </ActionItem>
        ))}
      </ActionsList>
    </SectionContainer>
  );
};

// Guidance Section Component
export const GuidanceSection: React.FC<GuidanceSectionProps> = ({
  title = 'Guidance & Insights',
  guidanceItems,
  theme,
}) => {
  if (guidanceItems.length === 0) return null;

  return (
    <SectionContainer theme={theme}>
      <SectionTitle theme={theme}>{title}</SectionTitle>
      <GuidanceList>
        {guidanceItems.map((item) => (
          <GuidanceItem key={item.id} theme={theme}>
            <GuidanceIcon>{item.icon || '💡'}</GuidanceIcon>
            <GuidanceContent>
              <GuidanceTitle>{item.title}</GuidanceTitle>
              <GuidanceDescription>{item.description}</GuidanceDescription>
            </GuidanceContent>
          </GuidanceItem>
        ))}
      </GuidanceList>
    </SectionContainer>
  );
};

// Styled Components
const SectionContainer = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const SectionTitle = styled.h3<{ theme: any }>`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: ${(props) => props.theme.colors.text.primary};
`;

// Trends Styled Components
const TrendsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const TrendItem = styled.div<{ theme: any }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: ${(props) => props.theme.colors.background.main};
  border-radius: 6px;
`;

const TrendLabel = styled.div`
  font-weight: 500;
`;

const TrendValueContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const TrendValue = styled.div`
  font-weight: 600;
`;

const TrendChange = styled.div<{ isPositive: boolean; theme: any }>`
  font-weight: 500;
  color: ${(props) =>
    props.isPositive ? props.theme.colors.success.main : props.theme.colors.error.main};
  padding: 2px 6px;
  border-radius: 4px;
  background-color: ${(props) =>
    props.isPositive ? props.theme.colors.success.light : props.theme.colors.error.light};
  font-size: 0.85rem;
`;

// Alerts Styled Components
const AlertsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const AlertItem = styled.div<{ severity: string; theme: any }>`
  display: flex;
  gap: 12px;
  padding: 12px;
  background-color: ${(props) => {
    switch (props.severity) {
      case 'critical':
        return props.theme.colors.error.light;
      case 'high':
        return props.theme.colors.warning.light;
      case 'medium':
        return props.theme.colors.info.light;
      case 'low':
        return props.theme.colors.background.main;
      default:
        return props.theme.colors.background.main;
    }
  }};
  border-radius: 6px;
  border-left: 4px solid
    ${(props) => {
      switch (props.severity) {
        case 'critical':
          return props.theme.colors.error.main;
        case 'high':
          return props.theme.colors.warning.main;
        case 'medium':
          return props.theme.colors.info.main;
        case 'low':
          return props.theme.colors.text.secondary;
        default:
          return props.theme.colors.text.secondary;
      }
    }};
`;

const AlertIcon = styled.div`
  font-size: 1.5rem;
  flex-shrink: 0;
`;

const AlertContent = styled.div`
  flex: 1;
`;

const AlertTitle = styled.div<{ severity: string }>`
  font-weight: 600;
  margin-bottom: 4px;
`;

const AlertDescription = styled.div`
  font-size: 0.9rem;
  line-height: 1.4;
`;

// Actions Styled Components
const ActionsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const ActionItem = styled.div<{ priority: string; theme: any; completed?: boolean }>`
  display: flex;
  gap: 12px;
  padding: 12px;
  background-color: ${(props) =>
    props.completed ? props.theme.colors.success.light : props.theme.colors.background.main};
  border-radius: 6px;
  opacity: ${(props) => (props.completed ? 0.7 : 1)};
  border-left: 4px solid
    ${(props) => {
      if (props.completed) return props.theme.colors.success.main;
      switch (props.priority) {
        case 'high':
          return props.theme.colors.error.main;
        case 'medium':
          return props.theme.colors.warning.main;
        case 'low':
          return props.theme.colors.info.main;
        default:
          return props.theme.colors.info.main;
      }
    }};
`;

const ActionIcon = styled.div`
  font-size: 1.5rem;
  flex-shrink: 0;
`;

const ActionContent = styled.div`
  flex: 1;
`;

const ActionTitle = styled.div`
  font-weight: 600;
  margin-bottom: 4px;
`;

const ActionDescription = styled.div`
  font-size: 0.9rem;
  line-height: 1.4;
`;

const ActionCheckbox = styled.input`
  width: 20px;
  height: 20px;
  cursor: pointer;
`;

// Guidance Styled Components
const GuidanceList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const GuidanceItem = styled.div<{ theme: any }>`
  display: flex;
  gap: 12px;
  padding: 16px;
  background-color: ${(props) => props.theme.colors.background.main};
  border-radius: 6px;
`;

const GuidanceIcon = styled.div`
  font-size: 1.5rem;
  flex-shrink: 0;
`;

const GuidanceContent = styled.div`
  flex: 1;
`;

const GuidanceTitle = styled.div`
  font-weight: 600;
  margin-bottom: 6px;
`;

const GuidanceDescription = styled.div`
  font-size: 0.9rem;
  line-height: 1.5;
`;
