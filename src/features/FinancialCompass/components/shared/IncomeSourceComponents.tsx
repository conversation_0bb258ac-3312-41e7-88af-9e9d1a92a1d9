import styled from 'styled-components';
import React from 'react';

export const IncomeSourcesList = styled.div`
  margin-bottom: 1rem;
`;

export const IncomeSourceItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-bottom: 0.5rem;
`;

export const IncomeSourceInfo = styled.div`
  display: flex;
  flex-direction: column;
`;

export const IncomeSourceAmount = styled.span`
  font-weight: 600;
  color: #333;
`;

export const IncomeSourceFrequency = styled.span`
  font-size: 0.8rem;
  color: #666;
`;

export const IncomeSourceDetails = styled.div`
  display: flex;
  flex-direction: column;
  font-size: 0.9rem;
  color: #555;
`;

export const RemoveButton = styled.button`
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #c82333;
  }
`;

export const EmptyMessage = styled.p`
  color: #666;
  font-style: italic;
  text-align: center;
  margin: 1rem 0;
`;

export const AddIncomeSourceSection = styled.div`
  background-color: #f4f4f4;
  border-radius: 4px;
  padding: 1rem;
  margin-top: 1rem;
`;

export const AddIncomeSourceTitle = styled.h4`
  margin-bottom: 0.75rem;
  color: #333;
`;

export const OptionalFieldsNote = styled.p`
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.5rem;
  font-style: italic;
`;

export const AddButton = styled.button`
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #218838;
  }

  &:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
  }
`;
