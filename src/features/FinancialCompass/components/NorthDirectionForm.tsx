import React from 'react';
import { FormProvider, useForm } from '../../../features/forms';
import { northDirectionSchema } from '../../../schemas/north';
import {
  FormContainer,
  FormHeader,
  FormTitle,
  FormSection,
  SectionTitle,
  FormRow,
  FormActions,
  Button,
  FormError,
  FormSuccess,
} from '../../../features/forms/FormLayout';
import { FormField } from '../../../features/forms/FormField';

// Define the form data type
type FormData = {
  income: {
    salary?: number;
    bonus?: number;
    frequency?: 'weekly' | 'bi-weekly' | 'monthly' | 'annually';
  };
  expenses: {
    housing?: number;
    utilities?: number;
    food?: number;
    transportation?: number;
    healthcare?: number;
    debtPayments?: number;
    entertainment?: number;
    other?: number;
    frequency?: 'weekly' | 'bi-weekly' | 'monthly' | 'annually';
  };
};

const initialValues: FormData = {
  income: {
    salary: undefined,
    bonus: undefined,
    frequency: 'monthly',
  },
  expenses: {
    housing: undefined,
    utilities: undefined,
    food: undefined,
    transportation: undefined,
    healthcare: undefined,
    debtPayments: undefined,
    entertainment: undefined,
    other: undefined,
    frequency: 'monthly',
  },
};

const frequencyOptions = [
  { value: 'weekly', label: 'Weekly' },
  { value: 'bi-weekly', label: 'Bi-weekly' },
  { value: 'monthly', label: 'Monthly' },
  { value: 'annually', label: 'Annually' },
];

export const NorthDirectionForm: React.FC = () => {
  const {
    values,
    errors,
    isSubmitting,
    submitError,
    submitSuccess,
    setFieldValue,
    handleSubmit,
    resetForm,
  } = useForm<FormData>({
    initialValues,
    validationSchema: northDirectionSchema,
    onSubmit: async (values) => {
      // In a real app, you would send this to your API
      console.log('Submitting form:', values);
      return new Promise((resolve) => {
        setTimeout(() => {
          console.log('Form submitted successfully');
          resolve();
        }, 1000);
      });
    },
  });

  const calculateTotalExpenses = () => {
    const { expenses } = values;
    if (!expenses) return 0;

    return Object.entries(expenses).reduce((total, [key, value]) => {
      if (key === 'frequency') return total;
      return total + (typeof value === 'number' ? value : 0);
    }, 0);
  };

  const totalExpenses = calculateTotalExpenses();
  const monthlyIncome = values.income?.salary || 0;
  const savings = monthlyIncome - totalExpenses;
  const savingsRate = monthlyIncome > 0 ? (savings / monthlyIncome) * 100 : 0;

  return (
    <FormContainer>
      <FormHeader>
        <FormTitle>Financial Position (North)</FormTitle>
        <p>Enter your income and expenses to analyze your financial health.</p>
      </FormHeader>

      <form onSubmit={handleSubmit}>
        {submitError && <FormError>{submitError}</FormError>}

        {submitSuccess && (
          <FormSuccess>Your financial information has been saved successfully!</FormSuccess>
        )}

        <FormSection>
          <SectionTitle>Income</SectionTitle>
          <FormRow>
            <FormField
              name="income.salary"
              label="Monthly Salary"
              type="number"
              min="0"
              step="0.01"
              placeholder="0.00"
              required
            />
            <FormField
              name="income.bonus"
              label="Annual Bonus"
              type="number"
              min="0"
              step="0.01"
              placeholder="0.00"
            />
          </FormRow>
          <FormRow>
            <FormField
              name="income.frequency"
              label="Income Frequency"
              as="select"
              options={frequencyOptions}
            />
          </FormRow>
        </FormSection>

        <FormSection>
          <SectionTitle>Monthly Expenses</SectionTitle>
          <FormRow>
            <FormField
              name="expenses.housing"
              label="Housing"
              type="number"
              min="0"
              step="0.01"
              placeholder="0.00"
            />
            <FormField
              name="expenses.utilities"
              label="Utilities"
              type="number"
              min="0"
              step="0.01"
              placeholder="0.00"
            />
          </FormRow>
          <FormRow>
            <FormField
              name="expenses.food"
              label="Food & Groceries"
              type="number"
              min="0"
              step="0.01"
              placeholder="0.00"
            />
            <FormField
              name="expenses.transportation"
              label="Transportation"
              type="number"
              min="0"
              step="0.01"
              placeholder="0.00"
            />
          </FormRow>
          <FormRow>
            <FormField
              name="expenses.healthcare"
              label="Healthcare"
              type="number"
              min="0"
              step="0.01"
              placeholder="0.00"
            />
            <FormField
              name="expenses.debtPayments"
              label="Debt Payments"
              type="number"
              min="0"
              step="0.01"
              placeholder="0.00"
            />
          </FormRow>
          <FormRow>
            <FormField
              name="expenses.entertainment"
              label="Entertainment"
              type="number"
              min="0"
              step="0.01"
              placeholder="0.00"
            />
            <FormField
              name="expenses.other"
              label="Other Expenses"
              type="number"
              min="0"
              step="0.01"
              placeholder="0.00"
            />
          </FormRow>
        </FormSection>

        <FormSection>
          <SectionTitle>Summary</SectionTitle>
          <div style={{ marginBottom: '1rem' }}>
            <p>
              <strong>Monthly Income:</strong> ${monthlyIncome.toFixed(2)}
            </p>
            <p>
              <strong>Total Monthly Expenses:</strong> ${totalExpenses.toFixed(2)}
            </p>
            <p>
              <strong>Monthly Savings:</strong> ${savings.toFixed(2)}
            </p>
            <p>
              <strong>Savings Rate:</strong> {savingsRate.toFixed(1)}%
            </p>
          </div>
        </FormSection>

        <FormActions>
          <Button type="button" variant="secondary" onClick={resetForm} disabled={isSubmitting}>
            Reset
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? 'Saving...' : 'Save Financial Information'}
          </Button>
        </FormActions>
      </form>
    </FormContainer>
  );
};

// Wrap the component with FormProvider for context
export default function NorthDirectionFormWrapper() {
  return (
    <FormProvider initialValues={initialValues}>
      <NorthDirectionForm />
    </FormProvider>
  );
}
