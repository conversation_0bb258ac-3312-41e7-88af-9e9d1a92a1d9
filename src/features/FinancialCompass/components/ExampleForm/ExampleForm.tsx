import React, { useState } from 'react';
import { FormProvider, useForm, useFormContext, useFormState } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { TextField, Typography, Box, Button } from '@mui/material';

// Define path types for nested form fields
type PathsToStringProps<T> = T extends string | number | boolean
  ? []
  : {
      [K in Extract<keyof T, string>]: [K, ...PathsToStringProps<T[K]>];
    }[Extract<keyof T, string>];

type Join<T extends string[], D extends string> = T extends []
  ? never
  : T extends [infer F]
    ? F
    : T extends [infer F, ...infer R]
      ? F extends string
        ? `${F}${D}${Join<Extract<R, string[]>, D>}`
        : never
      : string;

// Define the address type first
interface Address {
  street: string;
  city: string;
  state: string;
  zip: string;
}

export interface FormValues {
  firstName: string;
  lastName: string;
  email: string;
  phone: string | null; // Can be string or null
  address: Address;
}

type FormFieldPath = Join<PathsToStringProps<FormValues>, '.'>;

export interface ExampleFormProps {
  onSubmit: (values: FormValues) => Promise<void> | void;
  defaultValues?: Partial<FormValues>;
  validationSchema?: yup.ObjectSchema<FormValues>;
  children?: React.ReactNode;
}

// Define validation schema using Yup
const defaultValidationSchema: yup.ObjectSchema<FormValues> = yup
  .object({
    firstName: yup.string().required('First name is required'),
    lastName: yup.string().required('Last name is required'),
    email: yup.string().email('Invalid email').required('Email is required'),
    phone: yup
      .string()
      .nullable()
      .transform((value) => (value === '' ? null : value))
      .matches(/^$|^[0-9]{10}$/, 'Phone number must be 10 digits or empty'),
    address: yup
      .object({
        street: yup.string().required('Street address is required'),
        city: yup.string().required('City is required'),
        state: yup.string().required('State is required'),
        zip: yup
          .string()
          .matches(/^\d{5}$/, 'ZIP code must be 5 digits')
          .required('ZIP code is required'),
      })
      .required('Address is required'),
  })
  .required() as yup.ObjectSchema<FormValues>;

// FormStep component for multi-step forms
const FormStep: React.FC<{ step: number; currentStep: number; children: React.ReactNode }> = ({
  step,
  currentStep,
  children,
}) => {
  if (step !== currentStep) return null;
  return <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>{children}</Box>;
};

// FormNavigation component for form navigation
const FormNavigation: React.FC<{
  isFirstStep: boolean;
  isLastStep: boolean;
  onNext: () => void;
  onBack: () => void;
  onSubmit: () => void;
  isSubmitting: boolean;
}> = ({ isFirstStep, isLastStep, onNext, onBack, onSubmit, isSubmitting }) => {
  return (
    <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
      <Button
        type="button"
        onClick={onBack}
        disabled={isFirstStep || isSubmitting}
        variant="outlined"
      >
        Back
      </Button>
      {!isLastStep ? (
        <Button type="button" onClick={onNext} variant="contained" disabled={isSubmitting}>
          Next
        </Button>
      ) : (
        <Button type="button" onClick={onSubmit} variant="contained" disabled={isSubmitting}>
          {isSubmitting ? 'Submitting...' : 'Submit'}
        </Button>
      )}
    </Box>
  );
};

const ExampleForm = ({
  onSubmit,
  defaultValues = {},
  validationSchema = defaultValidationSchema,
  children,
}: ExampleFormProps) => {
  const [currentStep, setCurrentStep] = useState(0);
  const totalSteps = 3;
  const methods = useForm<FormValues>({
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: null,
      address: {
        street: '',
        city: '',
        state: '',
        zip: '',
      },
      ...defaultValues,
      // Ensure phone is null if empty string in defaultValues
      ...(defaultValues?.phone === '' ? { phone: null } : {}),
    },
    resolver: validationSchema ? yupResolver(validationSchema) : undefined,
    mode: 'onChange',
  });

  const {
    trigger,
    formState: { isSubmitting, isValid },
  } = methods;

  const handleFormSubmit = async (data: FormValues) => {
    try {
      // Ensure phone is null if empty string
      const formData = {
        ...data,
        phone: data.phone || null,
      };
      await onSubmit(formData);
    } catch (error) {
      console.error('Form submission error:', error);
      throw error;
    }
  };

  const formSubmitHandler = methods.handleSubmit(handleFormSubmit);

  const handleFormSubmitWrapper = (e: React.FormEvent) => {
    e.preventDefault();
    return formSubmitHandler();
  };

  const handleNext = async () => {
    if (currentStep < totalSteps - 1) {
      const isValid = await trigger();
      if (isValid) {
        setCurrentStep((prev) => prev + 1);
      }
    } else {
      await formSubmitHandler();
    }
  };

  const handleBack = () => {
    setCurrentStep((prev) => Math.max(0, prev - 1));
  };

  const onNext = async () => {
    // Validate current step fields before proceeding
    let fieldsToValidate: FormFieldPath[] = [];

    if (currentStep === 0) {
      fieldsToValidate = ['firstName', 'lastName', 'email'];
    } else if (currentStep === 1) {
      fieldsToValidate = [
        'phone',
        'address.street',
        'address.city',
        'address.state',
        'address.zip',
      ] as FormFieldPath[];
    }

    const isValid = await trigger(fieldsToValidate as any);
    if (isValid) {
      setCurrentStep((prev) => Math.min(prev + 1, totalSteps - 1));
    }
  };

  const onBack = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
  };

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Multi-step Form Example
      </Typography>
      <FormProvider {...methods}>
        <form onSubmit={handleFormSubmitWrapper}>
          {/* Step 1: Personal Information */}
          <FormStep step={0} currentStep={currentStep}>
            <Typography variant="h6" gutterBottom>
              Personal Information
            </Typography>

            <TextField
              {...methods.register('firstName')}
              label="First Name"
              margin="normal"
              fullWidth
              required
              error={!!methods.formState.errors.firstName}
              helperText={methods.formState.errors.firstName?.message}
            />

            <TextField
              {...methods.register('lastName')}
              label="Last Name"
              margin="normal"
              fullWidth
              required
              error={!!methods.formState.errors.lastName}
              helperText={methods.formState.errors.lastName?.message}
            />

            <TextField
              {...methods.register('email')}
              label="Email"
              type="email"
              margin="normal"
              fullWidth
              required
              error={!!methods.formState.errors.email}
              helperText={methods.formState.errors.email?.message}
            />
          </FormStep>

          {/* Step 2: Contact Information */}
          <FormStep step={1} currentStep={currentStep}>
            <Typography variant="h6" gutterBottom>
              Contact Information
            </Typography>

            <TextField
              {...methods.register('phone')}
              label="Phone Number"
              margin="normal"
              fullWidth
              placeholder="1234567890"
              error={!!methods.formState.errors.phone}
              helperText={methods.formState.errors.phone?.message}
            />

            <TextField
              {...methods.register('address.street')}
              label="Street Address"
              margin="normal"
              fullWidth
              required
              error={!!methods.formState.errors.address?.street}
              helperText={methods.formState.errors.address?.street?.message}
            />

            <Box sx={{ display: 'flex', gap: 2 }}>
              <TextField
                {...methods.register('address.city')}
                label="City"
                margin="normal"
                fullWidth
                required
                error={!!methods.formState.errors.address?.city}
                helperText={methods.formState.errors.address?.city?.message}
              />
              <TextField
                {...methods.register('address.state')}
                label="State"
                margin="normal"
                fullWidth
                required
                error={!!methods.formState.errors.address?.state}
                helperText={methods.formState.errors.address?.state?.message}
              />
              <TextField
                {...methods.register('address.zip')}
                label="ZIP Code"
                margin="normal"
                fullWidth
                required
                error={!!methods.formState.errors.address?.zip}
                helperText={methods.formState.errors.address?.zip?.message}
              />
            </Box>
          </FormStep>

          {/* Step 3: Review and Submit */}
          <FormStep step={2} currentStep={currentStep}>
            <Typography variant="h6" gutterBottom>
              Review Your Information
            </Typography>

            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle1" gutterBottom>
                Please review your information before submitting:
              </Typography>

              <pre
                style={{
                  backgroundColor: '#f5f5f5',
                  padding: '16px',
                  borderRadius: '4px',
                  overflowX: 'auto',
                }}
              >
                {JSON.stringify(methods.watch(), null, 2)}
              </pre>
            </Box>
          </FormStep>
          <FormNavigation
            isFirstStep={currentStep === 0}
            isLastStep={currentStep === totalSteps - 1}
            onNext={handleNext}
            onBack={handleBack}
            onSubmit={formSubmitHandler}
            isSubmitting={isSubmitting}
          />
        </form>
      </FormProvider>
    </Box>
  );
};

export { ExampleForm as default };
