import React from 'react';
import { StoryFn, Meta } from '@storybook/react';
import { action } from '@storybook/addon-actions';
import * as yup from 'yup';
import { FormValues } from './ExampleForm';
import ExampleForm from './ExampleForm';

// Define a validation schema for the story
const validationSchema = yup.object().shape({
  firstName: yup.string().required('First name is required'),
  lastName: yup.string().required('Last name is required'),
  email: yup.string().email('Invalid email').required('Email is required'),
  phone: yup.string().matches(/^[0-9]{10}$/, 'Phone number must be 10 digits'),
  address: yup.object({
    street: yup.string().required('Street address is required'),
    city: yup.string().required('City is required'),
    state: yup.string().required('State is required'),
    zip: yup.string().matches(/^[0-9]{5}$/, 'ZIP code must be 5 digits'),
  }),
});

// Define the default export with proper types
export default {
  title: 'FinancialCompass/Forms/ExampleForm',
  component: ExampleForm,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A multi-step form example with validation and navigation',
      },
    },
  },
  argTypes: {
    onSubmit: { action: 'formSubmitted' },
    defaultValues: { control: 'object' },
    validationSchema: { control: { disable: true } },
  },
} as Meta<typeof ExampleForm>;

// Define default values for the form
const defaultFormValues: Partial<FormValues> = {
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  phone: '1234567890',
  address: {
    street: '123 Main St',
    city: 'Anytown',
    state: 'CA',
    zip: '12345',
  },
};

// Define a validation schema for the story
const formValidationSchema = yup.object({
  firstName: yup.string().required('First name is required'),
  lastName: yup.string().required('Last name is required'),
  email: yup.string().email('Invalid email').required('Email is required'),
  phone: yup
    .string()
    .matches(/^[0-9]{10}$/, 'Phone number must be 10 digits')
    .notRequired(),
  address: yup
    .object({
      street: yup.string().required('Street address is required'),
      city: yup.string().required('City is required'),
      state: yup.string().required('State is required'),
      zip: yup
        .string()
        .matches(/^\d{5}$/, 'ZIP code must be 5 digits')
        .required('ZIP code is required'),
    })
    .required('Address is required'),
});

// Base template for stories
const Template: StoryFn<typeof ExampleForm> = (args) => (
  <div style={{ width: '100%', maxWidth: '800px' }}>
    <ExampleForm {...args} />
  </div>
);

// Default story with minimal props
export const Default = Template.bind({});
Default.args = {
  onSubmit: async (values: FormValues) => {
    action('form-submitted')(values);
    return new Promise<void>((resolve) => setTimeout(resolve, 1000));
  },
};

// Story with default values
export const WithDefaultValues = Template.bind({});
WithDefaultValues.args = {
  defaultValues: defaultFormValues,
  onSubmit: async (values: FormValues) => {
    action('form-submitted')(values);
    return new Promise<void>((resolve) => setTimeout(resolve, 1000));
  },
};

// Story with custom validation
const CustomValidationTemplate: StoryFn<typeof ExampleForm> = (args) => {
  const handleSubmit = async (values: FormValues) => {
    action('form-submitted')(values);
    return new Promise<void>((resolve) => setTimeout(resolve, 1500));
  };

  return (
    <div style={{ width: '100%', maxWidth: '800px' }}>
      <ExampleForm {...args} validationSchema={formValidationSchema} onSubmit={handleSubmit} />
    </div>
  );
};

export const WithCustomValidation = CustomValidationTemplate.bind({});
WithCustomValidation.args = {
  defaultValues: {
    ...defaultFormValues,
    email: '', // Clear email to show validation
  },
};

// Add documentation for each story
Default.parameters = {
  docs: {
    description: {
      story: 'Basic example of the ExampleForm component with minimal configuration.',
    },
  },
};

WithDefaultValues.parameters = {
  docs: {
    description: {
      story: 'ExampleForm with pre-filled default values.',
    },
  },
};

WithCustomValidation.parameters = {
  docs: {
    description: {
      story: 'ExampleForm with custom validation rules and error messages.',
    },
  },
};

// Add a11y addon configuration
export const WithA11y = Template.bind({});
WithA11y.parameters = {
  a11y: {
    config: {
      rules: [
        {
          // This rule is disabled because we're using Material-UI components
          // which already handle proper ARIA attributes
          id: 'label',
          enabled: false,
        },
      ],
    },
  },
};

// Add a story with a custom validation schema
export const WithCustomValidation = Template.bind({});
WithCustomValidation.args = {
  validationSchema: {
    firstName: {
      required: 'First name is required',
      minLength: {
        value: 2,
        message: 'First name must be at least 2 characters',
      },
    },
    email: {
      required: 'Email is required',
      pattern: {
        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
        message: 'Invalid email address',
      },
    },
  },
};

WithCustomValidation.parameters = {
  docs: {
    description: {
      story: 'ExampleForm with custom validation rules.',
    },
  },
};
