/**
 * Tax Planning Component
 *
 * This component helps users analyze their tax situation and plan for tax-efficient strategies.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { formatCurrency } from '../../../../utils/formatters';
import {
  calculateTaxLiability,
  calculateRetirementContributionSavings,
  calculateRMD,
  calculateCapitalGainsTax,
  calculateEstateTax,
} from '../../../../utils/taxPlanning';
import { TaxPlanningData, TaxResultsData } from '../../../../types/westDirection';

interface TaxPlanningProps {
  onComplete?: () => void;
  onBack?: () => void;
}

const TaxPlanning: React.FC<TaxPlanningProps> = ({ onComplete, onBack }) => {
  const { theme } = useTheme();
  const { data, updateData } = useFinancialCompass();

  const [isSaving, setIsSaving] = useState(false);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Initialize form data from context or with defaults
  const incomeDetails = data.north?.incomeDetails || {};
  const personalInfo = data.north?.personalInfo || {};

  // Safely access totalAnnualIncome
  const totalAnnualIncome =
    typeof incomeDetails === 'object' &&
    'totalAnnualIncome' in incomeDetails &&
    incomeDetails.totalAnnualIncome
      ? (incomeDetails.totalAnnualIncome as string)
      : '100000';

  // Safely access dateOfBirth
  const dateOfBirth =
    typeof personalInfo === 'object' && 'dateOfBirth' in personalInfo && personalInfo.dateOfBirth
      ? (personalInfo.dateOfBirth as string)
      : '';

  const [formData, setFormData] = useState<TaxPlanningData>(
    data.west?.taxPlanning || {
      income: totalAnnualIncome,
      filingStatus: 'single',
      retirementContribution: '6000',
      retirementBalance: '500000',
      age: dateOfBirth ? calculateAgeFromDateOfBirth(dateOfBirth) : '45',
      capitalGains: '10000',
      estateValue: '1000000',
    }
  );

  // Helper function to calculate age from date of birth
  function calculateAgeFromDateOfBirth(dateOfBirth: string): string {
    if (!dateOfBirth) return '45';

    try {
      const birthDate = new Date(dateOfBirth);
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();

      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }

      return age.toString();
    } catch (error) {
      console.error('Error calculating age:', error);
      return '45';
    }
  }

  // Calculate tax results
  const [results, setResults] = useState<TaxResultsData>({
    currentTax: 0,
    retirementSavings: 0,
    rmd: 0,
    capitalGainsTax: 0,
    estateTax: 0,
    effectiveTaxRate: 0,
  });

  // Calculate tax results when form data changes
  useEffect(() => {
    calculateResults();
  }, [formData]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    if (name === 'filingStatus') {
      setFormData((prev) => ({
        ...prev,
        [name]: value as 'single' | 'married' | 'headOfHousehold',
      }));
    } else {
      // Only allow numbers and decimal points for numeric fields
      if (value === '' || /^[0-9]*\.?[0-9]*$/.test(value)) {
        setFormData((prev) => ({
          ...prev,
          [name]: value,
        }));
      }
    }
  };

  // Calculate tax results
  const calculateResults = () => {
    const income = parseFloat(formData.income) || 0;
    const filingStatus = formData.filingStatus;
    const retirementContribution = parseFloat(formData.retirementContribution) || 0;
    const retirementBalance = parseFloat(formData.retirementBalance) || 0;
    const age = parseInt(formData.age) || 0;
    const capitalGains = parseFloat(formData.capitalGains) || 0;
    const estateValue = parseFloat(formData.estateValue) || 0;

    // Calculate current tax liability
    const currentTax = calculateTaxLiability(income, filingStatus);

    // Calculate retirement contribution tax savings
    const retirementSavings = calculateRetirementContributionSavings(
      income,
      retirementContribution,
      filingStatus
    );

    // Calculate required minimum distributions
    const rmd = calculateRMD(retirementBalance, age);

    // Calculate capital gains tax
    const capitalGainsTax = calculateCapitalGainsTax(capitalGains, income, filingStatus);

    // Calculate estate tax
    const estateTax = calculateEstateTax(estateValue);

    // Calculate effective tax rate
    const effectiveTaxRate = income > 0 ? (currentTax / income) * 100 : 0;

    setResults({
      currentTax,
      retirementSavings,
      rmd,
      capitalGainsTax,
      estateTax,
      effectiveTaxRate,
    });
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSave();

    if (onComplete) {
      onComplete();
    }
  };

  // Handle saving data
  const handleSave = () => {
    setIsSaving(true);

    // Update data in context
    updateData('west', 'taxPlanning', formData);
    updateData('west', 'taxResults', results);

    // Show save indicator
    setShowSaveIndicator(true);

    // Hide save indicator after 2 seconds
    setTimeout(() => {
      setIsSaving(false);
      setShowSaveIndicator(false);
    }, 2000);
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Tax Planning</Title>
        <Description theme={theme}>
          Analyze your tax situation and plan for tax-efficient strategies.
        </Description>
      </Header>

      <form onSubmit={handleSubmit}>
        <FormSection>
          <SectionTitle>Income & Filing Status</SectionTitle>

          <FormRow>
            <FormField>
              <Label htmlFor="income">Annual Income</Label>
              <InputWithPrefix>
                <Prefix>$</Prefix>
                <Input
                  type="text"
                  id="income"
                  name="income"
                  value={formData.income}
                  onChange={handleInputChange}
                  placeholder="100000"
                />
              </InputWithPrefix>
            </FormField>

            <FormField>
              <Label htmlFor="filingStatus">Filing Status</Label>
              <Select
                id="filingStatus"
                name="filingStatus"
                value={formData.filingStatus}
                onChange={handleInputChange}
              >
                <option value="single">Single</option>
                <option value="married">Married Filing Jointly</option>
                <option value="headOfHousehold">Head of Household</option>
              </Select>
            </FormField>
          </FormRow>

          <ResultContainer theme={theme}>
            <ResultLabel>Current Tax Liability:</ResultLabel>
            <ResultValue>{formatCurrency(results.currentTax.toString())}</ResultValue>
          </ResultContainer>

          <ResultContainer theme={theme}>
            <ResultLabel>Effective Tax Rate:</ResultLabel>
            <ResultValue>{results.effectiveTaxRate.toFixed(2)}%</ResultValue>
          </ResultContainer>
        </FormSection>

        <FormSection>
          <SectionTitle>Retirement Planning</SectionTitle>

          <FormRow>
            <FormField>
              <Label htmlFor="retirementContribution">Annual Retirement Contribution</Label>
              <InputWithPrefix>
                <Prefix>$</Prefix>
                <Input
                  type="text"
                  id="retirementContribution"
                  name="retirementContribution"
                  value={formData.retirementContribution}
                  onChange={handleInputChange}
                  placeholder="6000"
                />
              </InputWithPrefix>
            </FormField>

            <FormField>
              <Label htmlFor="retirementBalance">Retirement Account Balance</Label>
              <InputWithPrefix>
                <Prefix>$</Prefix>
                <Input
                  type="text"
                  id="retirementBalance"
                  name="retirementBalance"
                  value={formData.retirementBalance}
                  onChange={handleInputChange}
                  placeholder="500000"
                />
              </InputWithPrefix>
            </FormField>

            <FormField>
              <Label htmlFor="age">Current Age</Label>
              <Input
                type="text"
                id="age"
                name="age"
                value={formData.age}
                onChange={handleInputChange}
                placeholder="45"
              />
            </FormField>
          </FormRow>

          <ResultContainer theme={theme}>
            <ResultLabel>Tax Savings from Retirement Contributions:</ResultLabel>
            <ResultValue>{formatCurrency(results.retirementSavings.toString())}</ResultValue>
          </ResultContainer>

          <ResultContainer theme={theme}>
            <ResultLabel>Required Minimum Distribution (RMD):</ResultLabel>
            <ResultValue>
              {results.rmd > 0
                ? formatCurrency(results.rmd.toString())
                : 'Not applicable until age 72'}
            </ResultValue>
          </ResultContainer>
        </FormSection>

        <FormSection>
          <SectionTitle>Investment & Estate Planning</SectionTitle>

          <FormRow>
            <FormField>
              <Label htmlFor="capitalGains">Annual Capital Gains</Label>
              <InputWithPrefix>
                <Prefix>$</Prefix>
                <Input
                  type="text"
                  id="capitalGains"
                  name="capitalGains"
                  value={formData.capitalGains}
                  onChange={handleInputChange}
                  placeholder="10000"
                />
              </InputWithPrefix>
            </FormField>

            <FormField>
              <Label htmlFor="estateValue">Estimated Estate Value</Label>
              <InputWithPrefix>
                <Prefix>$</Prefix>
                <Input
                  type="text"
                  id="estateValue"
                  name="estateValue"
                  value={formData.estateValue}
                  onChange={handleInputChange}
                  placeholder="1000000"
                />
              </InputWithPrefix>
            </FormField>
          </FormRow>

          <ResultContainer theme={theme}>
            <ResultLabel>Capital Gains Tax:</ResultLabel>
            <ResultValue>{formatCurrency(results.capitalGainsTax.toString())}</ResultValue>
          </ResultContainer>

          <ResultContainer theme={theme}>
            <ResultLabel>Estimated Estate Tax:</ResultLabel>
            <ResultValue>
              {results.estateTax > 0
                ? formatCurrency(results.estateTax.toString())
                : 'Below exemption threshold'}
            </ResultValue>
          </ResultContainer>
        </FormSection>

        <TaxStrategies theme={theme}>
          <SectionTitle>Recommended Tax Strategies</SectionTitle>
          <StrategyList>
            {results.retirementSavings > 0 && (
              <StrategyItem>
                <StrategyIcon>💰</StrategyIcon>
                <StrategyContent>
                  <StrategyTitle>Maximize Retirement Contributions</StrategyTitle>
                  <StrategyDescription>
                    Your retirement contributions are saving you{' '}
                    {formatCurrency(results.retirementSavings.toString())} in taxes. Consider
                    maximizing your contributions to tax-advantaged accounts.
                  </StrategyDescription>
                </StrategyContent>
              </StrategyItem>
            )}

            {results.rmd > 0 && (
              <StrategyItem>
                <StrategyIcon>⏱️</StrategyIcon>
                <StrategyContent>
                  <StrategyTitle>Plan for Required Minimum Distributions</StrategyTitle>
                  <StrategyDescription>
                    Based on your age and retirement balance, your RMD is{' '}
                    {formatCurrency(results.rmd.toString())}. Consider tax-efficient withdrawal
                    strategies.
                  </StrategyDescription>
                </StrategyContent>
              </StrategyItem>
            )}

            {results.capitalGainsTax > 0 && (
              <StrategyItem>
                <StrategyIcon>📈</StrategyIcon>
                <StrategyContent>
                  <StrategyTitle>Tax-Efficient Investing</StrategyTitle>
                  <StrategyDescription>
                    You're paying {formatCurrency(results.capitalGainsTax.toString())} in capital
                    gains tax. Consider tax-loss harvesting or holding investments longer for
                    preferential tax treatment.
                  </StrategyDescription>
                </StrategyContent>
              </StrategyItem>
            )}

            {results.estateTax > 0 && (
              <StrategyItem>
                <StrategyIcon>🏛️</StrategyIcon>
                <StrategyContent>
                  <StrategyTitle>Estate Planning</StrategyTitle>
                  <StrategyDescription>
                    Your estate may be subject to estate tax. Consider gifting strategies, trusts,
                    or charitable giving to reduce potential estate tax liability.
                  </StrategyDescription>
                </StrategyContent>
              </StrategyItem>
            )}

            {results.effectiveTaxRate > 20 && (
              <StrategyItem>
                <StrategyIcon>📊</StrategyIcon>
                <StrategyContent>
                  <StrategyTitle>Income Timing Strategies</StrategyTitle>
                  <StrategyDescription>
                    Your effective tax rate is {results.effectiveTaxRate.toFixed(2)}%. Consider
                    timing income and deductions to manage your tax bracket.
                  </StrategyDescription>
                </StrategyContent>
              </StrategyItem>
            )}
          </StrategyList>
        </TaxStrategies>

        <ButtonContainer>
          {onBack && (
            <BackButton type="button" onClick={onBack} theme={theme}>
              Back
            </BackButton>
          )}

          <SaveIndicator visible={showSaveIndicator}>✓ Saved</SaveIndicator>

          <SubmitButton type="submit" theme={theme}>
            {isSaving ? 'Saving...' : 'Save & Continue'}
          </SubmitButton>
        </ButtonContainer>
      </form>
    </Container>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  padding: 24px;
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const Title = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const Description = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const FormSection = styled.div`
  margin-bottom: 32px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;

const FormField = styled.div`
  margin-bottom: 16px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
`;

const InputWithPrefix = styled.div`
  display: flex;
  align-items: center;
  position: relative;
`;

const Prefix = styled.span`
  position: absolute;
  left: 10px;
  color: #666;
`;

const Input = styled.input`
  width: 100%;
  padding: 10px;
  padding-left: 24px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
`;

const Select = styled.select`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  background-color: white;
`;

const ResultContainer = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  padding: 12px 16px;
  border-radius: 8px;
  margin-top: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const ResultLabel = styled.div`
  font-weight: 500;
`;

const ResultValue = styled.div`
  font-size: 1.1rem;
  font-weight: bold;
`;

const TaxStrategies = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 32px;
`;

const StrategyList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const StrategyItem = styled.div`
  display: flex;
  gap: 16px;
`;

const StrategyIcon = styled.div`
  font-size: 1.5rem;
  min-width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const StrategyContent = styled.div`
  flex: 1;
`;

const StrategyTitle = styled.h4`
  margin: 0 0 8px 0;
  font-size: 1.1rem;
`;

const StrategyDescription = styled.p`
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.5;
  color: #666;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
`;

const BackButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.text.secondary};
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const SubmitButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 24px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  color: green;
  margin-right: 16px;
`;

export default TaxPlanning;
