/**
 * Charitable Giving Component
 *
 * This component helps users track and plan their charitable contributions
 * and giving strategies.
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { formatCurrency } from '../../../../utils/formatters';

interface CharitableGivingProps {
  onComplete?: () => void;
  onBack?: () => void;
}

interface Charity {
  id: string;
  name: string;
  category: string;
  annualAmount: string;
  frequency: 'one_time' | 'monthly' | 'quarterly' | 'annual';
  taxDeductible: boolean;
  notes?: string;
}

interface CharitableGivingData {
  hasGivingPlan: boolean;
  annualGivingTarget: string;
  givingPercentage: string;
  charities: Charity[];
  givingVehicles: {
    directDonations: boolean;
    donorAdvisedFund: boolean;
    charitableTrust: boolean;
    foundationGiving: boolean;
    stockDonations: boolean;
    qcd: boolean; // Qualified Charitable Distributions
  };
  givingNotes: string;
}

const CharitableGiving: React.FC<CharitableGivingProps> = ({ onComplete, onBack }) => {
  const { theme } = useTheme();
  const { data, updateData } = useFinancialCompass();

  const [isSaving, setIsSaving] = useState(false);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Charity categories
  const charityCategories = [
    'Religious',
    'Education',
    'Human Services',
    'Health',
    'Environment',
    'Arts & Culture',
    'International',
    'Community Development',
    'Animal Welfare',
    'Other',
  ];

  // Donation frequency options
  const frequencyOptions = [
    { value: 'one_time', label: 'One-time' },
    { value: 'monthly', label: 'Monthly' },
    { value: 'quarterly', label: 'Quarterly' },
    { value: 'annual', label: 'Annual' },
  ];

  // Initialize form data from context or with defaults
  const [formData, setFormData] = useState<CharitableGivingData>(
    data.west?.charitableGiving || {
      hasGivingPlan: false,
      annualGivingTarget: '',
      givingPercentage: '',
      charities: [],
      givingVehicles: {
        directDonations: false,
        donorAdvisedFund: false,
        charitableTrust: false,
        foundationGiving: false,
        stockDonations: false,
        qcd: false,
      },
      givingNotes: '',
    }
  );

  // New charity form
  const [newCharity, setNewCharity] = useState<Omit<Charity, 'id'>>({
    name: '',
    category: '',
    annualAmount: '',
    frequency: 'annual',
    taxDeductible: true,
    notes: '',
  });

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;

    if (name.includes('.')) {
      // Handle nested properties (e.g., givingVehicles.directDonations)
      const [parent, child] = name.split('.');

      // Type-safe approach for nested properties
      if (parent === 'givingVehicles') {
        setFormData((prev) => ({
          ...prev,
          givingVehicles: {
            ...prev.givingVehicles,
            [child]: checked,
          },
        }));
      } else {
        // Generic fallback for other nested properties
        setFormData((prev) => {
          const parentObj = prev[parent as keyof CharitableGivingData];
          if (typeof parentObj === 'object' && parentObj !== null) {
            return {
              ...prev,
              [parent]: {
                ...parentObj,
                [child]: checked,
              },
            };
          }
          return prev;
        });
      }
    } else {
      // Handle top-level properties
      setFormData((prev) => ({
        ...prev,
        [name]: checked,
      }));
    }
  };

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle new charity field changes
  const handleNewCharityChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target;

    // Handle checkbox separately
    if (type === 'checkbox') {
      const isChecked = (e.target as HTMLInputElement).checked;
      setNewCharity((prev) => ({
        ...prev,
        [name]: isChecked,
      }));
    } else {
      setNewCharity((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  // Add new charity
  const handleAddCharity = () => {
    if (!newCharity.name || !newCharity.category) return;

    const newCharityWithId: Charity = {
      ...newCharity,
      id: `charity-${Date.now()}`,
    };

    // Add new charity to the list
    setFormData((prev) => ({
      ...prev,
      charities: [...prev.charities, newCharityWithId],
    }));

    // Reset new charity form
    setNewCharity({
      name: '',
      category: '',
      annualAmount: '',
      frequency: 'annual',
      taxDeductible: true,
      notes: '',
    });
  };

  // Remove charity
  const handleRemoveCharity = (id: string) => {
    setFormData((prev) => ({
      ...prev,
      charities: prev.charities.filter((charity) => charity.id !== id),
    }));
  };

  // Calculate total annual giving
  const calculateTotalAnnualGiving = (): number => {
    return formData.charities.reduce((total, charity) => {
      const amount = parseFloat(charity.annualAmount) || 0;

      if (charity.frequency === 'monthly') {
        return total + amount * 12;
      } else if (charity.frequency === 'quarterly') {
        return total + amount * 4;
      } else if (charity.frequency === 'annual') {
        return total + amount;
      } else {
        // One-time donation
        return total + amount;
      }
    }, 0);
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSave();

    if (onComplete) {
      onComplete();
    }
  };

  // Handle saving data
  const handleSave = () => {
    setIsSaving(true);

    // Update data in context
    updateData('west', 'charitableGiving', formData);

    // Show save indicator
    setShowSaveIndicator(true);

    // Hide save indicator after 2 seconds
    setTimeout(() => {
      setIsSaving(false);
      setShowSaveIndicator(false);
    }, 2000);
  };

  // Get frequency label
  const getFrequencyLabel = (frequency: Charity['frequency']): string => {
    switch (frequency) {
      case 'one_time':
        return 'One-time';
      case 'monthly':
        return 'Monthly';
      case 'quarterly':
        return 'Quarterly';
      case 'annual':
        return 'Annual';
      default:
        return 'Unknown';
    }
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Charitable Giving</Title>
        <Description theme={theme}>
          Track and plan your charitable contributions and giving strategies.
        </Description>
      </Header>

      <form onSubmit={handleSubmit}>
        <FormSection>
          <SectionTitle>Giving Plan</SectionTitle>

          <CheckboxField>
            <Checkbox
              type="checkbox"
              id="hasGivingPlan"
              name="hasGivingPlan"
              checked={formData.hasGivingPlan}
              onChange={handleCheckboxChange}
            />
            <CheckboxLabel htmlFor="hasGivingPlan">I have a charitable giving plan</CheckboxLabel>
          </CheckboxField>

          <FormRow>
            <FormField>
              <Label htmlFor="annualGivingTarget">Annual Giving Target</Label>
              <Input
                type="text"
                id="annualGivingTarget"
                name="annualGivingTarget"
                value={formData.annualGivingTarget}
                onChange={handleInputChange}
                placeholder="e.g., 5000"
              />
            </FormField>

            <FormField>
              <Label htmlFor="givingPercentage">Percentage of Income</Label>
              <Input
                type="text"
                id="givingPercentage"
                name="givingPercentage"
                value={formData.givingPercentage}
                onChange={handleInputChange}
                placeholder="e.g., 10"
              />
              <FieldHint>Percentage of your income you aim to donate</FieldHint>
            </FormField>
          </FormRow>
        </FormSection>

        <FormSection>
          <SectionTitle>Charitable Organizations</SectionTitle>

          {formData.charities.length > 0 ? (
            <CharitiesList>
              {formData.charities.map((charity) => (
                <CharityItem key={charity.id} theme={theme}>
                  <CharityHeader>
                    <CharityName>{charity.name}</CharityName>
                    <CharityCategory>{charity.category}</CharityCategory>
                    <RemoveButton
                      onClick={() => handleRemoveCharity(charity.id)}
                      theme={theme}
                      type="button"
                    >
                      Remove
                    </RemoveButton>
                  </CharityHeader>

                  <CharityDetails>
                    <CharityDetail>
                      <DetailLabel>Amount:</DetailLabel>
                      <DetailValue>
                        {formatCurrency(Number(charity.annualAmount) || 0)} (
                        {getFrequencyLabel(charity.frequency)})
                      </DetailValue>
                    </CharityDetail>

                    <CharityDetail>
                      <DetailLabel>Tax Deductible:</DetailLabel>
                      <DetailValue>{charity.taxDeductible ? 'Yes' : 'No'}</DetailValue>
                    </CharityDetail>

                    {charity.notes && <CharityNotes>{charity.notes}</CharityNotes>}
                  </CharityDetails>
                </CharityItem>
              ))}
            </CharitiesList>
          ) : (
            <EmptyState theme={theme}>
              <EmptyIcon>🎁</EmptyIcon>
              <EmptyText>
                You haven't added any charitable organizations yet. Use the form below to add your
                first charity.
              </EmptyText>
            </EmptyState>
          )}

          <AddCharityForm theme={theme}>
            <FormRow>
              <FormField>
                <Label htmlFor="name">Organization Name</Label>
                <Input
                  type="text"
                  id="name"
                  name="name"
                  value={newCharity.name}
                  onChange={handleNewCharityChange}
                  placeholder="e.g., Red Cross"
                  required
                />
              </FormField>

              <FormField>
                <Label htmlFor="category">Category</Label>
                <Select
                  id="category"
                  name="category"
                  value={newCharity.category}
                  onChange={handleNewCharityChange}
                  required
                >
                  <option value="">Select category...</option>
                  {charityCategories.map((category) => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </Select>
              </FormField>
            </FormRow>

            <FormRow>
              <FormField>
                <Label htmlFor="annualAmount">Donation Amount</Label>
                <Input
                  type="text"
                  id="annualAmount"
                  name="annualAmount"
                  value={newCharity.annualAmount}
                  onChange={handleNewCharityChange}
                  placeholder="e.g., 1000"
                />
              </FormField>

              <FormField>
                <Label htmlFor="frequency">Frequency</Label>
                <Select
                  id="frequency"
                  name="frequency"
                  value={newCharity.frequency}
                  onChange={handleNewCharityChange}
                >
                  {frequencyOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </Select>
              </FormField>
            </FormRow>

            <CheckboxField>
              <Checkbox
                type="checkbox"
                id="taxDeductible"
                name="taxDeductible"
                checked={newCharity.taxDeductible}
                onChange={handleNewCharityChange}
              />
              <CheckboxLabel htmlFor="taxDeductible">Tax Deductible</CheckboxLabel>
            </CheckboxField>

            <FormField>
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                name="notes"
                value={newCharity.notes}
                onChange={handleNewCharityChange}
                placeholder="Additional details about this charity"
              />
            </FormField>

            <AddButton
              type="button"
              onClick={handleAddCharity}
              disabled={!newCharity.name || !newCharity.category}
              theme={theme}
            >
              Add Charity
            </AddButton>
          </AddCharityForm>
        </FormSection>

        <FormSection>
          <SectionTitle>Giving Summary</SectionTitle>

          <SummaryContainer theme={theme}>
            <SummaryRow>
              <SummaryLabel>Total Annual Giving:</SummaryLabel>
              <SummaryValue highlight>{formatCurrency(calculateTotalAnnualGiving())}</SummaryValue>
            </SummaryRow>

            <SummaryRow>
              <SummaryLabel>Number of Organizations:</SummaryLabel>
              <SummaryValue>{formData.charities.length}</SummaryValue>
            </SummaryRow>
          </SummaryContainer>
        </FormSection>

        <FormSection>
          <SectionTitle>Giving Vehicles</SectionTitle>

          <GivingVehiclesContainer theme={theme}>
            <GivingVehicleItem>
              <Checkbox
                type="checkbox"
                id="directDonations"
                name="givingVehicles.directDonations"
                checked={formData.givingVehicles.directDonations}
                onChange={handleCheckboxChange}
              />
              <GivingVehicleLabel htmlFor="directDonations">Direct Donations</GivingVehicleLabel>
            </GivingVehicleItem>

            <GivingVehicleItem>
              <Checkbox
                type="checkbox"
                id="donorAdvisedFund"
                name="givingVehicles.donorAdvisedFund"
                checked={formData.givingVehicles.donorAdvisedFund}
                onChange={handleCheckboxChange}
              />
              <GivingVehicleLabel htmlFor="donorAdvisedFund">Donor-Advised Fund</GivingVehicleLabel>
            </GivingVehicleItem>

            <GivingVehicleItem>
              <Checkbox
                type="checkbox"
                id="charitableTrust"
                name="givingVehicles.charitableTrust"
                checked={formData.givingVehicles.charitableTrust}
                onChange={handleCheckboxChange}
              />
              <GivingVehicleLabel htmlFor="charitableTrust">Charitable Trust</GivingVehicleLabel>
            </GivingVehicleItem>

            <GivingVehicleItem>
              <Checkbox
                type="checkbox"
                id="foundationGiving"
                name="givingVehicles.foundationGiving"
                checked={formData.givingVehicles.foundationGiving}
                onChange={handleCheckboxChange}
              />
              <GivingVehicleLabel htmlFor="foundationGiving">Foundation Giving</GivingVehicleLabel>
            </GivingVehicleItem>

            <GivingVehicleItem>
              <Checkbox
                type="checkbox"
                id="stockDonations"
                name="givingVehicles.stockDonations"
                checked={formData.givingVehicles.stockDonations}
                onChange={handleCheckboxChange}
              />
              <GivingVehicleLabel htmlFor="stockDonations">Stock Donations</GivingVehicleLabel>
            </GivingVehicleItem>

            <GivingVehicleItem>
              <Checkbox
                type="checkbox"
                id="qcd"
                name="givingVehicles.qcd"
                checked={formData.givingVehicles.qcd}
                onChange={handleCheckboxChange}
              />
              <GivingVehicleLabel htmlFor="qcd">
                Qualified Charitable Distributions (QCD)
              </GivingVehicleLabel>
            </GivingVehicleItem>
          </GivingVehiclesContainer>
        </FormSection>

        <FormSection>
          <SectionTitle>Additional Notes</SectionTitle>
          <FormField>
            <Label htmlFor="givingNotes">Notes about your charitable giving</Label>
            <Textarea
              id="givingNotes"
              name="givingNotes"
              value={formData.givingNotes}
              onChange={handleInputChange}
              placeholder="Add any additional notes about your charitable giving strategy"
            />
          </FormField>
        </FormSection>

        <ButtonContainer>
          {onBack && (
            <BackButton type="button" onClick={onBack} theme={theme}>
              Back
            </BackButton>
          )}

          <SaveIndicator visible={showSaveIndicator}>✓ Saved</SaveIndicator>

          <SubmitButton type="submit" theme={theme}>
            {isSaving ? 'Saving...' : 'Save & Continue'}
          </SubmitButton>
        </ButtonContainer>
      </form>
    </Container>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  padding: 24px;
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const Title = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const Description = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const FormSection = styled.div`
  margin-bottom: 32px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;

const FormField = styled.div`
  margin-bottom: 16px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
`;

const Input = styled.input`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
`;

const Select = styled.select`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
`;

const Textarea = styled.textarea`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  min-height: 80px;
  resize: vertical;
`;

const FieldHint = styled.div`
  font-size: 0.8rem;
  color: #666;
  margin-top: 4px;
`;

const CheckboxField = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 16px;
`;

const Checkbox = styled.input`
  margin-right: 8px;
`;

const CheckboxLabel = styled.label`
  font-size: 1rem;
`;

const CharitiesList = styled.div`
  margin-bottom: 24px;
`;

const CharityItem = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
`;

const CharityHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const CharityName = styled.div`
  font-weight: 600;
  font-size: 1.1rem;
`;

const CharityCategory = styled.div`
  font-style: italic;
  color: #666;
`;

const RemoveButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.error.main};
  border: none;
  font-size: 0.9rem;
  cursor: pointer;
  padding: 4px 8px;

  &:hover {
    text-decoration: underline;
  }
`;

const CharityDetails = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
`;

const CharityDetail = styled.div`
  margin-bottom: 8px;
`;

const DetailLabel = styled.div`
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 4px;
`;

const DetailValue = styled.div`
  font-size: 0.9rem;
`;

const CharityNotes = styled.div`
  grid-column: 1 / -1;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #eee;
  font-style: italic;
  font-size: 0.9rem;
`;

const EmptyState = styled.div<{ theme: any }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  margin-bottom: 24px;
`;

const EmptyIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
`;

const EmptyText = styled.p`
  text-align: center;
  color: #666;
  max-width: 400px;
`;

const AddCharityForm = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  padding: 16px;
`;

const AddButton = styled.button<{ theme: any; disabled?: boolean }>`
  background-color: ${(props) =>
    props.disabled
      ? props.theme.colors.action.disabledBackground
      : props.theme.colors.primary.main};
  color: ${(props) =>
    props.disabled ? props.theme.colors.action.disabled : props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) =>
      props.disabled
        ? props.theme.colors.action.disabledBackground
        : props.theme.colors.primary.dark};
  }
`;

const SummaryContainer = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  padding: 16px;
  border-radius: 8px;
`;

const SummaryRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }
`;

const SummaryLabel = styled.div`
  font-weight: 500;
`;

const SummaryValue = styled.div<{ highlight?: boolean }>`
  font-weight: ${(props) => (props.highlight ? 'bold' : 'normal')};
  font-size: ${(props) => (props.highlight ? '1.2rem' : '1rem')};
`;

const GivingVehiclesContainer = styled.div<{ theme: any }>`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 12px;
  background-color: ${(props) => props.theme.colors.background.default};
  padding: 16px;
  border-radius: 8px;
`;

const GivingVehicleItem = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 8px;
`;

const GivingVehicleLabel = styled.label`
  font-size: 0.95rem;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
`;

const BackButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.text.secondary};
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const SubmitButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 24px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  color: green;
  margin-right: 16px;
`;

export default CharitableGiving;
