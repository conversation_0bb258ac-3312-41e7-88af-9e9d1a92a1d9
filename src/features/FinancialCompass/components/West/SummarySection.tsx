/**
 * West Direction Summary Section
 *
 * This component displays a summary of the West Direction journey,
 * showing the user's legacy and impact plan with trends, alerts, actions, and guidance.
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { formatCurrency } from '../../../../utils/formatters';
import {
  TrendsSection,
  AlertsSection,
  ActionsSection,
  GuidanceSection,
  TrendItem,
  AlertItem,
  GuidanceItem,
} from '../shared/SummaryComponents';
import type { ActionItem } from '../shared/SummaryComponents';
import { downloadComprehensivePDF } from '../../../../utils/enhancedPdfExport';

interface SummarySectionProps {
  onComplete?: () => void;
  onBack?: () => void;
}

const SummarySection: React.FC<SummarySectionProps> = ({ onComplete, onBack }) => {
  const { theme } = useTheme();
  const { data } = useFinancialCompass();
  const [isExporting, setIsExporting] = useState(false);

  // Get personal info for PDF export
  const personalInfo = data.north?.personalInformation || {};

  // Extract relevant data from the context with proper type checking
  const estatePlanning = data.west?.estatePlanning || {};
  const charitableGiving = data.west?.charitableGiving || {};
  const legacyPlanning = data.west?.legacyPlanning || {};
  const valuesGoals = data.west?.valuesGoals || {};
  const taxPlanning = data.west?.taxPlanning || {};
  const legacyMessages = Array.isArray(data.west?.legacyMessages) ? data.west?.legacyMessages : [];

  // Parse estate planning data
  const hasWill =
    typeof estatePlanning === 'object' && 'hasWill' in estatePlanning && estatePlanning.hasWill;

  const hasTrust =
    typeof estatePlanning === 'object' && 'hasTrust' in estatePlanning && estatePlanning.hasTrust;

  const hasPowerOfAttorney =
    typeof estatePlanning === 'object' &&
    'hasPowerOfAttorney' in estatePlanning &&
    estatePlanning.hasPowerOfAttorney;

  const hasHealthcareDirective =
    typeof estatePlanning === 'object' &&
    'hasHealthcareDirective' in estatePlanning &&
    estatePlanning.hasHealthcareDirective;

  // Parse charitable giving data
  const annualGivingTarget =
    typeof charitableGiving === 'object' &&
    'annualGivingTarget' in charitableGiving &&
    charitableGiving.annualGivingTarget
      ? parseFloat(charitableGiving.annualGivingTarget as string)
      : 0;

  const givingPercentage =
    typeof charitableGiving === 'object' &&
    'givingPercentage' in charitableGiving &&
    charitableGiving.givingPercentage
      ? parseFloat(charitableGiving.givingPercentage as string)
      : 0;

  const charities =
    typeof charitableGiving === 'object' &&
    'charities' in charitableGiving &&
    Array.isArray(charitableGiving.charities)
      ? charitableGiving.charities
      : [];

  // Parse legacy planning data
  const hasLegacyStatement =
    typeof legacyPlanning === 'object' &&
    'legacyStatement' in legacyPlanning &&
    legacyPlanning.legacyStatement;

  const values =
    typeof valuesGoals === 'object'
      ? 'values' in valuesGoals && Array.isArray(valuesGoals.values)
        ? valuesGoals.values
        : 'coreValues' in valuesGoals && Array.isArray(valuesGoals.coreValues)
          ? valuesGoals.coreValues
          : []
      : [];

  // Calculate legacy planning score (0-100)
  const calculateLegacyScore = () => {
    let score = 0;

    // Estate planning (0-40 points)
    if (hasWill) score += 10;
    if (hasTrust) score += 10;
    if (hasPowerOfAttorney) score += 10;
    if (hasHealthcareDirective) score += 10;

    // Charitable giving (0-20 points)
    if (annualGivingTarget > 0) score += 10;
    if (charities.length > 0) score += 10;

    // Legacy planning (0-40 points)
    if (hasLegacyStatement) score += 15;
    if (legacyMessages.length > 0) score += 15;
    if (values.length > 0) score += 10;

    return score;
  };

  const legacyScore = calculateLegacyScore();

  // Get legacy status
  const getLegacyStatus = () => {
    if (legacyScore >= 80) return 'Excellent';
    if (legacyScore >= 60) return 'Good';
    if (legacyScore >= 40) return 'Fair';
    if (legacyScore >= 20) return 'Needs Attention';
    return 'Critical';
  };

  // Generate trends
  const generateTrends = (): TrendItem[] => {
    return [
      {
        id: 'estate_planning',
        label: 'Estate Planning Completion',
        value: `${Math.round((((hasWill ? 1 : 0) + (hasTrust ? 1 : 0) + (hasPowerOfAttorney ? 1 : 0) + (hasHealthcareDirective ? 1 : 0)) / 4) * 100)}%`,
        change: 25, // This would ideally be calculated from historical data
        direction: 'up',
        isPositive: true,
      },
      {
        id: 'charitable_giving',
        label: 'Charitable Giving',
        value: formatCurrency(annualGivingTarget.toString()),
        change: 10, // This would ideally be calculated from historical data
        direction: 'up',
        isPositive: true,
      },
      {
        id: 'legacy_messages',
        label: 'Legacy Messages',
        value: legacyMessages.length.toString(),
        change: 5, // This would ideally be calculated from historical data
        direction: 'up',
        isPositive: true,
      },
      {
        id: 'values_identified',
        label: 'Values Identified',
        value: values.length.toString(),
        change: 3, // This would ideally be calculated from historical data
        direction: 'up',
        isPositive: true,
      },
    ];
  };

  // Generate alerts
  const generateAlerts = (): AlertItem[] => {
    const alerts: AlertItem[] = [];

    if (!hasWill) {
      alerts.push({
        id: 'missing_will',
        title: 'Missing Will',
        description:
          'You do not have a will in place. Creating a will is essential for ensuring your assets are distributed according to your wishes.',
        severity: 'high',
      });
    }

    if (!hasPowerOfAttorney) {
      alerts.push({
        id: 'missing_poa',
        title: 'Missing Power of Attorney',
        description:
          'You do not have a power of attorney document. This is important for designating someone to make financial decisions on your behalf if you become incapacitated.',
        severity: 'medium',
      });
    }

    if (!hasHealthcareDirective) {
      alerts.push({
        id: 'missing_healthcare_directive',
        title: 'Missing Healthcare Directive',
        description:
          'You do not have a healthcare directive. This document specifies your medical care preferences if you are unable to communicate them yourself.',
        severity: 'medium',
      });
    }

    if (!hasLegacyStatement) {
      alerts.push({
        id: 'missing_legacy_statement',
        title: 'Missing Legacy Statement',
        description:
          'You have not created a legacy statement. This helps clarify how you want to be remembered and the impact you want to have.',
        severity: 'low',
      });
    }

    return alerts;
  };

  // Generate actions
  const generateActions = (): ActionItem[] => {
    const actions: ActionItem[] = [];

    if (!hasWill) {
      actions.push({
        id: 'create_will',
        title: 'Create a Will',
        description:
          'Work with an estate planning attorney to create a will that ensures your assets are distributed according to your wishes.',
        priority: 'high',
        icon: '📜',
      });
    }

    if (!hasPowerOfAttorney) {
      actions.push({
        id: 'create_poa',
        title: 'Create a Power of Attorney',
        description:
          'Designate someone to make financial decisions on your behalf if you become incapacitated.',
        priority: 'medium',
        icon: '📝',
      });
    }

    if (!hasHealthcareDirective) {
      actions.push({
        id: 'create_healthcare_directive',
        title: 'Create a Healthcare Directive',
        description:
          'Document your medical care preferences in case you are unable to communicate them yourself.',
        priority: 'medium',
        icon: '🏥',
      });
    }

    if (!hasLegacyStatement) {
      actions.push({
        id: 'create_legacy_statement',
        title: 'Create a Legacy Statement',
        description:
          'Define how you want to be remembered and the impact you want to have on future generations.',
        priority: 'medium',
        icon: '🏛️',
      });
    }

    if (legacyMessages.length === 0) {
      actions.push({
        id: 'record_legacy_messages',
        title: 'Record Legacy Messages',
        description:
          'Leave personal messages for loved ones that can be shared after your passing.',
        priority: 'low',
        icon: '💌',
      });
    }

    return actions;
  };

  // Generate guidance
  const generateGuidance = (): GuidanceItem[] => {
    return [
      {
        id: 'estate_planning',
        title: 'Estate Planning Essentials',
        description:
          'A comprehensive estate plan should include a will, power of attorney, healthcare directive, and potentially trusts depending on your situation.',
        icon: '📚',
      },
      {
        id: 'charitable_giving',
        title: 'Strategic Charitable Giving',
        description:
          'Consider establishing a donor-advised fund or charitable trust for tax-efficient giving that aligns with your values and goals.',
        icon: '🎁',
      },
      {
        id: 'legacy_planning',
        title: 'Legacy Beyond Assets',
        description:
          'Your legacy includes not just financial assets, but also your values, wisdom, and life lessons. Consider recording these for future generations.',
        icon: '🏛️',
      },
      {
        id: 'tax_planning',
        title: 'Tax-Efficient Legacy Planning',
        description:
          'Work with a tax professional to structure your estate in a way that minimizes tax burden on your heirs while maximizing charitable impact.',
        icon: '📊',
      },
    ];
  };

  const trends = generateTrends();
  const alerts = generateAlerts();
  const actions = generateActions();
  const guidanceItems = generateGuidance();

  // Handle PDF export
  const handleExportPDF = async () => {
    setIsExporting(true);

    try {
      const userName =
        `${(personalInfo as any)?.firstName || ''} ${(personalInfo as any)?.lastName || ''}`.trim() ||
        'User';
      await downloadComprehensivePDF(data, userName);
    } catch (error) {
      console.error('Error exporting PDF:', error);
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Container theme={theme} data-testid="summary-section">
      <Header theme={theme}>
        <Title theme={theme}>West Direction Summary</Title>
        <Description theme={theme}>Review your legacy and impact plan.</Description>
      </Header>

      <LegacyScoreCard theme={theme}>
        <LegacyScoreHeader>
          <LegacyScoreTitle>Legacy Planning Score</LegacyScoreTitle>
          <LegacyScoreValue score={legacyScore}>{legacyScore}</LegacyScoreValue>
        </LegacyScoreHeader>
        <LegacyScoreStatus score={legacyScore}>{getLegacyStatus()}</LegacyScoreStatus>
        <LegacyScoreBar theme={theme}>
          <LegacyScoreFill score={legacyScore} theme={theme} />
        </LegacyScoreBar>
      </LegacyScoreCard>

      <SummaryCard theme={theme}>
        <SummaryTitle>Estate Planning</SummaryTitle>
        <SummaryContent>
          <SummaryItem>
            <SummaryLabel>Will Status:</SummaryLabel>
            <SummaryValue highlight={!hasWill}>
              {hasWill ? 'Completed' : 'Not Started'}
            </SummaryValue>
          </SummaryItem>
          <SummaryItem>
            <SummaryLabel>Power of Attorney:</SummaryLabel>
            <SummaryValue highlight={!hasPowerOfAttorney}>
              {hasPowerOfAttorney ? 'Completed' : 'Not Started'}
            </SummaryValue>
          </SummaryItem>
          <SummaryItem>
            <SummaryLabel>Healthcare Directive:</SummaryLabel>
            <SummaryValue highlight={!hasHealthcareDirective}>
              {hasHealthcareDirective ? 'Completed' : 'Not Started'}
            </SummaryValue>
          </SummaryItem>
          <SummaryItem>
            <SummaryLabel>Trust:</SummaryLabel>
            <SummaryValue>{hasTrust ? 'Completed' : 'Not Started'}</SummaryValue>
          </SummaryItem>
        </SummaryContent>
      </SummaryCard>

      <SummaryCard theme={theme}>
        <SummaryTitle>Legacy Planning</SummaryTitle>
        <SummaryContent>
          <SummaryItem>
            <SummaryLabel>Legacy Statement:</SummaryLabel>
            <SummaryValue highlight={!hasLegacyStatement}>
              {hasLegacyStatement ? 'Completed' : 'Not Started'}
            </SummaryValue>
          </SummaryItem>
          <SummaryItem>
            <SummaryLabel>Legacy Messages:</SummaryLabel>
            <SummaryValue>{legacyMessages.length} messages recorded</SummaryValue>
          </SummaryItem>
          <SummaryItem>
            <SummaryLabel>Values Identified:</SummaryLabel>
            <SummaryValue>{values.length} values</SummaryValue>
          </SummaryItem>
          <SummaryItem>
            <SummaryLabel>Charitable Giving:</SummaryLabel>
            <SummaryValue>{formatCurrency(annualGivingTarget.toString())}/year</SummaryValue>
          </SummaryItem>
        </SummaryContent>
      </SummaryCard>

      <TrendsSection title="Legacy Trends" trends={trends} theme={theme} />
      <AlertsSection alerts={alerts} theme={theme} />
      <ActionsSection actions={actions} theme={theme} />
      <GuidanceSection guidanceItems={guidanceItems} theme={theme} />

      <ButtonGroup>
        <ExportButton onClick={handleExportPDF} disabled={isExporting} theme={theme}>
          {isExporting ? 'Generating PDF...' : 'Export Legacy Plan Report'}
        </ExportButton>
      </ButtonGroup>

      <ButtonContainer>
        {onBack && (
          <BackButton type="button" onClick={onBack} theme={theme}>
            Back
          </BackButton>
        )}

        <CompleteButton type="button" onClick={onComplete} theme={theme}>
          Complete West Direction
        </CompleteButton>
      </ButtonContainer>
    </Container>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  padding: 24px;
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const Title = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const Description = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const SummaryCard = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const SummaryTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const SummaryContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const SummaryItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
`;

const SummaryLabel = styled.div`
  font-weight: 500;
`;

const SummaryValue = styled.div<{ highlight?: boolean }>`
  font-weight: 600;
  color: ${(props) => (props.highlight ? '#F44336' : '#2196F3')};
`;

const ActionItems = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const ActionTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const ActionList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const ActionItem = styled.div`
  display: flex;
  gap: 16px;
`;

const ActionIcon = styled.div`
  font-size: 1.5rem;
  min-width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const ActionContent = styled.div`
  flex: 1;
`;

const ActionName = styled.h4`
  margin: 0 0 4px 0;
  font-size: 1.1rem;
  font-weight: 500;
`;

const ActionDescription = styled.p`
  margin: 0;
  font-size: 0.9rem;
  color: #666;
  line-height: 1.5;
`;

const LegacyScoreCard = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  text-align: center;
`;

const LegacyScoreHeader = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
`;

const LegacyScoreTitle = styled.h3`
  margin: 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const LegacyScoreValue = styled.div<{ score: number }>`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.5rem;
  font-weight: 700;
  background-color: ${(props) => {
    if (props.score >= 80) return '#4CAF50';
    if (props.score >= 60) return '#8BC34A';
    if (props.score >= 40) return '#FFC107';
    if (props.score >= 20) return '#FF9800';
    return '#F44336';
  }};
  color: white;
`;

const LegacyScoreStatus = styled.div<{ score: number }>`
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: ${(props) => {
    if (props.score >= 80) return '#4CAF50';
    if (props.score >= 60) return '#8BC34A';
    if (props.score >= 40) return '#FFC107';
    if (props.score >= 20) return '#FF9800';
    return '#F44336';
  }};
`;

const LegacyScoreBar = styled.div<{ theme: any }>`
  height: 8px;
  background-color: ${(props) => props.theme.colors.background.main};
  border-radius: 4px;
  overflow: hidden;
`;

const LegacyScoreFill = styled.div<{ score: number; theme: any }>`
  height: 100%;
  width: ${(props) => props.score}%;
  background-color: ${(props) => {
    if (props.score >= 80) return '#4CAF50';
    if (props.score >= 60) return '#8BC34A';
    if (props.score >= 40) return '#FFC107';
    if (props.score >= 20) return '#FF9800';
    return '#F44336';
  }};
`;

const ButtonGroup = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
`;

const ExportButton = styled.button<{ theme: any; disabled?: boolean }>`
  padding: 12px 24px;
  background-color: ${(props) => (props.disabled ? '#ccc' : props.theme.colors.primary.main)};
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => (props.disabled ? '#ccc' : props.theme.colors.primary.dark)};
  }
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: 32px;
`;

const BackButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.text.secondary};
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const CompleteButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 24px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

export default SummarySection;
