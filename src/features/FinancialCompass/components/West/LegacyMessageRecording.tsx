/**
 * Legacy Message Recording Component
 *
 * This component allows users to record and save legacy messages for their loved ones.
 */

import React, { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';

interface LegacyMessageRecordingProps {
  onComplete?: () => void;
  onBack?: () => void;
}

interface MessageData {
  id: string;
  title: string;
  recipient: string;
  audioBlob?: Blob;
  audioUrl?: string;
  textContent?: string;
  createdAt: Date;
  updatedAt: Date;
}

const LegacyMessageRecording: React.FC<LegacyMessageRecordingProps> = ({ onComplete, onBack }) => {
  const { theme } = useTheme();
  const { data, updateData } = useFinancialCompass();

  const [isSaving, setIsSaving] = useState(false);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);
  // Initialize messages with proper date conversion
  const initialMessages = Array.isArray(data.west?.legacyMessages)
    ? data.west.legacyMessages.map((msg) => ({
        ...msg,
        createdAt: typeof msg.createdAt === 'string' ? new Date(msg.createdAt) : msg.createdAt,
        updatedAt: typeof msg.updatedAt === 'string' ? new Date(msg.updatedAt) : msg.updatedAt,
      }))
    : [];

  const [messages, setMessages] = useState<MessageData[]>(initialMessages);
  const [activeMessage, setActiveMessage] = useState<MessageData | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }

      // Revoke any object URLs to prevent memory leaks
      messages.forEach((message) => {
        if (message.audioUrl && message.audioUrl.startsWith('blob:')) {
          URL.revokeObjectURL(message.audioUrl);
        }
      });
    };
  }, [messages]);

  // Create a new message
  const createNewMessage = () => {
    const newMessage: MessageData = {
      id: Date.now().toString(),
      title: 'Untitled Message',
      recipient: '',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    setMessages((prev) => [...prev, newMessage]);
    setActiveMessage(newMessage);
  };

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    if (activeMessage) {
      const updatedMessage = {
        ...activeMessage,
        [name]: value,
        updatedAt: new Date(),
      };

      setActiveMessage(updatedMessage);

      // Update the message in the messages array
      setMessages((prev) =>
        prev.map((msg) => (msg.id === activeMessage.id ? updatedMessage : msg))
      );
    }
  };

  // Start recording
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

      mediaRecorderRef.current = new MediaRecorder(stream);
      audioChunksRef.current = [];

      mediaRecorderRef.current.ondataavailable = (e) => {
        audioChunksRef.current.push(e.data);
      };

      mediaRecorderRef.current.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
        const audioUrl = URL.createObjectURL(audioBlob);

        if (activeMessage) {
          // Revoke previous URL if it exists
          if (activeMessage.audioUrl && activeMessage.audioUrl.startsWith('blob:')) {
            URL.revokeObjectURL(activeMessage.audioUrl);
          }

          const updatedMessage = {
            ...activeMessage,
            audioBlob,
            audioUrl,
            updatedAt: new Date(),
          };

          setActiveMessage(updatedMessage);

          // Update the message in the messages array
          setMessages((prev) =>
            prev.map((msg) => (msg.id === activeMessage.id ? updatedMessage : msg))
          );
        }

        // Stop all tracks on the stream
        stream.getTracks().forEach((track) => track.stop());
      };

      mediaRecorderRef.current.start();
      setIsRecording(true);

      // Start timer
      setRecordingTime(0);
      timerRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);
    } catch (error) {
      console.error('Error accessing microphone:', error);
      alert(
        'Error accessing microphone. Please make sure your microphone is connected and you have granted permission to use it.'
      );
    }
  };

  // Stop recording
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);

      // Stop timer
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }
  };

  // Play audio
  const playAudio = () => {
    if (activeMessage?.audioUrl && audioRef.current) {
      audioRef.current.src = activeMessage.audioUrl;
      audioRef.current.play();
      setIsPlaying(true);
    }
  };

  // Pause audio
  const pauseAudio = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      setIsPlaying(false);
    }
  };

  // Format time (seconds to MM:SS)
  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Delete a message
  const deleteMessage = (id: string) => {
    // Revoke URL if it exists
    const messageToDelete = messages.find((msg) => msg.id === id);
    if (messageToDelete?.audioUrl && messageToDelete.audioUrl.startsWith('blob:')) {
      URL.revokeObjectURL(messageToDelete.audioUrl);
    }

    setMessages((prev) => prev.filter((msg) => msg.id !== id));

    if (activeMessage?.id === id) {
      setActiveMessage(null);
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSave();

    if (onComplete) {
      onComplete();
    }
  };

  // Handle saving data
  const handleSave = () => {
    setIsSaving(true);

    // Update data in context
    updateData('west', 'legacyMessages', messages);

    // Show save indicator
    setShowSaveIndicator(true);

    // Hide save indicator after 2 seconds
    setTimeout(() => {
      setIsSaving(false);
      setShowSaveIndicator(false);
    }, 2000);
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Legacy Message Recording</Title>
        <Description theme={theme}>
          Record and save personal messages for your loved ones to access in the future.
        </Description>
      </Header>

      <form onSubmit={handleSubmit}>
        <ContentContainer>
          <MessageList theme={theme}>
            <MessageListHeader>
              <h3>Your Messages</h3>
              <NewMessageButton type="button" onClick={createNewMessage} theme={theme}>
                + New Message
              </NewMessageButton>
            </MessageListHeader>

            <MessageItems>
              {messages.length === 0 ? (
                <EmptyState>
                  <p>No messages yet. Click "New Message" to create one.</p>
                </EmptyState>
              ) : (
                messages.map((message) => (
                  <MessageItem
                    key={message.id}
                    isActive={activeMessage?.id === message.id}
                    onClick={() => setActiveMessage(message)}
                    theme={theme}
                  >
                    <MessageItemTitle>{message.title}</MessageItemTitle>
                    <MessageItemRecipient>
                      {message.recipient ? `For: ${message.recipient}` : 'No recipient specified'}
                    </MessageItemRecipient>
                    <MessageItemDate>
                      {new Date(message.updatedAt).toLocaleDateString()}
                    </MessageItemDate>
                    <DeleteButton
                      type="button"
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteMessage(message.id);
                      }}
                      theme={theme}
                    >
                      ×
                    </DeleteButton>
                  </MessageItem>
                ))
              )}
            </MessageItems>
          </MessageList>

          <MessageEditor theme={theme}>
            {activeMessage ? (
              <>
                <FormRow>
                  <FormField>
                    <Label htmlFor="title">Message Title</Label>
                    <Input
                      type="text"
                      id="title"
                      name="title"
                      value={activeMessage.title}
                      onChange={handleInputChange}
                      placeholder="Enter a title for your message"
                    />
                  </FormField>

                  <FormField>
                    <Label htmlFor="recipient">Recipient</Label>
                    <Input
                      type="text"
                      id="recipient"
                      name="recipient"
                      value={activeMessage.recipient}
                      onChange={handleInputChange}
                      placeholder="Who is this message for?"
                    />
                  </FormField>
                </FormRow>

                <FormRow>
                  <FormField>
                    <Label htmlFor="textContent">Written Message (Optional)</Label>
                    <TextArea
                      id="textContent"
                      name="textContent"
                      value={activeMessage.textContent || ''}
                      onChange={handleInputChange}
                      placeholder="Write your message here..."
                      rows={6}
                    />
                  </FormField>
                </FormRow>

                <AudioSection>
                  <Label>Audio Message</Label>

                  <AudioControls>
                    {isRecording ? (
                      <>
                        <RecordingIndicator />
                        <RecordingTime>{formatTime(recordingTime)}</RecordingTime>
                        <ControlButton type="button" onClick={stopRecording} theme={theme}>
                          Stop Recording
                        </ControlButton>
                      </>
                    ) : (
                      <>
                        <ControlButton type="button" onClick={startRecording} theme={theme}>
                          Start Recording
                        </ControlButton>

                        {activeMessage.audioUrl && (
                          <>
                            <ControlButton
                              type="button"
                              onClick={isPlaying ? pauseAudio : playAudio}
                              theme={theme}
                            >
                              {isPlaying ? 'Pause' : 'Play'}
                            </ControlButton>
                            <audio
                              ref={audioRef}
                              onEnded={() => setIsPlaying(false)}
                              style={{ display: 'none' }}
                            />
                          </>
                        )}
                      </>
                    )}
                  </AudioControls>

                  {activeMessage.audioUrl && !isRecording && (
                    <AudioWaveform theme={theme}>
                      <AudioWaveformBar />
                      <AudioWaveformBar />
                      <AudioWaveformBar />
                      <AudioWaveformBar />
                      <AudioWaveformBar />
                      <AudioWaveformBar />
                      <AudioWaveformBar />
                      <AudioWaveformBar />
                    </AudioWaveform>
                  )}
                </AudioSection>
              </>
            ) : (
              <EmptyState>
                <p>Select a message from the list or create a new one.</p>
              </EmptyState>
            )}
          </MessageEditor>
        </ContentContainer>

        <Disclaimer theme={theme}>
          <DisclaimerTitle>Important Note</DisclaimerTitle>
          <DisclaimerText>
            Legacy messages are stored locally on your device. For long-term preservation, consider
            exporting and storing these messages with your estate planning documents. Make sure your
            loved ones know how to access these messages when needed.
          </DisclaimerText>
        </Disclaimer>

        <ButtonContainer>
          {onBack && (
            <BackButton type="button" onClick={onBack} theme={theme}>
              Back
            </BackButton>
          )}

          <SaveIndicator visible={showSaveIndicator}>✓ Saved</SaveIndicator>

          <SubmitButton type="submit" theme={theme}>
            {isSaving ? 'Saving...' : 'Save & Continue'}
          </SubmitButton>
        </ButtonContainer>
      </form>
    </Container>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  padding: 24px;
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const Title = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const Description = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const ContentContainer = styled.div`
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 24px;
  margin-bottom: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const MessageList = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 16px;
  height: 500px;
  display: flex;
  flex-direction: column;
`;

const MessageListHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  h3 {
    margin: 0;
    font-size: 1.1rem;
  }
`;

const NewMessageButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 0.9rem;
  cursor: pointer;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

const MessageItems = styled.div`
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const MessageItem = styled.div<{ isActive: boolean; theme: any }>`
  padding: 12px;
  border-radius: 4px;
  background-color: ${(props) =>
    props.isActive ? props.theme.colors.primary.light : props.theme.colors.background.default};
  cursor: pointer;
  position: relative;

  &:hover {
    background-color: ${(props) =>
      props.isActive ? props.theme.colors.primary.light : props.theme.colors.action.hover};
  }
`;

const MessageItemTitle = styled.div`
  font-weight: 500;
  margin-bottom: 4px;
`;

const MessageItemRecipient = styled.div`
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 4px;
`;

const MessageItemDate = styled.div`
  font-size: 0.8rem;
  color: #999;
`;

const DeleteButton = styled.button<{ theme: any }>`
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: transparent;
  color: ${(props) => props.theme.colors.error.main};
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;

  &:hover {
    background-color: ${(props) => props.theme.colors.error.light};
  }
`;

const MessageEditor = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 24px;
  height: 500px;
  overflow-y: auto;
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
  text-align: center;
  padding: 0 24px;

  p {
    margin: 0;
    line-height: 1.5;
  }
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;

const FormField = styled.div`
  margin-bottom: 16px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
`;

const Input = styled.input`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  resize: vertical;
`;

const AudioSection = styled.div`
  margin-top: 24px;
`;

const AudioControls = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  margin-top: 8px;
`;

const ControlButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: pointer;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

const RecordingIndicator = styled.div`
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: red;
  animation: pulse 1.5s infinite;

  @keyframes pulse {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
    100% {
      opacity: 1;
    }
  }
`;

const RecordingTime = styled.div`
  font-family: monospace;
  font-size: 1rem;
`;

const AudioWaveform = styled.div<{ theme: any }>`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  height: 60px;
  margin-top: 16px;
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 4px;
  padding: 0 16px;
`;

const AudioWaveformBar = styled.div`
  width: 4px;
  height: 30%;
  background-color: #666;
  border-radius: 2px;
  animation: waveform 1.5s infinite ease-in-out;

  &:nth-child(1) {
    animation-delay: 0s;
    height: 40%;
  }
  &:nth-child(2) {
    animation-delay: 0.2s;
    height: 60%;
  }
  &:nth-child(3) {
    animation-delay: 0.4s;
    height: 80%;
  }
  &:nth-child(4) {
    animation-delay: 0.6s;
    height: 50%;
  }
  &:nth-child(5) {
    animation-delay: 0.8s;
    height: 70%;
  }
  &:nth-child(6) {
    animation-delay: 1s;
    height: 30%;
  }
  &:nth-child(7) {
    animation-delay: 1.2s;
    height: 50%;
  }
  &:nth-child(8) {
    animation-delay: 1.4s;
    height: 40%;
  }

  @keyframes waveform {
    0%,
    100% {
      transform: scaleY(1);
    }
    50% {
      transform: scaleY(0.5);
    }
  }
`;

const Disclaimer = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.warning.light};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
`;

const DisclaimerTitle = styled.h4`
  margin: 0 0 8px 0;
  font-size: 1rem;
  font-weight: 500;
  color: #856404;
`;

const DisclaimerText = styled.p`
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.5;
  color: #856404;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
`;

const BackButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.text.secondary};
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const SubmitButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 24px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  color: green;
  margin-right: 16px;
`;

export default LegacyMessageRecording;
