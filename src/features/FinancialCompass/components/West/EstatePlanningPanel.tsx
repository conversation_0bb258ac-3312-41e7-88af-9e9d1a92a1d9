import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Divider,
  TextField,
  Button,
  MenuItem,
  IconButton,
  List,
  ListItem,
  ListItemText,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';

const EstatePlanningPanel: React.FC = () => {
  const [hasWill, setHasWill] = useState('no');
  const [beneficiaries, setBeneficiaries] = useState<string[]>([]);
  const [newBeneficiary, setNewBeneficiary] = useState('');
  const [errors, setErrors] = useState<{ hasWill?: string; newBeneficiary?: string }>({});
  const [submitted, setSubmitted] = useState(false);

  const validate = () => {
    const newErrors: { hasWill?: string; newBeneficiary?: string } = {};
    if (!hasWill) {
      newErrors.hasWill = 'Please select will/trust status';
    }
    if (newBeneficiary && newBeneficiary.length < 2) {
      newErrors.newBeneficiary = 'Name must be at least 2 characters';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleAddBeneficiary = (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitted(true);
    if (validate() && newBeneficiary) {
      setBeneficiaries([...beneficiaries, newBeneficiary]);
      setNewBeneficiary('');
      setErrors({});
    }
  };

  const handleRemoveBeneficiary = (index: number) => {
    setBeneficiaries(beneficiaries.filter((_, i) => i !== index));
  };

  return (
    <Card variant="outlined">
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Estate Planning
        </Typography>
        <Divider sx={{ mb: 2 }} />
        <Box mb={2}>
          <Typography variant="body2" color="textSecondary">
            Manage your will/trust status and beneficiaries below.
          </Typography>
        </Box>
        <Box display="flex" gap={2} mb={2}>
          <TextField
            select
            label="Do you have a will or trust?"
            value={hasWill}
            onChange={(e) => setHasWill(e.target.value)}
            error={!!errors.hasWill && submitted}
            helperText={submitted && errors.hasWill}
          >
            <MenuItem value="yes">Yes</MenuItem>
            <MenuItem value="no">No</MenuItem>
          </TextField>
        </Box>
        <form onSubmit={handleAddBeneficiary} noValidate>
          <Box display="flex" gap={2} mb={2}>
            <TextField
              label="Add Beneficiary"
              value={newBeneficiary}
              onChange={(e) => setNewBeneficiary(e.target.value)}
              error={!!errors.newBeneficiary && submitted}
              helperText={submitted && errors.newBeneficiary}
            />
            <Button type="submit" variant="contained" color="primary">
              Add
            </Button>
          </Box>
        </form>
        <List>
          {beneficiaries.map((b, i) => (
            <ListItem
              key={i}
              secondaryAction={
                <IconButton
                  edge="end"
                  aria-label="delete"
                  onClick={() => handleRemoveBeneficiary(i)}
                >
                  <DeleteIcon />
                </IconButton>
              }
            >
              <ListItemText primary={b} />
            </ListItem>
          ))}
        </List>
      </CardContent>
    </Card>
  );
};

export default EstatePlanningPanel;
