/**
 * Legacy Planning Component
 *
 * This component helps users define their legacy goals and create
 * a legacy letter or ethical will.
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';

interface LegacyPlanningProps {
  onComplete?: () => void;
  onBack?: () => void;
}

interface LegacyGoal {
  id: string;
  goal: string;
  priority: 'high' | 'medium' | 'low';
  timeframe: 'short_term' | 'medium_term' | 'long_term';
  notes?: string;
}

interface LegacyPlanningData {
  hasLegacyPlan: boolean;
  legacyGoals: LegacyGoal[];
  legacyStatement: string;
  legacyLetter: string;
  legacyNotes: string;
}

const LegacyPlanning: React.FC<LegacyPlanningProps> = ({ onComplete, onBack }) => {
  const { theme } = useTheme();
  const { data, updateData } = useFinancialCompass();

  const [isSaving, setIsSaving] = useState(false);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Priority options
  const priorityOptions = [
    { value: 'high', label: 'High' },
    { value: 'medium', label: 'Medium' },
    { value: 'low', label: 'Low' },
  ];

  // Timeframe options
  const timeframeOptions = [
    { value: 'short_term', label: 'Short-term (1-5 years)' },
    { value: 'medium_term', label: 'Medium-term (5-10 years)' },
    { value: 'long_term', label: 'Long-term (10+ years)' },
  ];

  // Initialize form data from context or with defaults
  const [formData, setFormData] = useState<LegacyPlanningData>(
    data.west?.legacyPlanning || {
      hasLegacyPlan: false,
      legacyGoals: [],
      legacyStatement: '',
      legacyLetter: '',
      legacyNotes: '',
    }
  );

  // New legacy goal form
  const [newGoal, setNewGoal] = useState<Omit<LegacyGoal, 'id'>>({
    goal: '',
    priority: 'medium',
    timeframe: 'medium_term',
    notes: '',
  });

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: checked,
    }));
  };

  // Handle text input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle new goal field changes
  const handleNewGoalChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setNewGoal((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Add new legacy goal
  const handleAddGoal = () => {
    if (!newGoal.goal) return;

    const newGoalWithId: LegacyGoal = {
      ...newGoal,
      id: `goal-${Date.now()}`,
    };

    // Add new goal to the list
    setFormData((prev) => ({
      ...prev,
      legacyGoals: [...prev.legacyGoals, newGoalWithId],
    }));

    // Reset new goal form
    setNewGoal({
      goal: '',
      priority: 'medium',
      timeframe: 'medium_term',
      notes: '',
    });
  };

  // Remove legacy goal
  const handleRemoveGoal = (id: string) => {
    setFormData((prev) => ({
      ...prev,
      legacyGoals: prev.legacyGoals.filter((goal) => goal.id !== id),
    }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSave();

    if (onComplete) {
      onComplete();
    }
  };

  // Handle saving data
  const handleSave = () => {
    setIsSaving(true);

    // Update data in context
    updateData('west', 'legacyPlanning', formData);

    // Show save indicator
    setShowSaveIndicator(true);

    // Hide save indicator after 2 seconds
    setTimeout(() => {
      setIsSaving(false);
      setShowSaveIndicator(false);
    }, 2000);
  };

  // Get priority color
  const getPriorityColor = (priority: LegacyGoal['priority'], theme: any): string => {
    switch (priority) {
      case 'high':
        return theme.colors.error.main;
      case 'medium':
        return theme.colors.warning.main;
      case 'low':
        return theme.colors.success.main;
      default:
        return theme.colors.text.secondary;
    }
  };

  // Get timeframe label
  const getTimeframeLabel = (timeframe: LegacyGoal['timeframe']): string => {
    switch (timeframe) {
      case 'short_term':
        return 'Short-term (1-5 years)';
      case 'medium_term':
        return 'Medium-term (5-10 years)';
      case 'long_term':
        return 'Long-term (10+ years)';
      default:
        return 'Unknown';
    }
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Legacy Planning</Title>
        <Description theme={theme}>
          Define your legacy goals and create a legacy letter or ethical will.
        </Description>
      </Header>

      <form onSubmit={handleSubmit}>
        <FormSection>
          <SectionTitle>Legacy Plan</SectionTitle>

          <CheckboxField>
            <Checkbox
              type="checkbox"
              id="hasLegacyPlan"
              name="hasLegacyPlan"
              checked={formData.hasLegacyPlan}
              onChange={handleCheckboxChange}
            />
            <CheckboxLabel htmlFor="hasLegacyPlan">I have a legacy plan</CheckboxLabel>
          </CheckboxField>

          <FormField>
            <Label htmlFor="legacyStatement">Legacy Statement</Label>
            <Textarea
              id="legacyStatement"
              name="legacyStatement"
              value={formData.legacyStatement}
              onChange={handleInputChange}
              placeholder="Write a brief statement about how you want to be remembered"
              rows={3}
            />
            <FieldHint>
              A concise statement that captures the essence of your desired legacy
            </FieldHint>
          </FormField>
        </FormSection>

        <FormSection>
          <SectionTitle>Legacy Goals</SectionTitle>

          {formData.legacyGoals.length > 0 ? (
            <GoalsList>
              {formData.legacyGoals.map((goal) => (
                <GoalItem key={goal.id} theme={theme}>
                  <GoalHeader>
                    <GoalText>{goal.goal}</GoalText>
                    <PriorityBadge priority={goal.priority} theme={theme}>
                      {goal.priority.charAt(0).toUpperCase() + goal.priority.slice(1)} Priority
                    </PriorityBadge>
                    <RemoveButton
                      onClick={() => handleRemoveGoal(goal.id)}
                      theme={theme}
                      type="button"
                    >
                      Remove
                    </RemoveButton>
                  </GoalHeader>

                  <GoalDetails>
                    <GoalDetail>
                      <DetailLabel>Timeframe:</DetailLabel>
                      <DetailValue>{getTimeframeLabel(goal.timeframe)}</DetailValue>
                    </GoalDetail>

                    {goal.notes && <GoalNotes>{goal.notes}</GoalNotes>}
                  </GoalDetails>
                </GoalItem>
              ))}
            </GoalsList>
          ) : (
            <EmptyState theme={theme}>
              <EmptyIcon>🎯</EmptyIcon>
              <EmptyText>
                You haven't added any legacy goals yet. Use the form below to add your first goal.
              </EmptyText>
            </EmptyState>
          )}

          <AddGoalForm theme={theme}>
            <FormField>
              <Label htmlFor="goal">Legacy Goal</Label>
              <Input
                type="text"
                id="goal"
                name="goal"
                value={newGoal.goal}
                onChange={handleNewGoalChange}
                placeholder="e.g., Establish a scholarship fund"
                required
              />
            </FormField>

            <FormRow>
              <FormField>
                <Label htmlFor="priority">Priority</Label>
                <Select
                  id="priority"
                  name="priority"
                  value={newGoal.priority}
                  onChange={handleNewGoalChange}
                >
                  {priorityOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </Select>
              </FormField>

              <FormField>
                <Label htmlFor="timeframe">Timeframe</Label>
                <Select
                  id="timeframe"
                  name="timeframe"
                  value={newGoal.timeframe}
                  onChange={handleNewGoalChange}
                >
                  {timeframeOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </Select>
              </FormField>
            </FormRow>

            <FormField>
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                name="notes"
                value={newGoal.notes}
                onChange={handleNewGoalChange}
                placeholder="Additional details about this legacy goal"
              />
            </FormField>

            <AddButton type="button" onClick={handleAddGoal} disabled={!newGoal.goal} theme={theme}>
              Add Goal
            </AddButton>
          </AddGoalForm>
        </FormSection>

        <FormSection>
          <SectionTitle>Legacy Letter / Ethical Will</SectionTitle>

          <InfoBox theme={theme}>
            <InfoIcon>ℹ️</InfoIcon>
            <InfoText>
              A legacy letter or ethical will is a way to share your values, life lessons, hopes,
              and wishes for the future with your loved ones. Unlike a legal will, it's not about
              distributing material possessions, but about passing on your wisdom, beliefs, and
              blessings.
            </InfoText>
          </InfoBox>

          <FormField>
            <Label htmlFor="legacyLetter">Your Legacy Letter</Label>
            <Textarea
              id="legacyLetter"
              name="legacyLetter"
              value={formData.legacyLetter}
              onChange={handleInputChange}
              placeholder="Write your legacy letter or ethical will here..."
              rows={10}
            />
            <FieldHint>
              Consider including your values, life lessons, family stories, hopes for future
              generations, and expressions of love and gratitude
            </FieldHint>
          </FormField>
        </FormSection>

        <FormSection>
          <SectionTitle>Additional Notes</SectionTitle>
          <FormField>
            <Label htmlFor="legacyNotes">Notes about your legacy plan</Label>
            <Textarea
              id="legacyNotes"
              name="legacyNotes"
              value={formData.legacyNotes}
              onChange={handleInputChange}
              placeholder="Add any additional notes about your legacy planning"
            />
          </FormField>
        </FormSection>

        <ButtonContainer>
          {onBack && (
            <BackButton type="button" onClick={onBack} theme={theme}>
              Back
            </BackButton>
          )}

          <SaveIndicator visible={showSaveIndicator}>✓ Saved</SaveIndicator>

          <SubmitButton type="submit" theme={theme}>
            {isSaving ? 'Saving...' : 'Save & Continue'}
          </SubmitButton>
        </ButtonContainer>
      </form>
    </Container>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  padding: 24px;
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const Title = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const Description = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const FormSection = styled.div`
  margin-bottom: 32px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;

const FormField = styled.div`
  margin-bottom: 16px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
`;

const Input = styled.input`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
`;

const Select = styled.select`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
`;

const Textarea = styled.textarea`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  min-height: 80px;
  resize: vertical;
`;

const FieldHint = styled.div`
  font-size: 0.8rem;
  color: #666;
  margin-top: 4px;
`;

const CheckboxField = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 16px;
`;

const Checkbox = styled.input`
  margin-right: 8px;
`;

const CheckboxLabel = styled.label`
  font-size: 1rem;
`;

const InfoBox = styled.div<{ theme: any }>`
  display: flex;
  align-items: flex-start;
  padding: 16px;
  background-color: ${(props) => props.theme.colors.info.light};
  border-radius: 4px;
  margin-bottom: 16px;
`;

const InfoIcon = styled.div`
  margin-right: 12px;
  font-size: 1.2rem;
`;

const InfoText = styled.p`
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.5;
`;

const GoalsList = styled.div`
  margin-bottom: 24px;
`;

const GoalItem = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
`;

const GoalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const GoalText = styled.div`
  font-weight: 600;
  font-size: 1.1rem;
`;

const PriorityBadge = styled.div<{ priority: string; theme: any }>`
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  background-color: ${(props) => {
    // Inline priority color logic
    let color;
    switch (props.priority) {
      case 'high':
        color = props.theme.colors.error.main;
        break;
      case 'medium':
        color = props.theme.colors.warning.main;
        break;
      case 'low':
        color = props.theme.colors.success.main;
        break;
      default:
        color = props.theme.colors.text.secondary;
    }
    return color + '20'; // Add 20% opacity
  }};
  color: ${(props) => {
    // Inline priority color logic
    switch (props.priority) {
      case 'high':
        return props.theme.colors.error.main;
      case 'medium':
        return props.theme.colors.warning.main;
      case 'low':
        return props.theme.colors.success.main;
      default:
        return props.theme.colors.text.secondary;
    }
  }};
`;

const RemoveButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.error.main};
  border: none;
  font-size: 0.9rem;
  cursor: pointer;
  padding: 4px 8px;

  &:hover {
    text-decoration: underline;
  }
`;

const GoalDetails = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
`;

const GoalDetail = styled.div`
  margin-bottom: 8px;
`;

const DetailLabel = styled.div`
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 4px;
`;

const DetailValue = styled.div`
  font-size: 0.9rem;
`;

const GoalNotes = styled.div`
  grid-column: 1 / -1;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #eee;
  font-style: italic;
  font-size: 0.9rem;
`;

const EmptyState = styled.div<{ theme: any }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  margin-bottom: 24px;
`;

const EmptyIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
`;

const EmptyText = styled.p`
  text-align: center;
  color: #666;
  max-width: 400px;
`;

const AddGoalForm = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  padding: 16px;
`;

const AddButton = styled.button<{ theme: any; disabled?: boolean }>`
  background-color: ${(props) =>
    props.disabled
      ? props.theme.colors.action.disabledBackground
      : props.theme.colors.primary.main};
  color: ${(props) =>
    props.disabled ? props.theme.colors.action.disabled : props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) =>
      props.disabled
        ? props.theme.colors.action.disabledBackground
        : props.theme.colors.primary.dark};
  }
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
`;

const BackButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.text.secondary};
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const SubmitButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 24px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  color: green;
  margin-right: 16px;
`;

export default LegacyPlanning;
