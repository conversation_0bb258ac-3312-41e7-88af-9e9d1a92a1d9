/**
 * Values & Goals Component
 *
 * This component helps users identify their core values and life goals.
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';

interface ValuesGoalsProps {
  onComplete?: () => void;
  onBack?: () => void;
}

interface CoreValue {
  id: string;
  value: string;
  description: string;
  importance: number; // 1-10
}

interface LifeGoal {
  id: string;
  goal: string;
  category: string;
  targetDate?: string;
  status: 'not_started' | 'in_progress' | 'completed';
  notes?: string;
}

interface ValuesGoalsData {
  coreValues: CoreValue[];
  lifeGoals: LifeGoal[];
  personalMission: string;
  valuesNotes: string;
}

const ValuesGoals: React.FC<ValuesGoalsProps> = ({ onComplete, onBack }) => {
  const { theme } = useTheme();
  const { data, updateData } = useFinancialCompass();

  const [isSaving, setIsSaving] = useState(false);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Goal categories
  const goalCategories = [
    'Family',
    'Career',
    'Financial',
    'Health',
    'Personal Growth',
    'Spiritual',
    'Community',
    'Travel',
    'Education',
    'Other',
  ];

  // Goal status options
  const statusOptions = [
    { value: 'not_started', label: 'Not Started' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'completed', label: 'Completed' },
  ];

  // Initialize form data from context or with defaults
  const [formData, setFormData] = useState<ValuesGoalsData>(
    data.west?.valuesGoals || {
      coreValues: [],
      lifeGoals: [],
      personalMission: '',
      valuesNotes: '',
    }
  );

  // New core value form
  const [newValue, setNewValue] = useState<Omit<CoreValue, 'id'>>({
    value: '',
    description: '',
    importance: 8,
  });

  // New life goal form
  const [newGoal, setNewGoal] = useState<Omit<LifeGoal, 'id'>>({
    goal: '',
    category: '',
    targetDate: '',
    status: 'not_started',
    notes: '',
  });

  // Handle text input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle new value field changes
  const handleNewValueChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setNewValue((prev) => ({
      ...prev,
      [name]: name === 'importance' ? parseInt(value) : value,
    }));
  };

  // Add new core value
  const handleAddValue = () => {
    if (!newValue.value) return;

    const newValueWithId: CoreValue = {
      ...newValue,
      id: `value-${Date.now()}`,
    };

    // Add new value to the list
    setFormData((prev) => ({
      ...prev,
      coreValues: [...prev.coreValues, newValueWithId],
    }));

    // Reset new value form
    setNewValue({
      value: '',
      description: '',
      importance: 8,
    });
  };

  // Remove core value
  const handleRemoveValue = (id: string) => {
    setFormData((prev) => ({
      ...prev,
      coreValues: prev.coreValues.filter((value) => value.id !== id),
    }));
  };

  // Handle new goal field changes
  const handleNewGoalChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setNewGoal((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Add new life goal
  const handleAddGoal = () => {
    if (!newGoal.goal || !newGoal.category) return;

    const newGoalWithId: LifeGoal = {
      ...newGoal,
      id: `goal-${Date.now()}`,
    };

    // Add new goal to the list
    setFormData((prev) => ({
      ...prev,
      lifeGoals: [...prev.lifeGoals, newGoalWithId],
    }));

    // Reset new goal form
    setNewGoal({
      goal: '',
      category: '',
      targetDate: '',
      status: 'not_started',
      notes: '',
    });
  };

  // Remove life goal
  const handleRemoveGoal = (id: string) => {
    setFormData((prev) => ({
      ...prev,
      lifeGoals: prev.lifeGoals.filter((goal) => goal.id !== id),
    }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSave();

    if (onComplete) {
      onComplete();
    }
  };

  // Handle saving data
  const handleSave = () => {
    setIsSaving(true);

    // Update data in context
    updateData('west', 'valuesGoals', formData);

    // Show save indicator
    setShowSaveIndicator(true);

    // Hide save indicator after 2 seconds
    setTimeout(() => {
      setIsSaving(false);
      setShowSaveIndicator(false);
    }, 2000);
  };

  // Get status color
  const getStatusColor = (status: LifeGoal['status'], theme: any): string => {
    switch (status) {
      case 'completed':
        return theme.colors.success.main;
      case 'in_progress':
        return theme.colors.warning.main;
      case 'not_started':
        return theme.colors.error.main;
      default:
        return theme.colors.text.secondary;
    }
  };

  // Get status label
  const getStatusLabel = (status: LifeGoal['status']): string => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'in_progress':
        return 'In Progress';
      case 'not_started':
        return 'Not Started';
      default:
        return 'Unknown';
    }
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Values & Goals</Title>
        <Description theme={theme}>
          Identify your core values and define your life goals.
        </Description>
      </Header>

      <form onSubmit={handleSubmit}>
        <FormSection>
          <SectionTitle>Personal Mission Statement</SectionTitle>

          <FormField>
            <Label htmlFor="personalMission">Your Mission Statement</Label>
            <Textarea
              id="personalMission"
              name="personalMission"
              value={formData.personalMission}
              onChange={handleInputChange}
              placeholder="Write a brief statement that captures your purpose and what you stand for"
              rows={3}
            />
            <FieldHint>A concise statement that guides your decisions and actions</FieldHint>
          </FormField>
        </FormSection>

        <FormSection>
          <SectionTitle>Core Values</SectionTitle>

          {formData.coreValues.length > 0 ? (
            <ValuesList>
              {formData.coreValues.map((value) => (
                <ValueItem key={value.id} theme={theme}>
                  <ValueHeader>
                    <ValueName>{value.value}</ValueName>
                    <ImportanceRating>
                      Importance: <ImportanceValue>{value.importance}/10</ImportanceValue>
                    </ImportanceRating>
                    <RemoveButton
                      onClick={() => handleRemoveValue(value.id)}
                      theme={theme}
                      type="button"
                    >
                      Remove
                    </RemoveButton>
                  </ValueHeader>

                  {value.description && <ValueDescription>{value.description}</ValueDescription>}
                </ValueItem>
              ))}
            </ValuesList>
          ) : (
            <EmptyState theme={theme}>
              <EmptyIcon>💎</EmptyIcon>
              <EmptyText>
                You haven't added any core values yet. Use the form below to add your first value.
              </EmptyText>
            </EmptyState>
          )}

          <AddValueForm theme={theme}>
            <FormRow>
              <FormField>
                <Label htmlFor="value">Core Value</Label>
                <Input
                  type="text"
                  id="value"
                  name="value"
                  value={newValue.value}
                  onChange={handleNewValueChange}
                  placeholder="e.g., Integrity, Family, Freedom"
                  required
                />
              </FormField>

              <FormField>
                <Label htmlFor="importance">Importance (1-10)</Label>
                <Input
                  type="number"
                  id="importance"
                  name="importance"
                  value={newValue.importance}
                  onChange={handleNewValueChange}
                  min="1"
                  max="10"
                />
              </FormField>
            </FormRow>

            <FormField>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                value={newValue.description}
                onChange={handleNewValueChange}
                placeholder="Describe what this value means to you"
              />
            </FormField>

            <AddButton
              type="button"
              onClick={handleAddValue}
              disabled={!newValue.value}
              theme={theme}
            >
              Add Value
            </AddButton>
          </AddValueForm>
        </FormSection>

        <FormSection>
          <SectionTitle>Life Goals</SectionTitle>

          {formData.lifeGoals.length > 0 ? (
            <GoalsList>
              {formData.lifeGoals.map((goal) => (
                <GoalItem key={goal.id} theme={theme}>
                  <GoalHeader>
                    <GoalText>{goal.goal}</GoalText>
                    <GoalCategory>{goal.category}</GoalCategory>
                    <StatusBadge status={goal.status} theme={theme}>
                      {getStatusLabel(goal.status)}
                    </StatusBadge>
                    <RemoveButton
                      onClick={() => handleRemoveGoal(goal.id)}
                      theme={theme}
                      type="button"
                    >
                      Remove
                    </RemoveButton>
                  </GoalHeader>

                  <GoalDetails>
                    {goal.targetDate && (
                      <GoalDetail>
                        <DetailLabel>Target Date:</DetailLabel>
                        <DetailValue>{goal.targetDate}</DetailValue>
                      </GoalDetail>
                    )}

                    {goal.notes && <GoalNotes>{goal.notes}</GoalNotes>}
                  </GoalDetails>
                </GoalItem>
              ))}
            </GoalsList>
          ) : (
            <EmptyState theme={theme}>
              <EmptyIcon>🎯</EmptyIcon>
              <EmptyText>
                You haven't added any life goals yet. Use the form below to add your first goal.
              </EmptyText>
            </EmptyState>
          )}

          <AddGoalForm theme={theme}>
            <FormRow>
              <FormField>
                <Label htmlFor="goal">Life Goal</Label>
                <Input
                  type="text"
                  id="goal"
                  name="goal"
                  value={newGoal.goal}
                  onChange={handleNewGoalChange}
                  placeholder="e.g., Run a marathon"
                  required
                />
              </FormField>

              <FormField>
                <Label htmlFor="category">Category</Label>
                <Select
                  id="category"
                  name="category"
                  value={newGoal.category}
                  onChange={handleNewGoalChange}
                  required
                >
                  <option value="">Select category...</option>
                  {goalCategories.map((category) => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </Select>
              </FormField>
            </FormRow>

            <FormRow>
              <FormField>
                <Label htmlFor="targetDate">Target Date</Label>
                <Input
                  type="date"
                  id="targetDate"
                  name="targetDate"
                  value={newGoal.targetDate}
                  onChange={handleNewGoalChange}
                />
              </FormField>

              <FormField>
                <Label htmlFor="status">Status</Label>
                <Select
                  id="status"
                  name="status"
                  value={newGoal.status}
                  onChange={handleNewGoalChange}
                >
                  {statusOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </Select>
              </FormField>
            </FormRow>

            <FormField>
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                name="notes"
                value={newGoal.notes}
                onChange={handleNewGoalChange}
                placeholder="Additional details about this life goal"
              />
            </FormField>

            <AddButton
              type="button"
              onClick={handleAddGoal}
              disabled={!newGoal.goal || !newGoal.category}
              theme={theme}
            >
              Add Goal
            </AddButton>
          </AddGoalForm>
        </FormSection>

        <FormSection>
          <SectionTitle>Additional Notes</SectionTitle>
          <FormField>
            <Label htmlFor="valuesNotes">Notes about your values and goals</Label>
            <Textarea
              id="valuesNotes"
              name="valuesNotes"
              value={formData.valuesNotes}
              onChange={handleInputChange}
              placeholder="Add any additional notes about your values and goals"
            />
          </FormField>
        </FormSection>

        <ButtonContainer>
          {onBack && (
            <BackButton type="button" onClick={onBack} theme={theme}>
              Back
            </BackButton>
          )}

          <SaveIndicator visible={showSaveIndicator}>✓ Saved</SaveIndicator>

          <SubmitButton type="submit" theme={theme}>
            {isSaving ? 'Saving...' : 'Save & Continue'}
          </SubmitButton>
        </ButtonContainer>
      </form>
    </Container>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  padding: 24px;
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const Title = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const Description = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const FormSection = styled.div`
  margin-bottom: 32px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;

const FormField = styled.div`
  margin-bottom: 16px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
`;

const Input = styled.input`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
`;

const Select = styled.select`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
`;

const Textarea = styled.textarea`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  min-height: 80px;
  resize: vertical;
`;

const FieldHint = styled.div`
  font-size: 0.8rem;
  color: #666;
  margin-top: 4px;
`;

const ValuesList = styled.div`
  margin-bottom: 24px;
`;

const ValueItem = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
`;

const ValueHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const ValueName = styled.div`
  font-weight: 600;
  font-size: 1.1rem;
`;

const ImportanceRating = styled.div`
  font-size: 0.9rem;
  color: #666;
`;

const ImportanceValue = styled.span`
  font-weight: 600;
`;

const ValueDescription = styled.div`
  font-size: 0.95rem;
  line-height: 1.5;
  color: #333;
`;

const RemoveButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.error.main};
  border: none;
  font-size: 0.9rem;
  cursor: pointer;
  padding: 4px 8px;

  &:hover {
    text-decoration: underline;
  }
`;

const EmptyState = styled.div<{ theme: any }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  margin-bottom: 24px;
`;

const EmptyIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
`;

const EmptyText = styled.p`
  text-align: center;
  color: #666;
  max-width: 400px;
`;

const AddValueForm = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  padding: 16px;
`;

const AddButton = styled.button<{ theme: any; disabled?: boolean }>`
  background-color: ${(props) =>
    props.disabled
      ? props.theme.colors.action.disabledBackground
      : props.theme.colors.primary.main};
  color: ${(props) =>
    props.disabled ? props.theme.colors.action.disabled : props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) =>
      props.disabled
        ? props.theme.colors.action.disabledBackground
        : props.theme.colors.primary.dark};
  }
`;

const GoalsList = styled.div`
  margin-bottom: 24px;
`;

const GoalItem = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
`;

const GoalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const GoalText = styled.div`
  font-weight: 600;
  font-size: 1.1rem;
`;

const GoalCategory = styled.div`
  font-style: italic;
  color: #666;
`;

const StatusBadge = styled.div<{ status: LifeGoal['status']; theme: any }>`
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  background-color: ${(props) => {
    // Use the existing getStatusColor function from the component
    const color = (() => {
      switch (props.status) {
        case 'completed':
          return props.theme.colors.success.main;
        case 'in_progress':
          return props.theme.colors.warning.main;
        case 'not_started':
          return props.theme.colors.error.main;
        default:
          return props.theme.colors.text.secondary;
      }
    })();
    return color + '20'; // Add 20% opacity
  }};
  color: ${(props) => {
    switch (props.status) {
      case 'completed':
        return props.theme.colors.success.main;
      case 'in_progress':
        return props.theme.colors.warning.main;
      case 'not_started':
        return props.theme.colors.error.main;
      default:
        return props.theme.colors.text.secondary;
    }
  }};
`;

const GoalDetails = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
`;

const GoalDetail = styled.div`
  margin-bottom: 8px;
`;

const DetailLabel = styled.div`
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 4px;
`;

const DetailValue = styled.div`
  font-size: 0.9rem;
`;

const GoalNotes = styled.div`
  grid-column: 1 / -1;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #eee;
  font-style: italic;
  font-size: 0.9rem;
`;

const AddGoalForm = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  padding: 16px;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
`;

const BackButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.text.secondary};
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const SubmitButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 24px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  color: green;
  margin-right: 16px;
`;

export default ValuesGoals;
