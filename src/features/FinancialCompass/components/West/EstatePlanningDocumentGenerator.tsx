/**
 * Estate Planning Document Generator Component
 *
 * This component helps users generate basic estate planning documents
 * and export them as PDFs.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { generatePDF } from '../../../../utils/enhancedPdfExport';

interface EstatePlanningDocumentGeneratorProps {
  onComplete?: () => void;
  onBack?: () => void;
}

interface DocumentData {
  // Personal Information
  fullName: string;
  address: string;
  phone: string;
  email: string;
  dateOfBirth: string;

  // Will Information
  executor: string;
  alternateExecutor: string;
  beneficiaries: {
    name: string;
    relationship: string;
    percentage: string;
  }[];
  guardianForMinors: string;
  specificBequests: {
    item: string;
    recipient: string;
  }[];

  // Healthcare Directive Information
  healthcareAgent: string;
  alternateHealthcareAgent: string;
  lifeSupportPreferences: string;
  painManagementPreferences: string;
  organDonationPreferences: string;

  // Power of Attorney Information
  attorneyInFact: string;
  alternateAttorneyInFact: string;
  powersGranted: string[];
  effectiveDate: 'immediate' | 'incapacity';

  // Letter of Intent
  letterContent: string;
}

const defaultBeneficiaries = [{ name: '', relationship: '', percentage: '' }];

const defaultSpecificBequests = [{ item: '', recipient: '' }];

const defaultPowersGranted = [
  'Real Estate Transactions',
  'Financial Institution Transactions',
  'Tax Matters',
  'Insurance Transactions',
  'Business Operations',
  'Gift Making',
];

const EstatePlanningDocumentGenerator: React.FC<EstatePlanningDocumentGeneratorProps> = ({
  onComplete,
  onBack,
}) => {
  const { theme } = useTheme();
  const { data, updateData } = useFinancialCompass();

  const [isSaving, setIsSaving] = useState(false);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);
  const [activeTab, setActiveTab] = useState<'personal' | 'will' | 'healthcare' | 'poa' | 'letter'>(
    'personal'
  );
  const [isGenerating, setIsGenerating] = useState(false);

  // Get personal information safely
  const personalInfo = data.north?.personalInfo || {};

  // Safely access personal information fields
  const fullName =
    typeof personalInfo === 'object' && 'fullName' in personalInfo
      ? (personalInfo.fullName as string)
      : '';
  const address =
    typeof personalInfo === 'object' && 'address' in personalInfo
      ? (personalInfo.address as string)
      : '';
  const phone =
    typeof personalInfo === 'object' && 'phone' in personalInfo
      ? (personalInfo.phone as string)
      : '';
  const email =
    typeof personalInfo === 'object' && 'email' in personalInfo
      ? (personalInfo.email as string)
      : '';
  const dateOfBirth =
    typeof personalInfo === 'object' && 'dateOfBirth' in personalInfo
      ? (personalInfo.dateOfBirth as string)
      : '';

  // Initialize form data from context or with defaults
  const [formData, setFormData] = useState<DocumentData>(
    data.west?.estatePlanningDocuments || {
      // Personal Information
      fullName: fullName,
      address: address,
      phone: phone,
      email: email,
      dateOfBirth: dateOfBirth,

      // Will Information
      executor: '',
      alternateExecutor: '',
      beneficiaries: defaultBeneficiaries,
      guardianForMinors: '',
      specificBequests: defaultSpecificBequests,

      // Healthcare Directive Information
      healthcareAgent: '',
      alternateHealthcareAgent: '',
      lifeSupportPreferences: '',
      painManagementPreferences: '',
      organDonationPreferences: '',

      // Power of Attorney Information
      attorneyInFact: '',
      alternateAttorneyInFact: '',
      powersGranted: defaultPowersGranted,
      effectiveDate: 'incapacity',

      // Letter of Intent
      letterContent: '',
    }
  );

  // Handle input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;

    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle checkbox changes for powers granted
  const handlePowerChange = (power: string) => {
    setFormData((prev) => {
      const currentPowers = [...prev.powersGranted];

      if (currentPowers.includes(power)) {
        return {
          ...prev,
          powersGranted: currentPowers.filter((p) => p !== power),
        };
      } else {
        return {
          ...prev,
          powersGranted: [...currentPowers, power],
        };
      }
    });
  };

  // Handle beneficiary changes
  const handleBeneficiaryChange = (index: number, field: string, value: string) => {
    setFormData((prev) => {
      const updatedBeneficiaries = [...prev.beneficiaries];
      updatedBeneficiaries[index] = {
        ...updatedBeneficiaries[index],
        [field]: value,
      };

      return {
        ...prev,
        beneficiaries: updatedBeneficiaries,
      };
    });
  };

  // Add a new beneficiary
  const addBeneficiary = () => {
    setFormData((prev) => ({
      ...prev,
      beneficiaries: [...prev.beneficiaries, { name: '', relationship: '', percentage: '' }],
    }));
  };

  // Remove a beneficiary
  const removeBeneficiary = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      beneficiaries: prev.beneficiaries.filter((_, i) => i !== index),
    }));
  };

  // Handle specific bequest changes
  const handleBequestChange = (index: number, field: string, value: string) => {
    setFormData((prev) => {
      const updatedBequests = [...prev.specificBequests];
      updatedBequests[index] = {
        ...updatedBequests[index],
        [field]: value,
      };

      return {
        ...prev,
        specificBequests: updatedBequests,
      };
    });
  };

  // Add a new specific bequest
  const addBequest = () => {
    setFormData((prev) => ({
      ...prev,
      specificBequests: [...prev.specificBequests, { item: '', recipient: '' }],
    }));
  };

  // Remove a specific bequest
  const removeBequest = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      specificBequests: prev.specificBequests.filter((_, i) => i !== index),
    }));
  };

  // Generate PDF documents
  const generateDocuments = async () => {
    setIsGenerating(true);

    try {
      // Generate Last Will and Testament
      await generatePDF({
        title: 'Last Will and Testament',
        filename: 'last_will_and_testament.pdf',
        content: generateWillContent(),
        headerText: 'LAST WILL AND TESTAMENT',
        footerText: `Generated for ${formData.fullName} on ${new Date().toLocaleDateString()}`,
      });

      // Generate Healthcare Directive
      await generatePDF({
        title: 'Healthcare Directive',
        filename: 'healthcare_directive.pdf',
        content: generateHealthcareDirectiveContent(),
        headerText: 'HEALTHCARE DIRECTIVE',
        footerText: `Generated for ${formData.fullName} on ${new Date().toLocaleDateString()}`,
      });

      // Generate Power of Attorney
      await generatePDF({
        title: 'Durable Power of Attorney',
        filename: 'power_of_attorney.pdf',
        content: generatePowerOfAttorneyContent(),
        headerText: 'DURABLE POWER OF ATTORNEY',
        footerText: `Generated for ${formData.fullName} on ${new Date().toLocaleDateString()}`,
      });

      // Generate Letter of Intent
      await generatePDF({
        title: 'Letter of Intent',
        filename: 'letter_of_intent.pdf',
        content: generateLetterOfIntentContent(),
        headerText: 'LETTER OF INTENT',
        footerText: `Generated for ${formData.fullName} on ${new Date().toLocaleDateString()}`,
      });
    } catch (error) {
      console.error('Error generating documents:', error);
    }

    setIsGenerating(false);
  };

  // Generate content for Last Will and Testament
  const generateWillContent = (): string => {
    return `
      <h1>LAST WILL AND TESTAMENT OF ${formData.fullName.toUpperCase()}</h1>

      <p>I, ${formData.fullName}, a resident of ${formData.address}, being of sound mind, declare this to be my Last Will and Testament.</p>

      <h2>ARTICLE I: REVOCATION OF PRIOR WILLS</h2>
      <p>I hereby revoke all prior wills and codicils made by me.</p>

      <h2>ARTICLE II: APPOINTMENT OF EXECUTOR</h2>
      <p>I appoint ${formData.executor} as Executor of my estate. If ${formData.executor} is unable or unwilling to serve, I appoint ${formData.alternateExecutor} as alternate Executor.</p>

      <h2>ARTICLE III: DISTRIBUTION OF ESTATE</h2>
      <p>I direct that my estate be distributed as follows:</p>
      <ul>
        ${formData.beneficiaries
          .map(
            (b) => `<li>${b.name} (${b.relationship}): ${b.percentage}% of my residuary estate</li>`
          )
          .join('')}
      </ul>

      ${
        formData.specificBequests.length > 0
          ? `
        <h2>ARTICLE IV: SPECIFIC BEQUESTS</h2>
        <p>I make the following specific bequests:</p>
        <ul>
          ${formData.specificBequests
            .map((b) => `<li>I give ${b.item} to ${b.recipient}</li>`)
            .join('')}
        </ul>
      `
          : ''
      }

      ${
        formData.guardianForMinors
          ? `
        <h2>ARTICLE V: GUARDIAN FOR MINOR CHILDREN</h2>
        <p>If I have any minor children at the time of my death, I appoint ${formData.guardianForMinors} as guardian of the person and property of such minor children.</p>
      `
          : ''
      }

      <h2>ARTICLE ${formData.specificBequests.length > 0 ? 'VI' : 'V'}: MISCELLANEOUS PROVISIONS</h2>
      <p>If any provision of this Will is held invalid, the other provisions shall remain in effect.</p>

      <p>IN WITNESS WHEREOF, I have signed this Will on _________________, 20____.</p>

      <p>____________________________<br>${formData.fullName}</p>

      <p>WITNESSES:<br><br>
      ____________________________<br>
      Name: ______________________<br>
      Address: ____________________<br><br>

      ____________________________<br>
      Name: ______________________<br>
      Address: ____________________<br>
      </p>
    `;
  };

  // Generate content for Healthcare Directive
  const generateHealthcareDirectiveContent = (): string => {
    return `
      <h1>HEALTHCARE DIRECTIVE OF ${formData.fullName.toUpperCase()}</h1>

      <p>I, ${formData.fullName}, a resident of ${formData.address}, being of sound mind, make this Healthcare Directive to provide instructions about my healthcare in the event I cannot make or communicate my own decisions.</p>

      <h2>APPOINTMENT OF HEALTHCARE AGENT</h2>
      <p>I appoint ${formData.healthcareAgent} as my healthcare agent. If ${formData.healthcareAgent} is unable or unwilling to serve, I appoint ${formData.alternateHealthcareAgent} as my alternate healthcare agent.</p>

      <h2>POWERS OF HEALTHCARE AGENT</h2>
      <p>My healthcare agent has full authority to make healthcare decisions for me, including decisions about life-sustaining treatment, when I cannot make or communicate my own decisions.</p>

      <h2>LIFE-SUSTAINING TREATMENT PREFERENCES</h2>
      <p>${formData.lifeSupportPreferences || 'No specific preferences stated.'}</p>

      <h2>PAIN MANAGEMENT PREFERENCES</h2>
      <p>${formData.painManagementPreferences || 'No specific preferences stated.'}</p>

      <h2>ORGAN DONATION PREFERENCES</h2>
      <p>${formData.organDonationPreferences || 'No specific preferences stated.'}</p>

      <p>IN WITNESS WHEREOF, I have signed this Healthcare Directive on _________________, 20____.</p>

      <p>____________________________<br>${formData.fullName}</p>

      <p>WITNESSES:<br><br>
      ____________________________<br>
      Name: ______________________<br>
      Address: ____________________<br><br>

      ____________________________<br>
      Name: ______________________<br>
      Address: ____________________<br>
      </p>
    `;
  };

  // Generate content for Power of Attorney
  const generatePowerOfAttorneyContent = (): string => {
    return `
      <h1>DURABLE POWER OF ATTORNEY OF ${formData.fullName.toUpperCase()}</h1>

      <p>I, ${formData.fullName}, a resident of ${formData.address}, hereby appoint ${formData.attorneyInFact} as my Attorney-in-Fact ("Agent").</p>

      <p>If ${formData.attorneyInFact} is unable or unwilling to serve, I appoint ${formData.alternateAttorneyInFact} as my alternate Agent.</p>

      <h2>EFFECTIVE DATE</h2>
      <p>This Power of Attorney shall become effective ${formData.effectiveDate === 'immediate' ? 'immediately upon execution' : 'upon my incapacity as determined by a written statement from my attending physician'}.</p>

      <h2>POWERS GRANTED</h2>
      <p>I grant my Agent the power and authority to do the following:</p>
      <ul>
        ${formData.powersGranted.map((power) => `<li>${power}</li>`).join('')}
      </ul>

      <h2>DURABILITY</h2>
      <p>This Power of Attorney shall not be affected by my subsequent disability or incapacity.</p>

      <p>IN WITNESS WHEREOF, I have signed this Power of Attorney on _________________, 20____.</p>

      <p>____________________________<br>${formData.fullName}</p>

      <p>WITNESSES:<br><br>
      ____________________________<br>
      Name: ______________________<br>
      Address: ____________________<br><br>

      ____________________________<br>
      Name: ______________________<br>
      Address: ____________________<br>
      </p>
    `;
  };

  // Generate content for Letter of Intent
  const generateLetterOfIntentContent = (): string => {
    return `
      <h1>LETTER OF INTENT</h1>

      <p>Date: ${new Date().toLocaleDateString()}</p>

      <p>From: ${formData.fullName}<br>
      ${formData.address}<br>
      ${formData.phone}<br>
      ${formData.email}</p>

      <p>To: My Loved Ones and Executor</p>

      <div>${formData.letterContent || 'No letter content provided.'}</div>

      <p>Sincerely,</p>

      <p>____________________________<br>${formData.fullName}</p>
    `;
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSave();

    if (onComplete) {
      onComplete();
    }
  };

  // Handle saving data
  const handleSave = () => {
    setIsSaving(true);

    // Update data in context
    updateData('west', 'estatePlanningDocuments', formData);

    // Show save indicator
    setShowSaveIndicator(true);

    // Hide save indicator after 2 seconds
    setTimeout(() => {
      setIsSaving(false);
      setShowSaveIndicator(false);
    }, 2000);
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Estate Planning Document Generator</Title>
        <Description theme={theme}>
          Create essential estate planning documents and export them as PDFs.
        </Description>
      </Header>

      <form onSubmit={handleSubmit}>
        <TabContainer theme={theme}>
          <Tab
            isActive={activeTab === 'personal'}
            onClick={() => setActiveTab('personal')}
            theme={theme}
          >
            Personal Info
          </Tab>
          <Tab isActive={activeTab === 'will'} onClick={() => setActiveTab('will')} theme={theme}>
            Will
          </Tab>
          <Tab
            isActive={activeTab === 'healthcare'}
            onClick={() => setActiveTab('healthcare')}
            theme={theme}
          >
            Healthcare Directive
          </Tab>
          <Tab isActive={activeTab === 'poa'} onClick={() => setActiveTab('poa')} theme={theme}>
            Power of Attorney
          </Tab>
          <Tab
            isActive={activeTab === 'letter'}
            onClick={() => setActiveTab('letter')}
            theme={theme}
          >
            Letter of Intent
          </Tab>
        </TabContainer>

        <TabContent theme={theme}>
          {activeTab === 'personal' && (
            <PersonalInfoForm
              formData={formData}
              handleInputChange={handleInputChange}
              theme={theme}
            />
          )}

          {activeTab === 'will' && (
            <WillForm
              formData={formData}
              handleInputChange={handleInputChange}
              handleBeneficiaryChange={handleBeneficiaryChange}
              addBeneficiary={addBeneficiary}
              removeBeneficiary={removeBeneficiary}
              handleBequestChange={handleBequestChange}
              addBequest={addBequest}
              removeBequest={removeBequest}
              theme={theme}
            />
          )}

          {activeTab === 'healthcare' && (
            <HealthcareDirectiveForm
              formData={formData}
              handleInputChange={handleInputChange}
              theme={theme}
            />
          )}

          {activeTab === 'poa' && (
            <PowerOfAttorneyForm
              formData={formData}
              handleInputChange={handleInputChange}
              handlePowerChange={handlePowerChange}
              theme={theme}
            />
          )}

          {activeTab === 'letter' && (
            <LetterOfIntentForm
              formData={formData}
              handleInputChange={handleInputChange}
              theme={theme}
            />
          )}
        </TabContent>

        <GenerateButtonContainer>
          <GenerateButton
            type="button"
            onClick={generateDocuments}
            disabled={isGenerating}
            theme={theme}
          >
            {isGenerating ? 'Generating...' : 'Generate PDF Documents'}
          </GenerateButton>
        </GenerateButtonContainer>

        <Disclaimer theme={theme}>
          <DisclaimerTitle>Important Disclaimer</DisclaimerTitle>
          <DisclaimerText>
            The documents generated by this tool are for informational purposes only and do not
            constitute legal advice. These documents may not be suitable for your specific situation
            and may not be valid in your jurisdiction. It is strongly recommended that you consult
            with a qualified attorney to review and finalize these documents.
          </DisclaimerText>
        </Disclaimer>

        <ButtonContainer>
          {onBack && (
            <BackButton type="button" onClick={onBack} theme={theme}>
              Back
            </BackButton>
          )}

          <SaveIndicator visible={showSaveIndicator}>✓ Saved</SaveIndicator>

          <SubmitButton type="submit" theme={theme}>
            {isSaving ? 'Saving...' : 'Save & Continue'}
          </SubmitButton>
        </ButtonContainer>
      </form>
    </Container>
  );
};

// Personal Information Form Component
const PersonalInfoForm: React.FC<{
  formData: DocumentData;
  handleInputChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => void;
  theme: any;
}> = ({ formData, handleInputChange, theme }) => {
  return (
    <div>
      <SectionTitle>Personal Information</SectionTitle>

      <FormRow>
        <FormField>
          <Label htmlFor="fullName">Full Legal Name</Label>
          <Input
            type="text"
            id="fullName"
            name="fullName"
            value={formData.fullName}
            onChange={handleInputChange}
            placeholder="John Doe"
          />
        </FormField>
      </FormRow>

      <FormRow>
        <FormField>
          <Label htmlFor="address">Address</Label>
          <Input
            type="text"
            id="address"
            name="address"
            value={formData.address}
            onChange={handleInputChange}
            placeholder="123 Main St, City, State, ZIP"
          />
        </FormField>
      </FormRow>

      <FormRow>
        <FormField>
          <Label htmlFor="phone">Phone Number</Label>
          <Input
            type="text"
            id="phone"
            name="phone"
            value={formData.phone}
            onChange={handleInputChange}
            placeholder="(*************"
          />
        </FormField>

        <FormField>
          <Label htmlFor="email">Email Address</Label>
          <Input
            type="text"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleInputChange}
            placeholder="<EMAIL>"
          />
        </FormField>
      </FormRow>

      <FormRow>
        <FormField>
          <Label htmlFor="dateOfBirth">Date of Birth</Label>
          <Input
            type="text"
            id="dateOfBirth"
            name="dateOfBirth"
            value={formData.dateOfBirth}
            onChange={handleInputChange}
            placeholder="MM/DD/YYYY"
          />
        </FormField>
      </FormRow>
    </div>
  );
};

// Will Form Component
const WillForm: React.FC<{
  formData: DocumentData;
  handleInputChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => void;
  handleBeneficiaryChange: (index: number, field: string, value: string) => void;
  addBeneficiary: () => void;
  removeBeneficiary: (index: number) => void;
  handleBequestChange: (index: number, field: string, value: string) => void;
  addBequest: () => void;
  removeBequest: (index: number) => void;
  theme: any;
}> = ({
  formData,
  handleInputChange,
  handleBeneficiaryChange,
  addBeneficiary,
  removeBeneficiary,
  handleBequestChange,
  addBequest,
  removeBequest,
  theme,
}) => {
  return (
    <div>
      <SectionTitle>Last Will and Testament</SectionTitle>

      <FormRow>
        <FormField>
          <Label htmlFor="executor">Executor</Label>
          <Input
            type="text"
            id="executor"
            name="executor"
            value={formData.executor}
            onChange={handleInputChange}
            placeholder="Full name of executor"
          />
          <FieldHint>The person who will manage your estate</FieldHint>
        </FormField>

        <FormField>
          <Label htmlFor="alternateExecutor">Alternate Executor</Label>
          <Input
            type="text"
            id="alternateExecutor"
            name="alternateExecutor"
            value={formData.alternateExecutor}
            onChange={handleInputChange}
            placeholder="Full name of alternate executor"
          />
          <FieldHint>In case your first choice cannot serve</FieldHint>
        </FormField>
      </FormRow>

      <FormRow>
        <FormField>
          <Label htmlFor="guardianForMinors">Guardian for Minor Children</Label>
          <Input
            type="text"
            id="guardianForMinors"
            name="guardianForMinors"
            value={formData.guardianForMinors}
            onChange={handleInputChange}
            placeholder="Full name of guardian"
          />
          <FieldHint>If applicable</FieldHint>
        </FormField>
      </FormRow>

      <Divider theme={theme} />

      <SectionSubtitle>Beneficiaries</SectionSubtitle>
      <FieldHint>People who will inherit your estate</FieldHint>

      {formData.beneficiaries.map((beneficiary, index) => (
        <FormRow key={index}>
          <FormField>
            <Label>Beneficiary Name</Label>
            <Input
              type="text"
              value={beneficiary.name}
              onChange={(e) => handleBeneficiaryChange(index, 'name', e.target.value)}
              placeholder="Full name"
            />
          </FormField>

          <FormField>
            <Label>Relationship</Label>
            <Input
              type="text"
              value={beneficiary.relationship}
              onChange={(e) => handleBeneficiaryChange(index, 'relationship', e.target.value)}
              placeholder="e.g., Spouse, Child"
            />
          </FormField>

          <FormField>
            <Label>Percentage</Label>
            <Input
              type="text"
              value={beneficiary.percentage}
              onChange={(e) => handleBeneficiaryChange(index, 'percentage', e.target.value)}
              placeholder="e.g., 50"
            />
          </FormField>

          <RemoveButton
            type="button"
            onClick={() => removeBeneficiary(index)}
            disabled={formData.beneficiaries.length <= 1}
            theme={theme}
          >
            Remove
          </RemoveButton>
        </FormRow>
      ))}

      <AddButton type="button" onClick={addBeneficiary} theme={theme}>
        Add Beneficiary
      </AddButton>

      <Divider theme={theme} />

      <SectionSubtitle>Specific Bequests</SectionSubtitle>
      <FieldHint>Specific items you want to give to specific people</FieldHint>

      {formData.specificBequests.map((bequest, index) => (
        <FormRow key={index}>
          <FormField>
            <Label>Item Description</Label>
            <Input
              type="text"
              value={bequest.item}
              onChange={(e) => handleBequestChange(index, 'item', e.target.value)}
              placeholder="e.g., My wedding ring"
            />
          </FormField>

          <FormField>
            <Label>Recipient</Label>
            <Input
              type="text"
              value={bequest.recipient}
              onChange={(e) => handleBequestChange(index, 'recipient', e.target.value)}
              placeholder="Full name"
            />
          </FormField>

          <RemoveButton
            type="button"
            onClick={() => removeBequest(index)}
            disabled={formData.specificBequests.length <= 1}
            theme={theme}
          >
            Remove
          </RemoveButton>
        </FormRow>
      ))}

      <AddButton type="button" onClick={addBequest} theme={theme}>
        Add Specific Bequest
      </AddButton>
    </div>
  );
};

// Healthcare Directive Form Component
const HealthcareDirectiveForm: React.FC<{
  formData: DocumentData;
  handleInputChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => void;
  theme: any;
}> = ({ formData, handleInputChange, theme }) => {
  return (
    <div>
      <SectionTitle>Healthcare Directive</SectionTitle>

      <FormRow>
        <FormField>
          <Label htmlFor="healthcareAgent">Healthcare Agent</Label>
          <Input
            type="text"
            id="healthcareAgent"
            name="healthcareAgent"
            value={formData.healthcareAgent}
            onChange={handleInputChange}
            placeholder="Full name of healthcare agent"
          />
          <FieldHint>The person who will make healthcare decisions for you</FieldHint>
        </FormField>

        <FormField>
          <Label htmlFor="alternateHealthcareAgent">Alternate Healthcare Agent</Label>
          <Input
            type="text"
            id="alternateHealthcareAgent"
            name="alternateHealthcareAgent"
            value={formData.alternateHealthcareAgent}
            onChange={handleInputChange}
            placeholder="Full name of alternate agent"
          />
          <FieldHint>In case your first choice cannot serve</FieldHint>
        </FormField>
      </FormRow>

      <FormRow>
        <FormField>
          <Label htmlFor="lifeSupportPreferences">Life Support Preferences</Label>
          <TextArea
            id="lifeSupportPreferences"
            name="lifeSupportPreferences"
            value={formData.lifeSupportPreferences}
            onChange={handleInputChange}
            placeholder="Describe your preferences regarding life support measures"
            rows={4}
          />
        </FormField>
      </FormRow>

      <FormRow>
        <FormField>
          <Label htmlFor="painManagementPreferences">Pain Management Preferences</Label>
          <TextArea
            id="painManagementPreferences"
            name="painManagementPreferences"
            value={formData.painManagementPreferences}
            onChange={handleInputChange}
            placeholder="Describe your preferences regarding pain management"
            rows={4}
          />
        </FormField>
      </FormRow>

      <FormRow>
        <FormField>
          <Label htmlFor="organDonationPreferences">Organ Donation Preferences</Label>
          <TextArea
            id="organDonationPreferences"
            name="organDonationPreferences"
            value={formData.organDonationPreferences}
            onChange={handleInputChange}
            placeholder="Describe your preferences regarding organ donation"
            rows={4}
          />
        </FormField>
      </FormRow>
    </div>
  );
};

// Power of Attorney Form Component
const PowerOfAttorneyForm: React.FC<{
  formData: DocumentData;
  handleInputChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => void;
  handlePowerChange: (power: string) => void;
  theme: any;
}> = ({ formData, handleInputChange, handlePowerChange, theme }) => {
  const availablePowers = [
    'Real Estate Transactions',
    'Financial Institution Transactions',
    'Tax Matters',
    'Insurance Transactions',
    'Business Operations',
    'Gift Making',
    'Legal Proceedings',
    'Personal and Family Maintenance',
    'Government Benefits',
    'Retirement Plans',
    'Health Insurance',
  ];

  return (
    <div>
      <SectionTitle>Durable Power of Attorney</SectionTitle>

      <FormRow>
        <FormField>
          <Label htmlFor="attorneyInFact">Attorney-in-Fact (Agent)</Label>
          <Input
            type="text"
            id="attorneyInFact"
            name="attorneyInFact"
            value={formData.attorneyInFact}
            onChange={handleInputChange}
            placeholder="Full name of agent"
          />
          <FieldHint>The person who will make financial decisions for you</FieldHint>
        </FormField>

        <FormField>
          <Label htmlFor="alternateAttorneyInFact">Alternate Attorney-in-Fact</Label>
          <Input
            type="text"
            id="alternateAttorneyInFact"
            name="alternateAttorneyInFact"
            value={formData.alternateAttorneyInFact}
            onChange={handleInputChange}
            placeholder="Full name of alternate agent"
          />
          <FieldHint>In case your first choice cannot serve</FieldHint>
        </FormField>
      </FormRow>

      <FormRow>
        <FormField>
          <Label htmlFor="effectiveDate">When Power of Attorney Becomes Effective</Label>
          <Select
            id="effectiveDate"
            name="effectiveDate"
            value={formData.effectiveDate}
            onChange={handleInputChange}
          >
            <option value="immediate">Immediately upon signing</option>
            <option value="incapacity">Only upon my incapacity</option>
          </Select>
        </FormField>
      </FormRow>

      <Divider theme={theme} />

      <SectionSubtitle>Powers Granted</SectionSubtitle>
      <FieldHint>Select the powers you want to grant to your agent</FieldHint>

      <CheckboxGrid>
        {availablePowers.map((power) => (
          <CheckboxItem key={power}>
            <Checkbox
              type="checkbox"
              id={`power-${power}`}
              checked={formData.powersGranted.includes(power)}
              onChange={() => handlePowerChange(power)}
            />
            <CheckboxLabel htmlFor={`power-${power}`}>{power}</CheckboxLabel>
          </CheckboxItem>
        ))}
      </CheckboxGrid>
    </div>
  );
};

// Letter of Intent Form Component
const LetterOfIntentForm: React.FC<{
  formData: DocumentData;
  handleInputChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => void;
  theme: any;
}> = ({ formData, handleInputChange, theme }) => {
  return (
    <div>
      <SectionTitle>Letter of Intent</SectionTitle>
      <FieldHint>
        A letter of intent is an informal document that provides personal information and wishes not
        included in legal documents. It can include funeral preferences, personal messages, location
        of important documents, digital asset information, etc.
      </FieldHint>

      <FormRow>
        <FormField>
          <Label htmlFor="letterContent">Letter Content</Label>
          <TextArea
            id="letterContent"
            name="letterContent"
            value={formData.letterContent}
            onChange={handleInputChange}
            placeholder="Write your letter of intent here..."
            rows={15}
          />
        </FormField>
      </FormRow>
    </div>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  padding: 24px;
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const Title = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const Description = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const TabContainer = styled.div<{ theme: any }>`
  display: flex;
  border-bottom: 1px solid ${(props) => props.theme.colors.border};
  margin-bottom: 24px;
  flex-wrap: wrap;
`;

const Tab = styled.div<{ isActive: boolean; theme: any }>`
  padding: 12px 16px;
  cursor: pointer;
  font-weight: ${(props) => (props.isActive ? 'bold' : 'normal')};
  color: ${(props) =>
    props.isActive ? props.theme.colors.primary.main : props.theme.colors.text.secondary};
  border-bottom: 2px solid
    ${(props) => (props.isActive ? props.theme.colors.primary.main : 'transparent')};
  transition: all 0.2s;

  &:hover {
    color: ${(props) => props.theme.colors.primary.main};
  }
`;

const TabContent = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const SectionSubtitle = styled.h4`
  margin: 16px 0 8px 0;
  font-size: 1.1rem;
  font-weight: 500;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;

const FormField = styled.div`
  margin-bottom: 16px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
`;

const Input = styled.input`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  resize: vertical;
`;

const Select = styled.select`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  background-color: white;
`;

const FieldHint = styled.div`
  font-size: 0.8rem;
  color: #666;
  margin-top: 4px;
`;

const Divider = styled.div<{ theme: any }>`
  height: 1px;
  background-color: ${(props) => props.theme.colors.border};
  margin: 24px 0;
`;

const CheckboxGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 8px;
  margin-top: 16px;
`;

const CheckboxItem = styled.div`
  display: flex;
  align-items: center;
`;

const Checkbox = styled.input`
  margin-right: 8px;
`;

const CheckboxLabel = styled.label`
  font-size: 0.9rem;
`;

const AddButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.light};
  color: ${(props) => props.theme.colors.primary.main};
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  margin-top: 8px;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.main};
    color: ${(props) => props.theme.colors.primary.contrastText};
  }
`;

const RemoveButton = styled.button<{ theme: any; disabled?: boolean }>`
  background-color: ${(props) => (props.disabled ? '#f0f0f0' : props.theme.colors.error.light)};
  color: ${(props) => (props.disabled ? '#999' : props.theme.colors.error.main)};
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  align-self: flex-end;

  &:hover {
    background-color: ${(props) => (props.disabled ? '#f0f0f0' : props.theme.colors.error.main)};
    color: ${(props) => (props.disabled ? '#999' : props.theme.colors.error.contrastText)};
  }
`;

const GenerateButtonContainer = styled.div`
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
`;

const GenerateButton = styled.button<{ theme: any; disabled?: boolean }>`
  background-color: ${(props) => (props.disabled ? '#f0f0f0' : props.theme.colors.success.main)};
  color: ${(props) => (props.disabled ? '#999' : props.theme.colors.success.contrastText)};
  border: none;
  border-radius: 4px;
  padding: 12px 24px;
  font-size: 1.1rem;
  font-weight: 500;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};

  &:hover {
    background-color: ${(props) => (props.disabled ? '#f0f0f0' : props.theme.colors.success.dark)};
  }
`;

const Disclaimer = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.warning.light};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
`;

const DisclaimerTitle = styled.h4`
  margin: 0 0 8px 0;
  font-size: 1rem;
  font-weight: 500;
  color: #856404;
`;

const DisclaimerText = styled.p`
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.5;
  color: #856404;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
`;

const BackButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.text.secondary};
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const SubmitButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 24px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  color: green;
  margin-right: 16px;
`;

export default EstatePlanningDocumentGenerator;
