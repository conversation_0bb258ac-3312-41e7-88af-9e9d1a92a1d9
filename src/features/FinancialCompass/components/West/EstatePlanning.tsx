/**
 * Estate Planning Component
 *
 * This component helps users track and manage their estate planning documents
 * and beneficiary designations.
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { formatCurrency } from '../../../../utils/formatters';

interface EstatePlanningProps {
  onComplete?: () => void;
  onBack?: () => void;
}

interface Document {
  id: string;
  type: string;
  status: 'completed' | 'in_progress' | 'not_started';
  lastUpdated?: string;
  location?: string;
  notes?: string;
}

interface Beneficiary {
  id: string;
  name: string;
  relationship: string;
  assetType: string;
  percentage: number;
  notes?: string;
}

interface EstatePlanningData {
  hasWill: boolean;
  hasTrust: boolean;
  hasPowerOfAttorney: boolean;
  hasHealthcareDirective: boolean;
  documents: Document[];
  beneficiaries: Beneficiary[];
  estateNotes: string;
}

const EstatePlanning: React.FC<EstatePlanningProps> = ({ onComplete, onBack }) => {
  const { theme } = useTheme();
  const { data, updateData } = useFinancialCompass();

  const [isSaving, setIsSaving] = useState(false);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Document types
  const documentTypes = [
    'Will',
    'Trust',
    'Power of Attorney',
    'Healthcare Directive',
    'Letter of Intent',
    'Life Insurance Policy',
    'Beneficiary Designations',
    'Digital Asset Plan',
    'Other',
  ];

  // Document status options
  const statusOptions = [
    { value: 'completed', label: 'Completed' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'not_started', label: 'Not Started' },
  ];

  // Asset types for beneficiaries
  const assetTypes = [
    'Retirement Account',
    'Life Insurance',
    'Investment Account',
    'Bank Account',
    'Real Estate',
    'Personal Property',
    'Business Interest',
    'Trust',
    'Other',
  ];

  // Initialize form data from context or with defaults
  const [formData, setFormData] = useState<EstatePlanningData>(
    data.west?.estatePlanning || {
      hasWill: false,
      hasTrust: false,
      hasPowerOfAttorney: false,
      hasHealthcareDirective: false,
      documents: [],
      beneficiaries: [],
      estateNotes: '',
    }
  );

  // New document form
  const [newDocument, setNewDocument] = useState<Omit<Document, 'id'>>({
    type: '',
    status: 'not_started',
    location: '',
    notes: '',
  });

  // New beneficiary form
  const [newBeneficiary, setNewBeneficiary] = useState<Omit<Beneficiary, 'id'>>({
    name: '',
    relationship: '',
    assetType: '',
    percentage: 100,
    notes: '',
  });

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: checked,
    }));
  };

  // Handle new document field changes
  const handleNewDocumentChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setNewDocument((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Add new document
  const handleAddDocument = () => {
    if (!newDocument.type) return;

    const newDocumentWithId: Document = {
      ...newDocument,
      id: `doc-${Date.now()}`,
      lastUpdated: new Date().toISOString(),
    };

    // Update document-related flags
    const updatedFormData = { ...formData };
    if (newDocument.type === 'Will') updatedFormData.hasWill = true;
    if (newDocument.type === 'Trust') updatedFormData.hasTrust = true;
    if (newDocument.type === 'Power of Attorney') updatedFormData.hasPowerOfAttorney = true;
    if (newDocument.type === 'Healthcare Directive') updatedFormData.hasHealthcareDirective = true;

    // Add new document to the list
    updatedFormData.documents = [...formData.documents, newDocumentWithId];

    setFormData(updatedFormData);

    // Reset new document form
    setNewDocument({
      type: '',
      status: 'not_started',
      location: '',
      notes: '',
    });
  };

  // Remove document
  const handleRemoveDocument = (id: string) => {
    const updatedDocuments = formData.documents.filter((doc) => doc.id !== id);

    // Update document-related flags
    const updatedFormData = { ...formData, documents: updatedDocuments };
    updatedFormData.hasWill = updatedDocuments.some((d) => d.type === 'Will');
    updatedFormData.hasTrust = updatedDocuments.some((d) => d.type === 'Trust');
    updatedFormData.hasPowerOfAttorney = updatedDocuments.some(
      (d) => d.type === 'Power of Attorney'
    );
    updatedFormData.hasHealthcareDirective = updatedDocuments.some(
      (d) => d.type === 'Healthcare Directive'
    );

    setFormData(updatedFormData);
  };

  // Handle new beneficiary field changes
  const handleNewBeneficiaryChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setNewBeneficiary((prev) => ({
      ...prev,
      [name]: name === 'percentage' ? parseFloat(value) : value,
    }));
  };

  // Add new beneficiary
  const handleAddBeneficiary = () => {
    if (!newBeneficiary.name || !newBeneficiary.assetType) return;

    const newBeneficiaryWithId: Beneficiary = {
      ...newBeneficiary,
      id: `ben-${Date.now()}`,
    };

    // Add new beneficiary to the list
    setFormData((prev) => ({
      ...prev,
      beneficiaries: [...prev.beneficiaries, newBeneficiaryWithId],
    }));

    // Reset new beneficiary form
    setNewBeneficiary({
      name: '',
      relationship: '',
      assetType: '',
      percentage: 100,
      notes: '',
    });
  };

  // Remove beneficiary
  const handleRemoveBeneficiary = (id: string) => {
    setFormData((prev) => ({
      ...prev,
      beneficiaries: prev.beneficiaries.filter((ben) => ben.id !== id),
    }));
  };

  // Handle notes change
  const handleNotesChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFormData((prev) => ({
      ...prev,
      estateNotes: e.target.value,
    }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSave();

    if (onComplete) {
      onComplete();
    }
  };

  // Handle saving data
  const handleSave = () => {
    setIsSaving(true);

    // Update data in context
    updateData('west', 'estatePlanning', formData);

    // Show save indicator
    setShowSaveIndicator(true);

    // Hide save indicator after 2 seconds
    setTimeout(() => {
      setIsSaving(false);
      setShowSaveIndicator(false);
    }, 2000);
  };

  // Get status color
  const getStatusColor = (status: Document['status'], theme: any): string => {
    switch (status) {
      case 'completed':
        return theme.colors.success.main;
      case 'in_progress':
        return theme.colors.warning.main;
      case 'not_started':
        return theme.colors.error.main;
      default:
        return theme.colors.text.secondary;
    }
  };

  // Get status label
  const getStatusLabel = (status: Document['status']): string => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'in_progress':
        return 'In Progress';
      case 'not_started':
        return 'Not Started';
      default:
        return 'Unknown';
    }
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Estate Planning</Title>
        <Description theme={theme}>
          Track and manage your estate planning documents and beneficiary designations.
        </Description>
      </Header>

      <form onSubmit={handleSubmit}>
        <FormSection>
          <SectionTitle>Essential Documents</SectionTitle>

          <DocumentChecklist>
            <ChecklistItem isChecked={formData.hasWill} theme={theme}>
              <ChecklistIcon isChecked={formData.hasWill}>
                {formData.hasWill ? '✓' : '○'}
              </ChecklistIcon>
              <ChecklistText>Will</ChecklistText>
            </ChecklistItem>

            <ChecklistItem isChecked={formData.hasTrust} theme={theme}>
              <ChecklistIcon isChecked={formData.hasTrust}>
                {formData.hasTrust ? '✓' : '○'}
              </ChecklistIcon>
              <ChecklistText>Trust</ChecklistText>
            </ChecklistItem>

            <ChecklistItem isChecked={formData.hasPowerOfAttorney} theme={theme}>
              <ChecklistIcon isChecked={formData.hasPowerOfAttorney}>
                {formData.hasPowerOfAttorney ? '✓' : '○'}
              </ChecklistIcon>
              <ChecklistText>Power of Attorney</ChecklistText>
            </ChecklistItem>

            <ChecklistItem isChecked={formData.hasHealthcareDirective} theme={theme}>
              <ChecklistIcon isChecked={formData.hasHealthcareDirective}>
                {formData.hasHealthcareDirective ? '✓' : '○'}
              </ChecklistIcon>
              <ChecklistText>Healthcare Directive</ChecklistText>
            </ChecklistItem>
          </DocumentChecklist>
        </FormSection>

        <FormSection>
          <SectionTitle>Document Inventory</SectionTitle>

          {formData.documents.length > 0 ? (
            <DocumentsList>
              {formData.documents.map((doc) => (
                <DocumentItem key={doc.id} theme={theme}>
                  <DocumentHeader>
                    <DocumentType>{doc.type}</DocumentType>
                    <StatusBadge status={doc.status} theme={theme}>
                      {getStatusLabel(doc.status)}
                    </StatusBadge>
                    <RemoveButton
                      onClick={() => handleRemoveDocument(doc.id)}
                      theme={theme}
                      type="button"
                    >
                      Remove
                    </RemoveButton>
                  </DocumentHeader>

                  <DocumentDetails>
                    {doc.lastUpdated && (
                      <DocumentDetail>
                        <DetailLabel>Last Updated:</DetailLabel>
                        <DetailValue>{new Date(doc.lastUpdated).toLocaleDateString()}</DetailValue>
                      </DocumentDetail>
                    )}

                    {doc.location && (
                      <DocumentDetail>
                        <DetailLabel>Location:</DetailLabel>
                        <DetailValue>{doc.location}</DetailValue>
                      </DocumentDetail>
                    )}

                    {doc.notes && <DocumentNotes>{doc.notes}</DocumentNotes>}
                  </DocumentDetails>
                </DocumentItem>
              ))}
            </DocumentsList>
          ) : (
            <EmptyState theme={theme}>
              <EmptyIcon>📄</EmptyIcon>
              <EmptyText>
                You haven't added any documents yet. Use the form below to add your first document.
              </EmptyText>
            </EmptyState>
          )}

          <AddDocumentForm theme={theme}>
            <FormRow>
              <FormField>
                <Label htmlFor="type">Document Type</Label>
                <Select
                  id="type"
                  name="type"
                  value={newDocument.type}
                  onChange={handleNewDocumentChange}
                  required
                >
                  <option value="">Select type...</option>
                  {documentTypes.map((type) => (
                    <option key={type} value={type}>
                      {type}
                    </option>
                  ))}
                </Select>
              </FormField>

              <FormField>
                <Label htmlFor="status">Status</Label>
                <Select
                  id="status"
                  name="status"
                  value={newDocument.status}
                  onChange={handleNewDocumentChange}
                >
                  {statusOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </Select>
              </FormField>

              <FormField>
                <Label htmlFor="location">Location</Label>
                <Input
                  type="text"
                  id="location"
                  name="location"
                  value={newDocument.location}
                  onChange={handleNewDocumentChange}
                  placeholder="e.g., Safe deposit box"
                />
              </FormField>
            </FormRow>

            <FormField>
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                name="notes"
                value={newDocument.notes}
                onChange={handleNewDocumentChange}
                placeholder="Additional details about this document"
              />
            </FormField>

            <AddButton
              type="button"
              onClick={handleAddDocument}
              disabled={!newDocument.type}
              theme={theme}
            >
              Add Document
            </AddButton>
          </AddDocumentForm>
        </FormSection>

        <FormSection>
          <SectionTitle>Beneficiary Designations</SectionTitle>

          {formData.beneficiaries.length > 0 ? (
            <BeneficiariesList>
              {formData.beneficiaries.map((ben) => (
                <BeneficiaryItem key={ben.id} theme={theme}>
                  <BeneficiaryHeader>
                    <BeneficiaryName>{ben.name}</BeneficiaryName>
                    <BeneficiaryRelationship>{ben.relationship}</BeneficiaryRelationship>
                    <RemoveButton
                      onClick={() => handleRemoveBeneficiary(ben.id)}
                      theme={theme}
                      type="button"
                    >
                      Remove
                    </RemoveButton>
                  </BeneficiaryHeader>

                  <BeneficiaryDetails>
                    <BeneficiaryDetail>
                      <DetailLabel>Asset Type:</DetailLabel>
                      <DetailValue>{ben.assetType}</DetailValue>
                    </BeneficiaryDetail>

                    <BeneficiaryDetail>
                      <DetailLabel>Percentage:</DetailLabel>
                      <DetailValue>{ben.percentage}%</DetailValue>
                    </BeneficiaryDetail>

                    {ben.notes && <BeneficiaryNotes>{ben.notes}</BeneficiaryNotes>}
                  </BeneficiaryDetails>
                </BeneficiaryItem>
              ))}
            </BeneficiariesList>
          ) : (
            <EmptyState theme={theme}>
              <EmptyIcon>👪</EmptyIcon>
              <EmptyText>
                You haven't added any beneficiaries yet. Use the form below to add your first
                beneficiary.
              </EmptyText>
            </EmptyState>
          )}

          <AddBeneficiaryForm theme={theme}>
            <FormRow>
              <FormField>
                <Label htmlFor="name">Beneficiary Name</Label>
                <Input
                  type="text"
                  id="name"
                  name="name"
                  value={newBeneficiary.name}
                  onChange={handleNewBeneficiaryChange}
                  placeholder="e.g., John Doe"
                  required
                />
              </FormField>

              <FormField>
                <Label htmlFor="relationship">Relationship</Label>
                <Input
                  type="text"
                  id="relationship"
                  name="relationship"
                  value={newBeneficiary.relationship}
                  onChange={handleNewBeneficiaryChange}
                  placeholder="e.g., Spouse, Child"
                />
              </FormField>
            </FormRow>

            <FormRow>
              <FormField>
                <Label htmlFor="assetType">Asset Type</Label>
                <Select
                  id="assetType"
                  name="assetType"
                  value={newBeneficiary.assetType}
                  onChange={handleNewBeneficiaryChange}
                  required
                >
                  <option value="">Select asset type...</option>
                  {assetTypes.map((type) => (
                    <option key={type} value={type}>
                      {type}
                    </option>
                  ))}
                </Select>
              </FormField>

              <FormField>
                <Label htmlFor="percentage">Percentage</Label>
                <Input
                  type="number"
                  id="percentage"
                  name="percentage"
                  value={newBeneficiary.percentage}
                  onChange={handleNewBeneficiaryChange}
                  min="0"
                  max="100"
                />
              </FormField>
            </FormRow>

            <FormField>
              <Label htmlFor="beneficiaryNotes">Notes</Label>
              <Textarea
                id="beneficiaryNotes"
                name="notes"
                value={newBeneficiary.notes}
                onChange={handleNewBeneficiaryChange}
                placeholder="Additional details about this beneficiary"
              />
            </FormField>

            <AddButton
              type="button"
              onClick={handleAddBeneficiary}
              disabled={!newBeneficiary.name || !newBeneficiary.assetType}
              theme={theme}
            >
              Add Beneficiary
            </AddButton>
          </AddBeneficiaryForm>
        </FormSection>

        <FormSection>
          <SectionTitle>Additional Notes</SectionTitle>
          <FormField>
            <Label htmlFor="estateNotes">Notes about your estate plan</Label>
            <Textarea
              id="estateNotes"
              name="estateNotes"
              value={formData.estateNotes}
              onChange={handleNotesChange}
              placeholder="Add any additional notes about your estate plan"
            />
          </FormField>
        </FormSection>

        <ButtonContainer>
          {onBack && (
            <BackButton type="button" onClick={onBack} theme={theme}>
              Back
            </BackButton>
          )}

          <SaveIndicator visible={showSaveIndicator}>✓ Saved</SaveIndicator>

          <SubmitButton type="submit" theme={theme}>
            {isSaving ? 'Saving...' : 'Save & Continue'}
          </SubmitButton>
        </ButtonContainer>
      </form>
    </Container>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  padding: 24px;
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const Title = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const Description = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const FormSection = styled.div`
  margin-bottom: 32px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const DocumentChecklist = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 24px;
`;

const ChecklistItem = styled.div<{ isChecked: boolean; theme: any }>`
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  background-color: ${(props) =>
    props.isChecked ? props.theme.colors.success.light : props.theme.colors.background.paper};
`;

const ChecklistIcon = styled.div<{ isChecked: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 8px;
  color: ${(props) => (props.isChecked ? 'green' : '#666')};
  font-weight: ${(props) => (props.isChecked ? 'bold' : 'normal')};
`;

const ChecklistText = styled.div`
  font-size: 0.9rem;
`;

const DocumentsList = styled.div`
  margin-bottom: 24px;
`;

const DocumentItem = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
`;

const DocumentHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const DocumentType = styled.div`
  font-weight: 600;
  font-size: 1.1rem;
`;

const StatusBadge = styled.div<{ status: string; theme: any }>`
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  background-color: ${(props) => {
    // Inline status color logic
    let color;
    switch (props.status) {
      case 'completed':
        color = props.theme.colors.success.main;
        break;
      case 'in_progress':
        color = props.theme.colors.warning.main;
        break;
      case 'not_started':
        color = props.theme.colors.error.main;
        break;
      default:
        color = props.theme.colors.text.secondary;
    }
    return color + '20'; // Add 20% opacity
  }};
  color: ${(props) => {
    // Inline status color logic
    switch (props.status) {
      case 'completed':
        return props.theme.colors.success.main;
      case 'in_progress':
        return props.theme.colors.warning.main;
      case 'not_started':
        return props.theme.colors.error.main;
      default:
        return props.theme.colors.text.secondary;
    }
  }};
`;

const RemoveButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.error.main};
  border: none;
  font-size: 0.9rem;
  cursor: pointer;
  padding: 4px 8px;

  &:hover {
    text-decoration: underline;
  }
`;

const DocumentDetails = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
`;

const DocumentDetail = styled.div`
  margin-bottom: 8px;
`;

const DetailLabel = styled.div`
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 4px;
`;

const DetailValue = styled.div`
  font-size: 0.9rem;
`;

const DocumentNotes = styled.div`
  grid-column: 1 / -1;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #eee;
  font-style: italic;
  font-size: 0.9rem;
`;

const EmptyState = styled.div<{ theme: any }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  margin-bottom: 24px;
`;

const EmptyIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
`;

const EmptyText = styled.p`
  text-align: center;
  color: #666;
  max-width: 400px;
`;

const AddDocumentForm = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  padding: 16px;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;

const FormField = styled.div`
  margin-bottom: 16px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
`;

const Input = styled.input`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
`;

const Select = styled.select`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
`;

const Textarea = styled.textarea`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  min-height: 80px;
  resize: vertical;
`;

const AddButton = styled.button<{ theme: any; disabled?: boolean }>`
  background-color: ${(props) =>
    props.disabled
      ? props.theme.colors.action.disabledBackground
      : props.theme.colors.primary.main};
  color: ${(props) =>
    props.disabled ? props.theme.colors.action.disabled : props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) =>
      props.disabled
        ? props.theme.colors.action.disabledBackground
        : props.theme.colors.primary.dark};
  }
`;

const BeneficiariesList = styled.div`
  margin-bottom: 24px;
`;

const BeneficiaryItem = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
`;

const BeneficiaryHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const BeneficiaryName = styled.div`
  font-weight: 600;
  font-size: 1.1rem;
`;

const BeneficiaryRelationship = styled.div`
  font-style: italic;
  color: #666;
`;

const BeneficiaryDetails = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
`;

const BeneficiaryDetail = styled.div`
  margin-bottom: 8px;
`;

const BeneficiaryNotes = styled.div`
  grid-column: 1 / -1;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #eee;
  font-style: italic;
  font-size: 0.9rem;
`;

const AddBeneficiaryForm = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  padding: 16px;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
`;

const BackButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.text.secondary};
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const SubmitButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 24px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  color: green;
  margin-right: 16px;
`;

export default EstatePlanning;
