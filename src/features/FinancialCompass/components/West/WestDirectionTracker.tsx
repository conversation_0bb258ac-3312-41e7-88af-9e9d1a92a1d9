/**
 * West Direction Tracker Component
 *
 * This component displays the progress of the West direction sections,
 * provides legacy planning insights, and allows navigation to individual sections.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { useGuidedJourney } from '../../../GuidedJourney/context/GuidedJourneyContext';
import { formatCurrency, formatPercentage } from '../../../../utils/formatters';

interface WestDirectionTrackerProps {
  onSectionSelect?: (sectionId: string) => void;
}

const WestDirectionTracker: React.FC<WestDirectionTrackerProps> = ({ onSectionSelect }) => {
  const { theme } = useTheme();
  const { updateActiveDirection } = useGuidedJourney();
  const { westSections, updateWestProgress, data } = useFinancialCompass();
  const [completedSections, setCompletedSections] = useState<string[]>([]);
  const [showLegacyInsights, setShowLegacyInsights] = useState(false);

  // Calculate overall progress for West direction
  const totalSections = westSections?.length || 0;
  const completedCount = completedSections.length;
  const progressPercentage = totalSections > 0 ? (completedCount / totalSections) * 100 : 0;

  // Extract legacy planning data
  const taxPlanning = data.west?.taxPlanning || {
    hasTaxStrategy: 'no',
    usesTaxDeferredAccounts: 'no',
    usesRothAccounts: 'no',
    hasTaxLossHarvesting: 'no',
  };
  const estatePlanning = data.west?.estatePlanning || {
    hasEstateStrategy: 'no',
    hasSuccessionPlan: 'no',
  };
  const estateDocuments = data.west?.estateDocuments || {
    will: 'no',
    trust: 'no',
    powerOfAttorney: 'no',
    advanceDirective: 'no',
  };
  const charitableGiving = data.west?.charitableGiving || {
    annualGiving: '0',
    plannedBequest: '0',
    hasCharitableStrategy: 'no',
  };
  const legacyPlanning = data.west?.legacyPlanning || {
    hasLegacyVision: 'no',
    hasLegacyLetter: 'no',
  };

  // Extract personal and financial data
  const personalInfo = data.north?.personalInformation || {};
  const familyInfo = data.north?.familyInformation || {};
  const netWorthDetails = data.north?.netWorthDetails || {
    totalAssets: '0',
    totalLiabilities: '0',
  };
  const assets = data.north?.assets || {};

  // Calculate key legacy metrics
  const totalAssets = parseFloat(netWorthDetails.totalAssets || '0');
  const totalLiabilities = parseFloat(netWorthDetails.totalLiabilities || '0');
  const netWorth = totalAssets - totalLiabilities;

  // Estate document status
  const hasWill = estateDocuments.will === 'yes';
  const hasTrust = estateDocuments.trust === 'yes';
  const hasPowerOfAttorney = estateDocuments.powerOfAttorney === 'yes';
  const hasAdvanceDirective = estateDocuments.advanceDirective === 'yes';

  // Calculate document completeness score
  const documentCompleteness =
    ([hasWill, hasTrust, hasPowerOfAttorney, hasAdvanceDirective].filter(Boolean).length / 4) * 100;

  // Calculate charitable giving metrics
  const annualCharitableGiving = parseFloat(charitableGiving.annualGiving || '0');
  const plannedBequest = parseFloat(charitableGiving.plannedBequest || '0');

  // Calculate tax efficiency metrics
  const hasTaxStrategy = taxPlanning.hasTaxStrategy === 'yes';
  const usesTaxDeferredAccounts = taxPlanning.usesTaxDeferredAccounts === 'yes';
  const usesRothAccounts = taxPlanning.usesRothAccounts === 'yes';
  const hasTaxLossHarvesting = taxPlanning.hasTaxLossHarvesting === 'yes';

  // Calculate tax strategy completeness
  const taxStrategyCompleteness =
    ([hasTaxStrategy, usesTaxDeferredAccounts, usesRothAccounts, hasTaxLossHarvesting].filter(
      Boolean
    ).length /
      4) *
    100;

  // Calculate overall legacy readiness score
  const calculateLegacyReadiness = () => {
    // Base scores for each component (out of 100)
    const documentScore = documentCompleteness;

    // Estate planning score
    const hasEstateStrategy = estatePlanning.hasEstateStrategy === 'yes';
    const hasSuccessionPlan = estatePlanning.hasSuccessionPlan === 'yes';
    const estateScore = ([hasEstateStrategy, hasSuccessionPlan].filter(Boolean).length / 2) * 100;

    // Tax planning score
    const taxScore = taxStrategyCompleteness;

    // Charitable giving score
    const givingScore = charitableGiving.hasCharitableStrategy === 'yes' ? 100 : 0;

    // Legacy planning score
    const hasLegacyVision = legacyPlanning.hasLegacyVision === 'yes';
    const hasLegacyLetter = legacyPlanning.hasLegacyLetter === 'yes';
    const legacyScore = ([hasLegacyVision, hasLegacyLetter].filter(Boolean).length / 2) * 100;

    // Weight the scores based on importance
    const weightedDocumentScore = documentScore * 0.3; // 30%
    const weightedEstateScore = estateScore * 0.25; // 25%
    const weightedTaxScore = taxScore * 0.2; // 20%
    const weightedGivingScore = givingScore * 0.1; // 10%
    const weightedLegacyScore = legacyScore * 0.15; // 15%

    // Calculate overall score
    return (
      weightedDocumentScore +
      weightedEstateScore +
      weightedTaxScore +
      weightedGivingScore +
      weightedLegacyScore
    );
  };

  const legacyReadinessScore = calculateLegacyReadiness();

  // Determine legacy readiness status
  const getLegacyReadinessStatus = () => {
    if (legacyReadinessScore >= 80) return 'Well Prepared';
    if (legacyReadinessScore >= 60) return 'Adequately Prepared';
    if (legacyReadinessScore >= 40) return 'Partially Prepared';
    if (legacyReadinessScore >= 20) return 'Minimally Prepared';
    return 'Unprepared';
  };

  // Get color based on score
  const getScoreColor = (score: number) => {
    if (score >= 80) return theme.colors.success.main;
    if (score >= 60) return theme.colors.success.light;
    if (score >= 40) return theme.colors.warning.main;
    if (score >= 20) return theme.colors.warning.dark;
    return theme.colors.error.main;
  };

  // Get recommendations based on legacy planning gaps
  const getRecommendations = () => {
    const recommendations = [];

    // Estate document recommendations
    if (!hasWill) {
      recommendations.push({
        title: 'Create a Will',
        description:
          'You do not have a will in place. This is a fundamental estate planning document that ensures your assets are distributed according to your wishes.',
        priority: 'High',
        action: 'estate_documents',
      });
    }

    if (!hasTrust && netWorth > 1000000) {
      recommendations.push({
        title: 'Consider a Trust',
        description:
          'Given your net worth, a trust may provide benefits for asset protection, privacy, and avoiding probate.',
        priority: 'Medium',
        action: 'estate_documents',
      });
    }

    if (!hasPowerOfAttorney) {
      recommendations.push({
        title: 'Establish Power of Attorney',
        description:
          'A power of attorney designates someone to make financial decisions if you become incapacitated. This is an essential document for all adults.',
        priority: 'High',
        action: 'estate_documents',
      });
    }

    if (!hasAdvanceDirective) {
      recommendations.push({
        title: 'Create an Advance Healthcare Directive',
        description:
          'An advance directive specifies your healthcare wishes if you cannot communicate them. This provides guidance to your family and medical providers.',
        priority: 'High',
        action: 'estate_documents',
      });
    }

    // Tax planning recommendations
    if (!hasTaxStrategy) {
      recommendations.push({
        title: 'Develop a Tax Strategy',
        description:
          'You do not have a comprehensive tax strategy. Working with a tax professional can help minimize your tax burden and maximize wealth transfer.',
        priority: 'Medium',
        action: 'tax_planning',
      });
    }

    // Estate planning recommendations
    if (estatePlanning.hasEstateStrategy !== 'yes') {
      recommendations.push({
        title: 'Create an Estate Strategy',
        description:
          'Develop a comprehensive estate strategy to ensure your assets are distributed according to your wishes while minimizing taxes and complications.',
        priority: 'Medium',
        action: 'estate_planning',
      });
    }

    // Legacy planning recommendations
    if (legacyPlanning.hasLegacyVision !== 'yes') {
      recommendations.push({
        title: 'Define Your Legacy Vision',
        description:
          'Take time to articulate your values and the impact you want to have on future generations and causes you care about.',
        priority: 'Medium',
        action: 'legacy_planning',
      });
    }

    if (legacyPlanning.hasLegacyLetter !== 'yes') {
      recommendations.push({
        title: 'Write a Legacy Letter',
        description:
          'Consider writing a legacy letter or ethical will to share your values, life lessons, and wishes with your loved ones.',
        priority: 'Low',
        action: 'legacy_planning',
      });
    }

    // If all looks good, provide positive reinforcement
    if (recommendations.length === 0) {
      recommendations.push({
        title: 'Review Your Legacy Plan Annually',
        description:
          'Your legacy plan is well-established. Continue to review it annually and update as needed when life circumstances change.',
        priority: 'Low',
        action: 'legacy_planning',
      });
    }

    return recommendations;
  };

  useEffect(() => {
    // Set active direction to West
    if (updateActiveDirection) {
      updateActiveDirection('west');
    }

    // Update progress in Financial Compass context
    if (updateWestProgress) {
      updateWestProgress(progressPercentage);
    }

    // Check which sections are completed
    if (westSections) {
      const completed = westSections
        .filter((section) => section.isCompleted)
        .map((section) => section.id);
      setCompletedSections(completed);
    }
  }, [westSections, progressPercentage, updateActiveDirection, updateWestProgress]);

  const handleSectionClick = (sectionId: string) => {
    if (onSectionSelect) {
      onSectionSelect(sectionId);
    }
  };

  // Toggle legacy insights visibility
  const toggleLegacyInsights = () => {
    setShowLegacyInsights(!showLegacyInsights);
  };

  return (
    <TrackerContainer theme={theme}>
      <TrackerHeader theme={theme}>
        <DirectionTitle theme={theme}>West: Your Legacy & Impact</DirectionTitle>
        <DirectionDescription theme={theme}>
          The West direction helps you plan for your legacy - estate planning, tax strategies, and
          creating lasting impact through charitable giving.
        </DirectionDescription>
      </TrackerHeader>

      {/* Legacy Dashboard */}
      <DashboardContainer>
        {/* Legacy Readiness */}
        <DashboardSection>
          <DashboardCard theme={theme}>
            <CardHeader>
              <CardTitle theme={theme}>Legacy Readiness</CardTitle>
              <ReadinessScoreValue score={legacyReadinessScore}>
                {Math.round(legacyReadinessScore)}
              </ReadinessScoreValue>
            </CardHeader>
            <ReadinessScoreStatus score={legacyReadinessScore}>
              {getLegacyReadinessStatus()}
            </ReadinessScoreStatus>
            <ReadinessScoreBar theme={theme}>
              <ReadinessScoreFill score={legacyReadinessScore} theme={theme} />
            </ReadinessScoreBar>

            <MetricBreakdown>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Documents</MetricBreakdownLabel>
                <MetricBreakdownValue>{Math.round(documentCompleteness)}%</MetricBreakdownValue>
              </MetricBreakdownItem>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Tax Strategy</MetricBreakdownLabel>
                <MetricBreakdownValue>{Math.round(taxStrategyCompleteness)}%</MetricBreakdownValue>
              </MetricBreakdownItem>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Estate Plan</MetricBreakdownLabel>
                <MetricBreakdownValue>
                  {estatePlanning.hasEstateStrategy === 'yes' ? 'Yes' : 'No'}
                </MetricBreakdownValue>
              </MetricBreakdownItem>
            </MetricBreakdown>

            <LegacyInsight theme={theme}>
              {legacyReadinessScore >= 80
                ? 'Your legacy plan is well-established. Continue to review it annually and update as needed when life circumstances change.'
                : legacyReadinessScore >= 60
                  ? 'Your legacy plan has a good foundation but needs some refinement. Focus on the recommendations below.'
                  : legacyReadinessScore >= 40
                    ? 'Your legacy plan needs significant attention. Prioritize the high-priority recommendations below.'
                    : 'Your legacy plan is in the early stages. Start with creating essential estate documents and defining your legacy vision.'}
            </LegacyInsight>
          </DashboardCard>
        </DashboardSection>

        {/* Estate Documents */}
        <DashboardSection>
          <DashboardCard theme={theme}>
            <CardHeader>
              <CardTitle theme={theme}>Estate Documents</CardTitle>
              <CardIcon positive={documentCompleteness >= 75}>📄</CardIcon>
            </CardHeader>
            <MetricHighlight positive={documentCompleteness >= 75}>
              {Math.round(documentCompleteness)}% Complete
            </MetricHighlight>
            <MetricDescription>
              {documentCompleteness >= 75
                ? 'Essential documents in place'
                : documentCompleteness >= 50
                  ? 'Some documents missing'
                  : 'Most documents missing'}
            </MetricDescription>

            <DocumentStatusList>
              <DocumentStatusItem status={hasWill ? 'complete' : 'incomplete'}>
                <DocumentStatusIcon status={hasWill ? 'complete' : 'incomplete'}>
                  {hasWill ? '✓' : '✗'}
                </DocumentStatusIcon>
                <DocumentStatusName>Will</DocumentStatusName>
              </DocumentStatusItem>

              <DocumentStatusItem status={hasTrust ? 'complete' : 'incomplete'}>
                <DocumentStatusIcon status={hasTrust ? 'complete' : 'incomplete'}>
                  {hasTrust ? '✓' : '✗'}
                </DocumentStatusIcon>
                <DocumentStatusName>Trust</DocumentStatusName>
              </DocumentStatusItem>

              <DocumentStatusItem status={hasPowerOfAttorney ? 'complete' : 'incomplete'}>
                <DocumentStatusIcon status={hasPowerOfAttorney ? 'complete' : 'incomplete'}>
                  {hasPowerOfAttorney ? '✓' : '✗'}
                </DocumentStatusIcon>
                <DocumentStatusName>Power of Attorney</DocumentStatusName>
              </DocumentStatusItem>

              <DocumentStatusItem status={hasAdvanceDirective ? 'complete' : 'incomplete'}>
                <DocumentStatusIcon status={hasAdvanceDirective ? 'complete' : 'incomplete'}>
                  {hasAdvanceDirective ? '✓' : '✗'}
                </DocumentStatusIcon>
                <DocumentStatusName>Advance Directive</DocumentStatusName>
              </DocumentStatusItem>
            </DocumentStatusList>

            <DocumentsInsight theme={theme}>
              {documentCompleteness === 100
                ? 'All essential estate documents are in place. Review them periodically to ensure they remain current with your wishes.'
                : !hasWill
                  ? 'Creating a will should be your top priority. This essential document ensures your assets are distributed according to your wishes.'
                  : !hasPowerOfAttorney
                    ? 'Consider establishing a power of attorney to designate someone to make financial decisions if you become incapacitated.'
                    : !hasAdvanceDirective
                      ? 'An advance healthcare directive would complete your essential documents, specifying your healthcare wishes if you cannot communicate them.'
                      : !hasTrust && netWorth > 1000000
                        ? 'Given your net worth, consider establishing a trust for asset protection, privacy, and avoiding probate.'
                        : 'Continue building your estate document portfolio to ensure your wishes are legally documented.'}
            </DocumentsInsight>
          </DashboardCard>
        </DashboardSection>

        {/* Tax Planning */}
        <DashboardSection>
          <DashboardCard theme={theme}>
            <CardHeader>
              <CardTitle theme={theme}>Tax Planning</CardTitle>
              <CardIcon positive={taxStrategyCompleteness >= 75}>📊</CardIcon>
            </CardHeader>
            <MetricHighlight positive={taxStrategyCompleteness >= 75}>
              {Math.round(taxStrategyCompleteness)}% Optimized
            </MetricHighlight>
            <MetricDescription>
              {taxStrategyCompleteness >= 75
                ? 'Comprehensive tax strategy'
                : taxStrategyCompleteness >= 50
                  ? 'Partial tax strategy'
                  : 'Minimal tax planning'}
            </MetricDescription>

            <TaxStrategyList>
              <TaxStrategyItem status={hasTaxStrategy ? 'active' : 'inactive'}>
                <TaxStrategyIcon status={hasTaxStrategy ? 'active' : 'inactive'}>
                  {hasTaxStrategy ? '✓' : '✗'}
                </TaxStrategyIcon>
                <TaxStrategyName>Comprehensive Tax Strategy</TaxStrategyName>
              </TaxStrategyItem>

              <TaxStrategyItem status={usesTaxDeferredAccounts ? 'active' : 'inactive'}>
                <TaxStrategyIcon status={usesTaxDeferredAccounts ? 'active' : 'inactive'}>
                  {usesTaxDeferredAccounts ? '✓' : '✗'}
                </TaxStrategyIcon>
                <TaxStrategyName>Tax-Deferred Accounts</TaxStrategyName>
              </TaxStrategyItem>

              <TaxStrategyItem status={usesRothAccounts ? 'active' : 'inactive'}>
                <TaxStrategyIcon status={usesRothAccounts ? 'active' : 'inactive'}>
                  {usesRothAccounts ? '✓' : '✗'}
                </TaxStrategyIcon>
                <TaxStrategyName>Roth Accounts</TaxStrategyName>
              </TaxStrategyItem>

              <TaxStrategyItem status={hasTaxLossHarvesting ? 'active' : 'inactive'}>
                <TaxStrategyIcon status={hasTaxLossHarvesting ? 'active' : 'inactive'}>
                  {hasTaxLossHarvesting ? '✓' : '✗'}
                </TaxStrategyIcon>
                <TaxStrategyName>Tax-Loss Harvesting</TaxStrategyName>
              </TaxStrategyItem>
            </TaxStrategyList>

            <TaxInsight theme={theme}>
              {taxStrategyCompleteness >= 75
                ? 'Your tax strategy is comprehensive. Continue to review annually with a tax professional to adapt to changing tax laws.'
                : !hasTaxStrategy
                  ? 'Developing a comprehensive tax strategy with a professional could significantly reduce your tax burden and maximize wealth transfer.'
                  : !usesTaxDeferredAccounts
                    ? 'Consider utilizing tax-deferred accounts like 401(k)s or traditional IRAs to reduce your current tax liability.'
                    : !usesRothAccounts
                      ? 'Roth accounts could provide tax-free growth and withdrawals in retirement, diversifying your tax exposure.'
                      : !hasTaxLossHarvesting
                        ? 'Tax-loss harvesting could help offset capital gains and reduce your tax burden on investment returns.'
                        : 'Continue refining your tax strategy to minimize taxes and maximize wealth preservation.'}
            </TaxInsight>
          </DashboardCard>
        </DashboardSection>

        {/* Charitable Giving */}
        <DashboardSection>
          <DashboardCard theme={theme}>
            <CardHeader>
              <CardTitle theme={theme}>Charitable Impact</CardTitle>
              <CardIcon
                positive={
                  charitableGiving.hasCharitableStrategy === 'yes' || annualCharitableGiving > 0
                }
              >
                💰
              </CardIcon>
            </CardHeader>
            <MetricHighlight positive={annualCharitableGiving > 0}>
              {annualCharitableGiving > 0
                ? formatCurrency(annualCharitableGiving) + '/year'
                : 'No Current Giving'}
            </MetricHighlight>
            <MetricDescription>
              {plannedBequest > 0
                ? `${formatCurrency(plannedBequest)} planned bequest`
                : 'No planned charitable bequest'}
            </MetricDescription>

            <MetricBreakdown>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Giving Strategy</MetricBreakdownLabel>
                <MetricBreakdownValue>
                  {charitableGiving.hasCharitableStrategy === 'yes' ? 'Yes' : 'No'}
                </MetricBreakdownValue>
              </MetricBreakdownItem>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Annual Impact</MetricBreakdownLabel>
                <MetricBreakdownValue>
                  {annualCharitableGiving > 0 ? formatCurrency(annualCharitableGiving) : 'None'}
                </MetricBreakdownValue>
              </MetricBreakdownItem>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Legacy Gift</MetricBreakdownLabel>
                <MetricBreakdownValue>
                  {plannedBequest > 0 ? formatCurrency(plannedBequest) : 'None'}
                </MetricBreakdownValue>
              </MetricBreakdownItem>
            </MetricBreakdown>

            <CharitableInsight theme={theme}>
              {charitableGiving.hasCharitableStrategy === 'yes' &&
              annualCharitableGiving > 0 &&
              plannedBequest > 0
                ? 'Your charitable giving strategy is well-established, with both current giving and planned legacy gifts.'
                : charitableGiving.hasCharitableStrategy === 'yes'
                  ? 'You have a charitable strategy in place. Consider how you might integrate both current giving and legacy gifts.'
                  : annualCharitableGiving > 0
                    ? "You're making charitable contributions. Consider developing a formal charitable strategy to maximize your impact."
                    : 'Consider how charitable giving might align with your values and legacy goals, potentially with tax benefits.'}
            </CharitableInsight>
          </DashboardCard>
        </DashboardSection>
      </DashboardContainer>

      {/* Priority Actions */}
      <ActionSection theme={theme}>
        <ActionSectionTitle theme={theme}>Priority Actions</ActionSectionTitle>
        <ActionCards>
          {getRecommendations().map((recommendation, index) => (
            <ActionCard
              key={index}
              priority={recommendation.priority}
              theme={theme}
              onClick={() => recommendation.action && handleSectionClick(recommendation.action)}
            >
              <ActionHeader>
                <ActionTitle>{recommendation.title}</ActionTitle>
                <PriorityBadge priority={recommendation.priority} theme={theme}>
                  {recommendation.priority}
                </PriorityBadge>
              </ActionHeader>
              <ActionDescription>{recommendation.description}</ActionDescription>
              <ActionButtonContainer>
                <ActionButton theme={theme}>Take Action</ActionButton>
              </ActionButtonContainer>
            </ActionCard>
          ))}
        </ActionCards>
      </ActionSection>

      {/* Legacy Journey */}
      <JourneySection theme={theme}>
        <JourneySectionTitle theme={theme}>Your Legacy Journey</JourneySectionTitle>
        <JourneyDescription theme={theme}>
          Complete these sections to build a comprehensive legacy plan that reflects your values and
          wishes.
        </JourneyDescription>

        <JourneySteps>
          {westSections?.map((section) => (
            <JourneyStep
              key={section.id}
              onClick={() => handleSectionClick(section.id)}
              theme={theme}
              isActive={section.isActive}
            >
              <JourneyStepIcon theme={theme}>{section.icon || '○'}</JourneyStepIcon>
              <JourneyStepContent>
                <JourneyStepTitle theme={theme}>{section.title}</JourneyStepTitle>
                {section.description && (
                  <JourneyStepDescription theme={theme}>
                    {section.description}
                  </JourneyStepDescription>
                )}
              </JourneyStepContent>
              <JourneyStepArrow theme={theme}>→</JourneyStepArrow>
            </JourneyStep>
          ))}
        </JourneySteps>
      </JourneySection>
    </TrackerContainer>
  );
};

// Styled components
const TrackerContainer = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.secondary};
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const TrackerHeader = styled.div<{ theme: any }>`
  margin-bottom: 24px;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 16px;
  position: relative;
`;

const DirectionTitle = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
  font-weight: bold;
`;

const DirectionDescription = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0 0 16px 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const InsightsButton = styled.button<{ theme: any; isActive: boolean }>`
  padding: 8px 16px;
  border-radius: 4px;
  background-color: ${(props) =>
    props.isActive ? props.theme.colors.primary.main : props.theme.colors.background.tertiary};
  color: ${(props) =>
    props.isActive ? props.theme.colors.text.onPrimary : props.theme.colors.text.primary};
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 16px;

  &:hover {
    background-color: ${(props) =>
      props.isActive ? props.theme.colors.primary.dark : props.theme.colors.background.hover};
  }
`;

const ProgressIndicator = styled.div`
  margin-top: 16px;
`;

const ProgressText = styled.div<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin-bottom: 8px;
  font-size: 0.9rem;
`;

const ProgressBar = styled.div<{ theme: any }>`
  height: 8px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-radius: 4px;
  overflow: hidden;
`;

const ProgressFill = styled.div<{ progress: number; theme: any }>`
  height: 100%;
  width: ${(props) => props.progress}%;
  background-color: ${(props) => props.theme.colors.primary.main};
  transition: width 0.3s ease;
`;

// Legacy Insights Styled Components
const LegacyInsights = styled.div<{ theme: any }>`
  margin: 0 0 24px 0;
  padding: 20px;
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
`;

const InsightsTitle = styled.h3<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 20px 0;
  font-size: 1.3rem;
  font-weight: 600;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 12px;
`;

// Readiness Score Card
const ReadinessScoreCard = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
`;

const ReadinessScoreHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
`;

const ReadinessScoreTitle = styled.h4`
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
`;

const ReadinessScoreValue = styled.div<{ score: number }>`
  font-size: 1.8rem;
  font-weight: 700;
  color: ${(props) => {
    if (props.score >= 80) return '#10b981'; // success
    if (props.score >= 60) return '#22c55e'; // success light
    if (props.score >= 40) return '#f59e0b'; // warning
    if (props.score >= 20) return '#d97706'; // warning dark
    return '#ef4444'; // error
  }};
`;

const ReadinessScoreStatus = styled.div<{ score: number }>`
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: ${(props) => {
    if (props.score >= 80) return '#10b981'; // success
    if (props.score >= 60) return '#22c55e'; // success light
    if (props.score >= 40) return '#f59e0b'; // warning
    if (props.score >= 20) return '#d97706'; // warning dark
    return '#ef4444'; // error
  }};
`;

const ReadinessScoreBar = styled.div<{ theme: any }>`
  height: 8px;
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 4px;
  overflow: hidden;
`;

const ReadinessScoreFill = styled.div<{ score: number; theme: any }>`
  height: 100%;
  width: ${(props) => props.score}%;
  background-color: ${(props) => {
    if (props.score >= 80) return props.theme.colors.success.main;
    if (props.score >= 60) return props.theme.colors.success.light;
    if (props.score >= 40) return props.theme.colors.warning.main;
    if (props.score >= 20) return props.theme.colors.warning.dark;
    return props.theme.colors.error.main;
  }};
  transition: width 0.5s ease;
`;

// Documents Overview
const DocumentsOverview = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
`;

const DocumentsOverviewTitle = styled.h4<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 8px;
`;

const DocumentsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
`;

const DocumentCard = styled.div<{ theme: any; isComplete: boolean }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 6px;
  padding: 16px;
  text-align: center;
  border: 1px solid
    ${(props) =>
      props.isComplete ? props.theme.colors.success.light : props.theme.colors.error.light};
  cursor: pointer;
  transition: transform 0.2s ease;

  &:hover {
    transform: translateY(-4px);
  }
`;

const DocumentIcon = styled.div<{ isComplete: boolean }>`
  font-size: 2rem;
  margin-bottom: 8px;
  color: ${(props) => (props.isComplete ? '#10b981' : '#ef4444')};
`;

const DocumentTitle = styled.div`
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 8px;
`;

const DocumentStatus = styled.div<{ isComplete: boolean; theme: any }>`
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  background-color: ${(props) =>
    props.isComplete ? props.theme.colors.success.light : props.theme.colors.error.light};
  color: ${(props) =>
    props.isComplete ? props.theme.colors.success.main : props.theme.colors.error.main};
`;

// Metrics Grid
const MetricsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
`;

const MetricCard = styled.div<{ theme: any }>`
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-radius: 8px;
`;

const MetricIcon = styled.div`
  font-size: 1.8rem;
  margin-right: 16px;
  color: #4f46e5;
`;

const MetricContent = styled.div`
  flex: 1;
`;

const MetricLabel = styled.div`
  font-size: 0.9rem;
  color: ${(props) => props.theme.colors.text.secondary};
  margin-bottom: 4px;
`;

const MetricValue = styled.div`
  font-size: 1.2rem;
  font-weight: 700;
  color: ${(props) => props.theme.colors.text.primary};
  margin-bottom: 4px;
`;

const MetricDescription = styled.div`
  font-size: 0.8rem;
  color: ${(props) => props.theme.colors.text.secondary};
`;

// Recommendations Section
const RecommendationsSection = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-radius: 8px;
  padding: 16px;
`;

const RecommendationsTitle = styled.h4<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 8px;
`;

const RecommendationsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const RecommendationItem = styled.div<{ priority: string; theme: any }>`
  padding: 12px;
  border-radius: 6px;
  background-color: ${(props) => props.theme.colors.background.paper};
  border-left: 4px solid
    ${(props) =>
      props.priority === 'High'
        ? props.theme.colors.error.main
        : props.priority === 'Medium'
          ? props.theme.colors.warning.main
          : props.theme.colors.success.main};
  cursor: pointer;
  transition: transform 0.2s ease;

  &:hover {
    transform: translateX(4px);
  }
`;

const RecommendationHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
`;

const RecommendationTitle = styled.h5`
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
`;

const RecommendationDescription = styled.p`
  margin: 0;
  font-size: 0.9rem;
  color: ${(props) => props.theme.colors.text.secondary};
  line-height: 1.4;
`;

const PriorityBadge = styled.span<{ priority: string; theme: any }>`
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
  background-color: ${(props) =>
    props.priority === 'High'
      ? props.theme.colors.error.light
      : props.priority === 'Medium'
        ? props.theme.colors.warning.light
        : props.theme.colors.success.light};
  color: ${(props) =>
    props.priority === 'High'
      ? props.theme.colors.error.dark
      : props.priority === 'Medium'
        ? props.theme.colors.warning.dark
        : props.theme.colors.success.dark};
`;

// Sections List
const SectionsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const SectionItem = styled.div<{ isCompleted: boolean; isActive: boolean; theme: any }>`
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  background-color: ${(props) =>
    props.isActive ? props.theme.colors.background.highlight : props.theme.colors.background.main};
  border-left: 4px solid
    ${(props) =>
      props.isCompleted
        ? props.theme.colors.success.main
        : props.isActive
          ? props.theme.colors.primary.main
          : props.theme.colors.border.main};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.hover};
    transform: translateX(4px);
  }
`;

const SectionIcon = styled.div<{ isCompleted: boolean; theme: any }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: ${(props) =>
    props.isCompleted ? props.theme.colors.success.main : props.theme.colors.background.tertiary};
  color: ${(props) =>
    props.isCompleted ? props.theme.colors.text.onPrimary : props.theme.colors.text.secondary};
  margin-right: 16px;
  font-size: 16px;
  flex-shrink: 0;
`;

const SectionContent = styled.div`
  flex: 1;
`;

const SectionTitle = styled.h3<{ isCompleted: boolean; isActive: boolean; theme: any }>`
  margin: 0 0 4px 0;
  font-size: 1.1rem;
  color: ${(props) =>
    props.isCompleted
      ? props.theme.colors.success.dark
      : props.isActive
        ? props.theme.colors.primary.dark
        : props.theme.colors.text.primary};
`;

const SectionDescription = styled.p<{ theme: any }>`
  margin: 0;
  font-size: 0.9rem;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const CompletionDate = styled.div<{ theme: any }>`
  font-size: 0.8rem;
  color: ${(props) => props.theme.colors.text.tertiary};
  margin-top: 4px;
`;

const SectionStatus = styled.div<{ isCompleted: boolean; theme: any }>`
  font-size: 0.8rem;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: ${(props) =>
    props.isCompleted ? props.theme.colors.success.light : props.theme.colors.background.tertiary};
  color: ${(props) =>
    props.isCompleted ? props.theme.colors.success.dark : props.theme.colors.text.secondary};
  margin-left: 16px;
`;

const CompletionMessage = styled.div<{ theme: any }>`
  margin-top: 32px;
  padding: 24px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.success.light};
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const CompletionIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
`;

const CompletionText = styled.p`
  color: ${(props) => props.theme.colors.text.primary};
  font-size: 1.1rem;
  line-height: 1.5;
  margin: 0 0 24px 0;
`;

const NextDirectionButton = styled.button<{ theme: any }>`
  padding: 12px 24px;
  border-radius: 4px;
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.text.onPrimary};
  border: none;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${(props) => props.theme.colors.primary.light};
  }
`;

// Dashboard Components
const DashboardContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
`;

const DashboardSection = styled.div`
  display: flex;
  flex-direction: column;
`;

const DashboardCard = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  height: 100%;
  display: flex;
  flex-direction: column;
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }
`;

const CardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const CardTitle = styled.h3<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
`;

const CardIcon = styled.div<{ positive: boolean }>`
  font-size: 1.8rem;
  color: ${(props) => (props.positive ? '#10b981' : '#ef4444')};
`;

const MetricHighlight = styled.div<{ positive: boolean }>`
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 8px;
  color: ${(props) => (props.positive ? '#10b981' : '#ef4444')};
`;

// This component is already defined above

const MetricBreakdown = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
  padding: 12px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-radius: 8px;
`;

const MetricBreakdownItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const MetricBreakdownLabel = styled.div`
  font-size: 0.85rem;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const MetricBreakdownValue = styled.div`
  font-size: 0.9rem;
  font-weight: 600;
  color: ${(props) => props.theme.colors.text.primary};
`;

// Document Status List
const DocumentStatusList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
`;

const DocumentStatusItem = styled.div<{ status: string }>`
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-radius: 6px;
`;

const DocumentStatusIcon = styled.div<{ status: string }>`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: ${(props) =>
    props.status === 'complete' ? props.theme.colors.success.main : props.theme.colors.error.main};
  color: white;
  margin-right: 12px;
  font-size: 0.8rem;
`;

const DocumentStatusName = styled.div`
  font-size: 0.9rem;
  font-weight: 500;
  color: ${(props) => props.theme.colors.text.primary};
`;

// Tax Strategy List
const TaxStrategyList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
`;

const TaxStrategyItem = styled.div<{ status: string }>`
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-radius: 6px;
`;

const TaxStrategyIcon = styled.div<{ status: string }>`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: ${(props) =>
    props.status === 'active' ? props.theme.colors.success.main : props.theme.colors.error.main};
  color: white;
  margin-right: 12px;
  font-size: 0.8rem;
`;

const TaxStrategyName = styled.div`
  font-size: 0.9rem;
  font-weight: 500;
  color: ${(props) => props.theme.colors.text.primary};
`;

// Insight Components
const LegacyInsight = styled.div<{ theme: any }>`
  font-size: 0.9rem;
  line-height: 1.5;
  color: ${(props) => props.theme.colors.text.primary};
  margin-top: auto;
  padding-top: 12px;
  border-top: 1px solid ${(props) => props.theme.colors.border.main};
`;

const DocumentsInsight = styled.div<{ theme: any }>`
  font-size: 0.9rem;
  line-height: 1.5;
  color: ${(props) => props.theme.colors.text.primary};
  margin-top: auto;
  padding-top: 12px;
  border-top: 1px solid ${(props) => props.theme.colors.border.main};
`;

const TaxInsight = styled.div<{ theme: any }>`
  font-size: 0.9rem;
  line-height: 1.5;
  color: ${(props) => props.theme.colors.text.primary};
  margin-top: auto;
  padding-top: 12px;
  border-top: 1px solid ${(props) => props.theme.colors.border.main};
`;

const CharitableInsight = styled.div<{ theme: any }>`
  font-size: 0.9rem;
  line-height: 1.5;
  color: ${(props) => props.theme.colors.text.primary};
  margin-top: auto;
  padding-top: 12px;
  border-top: 1px solid ${(props) => props.theme.colors.border.main};
`;

// Action Section
const ActionSection = styled.div<{ theme: any }>`
  margin-bottom: 32px;
  padding: 20px;
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
`;

const ActionSectionTitle = styled.h3<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 20px 0;
  font-size: 1.3rem;
  font-weight: 600;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 12px;
`;

const ActionCards = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
`;

const ActionCard = styled.div<{ priority: string; theme: any }>`
  padding: 16px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-left: 4px solid
    ${(props) =>
      props.priority === 'High'
        ? props.theme.colors.error.main
        : props.priority === 'Medium'
          ? props.theme.colors.warning.main
          : props.theme.colors.success.main};
  cursor: pointer;
  transition: transform 0.2s ease;
  display: flex;
  flex-direction: column;
  height: 100%;

  &:hover {
    transform: translateY(-4px);
  }
`;

const ActionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const ActionTitle = styled.h4`
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
`;

const ActionDescription = styled.p`
  margin: 0;
  font-size: 0.9rem;
  color: ${(props) => props.theme.colors.text.secondary};
  line-height: 1.5;
  flex: 1;
`;

const ActionButtonContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  align-items: center;
`;

const ActionButton = styled.div<{ theme: any }>`
  display: inline-block;
  padding: 8px 16px;
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.text.onPrimary};
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

// Journey Section
const JourneySection = styled.div<{ theme: any }>`
  padding: 20px;
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
`;

const JourneySectionTitle = styled.h3<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.3rem;
  font-weight: 600;
`;

const JourneyDescription = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0 0 20px 0;
  font-size: 0.9rem;
  line-height: 1.5;
`;

const JourneySteps = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const JourneyStep = styled.div<{ theme: any; isActive: boolean }>`
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  background-color: ${(props) =>
    props.isActive
      ? props.theme.colors.background.highlight
      : props.theme.colors.background.tertiary};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.hover};
    transform: translateX(8px);
  }
`;

const JourneyStepIcon = styled.div<{ theme: any }>`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.text.onPrimary};
  margin-right: 16px;
  font-size: 1.2rem;
`;

const JourneyStepContent = styled.div`
  flex: 1;
`;

const JourneyStepTitle = styled.h4<{ theme: any }>`
  margin: 0 0 4px 0;
  font-size: 1.1rem;
  color: ${(props) => props.theme.colors.text.primary};
`;

const JourneyStepDescription = styled.p<{ theme: any }>`
  margin: 0;
  font-size: 0.85rem;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const JourneyStepArrow = styled.div<{ theme: any }>`
  font-size: 1.5rem;
  color: ${(props) => props.theme.colors.text.secondary};
  margin-left: 16px;
`;

export default WestDirectionTracker;
