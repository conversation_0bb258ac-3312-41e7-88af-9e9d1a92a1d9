/**
 * South Direction Tracker Component
 *
 * This component displays the progress of the South direction sections,
 * provides protection and risk insights, and allows navigation to individual sections.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { useGuidedJourney } from '../../../GuidedJourney/context/GuidedJourneyContext';
import { formatCurrency, formatPercentage } from '../../../../utils/formatters';

interface SouthDirectionTrackerProps {
  onSectionSelect?: (sectionId: string) => void;
}

const SouthDirectionTracker: React.FC<SouthDirectionTrackerProps> = ({ onSectionSelect }) => {
  const { theme } = useTheme();
  const { updateActiveDirection } = useGuidedJourney();
  const { southSections, updateSouthProgress, updateSouthSectionStatus, data } =
    useFinancialCompass();
  const [completedSections, setCompletedSections] = useState<string[]>([]);
  const [showProtectionInsights, setShowProtectionInsights] = useState(false);

  // Calculate overall progress for South direction
  const totalSections = southSections?.length || 0;
  const completedCount = completedSections.length;
  const progressPercentage = totalSections > 0 ? (completedCount / totalSections) * 100 : 0;

  // Extract insurance and risk data
  const insuranceCoverage = data.south?.insuranceCoverage || {
    lifeInsurance: { coverage: '0', type: 'None' },
    disabilityInsurance: { coverage: '0', type: 'None' },
    healthInsurance: { coverage: '0', type: 'None' },
    homeownersInsurance: { coverage: '0', type: 'None' },
    autoInsurance: { coverage: '0', type: 'None' },
    liabilityInsurance: { coverage: '0', type: 'None' },
  };
  const riskTolerance = data.south?.riskTolerance || {
    score: 0,
  };
  const protectionGap = data.south?.protectionGap || {};
  const healthcarePlanning = data.south?.healthcarePlanning || {
    estimatedAnnualHealthcareExpenses: '0',
  };

  // Extract personal and financial data
  const personalInfo = data.north?.personalInformation || {};
  const familyInfo = data.north?.familyInformation || {};
  const assets = data.north?.assets || {};
  const liabilities = data.north?.liabilities || {};
  const netWorthDetails = data.north?.netWorthDetails || {
    totalAssets: '0',
    totalLiabilities: '0',
  };
  const incomeDetails = data.north?.incomeDetails || {
    primaryIncome: '0',
  };

  // Calculate key protection metrics
  const totalAssets = parseFloat(netWorthDetails.totalAssets || '0');
  const totalLiabilities = parseFloat(netWorthDetails.totalLiabilities || '0');
  const annualIncome = parseFloat(incomeDetails.primaryIncome || '0') * 12;

  // Calculate insurance coverage ratios
  const lifeInsuranceCoverage = parseFloat(insuranceCoverage.lifeInsurance?.coverage || '0');
  const disabilityInsuranceCoverage = parseFloat(
    insuranceCoverage.disabilityInsurance?.coverage || '0'
  );
  const healthInsuranceCoverage = parseFloat(insuranceCoverage.healthInsurance?.coverage || '0');
  const homeInsuranceCoverage = parseFloat(insuranceCoverage.homeownersInsurance?.coverage || '0');
  const autoInsuranceCoverage = parseFloat(insuranceCoverage.autoInsurance?.coverage || '0');
  const liabilityInsuranceCoverage = parseFloat(
    insuranceCoverage.liabilityInsurance?.coverage || '0'
  );

  // Calculate recommended coverage amounts
  const recommendedLifeInsurance = annualIncome * 10; // 10x annual income
  const recommendedDisabilityInsurance = annualIncome * 0.6; // 60% of annual income
  const recommendedLiabilityInsurance = Math.max(totalAssets, 500000); // Greater of total assets or $500k

  // Calculate coverage gaps
  const lifeInsuranceGap = Math.max(0, recommendedLifeInsurance - lifeInsuranceCoverage);
  const disabilityInsuranceGap = Math.max(
    0,
    recommendedDisabilityInsurance - disabilityInsuranceCoverage
  );
  const liabilityInsuranceGap = Math.max(
    0,
    recommendedLiabilityInsurance - liabilityInsuranceCoverage
  );

  // Calculate coverage percentages
  const lifeInsurancePercentage =
    recommendedLifeInsurance > 0 ? (lifeInsuranceCoverage / recommendedLifeInsurance) * 100 : 0;
  const disabilityInsurancePercentage =
    recommendedDisabilityInsurance > 0
      ? (disabilityInsuranceCoverage / recommendedDisabilityInsurance) * 100
      : 0;
  const liabilityInsurancePercentage =
    recommendedLiabilityInsurance > 0
      ? (liabilityInsuranceCoverage / recommendedLiabilityInsurance) * 100
      : 0;

  // Calculate overall protection score
  const calculateProtectionScore = () => {
    // Base scores for each insurance type (out of 100)
    const lifeScore = Math.min(100, lifeInsurancePercentage);
    const disabilityScore = Math.min(100, disabilityInsurancePercentage);
    const liabilityScore = Math.min(100, liabilityInsurancePercentage);

    // Health insurance score based on coverage type
    const healthScore = healthInsuranceCoverage > 0 ? 80 : 0;

    // Home insurance score
    const homeScore = homeInsuranceCoverage > 0 ? 80 : 0;

    // Auto insurance score
    const autoScore = autoInsuranceCoverage > 0 ? 80 : 0;

    // Weight the scores based on importance
    const weightedLifeScore = lifeScore * 0.3; // 30%
    const weightedDisabilityScore = disabilityScore * 0.2; // 20%
    const weightedHealthScore = healthScore * 0.2; // 20%
    const weightedHomeScore = homeScore * 0.1; // 10%
    const weightedAutoScore = autoScore * 0.1; // 10%
    const weightedLiabilityScore = liabilityScore * 0.1; // 10%

    // Calculate overall score
    return (
      weightedLifeScore +
      weightedDisabilityScore +
      weightedHealthScore +
      weightedHomeScore +
      weightedAutoScore +
      weightedLiabilityScore
    );
  };

  const protectionScore = calculateProtectionScore();

  // Determine protection status
  const getProtectionStatus = () => {
    if (protectionScore >= 80) return 'Well Protected';
    if (protectionScore >= 60) return 'Adequately Protected';
    if (protectionScore >= 40) return 'Partially Protected';
    if (protectionScore >= 20) return 'Underprotected';
    return 'Critically Underprotected';
  };

  // Get color based on score
  const getScoreColor = (score: number) => {
    if (score >= 80) return theme.colors.success.main;
    if (score >= 60) return theme.colors.success.light;
    if (score >= 40) return theme.colors.warning.main;
    if (score >= 20) return theme.colors.warning.dark;
    return theme.colors.error.main;
  };

  // Get recommendations based on protection gaps
  const getRecommendations = () => {
    const recommendations = [];

    // Life insurance recommendations
    if (lifeInsurancePercentage < 70) {
      recommendations.push({
        title: 'Increase Life Insurance Coverage',
        description: `Your life insurance covers only ${formatPercentage(lifeInsurancePercentage)}% of the recommended amount. Consider increasing your coverage by ${formatCurrency(lifeInsuranceGap)}.`,
        priority: lifeInsurancePercentage < 40 ? 'High' : 'Medium',
        action: 'insurance_coverage',
      });
    }

    // Disability insurance recommendations
    if (disabilityInsurancePercentage < 70) {
      recommendations.push({
        title: 'Enhance Disability Insurance',
        description: `Your disability insurance covers only ${formatPercentage(disabilityInsurancePercentage)}% of your income. Consider increasing your coverage to protect your earning potential.`,
        priority: disabilityInsurancePercentage < 40 ? 'High' : 'Medium',
        action: 'insurance_coverage',
      });
    }

    // Liability insurance recommendations
    if (liabilityInsurancePercentage < 70) {
      recommendations.push({
        title: 'Add Umbrella Liability Coverage',
        description: `Your liability protection is insufficient relative to your assets. Consider adding umbrella liability coverage of at least ${formatCurrency(liabilityInsuranceGap)}.`,
        priority: liabilityInsurancePercentage < 40 ? 'High' : 'Medium',
        action: 'insurance_coverage',
      });
    }

    // Health insurance recommendations
    if (healthInsuranceCoverage <= 0) {
      recommendations.push({
        title: 'Obtain Health Insurance',
        description:
          'You have not recorded any health insurance coverage. Health insurance is essential for protecting against catastrophic medical expenses.',
        priority: 'High',
        action: 'insurance_coverage',
      });
    }

    // Healthcare planning recommendations
    if (!healthcarePlanning.estimatedAnnualHealthcareExpenses) {
      recommendations.push({
        title: 'Complete Healthcare Planning',
        description:
          'You have not completed your healthcare planning. Understanding your healthcare needs and costs is crucial for financial security.',
        priority: 'Medium',
        action: 'healthcare_planning',
      });
    }

    // Risk tolerance recommendations
    if (!riskTolerance.score) {
      recommendations.push({
        title: 'Assess Your Risk Tolerance',
        description:
          'Complete your risk tolerance assessment to ensure your insurance coverage and investment strategies align with your comfort level with risk.',
        priority: 'Medium',
        action: 'risk_tolerance',
      });
    }

    // If all looks good, provide positive reinforcement
    if (recommendations.length === 0) {
      recommendations.push({
        title: 'Maintain Your Protection Strategy',
        description:
          'Your protection plan is solid. Continue to review your coverage annually to ensure it keeps pace with changes in your life and financial situation.',
        priority: 'Low',
        action: 'insurance_coverage',
      });
    }

    return recommendations;
  };

  useEffect(() => {
    // Set active direction to South
    if (updateActiveDirection) {
      updateActiveDirection('south');
    }

    // Update progress in Financial Compass context
    if (updateSouthProgress) {
      updateSouthProgress(progressPercentage);
    }

    // Check which sections are completed
    if (southSections) {
      const completed = southSections
        .filter((section) => section.isCompleted)
        .map((section) => section.id);
      setCompletedSections(completed);
    }
  }, [southSections, progressPercentage, updateActiveDirection, updateSouthProgress]);

  const handleSectionClick = (sectionId: string) => {
    // Update the active section
    southSections.forEach((section) => {
      const isActive = section.id === sectionId;
      updateActiveDirection && updateActiveDirection('south');

      // Only update if the active state is changing
      if (section.isActive !== isActive) {
        updateSouthSectionStatus(section.id, section.isCompleted, isActive);
      }
    });

    // Call the onSectionSelect callback
    if (onSectionSelect) {
      onSectionSelect(sectionId);
    }
  };

  // Toggle protection insights visibility
  const toggleProtectionInsights = () => {
    setShowProtectionInsights(!showProtectionInsights);
  };

  return (
    <TrackerContainer theme={theme}>
      <TrackerHeader theme={theme}>
        <DirectionTitle theme={theme}>South: What Protects You</DirectionTitle>
        <DirectionDescription theme={theme}>
          The South direction helps you build a comprehensive protection plan - insurance coverage,
          healthcare planning, and risk management.
        </DirectionDescription>
      </TrackerHeader>

      {/* Protection Dashboard */}
      <DashboardContainer>
        {/* Protection Score */}
        <DashboardSection>
          <DashboardCard theme={theme}>
            <CardHeader>
              <CardTitle theme={theme}>Protection Score</CardTitle>
              <ProtectionScoreValue score={protectionScore}>
                {Math.round(protectionScore)}
              </ProtectionScoreValue>
            </CardHeader>
            <ProtectionScoreStatus score={protectionScore}>
              {getProtectionStatus()}
            </ProtectionScoreStatus>
            <ProtectionScoreBar theme={theme}>
              <ProtectionScoreFill score={protectionScore} theme={theme} />
            </ProtectionScoreBar>

            <MetricBreakdown>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Life Insurance</MetricBreakdownLabel>
                <MetricBreakdownValue>{Math.round(lifeInsurancePercentage)}%</MetricBreakdownValue>
              </MetricBreakdownItem>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Disability</MetricBreakdownLabel>
                <MetricBreakdownValue>
                  {Math.round(disabilityInsurancePercentage)}%
                </MetricBreakdownValue>
              </MetricBreakdownItem>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Liability</MetricBreakdownLabel>
                <MetricBreakdownValue>
                  {Math.round(liabilityInsurancePercentage)}%
                </MetricBreakdownValue>
              </MetricBreakdownItem>
            </MetricBreakdown>

            <ProtectionInsight theme={theme}>
              {protectionScore >= 80
                ? 'Your protection plan is excellent. Continue to review annually to ensure it keeps pace with your changing needs.'
                : protectionScore >= 60
                  ? 'Your protection plan is good but has some gaps. Address the recommendations below to strengthen your safety net.'
                  : protectionScore >= 40
                    ? 'Your protection plan needs attention. Focus on the high-priority recommendations to improve your coverage.'
                    : 'Your protection plan has significant gaps. Take immediate action on the recommendations below.'}
            </ProtectionInsight>
          </DashboardCard>
        </DashboardSection>

        {/* Life Insurance */}
        <DashboardSection>
          <DashboardCard theme={theme}>
            <CardHeader>
              <CardTitle theme={theme}>Life Insurance</CardTitle>
              <CardIcon positive={lifeInsurancePercentage >= 70}>🛡️</CardIcon>
            </CardHeader>
            <MetricHighlight positive={lifeInsurancePercentage >= 70}>
              {formatCurrency(lifeInsuranceCoverage)}
            </MetricHighlight>
            <MetricDescription>
              {lifeInsurancePercentage >= 70
                ? 'Adequate coverage'
                : lifeInsurancePercentage >= 40
                  ? 'Partial coverage'
                  : 'Insufficient coverage'}
            </MetricDescription>

            <MetricBreakdown>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Coverage Ratio</MetricBreakdownLabel>
                <MetricBreakdownValue>{Math.round(lifeInsurancePercentage)}%</MetricBreakdownValue>
              </MetricBreakdownItem>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Recommended</MetricBreakdownLabel>
                <MetricBreakdownValue>
                  {formatCurrency(recommendedLifeInsurance)}
                </MetricBreakdownValue>
              </MetricBreakdownItem>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Coverage Gap</MetricBreakdownLabel>
                <MetricBreakdownValue>{formatCurrency(lifeInsuranceGap)}</MetricBreakdownValue>
              </MetricBreakdownItem>
            </MetricBreakdown>

            <CoverageInsight theme={theme}>
              {lifeInsurancePercentage >= 70
                ? 'Your life insurance coverage is adequate. It should provide sufficient protection for your dependents.'
                : lifeInsurancePercentage >= 40
                  ? `Consider increasing your life insurance by ${formatCurrency(lifeInsuranceGap)} to fully protect your dependents.`
                  : lifeInsuranceCoverage > 0
                    ? `Your life insurance coverage is significantly below the recommended amount. Increase by ${formatCurrency(lifeInsuranceGap)}.`
                    : "You don't have life insurance. Consider getting coverage to protect your dependents' financial future."}
            </CoverageInsight>
          </DashboardCard>
        </DashboardSection>

        {/* Disability Insurance */}
        <DashboardSection>
          <DashboardCard theme={theme}>
            <CardHeader>
              <CardTitle theme={theme}>Disability Insurance</CardTitle>
              <CardIcon positive={disabilityInsurancePercentage >= 70}>💼</CardIcon>
            </CardHeader>
            <MetricHighlight positive={disabilityInsurancePercentage >= 70}>
              {formatCurrency(disabilityInsuranceCoverage)}
            </MetricHighlight>
            <MetricDescription>
              {disabilityInsurancePercentage >= 70
                ? 'Adequate income protection'
                : disabilityInsurancePercentage >= 40
                  ? 'Partial income protection'
                  : 'Insufficient income protection'}
            </MetricDescription>

            <MetricBreakdown>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Coverage Ratio</MetricBreakdownLabel>
                <MetricBreakdownValue>
                  {Math.round(disabilityInsurancePercentage)}%
                </MetricBreakdownValue>
              </MetricBreakdownItem>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Recommended</MetricBreakdownLabel>
                <MetricBreakdownValue>
                  {formatCurrency(recommendedDisabilityInsurance)}
                </MetricBreakdownValue>
              </MetricBreakdownItem>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Coverage Gap</MetricBreakdownLabel>
                <MetricBreakdownValue>
                  {formatCurrency(disabilityInsuranceGap)}
                </MetricBreakdownValue>
              </MetricBreakdownItem>
            </MetricBreakdown>

            <CoverageInsight theme={theme}>
              {disabilityInsurancePercentage >= 70
                ? "Your disability insurance provides good income protection if you're unable to work due to illness or injury."
                : disabilityInsurancePercentage >= 40
                  ? `Your disability coverage provides partial protection. Consider increasing to ${formatCurrency(recommendedDisabilityInsurance)}.`
                  : disabilityInsuranceCoverage > 0
                    ? `Your disability insurance is insufficient. Increase coverage to protect your income if you can't work.`
                    : "You don't have disability insurance. This leaves your income unprotected if you become unable to work."}
            </CoverageInsight>
          </DashboardCard>
        </DashboardSection>

        {/* Other Insurance */}
        <DashboardSection>
          <DashboardCard theme={theme}>
            <CardHeader>
              <CardTitle theme={theme}>Other Protection</CardTitle>
              <CardIcon
                positive={healthInsuranceCoverage > 0 && liabilityInsurancePercentage >= 50}
              >
                🏥
              </CardIcon>
            </CardHeader>

            <InsuranceTypesList>
              <InsuranceTypeItem status={healthInsuranceCoverage > 0 ? 'good' : 'poor'}>
                <InsuranceTypeIcon status={healthInsuranceCoverage > 0 ? 'good' : 'poor'}>
                  {healthInsuranceCoverage > 0 ? '✓' : '✗'}
                </InsuranceTypeIcon>
                <InsuranceTypeContent>
                  <InsuranceTypeName>Health Insurance</InsuranceTypeName>
                  <InsuranceTypeValue>
                    {healthInsuranceCoverage > 0
                      ? insuranceCoverage.healthInsurance?.type || 'In Place'
                      : 'Not Covered'}
                  </InsuranceTypeValue>
                </InsuranceTypeContent>
              </InsuranceTypeItem>

              <InsuranceTypeItem status={homeInsuranceCoverage > 0 ? 'good' : 'poor'}>
                <InsuranceTypeIcon status={homeInsuranceCoverage > 0 ? 'good' : 'poor'}>
                  {homeInsuranceCoverage > 0 ? '✓' : '✗'}
                </InsuranceTypeIcon>
                <InsuranceTypeContent>
                  <InsuranceTypeName>Home Insurance</InsuranceTypeName>
                  <InsuranceTypeValue>
                    {homeInsuranceCoverage > 0
                      ? formatCurrency(homeInsuranceCoverage)
                      : 'Not Covered'}
                  </InsuranceTypeValue>
                </InsuranceTypeContent>
              </InsuranceTypeItem>

              <InsuranceTypeItem status={autoInsuranceCoverage > 0 ? 'good' : 'poor'}>
                <InsuranceTypeIcon status={autoInsuranceCoverage > 0 ? 'good' : 'poor'}>
                  {autoInsuranceCoverage > 0 ? '✓' : '✗'}
                </InsuranceTypeIcon>
                <InsuranceTypeContent>
                  <InsuranceTypeName>Auto Insurance</InsuranceTypeName>
                  <InsuranceTypeValue>
                    {autoInsuranceCoverage > 0
                      ? formatCurrency(autoInsuranceCoverage)
                      : 'Not Covered'}
                  </InsuranceTypeValue>
                </InsuranceTypeContent>
              </InsuranceTypeItem>

              <InsuranceTypeItem status={liabilityInsurancePercentage >= 50 ? 'good' : 'poor'}>
                <InsuranceTypeIcon status={liabilityInsurancePercentage >= 50 ? 'good' : 'poor'}>
                  {liabilityInsurancePercentage >= 50 ? '✓' : '✗'}
                </InsuranceTypeIcon>
                <InsuranceTypeContent>
                  <InsuranceTypeName>Liability Insurance</InsuranceTypeName>
                  <InsuranceTypeValue>
                    {liabilityInsuranceCoverage > 0
                      ? formatCurrency(liabilityInsuranceCoverage)
                      : 'Not Covered'}
                  </InsuranceTypeValue>
                </InsuranceTypeContent>
              </InsuranceTypeItem>
            </InsuranceTypesList>

            <OtherInsuranceInsight theme={theme}>
              {healthInsuranceCoverage > 0 &&
              homeInsuranceCoverage > 0 &&
              autoInsuranceCoverage > 0 &&
              liabilityInsurancePercentage >= 50
                ? 'You have good coverage across all major insurance types. Review annually to ensure continued adequate protection.'
                : healthInsuranceCoverage <= 0
                  ? 'Health insurance is a critical gap in your protection plan. Obtaining coverage should be a top priority.'
                  : liabilityInsurancePercentage < 50
                    ? 'Consider adding umbrella liability insurance to protect your assets from potential lawsuits.'
                    : 'Address the gaps in your insurance coverage to create a comprehensive protection plan.'}
            </OtherInsuranceInsight>
          </DashboardCard>
        </DashboardSection>
      </DashboardContainer>

      {/* Priority Actions */}
      <ActionSection theme={theme}>
        <ActionSectionTitle theme={theme}>Priority Actions</ActionSectionTitle>
        <ActionCards>
          {getRecommendations().map((recommendation, index) => (
            <ActionCard
              key={index}
              priority={recommendation.priority}
              theme={theme}
              onClick={() => recommendation.action && handleSectionClick(recommendation.action)}
            >
              <ActionHeader>
                <ActionTitle>{recommendation.title}</ActionTitle>
                <PriorityBadge priority={recommendation.priority} theme={theme}>
                  {recommendation.priority}
                </PriorityBadge>
              </ActionHeader>
              <ActionDescription>{recommendation.description}</ActionDescription>
              <ActionButtonContainer>
                <ActionButton theme={theme}>Take Action</ActionButton>
              </ActionButtonContainer>
            </ActionCard>
          ))}
        </ActionCards>
      </ActionSection>

      {/* Protection Journey */}
      <JourneySection theme={theme}>
        <JourneySectionTitle theme={theme}>Your Protection Journey</JourneySectionTitle>
        <JourneyDescription theme={theme}>
          Complete these sections to build a comprehensive protection plan against life's risks.
        </JourneyDescription>

        <JourneySteps>
          {southSections?.map((section) => (
            <JourneyStep
              key={section.id}
              onClick={() => handleSectionClick(section.id)}
              theme={theme}
              isActive={section.isActive}
            >
              <JourneyStepIcon theme={theme}>{section.icon || '○'}</JourneyStepIcon>
              <JourneyStepContent>
                <JourneyStepTitle theme={theme}>{section.title}</JourneyStepTitle>
                {section.description && (
                  <JourneyStepDescription theme={theme}>
                    {section.description}
                  </JourneyStepDescription>
                )}
              </JourneyStepContent>
              <JourneyStepArrow theme={theme}>→</JourneyStepArrow>
            </JourneyStep>
          ))}
        </JourneySteps>
      </JourneySection>
    </TrackerContainer>
  );
};

// Styled components
const TrackerContainer = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.secondary};
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const TrackerHeader = styled.div<{ theme: any }>`
  margin-bottom: 24px;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 16px;
  position: relative;
`;

const DirectionTitle = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
  font-weight: bold;
`;

const DirectionDescription = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0 0 16px 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const InsightsButton = styled.button<{ theme: any; isActive: boolean }>`
  padding: 8px 16px;
  border-radius: 4px;
  background-color: ${(props) =>
    props.isActive ? props.theme.colors.primary.main : props.theme.colors.background.tertiary};
  color: ${(props) =>
    props.isActive ? props.theme.colors.text.onPrimary : props.theme.colors.text.primary};
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 16px;

  &:hover {
    background-color: ${(props) =>
      props.isActive ? props.theme.colors.primary.dark : props.theme.colors.background.hover};
  }
`;

const ProgressIndicator = styled.div`
  margin-top: 16px;
`;

const ProgressText = styled.div<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin-bottom: 8px;
  font-size: 0.9rem;
`;

const ProgressBar = styled.div<{ theme: any }>`
  height: 8px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-radius: 4px;
  overflow: hidden;
`;

const ProgressFill = styled.div<{ progress: number; theme: any }>`
  height: 100%;
  width: ${(props) => props.progress}%;
  background-color: ${(props) => props.theme.colors.primary.main};
  transition: width 0.3s ease;
`;

// Protection Insights Styled Components
const ProtectionInsights = styled.div<{ theme: any }>`
  margin: 0 0 24px 0;
  padding: 20px;
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
`;

const InsightsTitle = styled.h3<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 20px 0;
  font-size: 1.3rem;
  font-weight: 600;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 12px;
`;

// Protection Score Card
const ProtectionScoreCard = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
`;

const ProtectionScoreHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
`;

const ProtectionScoreTitle = styled.h4`
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
`;

const ProtectionScoreValue = styled.div<{ score: number }>`
  font-size: 1.8rem;
  font-weight: 700;
  color: ${(props) => {
    if (props.score >= 80) return '#10b981'; // success
    if (props.score >= 60) return '#22c55e'; // success light
    if (props.score >= 40) return '#f59e0b'; // warning
    if (props.score >= 20) return '#d97706'; // warning dark
    return '#ef4444'; // error
  }};
`;

const ProtectionScoreStatus = styled.div<{ score: number }>`
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: ${(props) => {
    if (props.score >= 80) return '#10b981'; // success
    if (props.score >= 60) return '#22c55e'; // success light
    if (props.score >= 40) return '#f59e0b'; // warning
    if (props.score >= 20) return '#d97706'; // warning dark
    return '#ef4444'; // error
  }};
`;

const ProtectionScoreBar = styled.div<{ theme: any }>`
  height: 8px;
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 4px;
  overflow: hidden;
`;

const ProtectionScoreFill = styled.div<{ score: number; theme: any }>`
  height: 100%;
  width: ${(props) => props.score}%;
  background-color: ${(props) => {
    if (props.score >= 80) return props.theme.colors.success.main;
    if (props.score >= 60) return props.theme.colors.success.light;
    if (props.score >= 40) return props.theme.colors.warning.main;
    if (props.score >= 20) return props.theme.colors.warning.dark;
    return props.theme.colors.error.main;
  }};
  transition: width 0.5s ease;
`;

// Coverage Overview
const CoverageOverview = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
`;

const CoverageOverviewTitle = styled.h4<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 8px;
`;

const CoverageGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
`;

const CoverageCard = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 6px;
  padding: 16px;
`;

const CoverageType = styled.div`
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 8px;
`;

const CoverageAmount = styled.div`
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 12px;
`;

const CoverageBar = styled.div<{ theme: any }>`
  height: 8px;
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 12px;
`;

const CoverageFill = styled.div<{ percentage: number; status: string; theme: any }>`
  height: 100%;
  width: ${(props) => props.percentage}%;
  background-color: ${(props) =>
    props.status === 'good'
      ? props.theme.colors.success.main
      : props.status === 'moderate'
        ? props.theme.colors.warning.main
        : props.theme.colors.error.main};
  transition: width 0.5s ease;
`;

const CoverageDetails = styled.div`
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  margin-bottom: 12px;
`;

const CoverageLabel = styled.div`
  color: ${(props) => props.theme.colors.text.secondary};
`;

const CoverageValue = styled.div`
  font-weight: 600;
`;

const CoverageStatus = styled.div<{ status: string; theme: any }>`
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  background-color: ${(props) =>
    props.status === 'good'
      ? props.theme.colors.success.light
      : props.status === 'moderate'
        ? props.theme.colors.warning.light
        : props.theme.colors.error.light};
  color: ${(props) =>
    props.status === 'good'
      ? props.theme.colors.success.main
      : props.status === 'moderate'
        ? props.theme.colors.warning.main
        : props.theme.colors.error.main};
`;

// Recommendations Section
const RecommendationsSection = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-radius: 8px;
  padding: 16px;
`;

const RecommendationsTitle = styled.h4<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 8px;
`;

const RecommendationsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const RecommendationItem = styled.div<{ priority: string; theme: any }>`
  padding: 12px;
  border-radius: 6px;
  background-color: ${(props) => props.theme.colors.background.paper};
  border-left: 4px solid
    ${(props) =>
      props.priority === 'High'
        ? props.theme.colors.error.main
        : props.priority === 'Medium'
          ? props.theme.colors.warning.main
          : props.theme.colors.success.main};
  cursor: pointer;
  transition: transform 0.2s ease;

  &:hover {
    transform: translateX(4px);
  }
`;

const RecommendationHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
`;

const RecommendationTitle = styled.h5`
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
`;

const RecommendationDescription = styled.p`
  margin: 0;
  font-size: 0.9rem;
  color: ${(props) => props.theme.colors.text.secondary};
  line-height: 1.4;
`;

const PriorityBadge = styled.span<{ priority: string; theme: any }>`
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
  background-color: ${(props) =>
    props.priority === 'High'
      ? props.theme.colors.error.light
      : props.priority === 'Medium'
        ? props.theme.colors.warning.light
        : props.theme.colors.success.light};
  color: ${(props) =>
    props.priority === 'High'
      ? props.theme.colors.error.dark
      : props.priority === 'Medium'
        ? props.theme.colors.warning.dark
        : props.theme.colors.success.dark};
`;

// Sections List
const SectionsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const SectionItem = styled.div<{ isCompleted: boolean; isActive: boolean; theme: any }>`
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  background-color: ${(props) =>
    props.isActive ? props.theme.colors.background.highlight : props.theme.colors.background.main};
  border-left: 4px solid
    ${(props) =>
      props.isCompleted
        ? props.theme.colors.success.main
        : props.isActive
          ? props.theme.colors.primary.main
          : props.theme.colors.border.main};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.hover};
    transform: translateX(4px);
  }
`;

const SectionIcon = styled.div<{ isCompleted: boolean; theme: any }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: ${(props) =>
    props.isCompleted ? props.theme.colors.success.main : props.theme.colors.background.tertiary};
  color: ${(props) =>
    props.isCompleted ? props.theme.colors.text.onPrimary : props.theme.colors.text.secondary};
  margin-right: 16px;
  font-size: 16px;
  flex-shrink: 0;
`;

const SectionContent = styled.div`
  flex: 1;
`;

const SectionTitle = styled.h3<{ isCompleted: boolean; isActive: boolean; theme: any }>`
  margin: 0 0 4px 0;
  font-size: 1.1rem;
  color: ${(props) =>
    props.isCompleted
      ? props.theme.colors.success.dark
      : props.isActive
        ? props.theme.colors.primary.dark
        : props.theme.colors.text.primary};
`;

const SectionDescription = styled.p<{ theme: any }>`
  margin: 0;
  font-size: 0.9rem;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const CompletionDate = styled.div<{ theme: any }>`
  font-size: 0.8rem;
  color: ${(props) => props.theme.colors.text.tertiary};
  margin-top: 4px;
`;

const SectionStatus = styled.div<{ isCompleted: boolean; theme: any }>`
  font-size: 0.8rem;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: ${(props) =>
    props.isCompleted ? props.theme.colors.success.light : props.theme.colors.background.tertiary};
  color: ${(props) =>
    props.isCompleted ? props.theme.colors.success.dark : props.theme.colors.text.secondary};
  margin-left: 16px;
`;

const CompletionMessage = styled.div<{ theme: any }>`
  margin-top: 32px;
  padding: 24px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.success.light};
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const CompletionIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
`;

const CompletionText = styled.p`
  color: ${(props) => props.theme.colors.text.primary};
  font-size: 1.1rem;
  line-height: 1.5;
  margin: 0 0 24px 0;
`;

const NextDirectionButton = styled.button<{ theme: any }>`
  padding: 12px 24px;
  border-radius: 4px;
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.text.onPrimary};
  border: none;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${(props) => props.theme.colors.primary.light};
  }
`;

// Dashboard Components
const DashboardContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
`;

const DashboardSection = styled.div`
  display: flex;
  flex-direction: column;
`;

const DashboardCard = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  height: 100%;
  display: flex;
  flex-direction: column;
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }
`;

const CardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const CardTitle = styled.h3<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
`;

const CardIcon = styled.div<{ positive: boolean }>`
  font-size: 1.8rem;
  color: ${(props) => (props.positive ? '#10b981' : '#ef4444')};
`;

const MetricHighlight = styled.div<{ positive: boolean }>`
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 8px;
  color: ${(props) => (props.positive ? '#10b981' : '#ef4444')};
`;

const MetricDescription = styled.div`
  font-size: 0.9rem;
  color: ${(props) => props.theme.colors.text.secondary};
  margin-bottom: 16px;
`;

const MetricBreakdown = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
  padding: 12px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-radius: 8px;
`;

const MetricBreakdownItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const MetricBreakdownLabel = styled.div`
  font-size: 0.85rem;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const MetricBreakdownValue = styled.div`
  font-size: 0.9rem;
  font-weight: 600;
  color: ${(props) => props.theme.colors.text.primary};
`;

// Insurance Types List
const InsuranceTypesList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
`;

const InsuranceTypeItem = styled.div<{ status: string }>`
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-radius: 8px;
  border-left: 4px solid
    ${(props) =>
      props.status === 'good' ? props.theme.colors.success.main : props.theme.colors.error.main};
`;

const InsuranceTypeIcon = styled.div<{ status: string }>`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: ${(props) =>
    props.status === 'good' ? props.theme.colors.success.main : props.theme.colors.error.main};
  color: white;
  margin-right: 12px;
  font-size: 0.8rem;
`;

const InsuranceTypeContent = styled.div`
  flex: 1;
`;

const InsuranceTypeName = styled.div`
  font-size: 0.9rem;
  font-weight: 600;
  color: ${(props) => props.theme.colors.text.primary};
  margin-bottom: 4px;
`;

const InsuranceTypeValue = styled.div`
  font-size: 0.85rem;
  color: ${(props) => props.theme.colors.text.secondary};
`;

// Insight Components
const ProtectionInsight = styled.div<{ theme: any }>`
  font-size: 0.9rem;
  line-height: 1.5;
  color: ${(props) => props.theme.colors.text.primary};
  margin-top: auto;
  padding-top: 12px;
  border-top: 1px solid ${(props) => props.theme.colors.border.main};
`;

const CoverageInsight = styled.div<{ theme: any }>`
  font-size: 0.9rem;
  line-height: 1.5;
  color: ${(props) => props.theme.colors.text.primary};
  margin-top: auto;
  padding-top: 12px;
  border-top: 1px solid ${(props) => props.theme.colors.border.main};
`;

const OtherInsuranceInsight = styled.div<{ theme: any }>`
  font-size: 0.9rem;
  line-height: 1.5;
  color: ${(props) => props.theme.colors.text.primary};
  margin-top: auto;
  padding-top: 12px;
  border-top: 1px solid ${(props) => props.theme.colors.border.main};
`;

// Action Section
const ActionSection = styled.div<{ theme: any }>`
  margin-bottom: 32px;
  padding: 20px;
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
`;

const ActionSectionTitle = styled.h3<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 20px 0;
  font-size: 1.3rem;
  font-weight: 600;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 12px;
`;

const ActionCards = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
`;

const ActionCard = styled.div<{ priority: string; theme: any }>`
  padding: 16px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-left: 4px solid
    ${(props) =>
      props.priority === 'High'
        ? props.theme.colors.error.main
        : props.priority === 'Medium'
          ? props.theme.colors.warning.main
          : props.theme.colors.success.main};
  cursor: pointer;
  transition: transform 0.2s ease;
  display: flex;
  flex-direction: column;
  height: 100%;

  &:hover {
    transform: translateY(-4px);
  }
`;

const ActionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const ActionTitle = styled.h4`
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
`;

const ActionDescription = styled.p`
  margin: 0;
  font-size: 0.9rem;
  color: ${(props) => props.theme.colors.text.secondary};
  line-height: 1.5;
  flex: 1;
`;

const ActionButtonContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  align-items: center;
`;

const ActionButton = styled.div<{ theme: any }>`
  display: inline-block;
  padding: 8px 16px;
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.text.onPrimary};
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

// Journey Section
const JourneySection = styled.div<{ theme: any }>`
  padding: 20px;
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
`;

const JourneySectionTitle = styled.h3<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.3rem;
  font-weight: 600;
`;

const JourneyDescription = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0 0 20px 0;
  font-size: 0.9rem;
  line-height: 1.5;
`;

const JourneySteps = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const JourneyStep = styled.div<{ theme: any; isActive: boolean }>`
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  background-color: ${(props) =>
    props.isActive
      ? props.theme.colors.background.highlight
      : props.theme.colors.background.tertiary};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.hover};
    transform: translateX(8px);
  }
`;

const JourneyStepIcon = styled.div<{ theme: any }>`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.text.onPrimary};
  margin-right: 16px;
  font-size: 1.2rem;
`;

const JourneyStepContent = styled.div`
  flex: 1;
`;

const JourneyStepTitle = styled.h4<{ theme: any }>`
  margin: 0 0 4px 0;
  font-size: 1.1rem;
  color: ${(props) => props.theme.colors.text.primary};
`;

const JourneyStepDescription = styled.p<{ theme: any }>`
  margin: 0;
  font-size: 0.85rem;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const JourneyStepArrow = styled.div<{ theme: any }>`
  font-size: 1.5rem;
  color: ${(props) => props.theme.colors.text.secondary};
  margin-left: 16px;
`;

export default SouthDirectionTracker;
