/**
 * Insurance Needs Calculator Component
 *
 * This component helps users calculate their insurance needs based on
 * their financial situation and risk factors.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { formatCurrency } from '../../../../utils/formatters';
import {
  calculateLifeInsuranceNeeds,
  calculateDisabilityInsuranceNeeds,
  calculateLongTermCareInsuranceNeeds,
} from '../../../../utils/insuranceCalculator';
import { CalculatorData } from '../../../../types/southDirection';

interface InsuranceNeedsCalculatorProps {
  onComplete?: () => void;
  onBack?: () => void;
}

interface CalculationResults {
  lifeInsurance: number;
  disabilityInsurance: number;
  longTermCareInsurance: number;
}

const InsuranceNeedsCalculator: React.FC<InsuranceNeedsCalculatorProps> = ({
  onComplete,
  onBack,
}) => {
  const { theme } = useTheme();
  const { data, updateData } = useFinancialCompass();

  const [isSaving, setIsSaving] = useState(false);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);
  const [activeTab, setActiveTab] = useState<'life' | 'disability' | 'longterm'>('life');

  // Get income and expense details safely
  const incomeDetails = data.north?.incomeDetails || {};
  const expenseDetails = data.north?.expenseDetails || {};

  // Safely access totalAnnualIncome
  const totalAnnualIncome =
    typeof incomeDetails === 'object' &&
    'totalAnnualIncome' in incomeDetails &&
    incomeDetails.totalAnnualIncome
      ? (incomeDetails.totalAnnualIncome as string)
      : '0';

  // Safely access totalMonthlyExpenses
  const totalMonthlyExpenses =
    typeof expenseDetails === 'object' &&
    'totalMonthlyExpenses' in expenseDetails &&
    expenseDetails.totalMonthlyExpenses
      ? (expenseDetails.totalMonthlyExpenses as string)
      : '0';

  // Calculate monthly income from annual income
  const monthlyIncome =
    totalAnnualIncome !== '0' ? (parseFloat(totalAnnualIncome) / 12).toString() : '0';

  // Default calculator data
  const defaultCalculatorData: CalculatorData = {
    // Life Insurance
    annualIncome: totalAnnualIncome,
    yearsToReplace: '10',
    outstandingDebt: '0',
    educationFunds: '0',
    finalExpenses: '15000',
    existingLifeInsurance: '0',
    liquidAssets: '0',

    // Disability Insurance
    monthlyIncome: monthlyIncome,
    monthlyExpenses: totalMonthlyExpenses,
    existingDisabilityInsurance: '0',
    otherIncomeSources: '0',

    // Long-Term Care Insurance
    monthlyCareExpense: '5000',
    yearsOfCare: '3',
    existingLTCInsurance: '0',
    monthlyRetirementIncome: '0',
    availableAssets: '0',
  };

  // Initialize form data from context or with defaults
  const [formData, setFormData] = useState<CalculatorData>(
    data.south?.insuranceCalculator
      ? {
          ...defaultCalculatorData,
          ...data.south.insuranceCalculator,
        }
      : defaultCalculatorData
  );

  // Calculate results
  const [results, setResults] = useState<CalculationResults>({
    lifeInsurance: 0,
    disabilityInsurance: 0,
    longTermCareInsurance: 0,
  });

  // Calculate insurance needs when form data changes
  useEffect(() => {
    calculateResults();
  }, [formData]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    // Only allow numbers and decimal points
    if (value === '' || /^[0-9]*\.?[0-9]*$/.test(value)) {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  // Calculate all insurance needs
  const calculateResults = () => {
    // Calculate life insurance needs
    const lifeInsurance = calculateLifeInsuranceNeeds(
      parseFloat(formData.annualIncome) || 0,
      parseFloat(formData.yearsToReplace) || 0,
      parseFloat(formData.outstandingDebt) || 0,
      parseFloat(formData.educationFunds) || 0,
      parseFloat(formData.finalExpenses) || 0,
      parseFloat(formData.existingLifeInsurance) || 0,
      parseFloat(formData.liquidAssets) || 0
    );

    // Calculate disability insurance needs
    const disabilityInsurance = calculateDisabilityInsuranceNeeds(
      parseFloat(formData.monthlyIncome) || 0,
      parseFloat(formData.monthlyExpenses) || 0,
      parseFloat(formData.existingDisabilityInsurance) || 0,
      parseFloat(formData.otherIncomeSources) || 0
    );

    // Calculate long-term care insurance needs
    const longTermCareInsurance = calculateLongTermCareInsuranceNeeds(
      parseFloat(formData.monthlyCareExpense) || 0,
      parseFloat(formData.yearsOfCare) || 0,
      parseFloat(formData.existingLTCInsurance) || 0,
      parseFloat(formData.monthlyRetirementIncome) || 0,
      parseFloat(formData.availableAssets) || 0
    );

    setResults({
      lifeInsurance,
      disabilityInsurance,
      longTermCareInsurance,
    });
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSave();

    if (onComplete) {
      onComplete();
    }
  };

  // Handle saving data
  const handleSave = () => {
    setIsSaving(true);

    // Update data in context
    updateData('south', 'insuranceCalculator', formData);
    updateData('south', 'insuranceResults', results);

    // Show save indicator
    setShowSaveIndicator(true);

    // Hide save indicator after 2 seconds
    setTimeout(() => {
      setIsSaving(false);
      setShowSaveIndicator(false);
    }, 2000);
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Insurance Needs Calculator</Title>
        <Description theme={theme}>
          Calculate how much insurance coverage you need based on your financial situation.
        </Description>
      </Header>

      <form onSubmit={handleSubmit}>
        <TabContainer theme={theme}>
          <Tab isActive={activeTab === 'life'} onClick={() => setActiveTab('life')} theme={theme}>
            Life Insurance
          </Tab>
          <Tab
            isActive={activeTab === 'disability'}
            onClick={() => setActiveTab('disability')}
            theme={theme}
          >
            Disability Insurance
          </Tab>
          <Tab
            isActive={activeTab === 'longterm'}
            onClick={() => setActiveTab('longterm')}
            theme={theme}
          >
            Long-Term Care
          </Tab>
        </TabContainer>

        <TabContent theme={theme}>
          {activeTab === 'life' && (
            <LifeInsuranceCalculator
              formData={formData}
              handleInputChange={handleInputChange}
              result={results.lifeInsurance}
              theme={theme}
            />
          )}

          {activeTab === 'disability' && (
            <DisabilityInsuranceCalculator
              formData={formData}
              handleInputChange={handleInputChange}
              result={results.disabilityInsurance}
              theme={theme}
            />
          )}

          {activeTab === 'longterm' && (
            <LongTermCareCalculator
              formData={formData}
              handleInputChange={handleInputChange}
              result={results.longTermCareInsurance}
              theme={theme}
            />
          )}
        </TabContent>

        <ButtonContainer>
          {onBack && (
            <BackButton type="button" onClick={onBack} theme={theme}>
              Back
            </BackButton>
          )}

          <SaveIndicator visible={showSaveIndicator}>✓ Saved</SaveIndicator>

          <SubmitButton type="submit" theme={theme}>
            {isSaving ? 'Saving...' : 'Save & Continue'}
          </SubmitButton>
        </ButtonContainer>
      </form>
    </Container>
  );
};

// Life Insurance Calculator Component
const LifeInsuranceCalculator: React.FC<{
  formData: CalculatorData;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  result: number;
  theme: any;
}> = ({ formData, handleInputChange, result, theme }) => {
  return (
    <div>
      <SectionTitle>Life Insurance Needs</SectionTitle>

      <FormRow>
        <FormField>
          <Label htmlFor="annualIncome">Annual Income</Label>
          <InputWithPrefix>
            <Prefix>$</Prefix>
            <Input
              type="text"
              id="annualIncome"
              name="annualIncome"
              value={formData.annualIncome}
              onChange={handleInputChange}
              placeholder="0"
            />
          </InputWithPrefix>
        </FormField>

        <FormField>
          <Label htmlFor="yearsToReplace">Years to Replace</Label>
          <Input
            type="text"
            id="yearsToReplace"
            name="yearsToReplace"
            value={formData.yearsToReplace}
            onChange={handleInputChange}
            placeholder="10"
          />
        </FormField>
      </FormRow>

      <FormRow>
        <FormField>
          <Label htmlFor="outstandingDebt">Outstanding Debt</Label>
          <InputWithPrefix>
            <Prefix>$</Prefix>
            <Input
              type="text"
              id="outstandingDebt"
              name="outstandingDebt"
              value={formData.outstandingDebt}
              onChange={handleInputChange}
              placeholder="0"
            />
          </InputWithPrefix>
        </FormField>

        <FormField>
          <Label htmlFor="educationFunds">Education Funds</Label>
          <InputWithPrefix>
            <Prefix>$</Prefix>
            <Input
              type="text"
              id="educationFunds"
              name="educationFunds"
              value={formData.educationFunds}
              onChange={handleInputChange}
              placeholder="0"
            />
          </InputWithPrefix>
        </FormField>
      </FormRow>

      <FormRow>
        <FormField>
          <Label htmlFor="finalExpenses">Final Expenses</Label>
          <InputWithPrefix>
            <Prefix>$</Prefix>
            <Input
              type="text"
              id="finalExpenses"
              name="finalExpenses"
              value={formData.finalExpenses}
              onChange={handleInputChange}
              placeholder="15000"
            />
          </InputWithPrefix>
        </FormField>
      </FormRow>

      <Divider theme={theme} />

      <FormRow>
        <FormField>
          <Label htmlFor="existingLifeInsurance">Existing Life Insurance</Label>
          <InputWithPrefix>
            <Prefix>$</Prefix>
            <Input
              type="text"
              id="existingLifeInsurance"
              name="existingLifeInsurance"
              value={formData.existingLifeInsurance}
              onChange={handleInputChange}
              placeholder="0"
            />
          </InputWithPrefix>
        </FormField>

        <FormField>
          <Label htmlFor="liquidAssets">Liquid Assets</Label>
          <InputWithPrefix>
            <Prefix>$</Prefix>
            <Input
              type="text"
              id="liquidAssets"
              name="liquidAssets"
              value={formData.liquidAssets}
              onChange={handleInputChange}
              placeholder="0"
            />
          </InputWithPrefix>
        </FormField>
      </FormRow>

      <ResultContainer theme={theme}>
        <ResultLabel>Recommended Life Insurance Coverage:</ResultLabel>
        <ResultValue>{formatCurrency(result.toString())}</ResultValue>
      </ResultContainer>
    </div>
  );
};

// Disability Insurance Calculator Component
const DisabilityInsuranceCalculator: React.FC<{
  formData: CalculatorData;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  result: number;
  theme: any;
}> = ({ formData, handleInputChange, result, theme }) => {
  return (
    <div>
      <SectionTitle>Disability Insurance Needs</SectionTitle>

      <FormRow>
        <FormField>
          <Label htmlFor="monthlyIncome">Monthly Income</Label>
          <InputWithPrefix>
            <Prefix>$</Prefix>
            <Input
              type="text"
              id="monthlyIncome"
              name="monthlyIncome"
              value={formData.monthlyIncome}
              onChange={handleInputChange}
              placeholder="0"
            />
          </InputWithPrefix>
        </FormField>

        <FormField>
          <Label htmlFor="monthlyExpenses">Monthly Expenses</Label>
          <InputWithPrefix>
            <Prefix>$</Prefix>
            <Input
              type="text"
              id="monthlyExpenses"
              name="monthlyExpenses"
              value={formData.monthlyExpenses}
              onChange={handleInputChange}
              placeholder="0"
            />
          </InputWithPrefix>
        </FormField>
      </FormRow>

      <Divider theme={theme} />

      <FormRow>
        <FormField>
          <Label htmlFor="existingDisabilityInsurance">Existing Disability Insurance</Label>
          <InputWithPrefix>
            <Prefix>$</Prefix>
            <Input
              type="text"
              id="existingDisabilityInsurance"
              name="existingDisabilityInsurance"
              value={formData.existingDisabilityInsurance}
              onChange={handleInputChange}
              placeholder="0"
            />
          </InputWithPrefix>
          <FieldHint>Monthly benefit amount</FieldHint>
        </FormField>

        <FormField>
          <Label htmlFor="otherIncomeSources">Other Income Sources</Label>
          <InputWithPrefix>
            <Prefix>$</Prefix>
            <Input
              type="text"
              id="otherIncomeSources"
              name="otherIncomeSources"
              value={formData.otherIncomeSources}
              onChange={handleInputChange}
              placeholder="0"
            />
          </InputWithPrefix>
          <FieldHint>Monthly amount during disability</FieldHint>
        </FormField>
      </FormRow>

      <ResultContainer theme={theme}>
        <ResultLabel>Recommended Monthly Disability Benefit:</ResultLabel>
        <ResultValue>{formatCurrency(result.toString())}/month</ResultValue>
      </ResultContainer>
    </div>
  );
};

// Long-Term Care Calculator Component
const LongTermCareCalculator: React.FC<{
  formData: CalculatorData;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  result: number;
  theme: any;
}> = ({ formData, handleInputChange, result, theme }) => {
  return (
    <div>
      <SectionTitle>Long-Term Care Insurance Needs</SectionTitle>

      <FormRow>
        <FormField>
          <Label htmlFor="monthlyCareExpense">Monthly Care Expense</Label>
          <InputWithPrefix>
            <Prefix>$</Prefix>
            <Input
              type="text"
              id="monthlyCareExpense"
              name="monthlyCareExpense"
              value={formData.monthlyCareExpense}
              onChange={handleInputChange}
              placeholder="5000"
            />
          </InputWithPrefix>
          <FieldHint>Estimated monthly long-term care cost</FieldHint>
        </FormField>

        <FormField>
          <Label htmlFor="yearsOfCare">Years of Care</Label>
          <Input
            type="text"
            id="yearsOfCare"
            name="yearsOfCare"
            value={formData.yearsOfCare}
            onChange={handleInputChange}
            placeholder="3"
          />
          <FieldHint>Estimated duration of care needed</FieldHint>
        </FormField>
      </FormRow>

      <Divider theme={theme} />

      <FormRow>
        <FormField>
          <Label htmlFor="existingLTCInsurance">Existing LTC Insurance</Label>
          <InputWithPrefix>
            <Prefix>$</Prefix>
            <Input
              type="text"
              id="existingLTCInsurance"
              name="existingLTCInsurance"
              value={formData.existingLTCInsurance}
              onChange={handleInputChange}
              placeholder="0"
            />
          </InputWithPrefix>
          <FieldHint>Monthly benefit amount</FieldHint>
        </FormField>

        <FormField>
          <Label htmlFor="monthlyRetirementIncome">Monthly Retirement Income</Label>
          <InputWithPrefix>
            <Prefix>$</Prefix>
            <Input
              type="text"
              id="monthlyRetirementIncome"
              name="monthlyRetirementIncome"
              value={formData.monthlyRetirementIncome}
              onChange={handleInputChange}
              placeholder="0"
            />
          </InputWithPrefix>
        </FormField>
      </FormRow>

      <FormRow>
        <FormField>
          <Label htmlFor="availableAssets">Available Assets</Label>
          <InputWithPrefix>
            <Prefix>$</Prefix>
            <Input
              type="text"
              id="availableAssets"
              name="availableAssets"
              value={formData.availableAssets}
              onChange={handleInputChange}
              placeholder="0"
            />
          </InputWithPrefix>
          <FieldHint>Assets available for long-term care</FieldHint>
        </FormField>
      </FormRow>

      <ResultContainer theme={theme}>
        <ResultLabel>Recommended Monthly LTC Benefit:</ResultLabel>
        <ResultValue>{formatCurrency(result.toString())}/month</ResultValue>
      </ResultContainer>
    </div>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  padding: 24px;
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const Title = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const Description = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const TabContainer = styled.div<{ theme: any }>`
  display: flex;
  border-bottom: 1px solid ${(props) => props.theme.colors.border};
  margin-bottom: 24px;
`;

const Tab = styled.div<{ isActive: boolean; theme: any }>`
  padding: 12px 24px;
  cursor: pointer;
  font-weight: ${(props) => (props.isActive ? 'bold' : 'normal')};
  color: ${(props) =>
    props.isActive ? props.theme.colors.primary.main : props.theme.colors.text.secondary};
  border-bottom: 2px solid
    ${(props) => (props.isActive ? props.theme.colors.primary.main : 'transparent')};
  transition: all 0.2s;

  &:hover {
    color: ${(props) => props.theme.colors.primary.main};
  }
`;

const TabContent = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;

const FormField = styled.div`
  margin-bottom: 16px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
`;

const InputWithPrefix = styled.div`
  display: flex;
  align-items: center;
  position: relative;
`;

const Prefix = styled.span`
  position: absolute;
  left: 10px;
  color: #666;
`;

const Input = styled.input`
  width: 100%;
  padding: 10px;
  padding-left: 24px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
`;

const FieldHint = styled.div`
  font-size: 0.8rem;
  color: #666;
  margin-top: 4px;
`;

const Divider = styled.div<{ theme: any }>`
  height: 1px;
  background-color: ${(props) => props.theme.colors.border};
  margin: 16px 0;
`;

const ResultContainer = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.light};
  padding: 16px;
  border-radius: 8px;
  margin-top: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const ResultLabel = styled.div`
  font-weight: 500;
`;

const ResultValue = styled.div`
  font-size: 1.2rem;
  font-weight: bold;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
`;

const BackButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.text.secondary};
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const SubmitButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 24px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  color: green;
  margin-right: 16px;
`;

export default InsuranceNeedsCalculator;
