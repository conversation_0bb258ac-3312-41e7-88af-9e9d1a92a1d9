/**
 * South Direction Summary Section
 *
 * This component displays a summary of the South Direction journey,
 * showing the user's protection and risk management plan with trends, alerts, actions, and guidance.
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { formatCurrency } from '../../../../utils/formatters';
import {
  TrendsSection,
  AlertsSection,
  ActionsSection,
  GuidanceSection,
  TrendItem,
  AlertItem,
  GuidanceItem,
} from '../shared/SummaryComponents';
import type { ActionItem } from '../shared/SummaryComponents';
import { downloadComprehensivePDF } from '../../../../utils/enhancedPdfExport';

interface SummarySectionProps {
  onComplete?: () => void;
  onBack?: () => void;
}

const SummarySection: React.FC<SummarySectionProps> = ({ onComplete, onBack }) => {
  const { theme } = useTheme();
  const { data } = useFinancialCompass();
  const [isExporting, setIsExporting] = useState(false);

  // Get personal info for PDF export
  const personalInfo = data.north?.personalInformation || {};

  // Extract relevant data from the context
  const insuranceCoverage = data.south?.insuranceCoverage || {};
  const healthcarePlanning = data.south?.healthcarePlanning || {};
  const riskTolerance = data.south?.riskTolerance || {};
  const protectionGap = data.south?.protectionGap || {};
  const insuranceCalculator = data.south?.insuranceCalculator || {};

  // Create default objects for missing properties
  const insuranceResults = {
    lifeInsurance: 0,
    disabilityInsurance: 0,
    longTermCareInsurance: 0,
  };

  // Use healthcareCostProjections instead of healthcareProjections
  const healthcareProjections = data.south?.healthcareCostProjections || {};

  // Create default object for healthcareProjectionResults
  const healthcareProjectionResults = {
    totalLifetimeCost: 0,
  };

  // Calculate total insurance coverage - safely access nested properties
  const lifeInsuranceCoverage = insuranceCoverage?.lifeInsurance || 0;
  const disabilityInsuranceCoverage = insuranceCoverage?.disabilityInsurance || 0;
  const healthInsuranceCoverage = insuranceCoverage?.healthInsurance?.coverage || 0;
  const homeInsuranceCoverage = insuranceCoverage?.homeownersInsurance?.coverage || 0;
  const autoInsuranceCoverage = insuranceCoverage?.autoInsurance?.coverage || 0;

  const totalInsuranceCoverage =
    lifeInsuranceCoverage +
    disabilityInsuranceCoverage +
    healthInsuranceCoverage +
    homeInsuranceCoverage +
    autoInsuranceCoverage;

  // Calculate protection gap
  const protectionGapAmount = protectionGap?.totalGap || 0;

  // Get risk tolerance score
  const riskToleranceScore = typeof riskTolerance?.score === 'number' ? riskTolerance.score : 0;

  // Get recommended insurance amounts
  const recommendedLifeInsurance = insuranceResults?.lifeInsurance || 0;
  const recommendedDisabilityInsurance = insuranceResults?.disabilityInsurance || 0;
  const recommendedLongTermCareInsurance = insuranceResults?.longTermCareInsurance || 0;

  // Get healthcare projections
  const totalLifetimeHealthcareCost = healthcareProjectionResults?.totalLifetimeCost || 0;

  // Calculate protection score (0-100)
  const calculateProtectionScore = () => {
    let score = 0;

    // Life insurance coverage (0-25 points)
    if (lifeInsuranceCoverage >= recommendedLifeInsurance) {
      score += 25;
    } else if (lifeInsuranceCoverage >= recommendedLifeInsurance * 0.75) {
      score += 20;
    } else if (lifeInsuranceCoverage >= recommendedLifeInsurance * 0.5) {
      score += 15;
    } else if (lifeInsuranceCoverage >= recommendedLifeInsurance * 0.25) {
      score += 10;
    } else if (lifeInsuranceCoverage > 0) {
      score += 5;
    }

    // Disability insurance (0-25 points)
    if (disabilityInsuranceCoverage >= recommendedDisabilityInsurance) {
      score += 25;
    } else if (disabilityInsuranceCoverage >= recommendedDisabilityInsurance * 0.75) {
      score += 20;
    } else if (disabilityInsuranceCoverage >= recommendedDisabilityInsurance * 0.5) {
      score += 15;
    } else if (disabilityInsuranceCoverage >= recommendedDisabilityInsurance * 0.25) {
      score += 10;
    } else if (disabilityInsuranceCoverage > 0) {
      score += 5;
    }

    // Healthcare planning (0-25 points)
    let healthcareScore = 0;
    if (typeof healthcarePlanning === 'object') {
      if ('hasHealthInsurance' in healthcarePlanning && healthcarePlanning.hasHealthInsurance) {
        healthcareScore += 10;
      }
      if (
        'hasLongTermCareInsurance' in healthcarePlanning &&
        healthcarePlanning.hasLongTermCareInsurance
      ) {
        healthcareScore += 10;
      }
      if (
        'hasMedicareSupplemental' in healthcarePlanning &&
        healthcarePlanning.hasMedicareSupplemental
      ) {
        healthcareScore += 5;
      }
    }
    score += healthcareScore;

    // Risk assessment (0-25 points)
    if (
      typeof riskTolerance === 'object' &&
      'assessmentCompleted' in riskTolerance &&
      riskTolerance.assessmentCompleted
    ) {
      score += 25;
    }

    return score;
  };

  const protectionScore = calculateProtectionScore();

  // Get protection status
  const getProtectionStatus = () => {
    if (protectionScore >= 80) return 'Excellent';
    if (protectionScore >= 60) return 'Good';
    if (protectionScore >= 40) return 'Fair';
    if (protectionScore >= 20) return 'Needs Attention';
    return 'Critical';
  };

  // Generate trends
  const generateTrends = (): TrendItem[] => {
    return [
      {
        id: 'protection_gap',
        label: 'Protection Gap',
        value: formatCurrency(protectionGapAmount),
        change: 5, // This would ideally be calculated from historical data
        direction: 'down',
        isPositive: true,
      },
      {
        id: 'life_insurance',
        label: 'Life Insurance Coverage',
        value: formatCurrency(lifeInsuranceCoverage),
        change: 10, // This would ideally be calculated from historical data
        direction: 'up',
        isPositive: true,
      },
      {
        id: 'risk_tolerance',
        label: 'Risk Tolerance Score',
        value: `${riskToleranceScore}/100`,
        change: 0, // This would ideally be calculated from historical data
        direction: 'neutral',
        isPositive: true,
      },
      {
        id: 'healthcare_costs',
        label: 'Projected Lifetime Healthcare Costs',
        value: formatCurrency(totalLifetimeHealthcareCost),
        change: 3, // This would ideally be calculated from historical data
        direction: 'up',
        isPositive: false,
      },
    ];
  };

  // Generate alerts
  const generateAlerts = (): AlertItem[] => {
    const alerts: AlertItem[] = [];

    if (protectionGapAmount > 0) {
      const severityLevel =
        protectionGapAmount > 500000
          ? 'critical'
          : protectionGapAmount > 250000
            ? 'high'
            : protectionGapAmount > 100000
              ? 'medium'
              : 'low';

      alerts.push({
        id: 'protection_gap',
        title: 'Insurance Protection Gap',
        description: `You have an insurance protection gap of ${formatCurrency(protectionGapAmount)}. This means your loved ones may not be adequately protected in case of an unexpected event.`,
        severity: severityLevel,
      });
    }

    if (lifeInsuranceCoverage < recommendedLifeInsurance) {
      const coveragePercentage =
        recommendedLifeInsurance > 0 ? (lifeInsuranceCoverage / recommendedLifeInsurance) * 100 : 0;

      const severityLevel =
        coveragePercentage < 25
          ? 'critical'
          : coveragePercentage < 50
            ? 'high'
            : coveragePercentage < 75
              ? 'medium'
              : 'low';

      alerts.push({
        id: 'life_insurance',
        title: 'Insufficient Life Insurance',
        description: `Your life insurance coverage is only ${Math.round(coveragePercentage)}% of the recommended amount. Consider increasing your coverage to protect your dependents.`,
        severity: severityLevel,
      });
    }

    if (
      !(
        typeof healthcarePlanning === 'object' &&
        'hasLongTermCareInsurance' in healthcarePlanning &&
        healthcarePlanning.hasLongTermCareInsurance
      )
    ) {
      alerts.push({
        id: 'long_term_care',
        title: 'Missing Long-Term Care Insurance',
        description:
          'You do not have long-term care insurance, which could leave you vulnerable to high costs for extended care in the future.',
        severity: 'medium',
      });
    }

    return alerts;
  };

  // Generate actions
  const generateActions = (): ActionItem[] => {
    const actions: ActionItem[] = [];

    if (protectionGapAmount > 0) {
      actions.push({
        id: 'address_protection_gap',
        title: 'Address Protection Gap',
        description: `Consider increasing your insurance coverage by ${formatCurrency(protectionGapAmount)} to fully protect your loved ones.`,
        priority: protectionGapAmount > 500000 ? 'high' : 'medium',
        icon: '🛡️',
      });
    }

    if (lifeInsuranceCoverage < recommendedLifeInsurance) {
      actions.push({
        id: 'increase_life_insurance',
        title: 'Increase Life Insurance Coverage',
        description: `Consider increasing your life insurance coverage by ${formatCurrency(recommendedLifeInsurance - lifeInsuranceCoverage)} to reach the recommended amount.`,
        priority: 'high',
        icon: '💼',
      });
    }

    if (
      !(
        typeof healthcarePlanning === 'object' &&
        'hasLongTermCareInsurance' in healthcarePlanning &&
        healthcarePlanning.hasLongTermCareInsurance
      )
    ) {
      actions.push({
        id: 'get_long_term_care',
        title: 'Obtain Long-Term Care Insurance',
        description:
          'Research and obtain long-term care insurance to protect against potentially high costs of extended care in the future.',
        priority: 'medium',
        icon: '🏥',
      });
    }

    if (
      !(
        typeof riskTolerance === 'object' &&
        'assessmentCompleted' in riskTolerance &&
        riskTolerance.assessmentCompleted
      )
    ) {
      actions.push({
        id: 'complete_risk_assessment',
        title: 'Complete Risk Assessment',
        description:
          'Complete the risk tolerance assessment to better understand your risk profile and make more informed insurance decisions.',
        priority: 'medium',
        icon: '📊',
      });
    }

    return actions;
  };

  // Generate guidance
  const generateGuidance = (): GuidanceItem[] => {
    return [
      {
        id: 'insurance_strategy',
        title: 'Insurance Strategy',
        description:
          'Your insurance strategy should focus on protecting against catastrophic risks first, then address more manageable risks. Prioritize life, disability, and health insurance before other types.',
        icon: '🔍',
      },
      {
        id: 'healthcare_planning',
        title: 'Healthcare Planning',
        description:
          'Healthcare costs typically increase with age. Consider setting aside additional savings specifically for healthcare expenses in retirement.',
        icon: '🏥',
      },
      {
        id: 'risk_management',
        title: 'Risk Management Approach',
        description: `With a risk tolerance score of ${riskToleranceScore}/100, you should focus on a ${riskToleranceScore > 70 ? 'balanced' : riskToleranceScore > 40 ? 'conservative' : 'very conservative'} approach to risk management.`,
        icon: '⚖️',
      },
      {
        id: 'insurance_review',
        title: 'Regular Insurance Review',
        description:
          'Review your insurance coverage annually or after major life events such as marriage, birth of a child, or purchase of a home to ensure adequate protection.',
        icon: '📅',
      },
    ];
  };

  const trends = generateTrends();
  const alerts = generateAlerts();
  const actions = generateActions();
  const guidanceItems = generateGuidance();

  // Handle PDF export
  const handleExportPDF = async () => {
    setIsExporting(true);

    try {
      const userName =
        `${(personalInfo as any)?.firstName || ''} ${(personalInfo as any)?.lastName || ''}`.trim() ||
        'User';
      await downloadComprehensivePDF(data, userName);
    } catch (error) {
      console.error('Error exporting PDF:', error);
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Container theme={theme} data-testid="summary-section">
      <Header theme={theme}>
        <Title theme={theme}>South Direction Summary</Title>
        <Description theme={theme}>Review your protection and risk management plan.</Description>
      </Header>

      <ProtectionScoreCard theme={theme}>
        <ProtectionScoreHeader>
          <ProtectionScoreTitle>Protection Score</ProtectionScoreTitle>
          <ProtectionScoreValue score={protectionScore}>{protectionScore}</ProtectionScoreValue>
        </ProtectionScoreHeader>
        <ProtectionScoreStatus score={protectionScore}>
          {getProtectionStatus()}
        </ProtectionScoreStatus>
        <ProtectionScoreBar theme={theme}>
          <ProtectionScoreFill score={protectionScore} theme={theme} />
        </ProtectionScoreBar>
      </ProtectionScoreCard>

      <SummaryCard theme={theme}>
        <SummaryTitle>Insurance Coverage</SummaryTitle>
        <SummaryContent>
          <SummaryItem>
            <SummaryLabel>Total Insurance Coverage:</SummaryLabel>
            <SummaryValue>{formatCurrency(totalInsuranceCoverage)}</SummaryValue>
          </SummaryItem>
          <SummaryItem>
            <SummaryLabel>Protection Gap:</SummaryLabel>
            <SummaryValue highlight={protectionGapAmount > 0}>
              {formatCurrency(protectionGapAmount)}
            </SummaryValue>
          </SummaryItem>
          <SummaryItem>
            <SummaryLabel>Risk Tolerance Score:</SummaryLabel>
            <SummaryValue>{riskToleranceScore} / 100</SummaryValue>
          </SummaryItem>
        </SummaryContent>
      </SummaryCard>

      <SummaryCard theme={theme}>
        <SummaryTitle>Insurance Recommendations</SummaryTitle>
        <SummaryContent>
          <SummaryItem>
            <SummaryLabel>Recommended Life Insurance:</SummaryLabel>
            <SummaryValue highlight={lifeInsuranceCoverage < recommendedLifeInsurance}>
              {formatCurrency(recommendedLifeInsurance)}
            </SummaryValue>
          </SummaryItem>
          <SummaryItem>
            <SummaryLabel>Recommended Disability Insurance:</SummaryLabel>
            <SummaryValue highlight={disabilityInsuranceCoverage < recommendedDisabilityInsurance}>
              {formatCurrency(recommendedDisabilityInsurance)}/month
            </SummaryValue>
          </SummaryItem>
          <SummaryItem>
            <SummaryLabel>Recommended Long-Term Care Insurance:</SummaryLabel>
            <SummaryValue>{formatCurrency(recommendedLongTermCareInsurance)}/month</SummaryValue>
          </SummaryItem>
        </SummaryContent>
      </SummaryCard>

      <TrendsSection title="Protection Trends" trends={trends} theme={theme} />
      <AlertsSection alerts={alerts} theme={theme} />
      <ActionsSection actions={actions} theme={theme} />
      <GuidanceSection guidanceItems={guidanceItems} theme={theme} />

      <ButtonGroup>
        <ExportButton onClick={handleExportPDF} disabled={isExporting} theme={theme}>
          {isExporting ? 'Generating PDF...' : 'Export Protection Plan Report'}
        </ExportButton>
      </ButtonGroup>

      <ButtonContainer>
        {onBack && (
          <BackButton type="button" onClick={onBack} theme={theme}>
            Back
          </BackButton>
        )}

        <CompleteButton type="button" onClick={onComplete} theme={theme}>
          Complete South Direction
        </CompleteButton>
      </ButtonContainer>
    </Container>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  padding: 24px;
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const Title = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const Description = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const SummaryCard = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const SummaryTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const SummaryContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const SummaryItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
`;

const SummaryLabel = styled.div`
  font-weight: 500;
`;

const SummaryValue = styled.div<{ highlight?: boolean }>`
  font-weight: 600;
  color: ${(props) => (props.highlight ? '#F44336' : '#2196F3')};
`;

const ActionItems = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const ActionTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const ActionList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const ActionItem = styled.div`
  display: flex;
  gap: 16px;
`;

const ActionIcon = styled.div`
  font-size: 1.5rem;
  min-width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const ActionContent = styled.div`
  flex: 1;
`;

const ActionName = styled.h4`
  margin: 0 0 4px 0;
  font-size: 1.1rem;
  font-weight: 500;
`;

const ActionDescription = styled.p`
  margin: 0;
  font-size: 0.9rem;
  color: #666;
  line-height: 1.5;
`;

const ProtectionScoreCard = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  text-align: center;
`;

const ProtectionScoreHeader = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
`;

const ProtectionScoreTitle = styled.h3`
  margin: 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const ProtectionScoreValue = styled.div<{ score: number }>`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.5rem;
  font-weight: 700;
  background-color: ${(props) => {
    if (props.score >= 80) return '#4CAF50';
    if (props.score >= 60) return '#8BC34A';
    if (props.score >= 40) return '#FFC107';
    if (props.score >= 20) return '#FF9800';
    return '#F44336';
  }};
  color: white;
`;

const ProtectionScoreStatus = styled.div<{ score: number }>`
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: ${(props) => {
    if (props.score >= 80) return '#4CAF50';
    if (props.score >= 60) return '#8BC34A';
    if (props.score >= 40) return '#FFC107';
    if (props.score >= 20) return '#FF9800';
    return '#F44336';
  }};
`;

const ProtectionScoreBar = styled.div<{ theme: any }>`
  height: 8px;
  background-color: ${(props) => props.theme.colors.background.main};
  border-radius: 4px;
  overflow: hidden;
`;

const ProtectionScoreFill = styled.div<{ score: number; theme: any }>`
  height: 100%;
  width: ${(props) => props.score}%;
  background-color: ${(props) => {
    if (props.score >= 80) return '#4CAF50';
    if (props.score >= 60) return '#8BC34A';
    if (props.score >= 40) return '#FFC107';
    if (props.score >= 20) return '#FF9800';
    return '#F44336';
  }};
`;

const ButtonGroup = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
`;

const ExportButton = styled.button<{ theme: any; disabled?: boolean }>`
  padding: 12px 24px;
  background-color: ${(props) => (props.disabled ? '#ccc' : props.theme.colors.primary.main)};
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => (props.disabled ? '#ccc' : props.theme.colors.primary.dark)};
  }
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: 32px;
`;

const BackButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.text.secondary};
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const CompleteButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 24px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

export default SummarySection;
