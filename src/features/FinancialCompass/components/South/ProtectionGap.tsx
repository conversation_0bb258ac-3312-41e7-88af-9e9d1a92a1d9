/**
 * Protection Gap Component
 *
 * This component analyzes gaps in the user's insurance coverage and provides recommendations.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { formatCurrency } from '../../../../utils/formatters';
import { CoverageGap, ProtectionGapData } from '../../../../types/southDirection';

interface ProtectionGapProps {
  onComplete?: () => void;
  onBack?: () => void;
}

const ProtectionGap: React.FC<ProtectionGapProps> = ({ onComplete, onBack }) => {
  const { theme } = useTheme();
  const { data, updateData } = useFinancialCompass();

  const [isSaving, setIsSaving] = useState(false);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Default coverage gaps
  const defaultCoverageGaps: CoverageGap[] = [
    {
      type: 'Life Insurance',
      recommendation: 'Consider coverage of 10-12x your annual income if you have dependents.',
      priority: 'high',
      hasGap: false,
      notes: '',
    },
    {
      type: 'Disability Insurance',
      recommendation:
        'Consider coverage that replaces 60-70% of your income if you become disabled.',
      priority: 'high',
      hasGap: false,
      notes: '',
    },
    {
      type: 'Emergency Fund',
      recommendation: 'Aim for 3-6 months of expenses in a liquid emergency fund.',
      priority: 'high',
      hasGap: false,
      notes: '',
    },
    {
      type: 'Health Insurance',
      recommendation:
        'Ensure you have comprehensive health coverage with manageable deductibles and out-of-pocket maximums.',
      priority: 'high',
      hasGap: false,
      notes: '',
    },
    {
      type: 'Long-Term Care',
      recommendation: "Consider long-term care insurance, especially if you're over 50.",
      priority: 'medium',
      hasGap: false,
      notes: '',
    },
    {
      type: 'Property Insurance',
      recommendation:
        'Ensure your home/rental insurance covers replacement value, not just market value.',
      priority: 'medium',
      hasGap: false,
      notes: '',
    },
    {
      type: 'Auto Insurance',
      recommendation:
        'Consider liability coverage of at least $100,000/$300,000 for bodily injury and $100,000 for property damage.',
      priority: 'medium',
      hasGap: false,
      notes: '',
    },
    {
      type: 'Umbrella Insurance',
      recommendation:
        'Consider an umbrella policy if your net worth exceeds your auto/home liability coverage.',
      priority: 'low',
      hasGap: false,
      notes: '',
    },
  ];

  // Initialize form data from context or with defaults
  const [formData, setFormData] = useState<ProtectionGapData>(
    data.south?.protectionGap || {
      coverageGaps: defaultCoverageGaps,
      lifeInsuranceNeeded: '',
      disabilityInsuranceNeeded: '',
      emergencyFundNeeded: '',
      longTermCareNeeded: '',
      additionalNotes: '',
    }
  );

  // Analyze insurance coverage based on existing data
  useEffect(() => {
    if (data.south?.insuranceCoverage) {
      const {
        policies,
        hasLifeInsurance,
        hasHealthInsurance,
        hasAutoInsurance,
        hasHomeInsurance,
        hasDisabilityInsurance,
        hasLongTermCareInsurance,
      } = data.south.insuranceCoverage;

      // Update coverage gaps based on existing insurance data
      setFormData((prev) => ({
        ...prev,
        coverageGaps: prev.coverageGaps.map((gap) => {
          if (gap.type === 'Life Insurance') {
            return { ...gap, hasGap: !hasLifeInsurance };
          } else if (gap.type === 'Health Insurance') {
            return { ...gap, hasGap: !hasHealthInsurance };
          } else if (gap.type === 'Auto Insurance') {
            return { ...gap, hasGap: !hasAutoInsurance };
          } else if (gap.type === 'Property Insurance') {
            return { ...gap, hasGap: !hasHomeInsurance };
          } else if (gap.type === 'Disability Insurance') {
            return { ...gap, hasGap: !hasDisabilityInsurance };
          } else if (gap.type === 'Long-Term Care') {
            return { ...gap, hasGap: !hasLongTermCareInsurance };
          }
          return gap;
        }),
      }));
    }

    // Calculate life insurance needed
    if (data.north?.incomeDetails?.totalMonthlyIncome) {
      const annualIncome = parseFloat(data.north.incomeDetails.totalMonthlyIncome) * 12;
      const lifeInsuranceNeeded = annualIncome * 10; // 10x annual income as a rule of thumb

      setFormData((prev) => ({
        ...prev,
        lifeInsuranceNeeded: lifeInsuranceNeeded.toString(),
      }));
    }

    // Calculate disability insurance needed
    if (data.north?.incomeDetails?.totalMonthlyIncome) {
      const monthlyIncome = parseFloat(data.north.incomeDetails.totalMonthlyIncome);
      const disabilityInsuranceNeeded = monthlyIncome * 0.6; // 60% of income as a rule of thumb

      setFormData((prev) => ({
        ...prev,
        disabilityInsuranceNeeded: disabilityInsuranceNeeded.toString(),
      }));
    }

    // Calculate emergency fund needed
    if (data.north?.expenseDetails?.totalMonthlyExpenses) {
      const monthlyExpenses = parseFloat(data.north.expenseDetails.totalMonthlyExpenses);
      const emergencyFundNeeded = monthlyExpenses * 6; // 6 months of expenses as a rule of thumb

      setFormData((prev) => ({
        ...prev,
        emergencyFundNeeded: emergencyFundNeeded.toString(),
      }));
    }
  }, [data]);

  // Handle coverage gap toggle
  const handleGapToggle = (type: string, hasGap: boolean) => {
    setFormData((prev) => ({
      ...prev,
      coverageGaps: prev.coverageGaps.map((gap) => (gap.type === type ? { ...gap, hasGap } : gap)),
    }));
  };

  // Handle coverage gap notes change
  const handleGapNotesChange = (type: string, notes: string) => {
    setFormData((prev) => ({
      ...prev,
      coverageGaps: prev.coverageGaps.map((gap) => (gap.type === type ? { ...gap, notes } : gap)),
    }));
  };

  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSave();

    if (onComplete) {
      onComplete();
    }
  };

  // Handle saving data
  const handleSave = () => {
    setIsSaving(true);

    // Update data in context
    updateData('south', 'protectionGap', formData);

    // Show save indicator
    setShowSaveIndicator(true);

    // Hide save indicator after 2 seconds
    setTimeout(() => {
      setIsSaving(false);
      setShowSaveIndicator(false);
    }, 2000);
  };

  // Get priority color
  const getPriorityColor = (priority: CoverageGap['priority'], theme: any): string => {
    switch (priority) {
      case 'high':
        return theme.colors.error.main;
      case 'medium':
        return theme.colors.warning.main;
      case 'low':
        return theme.colors.success.main;
      default:
        return theme.colors.text.secondary;
    }
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Protection Gap Analysis</Title>
        <Description theme={theme}>
          Identify gaps in your insurance coverage and get recommendations for adequate protection.
        </Description>
      </Header>

      <form onSubmit={handleSubmit}>
        <FormSection>
          <SectionTitle>Coverage Gap Analysis</SectionTitle>

          <GapsList>
            {formData.coverageGaps.map((gap) => (
              <GapItem key={gap.type} theme={theme}>
                <GapHeader>
                  <GapType>{gap.type}</GapType>
                  <PriorityBadge priority={gap.priority} theme={theme}>
                    {gap.priority.charAt(0).toUpperCase() + gap.priority.slice(1)} Priority
                  </PriorityBadge>
                </GapHeader>

                <GapRecommendation theme={theme}>{gap.recommendation}</GapRecommendation>

                <GapToggle>
                  <ToggleLabel>Do you have a gap in this coverage?</ToggleLabel>
                  <ToggleButtons>
                    <ToggleButton
                      isActive={gap.hasGap}
                      onClick={() => handleGapToggle(gap.type, true)}
                      color={theme.colors.error.main}
                      theme={theme}
                      type="button"
                    >
                      Yes
                    </ToggleButton>
                    <ToggleButton
                      isActive={!gap.hasGap}
                      onClick={() => handleGapToggle(gap.type, false)}
                      color={theme.colors.success.main}
                      theme={theme}
                      type="button"
                    >
                      No
                    </ToggleButton>
                  </ToggleButtons>
                </GapToggle>

                {gap.hasGap && (
                  <GapNotes>
                    <Label htmlFor={`notes-${gap.type}`}>Notes about this gap</Label>
                    <Textarea
                      id={`notes-${gap.type}`}
                      value={gap.notes}
                      onChange={(e) => handleGapNotesChange(gap.type, e.target.value)}
                      placeholder={`Add notes about your ${gap.type.toLowerCase()} coverage gap`}
                    />
                  </GapNotes>
                )}
              </GapItem>
            ))}
          </GapsList>
        </FormSection>

        <FormSection>
          <SectionTitle>Coverage Recommendations</SectionTitle>

          <RecommendationsContainer theme={theme}>
            <RecommendationItem>
              <RecommendationLabel>Recommended Life Insurance:</RecommendationLabel>
              <RecommendationValue>
                {formData.lifeInsuranceNeeded
                  ? formatCurrency(Number(formData.lifeInsuranceNeeded) || 0)
                  : 'N/A'}
              </RecommendationValue>
              <RecommendationNote>Based on 10x your annual income</RecommendationNote>
            </RecommendationItem>

            <RecommendationItem>
              <RecommendationLabel>Recommended Disability Insurance:</RecommendationLabel>
              <RecommendationValue>
                {formData.disabilityInsuranceNeeded
                  ? formatCurrency(Number(formData.disabilityInsuranceNeeded) || 0) + '/month'
                  : 'N/A'}
              </RecommendationValue>
              <RecommendationNote>Based on 60% of your monthly income</RecommendationNote>
            </RecommendationItem>

            <RecommendationItem>
              <RecommendationLabel>Recommended Emergency Fund:</RecommendationLabel>
              <RecommendationValue>
                {formData.emergencyFundNeeded
                  ? formatCurrency(Number(formData.emergencyFundNeeded) || 0)
                  : 'N/A'}
              </RecommendationValue>
              <RecommendationNote>Based on 6 months of expenses</RecommendationNote>
            </RecommendationItem>
          </RecommendationsContainer>
        </FormSection>

        <FormSection>
          <SectionTitle>Additional Notes</SectionTitle>
          <FormField>
            <Label htmlFor="additionalNotes">Notes about your protection plan</Label>
            <Textarea
              id="additionalNotes"
              name="additionalNotes"
              value={formData.additionalNotes}
              onChange={handleChange}
              placeholder="Add any additional notes about your protection plan or insurance needs"
            />
          </FormField>
        </FormSection>

        <ButtonContainer>
          {onBack && (
            <BackButton type="button" onClick={onBack} theme={theme}>
              Back
            </BackButton>
          )}

          <SaveIndicator visible={showSaveIndicator}>✓ Saved</SaveIndicator>

          <SubmitButton type="submit" theme={theme}>
            {isSaving ? 'Saving...' : 'Save & Continue'}
          </SubmitButton>
        </ButtonContainer>
      </form>
    </Container>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  padding: 24px;
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const Title = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const Description = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const FormSection = styled.div`
  margin-bottom: 32px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const GapsList = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 16px;
`;

const GapItem = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  padding: 16px;
`;

const GapHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const GapType = styled.div`
  font-weight: 600;
  font-size: 1.1rem;
`;

const PriorityBadge = styled.div<{ priority: string; theme: any }>`
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  background-color: ${(props) => {
    // Inline priority color logic
    let color;
    switch (props.priority) {
      case 'high':
        color = props.theme.colors.error.main;
        break;
      case 'medium':
        color = props.theme.colors.warning.main;
        break;
      case 'low':
        color = props.theme.colors.success.main;
        break;
      default:
        color = props.theme.colors.text.secondary;
    }
    return color + '20'; // Add 20% opacity
  }};
  color: ${(props) => {
    // Inline priority color logic
    switch (props.priority) {
      case 'high':
        return props.theme.colors.error.main;
      case 'medium':
        return props.theme.colors.warning.main;
      case 'low':
        return props.theme.colors.success.main;
      default:
        return props.theme.colors.text.secondary;
    }
  }};
`;

const GapRecommendation = styled.p<{ theme: any }>`
  margin: 0 0 16px 0;
  font-size: 0.9rem;
  line-height: 1.5;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const GapToggle = styled.div`
  margin-bottom: 16px;
`;

const ToggleLabel = styled.div`
  font-weight: 500;
  margin-bottom: 8px;
`;

const ToggleButtons = styled.div`
  display: flex;
`;

const ToggleButton = styled.button<{ isActive: boolean; color: string; theme: any }>`
  flex: 1;
  padding: 8px 12px;
  border: 1px solid ${(props) => (props.isActive ? props.color : props.theme.colors.border)};
  background-color: ${(props) => (props.isActive ? `${props.color}20` : 'transparent')};
  color: ${(props) => (props.isActive ? props.color : props.theme.colors.text.secondary)};
  font-weight: ${(props) => (props.isActive ? 'bold' : 'normal')};
  cursor: pointer;
  transition: all 0.2s;

  &:first-child {
    border-radius: 4px 0 0 4px;
  }

  &:last-child {
    border-radius: 0 4px 4px 0;
  }

  &:hover {
    background-color: ${(props) =>
      props.isActive ? `${props.color}30` : props.theme.colors.background.paper};
  }
`;

const GapNotes = styled.div`
  margin-top: 16px;
`;

const RecommendationsContainer = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  padding: 16px;
`;

const RecommendationItem = styled.div`
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
`;

const RecommendationLabel = styled.div`
  font-weight: 500;
  margin-bottom: 4px;
`;

const RecommendationValue = styled.div`
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 4px;
`;

const RecommendationNote = styled.div`
  font-size: 0.8rem;
  color: #666;
`;

const FormField = styled.div`
  margin-bottom: 16px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
`;

const Textarea = styled.textarea`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  min-height: 80px;
  resize: vertical;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
`;

const BackButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.text.secondary};
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const SubmitButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 24px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  color: green;
  margin-right: 16px;
`;

export default ProtectionGap;
