import React, { useState } from 'react';
import { <PERSON>, CardContent, Typo<PERSON>, Box, Divider, TextField, Button } from '@mui/material';

function getRiskProfile(score: number, horizon: number) {
  if (score >= 8 && horizon >= 20)
    return { label: 'Aggressive', desc: 'You can tolerate high risk for high returns.' };
  if (score >= 5 && horizon >= 10)
    return { label: 'Moderate', desc: 'You prefer a balance of risk and reward.' };
  return { label: 'Conservative', desc: 'You prefer safety and capital preservation.' };
}

const RiskAssessmentPanel: React.FC = () => {
  const [riskTolerance, setRiskTolerance] = useState('5');
  const [horizon, setHorizon] = useState('10');
  const [errors, setErrors] = useState<{ riskTolerance?: string; horizon?: string }>({});
  const [submitted, setSubmitted] = useState(false);
  const [profile, setProfile] = useState<{ label: string; desc: string } | null>(null);

  const validate = () => {
    const newErrors: { riskTolerance?: string; horizon?: string } = {};
    if (
      !riskTolerance ||
      isNaN(Number(riskTolerance)) ||
      Number(riskTolerance) < 1 ||
      Number(riskTolerance) > 10
    ) {
      newErrors.riskTolerance = 'Risk tolerance must be between 1 and 10';
    }
    if (!horizon || isNaN(Number(horizon)) || Number(horizon) < 1 || Number(horizon) > 50) {
      newErrors.horizon = 'Investment horizon must be between 1 and 50 years';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitted(true);
    if (validate()) {
      const score = Number(riskTolerance);
      const years = Number(horizon);
      setProfile(getRiskProfile(score, years));
    }
  };

  return (
    <Card variant="outlined">
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Risk Assessment
        </Typography>
        <Divider sx={{ mb: 2 }} />
        <Box mb={2}>
          <Typography variant="body2" color="textSecondary">
            Enter your risk tolerance and investment horizon to see your risk profile.
          </Typography>
        </Box>
        <form onSubmit={handleSubmit} noValidate>
          <Box display="flex" gap={2} mb={2}>
            <TextField
              label="Risk Tolerance (1-10)"
              value={riskTolerance}
              onChange={(e) => setRiskTolerance(e.target.value)}
              error={!!errors.riskTolerance && submitted}
              helperText={submitted && errors.riskTolerance}
              type="number"
              inputProps={{ min: 1, max: 10 }}
            />
            <TextField
              label="Investment Horizon (years)"
              value={horizon}
              onChange={(e) => setHorizon(e.target.value)}
              error={!!errors.horizon && submitted}
              helperText={submitted && errors.horizon}
              type="number"
              inputProps={{ min: 1, max: 50 }}
            />
            <Button type="submit" variant="contained" color="primary">
              Assess
            </Button>
          </Box>
        </form>
        {profile && (
          <Box mt={3}>
            <Typography variant="subtitle1" gutterBottom>
              Risk Profile: <b>{profile.label}</b>
            </Typography>
            <Typography variant="body2" color="textSecondary">
              {profile.desc}
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default RiskAssessmentPanel;
