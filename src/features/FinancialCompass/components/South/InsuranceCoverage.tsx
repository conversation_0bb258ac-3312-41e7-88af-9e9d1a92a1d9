/**
 * Insurance Coverage Component
 *
 * This component collects and displays the user's insurance policies as part of the
 * South direction of the Financial Compass ("What Protects You").
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { useGuidedJourney } from '../../../GuidedJourney/context/GuidedJourneyContext';
import { formatCurrency } from '../../../../utils/formatters';
import { InsurancePolicy } from '../../../../types/southDirection';

interface InsuranceCoverageProps {
  onComplete?: () => void;
  onBack?: () => void;
}

interface InsuranceCoverageData {
  policies: InsurancePolicy[];
  hasLifeInsurance: boolean;
  hasHealthInsurance: boolean;
  hasAutoInsurance: boolean;
  hasHomeInsurance: boolean;
  hasDisabilityInsurance: boolean;
  hasLongTermCareInsurance: boolean;
  totalAnnualPremium: number;
}

const InsuranceCoverage: React.FC<InsuranceCoverageProps> = ({ onComplete, onBack }) => {
  const { theme } = useTheme();
  const { data, updateData, updateSouthSectionStatus } = useFinancialCompass();
  const { askQuestion } = useGuidedJourney();

  const [isSaving, setIsSaving] = useState(false);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Initialize form data from context or with defaults
  const defaultInsuranceCoverage: InsuranceCoverageData = {
    policies: [],
    hasLifeInsurance: false,
    hasHealthInsurance: false,
    hasAutoInsurance: false,
    hasHomeInsurance: false,
    hasDisabilityInsurance: false,
    hasLongTermCareInsurance: false,
    totalAnnualPremium: 0,
  };

  const [formData, setFormData] = useState<InsuranceCoverageData>(
    data.south?.insuranceCoverage
      ? {
          ...defaultInsuranceCoverage,
          ...data.south.insuranceCoverage,
          // Ensure policies is an array
          policies: Array.isArray(data.south.insuranceCoverage.policies)
            ? data.south.insuranceCoverage.policies
            : [],
        }
      : defaultInsuranceCoverage
  );

  // New policy form
  const [newPolicy, setNewPolicy] = useState<Omit<InsurancePolicy, 'id'>>({
    type: '',
    provider: '',
    policyNumber: '',
    coverageAmount: '',
    premium: '',
    frequency: 'monthly',
    deductible: '',
    beneficiaries: '',
    notes: '',
    renewalDate: '',
  });

  // Insurance type options
  const insuranceTypes = [
    'Life',
    'Health',
    'Auto',
    'Home/Renters',
    'Disability',
    'Long-Term Care',
    'Umbrella',
    'Other',
  ];

  // Premium frequency options
  const frequencyOptions = [
    { value: 'monthly', label: 'Monthly' },
    { value: 'quarterly', label: 'Quarterly' },
    { value: 'semi-annual', label: 'Semi-Annual' },
    { value: 'annual', label: 'Annual' },
  ];

  // Handle new policy field changes
  const handleNewPolicyChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setNewPolicy((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Add new policy
  const handleAddPolicy = () => {
    if (!newPolicy.type || !newPolicy.provider) return;

    const newPolicyWithId: InsurancePolicy = {
      ...newPolicy,
      id: `policy-${Date.now()}`,
    };

    // Update insurance type flags
    const updatedFormData = { ...formData };
    if (newPolicy.type === 'Life') updatedFormData.hasLifeInsurance = true;
    if (newPolicy.type === 'Health') updatedFormData.hasHealthInsurance = true;
    if (newPolicy.type === 'Auto') updatedFormData.hasAutoInsurance = true;
    if (newPolicy.type === 'Home/Renters') updatedFormData.hasHomeInsurance = true;
    if (newPolicy.type === 'Disability') updatedFormData.hasDisabilityInsurance = true;
    if (newPolicy.type === 'Long-Term Care') updatedFormData.hasLongTermCareInsurance = true;

    // Add new policy to the list
    updatedFormData.policies = [...formData.policies, newPolicyWithId];

    // Update total annual premium
    updatedFormData.totalAnnualPremium = calculateTotalAnnualPremium(updatedFormData.policies);

    setFormData(updatedFormData);

    // Reset new policy form
    setNewPolicy({
      type: '',
      provider: '',
      policyNumber: '',
      coverageAmount: '',
      premium: '',
      frequency: 'monthly',
      deductible: '',
      beneficiaries: '',
      notes: '',
      renewalDate: '',
    });
  };

  // Remove policy
  const handleRemovePolicy = (id: string) => {
    const updatedPolicies = formData.policies.filter((policy) => policy.id !== id);

    // Update insurance type flags
    const updatedFormData = { ...formData, policies: updatedPolicies };
    updatedFormData.hasLifeInsurance = updatedPolicies.some((p) => p.type === 'Life');
    updatedFormData.hasHealthInsurance = updatedPolicies.some((p) => p.type === 'Health');
    updatedFormData.hasAutoInsurance = updatedPolicies.some((p) => p.type === 'Auto');
    updatedFormData.hasHomeInsurance = updatedPolicies.some((p) => p.type === 'Home/Renters');
    updatedFormData.hasDisabilityInsurance = updatedPolicies.some((p) => p.type === 'Disability');
    updatedFormData.hasLongTermCareInsurance = updatedPolicies.some(
      (p) => p.type === 'Long-Term Care'
    );

    // Update total annual premium
    updatedFormData.totalAnnualPremium = calculateTotalAnnualPremium(updatedPolicies);

    setFormData(updatedFormData);
  };

  // Calculate total annual premium
  const calculateTotalAnnualPremium = (policies: InsurancePolicy[]): number => {
    return policies.reduce((total, policy) => {
      const premium = parseFloat(policy.premium) || 0;

      if (policy.frequency === 'monthly') {
        return total + premium * 12;
      } else if (policy.frequency === 'quarterly') {
        return total + premium * 4;
      } else if (policy.frequency === 'semi-annual') {
        return total + premium * 2;
      } else {
        return total + premium;
      }
    }, 0);
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('InsuranceCoverage: Form submitted');
    handleSave();

    // Mark this section as completed
    updateSouthSectionStatus('insurance_coverage', true, false);

    // Call the onComplete callback
    if (onComplete) {
      console.log('InsuranceCoverage: Calling onComplete');
      onComplete();
    }
  };

  // Handle saving data
  const handleSave = () => {
    setIsSaving(true);

    // Update data in context
    updateData('south', 'insuranceCoverage', formData);

    // Show save indicator
    setShowSaveIndicator(true);

    // Hide save indicator after 2 seconds
    setTimeout(() => {
      setIsSaving(false);
      setShowSaveIndicator(false);
    }, 2000);
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Insurance Coverage</Title>
        <Description theme={theme}>
          Track and manage your insurance policies to ensure adequate protection.
        </Description>
      </Header>

      <form onSubmit={handleSubmit}>
        <FormSection>
          <SectionTitle>Your Insurance Policies</SectionTitle>

          {formData.policies.length > 0 ? (
            <PoliciesList>
              {formData.policies.map((policy) => (
                <PolicyItem key={policy.id} theme={theme}>
                  <PolicyHeader>
                    <PolicyType>{policy.type} Insurance</PolicyType>
                    <PolicyProvider>{policy.provider}</PolicyProvider>
                    <RemoveButton onClick={() => handleRemovePolicy(policy.id)} theme={theme}>
                      Remove
                    </RemoveButton>
                  </PolicyHeader>

                  <PolicyDetails>
                    <PolicyDetail>
                      <DetailLabel>Policy Number:</DetailLabel>
                      <DetailValue>{policy.policyNumber}</DetailValue>
                    </PolicyDetail>

                    <PolicyDetail>
                      <DetailLabel>Coverage Amount:</DetailLabel>
                      <DetailValue>
                        {formatCurrency(Number(policy.coverageAmount) || 0)}
                      </DetailValue>
                    </PolicyDetail>

                    <PolicyDetail>
                      <DetailLabel>Premium:</DetailLabel>
                      <DetailValue>
                        {formatCurrency(Number(policy.premium) || 0)} ({policy.frequency})
                      </DetailValue>
                    </PolicyDetail>

                    {policy.deductible && (
                      <PolicyDetail>
                        <DetailLabel>Deductible:</DetailLabel>
                        <DetailValue>{formatCurrency(Number(policy.deductible) || 0)}</DetailValue>
                      </PolicyDetail>
                    )}

                    {policy.beneficiaries && (
                      <PolicyDetail>
                        <DetailLabel>Beneficiaries:</DetailLabel>
                        <DetailValue>{policy.beneficiaries}</DetailValue>
                      </PolicyDetail>
                    )}

                    {policy.renewalDate && (
                      <PolicyDetail>
                        <DetailLabel>Renewal Date:</DetailLabel>
                        <DetailValue>{policy.renewalDate}</DetailValue>
                      </PolicyDetail>
                    )}

                    {policy.notes && <PolicyNotes>{policy.notes}</PolicyNotes>}
                  </PolicyDetails>
                </PolicyItem>
              ))}
            </PoliciesList>
          ) : (
            <EmptyState theme={theme}>
              <EmptyIcon>🛡️</EmptyIcon>
              <EmptyText>
                You haven't added any insurance policies yet. Use the form below to add your first
                policy.
              </EmptyText>
            </EmptyState>
          )}
        </FormSection>

        <FormSection>
          <SectionTitle>Add Insurance Policy</SectionTitle>

          <AddPolicyForm theme={theme}>
            <FormRow>
              <FormField>
                <Label htmlFor="type">Insurance Type</Label>
                <Select
                  id="type"
                  name="type"
                  value={newPolicy.type}
                  onChange={handleNewPolicyChange}
                  required
                >
                  <option value="">Select type...</option>
                  {insuranceTypes.map((type) => (
                    <option key={type} value={type}>
                      {type}
                    </option>
                  ))}
                </Select>
              </FormField>

              <FormField>
                <Label htmlFor="provider">Insurance Provider</Label>
                <Input
                  type="text"
                  id="provider"
                  name="provider"
                  value={newPolicy.provider}
                  onChange={handleNewPolicyChange}
                  placeholder="e.g., State Farm"
                  required
                />
              </FormField>

              <FormField>
                <Label htmlFor="policyNumber">Policy Number</Label>
                <Input
                  type="text"
                  id="policyNumber"
                  name="policyNumber"
                  value={newPolicy.policyNumber}
                  onChange={handleNewPolicyChange}
                  placeholder="e.g., POL-123456"
                />
              </FormField>
            </FormRow>

            <FormRow>
              <FormField>
                <Label htmlFor="coverageAmount">Coverage Amount</Label>
                <Input
                  type="text"
                  id="coverageAmount"
                  name="coverageAmount"
                  value={newPolicy.coverageAmount}
                  onChange={handleNewPolicyChange}
                  placeholder="e.g., 500000"
                />
              </FormField>

              <FormField>
                <Label htmlFor="premium">Premium</Label>
                <Input
                  type="text"
                  id="premium"
                  name="premium"
                  value={newPolicy.premium}
                  onChange={handleNewPolicyChange}
                  placeholder="e.g., 150"
                />
              </FormField>

              <FormField>
                <Label htmlFor="frequency">Frequency</Label>
                <Select
                  id="frequency"
                  name="frequency"
                  value={newPolicy.frequency}
                  onChange={handleNewPolicyChange}
                >
                  {frequencyOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </Select>
              </FormField>
            </FormRow>

            <FormRow>
              <FormField>
                <Label htmlFor="deductible">Deductible</Label>
                <Input
                  type="text"
                  id="deductible"
                  name="deductible"
                  value={newPolicy.deductible}
                  onChange={handleNewPolicyChange}
                  placeholder="e.g., 1000"
                />
              </FormField>

              <FormField>
                <Label htmlFor="beneficiaries">Beneficiaries</Label>
                <Input
                  type="text"
                  id="beneficiaries"
                  name="beneficiaries"
                  value={newPolicy.beneficiaries}
                  onChange={handleNewPolicyChange}
                  placeholder="e.g., Spouse, Children"
                />
              </FormField>

              <FormField>
                <Label htmlFor="renewalDate">Renewal Date</Label>
                <Input
                  type="date"
                  id="renewalDate"
                  name="renewalDate"
                  value={newPolicy.renewalDate}
                  onChange={handleNewPolicyChange}
                />
              </FormField>
            </FormRow>

            <FormField>
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                name="notes"
                value={newPolicy.notes}
                onChange={handleNewPolicyChange}
                placeholder="Additional details about this policy"
              />
            </FormField>

            <AddButton
              type="button"
              onClick={handleAddPolicy}
              disabled={!newPolicy.type || !newPolicy.provider}
              theme={theme}
            >
              Add Policy
            </AddButton>
          </AddPolicyForm>
        </FormSection>

        <FormSection>
          <SectionTitle>Insurance Summary</SectionTitle>

          <SummaryContainer theme={theme}>
            <SummaryRow>
              <SummaryLabel>Total Policies:</SummaryLabel>
              <SummaryValue>{formData.policies.length}</SummaryValue>
            </SummaryRow>

            <SummaryRow>
              <SummaryLabel>Total Annual Premium:</SummaryLabel>
              <SummaryValue highlight>{formatCurrency(formData.totalAnnualPremium)}</SummaryValue>
            </SummaryRow>

            <CoverageChecklist>
              <ChecklistItem isChecked={formData.hasLifeInsurance} theme={theme}>
                <ChecklistIcon isChecked={formData.hasLifeInsurance}>
                  {formData.hasLifeInsurance ? '✓' : '○'}
                </ChecklistIcon>
                <ChecklistText>Life Insurance</ChecklistText>
              </ChecklistItem>

              <ChecklistItem isChecked={formData.hasHealthInsurance} theme={theme}>
                <ChecklistIcon isChecked={formData.hasHealthInsurance}>
                  {formData.hasHealthInsurance ? '✓' : '○'}
                </ChecklistIcon>
                <ChecklistText>Health Insurance</ChecklistText>
              </ChecklistItem>

              <ChecklistItem isChecked={formData.hasAutoInsurance} theme={theme}>
                <ChecklistIcon isChecked={formData.hasAutoInsurance}>
                  {formData.hasAutoInsurance ? '✓' : '○'}
                </ChecklistIcon>
                <ChecklistText>Auto Insurance</ChecklistText>
              </ChecklistItem>

              <ChecklistItem isChecked={formData.hasHomeInsurance} theme={theme}>
                <ChecklistIcon isChecked={formData.hasHomeInsurance}>
                  {formData.hasHomeInsurance ? '✓' : '○'}
                </ChecklistIcon>
                <ChecklistText>Home/Renters Insurance</ChecklistText>
              </ChecklistItem>

              <ChecklistItem isChecked={formData.hasDisabilityInsurance} theme={theme}>
                <ChecklistIcon isChecked={formData.hasDisabilityInsurance}>
                  {formData.hasDisabilityInsurance ? '✓' : '○'}
                </ChecklistIcon>
                <ChecklistText>Disability Insurance</ChecklistText>
              </ChecklistItem>

              <ChecklistItem isChecked={formData.hasLongTermCareInsurance} theme={theme}>
                <ChecklistIcon isChecked={formData.hasLongTermCareInsurance}>
                  {formData.hasLongTermCareInsurance ? '✓' : '○'}
                </ChecklistIcon>
                <ChecklistText>Long-Term Care Insurance</ChecklistText>
              </ChecklistItem>
            </CoverageChecklist>
          </SummaryContainer>
        </FormSection>

        <ButtonContainer>
          {onBack && (
            <BackButton type="button" onClick={onBack} theme={theme}>
              Back
            </BackButton>
          )}

          <SaveIndicator visible={showSaveIndicator}>✓ Saved</SaveIndicator>

          <SubmitButton type="submit" theme={theme}>
            {isSaving ? 'Saving...' : 'Save & Continue'}
          </SubmitButton>
        </ButtonContainer>
      </form>
    </Container>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  padding: 24px;
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const Title = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const Description = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const FormSection = styled.div`
  margin-bottom: 32px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const PoliciesList = styled.div`
  margin-bottom: 24px;
`;

const PolicyItem = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
`;

const PolicyHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const PolicyType = styled.div`
  font-weight: 600;
  font-size: 1.1rem;
`;

const PolicyProvider = styled.div`
  font-style: italic;
  color: #666;
`;

const RemoveButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.error.main};
  border: none;
  font-size: 0.9rem;
  cursor: pointer;
  padding: 4px 8px;

  &:hover {
    text-decoration: underline;
  }
`;

const PolicyDetails = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
`;

const PolicyDetail = styled.div`
  margin-bottom: 8px;
`;

const DetailLabel = styled.div`
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 4px;
`;

const DetailValue = styled.div`
  font-size: 0.9rem;
`;

const PolicyNotes = styled.div`
  grid-column: 1 / -1;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #eee;
  font-style: italic;
  font-size: 0.9rem;
`;

const EmptyState = styled.div<{ theme: any }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  margin-bottom: 24px;
`;

const EmptyIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
`;

const EmptyText = styled.p`
  text-align: center;
  color: #666;
  max-width: 400px;
`;

const AddPolicyForm = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  padding: 16px;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;

const FormField = styled.div`
  margin-bottom: 16px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
`;

const Input = styled.input`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
`;

const Select = styled.select`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
`;

const Textarea = styled.textarea`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  min-height: 80px;
  resize: vertical;
`;

const AddButton = styled.button<{ theme: any; disabled?: boolean }>`
  background-color: ${(props) =>
    props.disabled
      ? props.theme.colors.action.disabledBackground
      : props.theme.colors.primary.main};
  color: ${(props) =>
    props.disabled ? props.theme.colors.action.disabled : props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) =>
      props.disabled
        ? props.theme.colors.action.disabledBackground
        : props.theme.colors.primary.dark};
  }
`;

const SummaryContainer = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.default};
  padding: 16px;
  border-radius: 8px;
`;

const SummaryRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #eee;
  }
`;

const SummaryLabel = styled.div`
  font-weight: 500;
`;

const SummaryValue = styled.div<{ highlight?: boolean }>`
  font-weight: ${(props) => (props.highlight ? 'bold' : 'normal')};
  font-size: ${(props) => (props.highlight ? '1.2rem' : '1rem')};
`;

const CoverageChecklist = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
  margin-top: 16px;
`;

const ChecklistItem = styled.div<{ isChecked: boolean; theme: any }>`
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  background-color: ${(props) =>
    props.isChecked ? props.theme.colors.success.light : props.theme.colors.background.paper};
`;

const ChecklistIcon = styled.div<{ isChecked: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 8px;
  color: ${(props) => (props.isChecked ? 'green' : '#666')};
  font-weight: ${(props) => (props.isChecked ? 'bold' : 'normal')};
`;

const ChecklistText = styled.div`
  font-size: 0.9rem;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
`;

const BackButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.text.secondary};
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const SubmitButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 24px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  color: green;
  margin-right: 16px;
`;

export default InsuranceCoverage;
