/**
 * Risk Tolerance Component
 *
 * This component assesses the user's comfort level with financial risk.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { RiskQuestion, RiskToleranceData } from '../../../../types/southDirection';

interface RiskToleranceProps {
  onComplete?: () => void;
  onBack?: () => void;
}

const RiskTolerance: React.FC<RiskToleranceProps> = ({ onComplete, onBack }) => {
  const { theme } = useTheme();
  const { data, updateData } = useFinancialCompass();

  const [isSaving, setIsSaving] = useState(false);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Enhanced risk assessment questions with behavioral finance elements and risk capacity
  const defaultQuestions: RiskQuestion[] = [
    // Risk Tolerance Questions (Psychological)
    {
      id: 'market_decline',
      question:
        'If your investment portfolio lost 20% of its value in a short period, what would you do?',
      options: [
        { value: 1, text: 'Sell all my investments and move to cash' },
        { value: 2, text: 'Sell some of my investments to reduce risk' },
        { value: 3, text: 'Do nothing and wait for recovery' },
        { value: 4, text: 'Keep my current investments and add a little more' },
        {
          value: 5,
          text: 'Significantly increase my investments to take advantage of lower prices',
        },
      ],
      answer: null,
      category: 'risk_tolerance',
    },
    {
      id: 'investment_goal',
      question: 'Which statement best describes your primary investment goal?',
      options: [
        {
          value: 1,
          text: 'Preserving my capital is my primary goal, even if it means lower returns',
        },
        { value: 2, text: 'I want my investments to generate income with minimal risk' },
        { value: 3, text: 'I want a balance between growth and income' },
        {
          value: 4,
          text: 'I primarily want my investments to grow, and I can accept some volatility',
        },
        {
          value: 5,
          text: 'I want maximum growth potential, even if it means substantial volatility',
        },
      ],
      answer: null,
      category: 'risk_tolerance',
    },
    {
      id: 'risk_vs_return',
      question: 'Which investment portfolio would you be most comfortable with?',
      options: [
        { value: 1, text: 'Portfolio A: Potential return of 4-6% with very low risk' },
        { value: 2, text: 'Portfolio B: Potential return of 6-8% with low to moderate risk' },
        { value: 3, text: 'Portfolio C: Potential return of 8-10% with moderate risk' },
        { value: 4, text: 'Portfolio D: Potential return of 10-12% with moderate to high risk' },
        { value: 5, text: 'Portfolio E: Potential return of 12%+ with high risk' },
      ],
      answer: null,
      category: 'risk_tolerance',
    },

    // Risk Capacity Questions (Financial Ability)
    {
      id: 'time_horizon',
      question:
        'How long do you plan to invest before you need to access a significant portion of your money?',
      options: [
        { value: 1, text: 'Less than 3 years' },
        { value: 2, text: '3-5 years' },
        { value: 3, text: '6-10 years' },
        { value: 4, text: '11-20 years' },
        { value: 5, text: 'More than 20 years' },
      ],
      answer: null,
      category: 'risk_capacity',
    },
    {
      id: 'emergency_fund',
      question: 'How many months of expenses do you have saved in an emergency fund?',
      options: [
        { value: 1, text: 'Less than 1 month' },
        { value: 2, text: '1-2 months' },
        { value: 3, text: '3-5 months' },
        { value: 4, text: '6-12 months' },
        { value: 5, text: 'More than 12 months' },
      ],
      answer: null,
      category: 'risk_capacity',
    },
    {
      id: 'income_stability',
      question: 'How stable is your current income?',
      options: [
        { value: 1, text: 'Very unstable - My income varies significantly or my job is at risk' },
        { value: 2, text: 'Somewhat unstable - My income varies or my job security is uncertain' },
        { value: 3, text: 'Moderate - My income is relatively stable but not guaranteed' },
        { value: 4, text: 'Stable - My income is consistent and my job is secure' },
        {
          value: 5,
          text: 'Very stable - My income is guaranteed or I have multiple reliable sources',
        },
      ],
      answer: null,
      category: 'risk_capacity',
    },

    // Behavioral Finance Questions
    {
      id: 'past_behavior',
      question: 'Thinking about your past investment decisions, which statement is most accurate?',
      options: [
        {
          value: 1,
          text: 'I often regret selling investments too quickly when they drop in value',
        },
        { value: 2, text: 'I sometimes make emotional decisions during market volatility' },
        { value: 3, text: 'I occasionally check my investments during market downturns' },
        { value: 4, text: 'I rarely make changes to my investments based on market movements' },
        {
          value: 5,
          text: 'I consistently stick to my investment plan regardless of market conditions',
        },
      ],
      answer: null,
      category: 'behavioral',
    },
    {
      id: 'fomo',
      question:
        'When you hear about an investment that friends or media say is performing well, how do you typically react?',
      options: [
        { value: 5, text: 'I ignore it and stick to my investment plan' },
        {
          value: 4,
          text: 'I research it thoroughly before considering any changes to my portfolio',
        },
        { value: 3, text: 'I might add a small position if the research looks promising' },
        { value: 2, text: 'I often add it to my portfolio to avoid missing out' },
        { value: 1, text: 'I frequently adjust my portfolio based on trending investments' },
      ],
      answer: null,
      category: 'behavioral',
    },
    {
      id: 'loss_aversion',
      question: 'Which would bother you more?',
      options: [
        { value: 1, text: 'Losing 10% of your investment value' },
        { value: 2, text: 'Watching others gain 20% while you gain only 10%' },
        { value: 3, text: 'Both would bother me equally' },
        { value: 4, text: 'Neither would bother me much; I focus on long-term results' },
        { value: 5, text: 'I view market fluctuations as opportunities rather than problems' },
      ],
      answer: null,
      category: 'behavioral',
    },

    // Knowledge and Experience
    {
      id: 'experience',
      question: 'How would you describe your investment experience and knowledge?',
      options: [
        { value: 1, text: 'Very limited - I have little experience with investments' },
        { value: 2, text: 'Basic - I understand simple investment concepts' },
        { value: 3, text: 'Moderate - I have some experience with different investment types' },
        {
          value: 4,
          text: 'Advanced - I have invested in various assets and understand market dynamics',
        },
        {
          value: 5,
          text: 'Expert - I have extensive investment experience and deep market knowledge',
        },
      ],
      answer: null,
      category: 'knowledge',
    },
  ];

  // Initialize form data from context or with defaults
  const defaultRiskToleranceData: RiskToleranceData = {
    questions: defaultQuestions,
    score: 0,
    riskProfile: 'moderate',
    additionalNotes: '',
  };

  const [formData, setFormData] = useState<RiskToleranceData>(
    data.south?.riskTolerance
      ? {
          ...defaultRiskToleranceData,
          ...data.south.riskTolerance,
          // Ensure questions is properly initialized
          questions: Array.isArray(data.south.riskTolerance.questions)
            ? data.south.riskTolerance.questions
            : defaultQuestions,
        }
      : defaultRiskToleranceData
  );

  // Handle question answer
  const handleAnswerChange = (questionId: string, value: number) => {
    setFormData((prev) => ({
      ...prev,
      questions: prev.questions.map((q) => (q.id === questionId ? { ...q, answer: value } : q)),
    }));
  };

  // Handle notes change
  const handleNotesChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFormData((prev) => ({
      ...prev,
      additionalNotes: e.target.value,
    }));
  };

  // Calculate risk scores by category and overall profile whenever answers change
  useEffect(() => {
    const answeredQuestions = formData.questions.filter((q) => q.answer !== null);

    if (answeredQuestions.length > 0) {
      // Calculate total score
      const totalScore = answeredQuestions.reduce((sum, q) => sum + (q.answer || 0), 0);

      // Calculate average score (1-5 scale)
      const averageScore = totalScore / answeredQuestions.length;

      // Calculate category-specific scores
      const calculateCategoryScore = (category: string): number => {
        const categoryQuestions = answeredQuestions.filter((q) => q.category === category);
        if (categoryQuestions.length === 0) return 0;

        const categoryTotal = categoryQuestions.reduce((sum, q) => sum + (q.answer || 0), 0);
        return categoryTotal / categoryQuestions.length;
      };

      const riskToleranceScore = calculateCategoryScore('risk_tolerance');
      const riskCapacityScore = calculateCategoryScore('risk_capacity');
      const behavioralScore = calculateCategoryScore('behavioral');
      const knowledgeScore = calculateCategoryScore('knowledge');

      // Check for misalignment between risk tolerance and risk capacity
      const hasMisalignment = Math.abs(riskToleranceScore - riskCapacityScore) > 1.5;
      let misalignmentNotes = '';

      if (hasMisalignment) {
        if (riskToleranceScore > riskCapacityScore) {
          misalignmentNotes =
            'Your psychological comfort with risk is higher than your financial capacity to take risk. Consider adjusting your investment strategy to match your financial situation.';
        } else {
          misalignmentNotes =
            'Your financial capacity to take risk is higher than your psychological comfort with risk. You may be missing potential growth opportunities.';
        }
      }

      // Determine risk profile based on a weighted average of tolerance and capacity
      // Give more weight to the lower of the two to be conservative
      const weightedScore =
        riskToleranceScore < riskCapacityScore
          ? riskToleranceScore * 0.7 + riskCapacityScore * 0.3
          : riskCapacityScore * 0.7 + riskToleranceScore * 0.3;

      let riskProfile: RiskToleranceData['riskProfile'] = 'moderate';

      if (weightedScore <= 1.5) {
        riskProfile = 'conservative';
      } else if (weightedScore <= 2.5) {
        riskProfile = 'moderately_conservative';
      } else if (weightedScore <= 3.5) {
        riskProfile = 'moderate';
      } else if (weightedScore <= 4.5) {
        riskProfile = 'moderately_aggressive';
      } else {
        riskProfile = 'aggressive';
      }

      setFormData((prev) => ({
        ...prev,
        score: weightedScore,
        riskProfile,
        riskToleranceScore,
        riskCapacityScore,
        behavioralScore,
        knowledgeScore,
        hasMisalignment,
        misalignmentNotes,
      }));
    }
  }, [formData.questions]);

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSave();

    if (onComplete) {
      onComplete();
    }
  };

  // Handle saving data
  const handleSave = () => {
    setIsSaving(true);

    // Update data in context
    updateData('south', 'riskTolerance', formData);

    // Show save indicator
    setShowSaveIndicator(true);

    // Hide save indicator after 2 seconds
    setTimeout(() => {
      setIsSaving(false);
      setShowSaveIndicator(false);
    }, 2000);
  };

  // Get risk profile display name
  const getRiskProfileDisplayName = (profile: RiskToleranceData['riskProfile']): string => {
    switch (profile) {
      case 'conservative':
        return 'Conservative';
      case 'moderately_conservative':
        return 'Moderately Conservative';
      case 'moderate':
        return 'Moderate';
      case 'moderately_aggressive':
        return 'Moderately Aggressive';
      case 'aggressive':
        return 'Aggressive';
      default:
        return 'Moderate';
    }
  };

  // Get risk profile description
  const getRiskProfileDescription = (profile: RiskToleranceData['riskProfile']): string => {
    switch (profile) {
      case 'conservative':
        return 'You prioritize protecting your capital over growth. Your portfolio should focus on stable, income-producing investments with minimal volatility.';
      case 'moderately_conservative':
        return 'You prefer stability but are willing to accept some risk for potential growth. Your portfolio should emphasize income with some growth-oriented investments.';
      case 'moderate':
        return 'You seek a balance between growth and stability. Your portfolio should include a mix of growth and income investments with moderate volatility.';
      case 'moderately_aggressive':
        return 'You prioritize growth and can tolerate higher volatility. Your portfolio should emphasize growth-oriented investments with some stability.';
      case 'aggressive':
        return 'You seek maximum growth potential and can tolerate significant volatility. Your portfolio should focus on high-growth investments with minimal income components.';
      default:
        return 'You seek a balance between growth and stability. Your portfolio should include a mix of growth and income investments with moderate volatility.';
    }
  };

  // Calculate completion percentage
  const getCompletionPercentage = (): number => {
    const answeredQuestions = formData.questions.filter((q) => q.answer !== null).length;
    return (answeredQuestions / formData.questions.length) * 100;
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Risk Tolerance Assessment</Title>
        <Description theme={theme}>
          Assess your comfort level with financial risk to help determine your optimal investment
          strategy.
        </Description>
      </Header>

      <form onSubmit={handleSubmit}>
        <FormSection>
          <SectionTitle>Risk Assessment Questionnaire</SectionTitle>
          <ProgressBar theme={theme}>
            <ProgressFill width={getCompletionPercentage()} theme={theme} />
          </ProgressBar>
          <ProgressText>
            {formData.questions.filter((q) => q.answer !== null).length} of{' '}
            {formData.questions.length} questions answered
          </ProgressText>

          <QuestionsList>
            {formData.questions.map((question, index) => (
              <QuestionItem key={question.id} theme={theme}>
                <QuestionNumber theme={theme}>{index + 1}</QuestionNumber>
                <QuestionContent>
                  <QuestionText theme={theme}>{question.question}</QuestionText>
                  <OptionsContainer>
                    {question.options.map((option) => (
                      <OptionItem key={option.value}>
                        <RadioButton
                          type="radio"
                          id={`${question.id}_${option.value}`}
                          name={question.id}
                          checked={question.answer === option.value}
                          onChange={() => handleAnswerChange(question.id, option.value)}
                        />
                        <OptionLabel htmlFor={`${question.id}_${option.value}`}>
                          {option.text}
                        </OptionLabel>
                      </OptionItem>
                    ))}
                  </OptionsContainer>
                </QuestionContent>
              </QuestionItem>
            ))}
          </QuestionsList>
        </FormSection>

        {getCompletionPercentage() === 100 && (
          <FormSection>
            <SectionTitle>Your Risk Profile</SectionTitle>
            <RiskProfileContainer theme={theme}>
              <RiskProfileHeader>
                <RiskProfileTitle theme={theme}>
                  {getRiskProfileDisplayName(formData.riskProfile)}
                </RiskProfileTitle>
                <RiskScore theme={theme}>Overall Score: {formData.score.toFixed(1)} / 5</RiskScore>
              </RiskProfileHeader>

              <RiskProfileDescription theme={theme}>
                {getRiskProfileDescription(formData.riskProfile)}
              </RiskProfileDescription>

              {/* Category-specific scores */}
              <CategoryScores theme={theme}>
                <CategoryScoreTitle>Risk Assessment Breakdown</CategoryScoreTitle>

                <CategoryScore>
                  <CategoryScoreLabel>Risk Tolerance (Psychological):</CategoryScoreLabel>
                  <CategoryScoreValue>
                    {formData.riskToleranceScore?.toFixed(1) || 'N/A'}
                  </CategoryScoreValue>
                  <CategoryScoreBar theme={theme}>
                    <CategoryScoreFill
                      width={((formData.riskToleranceScore || 0) / 5) * 100}
                      theme={theme}
                    />
                  </CategoryScoreBar>
                </CategoryScore>

                <CategoryScore>
                  <CategoryScoreLabel>Risk Capacity (Financial):</CategoryScoreLabel>
                  <CategoryScoreValue>
                    {formData.riskCapacityScore?.toFixed(1) || 'N/A'}
                  </CategoryScoreValue>
                  <CategoryScoreBar theme={theme}>
                    <CategoryScoreFill
                      width={((formData.riskCapacityScore || 0) / 5) * 100}
                      theme={theme}
                    />
                  </CategoryScoreBar>
                </CategoryScore>

                <CategoryScore>
                  <CategoryScoreLabel>Behavioral Tendencies:</CategoryScoreLabel>
                  <CategoryScoreValue>
                    {formData.behavioralScore?.toFixed(1) || 'N/A'}
                  </CategoryScoreValue>
                  <CategoryScoreBar theme={theme}>
                    <CategoryScoreFill
                      width={((formData.behavioralScore || 0) / 5) * 100}
                      theme={theme}
                    />
                  </CategoryScoreBar>
                </CategoryScore>

                <CategoryScore>
                  <CategoryScoreLabel>Investment Knowledge:</CategoryScoreLabel>
                  <CategoryScoreValue>
                    {formData.knowledgeScore?.toFixed(1) || 'N/A'}
                  </CategoryScoreValue>
                  <CategoryScoreBar theme={theme}>
                    <CategoryScoreFill
                      width={((formData.knowledgeScore || 0) / 5) * 100}
                      theme={theme}
                    />
                  </CategoryScoreBar>
                </CategoryScore>
              </CategoryScores>

              {/* Misalignment warning */}
              {formData.hasMisalignment && (
                <MisalignmentWarning theme={theme}>
                  <WarningIcon>⚠️</WarningIcon>
                  <WarningText>{formData.misalignmentNotes}</WarningText>
                </MisalignmentWarning>
              )}

              <RiskMeter theme={theme}>
                <RiskMeterLabel position="left">Conservative</RiskMeterLabel>
                <RiskMeterBar theme={theme}>
                  <RiskMeterIndicator position={(formData.score / 5) * 100} theme={theme} />
                </RiskMeterBar>
                <RiskMeterLabel position="right">Aggressive</RiskMeterLabel>
              </RiskMeter>
            </RiskProfileContainer>
          </FormSection>
        )}

        <FormSection>
          <SectionTitle>Additional Notes</SectionTitle>
          <FormField>
            <Label htmlFor="additionalNotes">Notes about your risk tolerance</Label>
            <Textarea
              id="additionalNotes"
              name="additionalNotes"
              value={formData.additionalNotes}
              onChange={handleNotesChange}
              placeholder="Add any additional notes about your risk tolerance or investment preferences"
            />
          </FormField>
        </FormSection>

        <ButtonContainer>
          {onBack && (
            <BackButton type="button" onClick={onBack} theme={theme}>
              Back
            </BackButton>
          )}

          <SaveIndicator visible={showSaveIndicator}>✓ Saved</SaveIndicator>

          <SubmitButton type="submit" theme={theme} disabled={getCompletionPercentage() < 100}>
            {isSaving ? 'Saving...' : 'Save & Continue'}
          </SubmitButton>
        </ButtonContainer>
      </form>
    </Container>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  padding: 24px;
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const Title = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const Description = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const FormSection = styled.div`
  margin-bottom: 32px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const ProgressBar = styled.div<{ theme: any }>`
  height: 8px;
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 4px;
  margin-bottom: 8px;
  overflow: hidden;
`;

const ProgressFill = styled.div<{ width: number; theme: any }>`
  height: 100%;
  width: ${(props) => props.width}%;
  background-color: ${(props) => props.theme.colors.primary.main};
  transition: width 0.3s ease;
`;

const ProgressText = styled.div`
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 24px;
`;

const QuestionsList = styled.div`
  margin-bottom: 24px;
`;

const QuestionItem = styled.div<{ theme: any }>`
  display: flex;
  margin-bottom: 24px;
  padding: 16px;
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
`;

const QuestionNumber = styled.div<{ theme: any }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  font-weight: bold;
  margin-right: 16px;
  flex-shrink: 0;
`;

const QuestionContent = styled.div`
  flex: 1;
`;

const QuestionText = styled.div<{ theme: any }>`
  font-weight: 500;
  margin-bottom: 16px;
  color: ${(props) => props.theme.colors.text.primary};
`;

const OptionsContainer = styled.div`
  display: flex;
  flex-direction: column;
`;

const OptionItem = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 8px;
`;

const RadioButton = styled.input`
  margin-right: 8px;
`;

const OptionLabel = styled.label`
  font-size: 0.95rem;
`;

const RiskProfileContainer = styled.div<{ theme: any }>`
  padding: 24px;
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  margin-bottom: 24px;
`;

const RiskProfileHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const RiskProfileTitle = styled.div<{ theme: any }>`
  font-size: 1.2rem;
  font-weight: 600;
  color: ${(props) => props.theme.colors.text.primary};
`;

const RiskScore = styled.div<{ theme: any }>`
  font-size: 1rem;
  font-weight: 500;
  color: ${(props) => props.theme.colors.primary.main};
`;

const RiskProfileDescription = styled.p<{ theme: any }>`
  margin: 0 0 24px 0;
  line-height: 1.5;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const RiskMeter = styled.div<{ theme: any }>`
  margin-top: 16px;
`;

const RiskMeterBar = styled.div<{ theme: any }>`
  height: 8px;
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 4px;
  margin: 8px 0;
  position: relative;
`;

const RiskMeterIndicator = styled.div<{ position: number; theme: any }>`
  position: absolute;
  top: -6px;
  left: ${(props) => props.position}%;
  transform: translateX(-50%);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: ${(props) => props.theme.colors.primary.main};
  border: 3px solid ${(props) => props.theme.colors.background.paper};
`;

const RiskMeterLabel = styled.div<{ position: 'left' | 'right' }>`
  font-size: 0.8rem;
  color: #666;
  text-align: ${(props) => props.position};
  ${(props) => (props.position === 'left' ? 'float: left;' : 'float: right;')}
`;

const CategoryScores = styled.div<{ theme: any }>`
  margin: 24px 0;
  padding: 16px;
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
`;

const CategoryScoreTitle = styled.h4`
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 16px 0;
`;

const CategoryScore = styled.div`
  margin-bottom: 12px;
`;

const CategoryScoreLabel = styled.div`
  font-size: 0.9rem;
  margin-bottom: 4px;
`;

const CategoryScoreValue = styled.div`
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 4px;
`;

const CategoryScoreBar = styled.div<{ theme: any }>`
  height: 6px;
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 3px;
  overflow: hidden;
`;

const CategoryScoreFill = styled.div<{ width: number; theme: any }>`
  height: 100%;
  width: ${(props) => props.width}%;
  background-color: ${(props) => props.theme.colors.secondary.main};
`;

const MisalignmentWarning = styled.div<{ theme: any }>`
  display: flex;
  align-items: flex-start;
  padding: 16px;
  margin: 16px 0;
  background-color: ${(props) => props.theme.colors.warning.light};
  border-left: 4px solid ${(props) => props.theme.colors.warning.main};
  border-radius: 4px;
`;

const WarningIcon = styled.div`
  margin-right: 12px;
  font-size: 1.2rem;
`;

const WarningText = styled.div`
  font-size: 0.9rem;
  line-height: 1.5;
`;

const FormField = styled.div`
  margin-bottom: 16px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
`;

const Textarea = styled.textarea`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  min-height: 80px;
  resize: vertical;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
`;

const BackButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.text.secondary};
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const SubmitButton = styled.button<{ theme: any; disabled?: boolean }>`
  background-color: ${(props) =>
    props.disabled
      ? props.theme.colors.action.disabledBackground
      : props.theme.colors.primary.main};
  color: ${(props) =>
    props.disabled ? props.theme.colors.action.disabled : props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 24px;
  font-size: 1rem;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) =>
      props.disabled
        ? props.theme.colors.action.disabledBackground
        : props.theme.colors.primary.dark};
  }
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  color: green;
  margin-right: 16px;
`;

export default RiskTolerance;
