/**
 * Healthcare Cost Projections Component
 *
 * This component visualizes projected healthcare costs over time,
 * including Medicare, supplemental insurance, and out-of-pocket expenses.
 */

import React, { useState, useEffect, useCallback } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { formatCurrency } from '../../../../utils/formatters';
import { HealthcareCostData } from '../../../../types/southDirection';
import {
  calculateHealthcareCostProjections,
  CostProjection,
} from '../../../../utils/healthcareCostCalculator';

interface HealthcareCostProjectionsProps {
  onComplete?: () => void;
  onBack?: () => void;
}

const HealthcareCostProjections: React.FC<HealthcareCostProjectionsProps> = ({
  onComplete,
  onBack,
}) => {
  const { theme } = useTheme();
  const { data, updateData } = useFinancialCompass();

  const [isSaving, setIsSaving] = useState(false);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Initialize form data from context or with defaults
  const personalInfo = data.north?.personalInformation || {};
  const retirementGoals = data.east?.retirementGoals || {};

  // Calculate age from date of birth if available
  const calculateAge = (dateOfBirth: string): string => {
    if (!dateOfBirth) return '45';
    try {
      const birthDate = new Date(dateOfBirth);
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
      return age.toString();
    } catch (error) {
      return '45';
    }
  };

  // Safely access dateOfBirth
  const dateOfBirth =
    typeof personalInfo === 'object' && 'dateOfBirth' in personalInfo && personalInfo.dateOfBirth
      ? (personalInfo.dateOfBirth as string)
      : '';

  const currentAge = calculateAge(dateOfBirth);

  // Safely access retirementAge
  const retirementAge =
    typeof retirementGoals === 'object' &&
    'retirementAge' in retirementGoals &&
    retirementGoals.retirementAge
      ? (retirementGoals.retirementAge as string)
      : '65';

  const [formData, setFormData] = useState<HealthcareCostData>(
    data.south?.healthcareCostProjections || {
      currentAge: currentAge,
      retirementAge: retirementAge,
      lifeExpectancy: '90',
      annualInflation: '3.5',
      currentAnnualHealthcareCost: '5000',
      medicareStartAge: '65',
      medicarePartBPremium: '170.10',
      medicarePartDPremium: '33',
      medicareSupplementPremium: '150',
      outOfPocketCosts: '1500',
      longTermCareStartAge: '80',
      longTermCareCost: '5000',
      longTermCareDuration: '3',
    }
  );

  // Calculate projections
  const [projections, setProjections] = useState<CostProjection[]>([]);
  const [totalLifetimeCost, setTotalLifetimeCost] = useState<number>(0);

  // Calculate healthcare cost projections when form data changes
  useEffect(() => {
    const projectedCosts = calculateHealthcareCostProjections(formData);
    setProjections(projectedCosts);
    // Calculate total lifetime cost from the returned projections
    const total = projectedCosts.reduce((sum, projection) => sum + projection.total, 0);
    setTotalLifetimeCost(total);
  }, [formData]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    // Only allow numbers and decimal points
    if (value === '' || /^[0-9]*\.?[0-9]*$/.test(value)) {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSave();

    if (onComplete) {
      onComplete();
    }
  };

  // Handle saving data
  const handleSave = () => {
    setIsSaving(true);

    // Update data in context
    updateData('south', 'healthcareCostProjections', formData);
    // Save the generated projections and total cost to context as well
    updateData('south', 'healthcareProjectionResults', {
      projections,
      totalLifetimeCost,
    });

    // Show save indicator
    setShowSaveIndicator(true);

    // Hide save indicator after 2 seconds
    setTimeout(() => {
      setIsSaving(false);
      setShowSaveIndicator(false);
    }, 2000);
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Healthcare Cost Projections</Title>
        <Description theme={theme}>
          Visualize your projected healthcare costs over time, including Medicare, supplemental
          insurance, and long-term care.
        </Description>
      </Header>

      <form onSubmit={handleSubmit}>
        <FormSection>
          <SectionTitle>Basic Information</SectionTitle>

          <FormRow>
            <FormField>
              <Label htmlFor="currentAge">Current Age</Label>
              <Input
                type="text"
                id="currentAge"
                name="currentAge"
                value={formData.currentAge}
                onChange={handleInputChange}
                placeholder="45"
              />
            </FormField>

            <FormField>
              <Label htmlFor="retirementAge">Retirement Age</Label>
              <Input
                type="text"
                id="retirementAge"
                name="retirementAge"
                value={formData.retirementAge}
                onChange={handleInputChange}
                placeholder="65"
              />
            </FormField>

            <FormField>
              <Label htmlFor="lifeExpectancy">Life Expectancy</Label>
              <Input
                type="text"
                id="lifeExpectancy"
                name="lifeExpectancy"
                value={formData.lifeExpectancy}
                onChange={handleInputChange}
                placeholder="90"
              />
            </FormField>
          </FormRow>

          <FormRow>
            <FormField>
              <Label htmlFor="annualInflation">Healthcare Inflation (%)</Label>
              <Input
                type="text"
                id="annualInflation"
                name="annualInflation"
                value={formData.annualInflation}
                onChange={handleInputChange}
                placeholder="3.5"
              />
              <FieldHint>Healthcare costs typically rise faster than general inflation</FieldHint>
            </FormField>

            <FormField>
              <Label htmlFor="currentAnnualHealthcareCost">Current Annual Healthcare Cost</Label>
              <InputWithPrefix>
                <Prefix>$</Prefix>
                <Input
                  type="text"
                  id="currentAnnualHealthcareCost"
                  name="currentAnnualHealthcareCost"
                  value={formData.currentAnnualHealthcareCost}
                  onChange={handleInputChange}
                  placeholder="5000"
                />
              </InputWithPrefix>
              <FieldHint>Before Medicare eligibility</FieldHint>
            </FormField>
          </FormRow>
        </FormSection>

        <FormSection>
          <SectionTitle>Medicare & Supplemental Insurance</SectionTitle>

          <FormRow>
            <FormField>
              <Label htmlFor="medicareStartAge">Medicare Start Age</Label>
              <Input
                type="text"
                id="medicareStartAge"
                name="medicareStartAge"
                value={formData.medicareStartAge}
                onChange={handleInputChange}
                placeholder="65"
              />
            </FormField>

            <FormField>
              <Label htmlFor="medicarePartBPremium">Medicare Part B Premium</Label>
              <InputWithPrefix>
                <Prefix>$</Prefix>
                <Input
                  type="text"
                  id="medicarePartBPremium"
                  name="medicarePartBPremium"
                  value={formData.medicarePartBPremium}
                  onChange={handleInputChange}
                  placeholder="170.10"
                />
              </InputWithPrefix>
              <FieldHint>Monthly premium</FieldHint>
            </FormField>
          </FormRow>

          <FormRow>
            <FormField>
              <Label htmlFor="medicarePartDPremium">Medicare Part D Premium</Label>
              <InputWithPrefix>
                <Prefix>$</Prefix>
                <Input
                  type="text"
                  id="medicarePartDPremium"
                  name="medicarePartDPremium"
                  value={formData.medicarePartDPremium}
                  onChange={handleInputChange}
                  placeholder="33"
                />
              </InputWithPrefix>
              <FieldHint>Monthly premium</FieldHint>
            </FormField>

            <FormField>
              <Label htmlFor="medicareSupplementPremium">Medicare Supplement Premium</Label>
              <InputWithPrefix>
                <Prefix>$</Prefix>
                <Input
                  type="text"
                  id="medicareSupplementPremium"
                  name="medicareSupplementPremium"
                  value={formData.medicareSupplementPremium}
                  onChange={handleInputChange}
                  placeholder="150"
                />
              </InputWithPrefix>
              <FieldHint>Monthly premium</FieldHint>
            </FormField>
          </FormRow>

          <FormRow>
            <FormField>
              <Label htmlFor="outOfPocketCosts">Annual Out-of-Pocket Costs</Label>
              <InputWithPrefix>
                <Prefix>$</Prefix>
                <Input
                  type="text"
                  id="outOfPocketCosts"
                  name="outOfPocketCosts"
                  value={formData.outOfPocketCosts}
                  onChange={handleInputChange}
                  placeholder="1500"
                />
              </InputWithPrefix>
              <FieldHint>After Medicare eligibility</FieldHint>
            </FormField>
          </FormRow>
        </FormSection>

        <FormSection>
          <SectionTitle>Long-Term Care</SectionTitle>

          <FormRow>
            <FormField>
              <Label htmlFor="longTermCareStartAge">Long-Term Care Start Age</Label>
              <Input
                type="text"
                id="longTermCareStartAge"
                name="longTermCareStartAge"
                value={formData.longTermCareStartAge}
                onChange={handleInputChange}
                placeholder="80"
              />
            </FormField>

            <FormField>
              <Label htmlFor="longTermCareCost">Long-Term Care Monthly Cost</Label>
              <InputWithPrefix>
                <Prefix>$</Prefix>
                <Input
                  type="text"
                  id="longTermCareCost"
                  name="longTermCareCost"
                  value={formData.longTermCareCost}
                  onChange={handleInputChange}
                  placeholder="5000"
                />
              </InputWithPrefix>
            </FormField>

            <FormField>
              <Label htmlFor="longTermCareDuration">Long-Term Care Duration (Years)</Label>
              <Input
                type="text"
                id="longTermCareDuration"
                name="longTermCareDuration"
                value={formData.longTermCareDuration}
                onChange={handleInputChange}
                placeholder="3"
              />
            </FormField>
          </FormRow>
        </FormSection>

        <FormSection>
          <SectionTitle>Cost Projections</SectionTitle>

          <ProjectionSummary theme={theme}>
            <SummaryItem>
              <SummaryLabel>Total Lifetime Healthcare Cost:</SummaryLabel>
              <SummaryValue>{formatCurrency(totalLifetimeCost.toString())}</SummaryValue>
            </SummaryItem>
            <SummaryItem>
              <SummaryLabel>Average Annual Cost:</SummaryLabel>
              <SummaryValue>
                {formatCurrency((totalLifetimeCost / Math.max(1, projections.length)).toString())}
              </SummaryValue>
            </SummaryItem>
          </ProjectionSummary>

          <VisualizationContainer>
            <VisualizationHeader>
              <VisualizationTitle>Healthcare Cost Visualization</VisualizationTitle>
              <VisualizationLegend>
                <LegendItem color={theme.colors.primary.main}>
                  <LegendColor color={theme.colors.primary.main} />
                  <LegendText>Medicare Part B</LegendText>
                </LegendItem>
                <LegendItem color={theme.colors.secondary.main}>
                  <LegendColor color={theme.colors.secondary.main} />
                  <LegendText>Medicare Part D</LegendText>
                </LegendItem>
                <LegendItem color={theme.colors.info.main}>
                  <LegendColor color={theme.colors.info.main} />
                  <LegendText>Medicare Supplement</LegendText>
                </LegendItem>
                <LegendItem color={theme.colors.warning.main}>
                  <LegendColor color={theme.colors.warning.main} />
                  <LegendText>Out-of-Pocket</LegendText>
                </LegendItem>
                <LegendItem color={theme.colors.error.main}>
                  <LegendColor color={theme.colors.error.main} />
                  <LegendText>Long-Term Care</LegendText>
                </LegendItem>
              </VisualizationLegend>
            </VisualizationHeader>

            <ChartContainer>
              {projections.length > 0 && (
                <StackedBarChart projections={projections} theme={theme} />
              )}
            </ChartContainer>
          </VisualizationContainer>

          <ProjectionTable theme={theme}>
            <TableHeader theme={theme}>
              <TableHeaderCell>Age</TableHeaderCell>
              <TableHeaderCell>Year</TableHeaderCell>
              <TableHeaderCell>Medicare Part B</TableHeaderCell>
              <TableHeaderCell>Medicare Part D</TableHeaderCell>
              <TableHeaderCell>Medicare Supplement</TableHeaderCell>
              <TableHeaderCell>Out-of-Pocket</TableHeaderCell>
              <TableHeaderCell>Long-Term Care</TableHeaderCell>
              <TableHeaderCell>Annual Total</TableHeaderCell>
              <TableHeaderCell>Cumulative Total</TableHeaderCell>
            </TableHeader>
            <TableBody>
              {projections.map((projection, index) => (
                <TableRow key={index} isAlternate={index % 2 === 1} theme={theme}>
                  <TableCell>{projection.age}</TableCell>
                  <TableCell>{projection.year}</TableCell>
                  <TableCell>{formatCurrency(projection.medicarePartB.toString())}</TableCell>
                  <TableCell>{formatCurrency(projection.medicarePartD.toString())}</TableCell>
                  <TableCell>{formatCurrency(projection.medicareSupplement.toString())}</TableCell>
                  <TableCell>{formatCurrency(projection.outOfPocket.toString())}</TableCell>
                  <TableCell>{formatCurrency(projection.longTermCare.toString())}</TableCell>
                  <TableCell>{formatCurrency(projection.total.toString())}</TableCell>
                  <TableCell>{formatCurrency(projection.cumulativeTotal.toString())}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </ProjectionTable>
        </FormSection>

        <ButtonContainer>
          {onBack && (
            <BackButton type="button" onClick={onBack} theme={theme}>
              Back
            </BackButton>
          )}

          <SaveIndicator visible={showSaveIndicator}>✓ Saved</SaveIndicator>

          <SubmitButton type="submit" theme={theme}>
            {isSaving ? 'Saving...' : 'Save & Continue'}
          </SubmitButton>
        </ButtonContainer>
      </form>
    </Container>
  );
};

// Stacked Bar Chart Component
const StackedBarChart: React.FC<{
  projections: CostProjection[];
  theme: any;
}> = ({ projections, theme }) => {
  // Find the maximum total cost for scaling
  const maxTotal = Math.max(...projections.map((p) => p.total));

  // Calculate the chart height based on the number of projections
  const chartHeight = Math.min(600, projections.length * 30);

  return (
    <Chart height={chartHeight}>
      <YAxis>
        {projections.map((projection, index) => (
          <YAxisLabel key={index}>
            {projection.age} ({projection.year})
          </YAxisLabel>
        ))}
      </YAxis>

      <BarsContainer>
        {projections.map((projection, index) => (
          <BarGroup key={index}>
            <BarLabel>{formatCurrency(projection.total.toString())}</BarLabel>
            <Bar>
              {/* Medicare Part B */}
              <BarSegment
                width={(projection.medicarePartB / maxTotal) * 100}
                color={theme.colors.primary.main}
                title={`Medicare Part B: ${formatCurrency(projection.medicarePartB.toString())}`}
              />

              {/* Medicare Part D */}
              <BarSegment
                width={(projection.medicarePartD / maxTotal) * 100}
                color={theme.colors.secondary.main}
                title={`Medicare Part D: ${formatCurrency(projection.medicarePartD.toString())}`}
              />

              {/* Medicare Supplement */}
              <BarSegment
                width={(projection.medicareSupplement / maxTotal) * 100}
                color={theme.colors.info.main}
                title={`Medicare Supplement: ${formatCurrency(projection.medicareSupplement.toString())}`}
              />

              {/* Out-of-Pocket */}
              <BarSegment
                width={(projection.outOfPocket / maxTotal) * 100}
                color={theme.colors.warning.main}
                title={`Out-of-Pocket: ${formatCurrency(projection.outOfPocket.toString())}`}
              />

              {/* Long-Term Care */}
              <BarSegment
                width={(projection.longTermCare / maxTotal) * 100}
                color={theme.colors.error.main}
                title={`Long-Term Care: ${formatCurrency(projection.longTermCare.toString())}`}
              />
            </Bar>
          </BarGroup>
        ))}
      </BarsContainer>
    </Chart>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  padding: 24px;
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const Title = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const Description = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const FormSection = styled.div`
  margin-bottom: 32px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;

const FormField = styled.div`
  margin-bottom: 16px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
`;

const InputWithPrefix = styled.div`
  display: flex;
  align-items: center;
  position: relative;
`;

const Prefix = styled.span`
  position: absolute;
  left: 10px;
  color: #666;
`;

const Input = styled.input`
  width: 100%;
  padding: 10px;
  padding-left: 24px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
`;

const FieldHint = styled.div`
  font-size: 0.8rem;
  color: #666;
  margin-top: 4px;
`;

const ProjectionSummary = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
`;

const SummaryItem = styled.div`
  flex: 1;
  min-width: 200px;
`;

const SummaryLabel = styled.div`
  font-weight: 500;
  margin-bottom: 8px;
`;

const SummaryValue = styled.div`
  font-size: 1.5rem;
  font-weight: bold;
  color: ${(props) => props.theme.colors.primary.main};
`;

const VisualizationContainer = styled.div`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
`;

const VisualizationHeader = styled.div`
  margin-bottom: 16px;
`;

const VisualizationTitle = styled.h4`
  margin: 0 0 16px 0;
  font-size: 1.1rem;
`;

const VisualizationLegend = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
`;

const LegendItem = styled.div<{ color: string }>`
  display: flex;
  align-items: center;
`;

const LegendColor = styled.div<{ color: string }>`
  width: 16px;
  height: 16px;
  background-color: ${(props) => props.color};
  margin-right: 8px;
  border-radius: 2px;
`;

const LegendText = styled.div`
  font-size: 0.9rem;
`;

const ChartContainer = styled.div`
  overflow-x: auto;
  margin-bottom: 16px;
`;

const Chart = styled.div<{ height: number }>`
  display: flex;
  height: ${(props) => props.height}px;
  min-width: 600px;
`;

const YAxis = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-right: 16px;
  min-width: 100px;
`;

const YAxisLabel = styled.div`
  font-size: 0.8rem;
  text-align: right;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
`;

const BarsContainer = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
`;

const BarGroup = styled.div`
  display: flex;
  align-items: center;
  height: 24px;
  margin-bottom: 6px;
`;

const BarLabel = styled.div`
  font-size: 0.8rem;
  min-width: 80px;
  text-align: right;
  padding-right: 8px;
`;

const Bar = styled.div`
  flex: 1;
  height: 24px;
  display: flex;
  border-radius: 4px;
  overflow: hidden;
`;

const BarSegment = styled.div<{ width: number; color: string }>`
  width: ${(props) => props.width}%;
  background-color: ${(props) => props.color};
  height: 100%;
`;

const ProjectionTable = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 24px;
  overflow-x: auto;
`;

const TableHeader = styled.div<{ theme: any }>`
  display: grid;
  grid-template-columns: repeat(9, minmax(100px, 1fr));
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  font-weight: bold;
`;

const TableHeaderCell = styled.div`
  padding: 12px 16px;
  text-align: right;

  &:first-child {
    text-align: left;
  }
`;

const TableBody = styled.div``;

const TableRow = styled.div<{ isAlternate: boolean; theme: any }>`
  display: grid;
  grid-template-columns: repeat(9, minmax(100px, 1fr));
  background-color: ${(props) =>
    props.isAlternate ? props.theme.colors.background.default : 'transparent'};

  &:hover {
    background-color: ${(props) => props.theme.colors.action.hover};
  }
`;

const TableCell = styled.div`
  padding: 12px 16px;
  text-align: right;

  &:first-child {
    text-align: left;
  }
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
`;

const BackButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.text.secondary};
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const SubmitButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 24px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  color: green;
  margin-right: 16px;
`;

export default HealthcareCostProjections;
