/**
 * Healthcare Planning Component
 *
 * This component helps users plan for healthcare costs in retirement.
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { formatCurrency } from '../../../../utils/formatters';
import { HealthcarePlanningData } from '../../../../types/southDirection';

interface HealthcarePlanningProps {
  onComplete?: () => void;
  onBack?: () => void;
}

const HealthcarePlanning: React.FC<HealthcarePlanningProps> = ({ onComplete, onBack }) => {
  const { theme } = useTheme();
  const { data, updateData, updateSouthSectionStatus } = useFinancialCompass();

  const [isSaving, setIsSaving] = useState(false);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Initialize form data from context or with defaults
  const defaultHealthcarePlanningData: HealthcarePlanningData = {
    currentAnnualHealthcareExpenses: '',
    expectedRetirementHealthcareExpenses: '',
    hasMedicareSupplementalPlan: false,
    hasHealthSavingsAccount: false,
    hsaBalance: '',
    hsaAnnualContribution: '',
    hasLongTermCareInsurance: false,
    longTermCareMonthlyBenefit: '',
    longTermCarePremium: '',
    healthcareNotes: '',
  };

  const [formData, setFormData] = useState<HealthcarePlanningData>(
    data.south?.healthcarePlanning
      ? {
          ...defaultHealthcarePlanningData,
          ...data.south.healthcarePlanning,
        }
      : defaultHealthcarePlanningData
  );

  // Handle form field changes
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target;

    // Handle checkbox separately
    if (type === 'checkbox') {
      const isChecked = (e.target as HTMLInputElement).checked;
      setFormData((prev) => ({
        ...prev,
        [name]: isChecked,
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('HealthcarePlanning: Form submitted');
    handleSave();

    // Mark this section as completed
    updateSouthSectionStatus('healthcare_planning', true, false);

    if (onComplete) {
      console.log('HealthcarePlanning: Calling onComplete');
      onComplete();
    }
  };

  // Handle saving data
  const handleSave = () => {
    setIsSaving(true);

    // Update data in context
    updateData('south', 'healthcarePlanning', formData);

    // Show save indicator
    setShowSaveIndicator(true);

    // Hide save indicator after 2 seconds
    setTimeout(() => {
      setIsSaving(false);
      setShowSaveIndicator(false);
    }, 2000);
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Healthcare Planning</Title>
        <Description theme={theme}>
          Plan for healthcare costs in retirement and ensure adequate coverage.
        </Description>
      </Header>

      <form onSubmit={handleSubmit}>
        <FormSection>
          <SectionTitle>Healthcare Expenses</SectionTitle>
          <FormRow>
            <FormField>
              <Label htmlFor="currentAnnualHealthcareExpenses">
                Current Annual Healthcare Expenses
              </Label>
              <Input
                type="number"
                id="currentAnnualHealthcareExpenses"
                name="currentAnnualHealthcareExpenses"
                value={formData.currentAnnualHealthcareExpenses}
                onChange={handleChange}
                placeholder="e.g., 5000"
              />
              <FieldHint>
                Include insurance premiums, out-of-pocket costs, prescriptions, etc.
              </FieldHint>
            </FormField>

            <FormField>
              <Label htmlFor="expectedRetirementHealthcareExpenses">
                Expected Annual Expenses in Retirement
              </Label>
              <Input
                type="number"
                id="expectedRetirementHealthcareExpenses"
                name="expectedRetirementHealthcareExpenses"
                value={formData.expectedRetirementHealthcareExpenses}
                onChange={handleChange}
                placeholder="e.g., 12000"
              />
              <FieldHint>Estimate your annual healthcare costs in retirement</FieldHint>
            </FormField>
          </FormRow>
        </FormSection>

        <FormSection>
          <SectionTitle>Medicare & Supplemental Coverage</SectionTitle>
          <CheckboxField>
            <Checkbox
              type="checkbox"
              id="hasMedicareSupplementalPlan"
              name="hasMedicareSupplementalPlan"
              checked={formData.hasMedicareSupplementalPlan}
              onChange={handleChange}
            />
            <CheckboxLabel htmlFor="hasMedicareSupplementalPlan">
              I plan to purchase Medicare supplemental coverage
            </CheckboxLabel>
          </CheckboxField>
          <FieldHint>
            Medicare typically covers about 80% of healthcare costs. Supplemental plans can help
            cover the remaining 20%.
          </FieldHint>
        </FormSection>

        <FormSection>
          <SectionTitle>Health Savings Account (HSA)</SectionTitle>
          <CheckboxField>
            <Checkbox
              type="checkbox"
              id="hasHealthSavingsAccount"
              name="hasHealthSavingsAccount"
              checked={formData.hasHealthSavingsAccount}
              onChange={handleChange}
            />
            <CheckboxLabel htmlFor="hasHealthSavingsAccount">
              I have a Health Savings Account (HSA)
            </CheckboxLabel>
          </CheckboxField>

          {formData.hasHealthSavingsAccount && (
            <FormRow>
              <FormField>
                <Label htmlFor="hsaBalance">Current HSA Balance</Label>
                <Input
                  type="number"
                  id="hsaBalance"
                  name="hsaBalance"
                  value={formData.hsaBalance}
                  onChange={handleChange}
                  placeholder="e.g., 10000"
                />
              </FormField>

              <FormField>
                <Label htmlFor="hsaAnnualContribution">Annual HSA Contribution</Label>
                <Input
                  type="number"
                  id="hsaAnnualContribution"
                  name="hsaAnnualContribution"
                  value={formData.hsaAnnualContribution}
                  onChange={handleChange}
                  placeholder="e.g., 3650"
                />
              </FormField>
            </FormRow>
          )}
        </FormSection>

        <FormSection>
          <SectionTitle>Long-Term Care</SectionTitle>
          <CheckboxField>
            <Checkbox
              type="checkbox"
              id="hasLongTermCareInsurance"
              name="hasLongTermCareInsurance"
              checked={formData.hasLongTermCareInsurance}
              onChange={handleChange}
            />
            <CheckboxLabel htmlFor="hasLongTermCareInsurance">
              I have Long-Term Care insurance
            </CheckboxLabel>
          </CheckboxField>

          {formData.hasLongTermCareInsurance && (
            <FormRow>
              <FormField>
                <Label htmlFor="longTermCareMonthlyBenefit">Monthly Benefit</Label>
                <Input
                  type="number"
                  id="longTermCareMonthlyBenefit"
                  name="longTermCareMonthlyBenefit"
                  value={formData.longTermCareMonthlyBenefit}
                  onChange={handleChange}
                  placeholder="e.g., 5000"
                />
              </FormField>

              <FormField>
                <Label htmlFor="longTermCarePremium">Annual Premium</Label>
                <Input
                  type="number"
                  id="longTermCarePremium"
                  name="longTermCarePremium"
                  value={formData.longTermCarePremium}
                  onChange={handleChange}
                  placeholder="e.g., 2500"
                />
              </FormField>
            </FormRow>
          )}

          <InfoBox theme={theme}>
            <InfoIcon>ℹ️</InfoIcon>
            <InfoText>
              Long-term care costs can be substantial in retirement. The median annual cost for a
              private room in a nursing home is over $100,000, and home health aide services average
              around $50,000 per year.
            </InfoText>
          </InfoBox>
        </FormSection>

        <FormSection>
          <SectionTitle>Additional Notes</SectionTitle>
          <FormField>
            <Label htmlFor="healthcareNotes">Notes about healthcare planning</Label>
            <Textarea
              id="healthcareNotes"
              name="healthcareNotes"
              value={formData.healthcareNotes}
              onChange={handleChange}
              placeholder="Add any additional notes about your healthcare planning"
            />
          </FormField>
        </FormSection>

        <ButtonContainer>
          {onBack && (
            <BackButton type="button" onClick={onBack} theme={theme}>
              Back
            </BackButton>
          )}

          <SaveIndicator visible={showSaveIndicator}>✓ Saved</SaveIndicator>

          <SubmitButton type="submit" theme={theme}>
            {isSaving ? 'Saving...' : 'Save & Continue'}
          </SubmitButton>
        </ButtonContainer>
      </form>
    </Container>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  padding: 24px;
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const Title = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const Description = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const FormSection = styled.div`
  margin-bottom: 32px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;

const FormField = styled.div`
  margin-bottom: 16px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
`;

const Input = styled.input`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
`;

const Textarea = styled.textarea`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  min-height: 80px;
  resize: vertical;
`;

const FieldHint = styled.div`
  font-size: 0.8rem;
  color: #666;
  margin-top: 4px;
`;

const CheckboxField = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 16px;
`;

const Checkbox = styled.input`
  margin-right: 8px;
`;

const CheckboxLabel = styled.label`
  font-size: 1rem;
`;

const InfoBox = styled.div<{ theme: any }>`
  display: flex;
  align-items: flex-start;
  padding: 16px;
  background-color: ${(props) => props.theme.colors.info.light};
  border-radius: 4px;
  margin-top: 16px;
`;

const InfoIcon = styled.div`
  margin-right: 12px;
  font-size: 1.2rem;
`;

const InfoText = styled.p`
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.5;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
`;

const BackButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.text.secondary};
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const SubmitButton = styled.button<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 24px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  color: green;
  margin-right: 16px;
`;

export default HealthcarePlanning;
