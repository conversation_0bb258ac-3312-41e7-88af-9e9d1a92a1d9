import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Divider,
  TextField,
  Button,
  MenuItem,
} from '@mui/material';

function getCoverageRecommendation(type: string, amount: number) {
  if (type === 'life') {
    if (amount < 100000)
      return 'Consider increasing your life insurance coverage for adequate protection.';
    if (amount > 1000000) return 'You may be over-insured for life insurance.';
    return 'Your life insurance coverage is within a typical range.';
  }
  if (type === 'health') {
    if (amount < 5000) return 'Consider a higher deductible or more comprehensive health plan.';
    return 'Your health insurance coverage is typical.';
  }
  if (type === 'disability') {
    if (amount < 20000) return 'Consider increasing your disability coverage.';
    return 'Your disability coverage is typical.';
  }
  if (type === 'auto') {
    if (amount < 25000) return 'Consider increasing your auto insurance liability coverage.';
    return 'Your auto insurance coverage is typical.';
  }
  if (type === 'home') {
    if (amount < 100000) return 'Consider increasing your home insurance coverage.';
    return 'Your home insurance coverage is typical.';
  }
  return '';
}

const InsurancePlanningPanel: React.FC = () => {
  const [coverage, setCoverage] = useState('');
  const [policyType, setPolicyType] = useState('life');
  const [errors, setErrors] = useState<{ coverage?: string; policyType?: string }>({});
  const [submitted, setSubmitted] = useState(false);
  const [recommendation, setRecommendation] = useState<string | null>(null);

  const validate = () => {
    const newErrors: { coverage?: string; policyType?: string } = {};
    if (!coverage || isNaN(Number(coverage)) || Number(coverage) < 1000) {
      newErrors.coverage = 'Coverage must be at least $1,000';
    }
    if (!policyType) {
      newErrors.policyType = 'Policy type is required';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitted(true);
    if (validate()) {
      setRecommendation(getCoverageRecommendation(policyType, Number(coverage)));
    }
  };

  return (
    <Card variant="outlined">
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Insurance Planning
        </Typography>
        <Divider sx={{ mb: 2 }} />
        <Box mb={2}>
          <Typography variant="body2" color="textSecondary">
            Enter your coverage amount and policy type to get a recommendation.
          </Typography>
        </Box>
        <form onSubmit={handleSubmit} noValidate>
          <Box display="flex" gap={2} mb={2}>
            <TextField
              label="Coverage Amount ($)"
              value={coverage}
              onChange={(e) => setCoverage(e.target.value)}
              error={!!errors.coverage && submitted}
              helperText={submitted && errors.coverage}
              type="number"
              inputProps={{ min: 1000 }}
            />
            <TextField
              select
              label="Policy Type"
              value={policyType}
              onChange={(e) => setPolicyType(e.target.value)}
              error={!!errors.policyType && submitted}
              helperText={submitted && errors.policyType}
            >
              <MenuItem value="life">Life</MenuItem>
              <MenuItem value="health">Health</MenuItem>
              <MenuItem value="disability">Disability</MenuItem>
              <MenuItem value="auto">Auto</MenuItem>
              <MenuItem value="home">Home</MenuItem>
            </TextField>
            <Button type="submit" variant="contained" color="primary">
              Analyze
            </Button>
          </Box>
        </form>
        {recommendation && (
          <Box mt={3}>
            <Typography variant="subtitle1" gutterBottom>
              Recommendation:
            </Typography>
            <Typography variant="body2" color="textSecondary">
              {recommendation}
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default InsurancePlanningPanel;
