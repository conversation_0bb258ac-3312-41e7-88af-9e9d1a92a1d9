/**
 * Family Information Component
 *
 * This component collects and displays the user's family information as part of the
 * North direction of the Financial Compass ("Where You Are").
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { useGuidedJourney } from '../../../GuidedJourney/context/GuidedJourneyContext';
import { FamilyInformationData, FamilyMember } from '../../../../types/northDirection';

interface FamilyInformationProps {
  onComplete?: () => void;
  onSave?: (data: FamilyInformationData) => void;
  onBack?: () => void;
}

// Styled Components
const FormContainer = styled.div`
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: 8px;
  padding: 24px;
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.12),
    0 1px 2px rgba(0, 0, 0, 0.24);
  max-width: 800px;
  margin: 0 auto;
`;

const FormTitle = styled.h2`
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: 24px;
  font-weight: 500;
`;

const FormDescription = styled.p`
  color: ${({ theme }) => theme.colors.text.secondary};
  margin-bottom: 32px;
`;

const FormSection = styled.div`
  margin-bottom: 32px;
`;

const SectionTitle = styled.h3`
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: 16px;
  font-weight: 500;
  font-size: 1.1rem;
`;

const FormRow = styled.div`
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8px;
`;

const FormField = styled.div`
  flex: 1 0 250px;
  padding: 0 8px;
  margin-bottom: 16px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 4px;
  color: ${({ theme }) => theme.colors.text.primary};
  font-weight: 500;
`;

const Input = styled.input`
  width: 100%;
  padding: 12px;
  border: 1px solid ${({ theme }) => theme.colors.divider};
  border-radius: 4px;
  background-color: ${({ theme }) => theme.colors.background.default};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 1rem;
  transition: border-color 0.2s ease-in-out;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary.main};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light}40;
  }
`;

const Select = styled.select`
  width: 100%;
  padding: 12px;
  border: 1px solid ${({ theme }) => theme.colors.divider};
  border-radius: 4px;
  background-color: ${({ theme }) => theme.colors.background.default};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 1rem;
  transition: border-color 0.2s ease-in-out;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary.main};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light}40;
  }
`;

const Checkbox = styled.input`
  margin-right: 8px;
`;

const CheckboxLabel = styled.label`
  display: flex;
  align-items: center;
  color: ${({ theme }) => theme.colors.text.primary};
  cursor: pointer;
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: 12px;
  border: 1px solid ${({ theme }) => theme.colors.divider};
  border-radius: 4px;
  background-color: ${({ theme }) => theme.colors.background.default};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 1rem;
  min-height: 100px;
  resize: vertical;
  transition: border-color 0.2s ease-in-out;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary.main};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light}40;
  }
`;

const FamilyMemberCard = styled.div`
  background-color: ${({ theme }) => theme.colors.background.elevated};
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
  position: relative;
`;

const RemoveButton = styled.button`
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: transparent;
  border: none;
  color: ${({ theme }) => theme.colors.error.main};
  cursor: pointer;
  font-size: 1.2rem;
  padding: 4px;

  &:hover {
    color: ${({ theme }) => theme.colors.error.dark};
  }

  &:focus {
    outline: none;
  }
`;

const AddButton = styled.button`
  background-color: ${({ theme }) => theme.colors.background.elevated};
  border: 1px dashed ${({ theme }) => theme.colors.divider};
  border-radius: 4px;
  padding: 16px;
  width: 100%;
  text-align: center;
  color: ${({ theme }) => theme.colors.primary.main};
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease-in-out;

  &:hover {
    background-color: ${({ theme }) => theme.colors.primary.main}10;
    border-color: ${({ theme }) => theme.colors.primary.main};
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light}40;
  }
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-top: 32px;
`;

const Button = styled.button`
  padding: 12px 24px;
  background-color: ${({ theme }) => theme.colors.primary.main};
  color: ${({ theme }) => theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;

  &:hover {
    background-color: ${({ theme }) => theme.colors.primary.dark};
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light}40;
  }
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${({ visible }) => (visible ? 1 : 0)};
  transition: opacity 0.3s ease-in-out;
  margin-right: 16px;
  color: ${({ theme }) => theme.colors.success.main};
  display: flex;
  align-items: center;
`;

/**
 * Family Information Component
 */
const FamilyInformation: React.FC<FamilyInformationProps> = ({ onComplete, onSave }) => {
  const { theme } = useTheme();
  const { data, updateData } = useFinancialCompass();
  const { askQuestion } = useGuidedJourney();

  // Initialize form data from context or with defaults
  const [formData, setFormData] = useState<FamilyInformationData>(
    (data.north?.familyInformation as FamilyInformationData) || {
      maritalStatus: 'single',
      familyMembers: [],
    }
  );

  const [isSaving, setIsSaving] = useState(false);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Auto-save effect
  useEffect(() => {
    const saveTimer = setTimeout(() => {
      handleSave();
    }, 1000);

    return () => clearTimeout(saveTimer);
  }, [formData]);

  // Handle input changes for main form
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData({
      ...formData,
      [name]: checked,
    });
  };

  // Handle family member input changes
  const handleFamilyMemberChange = (id: string, field: keyof FamilyMember, value: any) => {
    setFormData({
      ...formData,
      familyMembers: formData.familyMembers.map((member) =>
        member.id === id ? { ...member, [field]: value } : member
      ),
    });
  };

  // Handle family member checkbox changes
  const handleFamilyMemberCheckboxChange = (
    id: string,
    field: keyof FamilyMember,
    checked: boolean
  ) => {
    setFormData({
      ...formData,
      familyMembers: formData.familyMembers.map((member) =>
        member.id === id ? { ...member, [field]: checked } : member
      ),
    });
  };

  // Add a new family member
  const handleAddFamilyMember = () => {
    const newMember: FamilyMember = {
      id: `member-${Date.now()}`,
      relationship: '',
      firstName: '',
      lastName: '',
      dateOfBirth: '',
      isDependent: false,
      occupation: '',
      notes: '',
    };

    setFormData({
      ...formData,
      familyMembers: [...formData.familyMembers, newMember],
    });
  };

  // Remove a family member
  const handleRemoveFamilyMember = (id: string) => {
    setFormData({
      ...formData,
      familyMembers: formData.familyMembers.filter((member) => member.id !== id),
    });
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSave();

    if (onComplete) {
      onComplete();
    }
  };

  // Handle saving data
  const handleSave = () => {
    setIsSaving(true);

    // Update data in context
    updateData('north', 'familyInformation', formData);

    // Call onSave prop if provided
    if (onSave) {
      onSave(formData);
    }

    // Show save indicator
    setShowSaveIndicator(true);

    // Hide save indicator after 2 seconds
    setTimeout(() => {
      setIsSaving(false);
      setShowSaveIndicator(false);
    }, 2000);
  };

  // Handle asking for help
  const handleAskHelp = async (question: string) => {
    const answer = await askQuestion(question);
    // In a real implementation, you would display the answer to the user
    console.log(answer);
  };

  // Conditional rendering for spouse information
  const showSpouseInfo = ['married', 'domestic_partnership'].includes(formData.maritalStatus);

  return (
    <FormContainer theme={theme}>
      <FormTitle theme={theme}>Family Information</FormTitle>
      <FormDescription theme={theme}>
        Please provide information about your family members to help us understand your household
        situation. This information will be used to personalize your financial compass journey.
      </FormDescription>

      <form onSubmit={handleSubmit}>
        <FormSection>
          <SectionTitle>Marital Status</SectionTitle>
          <FormRow>
            <FormField>
              <Label htmlFor="maritalStatus">Marital Status</Label>
              <Select
                id="maritalStatus"
                name="maritalStatus"
                value={formData.maritalStatus}
                onChange={handleChange}
                required
              >
                <option value="single">Single</option>
                <option value="married">Married</option>
                <option value="divorced">Divorced</option>
                <option value="widowed">Widowed</option>
                <option value="separated">Separated</option>
                <option value="domestic_partnership">Domestic Partnership</option>
              </Select>
            </FormField>
          </FormRow>
        </FormSection>

        {showSpouseInfo && (
          <FormSection>
            <SectionTitle>Spouse/Partner Information</SectionTitle>
            <FormRow>
              <FormField>
                <Label htmlFor="spouseFirstName">First Name</Label>
                <Input
                  type="text"
                  id="spouseFirstName"
                  name="spouseFirstName"
                  value={formData.spouseFirstName || ''}
                  onChange={handleChange}
                />
              </FormField>

              <FormField>
                <Label htmlFor="spouseLastName">Last Name</Label>
                <Input
                  type="text"
                  id="spouseLastName"
                  name="spouseLastName"
                  value={formData.spouseLastName || ''}
                  onChange={handleChange}
                />
              </FormField>

              <FormField>
                <Label htmlFor="spouseDateOfBirth">Date of Birth</Label>
                <Input
                  type="date"
                  id="spouseDateOfBirth"
                  name="spouseDateOfBirth"
                  value={formData.spouseDateOfBirth || ''}
                  onChange={handleChange}
                />
              </FormField>

              <FormField>
                <Label htmlFor="spouseOccupation">Occupation</Label>
                <Input
                  type="text"
                  id="spouseOccupation"
                  name="spouseOccupation"
                  value={formData.spouseOccupation || ''}
                  onChange={handleChange}
                />
              </FormField>
            </FormRow>
          </FormSection>
        )}

        <FormSection>
          <SectionTitle>Family Members</SectionTitle>

          {formData.familyMembers.map((member) => (
            <FamilyMemberCard key={member.id}>
              <RemoveButton
                type="button"
                onClick={() => handleRemoveFamilyMember(member.id)}
                aria-label="Remove family member"
              >
                ×
              </RemoveButton>

              <FormRow>
                <FormField>
                  <Label htmlFor={`relationship-${member.id}`}>Relationship</Label>
                  <Select
                    id={`relationship-${member.id}`}
                    value={member.relationship}
                    onChange={(e) =>
                      handleFamilyMemberChange(member.id, 'relationship', e.target.value)
                    }
                    required
                  >
                    <option value="">Select Relationship</option>
                    <option value="child">Child</option>
                    <option value="parent">Parent</option>
                    <option value="sibling">Sibling</option>
                    <option value="grandparent">Grandparent</option>
                    <option value="grandchild">Grandchild</option>
                    <option value="other">Other</option>
                  </Select>
                </FormField>

                <FormField>
                  <Label htmlFor={`firstName-${member.id}`}>First Name</Label>
                  <Input
                    type="text"
                    id={`firstName-${member.id}`}
                    value={member.firstName}
                    onChange={(e) =>
                      handleFamilyMemberChange(member.id, 'firstName', e.target.value)
                    }
                    required
                  />
                </FormField>

                <FormField>
                  <Label htmlFor={`lastName-${member.id}`}>Last Name</Label>
                  <Input
                    type="text"
                    id={`lastName-${member.id}`}
                    value={member.lastName}
                    onChange={(e) =>
                      handleFamilyMemberChange(member.id, 'lastName', e.target.value)
                    }
                    required
                  />
                </FormField>

                <FormField>
                  <Label htmlFor={`dateOfBirth-${member.id}`}>Date of Birth</Label>
                  <Input
                    type="date"
                    id={`dateOfBirth-${member.id}`}
                    value={member.dateOfBirth}
                    onChange={(e) =>
                      handleFamilyMemberChange(member.id, 'dateOfBirth', e.target.value)
                    }
                    required
                  />
                </FormField>
              </FormRow>

              <FormRow>
                <FormField>
                  <CheckboxLabel>
                    <Checkbox
                      type="checkbox"
                      checked={member.isDependent}
                      onChange={(e) =>
                        handleFamilyMemberCheckboxChange(member.id, 'isDependent', e.target.checked)
                      }
                    />
                    Dependent
                  </CheckboxLabel>
                </FormField>

                <FormField>
                  <Label htmlFor={`occupation-${member.id}`}>Occupation (if applicable)</Label>
                  <Input
                    type="text"
                    id={`occupation-${member.id}`}
                    value={member.occupation || ''}
                    onChange={(e) =>
                      handleFamilyMemberChange(member.id, 'occupation', e.target.value)
                    }
                  />
                </FormField>
              </FormRow>

              <FormRow>
                <FormField>
                  <Label htmlFor={`notes-${member.id}`}>Notes</Label>
                  <TextArea
                    id={`notes-${member.id}`}
                    value={member.notes || ''}
                    onChange={(e) => handleFamilyMemberChange(member.id, 'notes', e.target.value)}
                    placeholder="Any additional information about this family member..."
                  />
                </FormField>
              </FormRow>
            </FamilyMemberCard>
          ))}

          <AddButton type="button" onClick={handleAddFamilyMember}>
            + Add Family Member
          </AddButton>
        </FormSection>

        <ButtonContainer theme={theme}>
          <SaveIndicator visible={showSaveIndicator} theme={theme}>
            ✓ Saved
          </SaveIndicator>
          <Button type="submit" theme={theme}>
            {isSaving ? 'Saving...' : 'Save and Continue'}
          </Button>
        </ButtonContainer>
      </form>
    </FormContainer>
  );
};

export default FamilyInformation;
