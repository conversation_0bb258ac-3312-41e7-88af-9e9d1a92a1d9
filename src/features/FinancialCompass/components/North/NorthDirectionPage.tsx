import React from 'react';
import NorthSummarySection from './NorthSummarySection';
import DataVisualizationPanel from '../shared/DataVisualizationPanel';
import CrossFormValidationBanner from '../shared/CrossFormValidationBanner';
import { validateCrossForm } from '../../../utils/crossFormValidation';

const mockCrossFormData = {
  netWorth: 50000,
  insuranceCoverage: 100000, // triggers error
  retirementSavings: 120000,
  annualIncome: 10000, // triggers error
};

const crossFormResult = validateCrossForm(mockCrossFormData);

const NorthDirectionPage: React.FC = () => {
  return (
    <Box>
      <CrossFormValidationBanner errors={crossFormResult.errors} />
      <NorthSummarySection />
      <Box mt={3}>
        <DataVisualizationPanel />
      </Box>
    </Box>
  );
};

export default NorthDirectionPage;
