import React, { useMemo } from 'react';
import {
  Box,
  Grid,
  Typography,
  Card,
  CardContent,
  Divider,
  Chip,
  LinearProgress,
  Paper,
  useTheme,
} from '@mui/material';
import {
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  <PERSON>Chart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { formatCurrency, formatPercentage } from '../../../../utils/formatters';
import {
  calculateDetailedFinancialHealthScore,
  FinancialHealthScoreResult,
  FinancialHealthCategory,
} from '../../../../utils/financialHealthCalculator';

interface EnhancedSummarySectionProps {
  income: number;
  expenses: number;
  assets: number;
  liabilities: number;
  emergencyFund: number;
  monthlyDebtPayments: number;
  housingCost: number;
  previousIncome?: number;
  previousExpenses?: number;
  expenseBreakdown?: Array<{
    category: string;
    amount: number;
  }>;
}

const COLORS = [
  '#4CAF50',
  '#2196F3',
  '#9C27B0',
  '#FF9800',
  '#E91E63',
  '#009688',
  '#FF5722',
  '#795548',
];

const EnhancedSummarySection: React.FC<EnhancedSummarySectionProps> = ({
  income,
  expenses,
  assets,
  liabilities,
  emergencyFund,
  monthlyDebtPayments,
  housingCost,
  previousIncome = 0,
  previousExpenses = 0,
  expenseBreakdown = [],
}) => {
  const theme = useTheme();

  // Calculate key metrics
  const netCashFlow = income - expenses;
  const netWorth = assets - liabilities;
  const expenseToIncomeRatio = income > 0 ? (expenses / income) * 100 : 0;
  const savingsRate = income > 0 ? ((netCashFlow > 0 ? netCashFlow : 0) / income) * 100 : 0;
  const emergencyFundMonths = expenses > 0 ? emergencyFund / expenses : 0;

  // Calculate financial health score
  const financialHealth = useMemo(() => {
    return calculateDetailedFinancialHealthScore([
      {
        id: 'cash_flow',
        name: 'Cash Flow',
        score: 0, // Will be calculated
        weight: 0.2,
        weightedScore: 0,
        metrics: [],
      },
      {
        id: 'debt',
        name: 'Debt Management',
        score: 0, // Will be calculated
        weight: 0.2,
        weightedScore: 0,
        metrics: [],
      },
      {
        id: 'emergency',
        name: 'Emergency Fund',
        score: 0, // Will be calculated
        weight: 0.15,
        weightedScore: 0,
        metrics: [],
      },
      {
        id: 'net_worth',
        name: 'Net Worth',
        score: 0, // Will be calculated
        weight: 0.15,
        weightedScore: 0,
        metrics: [],
      },
      {
        id: 'retirement',
        name: 'Retirement',
        score: 0, // Will be calculated
        weight: 0.15,
        weightedScore: 0,
        metrics: [],
      },
      {
        id: 'protection',
        name: 'Protection',
        score: 0, // Will be calculated
        weight: 0.1,
        weightedScore: 0,
        metrics: [],
      },
      {
        id: 'estate',
        name: 'Estate Planning',
        score: 0, // Will be calculated
        weight: 0.05,
        weightedScore: 0,
        metrics: [],
      },
    ]);
  }, [income, expenses, assets, liabilities, emergencyFund, monthlyDebtPayments, housingCost]);

  // Prepare data for charts
  const incomeChartData = [
    { name: 'Primary Income', value: income },
    { name: 'Other Income', value: income * 0.15 }, // Placeholder for other income sources
  ].filter((item) => item.value > 0);

  const expenseChartData =
    expenseBreakdown.length > 0
      ? [...expenseBreakdown].sort((a, b) => b.amount - a.amount).slice(0, 6)
      : [
          { category: 'Housing', amount: expenses * 0.35 },
          { category: 'Food', amount: expenses * 0.15 },
          { category: 'Transportation', amount: expenses * 0.15 },
          { category: 'Utilities', amount: expenses * 0.1 },
          { category: 'Entertainment', amount: expenses * 0.1 },
          { category: 'Other', amount: expenses * 0.15 },
        ].filter((item) => item.amount > 0);

  const cashFlowData = [
    { name: 'Income', amount: income, fill: '#4CAF50' },
    { name: 'Expenses', amount: expenses, fill: '#F44336' },
    {
      name: netCashFlow >= 0 ? 'Savings' : 'Deficit',
      amount: Math.abs(netCashFlow),
      fill: netCashFlow >= 0 ? '#2196F3' : '#FF9800',
    },
  ];

  // Calculate trends
  const incomeTrend = income > 0 ? ((income - previousIncome) / previousIncome) * 100 : 0;
  const expenseTrend =
    previousExpenses > 0 ? ((expenses - previousExpenses) / previousExpenses) * 100 : 0;

  return (
    <Box sx={{ p: 3 }}>
      <Grid container spacing={3}>
        {/* Financial Overview Card */}
        <Grid item xs={12} md={8}>
          <Card elevation={3}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Financial Overview
              </Typography>
              <Divider sx={{ mb: 3 }} />

              <Grid container spacing={3}>
                {/* Income vs Expenses */}
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Income vs Expenses
                  </Typography>
                  <ResponsiveContainer width="100%" height={200}>
                    <BarChart data={cashFlowData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis
                        tickFormatter={(value) => `$${value >= 1000 ? `${value / 1000}k` : value}`}
                      />
                      <Tooltip formatter={(value: number) => [formatCurrency(value), 'Amount']} />
                      <Bar dataKey="amount">
                        {cashFlowData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.fill} />
                        ))}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                </Grid>

                {/* Income Sources */}
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Income Sources
                  </Typography>
                  <ResponsiveContainer width="100%" height={200}>
                    <PieChart>
                      <Pie
                        data={incomeChartData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        nameKey="name"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {incomeChartData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip
                        formatter={(value: number) => [formatCurrency(value), 'Monthly Amount']}
                      />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </Grid>

                {/* Key Metrics */}
                <Grid item xs={12}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6} md={4}>
                      <Paper sx={{ p: 2, textAlign: 'center' }}>
                        <Typography variant="body2" color="textSecondary" gutterBottom>
                          Monthly Income
                        </Typography>
                        <Box display="flex" alignItems="center" justifyContent="center">
                          <Typography variant="h6" sx={{ mr: 1 }}>
                            {formatCurrency(income)}
                          </Typography>
                          {incomeTrend !== 0 && (
                            <Chip
                              size="small"
                              icon={incomeTrend > 0 ? <TrendingUpIcon /> : <TrendingDownIcon />}
                              label={`${Math.abs(incomeTrend).toFixed(1)}%`}
                              color={incomeTrend > 0 ? 'success' : 'error'}
                              variant="outlined"
                            />
                          )}
                        </Box>
                      </Paper>
                    </Grid>

                    <Grid item xs={12} sm={6} md={4}>
                      <Paper sx={{ p: 2, textAlign: 'center' }}>
                        <Typography variant="body2" color="textSecondary" gutterBottom>
                          Monthly Expenses
                        </Typography>
                        <Box display="flex" alignItems="center" justifyContent="center">
                          <Typography variant="h6" sx={{ mr: 1 }}>
                            {formatCurrency(expenses)}
                          </Typography>
                          {expenseTrend !== 0 && (
                            <Chip
                              size="small"
                              icon={expenseTrend < 0 ? <TrendingDownIcon /> : <TrendingUpIcon />}
                              label={`${Math.abs(expenseTrend).toFixed(1)}%`}
                              color={expenseTrend < 0 ? 'success' : 'error'}
                              variant="outlined"
                            />
                          )}
                        </Box>
                      </Paper>
                    </Grid>

                    <Grid item xs={12} sm={6} md={4}>
                      <Paper
                        sx={{
                          p: 2,
                          textAlign: 'center',
                          backgroundColor: netCashFlow >= 0 ? 'success.light' : 'error.light',
                          color: 'common.white',
                        }}
                      >
                        <Typography variant="body2" gutterBottom>
                          {netCashFlow >= 0 ? 'Monthly Savings' : 'Monthly Deficit'}
                        </Typography>
                        <Typography variant="h6">
                          {formatCurrency(Math.abs(netCashFlow))}
                        </Typography>
                        <Typography variant="caption" display="block">
                          {savingsRate.toFixed(1)}% of income
                        </Typography>
                      </Paper>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Financial Health Card */}
        <Grid item xs={12} md={4}>
          <Card elevation={3}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Financial Health
              </Typography>
              <Divider sx={{ mb: 3 }} />

              <Box textAlign="center" mb={3}>
                <Box
                  sx={{
                    width: 120,
                    height: 120,
                    margin: '0 auto',
                    position: 'relative',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: '50%',
                    background: `conic-gradient(
                      #4CAF50 0% ${financialHealth.overallScore}%, 
                      #e0e0e0 ${financialHealth.overallScore}% 100%
                    )`,
                  }}
                >
                  <Box
                    sx={{
                      width: 100,
                      height: 100,
                      background: theme.palette.background.paper,
                      borderRadius: '50%',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Typography variant="h5" fontWeight="bold">
                      {financialHealth.overallScore}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      / 100
                    </Typography>
                  </Box>
                </Box>

                <Typography variant="subtitle1" mt={2}>
                  {financialHealth.overallScore >= 80
                    ? 'Excellent'
                    : financialHealth.overallScore >= 60
                      ? 'Good'
                      : financialHealth.overallScore >= 40
                        ? 'Fair'
                        : 'Needs Improvement'}
                </Typography>
              </Box>

              <Box mb={3}>
                {financialHealth.categories.map((category: FinancialHealthCategory) => (
                  <Box key={category.id} mb={2}>
                    <Box display="flex" justifyContent="space-between" mb={0.5}>
                      <Typography variant="body2">{category.name}</Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {category.score}/100
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={category.score}
                      color={
                        category.score >= 80
                          ? 'success'
                          : category.score >= 60
                            ? 'primary'
                            : category.score >= 40
                              ? 'warning'
                              : 'error'
                      }
                    />
                  </Box>
                ))}
              </Box>

              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Key Recommendations:
                </Typography>
                <Box component="ul" sx={{ pl: 2, m: 0, '& li': { mb: 1 } }}>
                  {financialHealth.recommendations.slice(0, 3).map((rec, index) => (
                    <Typography key={index} component="li" variant="body2">
                      {rec}
                    </Typography>
                  ))}
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Expense Breakdown Card */}
        <Grid item xs={12}>
          <Card elevation={3}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Expense Breakdown
              </Typography>
              <Divider sx={{ mb: 3 }} />

              <Grid container spacing={3}>
                <Grid item xs={12} md={7}>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart
                      data={expenseChartData}
                      layout="vertical"
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        type="number"
                        tickFormatter={(value) => `$${value >= 1000 ? `${value / 1000}k` : value}`}
                      />
                      <YAxis
                        dataKey="category"
                        type="category"
                        width={100}
                        tickFormatter={(value) =>
                          value
                            .split(' ')
                            .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                            .join(' ')
                        }
                      />
                      <Tooltip
                        formatter={(value: number) => [formatCurrency(value), 'Monthly Amount']}
                      />
                      <Bar dataKey="amount">
                        {expenseChartData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                </Grid>

                <Grid item xs={12} md={5}>
                  <Box mb={3}>
                    <Typography variant="subtitle1" gutterBottom>
                      Expense to Income Ratio
                    </Typography>
                    <Box display="flex" alignItems="center" mb={1}>
                      <Box width="100%" mr={1}>
                        <LinearProgress
                          variant="determinate"
                          value={Math.min(expenseToIncomeRatio, 100)}
                          color={
                            expenseToIncomeRatio <= 50
                              ? 'success'
                              : expenseToIncomeRatio <= 80
                                ? 'warning'
                                : 'error'
                          }
                        />
                      </Box>
                      <Typography variant="body2">{expenseToIncomeRatio.toFixed(1)}%</Typography>
                    </Box>
                    <Typography variant="caption" color="textSecondary">
                      {expenseToIncomeRatio <= 50
                        ? 'Excellent - You have significant room for savings'
                        : expenseToIncomeRatio <= 80
                          ? 'Good - Consider optimizing expenses'
                          : 'Warning - Expenses are high relative to income'}
                    </Typography>
                  </Box>

                  <Divider sx={{ my: 2 }} />

                  <Box>
                    <Typography variant="subtitle1" gutterBottom>
                      Emergency Fund
                    </Typography>
                    <Box display="flex" alignItems="center" mb={1}>
                      <Box width="100%" mr={1}>
                        <LinearProgress
                          variant="determinate"
                          value={Math.min((emergencyFundMonths / 6) * 100, 100)}
                          color={
                            emergencyFundMonths >= 6
                              ? 'success'
                              : emergencyFundMonths >= 3
                                ? 'warning'
                                : 'error'
                          }
                        />
                      </Box>
                      <Typography variant="body2">
                        {emergencyFundMonths.toFixed(1)} months
                      </Typography>
                    </Box>
                    <Typography variant="caption" color="textSecondary">
                      {emergencyFundMonths >= 6
                        ? 'Great! You have a solid emergency fund'
                        : emergencyFundMonths >= 3
                          ? 'Consider building a larger emergency fund'
                          : 'Priority: Build an emergency fund of 3-6 months of expenses'}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default EnhancedSummarySection;
