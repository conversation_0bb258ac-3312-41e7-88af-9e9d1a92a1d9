// Income frequency types
export type IncomeFrequency = 'weekly' | 'biweekly' | 'monthly' | 'annually';

// Income type options
export type IncomeType =
  | 'salary'
  | 'self_employed'
  | 'investment'
  | 'pension'
  | 'social_security'
  | 'other';

// Tax filing status
export type FilingStatusType =
  | 'SINGLE'
  | 'MARRIED_JOINT'
  | 'HEAD_OF_HOUSEHOLD'
  | 'MARRIED_SEPARATE';

// Income source interface
export interface IncomeSource {
  id: string;
  source: string;
  amount: string;
  frequency: IncomeFrequency;
  type: IncomeType;
  description?: string;
  isTaxable?: boolean;
  isPreTax?: boolean;
}

// Budget allocation interface
export interface BudgetAllocation {
  needs: number;
  wants: number;
  savings: number;
  needsPercentage: number;
  wantsPercentage: number;
  savingsPercentage: number;
  status: 'healthy' | 'needs_adjustment' | 'needs_major_adjustment';
}

// Main form data interface
export interface IncomeFormData {
  // Primary income fields
  primaryIncome: string;
  primaryIncomeFrequency: IncomeFrequency;
  primaryIncomeType: IncomeType;

  // Income sources
  incomeSources: IncomeSource[];

  // Tax-related fields
  filingStatus: FilingStatusType;
  state: string;
  dependents: number;
  preTaxDeductions: number;
  postTaxDeductions: number;

  // Calculated fields
  annualIncome: number;
  additionalIncome: number;
  totalAnnualIncome: number;
  federalTax: number;
  stateTax: number;
  ficaTax: number;
  totalTax: number;
  afterTaxIncome: number;
  effectiveTaxRate: number;
  budgetAllocation: BudgetAllocation;

  // Metadata
  lastUpdated: string;
  version: string;
}

// Component props
export interface IncomeDetailsProps {
  onComplete?: () => void;
  onSave?: (data: IncomeFormData) => Promise<void> | void;
  onBack?: () => void;
  data?: Partial<IncomeFormData>;
  updateData?: (data: Partial<IncomeFormData>) => void;
}

// Initial budget allocation values
export const initialBudgetAllocation: BudgetAllocation = {
  needs: 0,
  wants: 0,
  savings: 0,
  needsPercentage: 0,
  wantsPercentage: 0,
  savingsPercentage: 0,
  status: 'needs_adjustment',
};

// Initial form values
export const initialIncomeFormValues: IncomeFormData = {
  // Primary income
  primaryIncome: '',
  primaryIncomeFrequency: 'annually',
  primaryIncomeType: 'salary',

  // Tax info
  filingStatus: 'SINGLE',
  state: '',
  dependents: 0,
  preTaxDeductions: 0,
  postTaxDeductions: 0,

  // Calculated fields
  annualIncome: 0,
  additionalIncome: 0,
  totalAnnualIncome: 0,
  federalTax: 0,
  stateTax: 0,
  ficaTax: 0,
  totalTax: 0,
  afterTaxIncome: 0,
  effectiveTaxRate: 0,
  budgetAllocation: initialBudgetAllocation,

  // Metadata
  lastUpdated: new Date().toISOString(),
  version: '1.0.0',
};

// Form field options
export const INCOME_FREQUENCY_OPTIONS: Array<{ value: IncomeFrequency; label: string }> = [
  { value: 'weekly', label: 'Weekly' },
  { value: 'biweekly', label: 'Bi-weekly' },
  { value: 'monthly', label: 'Monthly' },
  { value: 'annually', label: 'Annually' },
];

export const INCOME_TYPE_OPTIONS: Array<{ value: IncomeType; label: string }> = [
  { value: 'salary', label: 'Salary/Wages' },
  { value: 'self_employed', label: 'Self-Employed' },
  { value: 'investment', label: 'Investment' },
  { value: 'pension', label: 'Pension' },
  { value: 'social_security', label: 'Social Security' },
  { value: 'other', label: 'Other' },
];

export const FILING_STATUS_OPTIONS: Array<{ value: FilingStatusType; label: string }> = [
  { value: 'SINGLE', label: 'Single' },
  { value: 'MARRIED_JOINT', label: 'Married Filing Jointly' },
  { value: 'HEAD_OF_HOUSEHOLD', label: 'Head of Household' },
  { value: 'MARRIED_SEPARATE', label: 'Married Filing Separately' },
];

// US states for dropdown
export const US_STATES = [
  { value: 'AL', label: 'Alabama' },
  { value: 'AK', label: 'Alaska' },
  { value: 'AZ', label: 'Arizona' },
  { value: 'AR', label: 'Arkansas' },
  { value: 'CA', label: 'California' },
  { value: 'CO', label: 'Colorado' },
  { value: 'CT', label: 'Connecticut' },
  { value: 'DE', label: 'Delaware' },
  { value: 'FL', label: 'Florida' },
  { value: 'GA', label: 'Georgia' },
  { value: 'HI', label: 'Hawaii' },
  { value: 'ID', label: 'Idaho' },
  { value: 'IL', label: 'Illinois' },
  { value: 'IN', label: 'Indiana' },
  { value: 'IA', label: 'Iowa' },
  { value: 'KS', label: 'Kansas' },
  { value: 'KY', label: 'Kentucky' },
  { value: 'LA', label: 'Louisiana' },
  { value: 'ME', label: 'Maine' },
  { value: 'MD', label: 'Maryland' },
  { value: 'MA', label: 'Massachusetts' },
  { value: 'MI', label: 'Michigan' },
  { value: 'MN', label: 'Minnesota' },
  { value: 'MS', label: 'Mississippi' },
  { value: 'MO', label: 'Missouri' },
  { value: 'MT', label: 'Montana' },
  { value: 'NE', label: 'Nebraska' },
  { value: 'NV', label: 'Nevada' },
  { value: 'NH', label: 'New Hampshire' },
  { value: 'NJ', label: 'New Jersey' },
  { value: 'NM', label: 'New Mexico' },
  { value: 'NY', label: 'New York' },
  { value: 'NC', label: 'North Carolina' },
  { value: 'ND', label: 'North Dakota' },
  { value: 'OH', label: 'Ohio' },
  { value: 'OK', label: 'Oklahoma' },
  { value: 'OR', label: 'Oregon' },
  { value: 'PA', label: 'Pennsylvania' },
  { value: 'RI', label: 'Rhode Island' },
  { value: 'SC', label: 'South Carolina' },
  { value: 'SD', label: 'South Dakota' },
  { value: 'TN', label: 'Tennessee' },
  { value: 'TX', label: 'Texas' },
  { value: 'UT', label: 'Utah' },
  { value: 'VT', label: 'Vermont' },
  { value: 'VA', label: 'Virginia' },
  { value: 'WA', label: 'Washington' },
  { value: 'WV', label: 'West Virginia' },
  { value: 'WI', label: 'Wisconsin' },
  { value: 'WY', label: 'Wyoming' },
];
