import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  CardHeader,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Button,
  Paper,
  Grid,
  LinearProgress,
  Tooltip,
  IconButton,
  Collapse,
  TextField,
  MenuItem,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  AttachMoney as AttachMoneyIcon,
  AccountBalance as AccountBalanceIcon,
  AccountBalanceWallet as AccountBalanceWalletIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import {
  getTaxOptimizationStrategies,
  calculateOptimalRetirementContribution,
  compareTaxScenarios,
} from '../../../../utils/taxOptimization';
import { formatCurrency } from '../../../../utils/formatters';

const StyledCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'transform 0.2s, box-shadow 0.2s',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.shadows[4],
  },
}));

const StrategyCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  marginBottom: theme.spacing(2),
  borderLeft: `4px solid ${theme.palette.primary.main}`,
  '&.high-impact': {
    borderLeftColor: theme.palette.error.main,
  },
  '&.medium-impact': {
    borderLeftColor: theme.palette.warning.main,
  },
  '&.low-impact': {
    borderLeftColor: theme.palette.success.main,
  },
}));

interface TaxOptimizationPanelProps {
  userData: {
    income: number;
    filingStatus: 'single' | 'married' | 'headOfHousehold';
    age: number;
    retirementAccounts: {
      traditionalIRA?: number;
      rothIRA?: number;
      employer401k?: number;
      employer401kMatch?: number;
    };
    investments: {
      taxableBrokerage: number;
      capitalGains: number;
    };
    deductions: {
      itemized: number;
      standard: number;
    };
    dependents: number;
    hsaEligible: boolean;
    hsaContribution: number;
  };
  onStrategySelect?: (strategyId: string) => void;
}

export const TaxOptimizationPanel: React.FC<TaxOptimizationPanelProps> = ({
  userData,
  onStrategySelect,
}) => {
  const [strategies, setStrategies] = useState<any[]>([]);
  const [expandedStrategy, setExpandedStrategy] = useState<string | null>(null);
  const [scenarioName, setScenarioName] = useState('My Scenario');
  const [scenarios, setScenarios] = useState<any[]>([]);
  const [selectedScenario, setSelectedScenario] = useState<number | null>(null);

  // Load strategies based on user data
  useEffect(() => {
    const loadStrategies = () => {
      const optimizationStrategies = getTaxOptimizationStrategies({
        ...userData,
        year: new Date().getFullYear(),
      });
      setStrategies(optimizationStrategies);
    };

    loadStrategies();
  }, [userData]);

  const handleStrategyExpand = (strategyId: string) => {
    setExpandedStrategy(expandedStrategy === strategyId ? null : strategyId);
  };

  const handleAddToScenario = (strategy: any) => {
    if (onStrategySelect) {
      onStrategySelect(strategy.id);
    }
  };

  const handleCreateScenario = () => {
    const newScenario = {
      id: Date.now(),
      name: scenarioName,
      strategies: [],
      date: new Date().toISOString(),
    };
    setScenarios([...scenarios, newScenario]);
    setSelectedScenario(scenarios.length);
    setScenarioName('My Scenario');
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high':
        return 'error';
      case 'medium':
        return 'warning';
      case 'low':
        return 'success';
      default:
        return 'default';
    }
  };

  const getImpactIcon = (impact: string) => {
    switch (impact) {
      case 'high':
        return <WarningIcon color="error" />;
      case 'medium':
        return <InfoIcon color="warning" />;
      case 'low':
        return <CheckCircleIcon color="success" />;
      default:
        return <InfoIcon color="info" />;
    }
  };

  return (
    <Box sx={{ mt: 3 }}>
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <StyledCard>
            <CardHeader
              title="Tax Optimization Strategies"
              subheader="Personalized recommendations to optimize your tax situation"
              action={
                <Box display="flex" alignItems="center">
                  <TextField
                    size="small"
                    label="Scenario Name"
                    value={scenarioName}
                    onChange={(e) => setScenarioName(e.target.value)}
                    sx={{ mr: 1, minWidth: 200 }}
                  />
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleCreateScenario}
                    disabled={!scenarioName.trim()}
                  >
                    Create Scenario
                  </Button>
                </Box>
              }
            />
            <CardContent>
              <Typography variant="body2" color="text.secondary" paragraph>
                Based on your financial profile, here are personalized tax optimization strategies.
                Each strategy includes an estimated tax savings and implementation steps.
              </Typography>

              <Divider sx={{ my: 2 }} />

              {strategies.length === 0 ? (
                <Box textAlign="center" py={4}>
                  <Typography variant="body1" color="text.secondary">
                    No optimization strategies available based on your current financial profile.
                  </Typography>
                </Box>
              ) : (
                <List disablePadding>
                  {strategies.map((strategy) => (
                    <React.Fragment key={strategy.id}>
                      <StrategyCard className={`${strategy.impact}-impact`} elevation={2}>
                        <Box display="flex" justifyContent="space-between" alignItems="center">
                          <Box display="flex" alignItems="center">
                            <ListItemIcon>{getImpactIcon(strategy.impact)}</ListItemIcon>
                            <Box>
                              <Typography variant="subtitle1" fontWeight="bold">
                                {strategy.title}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Potential Savings: {formatCurrency(strategy.estimatedSavings)}
                              </Typography>
                            </Box>
                          </Box>
                          <Box>
                            <Button
                              size="small"
                              color="primary"
                              onClick={() => handleAddToScenario(strategy)}
                              sx={{ mr: 1 }}
                            >
                              Add to Scenario
                            </Button>
                            <IconButton
                              size="small"
                              onClick={() => handleStrategyExpand(strategy.id)}
                            >
                              {expandedStrategy === strategy.id ? (
                                <ExpandLessIcon />
                              ) : (
                                <ExpandMoreIcon />
                              )}
                            </IconButton>
                          </Box>
                        </Box>

                        <Collapse
                          in={expandedStrategy === strategy.id}
                          timeout="auto"
                          unmountOnExit
                        >
                          <Box mt={2} pt={2} borderTop="1px solid rgba(0,0,0,0.12)">
                            <Typography variant="body2" paragraph>
                              {strategy.description}
                            </Typography>
                            <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
                              Implementation:
                            </Typography>
                            <Typography variant="body2" paragraph>
                              {strategy.implementation}
                            </Typography>
                            <Box mt={2}>
                              <Chip
                                label={`${strategy.impact} impact`.toUpperCase()}
                                size="small"
                                color={getImpactColor(strategy.impact)}
                                variant="outlined"
                                sx={{ mr: 1 }}
                              />
                              <Chip
                                label={`Save up to ${formatCurrency(strategy.estimatedSavings)}`}
                                size="small"
                                color="success"
                                variant="outlined"
                                icon={<AttachMoneyIcon fontSize="small" />}
                              />
                            </Box>
                          </Box>
                        </Collapse>
                      </StrategyCard>
                    </React.Fragment>
                  ))}
                </List>
              )}
            </CardContent>
          </StyledCard>
        </Grid>

        <Grid item xs={12} md={4}>
          <StyledCard>
            <CardHeader title="Tax Scenarios" subheader="Compare different tax strategies" />
            <CardContent>
              {scenarios.length === 0 ? (
                <Box textAlign="center" py={4}>
                  <AccountBalanceWalletIcon color="action" sx={{ fontSize: 40, mb: 1 }} />
                  <Typography variant="body1" color="text.secondary">
                    Create a scenario to compare different tax strategies.
                  </Typography>
                </Box>
              ) : (
                <Box>
                  <Box mb={2}>
                    <Typography variant="subtitle2" gutterBottom>
                      Your Scenarios:
                    </Typography>
                    <Box display="flex" flexWrap="wrap" gap={1} mb={2}>
                      {scenarios.map((scenario, index) => (
                        <Chip
                          key={scenario.id}
                          label={scenario.name}
                          onClick={() => setSelectedScenario(index)}
                          color={selectedScenario === index ? 'primary' : 'default'}
                          variant={selectedScenario === index ? 'filled' : 'outlined'}
                        />
                      ))}
                    </Box>
                  </Box>

                  {selectedScenario !== null && (
                    <Box mt={2}>
                      <Typography variant="subtitle2" gutterBottom>
                        {scenarios[selectedScenario].name}
                      </Typography>
                      <Typography
                        variant="caption"
                        color="text.secondary"
                        display="block"
                        gutterBottom
                      >
                        Created: {new Date(scenarios[selectedScenario].date).toLocaleDateString()}
                      </Typography>

                      <Box mt={2}>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          Tax Savings Potential:
                        </Typography>
                        <LinearProgress
                          variant="determinate"
                          value={Math.min(100, strategies.length * 15)}
                          sx={{ height: 10, borderRadius: 5, mb: 1 }}
                        />
                        <Box display="flex" justifyContent="space-between">
                          <Typography variant="caption" color="text.secondary">
                            $
                            {(
                              strategies.reduce((sum, s) => sum + s.estimatedSavings, 0) * 0.3
                            ).toLocaleString()}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            $
                            {strategies
                              .reduce((sum, s) => sum + s.estimatedSavings, 0)
                              .toLocaleString()}
                          </Typography>
                        </Box>
                      </Box>

                      <Box mt={3}>
                        <Button
                          variant="outlined"
                          color="primary"
                          fullWidth
                          startIcon={<TrendingUpIcon />}
                        >
                          View Detailed Projection
                        </Button>
                      </Box>
                    </Box>
                  )}
                </Box>
              )}
            </CardContent>
          </StyledCard>

          <Box mt={3}>
            <StyledCard>
              <CardHeader
                title="Tax Optimization Tips"
                subheader="Ways to improve your tax situation"
              />
              <CardContent>
                <List disablePadding>
                  <ListItem alignItems="flex-start" sx={{ px: 0 }}>
                    <ListItemIcon sx={{ minWidth: 36 }}>
                      <AccountBalanceIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Maximize Retirement Contributions"
                      secondary="Contributing to tax-advantaged accounts like 401(k)s and IRAs can significantly reduce your taxable income."
                    />
                  </ListItem>
                  <Divider component="li" variant="inset" />
                  <ListItem alignItems="flex-start" sx={{ px: 0 }}>
                    <ListItemIcon sx={{ minWidth: 36 }}>
                      <AccountBalanceWalletIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Harvest Tax Losses"
                      secondary="Sell underperforming investments to offset capital gains and reduce your tax liability."
                    />
                  </ListItem>
                  <Divider component="li" variant="inset" />
                  <ListItem alignItems="flex-start" sx={{ px: 0 }}>
                    <ListItemIcon sx={{ minWidth: 36 }}>
                      <AttachMoneyIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Consider Tax-Efficient Investments"
                      secondary="Invest in tax-efficient funds and hold investments for the long term to benefit from lower capital gains rates."
                    />
                  </ListItem>
                </List>
              </CardContent>
            </StyledCard>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default TaxOptimizationPanel;
