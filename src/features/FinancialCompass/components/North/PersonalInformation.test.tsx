import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ThemeProvider from '../../../../theme/ThemeProvider';
import PersonalInformation from './PersonalInformation';
import { FinancialCompassProvider } from '../../context/FinancialCompassContext';
import { GuidedJourneyProvider } from '../../../GuidedJourney/context/GuidedJourneyContext';

// Mock the useGuidedJourney hook
jest.mock('../../../GuidedJourney/context/GuidedJourneyContext', () => ({
  ...jest.requireActual('../../../GuidedJourney/context/GuidedJourneyContext'),
  useGuidedJourney: () => ({
    askQuestion: jest.fn().mockResolvedValue('This is a mock answer'),
  }),
}));

// Test component wrapper with providers
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ThemeProvider initialMode="light" initialSeason="spring">
      <FinancialCompassProvider>
        <GuidedJourneyProvider>{children}</GuidedJourneyProvider>
      </FinancialCompassProvider>
    </ThemeProvider>
  );
};

describe('PersonalInformation', () => {
  test('renders the component with all form fields', () => {
    render(
      <TestWrapper>
        <PersonalInformation />
      </TestWrapper>
    );

    // Check for section titles
    expect(screen.getByText('Personal Information')).toBeInTheDocument();
    expect(screen.getByText('Basic Information')).toBeInTheDocument();
    expect(screen.getByText('Contact Information')).toBeInTheDocument();
    expect(screen.getByText('Address')).toBeInTheDocument();
    expect(screen.getByText('Employment Information')).toBeInTheDocument();

    // Check for form fields
    expect(screen.getByLabelText('First Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Last Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Date of Birth')).toBeInTheDocument();
    expect(screen.getByLabelText('Email Address')).toBeInTheDocument();
    expect(screen.getByLabelText('Phone Number')).toBeInTheDocument();
    expect(screen.getByLabelText('Street Address')).toBeInTheDocument();
    expect(screen.getByLabelText('City')).toBeInTheDocument();
    expect(screen.getByLabelText('State')).toBeInTheDocument();
    expect(screen.getByLabelText('ZIP Code')).toBeInTheDocument();
    expect(screen.getByLabelText('Country')).toBeInTheDocument();
    expect(screen.getByLabelText('Occupation')).toBeInTheDocument();
    expect(screen.getByLabelText('Employment Status')).toBeInTheDocument();

    // Check for submit button
    expect(screen.getByRole('button', { name: /save and continue/i })).toBeInTheDocument();
  });

  test('updates form values when user inputs data', () => {
    render(
      <TestWrapper>
        <PersonalInformation />
      </TestWrapper>
    );

    // Fill out form fields
    fireEvent.change(screen.getByLabelText('First Name'), { target: { value: 'John' } });
    fireEvent.change(screen.getByLabelText('Last Name'), { target: { value: 'Doe' } });
    fireEvent.change(screen.getByLabelText('Email Address'), {
      target: { value: '<EMAIL>' },
    });

    // Check if values were updated
    expect(screen.getByLabelText('First Name')).toHaveValue('John');
    expect(screen.getByLabelText('Last Name')).toHaveValue('Doe');
    expect(screen.getByLabelText('Email Address')).toHaveValue('<EMAIL>');
  });

  test('calls onComplete when form is submitted', async () => {
    const handleComplete = jest.fn();

    render(
      <TestWrapper>
        <PersonalInformation onComplete={handleComplete} />
      </TestWrapper>
    );

    // Fill out required fields
    fireEvent.change(screen.getByLabelText('First Name'), { target: { value: 'John' } });
    fireEvent.change(screen.getByLabelText('Last Name'), { target: { value: 'Doe' } });
    fireEvent.change(screen.getByLabelText('Date of Birth'), { target: { value: '1990-01-01' } });
    fireEvent.change(screen.getByLabelText('Email Address'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.change(screen.getByLabelText('Phone Number'), { target: { value: '************' } });
    fireEvent.change(screen.getByLabelText('Street Address'), { target: { value: '123 Main St' } });
    fireEvent.change(screen.getByLabelText('City'), { target: { value: 'Anytown' } });
    fireEvent.change(screen.getByLabelText('State'), { target: { value: 'CA' } });
    fireEvent.change(screen.getByLabelText('ZIP Code'), { target: { value: '12345' } });
    fireEvent.change(screen.getByLabelText('Occupation'), {
      target: { value: 'Software Developer' },
    });

    // Submit the form
    fireEvent.click(screen.getByRole('button', { name: /save and continue/i }));

    // Wait for the save process to complete
    await waitFor(() => {
      expect(handleComplete).toHaveBeenCalledTimes(1);
    });
  });

  test('shows save indicator after auto-save', async () => {
    render(
      <TestWrapper>
        <PersonalInformation />
      </TestWrapper>
    );

    // Change a field to trigger auto-save
    fireEvent.change(screen.getByLabelText('First Name'), { target: { value: 'John' } });

    // Wait for the save indicator to appear
    await waitFor(() => {
      expect(screen.getByText('✓ Saved')).toBeInTheDocument();
    });

    // Wait for the save indicator to disappear
    await waitFor(
      () => {
        expect(screen.getByText('✓ Saved')).not.toBeVisible();
      },
      { timeout: 3000 }
    );
  });

  test('calls onSave with form data when saving', async () => {
    const handleSave = jest.fn();

    render(
      <TestWrapper>
        <PersonalInformation onSave={handleSave} />
      </TestWrapper>
    );

    // Change a field to trigger auto-save
    fireEvent.change(screen.getByLabelText('First Name'), { target: { value: 'John' } });

    // Wait for the save function to be called
    await waitFor(() => {
      expect(handleSave).toHaveBeenCalledTimes(1);
      expect(handleSave).toHaveBeenCalledWith(
        expect.objectContaining({
          firstName: 'John',
        })
      );
    });
  });
});
