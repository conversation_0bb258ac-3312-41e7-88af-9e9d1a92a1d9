import React, { useMemo } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { formatCurrency } from '../../../../utils/formatters';
import {
  calculateCashFlowHealthScore,
  calculateDebtManagementScore,
  calculateEmergencyFundScore,
  calculateNetWorthScore,
  calculateSimpleFinancialHealthScore,
} from '../../../../utils/financialHealthCalculator';

const NorthSummarySection: React.FC = () => {
  const { theme } = useTheme();
  const { data } = useFinancialCompass();

  // Calculate financial metrics
  const {
    totalMonthlyIncome,
    totalMonthlyExpenses,
    monthlyCashFlow,
    savingsRate,
    netWorth,
    overallScore,
    healthStatus
  } = useMemo(() => {
    // Get data
    const incomeDetails = data.north?.incomeDetails || {};
    const expenseDetails = data.north?.expenseDetails || {};
    const assets = data.north?.assets || {};
    const liabilities = data.north?.liabilities || {};

    // Calculate income
    const primaryIncome = parseFloat(incomeDetails.primaryIncome as string) || 0;
    const frequency = incomeDetails.primaryIncomeFrequency;

    let monthlyIncome = 0;
    switch (frequency) {
      case 'weekly':
        monthlyIncome = primaryIncome * 4.33;
        break;
      case 'biweekly':
        monthlyIncome = primaryIncome * 2.17;
        break;
      case 'monthly':
        monthlyIncome = primaryIncome;
        break;
      case 'annually':
        monthlyIncome = primaryIncome / 12;
        break;
      default:
        monthlyIncome = primaryIncome;
    }

    // Add additional income sources
    incomeDetails.incomeSources?.forEach((source: any) => {
      const amount = source.amount || 0;
      switch (source.frequency) {
        case 'weekly':
          monthlyIncome += amount * 4.33;
          break;
        case 'biweekly':
          monthlyIncome += amount * 2.17;
          break;
        case 'monthly':
          monthlyIncome += amount;
          break;
        case 'annually':
          monthlyIncome += amount / 12;
          break;
        default:
          monthlyIncome += amount;
      }
    });

    // Calculate expenses
    const monthlyExpenses = parseFloat(expenseDetails.totalMonthlyExpenses as string) || 0;

    // Calculate cash flow and savings rate
    const cashFlow = monthlyIncome - monthlyExpenses;
    const savings = monthlyIncome > 0 ? (cashFlow / monthlyIncome) * 100 : 0;

    // Calculate net worth
    const totalAssets = Object.values(assets).reduce((sum: number, category: any) => {
      if (typeof category === 'object' && category !== null) {
        return sum + Object.values(category).reduce((catSum: number, value: any) => {
          return catSum + (parseFloat(value) || 0);
        }, 0);
      }
      return sum + (parseFloat(category) || 0);
    }, 0);

    const totalLiabilities = Object.values(liabilities).reduce((sum: number, category: any) => {
      if (typeof category === 'object' && category !== null) {
        return sum + Object.values(category).reduce((catSum: number, value: any) => {
          return catSum + (parseFloat(value) || 0);
        }, 0);
      }
      return sum + (parseFloat(category) || 0);
    }, 0);

    const worth = totalAssets - totalLiabilities;

    // Calculate financial health scores
    const cashFlowScore = calculateCashFlowHealthScore(cashFlow, monthlyIncome);
    const debtScore = calculateDebtManagementScore(totalLiabilities, monthlyIncome * 12);
    const emergencyFundScore = calculateEmergencyFundScore(
      (assets.cash?.checking || 0) + (assets.cash?.savings || 0),
      monthlyExpenses
    );
    const netWorthScore = calculateNetWorthScore(worth, monthlyIncome * 12);
    const score = calculateSimpleFinancialHealthScore(
      cashFlowScore,
      debtScore,
      emergencyFundScore,
      netWorthScore
    );

    // Determine health status
    const status = score >= 80 ? 'Excellent' :
      score >= 70 ? 'Very Good' :
        score >= 60 ? 'Good' :
          score >= 50 ? 'Fair' :
            score >= 40 ? 'Needs Attention' : 'Critical';

    return {
      totalMonthlyIncome: monthlyIncome,
      totalMonthlyExpenses: monthlyExpenses,
      monthlyCashFlow: cashFlow,
      savingsRate: savings,
      netWorth: worth,
      overallScore: score,
      healthStatus: status
    };
  }, [data.north]);

  return (
    <SummaryContainer theme={theme}>
      <SummaryHeader>
        <SummaryTitle theme={theme}>North Direction Summary</SummaryTitle>
        <SummarySubtitle theme={theme}>Your Current Financial Position</SummarySubtitle>
      </SummaryHeader>

      <HealthScoreCard theme={theme}>
        <HealthScoreHeader>
          <HealthScoreLabel theme={theme}>Financial Health Score</HealthScoreLabel>
          <HealthScoreValue score={overallScore}>{Math.round(overallScore)}</HealthScoreValue>
        </HealthScoreHeader>
        <HealthScoreStatus score={overallScore}>{healthStatus}</HealthScoreStatus>
        <HealthScoreBar theme={theme}>
          <HealthScoreFill score={overallScore} theme={theme} />
        </HealthScoreBar>
      </HealthScoreCard>

      <MetricsGrid>
        <MetricCard theme={theme}>
          <MetricIcon>💰</MetricIcon>
          <MetricLabel theme={theme}>Monthly Income</MetricLabel>
          <MetricValue theme={theme}>{formatCurrency(totalMonthlyIncome)}</MetricValue>
        </MetricCard>

        <MetricCard theme={theme}>
          <MetricIcon>📊</MetricIcon>
          <MetricLabel theme={theme}>Monthly Expenses</MetricLabel>
          <MetricValue theme={theme}>{formatCurrency(totalMonthlyExpenses)}</MetricValue>
        </MetricCard>

        <MetricCard theme={theme}>
          <MetricIcon positive={monthlyCashFlow >= 0}>
            {monthlyCashFlow >= 0 ? '📈' : '📉'}
          </MetricIcon>
          <MetricLabel theme={theme}>Cash Flow</MetricLabel>
          <MetricValue theme={theme} positive={monthlyCashFlow >= 0}>
            {formatCurrency(monthlyCashFlow)}
          </MetricValue>
        </MetricCard>

        <MetricCard theme={theme}>
          <MetricIcon positive={netWorth >= 0}>
            {netWorth >= 0 ? '🏆' : '⚠️'}
          </MetricIcon>
          <MetricLabel theme={theme}>Net Worth</MetricLabel>
          <MetricValue theme={theme} positive={netWorth >= 0}>
            {formatCurrency(netWorth)}
          </MetricValue>
        </MetricCard>
      </MetricsGrid>

      <InsightCard theme={theme}>
        <InsightTitle theme={theme}>Key Insight</InsightTitle>
        <InsightText theme={theme}>
          {monthlyCashFlow >= 0 && savingsRate >= 15
            ? `Excellent! You're saving ${savingsRate.toFixed(1)}% of your income. Keep building your financial foundation.`
            : monthlyCashFlow >= 0 && savingsRate >= 10
              ? `Good progress! You're saving ${savingsRate.toFixed(1)}% of your income. Consider increasing to 15-20%.`
              : monthlyCashFlow >= 0
                ? `You have positive cash flow but low savings rate (${savingsRate.toFixed(1)}%). Focus on increasing savings.`
                : 'Your expenses exceed income. Review your budget to create positive cash flow.'}
        </InsightText>
      </InsightCard>
    </SummaryContainer>
  );
};
