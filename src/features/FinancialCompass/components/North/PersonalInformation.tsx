/**
 * Personal Information Component
 *
 * This component collects and displays the user's personal information as part of the
 * North direction of the Financial Compass ("Where You Are").
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { useGuidedJourney } from '../../../GuidedJourney/context/GuidedJourneyContext';
import { PersonalInformationData } from '../../../../types/northDirection';

interface PersonalInformationProps {
  onComplete?: () => void;
  onSave?: (data: PersonalInformationData) => void;
  onBack?: () => void;
}

// Styled Components
const FormContainer = styled.div`
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: ${({ theme }) => theme.shape.borderRadius}px;
  padding: ${({ theme }) => theme.spacing(3)}px;
  box-shadow: ${({ theme }) => theme.shadows[1]};
  max-width: 800px;
  margin: 0 auto;
`;

const FormTitle = styled.h2`
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing(3)}px;
  font-weight: 500;
`;

const FormDescription = styled.p`
  color: ${({ theme }) => theme.colors.text.secondary};
  margin-bottom: ${({ theme }) => theme.spacing(4)}px;
`;

const FormSection = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing(4)}px;
`;

const SectionTitle = styled.h3`
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing(2)}px;
  font-weight: 500;
  font-size: 1.1rem;
`;

const FormRow = styled.div`
  display: flex;
  flex-wrap: wrap;
  margin: 0 -${({ theme }) => theme.spacing(1)}px;
`;

const FormField = styled.div`
  flex: 1 0 250px;
  padding: 0 ${({ theme }) => theme.spacing(1)}px;
  margin-bottom: ${({ theme }) => theme.spacing(2)}px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: ${({ theme }) => theme.spacing(0.5)}px;
  color: ${({ theme }) => theme.colors.text.primary};
  font-weight: 500;
`;

const Input = styled.input`
  width: 100%;
  padding: ${({ theme }) => theme.spacing(1.5)}px;
  border: 1px solid ${({ theme }) => theme.colors.divider};
  border-radius: ${({ theme }) => theme.shape.borderRadius}px;
  background-color: ${({ theme }) => theme.colors.background.default};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 1rem;
  transition: border-color 0.2s ease-in-out;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary.main};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light}40;
  }
`;

const Select = styled.select`
  width: 100%;
  padding: ${({ theme }) => theme.spacing(1.5)}px;
  border: 1px solid ${({ theme }) => theme.colors.divider};
  border-radius: ${({ theme }) => theme.shape.borderRadius}px;
  background-color: ${({ theme }) => theme.colors.background.default};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 1rem;
  transition: border-color 0.2s ease-in-out;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary.main};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light}40;
  }
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-top: ${({ theme }) => theme.spacing(4)}px;
`;

const Button = styled.button`
  padding: ${({ theme }) => theme.spacing(1.5)}px ${({ theme }) => theme.spacing(3)}px;
  background-color: ${({ theme }) => theme.colors.primary.main};
  color: ${({ theme }) => theme.colors.primary.contrastText};
  border: none;
  border-radius: ${({ theme }) => theme.shape.borderRadius}px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;

  &:hover {
    background-color: ${({ theme }) => theme.colors.primary.dark};
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light}40;
  }
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${({ visible }) => (visible ? 1 : 0)};
  transition: opacity 0.3s ease-in-out;
  margin-right: ${({ theme }) => theme.spacing(2)}px;
  color: ${({ theme }) => theme.colors.success.main};
  display: flex;
  align-items: center;
`;

/**
 * Personal Information Component
 */
const PersonalInformation: React.FC<PersonalInformationProps> = ({ onComplete, onSave }) => {
  const { theme } = useTheme();
  const { data, updateData } = useFinancialCompass();
  const { askQuestion } = useGuidedJourney();

  // Calculate age from date of birth
  const calculateAge = (dateOfBirth: string): string => {
    if (!dateOfBirth) return '';

    const birthDate = new Date(dateOfBirth);
    const today = new Date();

    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDifference = today.getMonth() - birthDate.getMonth();

    if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age.toString();
  };

  // Initialize form data from context or with defaults
  const [formData, setFormData] = useState<PersonalInformationData>(() => {
    const existingData = data.north?.personalInformation as PersonalInformationData;

    if (existingData) {
      // If we have existing data, make sure age is calculated
      return {
        ...existingData,
        age: existingData.age || calculateAge(existingData.dateOfBirth),
      };
    }

    // Default data
    return {
      firstName: '',
      lastName: '',
      dateOfBirth: '',
      email: '',
      phone: '',
      age: '',
      address: {
        street: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'United States',
      },
      occupation: '',
      employmentStatus: 'employed',
    };
  });

  const [isSaving, setIsSaving] = useState(false);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Auto-save effect
  useEffect(() => {
    const saveTimer = setTimeout(() => {
      handleSave();
    }, 1000);

    return () => clearTimeout(saveTimer);
  }, [formData]);

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    // Handle nested address fields
    if (name.startsWith('address.')) {
      const addressField = name.split('.')[1];
      setFormData({
        ...formData,
        address: {
          ...formData.address,
          [addressField]: value,
        },
      });
    } else {
      // If date of birth is changed, calculate and update age
      if (name === 'dateOfBirth') {
        setFormData({
          ...formData,
          [name]: value,
          age: calculateAge(value),
        });
      } else {
        setFormData({
          ...formData,
          [name]: value,
        });
      }
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSave();

    if (onComplete) {
      onComplete();
    }
  };

  // Handle saving data
  const handleSave = () => {
    setIsSaving(true);

    // Update data in context
    updateData('north', 'personalInformation', formData);

    // Call onSave prop if provided
    if (onSave) {
      onSave(formData);
    }

    // Show save indicator
    setShowSaveIndicator(true);

    // Hide save indicator after 2 seconds
    setTimeout(() => {
      setIsSaving(false);
      setShowSaveIndicator(false);
    }, 2000);
  };

  // Handle asking for help
  const handleAskHelp = async (question: string) => {
    const answer = await askQuestion(question);
    // In a real implementation, you would display the answer to the user
    console.log(answer);
  };

  return (
    <FormContainer theme={theme}>
      <FormTitle theme={theme}>Personal Information</FormTitle>
      <FormDescription theme={theme}>
        Please provide your personal information to help us understand your current situation. This
        information will be used to personalize your financial compass journey.
      </FormDescription>

      <form onSubmit={handleSubmit}>
        <FormSection>
          <SectionTitle>Basic Information</SectionTitle>
          <FormRow>
            <FormField>
              <Label htmlFor="firstName">First Name</Label>
              <Input
                type="text"
                id="firstName"
                name="firstName"
                value={formData.firstName}
                onChange={handleChange}
                required
              />
            </FormField>

            <FormField>
              <Label htmlFor="lastName">Last Name</Label>
              <Input
                type="text"
                id="lastName"
                name="lastName"
                value={formData.lastName}
                onChange={handleChange}
                required
              />
            </FormField>

            <FormField>
              <Label htmlFor="dateOfBirth">Date of Birth</Label>
              <Input
                type="date"
                id="dateOfBirth"
                name="dateOfBirth"
                value={formData.dateOfBirth}
                onChange={handleChange}
                required
              />
            </FormField>
          </FormRow>
        </FormSection>

        <FormSection>
          <SectionTitle>Contact Information</SectionTitle>
          <FormRow>
            <FormField>
              <Label htmlFor="email">Email Address</Label>
              <Input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
              />
            </FormField>

            <FormField>
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                required
              />
            </FormField>
          </FormRow>
        </FormSection>

        <FormSection>
          <SectionTitle>Address</SectionTitle>
          <FormRow>
            <FormField>
              <Label htmlFor="address.street">Street Address</Label>
              <Input
                type="text"
                id="address.street"
                name="address.street"
                value={formData.address.street}
                onChange={handleChange}
                required
              />
            </FormField>
          </FormRow>

          <FormRow>
            <FormField>
              <Label htmlFor="address.city">City</Label>
              <Input
                type="text"
                id="address.city"
                name="address.city"
                value={formData.address.city}
                onChange={handleChange}
                required
              />
            </FormField>

            <FormField>
              <Label htmlFor="address.state">State</Label>
              <Input
                type="text"
                id="address.state"
                name="address.state"
                value={formData.address.state}
                onChange={handleChange}
                required
              />
            </FormField>

            <FormField>
              <Label htmlFor="address.zipCode">ZIP Code</Label>
              <Input
                type="text"
                id="address.zipCode"
                name="address.zipCode"
                value={formData.address.zipCode}
                onChange={handleChange}
                required
              />
            </FormField>

            <FormField>
              <Label htmlFor="address.country">Country</Label>
              <Input
                type="text"
                id="address.country"
                name="address.country"
                value={formData.address.country}
                onChange={handleChange}
                required
              />
            </FormField>
          </FormRow>
        </FormSection>

        <FormSection>
          <SectionTitle>Employment Information</SectionTitle>
          <FormRow>
            <FormField>
              <Label htmlFor="occupation">Occupation</Label>
              <Input
                type="text"
                id="occupation"
                name="occupation"
                value={formData.occupation}
                onChange={handleChange}
                required
              />
            </FormField>

            <FormField>
              <Label htmlFor="employmentStatus">Employment Status</Label>
              <Select
                id="employmentStatus"
                name="employmentStatus"
                value={formData.employmentStatus}
                onChange={handleChange}
                required
              >
                <option value="employed">Employed</option>
                <option value="self-employed">Self-Employed</option>
                <option value="unemployed">Unemployed</option>
                <option value="retired">Retired</option>
                <option value="student">Student</option>
              </Select>
            </FormField>
          </FormRow>
        </FormSection>

        <ButtonContainer theme={theme}>
          <SaveIndicator visible={showSaveIndicator} theme={theme}>
            ✓ Saved
          </SaveIndicator>
          <Button type="submit" theme={theme}>
            {isSaving ? 'Saving...' : 'Save and Continue'}
          </Button>
        </ButtonContainer>
      </form>
    </FormContainer>
  );
};

export default PersonalInformation;
