import React, { useState, useCallback } from 'react';
import {
  Box,
  Button,
  TextField,
  Typography,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Divider,
  IconButton,
  Card,
  CardContent,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch,
} from '@mui/material';
import { Save, ArrowBack, Add as AddIcon, Delete as DeleteIcon } from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import {
  IncomeFormData,
  IncomeSource,
  IncomeDetailsProps,
  initialIncomeFormValues,
  initialBudgetAllocation,
} from './types/income.types';

// Tax calculation utilities
const calculateFederalTax = (income: number, status: string): number => {
  const brackets = {
    SINGLE: [
      { max: 10275, rate: 0.1 },
      { max: 41775, rate: 0.12 },
      { max: 89075, rate: 0.22 },
      { max: 170050, rate: 0.24 },
      { max: 215950, rate: 0.32 },
      { max: 539900, rate: 0.35 },
      { max: Infinity, rate: 0.37 },
    ],
    MARRIED_JOINT: [
      { max: 20550, rate: 0.1 },
      { max: 83550, rate: 0.12 },
      { max: 178150, rate: 0.22 },
      { max: 340100, rate: 0.24 },
      { max: 431900, rate: 0.32 },
      { max: 647850, rate: 0.35 },
      { max: Infinity, rate: 0.37 },
    ],
    HEAD_OF_HOUSEHOLD: [
      { max: 14650, rate: 0.1 },
      { max: 55900, rate: 0.12 },
      { max: 89050, rate: 0.22 },
      { max: 170050, rate: 0.24 },
      { max: 215950, rate: 0.32 },
      { max: 539900, rate: 0.35 },
      { max: Infinity, rate: 0.37 },
    ],
  } as const;

  const bracket = brackets[status as keyof typeof brackets] || brackets.SINGLE;
  let tax = 0;
  let remainingIncome = income;

  for (const { max, rate } of bracket) {
    if (remainingIncome <= 0) break;
    const taxable = Math.min(remainingIncome, max);
    tax += taxable * rate;
    remainingIncome -= max;
  }

  return tax;
};

const calculateStateTax = (income: number, state?: string): number => {
  // Simplified state tax calculation (5% flat rate for example)
  return income * 0.05;
};

const calculateFICATax = (income: number): number => {
  // Social Security tax (6.2% up to $147,000) + Medicare tax (1.45%)
  const socialSecurityTax = Math.min(income, 147000) * 0.062;
  const medicareTax = income * 0.0145;
  return socialSecurityTax + medicareTax;
};

const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);
};

const validateIncomeForm = (data: Partial<IncomeFormData>): Record<string, string> => {
  const errors: Record<string, string> = {};
  if (!data.primaryIncome) errors.primaryIncome = 'Primary income is required';
  if (!data.filingStatus) errors.filingStatus = 'Filing status is required';
  return errors;
};

const IncomeDetails: React.FC<IncomeDetailsProps> = ({
  onComplete = () => {},
  onSave = async () => {},
  onBack,
  data = {},
  updateData = () => {},
}) => {
  const { enqueueSnackbar } = useSnackbar();

  // Form state
  const [formValues, setFormValues] = useState<IncomeFormData>(() => ({
    ...initialIncomeFormValues,
    ...data,
    incomeSources: data.incomeSources || [],
    budgetAllocation: data.budgetAllocation || initialBudgetAllocation,
  }));

  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Calculate total income from all sources
  const calculateTotalIncome = useCallback((values: IncomeFormData): number => {
    const primaryIncome = Number(values.primaryIncome) || 0;
    const additionalIncome = (values.incomeSources || []).reduce(
      (sum: number, source: IncomeSource) => sum + (Number(source.amount) || 0),
      0
    );
    return primaryIncome + additionalIncome;
  }, []);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormValues((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user types
    if (formErrors[name]) {
      setFormErrors((prev) => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  // Handle select changes
  const handleSelectChange = (e: any) => {
    const { name, value } = e.target;
    if (!name) return;

    setFormValues((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle income source changes
  const handleIncomeSourceChange = (
    id: string,
    field: keyof IncomeSource,
    value: string | number | boolean
  ) => {
    setFormValues((prev) => {
      const updatedSources = (prev.incomeSources || []).map((source) =>
        source.id === id ? { ...source, [field]: value } : source
      );

      return {
        ...prev,
        incomeSources: updatedSources as IncomeSource[],
      };
    });
  };

  // Add new income source
  const handleAddIncomeSource = () => {
    const newSource: IncomeSource = {
      id: `source-${Date.now()}`,
      source: 'New Income Source',
      amount: '0',
      frequency: 'monthly',
      type: 'other',
      isTaxable: true,
      isPreTax: false,
    };

    setFormValues((prev) => ({
      ...prev,
      incomeSources: [...(prev.incomeSources || []), newSource],
    }));
  };

  // Remove income source
  const handleRemoveIncomeSource = (id: string) => {
    setFormValues((prev) => ({
      ...prev,
      incomeSources: (prev.incomeSources || []).filter((source) => source.id !== id),
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const errors = validateIncomeForm(formValues);
    setFormErrors(errors);

    if (Object.keys(errors).length > 0) {
      return;
    }

    try {
      setIsSubmitting(true);

      // Calculate total income from all sources
      const totalIncome = calculateTotalIncome(formValues);

      // Calculate taxes
      const federalTax = calculateFederalTax(totalIncome, formValues.filingStatus || 'SINGLE');
      const stateTax = calculateStateTax(totalIncome, formValues.state);
      const ficaTax = calculateFICATax(totalIncome);
      const totalTax = federalTax + stateTax + ficaTax;
      const netIncome = totalIncome - totalTax;
      const effectiveRate = totalIncome > 0 ? (totalTax / totalIncome) * 100 : 0;

      // Create updated values with proper typing
      const updatedValues: IncomeFormData = {
        ...formValues,
        annualIncome: totalIncome,
        additionalIncome: totalIncome - (Number(formValues.primaryIncome) || 0),
        federalTax,
        stateTax,
        ficaTax,
        totalTax,
        effectiveTaxRate: parseFloat(effectiveRate.toFixed(2)),
        afterTaxIncome: netIncome,
        lastUpdated: new Date().toISOString(),
        totalAnnualIncome: totalIncome,
        version: '1.0.0',
      };

      // Update form state
      setFormValues(updatedValues);

      try {
        // Call save handler
        await onSave(updatedValues);
        enqueueSnackbar('Income details saved successfully!', { variant: 'success' });
        onComplete();
      } catch (error) {
        console.error('Error saving income details:', error);
        enqueueSnackbar('Failed to save income details. Please try again.', {
          variant: 'error',
        });
      }
    } catch (error) {
      console.error('Error processing form:', error);
      enqueueSnackbar('An error occurred while processing the form.', {
        variant: 'error',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ p: 3 }}>
      <Grid container spacing={3}>
        {/* Primary Income Section */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Primary Income
              </Typography>

              <FormControl fullWidth margin="normal">
                <TextField
                  label="Primary Income"
                  name="primaryIncome"
                  type="number"
                  value={formValues.primaryIncome || ''}
                  onChange={handleInputChange}
                  error={!!formErrors.primaryIncome}
                  helperText={formErrors.primaryIncome}
                  InputProps={{
                    startAdornment: <InputAdornment position="start">$</InputAdornment>,
                  }}
                />
              </FormControl>

              <FormControl fullWidth margin="normal">
                <InputLabel>Frequency</InputLabel>
                <Select
                  name="primaryIncomeFrequency"
                  value={formValues.primaryIncomeFrequency || 'annually'}
                  onChange={handleSelectChange}
                  label="Frequency"
                >
                  <MenuItem value="weekly">Weekly</MenuItem>
                  <MenuItem value="biweekly">Bi-weekly</MenuItem>
                  <MenuItem value="monthly">Monthly</MenuItem>
                  <MenuItem value="annually">Annually</MenuItem>
                </Select>
              </FormControl>

              <FormControl fullWidth margin="normal">
                <InputLabel>Income Type</InputLabel>
                <Select
                  name="primaryIncomeType"
                  value={formValues.primaryIncomeType || 'salary'}
                  onChange={handleSelectChange}
                  label="Income Type"
                >
                  <MenuItem value="salary">Salary</MenuItem>
                  <MenuItem value="self_employed">Self-Employed</MenuItem>
                  <MenuItem value="investment">Investment</MenuItem>
                  <MenuItem value="pension">Pension</MenuItem>
                  <MenuItem value="social_security">Social Security</MenuItem>
                  <MenuItem value="other">Other</MenuItem>
                </Select>
              </FormControl>
            </CardContent>
          </Card>

          {/* Tax Information */}
          <Card sx={{ mt: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Tax Information
              </Typography>

              <FormControl fullWidth margin="normal">
                <InputLabel>Filing Status</InputLabel>
                <Select
                  name="filingStatus"
                  value={formValues.filingStatus || 'SINGLE'}
                  onChange={handleSelectChange}
                  label="Filing Status"
                  error={!!formErrors.filingStatus}
                >
                  <MenuItem value="SINGLE">Single</MenuItem>
                  <MenuItem value="MARRIED_JOINT">Married Filing Jointly</MenuItem>
                  <MenuItem value="HEAD_OF_HOUSEHOLD">Head of Household</MenuItem>
                  <MenuItem value="MARRIED_SEPARATE">Married Filing Separately</MenuItem>
                </Select>
                {formErrors.filingStatus && (
                  <FormHelperText error>{formErrors.filingStatus}</FormHelperText>
                )}
              </FormControl>

              <FormControl fullWidth margin="normal">
                <InputLabel>State</InputLabel>
                <Select
                  name="state"
                  value={formValues.state || ''}
                  onChange={handleSelectChange}
                  label="State"
                >
                  <MenuItem value="">Select State</MenuItem>
                  <MenuItem value="CA">California</MenuItem>
                  <MenuItem value="NY">New York</MenuItem>
                  <MenuItem value="TX">Texas</MenuItem>
                  {/* Add more states as needed */}
                </Select>
              </FormControl>

              <TextField
                fullWidth
                margin="normal"
                label="Number of Dependents"
                name="dependents"
                type="number"
                value={formValues.dependents || 0}
                onChange={handleInputChange}
              />

              <TextField
                fullWidth
                margin="normal"
                label="Pre-tax Deductions ($/year)"
                name="preTaxDeductions"
                type="number"
                value={formValues.preTaxDeductions || 0}
                onChange={handleInputChange}
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                }}
              />

              <TextField
                fullWidth
                margin="normal"
                label="Post-tax Deductions ($/year)"
                name="postTaxDeductions"
                type="number"
                value={formValues.postTaxDeductions || 0}
                onChange={handleInputChange}
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                }}
              />
            </CardContent>
          </Card>
        </Grid>

        {/* Additional Income Sources */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6">Additional Income Sources</Typography>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<AddIcon />}
                  onClick={handleAddIncomeSource}
                >
                  Add Source
                </Button>
              </Box>

              <TableContainer component={Paper}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Source</TableCell>
                      <TableCell align="right">Amount</TableCell>
                      <TableCell>Frequency</TableCell>
                      <TableCell>Type</TableCell>
                      <TableCell>Taxable</TableCell>
                      <TableCell>Pre-tax</TableCell>
                      <TableCell></TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {formValues.incomeSources?.map((source) => (
                      <TableRow key={source.id}>
                        <TableCell>
                          <TextField
                            size="small"
                            value={source.source}
                            onChange={(e) =>
                              handleIncomeSourceChange(source.id, 'source', e.target.value)
                            }
                          />
                        </TableCell>
                        <TableCell>
                          <TextField
                            size="small"
                            type="number"
                            value={source.amount}
                            onChange={(e) =>
                              handleIncomeSourceChange(source.id, 'amount', e.target.value)
                            }
                            InputProps={{
                              startAdornment: <InputAdornment position="start">$</InputAdornment>,
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          <Select
                            size="small"
                            value={source.frequency}
                            onChange={(e) =>
                              handleIncomeSourceChange(
                                source.id,
                                'frequency',
                                e.target.value as any
                              )
                            }
                          >
                            <MenuItem value="weekly">Weekly</MenuItem>
                            <MenuItem value="biweekly">Bi-weekly</MenuItem>
                            <MenuItem value="monthly">Monthly</MenuItem>
                            <MenuItem value="annually">Annually</MenuItem>
                          </Select>
                        </TableCell>
                        <TableCell>
                          <Select
                            size="small"
                            value={source.type}
                            onChange={(e) =>
                              handleIncomeSourceChange(source.id, 'type', e.target.value as any)
                            }
                          >
                            <MenuItem value="salary">Salary</MenuItem>
                            <MenuItem value="self_employed">Self-Employed</MenuItem>
                            <MenuItem value="investment">Investment</MenuItem>
                            <MenuItem value="pension">Pension</MenuItem>
                            <MenuItem value="social_security">Social Security</MenuItem>
                            <MenuItem value="other">Other</MenuItem>
                          </Select>
                        </TableCell>
                        <TableCell>
                          <Switch
                            checked={source.isTaxable !== false}
                            onChange={(e) =>
                              handleIncomeSourceChange(source.id, 'isTaxable', e.target.checked)
                            }
                            color="primary"
                          />
                        </TableCell>
                        <TableCell>
                          <Switch
                            checked={source.isPreTax === true}
                            onChange={(e) =>
                              handleIncomeSourceChange(source.id, 'isPreTax', e.target.checked)
                            }
                            color="primary"
                          />
                        </TableCell>
                        <TableCell>
                          <IconButton
                            size="small"
                            onClick={() => handleRemoveIncomeSource(source.id)}
                            color="error"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                    {(!formValues.incomeSources || formValues.incomeSources.length === 0) && (
                      <TableRow>
                        <TableCell colSpan={7} align="center" sx={{ py: 2 }}>
                          No additional income sources added
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>

          {/* Summary Section */}
          <Card sx={{ mt: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Income Summary
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography>Primary Income:</Typography>
                </Grid>
                <Grid item xs={6} textAlign="right">
                  <Typography fontWeight="bold">
                    {formatCurrency(Number(formValues.primaryIncome) || 0)} /{' '}
                    {formValues.primaryIncomeFrequency}
                  </Typography>
                </Grid>

                <Grid item xs={6}>
                  <Typography>Additional Income:</Typography>
                </Grid>
                <Grid item xs={6} textAlign="right">
                  <Typography fontWeight="bold">
                    {formatCurrency(formValues.additionalIncome || 0)} / year
                  </Typography>
                </Grid>

                <Grid item xs={12}>
                  <Divider sx={{ my: 1 }} />
                </Grid>

                <Grid item xs={6}>
                  <Typography variant="subtitle1">Total Annual Income:</Typography>
                </Grid>
                <Grid item xs={6} textAlign="right">
                  <Typography variant="subtitle1" fontWeight="bold">
                    {formatCurrency(formValues.annualIncome || 0)}
                  </Typography>
                </Grid>

                <Grid item xs={6}>
                  <Typography>Total Taxes:</Typography>
                </Grid>
                <Grid item xs={6} textAlign="right">
                  <Typography>
                    {formatCurrency(formValues.totalTax || 0)}
                    {formValues.effectiveTaxRate > 0 && (
                      <Typography component="span" variant="caption" color="text.secondary" ml={1}>
                        ({formValues.effectiveTaxRate.toFixed(2)}%)
                      </Typography>
                    )}
                  </Typography>
                </Grid>

                <Grid item xs={6}>
                  <Typography variant="subtitle1">After-Tax Income:</Typography>
                </Grid>
                <Grid item xs={6} textAlign="right">
                  <Typography variant="subtitle1" fontWeight="bold" color="primary">
                    {formatCurrency(formValues.afterTaxIncome || 0)}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Form Actions */}
      <Box mt={4} display="flex" justifyContent="space-between">
        <Button
          variant="outlined"
          onClick={onBack}
          disabled={isSubmitting}
          startIcon={<ArrowBack />}
        >
          Back
        </Button>
        <Button
          type="submit"
          variant="contained"
          color="primary"
          disabled={isSubmitting}
          startIcon={<Save />}
        >
          {isSubmitting ? 'Saving...' : 'Save & Continue'}
        </Button>
      </Box>
    </Box>
  );
};

export default IncomeDetails;
