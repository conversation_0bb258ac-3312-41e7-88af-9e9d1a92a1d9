// Types for IncomeDetails component
export type IncomeFrequency = 'weekly' | 'biweekly' | 'monthly' | 'annually';
export type IncomeType =
  | 'salary'
  | 'self_employed'
  | 'investment'
  | 'pension'
  | 'social_security'
  | 'other';
export type FilingStatusType =
  | 'SINGLE'
  | 'MARRIED_JOINT'
  | 'HEAD_OF_HOUSEHOLD'
  | 'MARRIED_SEPARATE';

export interface IncomeSource {
  id: string;
  source: string;
  amount: string;
  frequency: IncomeFrequency;
  type: IncomeType;
  description?: string;
  isTaxable?: boolean;
  isPreTax?: boolean;
}

export interface BudgetAllocation {
  needs: number;
  wants: number;
  savings: number;
  needsPercentage: number;
  wantsPercentage: number;
  savingsPercentage: number;
  status: 'healthy' | 'needs_adjustment' | 'needs_major_adjustment';
}

export interface IncomeFormData {
  primaryIncome: string;
  primaryIncomeFrequency: IncomeFrequency;
  primaryIncomeType: IncomeType;
  filingStatus: FilingStatusType;
  state: string;
  incomeSources: IncomeSource[];
  annualIncome: number;
  additionalIncome: number;
  totalAnnualIncome: number;
  federalTax: number;
  stateTax: number;
  ficaTax: number;
  totalTax: number;
  afterTaxIncome: number;
  effectiveTaxRate: number;
  budgetAllocation: BudgetAllocation;
  dependents: number;
  preTaxDeductions: number;
  postTaxDeductions: number;
  lastUpdated: string;
  version: string;
}

export interface IncomeDetailsProps {
  onComplete?: () => void;
  onSave?: (data: IncomeFormData) => Promise<void> | void;
  onBack?: () => void;
  data?: Partial<IncomeFormData>;
  updateData?: (data: Partial<IncomeFormData>) => void;
}

// Initial budget allocation values
export const initialBudgetAllocation: BudgetAllocation = {
  needs: 0,
  wants: 0,
  savings: 0,
  needsPercentage: 0,
  wantsPercentage: 0,
  savingsPercentage: 0,
  status: 'needs_adjustment',
};

// Initial form values
export const initialIncomeFormValues: IncomeFormData = {
  primaryIncome: '',
  primaryIncomeFrequency: 'annually',
  primaryIncomeType: 'salary',
  filingStatus: 'SINGLE',
  state: '',
  incomeSources: [],
  annualIncome: 0,
  additionalIncome: 0,
  totalAnnualIncome: 0,
  federalTax: 0,
  stateTax: 0,
  ficaTax: 0,
  totalTax: 0,
  afterTaxIncome: 0,
  effectiveTaxRate: 0,
  budgetAllocation: initialBudgetAllocation,
  dependents: 0,
  preTaxDeductions: 0,
  postTaxDeductions: 0,
  lastUpdated: new Date().toISOString(),
  version: '1.0.0',
};
