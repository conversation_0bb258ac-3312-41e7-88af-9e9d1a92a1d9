import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { formatCurrency, formatPercentage } from '../../../../utils/formatters';
import autoSave from '../../../../utils/autoSave';

interface AssetsProps {
  onComplete?: () => void;
  onBack?: () => void;
}

// Asset categories with liquidity and risk classifications
const assetCategories = [
  {
    id: 'cash',
    label: 'Cash & Equivalents',
    icon: '💵',
    liquidity: 'high',
    risk: 'low',
    fields: [
      { id: 'checking', label: 'Checking Accounts', liquidity: 'high', risk: 'low' },
      { id: 'savings', label: 'Savings Accounts', liquidity: 'high', risk: 'low' },
      { id: 'moneyMarket', label: 'Money Market Accounts', liquidity: 'high', risk: 'low' },
      { id: 'cd', label: 'Certificates of Deposit', liquidity: 'medium', risk: 'low' },
      { id: 'cash', label: 'Cash', liquidity: 'high', risk: 'low' },
      { id: 'treasuryBills', label: 'Treasury Bills', liquidity: 'medium', risk: 'low' },
    ],
  },
  {
    id: 'investments',
    label: 'Investments',
    icon: '📈',
    liquidity: 'medium',
    risk: 'medium',
    fields: [
      { id: 'retirement401k', label: '401(k) / 403(b)', liquidity: 'low', risk: 'medium' },
      { id: 'ira', label: 'IRA / Roth IRA', liquidity: 'low', risk: 'medium' },
      { id: 'brokerage', label: 'Brokerage Accounts', liquidity: 'medium', risk: 'medium' },
      { id: 'stocks', label: 'Individual Stocks', liquidity: 'medium', risk: 'high' },
      { id: 'bonds', label: 'Bonds', liquidity: 'medium', risk: 'low' },
      { id: 'mutualFunds', label: 'Mutual Funds', liquidity: 'medium', risk: 'medium' },
      { id: 'etfs', label: 'ETFs', liquidity: 'medium', risk: 'medium' },
      { id: 'crypto', label: 'Cryptocurrency', liquidity: 'medium', risk: 'high' },
      { id: 'options', label: 'Options/Derivatives', liquidity: 'medium', risk: 'high' },
    ],
  },
  {
    id: 'realEstate',
    label: 'Real Estate',
    icon: '🏠',
    liquidity: 'low',
    risk: 'medium',
    fields: [
      { id: 'primaryResidence', label: 'Primary Residence', liquidity: 'low', risk: 'medium' },
      { id: 'secondaryResidence', label: 'Secondary Residence', liquidity: 'low', risk: 'medium' },
      { id: 'rentalProperties', label: 'Rental Properties', liquidity: 'low', risk: 'medium' },
      { id: 'commercialProperty', label: 'Commercial Property', liquidity: 'low', risk: 'medium' },
      { id: 'reits', label: 'REITs', liquidity: 'medium', risk: 'medium' },
      { id: 'land', label: 'Land', liquidity: 'low', risk: 'medium' },
    ],
  },
  {
    id: 'retirement',
    label: 'Retirement',
    icon: '👵',
    liquidity: 'low',
    risk: 'medium',
    fields: [
      { id: 'pensionValue', label: 'Pension (Present Value)', liquidity: 'low', risk: 'low' },
      { id: 'annuities', label: 'Annuities', liquidity: 'low', risk: 'low' },
      {
        id: 'socialSecurityValue',
        label: 'Social Security (Present Value)',
        liquidity: 'low',
        risk: 'low',
      },
      { id: 'otherRetirement', label: 'Other Retirement Assets', liquidity: 'low', risk: 'medium' },
    ],
  },
  {
    id: 'personal',
    label: 'Personal Property',
    icon: '🚗',
    liquidity: 'low',
    risk: 'medium',
    fields: [
      { id: 'vehicles', label: 'Vehicles', liquidity: 'medium', risk: 'high' },
      { id: 'jewelry', label: 'Jewelry & Collectibles', liquidity: 'medium', risk: 'medium' },
      { id: 'furniture', label: 'Furniture & Appliances', liquidity: 'low', risk: 'high' },
      { id: 'electronics', label: 'Electronics & Equipment', liquidity: 'low', risk: 'high' },
      { id: 'art', label: 'Art & Antiques', liquidity: 'medium', risk: 'medium' },
    ],
  },
  {
    id: 'business',
    label: 'Business Assets',
    icon: '🏢',
    liquidity: 'low',
    risk: 'high',
    fields: [
      { id: 'businessOwnership', label: 'Business Ownership', liquidity: 'low', risk: 'high' },
      { id: 'privateEquity', label: 'Private Equity', liquidity: 'low', risk: 'high' },
      {
        id: 'intellectualProperty',
        label: 'Intellectual Property',
        liquidity: 'low',
        risk: 'high',
      },
      { id: 'businessEquipment', label: 'Business Equipment', liquidity: 'low', risk: 'medium' },
    ],
  },
  {
    id: 'other',
    label: 'Other Assets',
    icon: '🔹',
    liquidity: 'medium',
    risk: 'medium',
    fields: [
      { id: 'loans', label: 'Loans to Others', liquidity: 'low', risk: 'medium' },
      { id: 'insurance', label: 'Cash Value Life Insurance', liquidity: 'medium', risk: 'low' },
      { id: 'trusts', label: 'Trust Assets', liquidity: 'low', risk: 'medium' },
      { id: 'other', label: 'Other Assets', liquidity: 'medium', risk: 'medium' },
    ],
  },
];

const Assets: React.FC<AssetsProps> = ({ onComplete, onBack }) => {
  const { theme } = useTheme();
  const { data, updateData, updateNorthSectionStatus } = useFinancialCompass();
  const skipNextUpdate = useRef(false);

  // Create default assets object
  function createDefaultAssets() {
    const defaultAssets: Record<string, Record<string, number>> = {};

    assetCategories.forEach((category) => {
      defaultAssets[category.id] = {};
      category.fields.forEach((field) => {
        defaultAssets[category.id][field.id] = 0;
      });
    });

    return defaultAssets;
  }

  // Initialize assets state from context or with default values
  const [assets, setAssets] = useState<Record<string, Record<string, number>>>(() => {
    // Get assets from context or create default
    const contextAssets = (data.north?.assets as Record<string, Record<string, number>>) || {};
    const defaultAssets = createDefaultAssets();

    // Merge context assets with default assets to ensure all categories and fields exist
    const mergedAssets: Record<string, Record<string, number>> = {};

    // First, copy all default assets
    assetCategories.forEach((category) => {
      mergedAssets[category.id] = { ...defaultAssets[category.id] };

      // Then override with any existing values from context
      if (contextAssets[category.id]) {
        category.fields.forEach((field) => {
          if (contextAssets[category.id] && contextAssets[category.id][field.id] !== undefined) {
            mergedAssets[category.id][field.id] = contextAssets[category.id][field.id];
          }
        });
      }
    });

    return mergedAssets;
  });

  // Track if form is dirty (has unsaved changes)
  const [isDirty, setIsDirty] = useState(false);

  // Track if form is valid
  const [isValid, setIsValid] = useState(false);

  // Track validation errors
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Track if auto-save is in progress
  const [isSaving, setIsSaving] = useState(false);

  // Track if save indicator is visible
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Track active category
  const [activeCategory, setActiveCategory] = useState(assetCategories[0].id);

  // Track if asset allocation analysis is visible
  const [showAssetAllocation, setShowAssetAllocation] = useState(false);

  // Track net worth history
  const [netWorthHistory, setNetWorthHistory] = useState<{ date: string; amount: number }[]>([]);

  // Track if net worth trend is visible
  const [showNetWorthTrend, setShowNetWorthTrend] = useState(false);

  // Enhanced calculation with asset allocation analysis
  const calculateTotals = () => {
    // Calculate total for each category
    const categoryTotals: Record<string, number> = {};
    let totalAssets = 0;

    // Track assets by liquidity and risk
    const liquidityTotals: Record<string, number> = {
      high: 0,
      medium: 0,
      low: 0,
    };

    const riskTotals: Record<string, number> = {
      low: 0,
      medium: 0,
      high: 0,
    };

    // Calculate category totals and overall total
    Object.entries(assets).forEach(([categoryId, fields]) => {
      const category = assetCategories.find((c) => c.id === categoryId);
      let categoryTotal = 0;

      // Calculate field totals within category
      Object.entries(fields).forEach(([fieldId, value]) => {
        const amount = value || 0;
        categoryTotal += amount;

        // Find the field to check its liquidity and risk
        const field = category?.fields.find((f) => f.id === fieldId);
        if (field && amount > 0) {
          // Add to liquidity totals
          if (field.liquidity === 'high') {
            liquidityTotals.high += amount;
          } else if (field.liquidity === 'medium') {
            liquidityTotals.medium += amount;
          } else if (field.liquidity === 'low') {
            liquidityTotals.low += amount;
          }

          // Add to risk totals
          if (field.risk === 'low') {
            riskTotals.low += amount;
          } else if (field.risk === 'medium') {
            riskTotals.medium += amount;
          } else if (field.risk === 'high') {
            riskTotals.high += amount;
          }
        }
      });

      // Add to category total
      categoryTotals[categoryId] = categoryTotal;
      totalAssets += categoryTotal;
    });

    // Calculate percentages
    const categoryPercentages: Record<string, number> = {};
    Object.entries(categoryTotals).forEach(([categoryId, total]) => {
      categoryPercentages[categoryId] = totalAssets > 0 ? (total / totalAssets) * 100 : 0;
    });

    const liquidityPercentages = {
      high: totalAssets > 0 ? (liquidityTotals.high / totalAssets) * 100 : 0,
      medium: totalAssets > 0 ? (liquidityTotals.medium / totalAssets) * 100 : 0,
      low: totalAssets > 0 ? (liquidityTotals.low / totalAssets) * 100 : 0,
    };

    const riskPercentages = {
      low: totalAssets > 0 ? (riskTotals.low / totalAssets) * 100 : 0,
      medium: totalAssets > 0 ? (riskTotals.medium / totalAssets) * 100 : 0,
      high: totalAssets > 0 ? (riskTotals.high / totalAssets) * 100 : 0,
    };

    // Calculate liquidity ratio (high liquidity assets / total assets)
    const liquidityRatio = totalAssets > 0 ? liquidityTotals.high / totalAssets : 0;

    // Get total liabilities from context
    const totalLiabilities = parseFloat(data.north?.netWorthDetails?.totalLiabilities || '0');

    // Calculate net worth
    const netWorth = totalAssets - totalLiabilities;

    // Calculate debt-to-asset ratio
    const debtToAssetRatio = totalAssets > 0 ? totalLiabilities / totalAssets : 0;

    // Determine asset allocation health
    const assetAllocationHealth = {
      diversification: 'moderate',
      liquidity: liquidityRatio >= 0.2 ? 'good' : liquidityRatio >= 0.1 ? 'moderate' : 'poor',
      risk: riskPercentages.high <= 30 ? 'good' : riskPercentages.high <= 50 ? 'moderate' : 'poor',
      overall: 'moderate',
    };

    // Determine overall asset allocation health
    if (liquidityRatio >= 0.2 && riskPercentages.high <= 30) {
      assetAllocationHealth.overall = 'good';
    } else if (liquidityRatio < 0.1 || riskPercentages.high > 50) {
      assetAllocationHealth.overall = 'poor';
    }

    // Check diversification (no single category should be more than 50% except primary residence)
    const nonHomeCategories = Object.entries(categoryPercentages).filter(
      ([categoryId]) => categoryId !== 'realEstate'
    );

    const hasDiversification = !nonHomeCategories.some(([_, percentage]) => percentage > 50);

    if (hasDiversification) {
      assetAllocationHealth.diversification = 'good';
    } else {
      assetAllocationHealth.diversification = 'poor';
    }

    return {
      categoryTotals,
      categoryPercentages,
      totalAssets,
      liquidityTotals,
      liquidityPercentages,
      riskTotals,
      riskPercentages,
      liquidityRatio,
      netWorth,
      debtToAssetRatio,
      assetAllocationHealth,
    };
  };

  const {
    categoryTotals,
    categoryPercentages,
    totalAssets,
    liquidityTotals,
    liquidityPercentages,
    riskTotals,
    riskPercentages,
    netWorth,
    debtToAssetRatio,
    assetAllocationHealth,
  } = calculateTotals();

  // Handle asset value change
  const handleAssetChange = (categoryId: string, fieldId: string, value: string) => {
    const numericValue = value === '' ? 0 : parseFloat(value);

    if (isNaN(numericValue)) return;

    setAssets((prev) => ({
      ...prev,
      [categoryId]: {
        ...prev[categoryId],
        [fieldId]: numericValue,
      },
    }));

    setIsDirty(true);
  };

  // Enhanced form validation
  useEffect(() => {
    // Collect validation errors
    const errors: string[] = [];

    // Check if at least one asset is entered
    const hasAssets = Object.values(assets).some((category) =>
      Object.values(category).some((value) => value > 0)
    );

    if (!hasAssets) {
      errors.push('Please enter at least one asset value.');
    }

    // Check for negative values
    let hasNegativeValues = false;
    Object.entries(assets).forEach(([categoryId, fields]) => {
      Object.entries(fields).forEach(([fieldId, value]) => {
        if (value < 0) {
          hasNegativeValues = true;
          const category = assetCategories.find((c) => c.id === categoryId);
          const field = category?.fields.find((f) => f.id === fieldId);
          if (category && field) {
            errors.push(`${field.label} in ${category.label} cannot be negative.`);
          }
        }
      });
    });

    // Check liquidity ratio
    const liquidityRatio = totalAssets > 0 ? liquidityTotals.high / totalAssets : 0;
    if (liquidityRatio < 0.1 && totalAssets > 0) {
      errors.push(
        'Warning: Your liquid assets are below 10% of your total assets. Consider increasing your emergency fund or other liquid assets.'
      );
    }

    // Check risk allocation
    if (riskPercentages.high > 50 && totalAssets > 0) {
      errors.push(
        'Warning: More than 50% of your assets are in high-risk categories. Consider diversifying to reduce risk.'
      );
    }

    // Update validation state
    setValidationErrors(errors);
    setIsValid(hasAssets && !hasNegativeValues);
  }, [assets, totalAssets, liquidityTotals, riskPercentages]);

  // Load net worth history on component mount
  useEffect(() => {
    try {
      const historyString = localStorage.getItem('lifecompass_networth_history');
      if (historyString) {
        const history = JSON.parse(historyString);
        setNetWorthHistory(history);
      }
    } catch (error) {
      console.error('Error loading net worth history:', error);
    }
  }, []);

  // Auto-save when form is dirty with debounce
  useEffect(() => {
    if (isDirty && !skipNextUpdate.current) {
      const timer = setTimeout(() => {
        saveAssets();
      }, 1000);

      return () => clearTimeout(timer);
    }

    skipNextUpdate.current = false;
  }, [assets, isDirty]);

  // Enhanced save assets function with multiple persistence mechanisms
  const saveAssets = () => {
    setIsSaving(true);
    setShowSaveIndicator(true);

    // Calculate totals for saving
    const totals = calculateTotals();

    // Update net worth history
    const now = new Date();
    const newHistoryEntry = {
      date: now.toISOString(),
      amount: totals.netWorth,
    };

    // Add to history if it's a new day or significant change
    let updatedHistory = [...netWorthHistory];
    const lastEntry = updatedHistory[updatedHistory.length - 1];

    if (
      !lastEntry ||
      new Date(lastEntry.date).toDateString() !== now.toDateString() ||
      Math.abs((lastEntry.amount - totals.netWorth) / lastEntry.amount) > 0.05
    ) {
      updatedHistory.push(newHistoryEntry);

      // Keep only the last 12 entries
      if (updatedHistory.length > 12) {
        updatedHistory = updatedHistory.slice(-12);
      }

      setNetWorthHistory(updatedHistory);

      // Save history to localStorage
      try {
        localStorage.setItem('lifecompass_networth_history', JSON.stringify(updatedHistory));
      } catch (error) {
        console.error('Error saving net worth history:', error);
      }
    }

    // Create complete data object with calculated values
    const completeData = {
      ...assets,
      totalAssets: totals.totalAssets.toString(),
      categoryTotals: totals.categoryTotals,
      categoryPercentages: totals.categoryPercentages,
      liquidityTotals: totals.liquidityTotals,
      liquidityPercentages: totals.liquidityPercentages,
      riskTotals: totals.riskTotals,
      riskPercentages: totals.riskPercentages,
      assetAllocationHealth: totals.assetAllocationHealth,
      lastUpdated: new Date().toISOString(),
    };

    // Set flag to prevent auto-save from triggering an infinite loop
    skipNextUpdate.current = true;

    // 1. Update data in context
    updateData('north', 'assets', completeData);

    // 2. Save to auto-save service
    autoSave.save(completeData, 'assets', 'north');

    // Update net worth details
    const liabilities = data.north?.liabilities || {};
    const totalLiabilities = parseFloat(data.north?.netWorthDetails?.totalLiabilities || '0');

    const netWorthData = {
      totalAssets: totals.totalAssets.toString(),
      totalLiabilities: totalLiabilities.toString(),
      netWorth: totals.netWorth.toString(),
      debtToAssetRatio: totals.debtToAssetRatio.toString(),
      netWorthHistory: updatedHistory,
      lastUpdated: new Date().toISOString(),
    };

    // Update net worth in context
    updateData('north', 'netWorthDetails', netWorthData);

    // Save net worth to auto-save service
    autoSave.save(netWorthData, 'netWorthDetails', 'north');

    // 3. Also save to localStorage directly as a backup
    const storageKey = 'lifecompass_financial_compass';
    try {
      const storedData = localStorage.getItem(storageKey);
      if (storedData) {
        const parsedData = JSON.parse(storedData);

        // Make sure we're saving to the correct structure
        let updatedData;

        // Check if the data structure has a nested 'data' property
        if (parsedData.data) {
          updatedData = {
            ...parsedData,
            data: {
              ...parsedData.data,
              north: {
                ...parsedData.data.north,
                assets: completeData,
                netWorthDetails: netWorthData,
              },
            },
          };
        } else {
          // Direct structure without nested 'data'
          updatedData = {
            ...parsedData,
            north: {
              ...parsedData.north,
              assets: completeData,
              netWorthDetails: netWorthData,
            },
          };
        }

        localStorage.setItem(storageKey, JSON.stringify(updatedData));
      } else {
        // If no data exists yet, create a new structure
        const newData = {
          north: {
            assets: completeData,
            netWorthDetails: netWorthData,
          },
        };
        localStorage.setItem(storageKey, JSON.stringify(newData));
      }
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }

    // Mark form as not dirty
    setIsDirty(false);

    // Hide save indicator after 2 seconds
    setTimeout(() => {
      setIsSaving(false);
      setShowSaveIndicator(false);
    }, 2000);
  };

  // Enhanced form submission with validation
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Check if form is valid
    if (!isValid) {
      // Show validation errors
      alert('Please correct the following errors:\n' + validationErrors.join('\n'));
      return;
    }

    // Save final data
    saveAssets();

    // Mark section as completed
    updateNorthSectionStatus('assets_investments', true, false);

    // Call onComplete callback
    if (onComplete) {
      onComplete();
    }
  };

  // Handle back button
  const handleBack = () => {
    // Save current data before going back
    if (isDirty) {
      saveAssets();
    }

    // Call onBack callback
    if (onBack) {
      onBack();
    }
  };

  // Toggle asset allocation analysis visibility
  const toggleAssetAllocation = () => {
    setShowAssetAllocation(!showAssetAllocation);
  };

  // Toggle net worth trend visibility
  const toggleNetWorthTrend = () => {
    setShowNetWorthTrend(!showNetWorthTrend);
  };

  // Get asset allocation health status color
  const getHealthColor = (status: string) => {
    switch (status) {
      case 'good':
        return theme.colors.success.main;
      case 'moderate':
        return theme.colors.warning.main;
      case 'poor':
        return theme.colors.error.main;
      default:
        return theme.colors.text.secondary;
    }
  };

  // Get asset allocation recommendations
  const getAssetAllocationRecommendations = () => {
    const recommendations: string[] = [];

    // Check liquidity
    if (assetAllocationHealth.liquidity === 'poor') {
      recommendations.push(
        'Increase your emergency fund and other liquid assets to at least 10-20% of your total assets for financial security.'
      );
    }

    // Check risk
    if (assetAllocationHealth.risk === 'poor') {
      recommendations.push(
        'Reduce your exposure to high-risk assets by diversifying into more moderate and low-risk investments.'
      );
    }

    // Check diversification
    if (assetAllocationHealth.diversification === 'poor') {
      // Find the category with the highest percentage
      const highestCategory = Object.entries(categoryPercentages)
        .filter(([categoryId]) => categoryId !== 'realEstate')
        .sort(([_, a], [__, b]) => b - a)[0];

      if (highestCategory) {
        const [categoryId, percentage] = highestCategory;
        const category = assetCategories.find((c) => c.id === categoryId);
        if (category && percentage > 50) {
          recommendations.push(
            `Your portfolio is heavily concentrated in ${category.label} (${formatPercentage(percentage)}%). Consider diversifying into other asset classes.`
          );
        }
      }
    }

    // Check age-appropriate asset allocation
    const currentAge = parseInt(data.north?.personalInformation?.age || '0');
    if (currentAge > 0) {
      const recommendedStockPercentage = Math.max(0, 100 - currentAge);
      const actualStockPercentage = categoryPercentages['investments'] || 0;

      if (currentAge > 50 && actualStockPercentage > recommendedStockPercentage + 20) {
        recommendations.push(
          `Based on your age (${currentAge}), you may have too much exposure to stocks and other growth investments. Consider shifting some assets to more conservative investments.`
        );
      } else if (currentAge < 40 && actualStockPercentage < recommendedStockPercentage - 20) {
        recommendations.push(
          `Based on your age (${currentAge}), you may be too conservative with your investments. Consider increasing your exposure to growth assets like stocks.`
        );
      }
    }

    // If no specific recommendations, add general advice
    if (recommendations.length === 0) {
      if (assetAllocationHealth.overall === 'good') {
        recommendations.push(
          'Your asset allocation is well-balanced! Continue maintaining your diversified portfolio.'
        );
      } else {
        recommendations.push(
          'Consider following the general rule of having a diversified portfolio with a mix of cash, stocks, bonds, and other assets appropriate for your age and risk tolerance.'
        );
      }
    }

    return recommendations;
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Assets & Investments</Title>
        <Description theme={theme}>
          Track your assets and investments to understand your net worth and financial position.
        </Description>

        {/* Auto-save indicator */}
        <SaveIndicator visible={showSaveIndicator} theme={theme}>
          <SaveIcon>💾</SaveIcon>
          <SaveText>Saving...</SaveText>
        </SaveIndicator>

        {/* Asset allocation health indicator */}
        {totalAssets > 0 && (
          <AllocationHealthIndicator
            status={assetAllocationHealth.overall}
            onClick={toggleAssetAllocation}
            theme={theme}
          >
            <AllocationHealthIcon>
              {assetAllocationHealth.overall === 'good'
                ? '✅'
                : assetAllocationHealth.overall === 'moderate'
                  ? '⚠️'
                  : '❗'}
            </AllocationHealthIcon>
            <AllocationHealthText color={getHealthColor(assetAllocationHealth.overall)}>
              Asset Allocation:{' '}
              {assetAllocationHealth.overall.charAt(0).toUpperCase() +
                assetAllocationHealth.overall.slice(1)}
            </AllocationHealthText>
          </AllocationHealthIndicator>
        )}
      </Header>

      {/* Validation errors */}
      {validationErrors.length > 0 && (
        <ValidationErrors theme={theme}>
          <ValidationErrorsTitle>Please address the following:</ValidationErrorsTitle>
          <ValidationErrorsList>
            {validationErrors.map((error, index) => (
              <ValidationErrorItem key={index} isWarning={error.startsWith('Warning:')}>
                {error}
              </ValidationErrorItem>
            ))}
          </ValidationErrorsList>
        </ValidationErrors>
      )}

      <Content>
        <CategoriesNav theme={theme}>
          {assetCategories.map((category) => (
            <CategoryTab
              key={category.id}
              isActive={activeCategory === category.id}
              onClick={() => setActiveCategory(category.id)}
              theme={theme}
            >
              <CategoryIcon>{category.icon}</CategoryIcon>
              <CategoryLabel>{category.label}</CategoryLabel>
              <CategoryTotal>{formatCurrency(categoryTotals[category.id] || 0)}</CategoryTotal>
            </CategoryTab>
          ))}
        </CategoriesNav>

        <AssetForm onSubmit={handleSubmit}>
          <ActiveCategory theme={theme}>
            {assetCategories.find((c) => c.id === activeCategory)?.label}
          </ActiveCategory>

          <AssetItems theme={theme}>
            {assetCategories
              .find((c) => c.id === activeCategory)
              ?.fields.map((field) => (
                <AssetItem key={field.id} theme={theme}>
                  <AssetLabel theme={theme}>{field.label}</AssetLabel>
                  <AssetInput
                    type="number"
                    min="0"
                    step="0.01"
                    value={assets[activeCategory][field.id] || ''}
                    onChange={(e) => handleAssetChange(activeCategory, field.id, e.target.value)}
                    placeholder="0.00"
                    theme={theme}
                  />
                </AssetItem>
              ))}
          </AssetItems>

          <Summary theme={theme}>
            <SummaryTitle theme={theme}>Assets Summary</SummaryTitle>

            <SummaryItems>
              {Object.entries(categoryTotals).map(([categoryId, total]) => {
                if (total <= 0) return null;

                const category = assetCategories.find((c) => c.id === categoryId);
                const percentage = categoryPercentages[categoryId] || 0;
                return (
                  <SummaryItem key={categoryId} theme={theme}>
                    <SummaryItemLabel>
                      {category?.icon} {category?.label}
                    </SummaryItemLabel>
                    <SummaryItemValue>
                      {formatCurrency(total)} ({formatPercentage(percentage)}%)
                    </SummaryItemValue>
                  </SummaryItem>
                );
              })}

              <SummaryDivider theme={theme} />

              <NetWorthItem theme={theme}>
                <NetWorthLabel>Total Assets</NetWorthLabel>
                <NetWorthValue>{formatCurrency(totalAssets)}</NetWorthValue>
              </NetWorthItem>

              <NetWorthItem theme={theme}>
                <NetWorthLabel>Total Liabilities</NetWorthLabel>
                <NetWorthValue negative>
                  {formatCurrency(parseFloat(data.north?.netWorthDetails?.totalLiabilities || '0'))}
                </NetWorthValue>
              </NetWorthItem>

              <NetWorthItem theme={theme} isTotal>
                <NetWorthLabel>Net Worth</NetWorthLabel>
                <NetWorthValue isTotal>{formatCurrency(netWorth)}</NetWorthValue>
              </NetWorthItem>

              <AnalysisButton
                onClick={toggleAssetAllocation}
                theme={theme}
                isActive={showAssetAllocation}
              >
                {showAssetAllocation
                  ? 'Hide Asset Allocation Analysis'
                  : 'Show Asset Allocation Analysis'}
              </AnalysisButton>

              {netWorthHistory.length > 1 && (
                <AnalysisButton
                  onClick={toggleNetWorthTrend}
                  theme={theme}
                  isActive={showNetWorthTrend}
                >
                  {showNetWorthTrend ? 'Hide Net Worth Trend' : 'Show Net Worth Trend'}
                </AnalysisButton>
              )}
            </SummaryItems>

            {/* Asset Allocation Analysis */}
            {showAssetAllocation && (
              <AllocationAnalysis theme={theme}>
                <AllocationAnalysisTitle theme={theme}>
                  Asset Allocation Analysis
                </AllocationAnalysisTitle>

                <AllocationMetrics theme={theme}>
                  <AllocationMetric theme={theme}>
                    <AllocationMetricLabel>Liquidity</AllocationMetricLabel>
                    <AllocationMetricValue>
                      <AllocationMetricBar theme={theme}>
                        <AllocationMetricFill
                          width={liquidityPercentages.high}
                          color={theme.colors.success.main}
                          theme={theme}
                        />
                        <AllocationMetricFill
                          width={liquidityPercentages.medium}
                          color={theme.colors.warning.main}
                          theme={theme}
                        />
                        <AllocationMetricFill
                          width={liquidityPercentages.low}
                          color={theme.colors.error.main}
                          theme={theme}
                        />
                      </AllocationMetricBar>
                      <AllocationMetricLegend theme={theme}>
                        <LegendItem color={theme.colors.success.main} theme={theme}>
                          High: {formatPercentage(liquidityPercentages.high)}%
                        </LegendItem>
                        <LegendItem color={theme.colors.warning.main} theme={theme}>
                          Medium: {formatPercentage(liquidityPercentages.medium)}%
                        </LegendItem>
                        <LegendItem color={theme.colors.error.main} theme={theme}>
                          Low: {formatPercentage(liquidityPercentages.low)}%
                        </LegendItem>
                      </AllocationMetricLegend>
                    </AllocationMetricValue>
                    <AllocationMetricStatus status={assetAllocationHealth.liquidity} theme={theme}>
                      {assetAllocationHealth.liquidity.toUpperCase()}
                    </AllocationMetricStatus>
                  </AllocationMetric>

                  <AllocationMetric theme={theme}>
                    <AllocationMetricLabel>Risk</AllocationMetricLabel>
                    <AllocationMetricValue>
                      <AllocationMetricBar theme={theme}>
                        <AllocationMetricFill
                          width={riskPercentages.low}
                          color={theme.colors.success.main}
                          theme={theme}
                        />
                        <AllocationMetricFill
                          width={riskPercentages.medium}
                          color={theme.colors.warning.main}
                          theme={theme}
                        />
                        <AllocationMetricFill
                          width={riskPercentages.high}
                          color={theme.colors.error.main}
                          theme={theme}
                        />
                      </AllocationMetricBar>
                      <AllocationMetricLegend theme={theme}>
                        <LegendItem color={theme.colors.success.main} theme={theme}>
                          Low: {formatPercentage(riskPercentages.low)}%
                        </LegendItem>
                        <LegendItem color={theme.colors.warning.main} theme={theme}>
                          Medium: {formatPercentage(riskPercentages.medium)}%
                        </LegendItem>
                        <LegendItem color={theme.colors.error.main} theme={theme}>
                          High: {formatPercentage(riskPercentages.high)}%
                        </LegendItem>
                      </AllocationMetricLegend>
                    </AllocationMetricValue>
                    <AllocationMetricStatus status={assetAllocationHealth.risk} theme={theme}>
                      {assetAllocationHealth.risk.toUpperCase()}
                    </AllocationMetricStatus>
                  </AllocationMetric>

                  <AllocationMetric theme={theme}>
                    <AllocationMetricLabel>Diversification</AllocationMetricLabel>
                    <AllocationMetricValue>
                      <AllocationMetricBar theme={theme}>
                        {Object.entries(categoryPercentages).map(([categoryId, percentage]) => {
                          if (percentage <= 0) return null;
                          const category = assetCategories.find((c) => c.id === categoryId);
                          return (
                            <AllocationMetricFill
                              key={categoryId}
                              width={percentage}
                              color={`hsl(${parseInt(categoryId, 36) % 360}, 70%, 50%)`}
                              theme={theme}
                              tooltip={`${category?.label}: ${formatPercentage(percentage)}%`}
                            />
                          );
                        })}
                      </AllocationMetricBar>
                      <AllocationMetricLegend theme={theme}>
                        {Object.entries(categoryPercentages)
                          .filter(([_, percentage]) => percentage > 5)
                          .map(([categoryId, percentage]) => {
                            const category = assetCategories.find((c) => c.id === categoryId);
                            return (
                              <LegendItem
                                key={categoryId}
                                color={`hsl(${parseInt(categoryId, 36) % 360}, 70%, 50%)`}
                                theme={theme}
                              >
                                {category?.label}: {formatPercentage(percentage)}%
                              </LegendItem>
                            );
                          })}
                      </AllocationMetricLegend>
                    </AllocationMetricValue>
                    <AllocationMetricStatus
                      status={assetAllocationHealth.diversification}
                      theme={theme}
                    >
                      {assetAllocationHealth.diversification.toUpperCase()}
                    </AllocationMetricStatus>
                  </AllocationMetric>
                </AllocationMetrics>

                <AllocationRecommendations theme={theme}>
                  <AllocationRecommendationsTitle theme={theme}>
                    Recommendations
                  </AllocationRecommendationsTitle>
                  <AllocationRecommendationsList>
                    {getAssetAllocationRecommendations().map((recommendation, index) => (
                      <AllocationRecommendationItem key={index} theme={theme}>
                        {recommendation}
                      </AllocationRecommendationItem>
                    ))}
                  </AllocationRecommendationsList>
                </AllocationRecommendations>
              </AllocationAnalysis>
            )}

            {/* Net Worth Trend */}
            {showNetWorthTrend && netWorthHistory.length > 1 && (
              <NetWorthTrend theme={theme}>
                <NetWorthTrendTitle theme={theme}>Net Worth Trend</NetWorthTrendTitle>
                <NetWorthTrendChart theme={theme}>
                  {netWorthHistory.map((entry, index) => {
                    const maxAmount = Math.max(...netWorthHistory.map((e) => e.amount));
                    const height = maxAmount > 0 ? (entry.amount / maxAmount) * 100 : 0;
                    const date = new Date(entry.date);
                    return (
                      <NetWorthTrendBar
                        key={index}
                        height={height}
                        isPositive={entry.amount >= 0}
                        theme={theme}
                        tooltip={`${date.toLocaleDateString()}: ${formatCurrency(entry.amount)}`}
                      />
                    );
                  })}
                </NetWorthTrendChart>
                <NetWorthTrendLegend theme={theme}>
                  {netWorthHistory.map((entry, index) => {
                    if (index % Math.ceil(netWorthHistory.length / 4) !== 0) return null;
                    const date = new Date(entry.date);
                    return (
                      <NetWorthTrendDate key={index}>
                        {date.toLocaleDateString(undefined, { month: 'short', year: '2-digit' })}
                      </NetWorthTrendDate>
                    );
                  })}
                </NetWorthTrendLegend>
              </NetWorthTrend>
            )}
          </Summary>

          <ButtonContainer>
            <BackButton type="button" onClick={handleBack} theme={theme}>
              Back
            </BackButton>
            <SubmitButton type="submit" disabled={!isValid} theme={theme}>
              Save & Continue
            </SubmitButton>
          </ButtonContainer>
        </AssetForm>
      </Content>
    </Container>
  );
};

// Styled components - reusing styles from ExpenseDetails for consistency
const Container = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.secondary};
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 24px;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 16px;
  position: relative;
`;

const Title = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const Description = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const SaveIndicator = styled.div<{ visible: boolean; theme: any }>`
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  background-color: ${(props) => props.theme.colors.success.light};
  padding: 4px 8px;
  border-radius: 4px;
`;

const SaveIcon = styled.span`
  margin-right: 4px;
  font-size: 0.9rem;
`;

const SaveText = styled.span`
  font-size: 0.9rem;
`;

const Content = styled.div`
  display: flex;
  flex-direction: column;
`;

const CategoriesNav = styled.div<{ theme: any }>`
  display: flex;
  overflow-x: auto;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};

  &::-webkit-scrollbar {
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    background: ${(props) => props.theme.colors.background.tertiary};
  }

  &::-webkit-scrollbar-thumb {
    background-color: ${(props) => props.theme.colors.primary.light};
    border-radius: 4px;
  }
`;

const CategoryTab = styled.div<{ isActive: boolean; theme: any }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 16px;
  min-width: 100px;
  cursor: pointer;
  border-radius: 8px;
  background-color: ${(props) =>
    props.isActive ? props.theme.colors.primary.light : 'transparent'};
  color: ${(props) =>
    props.isActive ? props.theme.colors.primary.dark : props.theme.colors.text.secondary};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) =>
      props.isActive ? props.theme.colors.primary.light : props.theme.colors.background.hover};
  }

  &:not(:last-child) {
    margin-right: 8px;
  }
`;

const CategoryIcon = styled.div`
  font-size: 1.5rem;
  margin-bottom: 4px;
`;

const CategoryLabel = styled.div`
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 4px;
  text-align: center;
`;

const CategoryTotal = styled.div`
  font-size: 0.8rem;
  font-weight: 700;
`;

const AssetForm = styled.form`
  display: flex;
  flex-direction: column;
`;

const ActiveCategory = styled.h3<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 16px 0;
  font-size: 1.2rem;
`;

const AssetItems = styled.div<{ theme: any }>`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const AssetItem = styled.div<{ theme: any }>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: 4px;
  background-color: ${(props) => props.theme.colors.background.main};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.hover};
  }
`;

const AssetLabel = styled.label<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  font-size: 1rem;
  margin-right: 16px;
`;

const AssetInput = styled.input<{ theme: any }>`
  width: 120px;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid ${(props) => props.theme.colors.border.main};
  background-color: ${(props) => props.theme.colors.background.input};
  color: ${(props) => props.theme.colors.text.primary};
  font-size: 1rem;
  text-align: right;

  &:focus {
    outline: none;
    border-color: ${(props) => props.theme.colors.primary.main};
    box-shadow: 0 0 0 2px ${(props) => props.theme.colors.primary.light};
  }

  &::placeholder {
    color: ${(props) => props.theme.colors.text.placeholder};
  }

  /* Hide spinner for number input */
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  &[type='number'] {
    -moz-appearance: textfield;
  }
`;

const Summary = styled.div<{ theme: any }>`
  margin-bottom: 24px;
  padding: 16px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
`;

const SummaryTitle = styled.h4<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 8px;
`;

const SummaryItems = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const SummaryItem = styled.div<{ theme: any }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const SummaryItemLabel = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const SummaryItemValue = styled.div`
  font-weight: 500;
`;

const TotalItem = styled.div<{ theme: any }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1rem;
  font-weight: 700;
  color: ${(props) => props.theme.colors.text.primary};
  margin-top: 16px;
  padding-top: 8px;
  border-top: 1px solid ${(props) => props.theme.colors.border.main};
`;

const TotalLabel = styled.div``;

const TotalValue = styled.div``;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: 24px;
`;

const BackButton = styled.button<{ theme: any }>`
  padding: 12px 24px;
  border-radius: 4px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
  color: ${(props) => props.theme.colors.text.primary};
  border: none;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.hover};
  }
`;

const SubmitButton = styled.button<{ disabled: boolean; theme: any }>`
  padding: 12px 24px;
  border-radius: 4px;
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.text.onPrimary};
  border: none;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  opacity: ${(props) => (props.disabled ? 0.7 : 1)};
  font-weight: bold;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) =>
      props.disabled ? props.theme.colors.primary.main : props.theme.colors.primary.dark};
  }
`;

// Asset allocation health indicator
const AllocationHealthIndicator = styled.div<{ status: string; theme: any }>`
  display: flex;
  align-items: center;
  position: absolute;
  top: 0;
  right: 120px;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: ${(props) => props.theme.colors.background.paper};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
`;

const AllocationHealthIcon = styled.span`
  margin-right: 8px;
  font-size: 1rem;
`;

const AllocationHealthText = styled.span<{ color: string }>`
  font-size: 0.9rem;
  font-weight: 600;
  color: ${(props) => props.color};
`;

// Validation errors
const ValidationErrors = styled.div<{ theme: any }>`
  margin-bottom: 16px;
  padding: 12px 16px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.background.paper};
  border-left: 4px solid ${(props) => props.theme.colors.warning.main};
`;

const ValidationErrorsTitle = styled.h4`
  margin: 0 0 8px 0;
  font-size: 1rem;
  font-weight: 600;
`;

const ValidationErrorsList = styled.ul`
  margin: 0;
  padding-left: 20px;
`;

const ValidationErrorItem = styled.li<{ isWarning: boolean }>`
  margin-bottom: 4px;
  font-size: 0.9rem;
  color: ${(props) => (props.isWarning ? '#f59e0b' : '#ef4444')};
`;

// Summary components
const SummaryDivider = styled.div<{ theme: any }>`
  height: 1px;
  background-color: ${(props) => props.theme.colors.border.main};
  margin: 12px 0;
`;

const NetWorthItem = styled.div<{ theme: any; isTotal?: boolean }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: ${(props) => (props.isTotal ? '1.1rem' : '1rem')};
  font-weight: ${(props) => (props.isTotal ? 700 : 500)};
  color: ${(props) => props.theme.colors.text.primary};
  margin-top: ${(props) => (props.isTotal ? '8px' : '4px')};
  padding-top: ${(props) => (props.isTotal ? '8px' : '0')};
  border-top: ${(props) =>
    props.isTotal ? `1px solid ${props.theme.colors.border.main}` : 'none'};
`;

const NetWorthLabel = styled.div``;

const NetWorthValue = styled.div<{ negative?: boolean; isTotal?: boolean }>`
  color: ${(props) =>
    props.isTotal
      ? props.theme.colors.primary.main
      : props.negative
        ? props.theme.colors.error.main
        : props.theme.colors.text.primary};
`;

const AnalysisButton = styled.button<{ theme: any; isActive: boolean }>`
  width: 100%;
  padding: 10px;
  margin-top: 16px;
  border: none;
  border-radius: 4px;
  background-color: ${(props) =>
    props.isActive ? props.theme.colors.secondary.main : props.theme.colors.background.tertiary};
  color: ${(props) =>
    props.isActive ? props.theme.colors.text.onPrimary : props.theme.colors.text.primary};
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) =>
      props.isActive ? props.theme.colors.secondary.dark : props.theme.colors.background.hover};
  }
`;

// Asset allocation analysis components
const AllocationAnalysis = styled.div<{ theme: any }>`
  margin-top: 20px;
  padding: 16px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.background.paper};
`;

const AllocationAnalysisTitle = styled.h4<{ theme: any }>`
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  color: ${(props) => props.theme.colors.text.primary};
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 8px;
`;

const AllocationMetrics = styled.div<{ theme: any }>`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
`;

const AllocationMetric = styled.div<{ theme: any }>`
  padding: 12px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
`;

const AllocationMetricLabel = styled.div`
  font-weight: 600;
  margin-bottom: 8px;
  font-size: 1rem;
`;

const AllocationMetricValue = styled.div`
  margin-bottom: 12px;
`;

const AllocationMetricBar = styled.div<{ theme: any }>`
  height: 24px;
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  margin-bottom: 8px;
`;

const AllocationMetricFill = styled.div<{
  width: number;
  color: string;
  theme: any;
  tooltip?: string;
}>`
  height: 100%;
  width: ${(props) => props.width}%;
  background-color: ${(props) => props.color};
  position: relative;

  &:hover::after {
    content: '${(props) => props.tooltip || ''}';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    z-index: 10;
  }
`;

const AllocationMetricLegend = styled.div<{ theme: any }>`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  font-size: 0.8rem;
`;

const LegendItem = styled.div<{ color: string; theme: any }>`
  display: flex;
  align-items: center;
  margin-right: 8px;

  &::before {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 4px;
    background-color: ${(props) => props.color};
    border-radius: 2px;
  }
`;

const AllocationMetricStatus = styled.div<{ status: string; theme: any }>`
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 700;
  text-align: center;
  background-color: ${(props) =>
    props.status === 'good'
      ? props.theme.colors.success.light
      : props.status === 'moderate'
        ? props.theme.colors.warning.light
        : props.theme.colors.error.light};
  color: ${(props) =>
    props.status === 'good'
      ? props.theme.colors.success.main
      : props.status === 'moderate'
        ? props.theme.colors.warning.main
        : props.theme.colors.error.main};
`;

const AllocationRecommendations = styled.div<{ theme: any }>`
  padding: 16px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
`;

const AllocationRecommendationsTitle = styled.h5<{ theme: any }>`
  margin: 0 0 12px 0;
  font-size: 1rem;
  color: ${(props) => props.theme.colors.text.primary};
`;

const AllocationRecommendationsList = styled.ul`
  margin: 0;
  padding-left: 20px;
`;

const AllocationRecommendationItem = styled.li<{ theme: any }>`
  margin-bottom: 8px;
  font-size: 0.9rem;
  color: ${(props) => props.theme.colors.text.secondary};
  line-height: 1.4;
`;

// Net worth trend components
const NetWorthTrend = styled.div<{ theme: any }>`
  margin-top: 20px;
  padding: 16px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.background.paper};
`;

const NetWorthTrendTitle = styled.h4<{ theme: any }>`
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  color: ${(props) => props.theme.colors.text.primary};
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 8px;
`;

const NetWorthTrendChart = styled.div<{ theme: any }>`
  display: flex;
  align-items: flex-end;
  height: 150px;
  gap: 4px;
  padding: 0 0 8px 0;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
`;

const NetWorthTrendBar = styled.div<{
  height: number;
  isPositive: boolean;
  theme: any;
  tooltip: string;
}>`
  flex: 1;
  height: ${(props) => props.height}%;
  min-height: 1px;
  background-color: ${(props) =>
    props.isPositive ? props.theme.colors.success.main : props.theme.colors.error.main};
  border-radius: 2px 2px 0 0;
  position: relative;

  &:hover {
    opacity: 0.8;
  }

  &:hover::after {
    content: '${(props) => props.tooltip}';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    z-index: 10;
  }
`;

const NetWorthTrendLegend = styled.div<{ theme: any }>`
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 0.8rem;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const NetWorthTrendDate = styled.div`
  text-align: center;
`;

export default Assets;
