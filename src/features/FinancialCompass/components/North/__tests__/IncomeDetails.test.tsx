import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { ThemeProvider } from 'styled-components';
import IncomeDetails from '../IncomeDetails';

// Minimal theme object for testing
const testTheme = {
  colors: {
    primary: '#1976D2',
    error: '#D32F2F',
  },
  spacing: (factor: number) => `${8 * factor}px`,
};

// Mock the tax calculator and other utilities
jest.mock('../../../../../utils/taxCalculator', () => ({
  calculateDetailedTaxLiability: jest.fn().mockReturnValue({
    federalTax: 10000,
    stateTax: 5000,
    ficaTax: 7650,
    effectiveTaxRate: 22.65,
  }),
}));

jest.mock('../../../../../utils/expenseCalculator', () => ({
  convertToAnnual: jest.fn((amount, frequency) => {
    const multipliers = {
      weekly: 52,
      biweekly: 26,
      monthly: 12,
      annually: 1,
    };
    return amount * ((multipliers as Record<string, number>)[frequency] || 1);
  }),
}));

describe('IncomeDetails', () => {
  const mockOnSave = jest.fn();
  const mockOnComplete = jest.fn();
  const mockOnBack = jest.fn();
  const mockUpdateData = jest.fn();

  const renderComponent = (props = {}) => {
    return render(
      <ThemeProvider theme={testTheme as any}>
        <IncomeDetails
          onSave={mockOnSave}
          onComplete={mockOnComplete}
          onBack={mockOnBack}
          updateData={mockUpdateData}
          data={{}}
          {...props}
        />
      </ThemeProvider>
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the component with default values', () => {
    renderComponent();

    // Check if the main heading is rendered
    expect(screen.getByText('Income Details')).toBeInTheDocument();

    // Check if primary income input is rendered with default value
    const incomeInput = screen.getByLabelText('Amount ($)') as HTMLInputElement;
    expect(incomeInput).toBeInTheDocument();
    expect(incomeInput.value).toBe('');

    // Check if frequency select is rendered with default value
    const frequencySelect = screen.getByLabelText('Frequency') as HTMLSelectElement;
    expect(frequencySelect).toBeInTheDocument();
    expect(frequencySelect.value).toBe('annually');
  });

  it('validates required fields on submit', async () => {
    renderComponent();

    // Submit form without filling required fields
    fireEvent.click(screen.getByText('Save & Continue'));

    // Check for validation errors
    await waitFor(() => {
      expect(screen.getByText('This field is required')).toBeInTheDocument();
    });

    // Verify onSave was not called
    expect(mockOnSave).not.toHaveBeenCalled();
  });

  it('calculates taxes when income is entered', async () => {
    renderComponent();

    // Enter income amount
    const incomeInput = screen.getByLabelText('Amount ($)');
    fireEvent.change(incomeInput, { target: { value: '100000' } });

    // Select filing status
    const filingStatusSelect = screen.getByLabelText('Filing Status');
    fireEvent.change(filingStatusSelect, { target: { value: 'SINGLE' } });

    // Submit the form
    fireEvent.click(screen.getByText('Save & Continue'));

    // Verify tax calculations
    await waitFor(() => {
      expect(screen.getByText('$22,650.00')).toBeInTheDocument(); // Total tax
      expect(screen.getByText('$77,350.00')).toBeInTheDocument(); // After-tax income
    });

    // Verify onSave was called with correct data
    expect(mockOnSave).toHaveBeenCalledWith(
      expect.objectContaining({
        primaryIncome: '100000',
        filingStatus: 'SINGLE',
        federalTax: 10000,
        stateTax: 5000,
        ficaTax: 7650,
        totalTax: 22650,
        afterTaxIncome: 77350,
        effectiveTaxRate: 22.65,
      })
    );
  });

  it('handles frequency conversion correctly', async () => {
    renderComponent();

    // Enter weekly income
    const incomeInput = screen.getByLabelText('Amount ($)');
    fireEvent.change(incomeInput, { target: { value: '2000' } });

    // Change frequency to weekly
    const frequencySelect = screen.getByLabelText('Frequency');
    fireEvent.change(frequencySelect, { target: { value: 'weekly' } });

    // Select filing status
    const filingStatusSelect = screen.getByLabelText('Filing Status');
    fireEvent.change(filingStatusSelect, { target: { value: 'SINGLE' } });

    // Submit the form
    fireEvent.click(screen.getByText('Save & Continue'));

    // Verify calculations with weekly conversion
    await waitFor(() => {
      // 2000 * 52 = 104,000 annual income
      expect(mockOnSave).toHaveBeenCalledWith(
        expect.objectContaining({
          primaryIncome: '2000',
          primaryIncomeFrequency: 'weekly',
          annualIncome: 104000, // 2000 * 52
          totalAnnualIncome: 104000,
          federalTax: 10000,
          stateTax: 5000,
          ficaTax: 7650,
          totalTax: 22650,
          afterTaxIncome: 81350, // 104000 - 22650
          effectiveTaxRate: 22.65,
        })
      );
    });
  });

  it('handles pre-tax and post-tax deductions', async () => {
    renderComponent();

    // Enter income and deductions
    fireEvent.change(screen.getByLabelText('Amount ($)'), { target: { value: '100000' } });
    fireEvent.change(screen.getByLabelText('Filing Status'), { target: { value: 'SINGLE' } });
    fireEvent.change(screen.getByLabelText('Pre-Tax Deductions ($)'), {
      target: { value: '10000' },
    });
    fireEvent.change(screen.getByLabelText('Post-Tax Deductions ($)'), {
      target: { value: '5000' },
    });

    // Submit the form
    fireEvent.click(screen.getByText('Save & Continue'));

    // Verify calculations with deductions
    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalledWith(
        expect.objectContaining({
          preTaxDeductions: 10000,
          postTaxDeductions: 5000,
          totalAnnualIncome: 90000, // 100000 - 10000 (pre-tax deductions)
          afterTaxIncome: 67350, // 90000 - 22650 (tax) - 5000 (post-tax)
        })
      );
    });
  });

  it('displays error for invalid input', async () => {
    renderComponent();

    // Enter negative income
    const incomeInput = screen.getByLabelText('Amount ($)');
    fireEvent.change(incomeInput, { target: { value: '-1000' } });

    // Submit the form
    fireEvent.click(screen.getByText('Save & Continue'));

    // Check for validation error
    await waitFor(() => {
      expect(screen.getByText('Income must be a positive number')).toBeInTheDocument();
    });
  });

  it('handles additional income sources', async () => {
    renderComponent();

    // Click to add a new income source
    fireEvent.click(screen.getByText('Add Income Source'));

    // Fill in the new income source
    const sourceName = screen.getByLabelText('Source');
    const sourceAmount = screen.getAllByLabelText('Amount ($)')[1];
    const sourceFrequency = screen.getAllByLabelText('Frequency')[1];

    fireEvent.change(sourceName, { target: { value: 'Freelance Work' } });
    fireEvent.change(sourceAmount, { target: { value: '2000' } });
    fireEvent.change(sourceFrequency, { target: { value: 'monthly' } });

    // Submit the form
    fireEvent.click(screen.getByText('Save & Continue'));

    // Verify the income source was added
    await waitFor(() => {
      expect(screen.getByText('Freelance Work')).toBeInTheDocument();
    });
  });

  it('calculates total annual income correctly', async () => {
    renderComponent();

    // Enter primary income
    const incomeInput = screen.getByLabelText('Amount ($)');
    fireEvent.change(incomeInput, { target: { value: '100000' } });

    // Add an additional income source
    fireEvent.click(screen.getByText('Add Income Source'));

    const sourceAmount = screen.getAllByLabelText('Amount ($)')[1];
    const sourceFrequency = screen.getAllByLabelText('Frequency')[1];

    fireEvent.change(sourceAmount, { target: { value: '1000' } });
    fireEvent.change(sourceFrequency, { target: { value: 'monthly' } });

    // Submit the form
    fireEvent.click(screen.getByText('Save & Continue'));

    // Verify the total annual income calculation
    await waitFor(() => {
      // 100,000 (primary) + (1,000 * 12) = 112,000
      expect(screen.getByText('$112,000.00')).toBeInTheDocument();
    });
  });

  it('handles form submission with all required fields', async () => {
    renderComponent();

    // Fill in required fields
    const incomeInput = screen.getByLabelText('Amount ($)');
    const filingStatus = screen.getByLabelText('Filing Status');
    const stateSelect = screen.getByLabelText('State');

    await act(async () => {
      fireEvent.change(incomeInput, { target: { value: '85000' } });
      fireEvent.change(filingStatus, { target: { value: 'SINGLE' } });
      fireEvent.change(stateSelect, { target: { value: 'CA' } });

      // Submit the form
      fireEvent.click(screen.getByText('Save & Continue'));
    });

    // Verify the form was submitted successfully
    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalledWith(
        expect.objectContaining({
          primaryIncome: '85000',
          filingStatus: 'SINGLE',
          state: 'CA',
          // Add other expected fields with their default values
          primaryIncomeFrequency: 'annually',
          primaryIncomeType: 'salary',
          dependents: 0,
          preTaxDeductions: 0,
          postTaxDeductions: 0,
          annualIncome: 0,
          additionalIncome: 0,
          totalAnnualIncome: 0,
          federalTax: 0,
          stateTax: 0,
          ficaTax: 0,
          totalTax: 0,
          afterTaxIncome: 0,
          effectiveTaxRate: 0,
          budgetAllocation: expect.any(Object),
          lastUpdated: expect.any(String),
          version: expect.any(String),
        })
      );
    });
  });

  it('handles tab navigation', () => {
    renderComponent();

    // Click on the 'Taxes' tab
    fireEvent.click(screen.getByText('Taxes'));

    // Verify tax-related fields are visible
    expect(screen.getByText('Federal Tax')).toBeInTheDocument();
    expect(screen.getByText('State Tax')).toBeInTheDocument();

    // Click on the 'Budget' tab
    fireEvent.click(screen.getByText('Budget'));

    // Verify budget allocation is visible
    expect(screen.getByText('Budget Allocation')).toBeInTheDocument();
  });

  it('displays error for invalid input', async () => {
    renderComponent();

    // Enter negative income
    const incomeInput = screen.getByLabelText('Amount ($)');
    fireEvent.change(incomeInput, { target: { value: '-1000' } });

    // Blur the input to trigger validation
    fireEvent.blur(incomeInput);

    // Check for validation error
    await waitFor(() => {
      expect(screen.getByText('Income must be a positive number')).toBeInTheDocument();
    });

    // Check for validation error
    await waitFor(() => {
      expect(screen.getByText('Value cannot be negative')).toBeInTheDocument();
    });

    // Verify the value was corrected to 0
    expect(incomeInput.value).toBe('0');
  });

  it('renders without crashing', () => {
    render(<IncomeDetails />);
    expect(screen.getByText(/Income/i)).toBeInTheDocument();
  });

  it('renders TaxOptimizationPanel and CashFlowAnalysis', () => {
    renderComponent();
    expect(screen.getByText('Tax Optimization Strategies')).toBeInTheDocument();
    expect(screen.getByText('Cash Flow Analysis')).toBeInTheDocument();
  });
});
