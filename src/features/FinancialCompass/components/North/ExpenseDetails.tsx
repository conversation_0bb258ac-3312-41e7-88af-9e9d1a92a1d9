import React, { useState, useEffect, useRef, useCallback } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { formatCurrency, formatPercentage } from '../../../../utils/formatters';
import autoSave from '../../../../utils/autoSave';
import {
  convertToAnnual,
  convertToMonthly,
  calculateBudgetHealth as calculateBudgetHealthUtil,
  BUDGET_RULE,
} from '../../../../utils/expenseCalculator';

interface ExpenseDetailsProps {
  onComplete?: () => void;
  onBack?: () => void;
}

// Define expense categories with essential vs. discretionary classification
const expenseCategories = [
  {
    id: 'housing',
    label: 'Housing',
    icon: '🏠',
    type: 'essential',
    items: [
      { id: 'mortgage', label: 'Mortgage/Rent', isEssential: true },
      { id: 'propertyTax', label: 'Property Tax', isEssential: true },
      { id: 'homeInsurance', label: 'Home Insurance', isEssential: true },
      { id: 'maintenance', label: 'Maintenance', isEssential: true },
      { id: 'utilities', label: 'Utilities', isEssential: true },
      { id: 'internet', label: 'Internet/Cable', isEssential: false },
      { id: 'homeDecor', label: 'Home Decor/Furnishings', isEssential: false },
    ],
  },
  {
    id: 'transportation',
    label: 'Transportation',
    icon: '🚗',
    type: 'essential',
    items: [
      { id: 'carPayment', label: 'Car Payment', isEssential: true },
      { id: 'fuel', label: 'Fuel', isEssential: true },
      { id: 'insurance', label: 'Auto Insurance', isEssential: true },
      { id: 'maintenance', label: 'Maintenance', isEssential: true },
      { id: 'publicTransport', label: 'Public Transportation', isEssential: true },
      { id: 'rideshare', label: 'Rideshare Services', isEssential: false },
      { id: 'parking', label: 'Parking & Tolls', isEssential: false },
    ],
  },
  {
    id: 'food',
    label: 'Food',
    icon: '🍽️',
    type: 'essential',
    items: [
      { id: 'groceries', label: 'Groceries', isEssential: true },
      { id: 'diningOut', label: 'Dining Out', isEssential: false },
      { id: 'coffeeShops', label: 'Coffee Shops', isEssential: false },
      { id: 'foodDelivery', label: 'Food Delivery', isEssential: false },
    ],
  },
  {
    id: 'healthcare',
    label: 'Healthcare',
    icon: '🏥',
    type: 'essential',
    items: [
      { id: 'insurance', label: 'Health Insurance', isEssential: true },
      { id: 'outOfPocket', label: 'Out-of-Pocket Expenses', isEssential: true },
      { id: 'prescriptions', label: 'Prescriptions', isEssential: true },
      { id: 'dental', label: 'Dental Care', isEssential: true },
      { id: 'vision', label: 'Vision Care', isEssential: true },
      { id: 'gym', label: 'Gym Membership', isEssential: false },
      { id: 'supplements', label: 'Supplements/Vitamins', isEssential: false },
    ],
  },
  {
    id: 'personal',
    label: 'Personal',
    icon: '👤',
    type: 'discretionary',
    items: [
      { id: 'clothing', label: 'Clothing', isEssential: false },
      { id: 'entertainment', label: 'Entertainment', isEssential: false },
      { id: 'travel', label: 'Travel', isEssential: false },
      { id: 'gifts', label: 'Gifts', isEssential: false },
      { id: 'education', label: 'Education', isEssential: true },
      { id: 'personalCare', label: 'Personal Care', isEssential: true },
      { id: 'hobbies', label: 'Hobbies', isEssential: false },
    ],
  },
  {
    id: 'debt',
    label: 'Debt Payments',
    icon: '💳',
    type: 'essential',
    items: [
      { id: 'creditCards', label: 'Credit Cards', isEssential: true },
      { id: 'studentLoans', label: 'Student Loans', isEssential: true },
      { id: 'personalLoans', label: 'Personal Loans', isEssential: true },
      { id: 'medicalDebt', label: 'Medical Debt', isEssential: true },
      { id: 'otherDebt', label: 'Other Debt', isEssential: true },
    ],
  },
  {
    id: 'savings',
    label: 'Savings',
    icon: '💰',
    type: 'essential',
    items: [
      { id: 'emergencyFund', label: 'Emergency Fund', isEssential: true },
      { id: 'retirement', label: 'Retirement Contributions', isEssential: true },
      { id: 'investments', label: 'Other Investments', isEssential: false },
      { id: 'goals', label: 'Specific Goal Savings', isEssential: false },
    ],
  },
  {
    id: 'other',
    label: 'Other Expenses',
    icon: '📝',
    type: 'discretionary',
    items: [
      { id: 'subscriptions', label: 'Subscriptions', isEssential: false },
      { id: 'memberships', label: 'Memberships', isEssential: false },
      { id: 'pets', label: 'Pet Expenses', isEssential: true },
      { id: 'childcare', label: 'Childcare', isEssential: true },
      { id: 'charitable', label: 'Charitable Giving', isEssential: false },
      { id: 'miscellaneous', label: 'Miscellaneous', isEssential: false },
    ],
  },
];

const ExpenseDetails: React.FC<ExpenseDetailsProps> = ({ onComplete, onBack }) => {
  const { theme } = useTheme();
  const { data, updateData, updateNorthSectionStatus } = useFinancialCompass();
  const skipNextUpdate = useRef(false);

  // Initialize expenses state from context or with default values
  const expenseDetails = data.north?.expenseDetails || {};
  const [expenses, setExpenses] = useState<Record<string, Record<string, number>>>(
    typeof expenseDetails === 'object' &&
      'expenseCategories' in expenseDetails &&
      expenseDetails.expenseCategories
      ? (expenseDetails.expenseCategories as Record<string, Record<string, number>>)
      : createDefaultExpenses()
  );

  // Track if form is dirty (has unsaved changes)
  const [isDirty, setIsDirty] = useState(false);

  // Track if form is valid
  const [isValid, setIsValid] = useState(false);

  // Track validation errors
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Track if auto-save is in progress
  const [isSaving, setIsSaving] = useState(false);

  // Track if save indicator is visible
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Track active category
  const [activeCategory, setActiveCategory] = useState(expenseCategories[0].id);

  // Track if budget analysis is visible
  const [showBudgetAnalysis, setShowBudgetAnalysis] = useState(false);

  // Create default expenses object
  function createDefaultExpenses() {
    const defaultExpenses: Record<string, Record<string, number>> = {};

    expenseCategories.forEach((category) => {
      defaultExpenses[category.id] = {};
      category.items.forEach((item) => {
        defaultExpenses[category.id][item.id] = 0;
      });
    });

    return defaultExpenses;
  }

  // Define types for budget health status
  type BudgetHealthStatus = 'excellent' | 'good' | 'moderate' | 'poor' | 'danger';
  type BudgetHealthCategory = 'good' | 'warning' | 'danger';

  interface BudgetHealth {
    needs: BudgetHealthCategory;
    wants: BudgetHealthCategory;
    savings: BudgetHealthCategory;
    overall: BudgetHealthStatus;
    needsRatio: number;
    wantsRatio: number;
    savingsRatio: number;
  }

  // Define the return type for calculateTotals
  interface CalculateTotalsResult {
    categoryTotals: Record<string, number>;
    monthlyTotal: number;
    annualTotal: number;
    essentialTotal: number;
    discretionaryTotal: number;
    annualEssentialTotal: number;
    annualDiscretionaryTotal: number;
    essentialPercentage: number;
    discretionaryPercentage: number;
    expenseTypeBreakdown: Record<string, number>;
    budgetRule: {
      needsTarget: number;
      wantsTarget: number;
      savingsTarget: number;
      actualSavings: number;
      savingsPercentage: number;
      needsRatio: number;
      wantsRatio: number;
      savingsRatio: number;
    };
    budgetHealth: BudgetHealth;
  }

  // Calculate totals with enhanced breakdown using the expense calculator utility
  const calculateTotals = useCallback((): CalculateTotalsResult => {
    // Add empty dependency array to useCallback
    const calculate = (): CalculateTotalsResult => {
      // Calculate monthly total for each category
      const categoryTotals: Record<string, number> = {};
      let monthlyTotal = 0;
      let essentialTotal = 0;
      let discretionaryTotal = 0;

      // Track totals by expense type
      const expenseTypeBreakdown: Record<string, number> = {};

      // Calculate category totals
      Object.entries(expenses).forEach(([categoryId, items]) => {
        const category = expenseCategories.find((c) => c.id === categoryId);
        let categoryTotal = 0;

        // Calculate item totals within category
        Object.entries(items).forEach(([itemId, value]) => {
          const amount = value || 0;
          categoryTotal += amount;

          // Find the item to check if it's essential
          const item = category?.items.find((i) => i.id === itemId);
          if (item) {
            if (item.isEssential) {
              essentialTotal += amount;
            } else {
              discretionaryTotal += amount;
            }
          }
        });

        // Add to category total
        categoryTotals[categoryId] = categoryTotal;
        monthlyTotal += categoryTotal;

        // Add to expense type breakdown
        const expenseType = category?.type || 'other';
        expenseTypeBreakdown[expenseType] =
          (expenseTypeBreakdown[expenseType] || 0) + categoryTotal;
      });

      // Calculate annual totals using the utility function for consistency
      const annualTotal = convertToAnnual(monthlyTotal, 'monthly');
      const annualEssentialTotal = convertToAnnual(essentialTotal, 'monthly');
      const annualDiscretionaryTotal = convertToAnnual(discretionaryTotal, 'monthly');

      // Calculate percentages
      const essentialPercentage = monthlyTotal > 0 ? (essentialTotal / monthlyTotal) * 100 : 0;
      const discretionaryPercentage =
        monthlyTotal > 0 ? (discretionaryTotal / monthlyTotal) * 100 : 0;

      // Get actual savings amount from the savings category
      const actualSavings = categoryTotals['savings'] || 0;
      const savingsPercentage = monthlyTotal > 0 ? (actualSavings / monthlyTotal) * 100 : 0;

      // Calculate budget health using the utility function
      const budgetHealthResult = calculateBudgetHealthUtil(
        essentialTotal,
        discretionaryTotal,
        actualSavings,
        monthlyTotal
      );

      // Get budget rule targets from the utility
      const needsTarget = monthlyTotal * BUDGET_RULE.NEEDS_TARGET;
      const wantsTarget = monthlyTotal * BUDGET_RULE.WANTS_TARGET;
      const savingsTarget = monthlyTotal * BUDGET_RULE.SAVINGS_TARGET;

      // Calculate ratios for backward compatibility
      const needsRatio = essentialTotal / (needsTarget || 1);
      const wantsRatio = discretionaryTotal / (wantsTarget || 1);
      const savingsRatio = actualSavings / (savingsTarget || 1);

      // First, create a mutable object for budget health
      const budgetHealth: BudgetHealth = {
        needs:
          budgetHealthResult.needsStatus === 'on_target'
            ? 'good'
            : budgetHealthResult.needsStatus === 'over_target'
              ? 'warning'
              : 'danger',
        wants:
          budgetHealthResult.wantsStatus === 'on_target'
            ? 'good'
            : budgetHealthResult.wantsStatus === 'over_target'
              ? 'warning'
              : 'danger',
        savings:
          budgetHealthResult.savingsStatus === 'on_target'
            ? 'good'
            : budgetHealthResult.savingsStatus === 'over_target'
              ? 'warning'
              : 'danger',
        // Set initial overall status based on the utility result
        overall:
          budgetHealthResult.overallStatus === 'healthy'
            ? 'good'
            : budgetHealthResult.overallStatus === 'needs_adjustment'
              ? 'moderate'
              : 'danger',
        needsRatio,
        wantsRatio,
        savingsRatio,
      };

      // Update the overall status based on additional conditions
      if (budgetHealth.overall === 'good') {
        budgetHealth.overall = 'excellent';
      } else if (budgetHealth.needs === 'danger' || budgetHealth.savings === 'danger') {
        budgetHealth.overall = 'poor';
      }

      return {
        categoryTotals,
        monthlyTotal,
        annualTotal,
        essentialTotal,
        discretionaryTotal,
        annualEssentialTotal,
        annualDiscretionaryTotal,
        essentialPercentage,
        discretionaryPercentage,
        expenseTypeBreakdown,
        budgetRule: {
          needsTarget,
          wantsTarget,
          savingsTarget,
          actualSavings,
          savingsPercentage,
          needsRatio,
          wantsRatio,
          savingsRatio,
        },
        budgetHealth,
      };
    };

    return calculate();
  }, [expenses]); // Add expenses as a dependency

  // Calculate totals and destructure the result
  const {
    categoryTotals,
    monthlyTotal,
    annualTotal,
    essentialTotal,
    discretionaryTotal,
    essentialPercentage,
    discretionaryPercentage,
    budgetRule,
    budgetHealth,
  } = React.useMemo(() => calculateTotals(), [expenses]);

  // Handle expense change
  const handleExpenseChange = (categoryId: string, itemId: string, value: string) => {
    const numericValue = value === '' ? 0 : parseFloat(value);

    if (isNaN(numericValue)) return;

    setExpenses((prev) => ({
      ...prev,
      [categoryId]: {
        ...prev[categoryId],
        [itemId]: numericValue,
      },
    }));

    setIsDirty(true);
  };

  // Enhanced form validation
  useEffect(() => {
    // Collect validation errors
    const errors: string[] = [];

    // Check if at least one expense is entered
    const hasExpenses = Object.values(expenses).some((category) =>
      Object.values(category).some((value) => value > 0)
    );

    if (!hasExpenses) {
      errors.push('Please enter at least one expense amount.');
    }

    // Check for negative values
    let hasNegativeValues = false;
    Object.entries(expenses).forEach(([categoryId, items]) => {
      Object.entries(items).forEach(([itemId, value]) => {
        if (value < 0) {
          hasNegativeValues = true;
          const category = expenseCategories.find((c) => c.id === categoryId);
          const item = category?.items.find((i) => i.id === itemId);
          if (category && item) {
            errors.push(`${item.label} in ${category.label} cannot be negative.`);
          }
        }
      });
    });

    // Check if savings are too low (less than 10% of income)
    const savingsCategory = expenses['savings'];
    if (savingsCategory) {
      const totalSavings = Object.values(savingsCategory).reduce(
        (sum, value) => sum + (value || 0),
        0
      );
      const savingsPercentage = monthlyTotal > 0 ? (totalSavings / monthlyTotal) * 100 : 0;

      if (savingsPercentage < 10 && monthlyTotal > 0) {
        errors.push(
          'Warning: Your savings rate is below 10% of your total expenses. Consider increasing your savings.'
        );
      }
    }

    // Check if essential expenses are too high (more than 60% of total)
    if (essentialPercentage > 60 && monthlyTotal > 0) {
      errors.push(
        'Warning: Your essential expenses are above 60% of your total expenses. Consider reducing some essential costs if possible.'
      );
    }

    // Update validation state
    setValidationErrors(errors);
    setIsValid(hasExpenses && !hasNegativeValues);
  }, [expenses, monthlyTotal, essentialPercentage]);

  // Auto-save when form is dirty with debounce
  useEffect(() => {
    if (isDirty && !skipNextUpdate.current) {
      const timer = setTimeout(() => {
        saveExpenses();
      }, 1000);

      return () => clearTimeout(timer);
    }

    skipNextUpdate.current = false;
  }, [expenses, isDirty]);

  // Enhanced save expenses function with multiple persistence mechanisms
  const saveExpenses = () => {
    setIsSaving(true);
    setShowSaveIndicator(true);

    // Calculate totals for saving
    const totals = calculateTotals();

    // Create complete data object with calculated values
    const completeData = {
      expenseCategories: expenses,
      totalMonthlyExpenses: totals.monthlyTotal.toString(),
      totalAnnualExpenses: totals.annualTotal.toString(),
      essentialExpenses: totals.essentialTotal.toString(),
      discretionaryExpenses: totals.discretionaryTotal.toString(),
      essentialPercentage: totals.essentialPercentage.toString(),
      discretionaryPercentage: totals.discretionaryPercentage.toString(),
      budgetHealth: totals.budgetHealth,
      lastUpdated: new Date().toISOString(),
    };

    // Set flag to prevent auto-save from triggering an infinite loop
    skipNextUpdate.current = true;

    // 1. Update data in context
    updateData('north', 'expenseDetails', completeData);

    // 2. Save to auto-save service
    autoSave.save(completeData, 'expenseDetails', 'north');

    // 3. Also save to localStorage directly as a backup
    const storageKey = 'lifecompass_financial_compass';
    try {
      const storedData = localStorage.getItem(storageKey);
      if (storedData) {
        const parsedData = JSON.parse(storedData);

        // Make sure we're saving to the correct structure
        let updatedData;

        // Check if the data structure has a nested 'data' property
        if (parsedData.data) {
          updatedData = {
            ...parsedData,
            data: {
              ...parsedData.data,
              north: {
                ...parsedData.data.north,
                expenseDetails: completeData,
              },
            },
          };
        } else {
          // Direct structure without nested 'data'
          updatedData = {
            ...parsedData,
            north: {
              ...parsedData.north,
              expenseDetails: completeData,
            },
          };
        }

        localStorage.setItem(storageKey, JSON.stringify(updatedData));
      } else {
        // If no data exists yet, create a new structure
        const newData = {
          north: {
            expenseDetails: completeData,
          },
        };
        localStorage.setItem(storageKey, JSON.stringify(newData));
      }
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }

    // Mark form as not dirty
    setIsDirty(false);

    // Hide save indicator after 2 seconds
    setTimeout(() => {
      setIsSaving(false);
      setShowSaveIndicator(false);
    }, 2000);
  };

  // Enhanced form submission with validation
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Check if form is valid
    if (!isValid) {
      // Show validation errors
      alert('Please correct the following errors:\n' + validationErrors.join('\n'));
      return;
    }

    // Save final data
    saveExpenses();

    // Mark section as completed
    updateNorthSectionStatus('expense_details', true, false);

    // Call onComplete callback
    if (onComplete) {
      onComplete();
    }
  };

  // Handle back button
  const handleBack = () => {
    // Save current data before going back
    if (isDirty) {
      saveExpenses();
    }

    // Call onBack callback
    if (onBack) {
      onBack();
    }
  };

  // Toggle budget analysis visibility
  const toggleBudgetAnalysis = () => {
    setShowBudgetAnalysis(!showBudgetAnalysis);
  };

  // Get budget health status color
  const getBudgetHealthColor = (status: string) => {
    switch (status) {
      case 'excellent':
        return theme.colors.success.main;
      case 'good':
        return theme.colors.success.main;
      case 'moderate':
        return theme.colors.warning.main;
      case 'warning':
        return theme.colors.warning.main;
      case 'poor':
      case 'danger':
        return theme.colors.error.main;
      default:
        return theme.colors.text.secondary;
    }
  };

  // Get budget recommendations based on budget health
  const getBudgetRecommendations = () => {
    const recommendations: string[] = [];

    // Check savings rate
    if (budgetRule.savingsRatio < 1) {
      recommendations.push(
        'Consider increasing your savings to at least 20% of your income for long-term financial security.'
      );
    }

    // Check essential expenses
    if (budgetHealth.needs === 'danger') {
      recommendations.push(
        'Your essential expenses are higher than recommended. Look for ways to reduce housing, transportation, or other fixed costs.'
      );
    }

    // Check discretionary spending
    if (budgetHealth.wants === 'danger') {
      recommendations.push(
        'Your discretionary spending is higher than recommended. Consider cutting back on non-essential purchases.'
      );
    }

    // Check specific categories
    Object.entries(categoryTotals).forEach(([categoryId, total]) => {
      const category = expenseCategories.find((c) => c.id === categoryId);
      if (!category) return;

      // Check if housing is more than 30% of total
      if (categoryId === 'housing' && monthlyTotal > 0) {
        const housingPercentage = (total / monthlyTotal) * 100;
        if (housingPercentage > 30) {
          recommendations.push(
            `Housing costs (${formatPercentage(housingPercentage)}% of expenses) are higher than the recommended 30%. Consider downsizing or finding a roommate.`
          );
        }
      }

      // Check if transportation is more than 15% of total
      if (categoryId === 'transportation' && monthlyTotal > 0) {
        const transportationPercentage = (total / monthlyTotal) * 100;
        if (transportationPercentage > 15) {
          recommendations.push(
            `Transportation costs (${formatPercentage(transportationPercentage)}% of expenses) are higher than the recommended 15%. Consider using public transportation or carpooling.`
          );
        }
      }

      // Check if debt payments are more than 20% of total
      if (categoryId === 'debt' && monthlyTotal > 0) {
        const debtPercentage = (total / monthlyTotal) * 100;
        if (debtPercentage > 20) {
          recommendations.push(
            `Debt payments (${formatPercentage(debtPercentage)}% of expenses) are higher than the recommended 20%. Consider debt consolidation or accelerated repayment strategies.`
          );
        }
      }
    });

    // If no specific recommendations, add general advice
    if (recommendations.length === 0) {
      if (budgetHealth.overall === 'excellent') {
        recommendations.push(
          'Your budget is well-balanced! Continue maintaining your healthy financial habits.'
        );
      } else {
        recommendations.push(
          'Consider following the 50/30/20 rule: 50% for needs, 30% for wants, and 20% for savings and debt repayment.'
        );
      }
    }

    return recommendations;
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Expense Details</Title>
        <Description theme={theme}>
          Track your monthly expenses to understand your spending patterns and identify areas for
          potential savings.
        </Description>

        {/* Auto-save indicator */}
        <SaveIndicator visible={showSaveIndicator} theme={theme}>
          <SaveIcon>💾</SaveIcon>
          <SaveText>Saving...</SaveText>
        </SaveIndicator>

        {/* Budget health indicator */}
        {monthlyTotal > 0 && (
          <BudgetHealthIndicator
            status={budgetHealth.overall}
            onClick={toggleBudgetAnalysis}
            theme={theme}
          >
            <BudgetHealthIcon>
              {budgetHealth.overall === 'excellent'
                ? '🌟'
                : budgetHealth.overall === 'good'
                  ? '✅'
                  : budgetHealth.overall === 'moderate'
                    ? '⚠️'
                    : '❗'}
            </BudgetHealthIcon>
            <BudgetHealthText color={getBudgetHealthColor(budgetHealth.overall)}>
              Budget Health:{' '}
              {budgetHealth.overall.charAt(0).toUpperCase() + budgetHealth.overall.slice(1)}
            </BudgetHealthText>
          </BudgetHealthIndicator>
        )}
      </Header>

      {/* Validation errors */}
      {validationErrors.length > 0 && (
        <ValidationErrors theme={theme}>
          <ValidationErrorsTitle>Please address the following:</ValidationErrorsTitle>
          <ValidationErrorsList>
            {validationErrors.map((error, index) => (
              <ValidationErrorItem key={index} isWarning={error.startsWith('Warning:')}>
                {error}
              </ValidationErrorItem>
            ))}
          </ValidationErrorsList>
        </ValidationErrors>
      )}

      <Content>
        <CategoriesNav theme={theme}>
          {expenseCategories.map((category) => (
            <CategoryTab
              key={category.id}
              isActive={activeCategory === category.id}
              onClick={() => setActiveCategory(category.id)}
              theme={theme}
            >
              <CategoryIcon>{category.icon}</CategoryIcon>
              <CategoryLabel>{category.label}</CategoryLabel>
              <CategoryTotal>{formatCurrency(categoryTotals[category.id] || 0)}</CategoryTotal>
            </CategoryTab>
          ))}
        </CategoriesNav>

        <ExpenseForm onSubmit={handleSubmit}>
          <ActiveCategory theme={theme}>
            {expenseCategories.find((c) => c.id === activeCategory)?.label} Expenses
          </ActiveCategory>

          <ExpenseItems theme={theme}>
            {expenseCategories
              .find((c) => c.id === activeCategory)
              ?.items.map((item) => (
                <ExpenseItem key={item.id} theme={theme}>
                  <ExpenseLabel theme={theme}>{item.label}</ExpenseLabel>
                  <ExpenseInput
                    type="number"
                    min="0"
                    step="0.01"
                    value={expenses[activeCategory][item.id] || ''}
                    onChange={(e) => handleExpenseChange(activeCategory, item.id, e.target.value)}
                    placeholder="0.00"
                    theme={theme}
                  />
                </ExpenseItem>
              ))}
          </ExpenseItems>

          <Summary theme={theme}>
            <SummaryTitle theme={theme}>Monthly Expense Summary</SummaryTitle>

            <SummaryItems>
              {Object.entries(categoryTotals).map(([categoryId, total]) => {
                if (total <= 0) return null;

                const category = expenseCategories.find((c) => c.id === categoryId);
                return (
                  <SummaryItem key={categoryId} theme={theme}>
                    <SummaryItemLabel>
                      {category?.icon} {category?.label}
                    </SummaryItemLabel>
                    <SummaryItemValue>{formatCurrency(total)}</SummaryItemValue>
                  </SummaryItem>
                );
              })}

              <SummaryDivider theme={theme} />

              <TotalItem theme={theme}>
                <TotalLabel>Essential Expenses</TotalLabel>
                <TotalValue>
                  {formatCurrency(essentialTotal)} ({formatPercentage(essentialPercentage)}%)
                </TotalValue>
              </TotalItem>

              <TotalItem theme={theme}>
                <TotalLabel>Discretionary Expenses</TotalLabel>
                <TotalValue>
                  {formatCurrency(discretionaryTotal)} ({formatPercentage(discretionaryPercentage)}
                  %)
                </TotalValue>
              </TotalItem>

              <SummaryDivider theme={theme} />

              <TotalItem theme={theme}>
                <TotalLabel>Total Monthly Expenses</TotalLabel>
                <TotalValue>{formatCurrency(monthlyTotal)}</TotalValue>
              </TotalItem>

              <TotalItem theme={theme}>
                <TotalLabel>Total Annual Expenses</TotalLabel>
                <TotalValue>{formatCurrency(annualTotal)}</TotalValue>
              </TotalItem>

              <AnalysisButton
                onClick={toggleBudgetAnalysis}
                theme={theme}
                isActive={showBudgetAnalysis}
              >
                {showBudgetAnalysis ? 'Hide Budget Analysis' : 'Show Budget Analysis'}
              </AnalysisButton>
            </SummaryItems>

            {/* Budget Analysis Section */}
            {showBudgetAnalysis && (
              <BudgetAnalysis theme={theme}>
                <BudgetAnalysisTitle theme={theme}>
                  Budget Analysis (50/30/20 Rule)
                </BudgetAnalysisTitle>

                <BudgetRuleContainer theme={theme}>
                  <BudgetRuleItem theme={theme}>
                    <BudgetRuleLabel>Needs (50%)</BudgetRuleLabel>
                    <BudgetRuleValue>
                      Target: {formatCurrency(budgetRule.needsTarget)}
                    </BudgetRuleValue>
                    <BudgetRuleValue>
                      Actual: {formatCurrency(essentialTotal)} (
                      {formatPercentage(essentialPercentage)}%)
                    </BudgetRuleValue>
                    <BudgetRuleStatus status={budgetHealth.needs} theme={theme}>
                      {budgetHealth.needs.toUpperCase()}
                    </BudgetRuleStatus>
                  </BudgetRuleItem>

                  <BudgetRuleItem theme={theme}>
                    <BudgetRuleLabel>Wants (30%)</BudgetRuleLabel>
                    <BudgetRuleValue>
                      Target: {formatCurrency(budgetRule.wantsTarget)}
                    </BudgetRuleValue>
                    <BudgetRuleValue>
                      Actual: {formatCurrency(discretionaryTotal)} (
                      {formatPercentage(discretionaryPercentage)}%)
                    </BudgetRuleValue>
                    <BudgetRuleStatus status={budgetHealth.wants} theme={theme}>
                      {budgetHealth.wants.toUpperCase()}
                    </BudgetRuleStatus>
                  </BudgetRuleItem>

                  <BudgetRuleItem theme={theme}>
                    <BudgetRuleLabel>Savings (20%)</BudgetRuleLabel>
                    <BudgetRuleValue>
                      Target: {formatCurrency(budgetRule.savingsTarget)}
                    </BudgetRuleValue>
                    <BudgetRuleValue>
                      Actual: {formatCurrency(budgetRule.actualSavings)} (
                      {formatPercentage(budgetRule.savingsPercentage)}%)
                    </BudgetRuleValue>
                    <BudgetRuleStatus status={budgetHealth.savings} theme={theme}>
                      {budgetHealth.savings.toUpperCase()}
                    </BudgetRuleStatus>
                  </BudgetRuleItem>
                </BudgetRuleContainer>

                <BudgetRecommendations theme={theme}>
                  <BudgetRecommendationsTitle theme={theme}>
                    Recommendations
                  </BudgetRecommendationsTitle>
                  <BudgetRecommendationsList>
                    {getBudgetRecommendations().map((recommendation, index) => (
                      <BudgetRecommendationItem key={index} theme={theme}>
                        {recommendation}
                      </BudgetRecommendationItem>
                    ))}
                  </BudgetRecommendationsList>
                </BudgetRecommendations>
              </BudgetAnalysis>
            )}
          </Summary>

          <ButtonContainer>
            <BackButton type="button" onClick={handleBack} theme={theme}>
              Back
            </BackButton>
            <SubmitButton type="submit" disabled={!isValid} theme={theme}>
              Save & Continue
            </SubmitButton>
          </ButtonContainer>
        </ExpenseForm>
      </Content>
    </Container>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.secondary};
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 24px;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 16px;
  position: relative;
`;

const Title = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const Description = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const SaveIndicator = styled.div<{ visible: boolean; theme: any }>`
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  background-color: ${(props) => props.theme.colors.success.light};
  padding: 4px 8px;
  border-radius: 4px;
`;

const SaveIcon = styled.span`
  margin-right: 4px;
  font-size: 0.9rem;
`;

const SaveText = styled.span`
  font-size: 0.9rem;
`;

const Content = styled.div`
  display: flex;
  flex-direction: column;
`;

const CategoriesNav = styled.div<{ theme: any }>`
  display: flex;
  overflow-x: auto;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};

  &::-webkit-scrollbar {
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    background: ${(props) => props.theme.colors.background.tertiary};
  }

  &::-webkit-scrollbar-thumb {
    background-color: ${(props) => props.theme.colors.primary.light};
    border-radius: 4px;
  }
`;

const CategoryTab = styled.div<{ isActive: boolean; theme: any }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 16px;
  min-width: 100px;
  cursor: pointer;
  border-radius: 8px;
  background-color: ${(props) =>
    props.isActive ? props.theme.colors.primary.light : 'transparent'};
  color: ${(props) =>
    props.isActive ? props.theme.colors.primary.dark : props.theme.colors.text.secondary};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) =>
      props.isActive ? props.theme.colors.primary.light : props.theme.colors.background.hover};
  }

  &:not(:last-child) {
    margin-right: 8px;
  }
`;

const CategoryIcon = styled.div`
  font-size: 1.5rem;
  margin-bottom: 4px;
`;

const CategoryLabel = styled.div`
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 4px;
  text-align: center;
`;

const CategoryTotal = styled.div`
  font-size: 0.8rem;
  font-weight: 700;
`;

const ExpenseForm = styled.form`
  display: flex;
  flex-direction: column;
`;

const ActiveCategory = styled.h3<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 16px 0;
  font-size: 1.2rem;
`;

const ExpenseItems = styled.div<{ theme: any }>`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const ExpenseItem = styled.div<{ theme: any }>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: 4px;
  background-color: ${(props) => props.theme.colors.background.main};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.hover};
  }
`;

const ExpenseLabel = styled.label<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  font-size: 1rem;
  margin-right: 16px;
`;

const ExpenseInput = styled.input<{ theme: any }>`
  width: 120px;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid ${(props) => props.theme.colors.border.main};
  background-color: ${(props) => props.theme.colors.background.input};
  color: ${(props) => props.theme.colors.text.primary};
  font-size: 1rem;
  text-align: right;

  &:focus {
    outline: none;
    border-color: ${(props) => props.theme.colors.primary.main};
    box-shadow: 0 0 0 2px ${(props) => props.theme.colors.primary.light};
  }

  &::placeholder {
    color: ${(props) => props.theme.colors.text.placeholder};
  }

  /* Hide spinner for number input */
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  &[type='number'] {
    -moz-appearance: textfield;
  }
`;

const Summary = styled.div<{ theme: any }>`
  margin-bottom: 24px;
  padding: 16px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
`;

const SummaryTitle = styled.h4<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 8px;
`;

const SummaryItems = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const SummaryItem = styled.div<{ theme: any }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const SummaryItemLabel = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const SummaryItemValue = styled.div`
  font-weight: 500;
`;

const TotalItem = styled.div<{ theme: any }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1rem;
  font-weight: 700;
  color: ${(props) => props.theme.colors.text.primary};
  margin-top: 16px;
  padding-top: 8px;
  border-top: 1px solid ${(props) => props.theme.colors.border.main};
`;

const TotalLabel = styled.div``;

const TotalValue = styled.div``;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: 24px;
`;

const BackButton = styled.button<{ theme: any }>`
  padding: 12px 24px;
  border-radius: 4px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
  color: ${(props) => props.theme.colors.text.primary};
  border: none;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.hover};
  }
`;

const SubmitButton = styled.button<{ disabled: boolean; theme: any }>`
  padding: 12px 24px;
  border-radius: 4px;
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.text.onPrimary};
  border: none;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  opacity: ${(props) => (props.disabled ? 0.7 : 1)};
  font-weight: bold;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) =>
      props.disabled ? props.theme.colors.primary.main : props.theme.colors.primary.dark};
  }
`;

// Budget health indicator
const BudgetHealthIndicator = styled.div<{ status: string; theme: any }>`
  display: flex;
  align-items: center;
  position: absolute;
  top: 0;
  right: 120px;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: ${(props) => props.theme.colors.background.paper};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
`;

const BudgetHealthIcon = styled.span`
  margin-right: 8px;
  font-size: 1rem;
`;

const BudgetHealthText = styled.span<{ color: string }>`
  font-size: 0.9rem;
  font-weight: 600;
  color: ${(props) => props.color};
`;

// Validation errors
const ValidationErrors = styled.div<{ theme: any }>`
  margin-bottom: 16px;
  padding: 12px 16px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.background.paper};
  border-left: 4px solid ${(props) => props.theme.colors.warning.main};
`;

const ValidationErrorsTitle = styled.h4`
  margin: 0 0 8px 0;
  font-size: 1rem;
  font-weight: 600;
`;

const ValidationErrorsList = styled.ul`
  margin: 0;
  padding-left: 20px;
`;

const ValidationErrorItem = styled.li<{ isWarning: boolean }>`
  margin-bottom: 4px;
  font-size: 0.9rem;
  color: ${(props) => (props.isWarning ? '#f59e0b' : '#ef4444')};
`;

// Budget analysis components
const SummaryDivider = styled.div<{ theme: any }>`
  height: 1px;
  background-color: ${(props) => props.theme.colors.border.main};
  margin: 12px 0;
`;

const AnalysisButton = styled.button<{ theme: any; isActive: boolean }>`
  width: 100%;
  padding: 10px;
  margin-top: 16px;
  border: none;
  border-radius: 4px;
  background-color: ${(props) =>
    props.isActive ? props.theme.colors.secondary.main : props.theme.colors.background.tertiary};
  color: ${(props) =>
    props.isActive ? props.theme.colors.text.onPrimary : props.theme.colors.text.primary};
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) =>
      props.isActive ? props.theme.colors.secondary.dark : props.theme.colors.background.hover};
  }
`;

const BudgetAnalysis = styled.div<{ theme: any }>`
  margin-top: 20px;
  padding: 16px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.background.paper};
`;

const BudgetAnalysisTitle = styled.h4<{ theme: any }>`
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  color: ${(props) => props.theme.colors.text.primary};
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 8px;
`;

const BudgetRuleContainer = styled.div<{ theme: any }>`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
`;

const BudgetRuleItem = styled.div<{ theme: any }>`
  padding: 12px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
`;

const BudgetRuleLabel = styled.div`
  font-weight: 600;
  margin-bottom: 8px;
  font-size: 1rem;
`;

const BudgetRuleValue = styled.div`
  font-size: 0.9rem;
  margin-bottom: 4px;
`;

const BudgetRuleStatus = styled.div<{ status: string; theme: any }>`
  margin-top: 8px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 700;
  text-align: center;
  background-color: ${(props) =>
    props.status === 'good'
      ? props.theme.colors.success.light
      : props.status === 'warning'
        ? props.theme.colors.warning.light
        : props.theme.colors.error.light};
  color: ${(props) =>
    props.status === 'good'
      ? props.theme.colors.success.main
      : props.status === 'warning'
        ? props.theme.colors.warning.main
        : props.theme.colors.error.main};
`;

const BudgetRecommendations = styled.div<{ theme: any }>`
  padding: 16px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
`;

const BudgetRecommendationsTitle = styled.h5<{ theme: any }>`
  margin: 0 0 12px 0;
  font-size: 1rem;
  color: ${(props) => props.theme.colors.text.primary};
`;

const BudgetRecommendationsList = styled.ul`
  margin: 0;
  padding-left: 20px;
`;

const BudgetRecommendationItem = styled.li<{ theme: any }>`
  margin-bottom: 8px;
  font-size: 0.9rem;
  color: ${(props) => props.theme.colors.text.secondary};
  line-height: 1.4;
`;

export default ExpenseDetails;
