import React, { useContext } from 'react';
import { Card, CardContent, Typography, Box, Divider, Grid } from '@mui/material';
// Assume context or props for demo; replace with actual data source in real app
// import { FinancialCompassContext } from '../../context/FinancialCompassContext';

const mockIncome = 85000;
const mockExpenses = 42000;
const mockTrends = [
  { month: 'Jan', income: 7000, expenses: 3500 },
  { month: 'Feb', income: 7100, expenses: 3600 },
  { month: 'Mar', income: 6900, expenses: 3400 },
];

const CashFlowAnalysis: React.FC = () => {
  // const { incomeDetails, expenseDetails } = useContext(FinancialCompassContext);
  // const income = incomeDetails.totalAnnualIncome;
  // const expenses = expenseDetails.totalAnnualExpenses;
  // const trends = ...

  return (
    <Card variant="outlined">
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Cash Flow Analysis
        </Typography>
        <Divider sx={{ mb: 2 }} />
        <Grid container spacing={2}>
          <Grid item xs={6}>
            <Typography variant="subtitle2">Annual Income</Typography>
            <Typography variant="h5" color="primary">
              ${mockIncome.toLocaleString()}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="subtitle2">Annual Expenses</Typography>
            <Typography variant="h5" color="error">
              ${mockExpenses.toLocaleString()}
            </Typography>
          </Grid>
        </Grid>
        <Divider sx={{ my: 2 }} />
        <Typography variant="subtitle2" gutterBottom>
          Monthly Trends
        </Typography>
        <Box>
          {mockTrends.map((t) => (
            <Box key={t.month} sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography>{t.month}</Typography>
              <Typography color="primary">Income: ${t.income}</Typography>
              <Typography color="error">Expenses: ${t.expenses}</Typography>
            </Box>
          ))}
        </Box>
        {/* TODO: Replace mock data with real context/props and add charts */}
      </CardContent>
    </Card>
  );
};

export default CashFlowAnalysis;
