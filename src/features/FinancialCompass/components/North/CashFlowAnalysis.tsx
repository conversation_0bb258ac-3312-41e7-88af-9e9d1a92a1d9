import React, { useMemo } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';

interface CashFlowAnalysisProps {
  onClose?: () => void;
}

const CashFlowAnalysis: React.FC<CashFlowAnalysisProps> = ({ onClose }) => {
  const { theme } = useTheme();
  const { data } = useFinancialCompass();

  // Calculate real financial data
  const { annualIncome, monthlyIncome, annualExpenses, monthlyExpenses, monthlyCashFlow, savingsRate } = useMemo(() => {
    // Get income data
    const incomeData = data.north.incomeDetails;
    const primaryIncome = parseFloat(incomeData.primaryIncome as string) || 0;
    const frequency = incomeData.primaryIncomeFrequency;

    // Convert to annual income
    let annualIncome = 0;
    switch (frequency) {
      case 'weekly':
        annualIncome = primaryIncome * 52.14;
        break;
      case 'biweekly':
        annualIncome = primaryIncome * 26.07;
        break;
      case 'monthly':
        annualIncome = primaryIncome * 12;
        break;
      case 'annually':
        annualIncome = primaryIncome;
        break;
      default:
        annualIncome = primaryIncome * 12; // Default to monthly
    }

    // Add additional income sources
    incomeData.incomeSources.forEach(source => {
      const amount = source.amount || 0;
      switch (source.frequency) {
        case 'weekly':
          annualIncome += amount * 52.14;
          break;
        case 'biweekly':
          annualIncome += amount * 26.07;
          break;
        case 'monthly':
          annualIncome += amount * 12;
          break;
        case 'annually':
          annualIncome += amount;
          break;
        default:
          annualIncome += amount * 12;
      }
    });

    const monthlyIncome = annualIncome / 12;

    // Get expense data
    const expenseData = data.north.expenseDetails;
    const monthlyExpenses = parseFloat(expenseData.totalMonthlyExpenses as string) || 0;
    const annualExpenses = monthlyExpenses * 12;

    // Calculate cash flow and savings rate
    const monthlyCashFlow = monthlyIncome - monthlyExpenses;
    const savingsRate = monthlyIncome > 0 ? (monthlyCashFlow / monthlyIncome) * 100 : 0;

    return {
      annualIncome,
      monthlyIncome,
      annualExpenses,
      monthlyExpenses,
      monthlyCashFlow,
      savingsRate
    };
  }, [data.north.incomeDetails, data.north.expenseDetails]);

  // Helper functions
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (percentage: number) => {
    return `${percentage.toFixed(1)}%`;
  };

  return (
    <AnalysisContainer theme={theme}>
      {onClose && (
        <CloseButton onClick={onClose} theme={theme}>
          ×
        </CloseButton>
      )}

      <AnalysisHeader>
        <AnalysisTitle theme={theme}>Cash Flow Analysis</AnalysisTitle>
        <AnalysisSubtitle theme={theme}>
          Detailed breakdown of your income, expenses, and savings
        </AnalysisSubtitle>
      </AnalysisHeader>

      <MetricsGrid>
        <MetricCard theme={theme}>
          <MetricLabel theme={theme}>Annual Income</MetricLabel>
          <MetricValue positive={true} theme={theme}>
            {formatCurrency(annualIncome)}
          </MetricValue>
          <MetricSubtext theme={theme}>
            {formatCurrency(monthlyIncome)}/month
          </MetricSubtext>
        </MetricCard>

        <MetricCard theme={theme}>
          <MetricLabel theme={theme}>Annual Expenses</MetricLabel>
          <MetricValue positive={false} theme={theme}>
            {formatCurrency(annualExpenses)}
          </MetricValue>
          <MetricSubtext theme={theme}>
            {formatCurrency(monthlyExpenses)}/month
          </MetricSubtext>
        </MetricCard>

        <MetricCard theme={theme}>
          <MetricLabel theme={theme}>Monthly Cash Flow</MetricLabel>
          <MetricValue positive={monthlyCashFlow >= 0} theme={theme}>
            {formatCurrency(monthlyCashFlow)}
          </MetricValue>
          <MetricSubtext theme={theme}>
            {monthlyCashFlow >= 0 ? 'Surplus' : 'Deficit'}
          </MetricSubtext>
        </MetricCard>

        <MetricCard theme={theme}>
          <MetricLabel theme={theme}>Savings Rate</MetricLabel>
          <MetricValue positive={savingsRate >= 10} theme={theme}>
            {formatPercentage(savingsRate)}
          </MetricValue>
          <MetricSubtext theme={theme}>
            Target: 15-20%
          </MetricSubtext>
        </MetricCard>
      </MetricsGrid>

      <InsightsSection>
        <SectionTitle theme={theme}>Financial Insights</SectionTitle>
        <InsightsList>
          <InsightItem theme={theme} type={monthlyCashFlow >= 0 ? 'positive' : 'negative'}>
            <InsightIcon>{monthlyCashFlow >= 0 ? '✅' : '⚠️'}</InsightIcon>
            <InsightText>
              {monthlyCashFlow >= 0
                ? `You have a positive cash flow of ${formatCurrency(monthlyCashFlow)} per month.`
                : `You have a negative cash flow of ${formatCurrency(Math.abs(monthlyCashFlow))} per month.`}
            </InsightText>
          </InsightItem>

          <InsightItem theme={theme} type={savingsRate >= 15 ? 'positive' : savingsRate >= 10 ? 'warning' : 'negative'}>
            <InsightIcon>
              {savingsRate >= 15 ? '🎯' : savingsRate >= 10 ? '📊' : '📉'}
            </InsightIcon>
            <InsightText>
              {savingsRate >= 20
                ? 'Excellent savings rate! You\'re well-positioned for financial growth.'
                : savingsRate >= 15
                  ? 'Good savings rate. You\'re on track for your financial goals.'
                  : savingsRate >= 10
                    ? 'Moderate savings rate. Consider increasing savings to accelerate goals.'
                    : savingsRate > 0
                      ? 'Low savings rate. Look for ways to reduce expenses or increase income.'
                      : 'No savings currently. Focus on creating a positive cash flow first.'}
            </InsightText>
          </InsightItem>

          {annualIncome > 0 && (
            <InsightItem theme={theme} type="info">
              <InsightIcon>💡</InsightIcon>
              <InsightText>
                Based on your income of {formatCurrency(annualIncome)}, consider setting aside{' '}
                {formatCurrency(annualIncome * 0.15 / 12)} per month (15% savings rate) for optimal financial health.
              </InsightText>
            </InsightItem>
          )}
        </InsightsList>
      </InsightsSection>
    </AnalysisContainer>
  );
};

// Styled Components
const AnalysisContainer = styled.div<{ theme: any }>`
  background: ${({ theme }) => theme.colors?.paper || '#ffffff'};
  border-radius: 12px;
  padding: 24px;
  box-shadow: ${({ theme }) => theme.shadows?.lg || '0 4px 6px rgba(0, 0, 0, 0.1)'};
  max-width: 800px;
  margin: 0 auto;
  position: relative;
`;

const CloseButton = styled.button<{ theme: any }>`
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  padding: 4px;
  border-radius: 4px;

  &:hover {
    background: ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  }
`;

const AnalysisHeader = styled.div`
  text-align: center;
  margin-bottom: 32px;
`;

const AnalysisTitle = styled.h2<{ theme: any }>`
  color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
`;

const AnalysisSubtitle = styled.p<{ theme: any }>`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin: 0;
  font-size: 16px;
  opacity: 0.8;
`;

const MetricsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
`;

const MetricCard = styled.div<{ theme: any }>`
  background: ${({ theme }) => theme.colors?.background || '#f5f5f5'};
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 8px;
  padding: 20px;
  text-align: center;
`;

const MetricLabel = styled.div<{ theme: any }>`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  opacity: 0.8;
`;

const MetricValue = styled.div<{ positive: boolean; theme: any }>`
  color: ${({ positive, theme }) =>
    positive
      ? theme.colors?.success || '#4caf50'
      : theme.colors?.error || '#f44336'
  };
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
`;

const MetricSubtext = styled.div<{ theme: any }>`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  font-size: 12px;
  opacity: 0.6;
`;

const InsightsSection = styled.div`
  margin-top: 32px;
`;

const SectionTitle = styled.h3<{ theme: any }>`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
`;

const InsightsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const InsightItem = styled.div<{ theme: any; type: 'positive' | 'negative' | 'warning' | 'info' }>`
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border-radius: 8px;
  background: ${({ type, theme }) => {
    switch (type) {
      case 'positive':
        return theme.colors?.success ? `${theme.colors.success}10` : '#4caf5010';
      case 'negative':
        return theme.colors?.error ? `${theme.colors.error}10` : '#f4433610';
      case 'warning':
        return theme.colors?.warning ? `${theme.colors.warning}10` : '#ff980010';
      default:
        return theme.colors?.info ? `${theme.colors.info}10` : '#2196f310';
    }
  }};
  border-left: 3px solid ${({ type, theme }) => {
    switch (type) {
      case 'positive':
        return theme.colors?.success || '#4caf50';
      case 'negative':
        return theme.colors?.error || '#f44336';
      case 'warning':
        return theme.colors?.warning || '#ff9800';
      default:
        return theme.colors?.info || '#2196f3';
    }
  }};
`;

const InsightIcon = styled.div`
  font-size: 20px;
  flex-shrink: 0;
  margin-top: 2px;
`;

const InsightText = styled.div<{ theme?: any }>`
  color: ${({ theme }) => theme?.colors?.text || '#000000'};
  font-size: 14px;
  line-height: 1.5;
`;

export default CashFlowAnalysis;
