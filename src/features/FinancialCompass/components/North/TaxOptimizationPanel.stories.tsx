import React from 'react';
import { Story, Meta } from '@storybook/react';
import { TaxOptimizationPanel } from './TaxOptimizationPanel';
import { Box } from '@mui/material';

export default {
  title: 'FinancialCompass/North/TaxOptimizationPanel',
  component: TaxOptimizationPanel,
  parameters: {
    layout: 'fullscreen',
    backgrounds: {
      default: 'light',
    },
  },
} as Meta;

const Template: Story<React.ComponentProps<typeof TaxOptimizationPanel>> = (args) => (
  <Box p={4}>
    <TaxOptimizationPanel {...args} />
  </Box>
);

export const Default = Template.bind({});
Default.args = {
  userData: {
    income: 120000,
    filingStatus: 'single',
    age: 35,
    retirementAccounts: {
      traditionalIRA: 2000,
      rothIRA: 0,
      employer401k: 15000,
      employer401kMatch: 5000,
    },
    investments: {
      taxableBrokerage: 75000,
      capitalGains: 15000,
    },
    deductions: {
      itemized: 12000,
      standard: 13850,
    },
    dependents: 0,
    hsaEligible: true,
    hsaContribution: 2000,
  },
  onStrategySelect: (strategyId) => {
    console.log('Selected strategy:', strategyId);
  },
};

export const MarriedWithDependents = Template.bind({});
MarriedWithDependents.args = {
  userData: {
    income: 220000,
    filingStatus: 'married',
    age: 42,
    retirementAccounts: {
      traditionalIRA: 5000,
      rothIRA: 3000,
      employer401k: 19500,
      employer401kMatch: 8000,
    },
    investments: {
      taxableBrokerage: 150000,
      capitalGains: 25000,
    },
    deductions: {
      itemized: 25000,
      standard: 27700,
    },
    dependents: 2,
    hsaEligible: true,
    hsaContribution: 3850,
  },
};

export const EmptyState = Template.bind({});
EmptyState.args = {
  userData: {
    income: 0,
    filingStatus: 'single',
    age: 25,
    retirementAccounts: {},
    investments: {
      taxableBrokerage: 0,
      capitalGains: 0,
    },
    deductions: {
      itemized: 0,
      standard: 13850,
    },
    dependents: 0,
    hsaEligible: false,
    hsaContribution: 0,
  },
};

// Add a story to demonstrate loading states
export const Loading = Template.bind({});
Loading.args = {
  ...Default.args,
  isLoading: true,
};

// Add a story to demonstrate error states
export const ErrorState = Template.bind({});
ErrorState.args = {
  ...Default.args,
  error: 'Failed to load tax optimization strategies. Please try again later.',
};
