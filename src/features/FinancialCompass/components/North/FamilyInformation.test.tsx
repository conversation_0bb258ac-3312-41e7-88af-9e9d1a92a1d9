import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ThemeProvider from '../../../../theme/ThemeProvider';
import FamilyInformation from './FamilyInformation';
import { FinancialCompassProvider } from '../../context/FinancialCompassContext';
import { GuidedJourneyProvider } from '../../../GuidedJourney/context/GuidedJourneyContext';

// Mock the useGuidedJourney hook
jest.mock('../../../GuidedJourney/context/GuidedJourneyContext', () => ({
  ...jest.requireActual('../../../GuidedJourney/context/GuidedJourneyContext'),
  useGuidedJourney: () => ({
    askQuestion: jest.fn().mockResolvedValue('This is a mock answer'),
  }),
}));

// Test component wrapper with providers
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ThemeProvider initialMode="light" initialSeason="spring">
      <FinancialCompassProvider>
        <GuidedJourneyProvider>{children}</GuidedJourneyProvider>
      </FinancialCompassProvider>
    </ThemeProvider>
  );
};

describe('FamilyInformation', () => {
  test('renders the component with all form fields', () => {
    render(
      <TestWrapper>
        <FamilyInformation />
      </TestWrapper>
    );

    // Check for section titles
    expect(screen.getByText('Family Information')).toBeInTheDocument();
    expect(screen.getByText('Marital Status')).toBeInTheDocument();

    // Check for form fields
    expect(screen.getByLabelText('Marital Status')).toBeInTheDocument();

    // Check for add family member button
    expect(screen.getByText('+ Add Family Member')).toBeInTheDocument();

    // Check for submit button
    expect(screen.getByRole('button', { name: /save and continue/i })).toBeInTheDocument();
  });

  test('shows spouse information when marital status is married', () => {
    render(
      <TestWrapper>
        <FamilyInformation />
      </TestWrapper>
    );

    // Initially, spouse information should not be visible
    expect(screen.queryByText('Spouse/Partner Information')).not.toBeInTheDocument();

    // Change marital status to married
    fireEvent.change(screen.getByLabelText('Marital Status'), { target: { value: 'married' } });

    // Now spouse information should be visible
    expect(screen.getByText('Spouse/Partner Information')).toBeInTheDocument();
    expect(screen.getByLabelText('First Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Last Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Date of Birth')).toBeInTheDocument();
    expect(screen.getByLabelText('Occupation')).toBeInTheDocument();
  });

  test('adds and removes family members', async () => {
    render(
      <TestWrapper>
        <FamilyInformation />
      </TestWrapper>
    );

    // Initially, no family members should be visible
    expect(screen.queryByText('Relationship')).not.toBeInTheDocument();

    // Add a family member
    fireEvent.click(screen.getByText('+ Add Family Member'));

    // Now family member fields should be visible
    expect(screen.getByLabelText('Relationship')).toBeInTheDocument();
    expect(screen.getByLabelText('First Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Last Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Date of Birth')).toBeInTheDocument();
    expect(screen.getByText('Dependent')).toBeInTheDocument();

    // Fill out family member information
    fireEvent.change(screen.getByLabelText('Relationship'), { target: { value: 'child' } });
    fireEvent.change(screen.getByLabelText('First Name'), { target: { value: 'Jane' } });
    fireEvent.change(screen.getByLabelText('Last Name'), { target: { value: 'Doe' } });
    fireEvent.change(screen.getByLabelText('Date of Birth'), { target: { value: '2010-01-01' } });
    fireEvent.click(screen.getByText('Dependent'));

    // Add another family member
    fireEvent.click(screen.getByText('+ Add Family Member'));

    // Now there should be two sets of family member fields
    expect(screen.getAllByLabelText('Relationship').length).toBe(2);

    // Remove the first family member
    fireEvent.click(screen.getAllByLabelText('Remove family member')[0]);

    // Now there should be one set of family member fields
    expect(screen.getAllByLabelText('Relationship').length).toBe(1);
  });

  test('calls onComplete when form is submitted', async () => {
    const handleComplete = jest.fn();

    render(
      <TestWrapper>
        <FamilyInformation onComplete={handleComplete} />
      </TestWrapper>
    );

    // Submit the form
    fireEvent.click(screen.getByRole('button', { name: /save and continue/i }));

    // Wait for the save process to complete
    await waitFor(() => {
      expect(handleComplete).toHaveBeenCalledTimes(1);
    });
  });

  test('shows save indicator after auto-save', async () => {
    render(
      <TestWrapper>
        <FamilyInformation />
      </TestWrapper>
    );

    // Change marital status to trigger auto-save
    fireEvent.change(screen.getByLabelText('Marital Status'), { target: { value: 'married' } });

    // Wait for the save indicator to appear
    await waitFor(() => {
      expect(screen.getByText('✓ Saved')).toBeInTheDocument();
    });

    // Wait for the save indicator to disappear
    await waitFor(
      () => {
        expect(screen.getByText('✓ Saved')).not.toBeVisible();
      },
      { timeout: 3000 }
    );
  });

  test('calls onSave with form data when saving', async () => {
    const handleSave = jest.fn();

    render(
      <TestWrapper>
        <FamilyInformation onSave={handleSave} />
      </TestWrapper>
    );

    // Change marital status to trigger auto-save
    fireEvent.change(screen.getByLabelText('Marital Status'), { target: { value: 'married' } });

    // Wait for the save function to be called
    await waitFor(() => {
      expect(handleSave).toHaveBeenCalledTimes(1);
      expect(handleSave).toHaveBeenCalledWith(
        expect.objectContaining({
          maritalStatus: 'married',
        })
      );
    });
  });
});
