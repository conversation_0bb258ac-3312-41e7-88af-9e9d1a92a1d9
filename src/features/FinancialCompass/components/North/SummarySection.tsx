/**
 * North Direction Summary Section
 *
 * This component displays a summary of the North Direction journey,
 * showing the user's current financial position, trends, alerts, actions, and guidance.
 * Enhanced with improved financial health score calculation and transparency.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { formatCurrency } from '../../../../utils/formatters';
import {
  TrendsSection,
  AlertsSection,
  ActionsSection,
  GuidanceSection,
  TrendItem,
  AlertItem,
  ActionItem,
  GuidanceItem,
} from '../shared/SummaryComponents';
import CashFlowAnalysis from './CashFlowAnalysis';
import { downloadComprehensivePDF } from '../../../../utils/enhancedPdfExport';
import {
  calculateCashFlowHealthScore,
  calculateDebtManagementScore,
  calculateEmergencyFundScore,
  calculateNetWorthScore,
  calculateDetailedFinancialHealthScore,
  FinancialHealthCategory,
  FinancialHealthScoreResult,
} from '../../../../utils/financialHealthCalculator';

interface SummarySectionProps {
  onComplete?: () => void;
  onBack?: () => void;
}

const SummarySection: React.FC<SummarySectionProps> = ({ onComplete, onBack }) => {
  const { theme } = useTheme();
  const { data, northSections } = useFinancialCompass();
  const [showCashFlowAnalysis, setShowCashFlowAnalysis] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // Extract relevant data from the context
  const personalInfo = data.north?.personalInformation || {};
  const incomeDetails = data.north?.incomeDetails || {};
  const expenseDetails = data.north?.expenseDetails || {};
  const assets = data.north?.assets || {};
  const liabilities = data.north?.liabilities || {};

  // Calculate key financial metrics

  // Calculate primary income (monthly)
  let primaryMonthlyIncome = 0;
  if (incomeDetails.primaryIncome) {
    const primaryIncome = parseFloat(incomeDetails.primaryIncome || '0');
    if (!isNaN(primaryIncome)) {
      switch (incomeDetails.primaryIncomeFrequency) {
        case 'annual':
          primaryMonthlyIncome = primaryIncome / 12;
          break;
        case 'monthly':
          primaryMonthlyIncome = primaryIncome;
          break;
        case 'biweekly':
          primaryMonthlyIncome = (primaryIncome * 26) / 12;
          break;
        case 'weekly':
          primaryMonthlyIncome = (primaryIncome * 52) / 12;
          break;
        default:
          primaryMonthlyIncome = primaryIncome / 12; // Default to annual
      }
    }
  }

  // Ensure we have income sources data
  const incomeSources = Array.isArray((incomeDetails as any)?.incomeSources)
    ? (incomeDetails as any)?.incomeSources
    : [];

  // Calculate additional monthly income from income sources
  let additionalMonthlyIncome = 0;
  if (incomeSources.length > 0) {
    additionalMonthlyIncome = incomeSources.reduce((total: number, source: any) => {
      const amount = parseFloat(source.amount || '0');
      if (isNaN(amount)) return total;

      switch (source.frequency) {
        case 'annual':
          return total + amount / 12;
        case 'monthly':
          return total + amount;
        case 'biweekly':
          return total + (amount * 26) / 12;
        case 'weekly':
          return total + (amount * 52) / 12;
        case 'one-time':
          return total + amount / 12; // Spread one-time income over a year
        default:
          return total + amount;
      }
    }, 0);
  }

  // Calculate total monthly income
  // First try to use the pre-calculated value, then calculate it ourselves
  const totalMonthlyIncome =
    parseFloat((incomeDetails as any)?.totalMonthlyIncome || '0') ||
    primaryMonthlyIncome + additionalMonthlyIncome;

  console.log(
    'North Summary - Total Monthly Income:',
    totalMonthlyIncome,
    'Primary:',
    primaryMonthlyIncome,
    'Additional:',
    additionalMonthlyIncome
  );

  const totalMonthlyExpenses = parseFloat((expenseDetails as any)?.totalMonthlyExpenses || '0');
  const monthlyCashFlow = totalMonthlyIncome - totalMonthlyExpenses;
  const annualCashFlow = monthlyCashFlow * 12;
  const totalAssets = parseFloat((assets as any)?.totalAssets || '0');
  const totalLiabilities = parseFloat((liabilities as any)?.totalLiabilities || '0');
  const netWorth = totalAssets - totalLiabilities;

  // Calculate financial ratios
  const savingsRate = totalMonthlyIncome > 0 ? (monthlyCashFlow / totalMonthlyIncome) * 100 : 0;
  const debtToIncomeRatio =
    totalMonthlyIncome > 0 ? (totalLiabilities / (totalMonthlyIncome * 12)) * 100 : 0;
  const emergencyFundMonths =
    totalMonthlyExpenses > 0
      ? parseFloat((assets as any)?.emergencyFund || '0') / totalMonthlyExpenses
      : 0;

  // Calculate financial health score using the enhanced calculator
  const [healthScoreResult, setHealthScoreResult] = useState<FinancialHealthScoreResult>({
    overallScore: 0,
    categories: [],
    status: 'Calculating...',
    strengths: [],
    weaknesses: [],
    recommendations: [],
  });

  // Calculate financial health score
  useEffect(() => {
    // Calculate cash flow health score
    const cashFlowCategory: FinancialHealthCategory = {
      id: 'cash_flow',
      name: 'Cash Flow',
      score: calculateCashFlowHealthScore(savingsRate, monthlyCashFlow, totalMonthlyIncome),
      weight: 0.3,
      metrics: [
        {
          id: 'savings_rate',
          name: 'Savings Rate',
          score:
            savingsRate >= 20
              ? 100
              : savingsRate >= 15
                ? 80
                : savingsRate >= 10
                  ? 60
                  : savingsRate >= 5
                    ? 40
                    : 20,
          value: `${Math.round(savingsRate)}%`,
          target: '15-20%',
          description: 'Percentage of income saved',
        },
        {
          id: 'cash_flow',
          name: 'Monthly Cash Flow',
          score: monthlyCashFlow > 0 ? 100 : 0,
          value: formatCurrency(monthlyCashFlow),
          target: 'Positive',
          description: 'Monthly income minus expenses',
        },
      ],
    };

    // Calculate debt management score
    const hasHighInterestDebt = false; // This should be determined from the data
    const hasMortgage = false; // This should be determined from the data
    const hasStudentLoans = false; // This should be determined from the data

    const debtScore = calculateDebtManagementScore(
      debtToIncomeRatio,
      hasMortgage,
      hasHighInterestDebt,
      hasStudentLoans
    );

    const debtManagementCategory: FinancialHealthCategory = {
      id: 'debt_management',
      name: 'Debt Management',
      score: debtScore,
      weight: 0.25,
      metrics: [
        {
          id: 'debt_to_income',
          name: 'Debt-to-Income Ratio',
          score:
            debtToIncomeRatio <= 36
              ? 100
              : debtToIncomeRatio <= 43
                ? 70
                : debtToIncomeRatio <= 50
                  ? 40
                  : 20,
          value: `${Math.round(debtToIncomeRatio)}%`,
          target: '< 36%',
          description: 'Annual debt payments divided by annual income',
        },
      ],
    };

    // Calculate emergency fund score
    const hasStableIncome = true; // This should be determined from the data

    const emergencyScore = calculateEmergencyFundScore(emergencyFundMonths, hasStableIncome);

    const emergencyFundCategory: FinancialHealthCategory = {
      id: 'emergency_fund',
      name: 'Emergency Fund',
      score: emergencyScore,
      weight: 0.25,
      metrics: [
        {
          id: 'emergency_fund_months',
          name: 'Emergency Fund Coverage',
          score:
            emergencyFundMonths >= 6
              ? 100
              : emergencyFundMonths >= 3
                ? 70
                : emergencyFundMonths >= 1
                  ? 40
                  : 20,
          value: `${emergencyFundMonths.toFixed(1)} months`,
          target: '3-6 months',
          description: 'Months of expenses covered by emergency fund',
        },
      ],
    };

    // Calculate net worth score
    const age = (personalInfo as any)?.age || 30;
    const annualIncome = totalMonthlyIncome * 12;

    const netWorthScore = calculateNetWorthScore(netWorth, annualIncome, age);

    const netWorthCategory: FinancialHealthCategory = {
      id: 'net_worth',
      name: 'Net Worth',
      score: netWorthScore,
      weight: 0.2,
      metrics: [
        {
          id: 'net_worth_to_income',
          name: 'Net Worth to Income Ratio',
          score:
            netWorth > annualIncome * 5
              ? 100
              : netWorth > annualIncome * 2
                ? 80
                : netWorth > annualIncome
                  ? 60
                  : netWorth > 0
                    ? 40
                    : 20,
          value: formatCurrency(netWorth),
          target: `${formatCurrency(annualIncome * 2)} - ${formatCurrency(annualIncome * 5)}`,
          description: 'Total assets minus total liabilities',
        },
      ],
    };

    // Calculate weighted scores for each category
    cashFlowCategory.weightedScore = cashFlowCategory.score * cashFlowCategory.weight;
    debtManagementCategory.weightedScore =
      debtManagementCategory.score * debtManagementCategory.weight;
    emergencyFundCategory.weightedScore =
      emergencyFundCategory.score * emergencyFundCategory.weight;
    netWorthCategory.weightedScore = netWorthCategory.score * netWorthCategory.weight;

    // Calculate overall financial health score
    const result = calculateDetailedFinancialHealthScore([
      cashFlowCategory,
      debtManagementCategory,
      emergencyFundCategory,
      netWorthCategory,
    ]);

    setHealthScoreResult(result);
  }, [
    savingsRate,
    monthlyCashFlow,
    totalMonthlyIncome,
    debtToIncomeRatio,
    emergencyFundMonths,
    netWorth,
    personalInfo,
  ]);

  const financialHealthScore = healthScoreResult.overallScore;

  // Get financial health status
  const getFinancialHealthStatus = () => healthScoreResult.status;

  // Generate trends
  const generateTrends = (): TrendItem[] => {
    return [
      {
        id: 'cash_flow',
        label: 'Monthly Cash Flow',
        value: formatCurrency(monthlyCashFlow),
        change: 3, // This would ideally be calculated from historical data
        direction: monthlyCashFlow >= 0 ? 'up' : 'down',
        isPositive: monthlyCashFlow >= 0,
      },
      {
        id: 'savings_rate',
        label: 'Savings Rate',
        value: `${Math.round(savingsRate)}%`,
        change: 2, // This would ideally be calculated from historical data
        direction: 'up',
        isPositive: true,
      },
      {
        id: 'net_worth',
        label: 'Net Worth',
        value: formatCurrency(netWorth),
        change: 5, // This would ideally be calculated from historical data
        direction: 'up',
        isPositive: true,
      },
      {
        id: 'debt_to_income',
        label: 'Debt-to-Income Ratio',
        value: `${Math.round(debtToIncomeRatio)}%`,
        change: 2, // This would ideally be calculated from historical data
        direction: 'down',
        isPositive: true,
      },
    ];
  };

  // Generate alerts based on financial health score weaknesses
  const generateAlerts = (): AlertItem[] => {
    const alerts: AlertItem[] = [];

    // Add alerts based on financial health score weaknesses
    healthScoreResult.weaknesses.forEach((weakness, index) => {
      let severity: 'low' | 'medium' | 'high' | 'critical' = 'medium';

      // Determine severity based on the specific weakness
      if (weakness.includes('cash flow') && monthlyCashFlow < 0) {
        severity = 'critical';
      } else if (weakness.includes('debt') && debtToIncomeRatio > 50) {
        severity = 'critical';
      } else if (weakness.includes('emergency fund') && emergencyFundMonths < 1) {
        severity = 'critical';
      } else if (weakness.includes('net worth') && netWorth < 0) {
        severity = 'high';
      } else {
        severity = 'medium';
      }

      alerts.push({
        id: `weakness_${index}`,
        title: weakness,
        description:
          healthScoreResult.recommendations[index] ||
          'Review this area for improvement opportunities.',
        severity,
      });
    });

    // Add traditional alerts for backward compatibility
    if (monthlyCashFlow < 0 && !alerts.some((alert) => alert.title.includes('cash flow'))) {
      alerts.push({
        id: 'negative_cash_flow',
        title: 'Negative Cash Flow',
        description: `Your expenses exceed your income by ${formatCurrency(Math.abs(monthlyCashFlow))} per month. Review your budget to identify areas for potential savings.`,
        severity: 'critical',
      });
    }

    if (savingsRate < 10 && !alerts.some((alert) => alert.title.includes('savings'))) {
      alerts.push({
        id: 'low_savings_rate',
        title: 'Low Savings Rate',
        description: `Your current savings rate is ${Math.round(savingsRate)}%. Financial experts recommend saving at least 15-20% of your income.`,
        severity: savingsRate < 5 ? 'high' : 'medium',
      });
    }

    if (debtToIncomeRatio > 43 && !alerts.some((alert) => alert.title.includes('debt'))) {
      alerts.push({
        id: 'high_debt_ratio',
        title: 'High Debt-to-Income Ratio',
        description: `Your debt-to-income ratio of ${Math.round(debtToIncomeRatio)}% is above the recommended maximum of 36%. This may limit your ability to qualify for new loans.`,
        severity: debtToIncomeRatio > 50 ? 'critical' : 'high',
      });
    }

    if (emergencyFundMonths < 3 && !alerts.some((alert) => alert.title.includes('emergency'))) {
      alerts.push({
        id: 'insufficient_emergency_fund',
        title: 'Insufficient Emergency Fund',
        description: `Your emergency fund covers only ${emergencyFundMonths.toFixed(1)} months of expenses. Aim for 3-6 months of expenses in an easily accessible account.`,
        severity: emergencyFundMonths < 1 ? 'critical' : 'high',
      });
    }

    return alerts;
  };

  // Generate actions based on financial health score recommendations
  const generateActions = (): ActionItem[] => {
    const actions: ActionItem[] = [];

    // Add actions based on financial health score recommendations
    healthScoreResult.recommendations.forEach((recommendation, index) => {
      let priority: 'low' | 'medium' | 'high' = 'medium';
      let icon = '📋';

      // Determine priority and icon based on the recommendation content
      if (recommendation.includes('cash flow') || recommendation.includes('budget')) {
        priority = monthlyCashFlow < 0 ? 'high' : 'medium';
        icon = '💰';
      } else if (recommendation.includes('debt')) {
        priority = debtToIncomeRatio > 50 ? 'high' : 'medium';
        icon = '📉';
      } else if (recommendation.includes('emergency fund')) {
        priority = emergencyFundMonths < 1 ? 'high' : 'medium';
        icon = '🛡️';
      } else if (recommendation.includes('savings') || recommendation.includes('income')) {
        priority = savingsRate < 5 ? 'high' : 'medium';
        icon = '📈';
      }

      // Create action from recommendation
      const title = recommendation.split('.')[0]; // Use first sentence as title

      actions.push({
        id: `recommendation_${index}`,
        title: title || 'Financial Improvement Action',
        description: recommendation,
        priority,
        icon,
      });
    });

    // Add traditional actions for backward compatibility if no recommendations
    if (actions.length === 0) {
      if (monthlyCashFlow < 0) {
        actions.push({
          id: 'improve_cash_flow',
          title: 'Improve Cash Flow',
          description:
            'Review your expenses to identify non-essential items that can be reduced or eliminated.',
          priority: 'high',
          icon: '💰',
        });
      }

      if (savingsRate < 15) {
        actions.push({
          id: 'increase_savings',
          title: 'Increase Savings Rate',
          description:
            'Aim to save at least 15-20% of your income for long-term financial security.',
          priority: 'medium',
          icon: '📈',
        });
      }

      if (debtToIncomeRatio > 36) {
        actions.push({
          id: 'reduce_debt',
          title: 'Reduce Debt',
          description:
            'Focus on paying down high-interest debt to improve your debt-to-income ratio.',
          priority: debtToIncomeRatio > 50 ? 'high' : 'medium',
          icon: '📉',
        });
      }

      if (emergencyFundMonths < 3) {
        actions.push({
          id: 'build_emergency_fund',
          title: 'Build Emergency Fund',
          description:
            'Work toward saving 3-6 months of expenses in an easily accessible account for emergencies.',
          priority: emergencyFundMonths < 1 ? 'high' : 'medium',
          icon: '🛡️',
        });
      }
    }

    return actions;
  };

  // Generate guidance based on financial health categories
  const generateGuidance = (): GuidanceItem[] => {
    const guidanceItems: GuidanceItem[] = [];

    // Add guidance based on financial health categories
    healthScoreResult.categories.forEach((category) => {
      let icon = '📋';

      // Determine icon based on category
      switch (category.id) {
        case 'cash_flow':
          icon = '📊';
          break;
        case 'debt_management':
          icon = '💳';
          break;
        case 'emergency_fund':
          icon = '🛡️';
          break;
        case 'net_worth':
          icon = '💰';
          break;
        case 'retirement':
          icon = '🏖️';
          break;
        case 'protection':
          icon = '🔒';
          break;
        case 'estate_planning':
          icon = '📜';
          break;
      }

      // Add category guidance
      guidanceItems.push({
        id: `category_${category.id}`,
        title: category.name,
        description: `Your score in this category is ${category.score}/100. ${
          category.score >= 80
            ? "You're doing excellent in this area!"
            : category.score >= 60
              ? "You're doing well, but there's room for improvement."
              : 'This is an area that needs attention.'
        }`,
        icon,
      });

      // Add metric-specific guidance for low scores
      category.metrics.forEach((metric) => {
        if (metric.score < 50) {
          guidanceItems.push({
            id: `metric_${metric.id}`,
            title: `Improve Your ${metric.name}`,
            description: `Current: ${metric.value}, Target: ${metric.target}. ${metric.description}.`,
            icon,
          });
        }
      });
    });

    // Add traditional guidance if no categories
    if (guidanceItems.length === 0) {
      guidanceItems.push(
        {
          id: 'budget_optimization',
          title: 'Budget Optimization',
          description:
            'Review your budget regularly and look for opportunities to reduce expenses in discretionary categories like entertainment, dining out, and subscriptions.',
          icon: '📊',
        },
        {
          id: 'debt_management',
          title: 'Strategic Debt Management',
          description:
            'Prioritize paying down high-interest debt while maintaining minimum payments on lower-interest debt. Consider debt consolidation if appropriate.',
          icon: '💳',
        },
        {
          id: 'emergency_planning',
          title: 'Emergency Planning',
          description:
            'Keep your emergency fund in a high-yield savings account that is easily accessible but separate from your regular checking account.',
          icon: '🛡️',
        },
        {
          id: 'income_growth',
          title: 'Income Growth Strategies',
          description:
            'Explore opportunities to increase your income through career advancement, additional education, side gigs, or passive income streams.',
          icon: '📈',
        }
      );
    }

    return guidanceItems;
  };

  const trends = generateTrends();
  const alerts = generateAlerts();
  const actions = generateActions();
  const guidanceItems = generateGuidance();

  // Check if all sections are completed
  const totalSections = northSections?.length || 0;
  const completedSections = northSections?.filter((section) => section.isCompleted).length || 0;
  const allCompleted = completedSections === totalSections && totalSections > 0;

  // Handle PDF export
  const handleExportPDF = async () => {
    setIsExporting(true);

    try {
      const userName =
        `${(personalInfo as any)?.firstName || ''} ${(personalInfo as any)?.lastName || ''}`.trim() ||
        'User';
      await downloadComprehensivePDF(data, userName);
    } catch (error) {
      console.error('Error exporting PDF:', error);
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Container theme={theme} data-testid="summary-section">
      <Header theme={theme}>
        <Title theme={theme}>North Direction Summary</Title>
        <Description theme={theme}>Review your current financial position and health.</Description>
      </Header>

      <HealthScoreCard theme={theme}>
        <HealthScoreHeader>
          <HealthScoreTitle>Financial Health Score</HealthScoreTitle>
          <HealthScoreValue score={financialHealthScore}>{financialHealthScore}</HealthScoreValue>
        </HealthScoreHeader>
        <HealthScoreStatus score={financialHealthScore}>
          {getFinancialHealthStatus()}
        </HealthScoreStatus>
        <HealthScoreBar theme={theme}>
          <HealthScoreFill score={financialHealthScore} theme={theme} />
        </HealthScoreBar>

        {/* Score breakdown */}
        <HealthScoreBreakdown>
          {healthScoreResult.categories.map((category) => (
            <CategoryScore key={category.id}>
              <CategoryName>{category.name}</CategoryName>
              <CategoryScoreBar theme={theme}>
                <CategoryScoreFill score={category.score} theme={theme} />
                <CategoryScoreLabel>{category.score}/100</CategoryScoreLabel>
              </CategoryScoreBar>
              <CategoryWeight>Weight: {Math.round(category.weight * 100)}%</CategoryWeight>
            </CategoryScore>
          ))}
        </HealthScoreBreakdown>

        <HealthScoreDescription theme={theme}>
          Your financial health score is calculated based on multiple factors including cash flow
          management, debt management, emergency fund adequacy, and net worth. Each category is
          weighted according to its importance in your overall financial health.
        </HealthScoreDescription>
      </HealthScoreCard>

      <SummaryCard theme={theme}>
        <SummaryTitle>Cash Flow Summary</SummaryTitle>
        <SummaryContent>
          <SummaryItem>
            <SummaryLabel>Monthly Income:</SummaryLabel>
            <SummaryValue>{formatCurrency(totalMonthlyIncome)}</SummaryValue>
          </SummaryItem>
          <SummaryItem>
            <SummaryLabel>Monthly Expenses:</SummaryLabel>
            <SummaryValue>{formatCurrency(totalMonthlyExpenses)}</SummaryValue>
          </SummaryItem>
          <SummaryItem>
            <SummaryLabel>Monthly Cash Flow:</SummaryLabel>
            <SummaryValue highlight={monthlyCashFlow < 0}>
              {formatCurrency(monthlyCashFlow)}
            </SummaryValue>
          </SummaryItem>
          <SummaryItem>
            <SummaryLabel>Savings Rate:</SummaryLabel>
            <SummaryValue highlight={savingsRate < 10}>{Math.round(savingsRate)}%</SummaryValue>
          </SummaryItem>
        </SummaryContent>
      </SummaryCard>

      <SummaryCard theme={theme}>
        <SummaryTitle>Net Worth Summary</SummaryTitle>
        <SummaryContent>
          <SummaryItem>
            <SummaryLabel>Total Assets:</SummaryLabel>
            <SummaryValue>{formatCurrency(totalAssets)}</SummaryValue>
          </SummaryItem>
          <SummaryItem>
            <SummaryLabel>Total Liabilities:</SummaryLabel>
            <SummaryValue>{formatCurrency(totalLiabilities)}</SummaryValue>
          </SummaryItem>
          <SummaryItem>
            <SummaryLabel>Net Worth:</SummaryLabel>
            <SummaryValue highlight={netWorth < 0}>{formatCurrency(netWorth)}</SummaryValue>
          </SummaryItem>
          <SummaryItem>
            <SummaryLabel>Debt-to-Income Ratio:</SummaryLabel>
            <SummaryValue highlight={debtToIncomeRatio > 36}>
              {Math.round(debtToIncomeRatio)}%
            </SummaryValue>
          </SummaryItem>
        </SummaryContent>
      </SummaryCard>

      <TrendsSection title="Financial Trends" trends={trends} theme={theme} />
      <AlertsSection alerts={alerts} theme={theme} />
      <ActionsSection actions={actions} theme={theme} />
      <GuidanceSection guidanceItems={guidanceItems} theme={theme} />

      <ButtonGroup>
        <AnalysisButton onClick={() => setShowCashFlowAnalysis(true)} theme={theme}>
          View Detailed Cash Flow Analysis
        </AnalysisButton>

        <ExportButton onClick={handleExportPDF} disabled={isExporting} theme={theme}>
          {isExporting ? 'Generating PDF...' : 'Export Comprehensive Financial Report'}
        </ExportButton>
      </ButtonGroup>

      {showCashFlowAnalysis && (
        <Modal theme={theme}>
          <CashFlowAnalysis onClose={() => setShowCashFlowAnalysis(false)} />
        </Modal>
      )}

      <ButtonContainer>
        {onBack && (
          <BackButton type="button" onClick={onBack} theme={theme}>
            Back
          </BackButton>
        )}

        <CompleteButton type="button" onClick={onComplete} theme={theme}>
          Continue to East Direction
        </CompleteButton>
      </ButtonContainer>
    </Container>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  padding: 24px;
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 24px;
`;

const Title = styled.h2<{ theme: any }>`
  margin: 0 0 8px 0;
  font-size: 1.8rem;
  color: ${(props) => props.theme.colors.primary.main};
`;

const Description = styled.p<{ theme: any }>`
  margin: 0;
  font-size: 1rem;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const HealthScoreCard = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  text-align: center;
`;

const HealthScoreHeader = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
`;

const HealthScoreTitle = styled.h3`
  margin: 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const HealthScoreValue = styled.div<{ score: number }>`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.5rem;
  font-weight: 700;
  background-color: ${(props) => {
    if (props.score >= 80) return '#4CAF50';
    if (props.score >= 60) return '#8BC34A';
    if (props.score >= 40) return '#FFC107';
    if (props.score >= 20) return '#FF9800';
    return '#F44336';
  }};
  color: white;
`;

const HealthScoreStatus = styled.div<{ score: number }>`
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: ${(props) => {
    if (props.score >= 80) return '#4CAF50';
    if (props.score >= 60) return '#8BC34A';
    if (props.score >= 40) return '#FFC107';
    if (props.score >= 20) return '#FF9800';
    return '#F44336';
  }};
`;

const HealthScoreBar = styled.div<{ theme: any }>`
  height: 8px;
  background-color: ${(props) => props.theme.colors.background.main};
  border-radius: 4px;
  overflow: hidden;
`;

const HealthScoreFill = styled.div<{ score: number; theme: any }>`
  height: 100%;
  width: ${(props) => props.score}%;
  background-color: ${(props) => {
    if (props.score >= 80) return '#4CAF50';
    if (props.score >= 60) return '#8BC34A';
    if (props.score >= 40) return '#FFC107';
    if (props.score >= 20) return '#FF9800';
    return '#F44336';
  }};
`;

const HealthScoreBreakdown = styled.div`
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const CategoryScore = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

const CategoryName = styled.div`
  font-size: 0.9rem;
  font-weight: 500;
  display: flex;
  justify-content: space-between;
`;

const CategoryScoreBar = styled.div<{ theme: any }>`
  height: 6px;
  background-color: ${(props) => props.theme.colors.background.main};
  border-radius: 3px;
  overflow: hidden;
  position: relative;
`;

const CategoryScoreFill = styled.div<{ score: number; theme: any }>`
  height: 100%;
  width: ${(props) => props.score}%;
  background-color: ${(props) => {
    if (props.score >= 80) return '#4CAF50';
    if (props.score >= 60) return '#8BC34A';
    if (props.score >= 40) return '#FFC107';
    if (props.score >= 20) return '#FF9800';
    return '#F44336';
  }};
`;

const CategoryScoreLabel = styled.span`
  position: absolute;
  right: 0;
  top: -18px;
  font-size: 0.8rem;
  font-weight: 500;
`;

const CategoryWeight = styled.div`
  font-size: 0.8rem;
  color: #666;
  text-align: right;
`;

const HealthScoreDescription = styled.p<{ theme: any }>`
  margin-top: 16px;
  font-size: 0.9rem;
  color: ${(props) => props.theme.colors.text.secondary};
  text-align: center;
  line-height: 1.4;
`;

const SummaryCard = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const SummaryTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const SummaryContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const SummaryItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
`;

const SummaryLabel = styled.div`
  font-weight: 500;
`;

const SummaryValue = styled.div<{ highlight?: boolean }>`
  font-weight: 600;
  color: ${(props) => (props.highlight ? '#F44336' : '#2196F3')};
`;

const ButtonGroup = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
`;

const AnalysisButton = styled.button<{ theme: any }>`
  padding: 12px 24px;
  background-color: ${(props) => props.theme.colors.secondary.main};
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => props.theme.colors.secondary.dark};
  }
`;

const ExportButton = styled.button<{ theme: any; disabled?: boolean }>`
  padding: 12px 24px;
  background-color: ${(props) => (props.disabled ? '#ccc' : props.theme.colors.primary.main)};
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => (props.disabled ? '#ccc' : props.theme.colors.primary.dark)};
  }
`;

const Modal = styled.div<{ theme: any }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: 32px;
`;

const BackButton = styled.button<{ theme: any }>`
  padding: 12px 24px;
  background-color: transparent;
  color: ${(props) => props.theme.colors.primary.main};
  border: 1px solid ${(props) => props.theme.colors.primary.main};
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.light};
  }
`;

const CompleteButton = styled.button<{ theme: any }>`
  padding: 12px 24px;
  background-color: ${(props) => props.theme.colors.primary.main};
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

export default SummarySection;
