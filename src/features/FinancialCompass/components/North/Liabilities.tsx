import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { formatCurrency } from '../../../../utils/formatters';

interface LiabilitiesProps {
  onComplete?: () => void;
  onBack?: () => void;
}

// Liability categories
const liabilityCategories = [
  {
    id: 'mortgage',
    label: 'Mortgages',
    icon: '🏠',
    fields: [
      { id: 'primaryMortgage', label: 'Primary Residence Mortgage' },
      { id: 'secondaryMortgage', label: 'Secondary Residence Mortgage' },
      { id: 'heloc', label: 'Home Equity Line of Credit' },
      { id: 'rentalMortgage', label: 'Rental Property Mortgage' },
    ],
  },
  {
    id: 'loans',
    label: 'Loans',
    icon: '💰',
    fields: [
      { id: 'autoLoan', label: 'Auto Loan' },
      { id: 'studentLoan', label: 'Student Loan' },
      { id: 'personalLoan', label: 'Personal Loan' },
      { id: 'businessLoan', label: 'Business Loan' },
      { id: 'familyLoan', label: 'Family Loan' },
    ],
  },
  {
    id: 'creditCards',
    label: 'Credit Cards',
    icon: '💳',
    fields: [
      { id: 'creditCard1', label: 'Credit Card 1' },
      { id: 'creditCard2', label: 'Credit Card 2' },
      { id: 'creditCard3', label: 'Credit Card 3' },
      { id: 'storeCreditCard', label: 'Store Credit Card' },
    ],
  },
  {
    id: 'other',
    label: 'Other Debts',
    icon: '📝',
    fields: [
      { id: 'medicalDebt', label: 'Medical Debt' },
      { id: 'taxLiability', label: 'Tax Liability' },
      { id: 'judgments', label: 'Legal Judgments' },
      { id: 'otherDebt', label: 'Other Debt' },
    ],
  },
];

const Liabilities: React.FC<LiabilitiesProps> = ({ onComplete, onBack }) => {
  const { theme } = useTheme();
  const { data, updateData, updateNorthSectionStatus } = useFinancialCompass();

  // Initialize liabilities state from context or with default values
  const [liabilities, setLiabilities] = useState<Record<string, Record<string, number>>>(
    data.north?.liabilities || createDefaultLiabilities()
  );

  // Track if form is dirty (has unsaved changes)
  const [isDirty, setIsDirty] = useState(false);

  // Track if form is valid
  const [isValid, setIsValid] = useState(false);

  // Track if auto-save is in progress
  const [isSaving, setIsSaving] = useState(false);

  // Track active category
  const [activeCategory, setActiveCategory] = useState(liabilityCategories[0].id);

  // Create default liabilities object
  function createDefaultLiabilities() {
    const defaultLiabilities: Record<string, Record<string, number>> = {};

    liabilityCategories.forEach((category) => {
      defaultLiabilities[category.id] = {};
      category.fields.forEach((field) => {
        defaultLiabilities[category.id][field.id] = 0;
      });
    });

    return defaultLiabilities;
  }

  // Calculate totals
  const calculateTotals = () => {
    // Calculate total for each category
    const categoryTotals: Record<string, number> = {};
    let totalLiabilities = 0;

    Object.entries(liabilities).forEach(([categoryId, fields]) => {
      const categoryTotal = Object.values(fields).reduce((sum, value) => sum + (value || 0), 0);
      categoryTotals[categoryId] = categoryTotal;
      totalLiabilities += categoryTotal;
    });

    return {
      categoryTotals,
      totalLiabilities,
    };
  };

  const { categoryTotals, totalLiabilities } = calculateTotals();

  // Calculate net worth if assets data is available
  const calculateNetWorth = () => {
    const netWorthDetails = data.north?.netWorthDetails || {
      totalAssets: '0',
      totalLiabilities: '0',
    };
    const totalAssets = parseFloat(netWorthDetails.totalAssets || '0');
    return totalAssets - totalLiabilities;
  };

  const netWorth = calculateNetWorth();

  // Handle liability value change
  const handleLiabilityChange = (categoryId: string, fieldId: string, value: string) => {
    const numericValue = value === '' ? 0 : parseFloat(value);

    if (isNaN(numericValue)) return;

    setLiabilities((prev) => ({
      ...prev,
      [categoryId]: {
        ...prev[categoryId],
        [fieldId]: numericValue,
      },
    }));

    setIsDirty(true);
  };

  // Validate form
  useEffect(() => {
    // Form is valid if at least one liability is entered
    const hasLiabilities = Object.values(liabilities).some((category) =>
      Object.values(category).some((value) => value > 0)
    );

    setIsValid(true); // Always valid, even with zero liabilities
  }, [liabilities]);

  // Auto-save when form is dirty
  useEffect(() => {
    if (isDirty) {
      const timer = setTimeout(() => {
        saveLiabilities();
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [liabilities, isDirty]);

  // Save liabilities to context
  const saveLiabilities = () => {
    setIsSaving(true);

    // Calculate totals for saving
    const { totalLiabilities } = calculateTotals();
    const netWorth = calculateNetWorth();

    // Update data in context
    updateData('north', 'liabilities', liabilities);

    // Update net worth details
    const netWorthDetails = data.north?.netWorthDetails || {
      totalAssets: '0',
      totalLiabilities: '0',
    };
    updateData('north', 'netWorthDetails', {
      ...netWorthDetails,
      totalLiabilities: totalLiabilities.toString(),
      netWorth: netWorth.toString(),
    });

    // Mark form as not dirty
    setIsDirty(false);

    // Show saving indicator briefly
    setTimeout(() => {
      setIsSaving(false);
    }, 500);
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Save final data
    saveLiabilities();

    // Mark section as completed
    updateNorthSectionStatus('liabilities_debt', true, false);

    // Call onComplete callback
    if (onComplete) {
      onComplete();
    }
  };

  // Handle back button
  const handleBack = () => {
    // Save current data before going back
    if (isDirty) {
      saveLiabilities();
    }

    // Call onBack callback
    if (onBack) {
      onBack();
    }
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Liabilities & Debt</Title>
        <Description theme={theme}>
          Track your liabilities and debt to understand your net worth and financial obligations.
        </Description>

        {/* Auto-save indicator */}
        <SaveIndicator visible={isSaving} theme={theme}>
          <SaveIcon>💾</SaveIcon>
          <SaveText>Saving...</SaveText>
        </SaveIndicator>
      </Header>

      <Content>
        <CategoriesNav theme={theme}>
          {liabilityCategories.map((category) => (
            <CategoryTab
              key={category.id}
              isActive={activeCategory === category.id}
              onClick={() => setActiveCategory(category.id)}
              theme={theme}
            >
              <CategoryIcon>{category.icon}</CategoryIcon>
              <CategoryLabel>{category.label}</CategoryLabel>
              <CategoryTotal>{formatCurrency(categoryTotals[category.id] || 0)}</CategoryTotal>
            </CategoryTab>
          ))}
        </CategoriesNav>

        <LiabilityForm onSubmit={handleSubmit}>
          <ActiveCategory theme={theme}>
            {liabilityCategories.find((c) => c.id === activeCategory)?.label}
          </ActiveCategory>

          <LiabilityItems theme={theme}>
            {liabilityCategories
              .find((c) => c.id === activeCategory)
              ?.fields.map((field) => (
                <LiabilityItem key={field.id} theme={theme}>
                  <LiabilityLabel theme={theme}>{field.label}</LiabilityLabel>
                  <LiabilityInput
                    type="number"
                    min="0"
                    step="0.01"
                    value={liabilities[activeCategory][field.id] || ''}
                    onChange={(e) =>
                      handleLiabilityChange(activeCategory, field.id, e.target.value)
                    }
                    placeholder="0.00"
                    theme={theme}
                  />
                </LiabilityItem>
              ))}
          </LiabilityItems>

          <Summary theme={theme}>
            <SummaryTitle theme={theme}>Financial Position Summary</SummaryTitle>

            <SummaryItems>
              {Object.entries(categoryTotals).map(([categoryId, total]) => {
                if (total <= 0) return null;

                const category = liabilityCategories.find((c) => c.id === categoryId);
                return (
                  <SummaryItem key={categoryId} theme={theme}>
                    <SummaryItemLabel>
                      {category?.icon} {category?.label}
                    </SummaryItemLabel>
                    <SummaryItemValue>{formatCurrency(total)}</SummaryItemValue>
                  </SummaryItem>
                );
              })}

              <TotalItem theme={theme}>
                <TotalLabel>Total Liabilities</TotalLabel>
                <TotalValue>{formatCurrency(totalLiabilities)}</TotalValue>
              </TotalItem>

              <TotalItem theme={theme}>
                <TotalLabel>Total Assets</TotalLabel>
                <TotalValue>
                  {formatCurrency(parseFloat(data.north?.netWorthDetails?.totalAssets || '0'))}
                </TotalValue>
              </TotalItem>

              <NetWorthItem isPositive={netWorth >= 0} theme={theme}>
                <NetWorthLabel>Net Worth</NetWorthLabel>
                <NetWorthValue>{formatCurrency(netWorth)}</NetWorthValue>
              </NetWorthItem>
            </SummaryItems>
          </Summary>

          <ButtonContainer>
            <BackButton type="button" onClick={handleBack} theme={theme}>
              Back
            </BackButton>
            <SubmitButton type="submit" disabled={!isValid} theme={theme}>
              Save & Continue
            </SubmitButton>
          </ButtonContainer>
        </LiabilityForm>
      </Content>
    </Container>
  );
};

// Styled components - reusing styles for consistency
const Container = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.secondary};
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const Header = styled.div<{ theme: any }>`
  margin-bottom: 24px;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 16px;
  position: relative;
`;

const Title = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
`;

const Description = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const SaveIndicator = styled.div<{ visible: boolean; theme: any }>`
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  background-color: ${(props) => props.theme.colors.success.light};
  padding: 4px 8px;
  border-radius: 4px;
`;

const SaveIcon = styled.span`
  margin-right: 4px;
  font-size: 0.9rem;
`;

const SaveText = styled.span`
  font-size: 0.9rem;
`;

const Content = styled.div`
  display: flex;
  flex-direction: column;
`;

const CategoriesNav = styled.div<{ theme: any }>`
  display: flex;
  overflow-x: auto;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};

  &::-webkit-scrollbar {
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    background: ${(props) => props.theme.colors.background.tertiary};
  }

  &::-webkit-scrollbar-thumb {
    background-color: ${(props) => props.theme.colors.primary.light};
    border-radius: 4px;
  }
`;

const CategoryTab = styled.div<{ isActive: boolean; theme: any }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 16px;
  min-width: 100px;
  cursor: pointer;
  border-radius: 8px;
  background-color: ${(props) =>
    props.isActive ? props.theme.colors.primary.light : 'transparent'};
  color: ${(props) =>
    props.isActive ? props.theme.colors.primary.dark : props.theme.colors.text.secondary};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) =>
      props.isActive ? props.theme.colors.primary.light : props.theme.colors.background.hover};
  }

  &:not(:last-child) {
    margin-right: 8px;
  }
`;

const CategoryIcon = styled.div`
  font-size: 1.5rem;
  margin-bottom: 4px;
`;

const CategoryLabel = styled.div`
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 4px;
  text-align: center;
`;

const CategoryTotal = styled.div`
  font-size: 0.8rem;
  font-weight: 700;
`;

const LiabilityForm = styled.form`
  display: flex;
  flex-direction: column;
`;

const ActiveCategory = styled.h3<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 16px 0;
  font-size: 1.2rem;
`;

const LiabilityItems = styled.div<{ theme: any }>`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const LiabilityItem = styled.div<{ theme: any }>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: 4px;
  background-color: ${(props) => props.theme.colors.background.main};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.hover};
  }
`;

const LiabilityLabel = styled.label<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  font-size: 1rem;
  margin-right: 16px;
`;

const LiabilityInput = styled.input<{ theme: any }>`
  width: 120px;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid ${(props) => props.theme.colors.border.main};
  background-color: ${(props) => props.theme.colors.background.input};
  color: ${(props) => props.theme.colors.text.primary};
  font-size: 1rem;
  text-align: right;

  &:focus {
    outline: none;
    border-color: ${(props) => props.theme.colors.primary.main};
    box-shadow: 0 0 0 2px ${(props) => props.theme.colors.primary.light};
  }

  &::placeholder {
    color: ${(props) => props.theme.colors.text.placeholder};
  }

  /* Hide spinner for number input */
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  &[type='number'] {
    -moz-appearance: textfield;
  }
`;

const Summary = styled.div<{ theme: any }>`
  margin-bottom: 24px;
  padding: 16px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
`;

const SummaryTitle = styled.h4<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 8px;
`;

const SummaryItems = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const SummaryItem = styled.div<{ theme: any }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const SummaryItemLabel = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const SummaryItemValue = styled.div`
  font-weight: 500;
`;

const TotalItem = styled.div<{ theme: any }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1rem;
  font-weight: 700;
  color: ${(props) => props.theme.colors.text.primary};
  margin-top: 16px;
  padding-top: 8px;
  border-top: 1px solid ${(props) => props.theme.colors.border.main};
`;

const TotalLabel = styled.div``;

const TotalValue = styled.div``;

const NetWorthItem = styled.div<{ isPositive: boolean; theme: any }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.2rem;
  font-weight: 700;
  color: ${(props) =>
    props.isPositive ? props.theme.colors.success.dark : props.theme.colors.error.main};
  margin-top: 16px;
  padding-top: 8px;
  border-top: 2px solid
    ${(props) =>
      props.isPositive ? props.theme.colors.success.main : props.theme.colors.error.main};
`;

const NetWorthLabel = styled.div``;

const NetWorthValue = styled.div``;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: 24px;
`;

const BackButton = styled.button<{ theme: any }>`
  padding: 12px 24px;
  border-radius: 4px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
  color: ${(props) => props.theme.colors.text.primary};
  border: none;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.hover};
  }
`;

const SubmitButton = styled.button<{ disabled: boolean; theme: any }>`
  padding: 12px 24px;
  border-radius: 4px;
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.text.onPrimary};
  border: none;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  opacity: ${(props) => (props.disabled ? 0.7 : 1)};
  font-weight: bold;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) =>
      props.disabled ? props.theme.colors.primary.main : props.theme.colors.primary.dark};
  }
`;

export default Liabilities;
