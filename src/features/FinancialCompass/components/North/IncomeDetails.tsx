/**
 * Income Details Component (Clean Implementation)
 *
 * This component collects and displays the user's income information as part of the
 * North direction of the Financial Compass ("Where You Are").
 */
import React, { useState, useEffect, useCallback } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { useGuidedJourney } from '../../../GuidedJourney/context/GuidedJourneyContext';
import { calculateDetailedTaxLiability } from '../../../../utils/taxCalculator';
import { convertToAnnual } from '../../../../utils/expenseCalculator';
import TaxOptimizationPanel from './TaxOptimizationPanel';
import CashFlowAnalysis from './CashFlowAnalysis';

// Types
type IncomeFrequency = 'weekly' | 'biweekly' | 'monthly' | 'annually';
type IncomeType =
  | 'salary'
  | 'self_employed'
  | 'investment'
  | 'pension'
  | 'social_security'
  | 'other';
type FilingStatusType = 'SINGLE' | 'MARRIED_JOINT' | 'HEAD_OF_HOUSEHOLD' | 'MARRIED_SEPARATE';

const FILING_STATUS_OPTIONS = [
  { value: 'SINGLE' as const, label: 'Single' },
  { value: 'MARRIED_JOINT' as const, label: 'Married Filing Jointly' },
  { value: 'HEAD_OF_HOUSEHOLD' as const, label: 'Head of Household' },
  { value: 'MARRIED_SEPARATE' as const, label: 'Married Filing Separately' },
];

interface IncomeSource {
  id: string;
  source: string;
  amount: string;
  frequency: IncomeFrequency;
  type: IncomeType;
  description?: string;
}

interface FormData {
  // Primary income
  primaryIncome: string;
  primaryIncomeFrequency: IncomeFrequency;
  primaryIncomeType: IncomeType;

  // Tax information
  filingStatus: FilingStatusType;
  state: string;

  // Other income sources
  otherIncome: string;
  otherIncomeFrequency: IncomeFrequency;
  incomeSources: IncomeSource[];

  // Calculated values
  annualIncome: number;
  additionalIncome: number;
  totalAnnualIncome: number;

  // Tax calculations
  federalTax: number;
  stateTax: number;
  ficaTax: number;
  totalTax: number;
  afterTaxIncome: number;
  effectiveTaxRate: number;

  // Additional fields
  dependents: number;
  preTaxDeductions: number;
  postTaxDeductions: number;
}

interface IncomeDetailsProps {
  onComplete?: () => void;
  onSave?: (data: FormData) => Promise<void>;
  onBack?: () => void;
  data?: Partial<FormData>;
  updateData?: (data: Partial<FormData>) => void;
}

type ValidationErrors = Record<string, string>;

// Extend the DefaultTheme interface
declare module 'styled-components' {
  export interface DefaultTheme {
    colors: {
      primary: string;
      primaryDark: string;
      background: string;
      paper: string;
      text: string;
      textSecondary: string;
      border: string;
      error: string;
      success: string;
      warning: string;
      disabled: string;
    };
    spacing: (multiplier?: number) => string;
  }
}

// Constants
const INCOME_FREQUENCIES = [
  { value: 'weekly' as const, label: 'Weekly' },
  { value: 'biweekly' as const, label: 'Bi-Weekly' },
  { value: 'monthly' as const, label: 'Monthly' },
  { value: 'annually' as const, label: 'Annually' },
];

const INCOME_TYPES = [
  { value: 'salary' as const, label: 'Salary/Wages' },
  { value: 'self_employed' as const, label: 'Self-Employment' },
  { value: 'investment' as const, label: 'Investment' },
  { value: 'pension' as const, label: 'Pension' },
  { value: 'social_security' as const, label: 'Social Security' },
  { value: 'other' as const, label: 'Other' },
];

// Initial form values
const initialFormValues: FormData = {
  // Primary income
  primaryIncome: '',
  primaryIncomeFrequency: 'annually',
  primaryIncomeType: 'salary',

  // Tax information
  filingStatus: 'SINGLE',
  state: '',

  // Other income
  otherIncome: '0',
  otherIncomeFrequency: 'annually',
  incomeSources: [],

  // Calculated values
  annualIncome: 0,
  additionalIncome: 0,
  totalAnnualIncome: 0,

  // Tax calculations
  federalTax: 0,
  stateTax: 0,
  ficaTax: 0,
  totalTax: 0,
  afterTaxIncome: 0,
  effectiveTaxRate: 0,

  // Additional fields
  dependents: 0,
  preTaxDeductions: 0,
  postTaxDeductions: 0,
};

// Styled Components
const FormContainer = styled.div`
  max-width: 1000px;
  margin: 0 auto;
  padding: ${({ theme }) => theme.spacing(3)}px;
  background: ${({ theme }) => theme.colors.paper};
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${({ theme }) => theme.spacing(2)}px;
  margin-bottom: ${({ theme }) => theme.spacing(3)}px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const SummaryContainer = styled.div`
  background: ${({ theme }) => theme.colors.background};
  border-radius: 8px;
  padding: ${({ theme }) => theme.spacing(3)}px;
  margin: ${({ theme }) => theme.spacing(2)}px 0;
  border: 1px solid ${({ theme }) => theme.colors.border};
`;

const SummaryRow = styled.div`
  display: flex;
  justify-content: space-between;
  padding: ${({ theme }) => theme.spacing(1)}px 0;

  &.highlight {
    font-weight: 600;
    font-size: 1.1em;
    padding: ${({ theme }) => theme.spacing(1.5)}px 0;
  }

  span:last-child {
    font-family: 'Roboto Mono', monospace;
  }
`;

const SummaryDivider = styled.hr`
  border: none;
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  margin: ${({ theme }) => theme.spacing(1.5)}px 0;
`;

const FormGroup = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing(2)}px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: ${({ theme }) => theme.spacing(1)}px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.text};
`;

const Input = styled.input`
  width: 100%;
  padding: ${({ theme }) => `${theme.spacing(1)}px ${theme.spacing(2)}px`};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 4px;
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
  background: ${({ theme }) => theme.colors.background};

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
    box-shadow: 0 0 0 2px ${({ theme }) => `${theme.colors.primary}33`};
  }
`;

const Select = styled.select`
  width: 100%;
  padding: ${({ theme }) => `${theme.spacing(1)}px ${theme.spacing(2)}px`};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 4px;
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
  background: ${({ theme }) => theme.colors.background};

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
    box-shadow: 0 0 0 2px ${({ theme }) => `${theme.colors.primary}33`};
  }
`;

const Button = styled.button<{ variant?: 'primary' | 'secondary' }>`
  padding: ${({ theme }) => `${theme.spacing(1)}px ${theme.spacing(3)}px`};
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  background: ${({ theme, variant = 'primary' }) =>
    variant === 'primary' ? theme.colors.primary : 'transparent'};
  color: ${({ theme, variant = 'primary' }) =>
    variant === 'primary' ? '#fff' : theme.colors.primary};
  border: ${({ theme, variant = 'primary' }) =>
    variant === 'secondary' ? `1px solid ${theme.colors.primary}` : 'none'};

  &:hover {
    opacity: 0.9;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const ErrorMessage = styled.div`
  color: ${({ theme }) => theme.colors.error};
  font-size: 14px;
  margin-top: 4px;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing(2)}px;
  margin-top: ${({ theme }) => theme.spacing(3)}px;
`;

const mockUserData = {
  income: 85000,
  filingStatus: 'single',
  age: 35,
  retirementAccounts: {
    traditionalIRA: 10000,
    rothIRA: 5000,
    employer401k: 15000,
    employer401kMatch: 3000,
  },
  investments: {
    taxableBrokerage: 20000,
    capitalGains: 2500,
  },
  deductions: {
    itemized: 12000,
    standard: 12550,
  },
  dependents: 0,
  hsaEligible: true,
  hsaContribution: 3000,
};

const IncomeDetails: React.FC<IncomeDetailsProps> = ({
  onComplete,
  onSave,
  onBack,
  data = {},
  updateData,
}) => {
  const theme = useTheme();
  const [formValues, setFormValues] = useState<FormData>({ ...initialFormValues, ...data });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Calculate taxes when form values change
  useEffect(() => {
    calculateTaxes();
  }, [
    formValues.primaryIncome,
    formValues.primaryIncomeFrequency,
    formValues.otherIncome,
    formValues.otherIncomeFrequency,
    formValues.filingStatus,
    formValues.state,
    formValues.dependents,
    formValues.preTaxDeductions,
    formValues.postTaxDeductions,
  ]);

  // Calculate taxes based on current form values
  const calculateTaxes = useCallback(() => {
    const primaryIncome = parseFloat(formValues.primaryIncome) || 0;
    const otherIncome = parseFloat(formValues.otherIncome) || 0;

    // Skip calculation if no primary income
    if (primaryIncome <= 0) return;

    // Convert all income to annual amounts
    const annualPrimaryIncome = convertToAnnual(primaryIncome, formValues.primaryIncomeFrequency);

    const annualOtherIncome = convertToAnnual(otherIncome, formValues.otherIncomeFrequency);

    const grossIncome =
      annualPrimaryIncome + annualOtherIncome - (formValues.preTaxDeductions || 0);

    try {
      const taxResult = calculateDetailedTaxLiability(
        grossIncome,
        formValues.filingStatus.toLowerCase() as 'single' | 'married' | 'headOfHousehold',
        {
          stateCode: formValues.state,
          dependents: formValues.dependents,
          additionalDeductions: formValues.preTaxDeductions || 0,
        }
      );

      const totalTax = taxResult.federalTax + taxResult.stateTax + (taxResult.ficaTax as number);
      const afterTaxIncome = grossIncome - totalTax - (formValues.postTaxDeductions || 0);
      const effectiveTaxRate = grossIncome > 0 ? (totalTax / grossIncome) * 100 : 0;

      setFormValues((prev) => ({
        ...prev,
        annualIncome: annualPrimaryIncome,
        additionalIncome: annualOtherIncome,
        totalAnnualIncome: grossIncome + (formValues.preTaxDeductions || 0),
        federalTax: taxResult.federalTax,
        stateTax: taxResult.stateTax,
        ficaTax: taxResult.ficaTax as number,
        totalTax,
        afterTaxIncome,
        effectiveTaxRate,
      }));
    } catch (error) {
      console.error('Error calculating taxes:', error);
    }
  }, [formValues]);

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    // Handle numeric inputs
    const processedValue =
      type === 'number' && value !== '' ? (parseFloat(value) < 0 ? '0' : value) : value;

    setFormValues((prev) => ({
      ...prev,
      [name]: processedValue,
    }));

    // Validate field if it was touched
    if (touched[name]) {
      validateField(name, processedValue);
    }

    // Update parent component if needed
    if (updateData) {
      updateData({ [name]: value });
    }
  };

  // Handle blur events
  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    // Mark field as touched
    setTouched((prev) => ({
      ...prev,
      [name]: true,
    }));

    // Validate the field
    validateField(name, value);
  };

  // Format currency for display
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Format percentage for display
  const formatPercentage = (value: number): string => {
    return `${value.toFixed(1)}%`;
  };

  // Validate a single field
  const validateField = (fieldName: string, value: unknown): boolean => {
    const newErrors = { ...errors };

    // Common validation patterns
    const requiredFields = ['primaryIncome', 'filingStatus'];
    const numberFields = [
      'primaryIncome',
      'otherIncome',
      'preTaxDeductions',
      'postTaxDeductions',
      'dependents',
    ];

    // Required field validation
    if (requiredFields.includes(fieldName) && !value) {
      newErrors[fieldName] = 'This field is required';
    }
    // Number validation
    else if (numberFields.includes(fieldName) && value !== '' && isNaN(Number(value))) {
      newErrors[fieldName] = 'Please enter a valid number';
    }
    // Positive number validation
    else if (numberFields.includes(fieldName) && Number(value) < 0) {
      newErrors[fieldName] = 'Value cannot be negative';
    }
    // Specific field validations
    else if (fieldName === 'primaryIncome' && Number(value) > 10000000) {
      newErrors[fieldName] = 'Income seems unusually high. Please verify.';
    }
    // Clear error if validation passes
    else {
      delete newErrors[fieldName];
    }

    setErrors(newErrors);
    return !newErrors[fieldName];
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Mark all fields as touched
    const allTouched = Object.keys(formValues).reduce<Record<string, boolean>>((acc, key) => {
      acc[key] = true;
      return acc;
    }, {});

    setTouched(allTouched);

    // Validate all fields
    const validationResults = Object.entries(formValues).map(([key, value]) =>
      validateField(key, value)
    );

    const isValid = validationResults.every(Boolean);

    if (!isValid) {
      setIsSubmitting(false);
      return;
    }

    try {
      // Prepare data for saving
      const dataToSave = {
        ...formValues,
        // Ensure all numeric fields are properly formatted
        annualIncome: parseFloat(formValues.annualIncome.toString()) || 0,
        additionalIncome: parseFloat(formValues.additionalIncome.toString()) || 0,
        totalAnnualIncome: parseFloat(formValues.totalAnnualIncome.toString()) || 0,
        federalTax: parseFloat(formValues.federalTax.toString()) || 0,
        stateTax: parseFloat(formValues.stateTax.toString()) || 0,
        ficaTax: parseFloat(formValues.ficaTax.toString()) || 0,
        totalTax: parseFloat(formValues.totalTax.toString()) || 0,
        afterTaxIncome: parseFloat(formValues.afterTaxIncome.toString()) || 0,
        effectiveTaxRate: parseFloat(formValues.effectiveTaxRate.toString()) || 0,
        dependents: parseInt(formValues.dependents.toString()) || 0,
        preTaxDeductions: parseFloat(formValues.preTaxDeductions.toString()) || 0,
        postTaxDeductions: parseFloat(formValues.postTaxDeductions.toString()) || 0,
      };

      // Save data if updateData exists
      if (updateData) {
        await updateData(dataToSave);
      }

      // Call onSave callback if provided
      if (onSave) {
        await onSave(dataToSave);
      }

      // Call onComplete callback if provided
      if (onComplete) {
        onComplete();
      }
    } catch (error) {
      console.error('Error saving income details:', error);
      setErrors((prev) => ({
        ...prev,
        submit: 'Failed to save income details. Please try again.',
      }));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <FormContainer>
      <h2>Income Details</h2>
      <p>Please provide information about your primary income source and any additional income.</p>

      <form onSubmit={handleSubmit}>
        <h3>Primary Income</h3>
        <FormRow>
          <FormGroup>
            <Label htmlFor="primaryIncome">Amount ($)</Label>
            <Input
              type="number"
              id="primaryIncome"
              name="primaryIncome"
              value={formValues.primaryIncome}
              onChange={handleChange}
              onBlur={handleBlur}
              placeholder="0.00"
              min="0"
              step="0.01"
            />
            {errors.primaryIncome && <ErrorMessage>{errors.primaryIncome}</ErrorMessage>}
          </FormGroup>

          <FormGroup>
            <Label htmlFor="primaryIncomeFrequency">Frequency</Label>
            <Select
              id="primaryIncomeFrequency"
              name="primaryIncomeFrequency"
              value={formValues.primaryIncomeFrequency}
              onChange={handleChange}
              onBlur={handleBlur}
            >
              {INCOME_FREQUENCIES.map((freq) => (
                <option key={freq.value} value={freq.value}>
                  {freq.label}
                </option>
              ))}
            </Select>
          </FormGroup>

          <FormGroup>
            <Label htmlFor="primaryIncomeType">Income Type</Label>
            <Select
              id="primaryIncomeType"
              name="primaryIncomeType"
              value={formValues.primaryIncomeType}
              onChange={handleChange}
              onBlur={handleBlur}
            >
              {INCOME_TYPES.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </Select>
          </FormGroup>
        </FormRow>

        <h3>Tax Information</h3>
        <FormRow>
          <FormGroup>
            <Label htmlFor="filingStatus">Filing Status</Label>
            <Select
              id="filingStatus"
              name="filingStatus"
              value={formValues.filingStatus}
              onChange={handleChange}
              onBlur={handleBlur}
            >
              {FILING_STATUS_OPTIONS.map((status) => (
                <option key={status.value} value={status.value}>
                  {status.label}
                </option>
              ))}
            </Select>
            {errors.filingStatus && <ErrorMessage>{errors.filingStatus}</ErrorMessage>}
          </FormGroup>

          <FormGroup>
            <Label htmlFor="state">State (for state tax)</Label>
            <Input
              type="text"
              id="state"
              name="state"
              value={formValues.state}
              onChange={handleChange}
              onBlur={handleBlur}
              placeholder="e.g., CA"
              maxLength={2}
              style={{ textTransform: 'uppercase' }}
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="dependents">Dependents</Label>
            <Input
              type="number"
              id="dependents"
              name="dependents"
              value={formValues.dependents}
              onChange={handleChange}
              onBlur={handleBlur}
              min="0"
              step="1"
            />
          </FormGroup>
        </FormRow>

        <h3>Additional Income & Deductions</h3>
        <FormRow>
          <FormGroup>
            <Label htmlFor="otherIncome">Other Income ($)</Label>
            <Input
              type="number"
              id="otherIncome"
              name="otherIncome"
              value={formValues.otherIncome}
              onChange={handleChange}
              onBlur={handleBlur}
              placeholder="0.00"
              min="0"
              step="0.01"
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="otherIncomeFrequency">Frequency</Label>
            <Select
              id="otherIncomeFrequency"
              name="otherIncomeFrequency"
              value={formValues.otherIncomeFrequency}
              onChange={handleChange}
              onBlur={handleBlur}
            >
              {INCOME_FREQUENCIES.map((freq) => (
                <option key={freq.value} value={freq.value}>
                  {freq.label}
                </option>
              ))}
            </Select>
          </FormGroup>

          <FormGroup>
            <Label htmlFor="preTaxDeductions">Pre-tax Deductions ($/year)</Label>
            <Input
              type="number"
              id="preTaxDeductions"
              name="preTaxDeductions"
              value={formValues.preTaxDeductions}
              onChange={handleChange}
              onBlur={handleBlur}
              placeholder="0.00"
              min="0"
              step="0.01"
            />
          </FormGroup>
        </FormRow>

        <FormRow>
          <FormGroup>
            <Label htmlFor="postTaxDeductions">Post-tax Deductions ($/year)</Label>
            <Input
              type="number"
              id="postTaxDeductions"
              name="postTaxDeductions"
              value={formValues.postTaxDeductions}
              onChange={handleChange}
              onBlur={handleBlur}
              placeholder="0.00"
              min="0"
              step="0.01"
            />
          </FormGroup>
        </FormRow>

        {/* Summary Section */}
        <h3>Income Summary</h3>
        <SummaryContainer>
          <SummaryRow>
            <span>Annual Income:</span>
            <span>{formatCurrency(formValues.annualIncome)}</span>
          </SummaryRow>
          <SummaryRow>
            <span>Additional Income:</span>
            <span>{formatCurrency(formValues.additionalIncome)}</span>
          </SummaryRow>
          <SummaryRow>
            <span>Pre-tax Deductions:</span>
            <span>-{formatCurrency(formValues.preTaxDeductions)}</span>
          </SummaryRow>
          <SummaryDivider />
          <SummaryRow className="highlight">
            <span>Total Annual Income:</span>
            <span>{formatCurrency(formValues.totalAnnualIncome)}</span>
          </SummaryRow>

          <SummaryDivider />

          <SummaryRow>
            <span>Federal Tax:</span>
            <span>-{formatCurrency(formValues.federalTax)}</span>
          </SummaryRow>
          <SummaryRow>
            <span>State Tax:</span>
            <span>-{formatCurrency(formValues.stateTax)}</span>
          </SummaryRow>
          <SummaryRow>
            <span>FICA Tax:</span>
            <span>-{formatCurrency(formValues.ficaTax)}</span>
          </SummaryRow>
          <SummaryRow>
            <span>Post-tax Deductions:</span>
            <span>-{formatCurrency(formValues.postTaxDeductions)}</span>
          </SummaryRow>
          <SummaryDivider />
          <SummaryRow className="highlight">
            <span>After-tax Income:</span>
            <span>{formatCurrency(formValues.afterTaxIncome)}</span>
          </SummaryRow>
          <SummaryRow>
            <span>Effective Tax Rate:</span>
            <span>{formatPercentage(formValues.effectiveTaxRate)}</span>
          </SummaryRow>
        </SummaryContainer>

        <ButtonGroup>
          {onBack && (
            <Button type="button" onClick={onBack} variant="secondary">
              Back
            </Button>
          )}
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? 'Saving...' : 'Save & Continue'}
          </Button>
        </ButtonGroup>
        {errors.submit && <ErrorMessage>{errors.submit}</ErrorMessage>}
      </form>
      <TaxOptimizationPanel userData={mockUserData} />
      <Box mt={3}>
        <CashFlowAnalysis />
      </Box>
    </FormContainer>
  );
};

export default IncomeDetails;
