import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ThemeProvider from '../../../../theme/ThemeProvider';
import IncomeDetails from './IncomeDetails';
import { FinancialCompassProvider } from '../../context/FinancialCompassContext';
import { GuidedJourneyProvider } from '../../../GuidedJourney/context/GuidedJourneyContext';

// Mock the useGuidedJourney hook
jest.mock('../../../GuidedJourney/context/GuidedJourneyContext', () => ({
  ...jest.requireActual('../../../GuidedJourney/context/GuidedJourneyContext'),
  useGuidedJourney: () => ({
    askQuestion: jest.fn().mockResolvedValue('This is a mock answer'),
  }),
}));

// Mock the createEnhancedAutoSave function
jest.mock('../../../DataPortability/services/autoSaveService', () => ({
  createEnhancedAutoSave: jest.fn().mockReturnValue({
    save: jest.fn().mockResolvedValue(undefined),
    load: jest.fn().mockResolvedValue(null),
    clear: jest.fn().mockResolvedValue(undefined),
    getLastSaved: jest.fn().mockReturnValue(null),
  }),
}));

// Test component wrapper with providers
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ThemeProvider initialMode="light" initialSeason="spring">
      <FinancialCompassProvider>
        <GuidedJourneyProvider>{children}</GuidedJourneyProvider>
      </FinancialCompassProvider>
    </ThemeProvider>
  );
};

describe('IncomeDetails', () => {
  test('renders the component with all form fields', () => {
    render(
      <TestWrapper>
        <IncomeDetails />
      </TestWrapper>
    );

    // Check for section titles
    expect(screen.getByText('Income Details')).toBeInTheDocument();
    expect(screen.getByText('Primary Income')).toBeInTheDocument();
    expect(screen.getByText('Additional Income Sources')).toBeInTheDocument();
    expect(screen.getByText('Income Summary')).toBeInTheDocument();

    // Check for form fields
    expect(screen.getByLabelText('Primary Income Amount *')).toBeInTheDocument();
    expect(screen.getByLabelText('Frequency *')).toBeInTheDocument();
    expect(screen.getByLabelText('Income Type *')).toBeInTheDocument();
    expect(screen.getByLabelText('Estimated Tax Rate (%)')).toBeInTheDocument();

    // Check for add income source section
    expect(screen.getByText('Add Income Source')).toBeInTheDocument();

    // Check for summary section
    expect(screen.getByText('Annual Primary Income:')).toBeInTheDocument();
    expect(screen.getByText('Annual Additional Income:')).toBeInTheDocument();
    expect(screen.getByText('Total Annual Income:')).toBeInTheDocument();

    // Check for submit button
    expect(screen.getByRole('button', { name: /save and continue/i })).toBeInTheDocument();
  });

  test('updates form values when user inputs data', () => {
    render(
      <TestWrapper>
        <IncomeDetails />
      </TestWrapper>
    );

    // Fill out primary income fields
    fireEvent.change(screen.getByLabelText('Primary Income Amount *'), {
      target: { value: '75000' },
    });
    fireEvent.change(screen.getByLabelText('Frequency *'), { target: { value: 'annual' } });
    fireEvent.change(screen.getByLabelText('Income Type *'), { target: { value: 'salary' } });
    fireEvent.change(screen.getByLabelText('Estimated Tax Rate (%)'), { target: { value: '25' } });

    // Check if values were updated
    expect(screen.getByLabelText('Primary Income Amount *')).toHaveValue(75000);
    expect(screen.getByLabelText('Frequency *')).toHaveValue('annual');
    expect(screen.getByLabelText('Income Type *')).toHaveValue('salary');
    expect(screen.getByLabelText('Estimated Tax Rate (%)')).toHaveValue(25);
  });

  test('adds and removes additional income sources', async () => {
    render(
      <TestWrapper>
        <IncomeDetails />
      </TestWrapper>
    );

    // Initially, no income sources should be visible
    expect(screen.getByText('No additional income sources added yet.')).toBeInTheDocument();

    // Fill out new income source fields
    fireEvent.change(screen.getByLabelText('Income Type *'), {
      target: { value: 'Rental Income' },
    });
    fireEvent.change(screen.getByLabelText('Amount *'), { target: { value: '1500' } });
    fireEvent.change(screen.getByLabelText('Frequency'), { target: { value: 'monthly' } });
    fireEvent.change(screen.getByLabelText('Description'), {
      target: { value: 'Rental property' },
    });

    // Add the income source
    fireEvent.click(screen.getByRole('button', { name: '+ Add Income Source' }));

    // Wait for the income source to be added
    await waitFor(() => {
      expect(screen.queryByText('No additional income sources added yet.')).not.toBeInTheDocument();
      expect(screen.getByText('Rental Income')).toBeInTheDocument();
      expect(screen.getByText('• Rental property')).toBeInTheDocument();
    });

    // Remove the income source
    fireEvent.click(screen.getByRole('button', { name: 'Remove income source' }));

    // Wait for the income source to be removed
    await waitFor(() => {
      expect(screen.getByText('No additional income sources added yet.')).toBeInTheDocument();
    });
  });

  test('calculates income summary correctly', async () => {
    render(
      <TestWrapper>
        <IncomeDetails />
      </TestWrapper>
    );

    // Set primary income
    fireEvent.change(screen.getByLabelText('Primary Income Amount *'), {
      target: { value: '100000' },
    });
    fireEvent.change(screen.getByLabelText('Frequency *'), { target: { value: 'annual' } });

    // Add additional income source
    fireEvent.change(screen.getByLabelText('Income Type *'), {
      target: { value: 'Rental Income' },
    });
    fireEvent.change(screen.getByLabelText('Amount *'), { target: { value: '2000' } });
    fireEvent.change(screen.getByLabelText('Frequency'), { target: { value: 'monthly' } });
    fireEvent.click(screen.getByRole('button', { name: '+ Add Income Source' }));

    // Set tax rate
    fireEvent.change(screen.getByLabelText('Estimated Tax Rate (%)'), { target: { value: '30' } });

    // Wait for calculations to update
    await waitFor(() => {
      // Annual primary income should be $100,000
      expect(screen.getByText('$100,000')).toBeInTheDocument();

      // Annual additional income should be $24,000 ($2,000 * 12)
      expect(screen.getByText('$24,000')).toBeInTheDocument();

      // Total annual income should be $124,000
      expect(screen.getByText('$124,000')).toBeInTheDocument();

      // After-tax income should be $86,800 ($124,000 * 0.7)
      expect(screen.getByText('$86,800')).toBeInTheDocument();

      // Monthly income should be $7,233 ($86,800 / 12)
      expect(screen.getByText('$7,233')).toBeInTheDocument();
    });
  });

  test('calls onComplete when form is submitted', async () => {
    const handleComplete = jest.fn();

    render(
      <TestWrapper>
        <IncomeDetails onComplete={handleComplete} />
      </TestWrapper>
    );

    // Fill out required fields
    fireEvent.change(screen.getByLabelText('Primary Income Amount *'), {
      target: { value: '75000' },
    });

    // Submit the form
    fireEvent.click(screen.getByRole('button', { name: /save and continue/i }));

    // Wait for the save process to complete
    await waitFor(() => {
      expect(handleComplete).toHaveBeenCalledTimes(1);
    });
  });

  test('shows save indicator after auto-save', async () => {
    render(
      <TestWrapper>
        <IncomeDetails />
      </TestWrapper>
    );

    // Change a field to trigger auto-save
    fireEvent.change(screen.getByLabelText('Primary Income Amount *'), {
      target: { value: '75000' },
    });

    // Wait for the save indicator to appear
    await waitFor(() => {
      expect(screen.getByText('✓ Saved')).toBeInTheDocument();
    });

    // Wait for the save indicator to disappear
    await waitFor(
      () => {
        expect(screen.getByText('✓ Saved')).not.toBeVisible();
      },
      { timeout: 3000 }
    );
  });

  test('calls onSave with form data when saving', async () => {
    const handleSave = jest.fn();

    render(
      <TestWrapper>
        <IncomeDetails onSave={handleSave} />
      </TestWrapper>
    );

    // Fill out required fields
    fireEvent.change(screen.getByLabelText('Primary Income Amount *'), {
      target: { value: '75000' },
    });

    // Submit the form
    fireEvent.click(screen.getByRole('button', { name: /save and continue/i }));

    // Wait for the save function to be called
    await waitFor(() => {
      expect(handleSave).toHaveBeenCalledTimes(1);
      expect(handleSave).toHaveBeenCalledWith(
        expect.objectContaining({
          primaryIncome: '75000',
        })
      );
    });
  });
});
