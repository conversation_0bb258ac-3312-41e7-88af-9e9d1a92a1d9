import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useGuidedJourney } from '../../../GuidedJourney/context/GuidedJourneyContext';
import { useFinancialCompass } from '../../context/FinancialCompassContext';
import { formatCurrency, formatPercentage } from '../../../../utils/formatters';
import {
  calculateCashFlowHealthScore,
  calculateDebtManagementScore,
  calculateEmergencyFundScore,
  calculateNetWorthScore,
  calculateSimpleFinancialHealthScore,
} from '../../../../utils/financialHealthCalculator';

interface NorthDirectionTrackerProps {
  onSectionSelect?: (sectionId: string) => void;
}

const NorthDirectionTracker: React.FC<NorthDirectionTrackerProps> = ({ onSectionSelect }) => {
  const { theme } = useTheme();
  const { updateActiveDirection } = useGuidedJourney();
  const { northSections, updateNorthProgress, data } = useFinancialCompass();
  const [completedSections, setCompletedSections] = useState<string[]>([]);
  const [showFinancialSnapshot, setShowFinancialSnapshot] = useState(false);

  // Calculate overall progress for North direction
  const totalSections = northSections?.length || 0;
  const completedCount = completedSections.length;
  const progressPercentage = totalSections > 0 ? (completedCount / totalSections) * 100 : 0;

  // Extract financial data
  const personalInfo = data.north?.personalInformation || {};
  const incomeDetails = data.north?.incomeDetails || {};
  const expenseDetails = data.north?.expenseDetails || {};
  const assets = data.north?.assets || {};
  const liabilities = data.north?.liabilities || {};
  const netWorthDetails = data.north?.netWorthDetails || {};
  const riskAssessment = data.north?.riskAssessment || {};

  // Calculate key financial metrics
  const totalMonthlyIncome = parseFloat(incomeDetails.primaryIncome || '0');
  const totalMonthlyExpenses = parseFloat(expenseDetails.totalMonthlyExpenses || '0');
  const monthlyCashFlow = totalMonthlyIncome - totalMonthlyExpenses;
  const annualCashFlow = monthlyCashFlow * 12;

  const totalAssets = parseFloat(netWorthDetails.totalAssets || '0');
  const totalLiabilities = parseFloat(netWorthDetails.totalLiabilities || '0');
  const netWorth = totalAssets - totalLiabilities;

  const debtToIncomeRatio =
    totalMonthlyIncome > 0 ? (totalLiabilities / (totalMonthlyIncome * 12)) * 100 : 0;

  const savingsRate = totalMonthlyIncome > 0 ? (monthlyCashFlow / totalMonthlyIncome) * 100 : 0;

  // Calculate financial health scores
  const cashFlowScore = calculateCashFlowHealthScore(monthlyCashFlow, totalMonthlyIncome);
  const debtScore = calculateDebtManagementScore(
    totalLiabilities,
    totalMonthlyIncome * 12,
    debtToIncomeRatio
  );
  const emergencyFundScore = calculateEmergencyFundScore(
    (assets.cash?.checking || 0) + (assets.cash?.savings || 0),
    totalMonthlyExpenses
  );
  const netWorthScore = calculateNetWorthScore(netWorth, totalMonthlyIncome * 12);
  const overallScore = calculateSimpleFinancialHealthScore(
    cashFlowScore,
    debtScore,
    emergencyFundScore,
    netWorthScore
  );

  // Determine financial health status
  const getFinancialHealthStatus = () => {
    if (overallScore >= 80) return 'Excellent';
    if (overallScore >= 70) return 'Very Good';
    if (overallScore >= 60) return 'Good';
    if (overallScore >= 50) return 'Fair';
    if (overallScore >= 40) return 'Needs Attention';
    return 'Critical';
  };

  // Get color based on score
  const getScoreColor = (score: number) => {
    if (score >= 80) return theme.colors.success.main;
    if (score >= 60) return theme.colors.success.light;
    if (score >= 40) return theme.colors.warning.main;
    return theme.colors.error.main;
  };

  // Get recommendations based on financial health
  const getRecommendations = () => {
    const recommendations = [];

    // Cash flow recommendations
    if (cashFlowScore < 60) {
      recommendations.push({
        title: 'Improve Cash Flow',
        description:
          'Your monthly expenses are too high relative to your income. Review your expense details to identify areas to cut back.',
        priority: 'High',
        action: 'expense_details',
      });
    }

    // Debt recommendations
    if (debtScore < 60) {
      recommendations.push({
        title: 'Reduce Debt Burden',
        description:
          'Your debt-to-income ratio is concerning. Consider creating a debt reduction plan focusing on high-interest debt first.',
        priority: 'High',
        action: 'liabilities_debt',
      });
    }

    // Emergency fund recommendations
    if (emergencyFundScore < 60) {
      recommendations.push({
        title: 'Build Emergency Fund',
        description:
          'Your emergency savings are insufficient. Aim to save 3-6 months of expenses in liquid accounts.',
        priority: emergencyFundScore < 40 ? 'High' : 'Medium',
        action: 'assets_investments',
      });
    }

    // Net worth recommendations
    if (netWorthScore < 60) {
      recommendations.push({
        title: 'Increase Net Worth',
        description:
          'Focus on building assets and reducing liabilities to improve your overall financial position.',
        priority: 'Medium',
        action: 'assets_investments',
      });
    }

    // If all scores are good, provide positive reinforcement
    if (recommendations.length === 0) {
      recommendations.push({
        title: 'Maintain Your Strong Position',
        description:
          'Your financial health is solid. Continue your good habits and consider increasing investments for long-term growth.',
        priority: 'Low',
        action: 'assets_investments',
      });
    }

    return recommendations;
  };

  useEffect(() => {
    // Set active direction to North
    if (updateActiveDirection) {
      updateActiveDirection('north');
    }

    // Update progress in Financial Compass context
    if (updateNorthProgress) {
      updateNorthProgress(progressPercentage);
    }

    // Check which sections are completed
    if (northSections) {
      const completed = northSections
        .filter((section) => section.isCompleted)
        .map((section) => section.id);
      setCompletedSections(completed);
    }
  }, [northSections, progressPercentage, updateActiveDirection, updateNorthProgress]);

  const handleSectionClick = (sectionId: string) => {
    if (onSectionSelect) {
      onSectionSelect(sectionId);
    }
  };

  // Toggle financial snapshot visibility
  const toggleFinancialSnapshot = () => {
    setShowFinancialSnapshot(!showFinancialSnapshot);
  };

  return (
    <TrackerContainer theme={theme}>
      <TrackerHeader theme={theme}>
        <DirectionTitle theme={theme}>North: Where You Are</DirectionTitle>
        <DirectionDescription theme={theme}>
          The North direction helps you understand your current financial position - your income,
          expenses, assets, and liabilities.
        </DirectionDescription>
      </TrackerHeader>

      {/* Financial Dashboard */}
      <DashboardContainer>
        {/* Financial Health Overview */}
        <DashboardSection>
          <DashboardCard theme={theme}>
            <CardHeader>
              <CardTitle theme={theme}>Financial Health</CardTitle>
              <HealthScoreValue score={overallScore}>{Math.round(overallScore)}</HealthScoreValue>
            </CardHeader>
            <HealthScoreStatus score={overallScore}>{getFinancialHealthStatus()}</HealthScoreStatus>
            <HealthScoreBar theme={theme}>
              <HealthScoreFill score={overallScore} theme={theme} />
            </HealthScoreBar>
            <HealthScoreBreakdown>
              <ScoreCategory>
                <ScoreCategoryLabel>Cash Flow</ScoreCategoryLabel>
                <ScoreCategoryValue color={getScoreColor(cashFlowScore)}>
                  {Math.round(cashFlowScore)}
                </ScoreCategoryValue>
              </ScoreCategory>
              <ScoreCategory>
                <ScoreCategoryLabel>Debt</ScoreCategoryLabel>
                <ScoreCategoryValue color={getScoreColor(debtScore)}>
                  {Math.round(debtScore)}
                </ScoreCategoryValue>
              </ScoreCategory>
              <ScoreCategory>
                <ScoreCategoryLabel>Emergency</ScoreCategoryLabel>
                <ScoreCategoryValue color={getScoreColor(emergencyFundScore)}>
                  {Math.round(emergencyFundScore)}
                </ScoreCategoryValue>
              </ScoreCategory>
              <ScoreCategory>
                <ScoreCategoryLabel>Net Worth</ScoreCategoryLabel>
                <ScoreCategoryValue color={getScoreColor(netWorthScore)}>
                  {Math.round(netWorthScore)}
                </ScoreCategoryValue>
              </ScoreCategory>
            </HealthScoreBreakdown>
            <HealthInsight theme={theme}>
              {overallScore >= 80
                ? 'Your financial health is excellent. Focus on optimizing and growing your wealth.'
                : overallScore >= 60
                  ? 'Your financial health is good. Continue building on your strong foundation.'
                  : overallScore >= 40
                    ? 'Your financial health needs attention. Focus on the areas highlighted below.'
                    : 'Your financial health requires immediate attention. Address the critical areas below.'}
            </HealthInsight>
          </DashboardCard>
        </DashboardSection>

        {/* Cash Flow & Savings */}
        <DashboardSection>
          <DashboardCard theme={theme}>
            <CardHeader>
              <CardTitle theme={theme}>Cash Flow & Savings</CardTitle>
              <CardIcon positive={monthlyCashFlow > 0}>💰</CardIcon>
            </CardHeader>
            <MetricHighlight positive={monthlyCashFlow > 0}>
              {formatCurrency(monthlyCashFlow)}/month
            </MetricHighlight>
            <MetricDescription>
              {monthlyCashFlow > 0
                ? `You're saving ${formatPercentage(savingsRate)} of your income`
                : 'Your expenses exceed your income'}
            </MetricDescription>

            <MetricBreakdown>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Monthly Income</MetricBreakdownLabel>
                <MetricBreakdownValue>{formatCurrency(totalMonthlyIncome)}</MetricBreakdownValue>
              </MetricBreakdownItem>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Monthly Expenses</MetricBreakdownLabel>
                <MetricBreakdownValue>{formatCurrency(totalMonthlyExpenses)}</MetricBreakdownValue>
              </MetricBreakdownItem>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Annual Cash Flow</MetricBreakdownLabel>
                <MetricBreakdownValue>{formatCurrency(annualCashFlow)}</MetricBreakdownValue>
              </MetricBreakdownItem>
            </MetricBreakdown>

            <CashFlowInsight theme={theme}>
              {monthlyCashFlow > 0 && savingsRate >= 20
                ? "Excellent savings rate! You're well-positioned for financial growth."
                : monthlyCashFlow > 0 && savingsRate >= 10
                  ? 'Good savings rate. Consider increasing your savings to accelerate your financial goals.'
                  : monthlyCashFlow > 0
                    ? 'You have positive cash flow, but your savings rate is low. Look for ways to increase savings.'
                    : 'Your expenses exceed your income. Review your budget to find areas to cut back.'}
            </CashFlowInsight>
          </DashboardCard>
        </DashboardSection>

        {/* Net Worth */}
        <DashboardSection>
          <DashboardCard theme={theme}>
            <CardHeader>
              <CardTitle theme={theme}>Net Worth</CardTitle>
              <CardIcon positive={netWorth > 0}>📊</CardIcon>
            </CardHeader>
            <MetricHighlight positive={netWorth > 0}>{formatCurrency(netWorth)}</MetricHighlight>
            <MetricDescription>
              {netWorth > 0
                ? `Assets exceed liabilities by ${formatCurrency(netWorth)}`
                : 'Liabilities exceed assets'}
            </MetricDescription>

            <MetricBreakdown>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Total Assets</MetricBreakdownLabel>
                <MetricBreakdownValue>{formatCurrency(totalAssets)}</MetricBreakdownValue>
              </MetricBreakdownItem>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Total Liabilities</MetricBreakdownLabel>
                <MetricBreakdownValue>{formatCurrency(totalLiabilities)}</MetricBreakdownValue>
              </MetricBreakdownItem>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Debt-to-Income</MetricBreakdownLabel>
                <MetricBreakdownValue>{formatPercentage(debtToIncomeRatio)}</MetricBreakdownValue>
              </MetricBreakdownItem>
            </MetricBreakdown>

            <NetWorthInsight theme={theme}>
              {netWorth > 0 && debtToIncomeRatio < 36
                ? 'Your net worth is positive with a healthy debt-to-income ratio. Continue building assets.'
                : netWorth > 0
                  ? 'Your net worth is positive, but your debt level is high. Focus on debt reduction.'
                  : 'Your net worth is negative. Focus on paying down high-interest debt and building assets.'}
            </NetWorthInsight>
          </DashboardCard>
        </DashboardSection>

        {/* Emergency Preparedness */}
        <DashboardSection>
          <DashboardCard theme={theme}>
            <CardHeader>
              <CardTitle theme={theme}>Emergency Preparedness</CardTitle>
              <CardIcon positive={emergencyFundScore > 60}>🛡️</CardIcon>
            </CardHeader>
            <MetricHighlight positive={emergencyFundScore > 60}>
              {totalMonthlyExpenses > 0
                ? `${(((assets.cash?.checking || 0) + (assets.cash?.savings || 0)) / totalMonthlyExpenses).toFixed(1)} months`
                : 'N/A'}
            </MetricHighlight>
            <MetricDescription>
              {emergencyFundScore > 80
                ? 'Excellent emergency savings'
                : emergencyFundScore > 60
                  ? 'Good emergency savings'
                  : 'Insufficient emergency savings'}
            </MetricDescription>

            <MetricBreakdown>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Cash Savings</MetricBreakdownLabel>
                <MetricBreakdownValue>
                  {formatCurrency((assets.cash?.checking || 0) + (assets.cash?.savings || 0))}
                </MetricBreakdownValue>
              </MetricBreakdownItem>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Monthly Expenses</MetricBreakdownLabel>
                <MetricBreakdownValue>{formatCurrency(totalMonthlyExpenses)}</MetricBreakdownValue>
              </MetricBreakdownItem>
              <MetricBreakdownItem>
                <MetricBreakdownLabel>Target (6 months)</MetricBreakdownLabel>
                <MetricBreakdownValue>
                  {formatCurrency(totalMonthlyExpenses * 6)}
                </MetricBreakdownValue>
              </MetricBreakdownItem>
            </MetricBreakdown>

            <EmergencyInsight theme={theme}>
              {emergencyFundScore > 80
                ? 'Your emergency fund is well-established. Consider investing additional savings for growth.'
                : emergencyFundScore > 60
                  ? 'Your emergency fund is good. Continue building toward 6 months of expenses.'
                  : emergencyFundScore > 40
                    ? 'Your emergency fund needs strengthening. Aim for at least 3-6 months of expenses.'
                    : 'Building an emergency fund should be a top priority. Start with a goal of 1 month of expenses.'}
            </EmergencyInsight>
          </DashboardCard>
        </DashboardSection>
      </DashboardContainer>

      {/* Priority Actions */}
      <ActionSection theme={theme}>
        <ActionSectionTitle theme={theme}>Priority Actions</ActionSectionTitle>
        <ActionCards>
          {getRecommendations().map((recommendation, index) => (
            <ActionCard
              key={index}
              priority={recommendation.priority}
              theme={theme}
              onClick={() => recommendation.action && handleSectionClick(recommendation.action)}
            >
              <ActionHeader>
                <ActionTitle>{recommendation.title}</ActionTitle>
                <PriorityBadge priority={recommendation.priority} theme={theme}>
                  {recommendation.priority}
                </PriorityBadge>
              </ActionHeader>
              <ActionDescription>{recommendation.description}</ActionDescription>
              <ActionButtonContainer>
                <ActionButton theme={theme}>Take Action</ActionButton>
              </ActionButtonContainer>
            </ActionCard>
          ))}
        </ActionCards>
      </ActionSection>

      {/* Financial Journey */}
      <JourneySection theme={theme}>
        <JourneySectionTitle theme={theme}>Your Financial Journey</JourneySectionTitle>
        <JourneyDescription theme={theme}>
          Complete these sections to build a comprehensive picture of your current financial
          position.
        </JourneyDescription>

        <JourneySteps>
          {northSections?.map((section) => (
            <JourneyStep
              key={section.id}
              onClick={() => handleSectionClick(section.id)}
              theme={theme}
              isActive={section.isActive}
            >
              <JourneyStepIcon theme={theme}>{section.icon || '○'}</JourneyStepIcon>
              <JourneyStepContent>
                <JourneyStepTitle theme={theme}>{section.title}</JourneyStepTitle>
                {section.description && (
                  <JourneyStepDescription theme={theme}>
                    {section.description}
                  </JourneyStepDescription>
                )}
              </JourneyStepContent>
              <JourneyStepArrow theme={theme}>→</JourneyStepArrow>
            </JourneyStep>
          ))}
        </JourneySteps>
      </JourneySection>
    </TrackerContainer>
  );
};

const TrackerContainer = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.secondary};
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const TrackerHeader = styled.div<{ theme: any }>`
  margin-bottom: 24px;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 16px;
  position: relative;
`;

const DirectionTitle = styled.h2<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.5rem;
  font-weight: bold;
`;

const DirectionDescription = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0 0 16px 0;
  font-size: 1rem;
  line-height: 1.5;
`;

// Dashboard Components
const DashboardContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
`;

const DashboardSection = styled.div`
  display: flex;
  flex-direction: column;
`;

const DashboardCard = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  height: 100%;
  display: flex;
  flex-direction: column;
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }
`;

const CardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const CardTitle = styled.h3<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
`;

const CardIcon = styled.div<{ positive: boolean }>`
  font-size: 1.8rem;
  color: ${(props) => (props.positive ? '#10b981' : '#ef4444')};
`;

const MetricHighlight = styled.div<{ positive: boolean }>`
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 8px;
  color: ${(props) => (props.positive ? '#10b981' : '#ef4444')};
`;

const MetricDescription = styled.div`
  font-size: 0.9rem;
  color: ${(props) => props.theme.colors.text.secondary};
  margin-bottom: 16px;
`;

const MetricBreakdown = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
  padding: 12px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-radius: 8px;
`;

const MetricBreakdownItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const MetricBreakdownLabel = styled.div`
  font-size: 0.85rem;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const MetricBreakdownValue = styled.div`
  font-size: 0.9rem;
  font-weight: 600;
  color: ${(props) => props.theme.colors.text.primary};
`;

const CashFlowInsight = styled.div<{ theme: any }>`
  font-size: 0.9rem;
  line-height: 1.5;
  color: ${(props) => props.theme.colors.text.primary};
  margin-top: auto;
  padding-top: 12px;
  border-top: 1px solid ${(props) => props.theme.colors.border.main};
`;

const NetWorthInsight = styled.div<{ theme: any }>`
  font-size: 0.9rem;
  line-height: 1.5;
  color: ${(props) => props.theme.colors.text.primary};
  margin-top: auto;
  padding-top: 12px;
  border-top: 1px solid ${(props) => props.theme.colors.border.main};
`;

const EmergencyInsight = styled.div<{ theme: any }>`
  font-size: 0.9rem;
  line-height: 1.5;
  color: ${(props) => props.theme.colors.text.primary};
  margin-top: auto;
  padding-top: 12px;
  border-top: 1px solid ${(props) => props.theme.colors.border.main};
`;

const HealthInsight = styled.div<{ theme: any }>`
  font-size: 0.9rem;
  line-height: 1.5;
  color: ${(props) => props.theme.colors.text.primary};
  margin-top: auto;
  padding-top: 12px;
  border-top: 1px solid ${(props) => props.theme.colors.border.main};
`;

// Action Section
const ActionSection = styled.div<{ theme: any }>`
  margin-bottom: 32px;
  padding: 20px;
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
`;

const ActionSectionTitle = styled.h3<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 20px 0;
  font-size: 1.3rem;
  font-weight: 600;
  border-bottom: 1px solid ${(props) => props.theme.colors.border.main};
  padding-bottom: 12px;
`;

const ActionCards = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
`;

const ActionCard = styled.div<{ priority: string; theme: any }>`
  padding: 16px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.background.tertiary};
  border-left: 4px solid
    ${(props) =>
      props.priority === 'High'
        ? props.theme.colors.error.main
        : props.priority === 'Medium'
          ? props.theme.colors.warning.main
          : props.theme.colors.success.main};
  cursor: pointer;
  transition: transform 0.2s ease;
  display: flex;
  flex-direction: column;
  height: 100%;

  &:hover {
    transform: translateY(-4px);
  }
`;

const ActionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const ActionTitle = styled.h4`
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
`;

const ActionDescription = styled.p`
  margin: 0;
  font-size: 0.9rem;
  color: ${(props) => props.theme.colors.text.secondary};
  line-height: 1.5;
  flex: 1;
`;

const ActionButtonContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  align-items: center;
`;

const ActionButton = styled.div<{ theme: any }>`
  display: inline-block;
  padding: 8px 16px;
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.text.onPrimary};
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

// Journey Section
const JourneySection = styled.div<{ theme: any }>`
  padding: 20px;
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
`;

const JourneySectionTitle = styled.h3<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.primary};
  margin: 0 0 8px 0;
  font-size: 1.3rem;
  font-weight: 600;
`;

const JourneyDescription = styled.p<{ theme: any }>`
  color: ${(props) => props.theme.colors.text.secondary};
  margin: 0 0 20px 0;
  font-size: 0.9rem;
  line-height: 1.5;
`;

const JourneySteps = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const JourneyStep = styled.div<{ theme: any; isActive: boolean }>`
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  background-color: ${(props) =>
    props.isActive
      ? props.theme.colors.background.highlight
      : props.theme.colors.background.tertiary};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.hover};
    transform: translateX(8px);
  }
`;

const JourneyStepIcon = styled.div<{ theme: any }>`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.text.onPrimary};
  margin-right: 16px;
  font-size: 1.2rem;
`;

const JourneyStepContent = styled.div`
  flex: 1;
`;

const JourneyStepTitle = styled.h4<{ theme: any }>`
  margin: 0 0 4px 0;
  font-size: 1.1rem;
  color: ${(props) => props.theme.colors.text.primary};
`;

const JourneyStepDescription = styled.p<{ theme: any }>`
  margin: 0;
  font-size: 0.85rem;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const JourneyStepArrow = styled.div<{ theme: any }>`
  font-size: 1.5rem;
  color: ${(props) => props.theme.colors.text.secondary};
  margin-left: 16px;
`;

// Health Score Card
const HealthScoreValue = styled.div<{ score: number }>`
  font-size: 1.8rem;
  font-weight: 700;
  color: ${(props) => {
    if (props.score >= 80) return '#10b981'; // success
    if (props.score >= 60) return '#22c55e'; // success light
    if (props.score >= 40) return '#f59e0b'; // warning
    return '#ef4444'; // error
  }};
`;

const HealthScoreStatus = styled.div<{ score: number }>`
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: ${(props) => {
    if (props.score >= 80) return '#10b981'; // success
    if (props.score >= 60) return '#22c55e'; // success light
    if (props.score >= 40) return '#f59e0b'; // warning
    return '#ef4444'; // error
  }};
`;

const HealthScoreBar = styled.div<{ theme: any }>`
  height: 8px;
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 16px;
`;

const HealthScoreFill = styled.div<{ score: number; theme: any }>`
  height: 100%;
  width: ${(props) => props.score}%;
  background-color: ${(props) => {
    if (props.score >= 80) return props.theme.colors.success.main;
    if (props.score >= 60) return props.theme.colors.success.light;
    if (props.score >= 40) return props.theme.colors.warning.main;
    return props.theme.colors.error.main;
  }};
  transition: width 0.5s ease;
`;

const HealthScoreBreakdown = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
`;

const ScoreCategory = styled.div`
  text-align: center;
`;

const ScoreCategoryLabel = styled.div`
  font-size: 0.8rem;
  margin-bottom: 4px;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const ScoreCategoryValue = styled.div<{ color: string }>`
  font-size: 1.1rem;
  font-weight: 700;
  color: ${(props) => props.color};
`;

// Priority Badge
const PriorityBadge = styled.span<{ priority: string; theme: any }>`
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
  background-color: ${(props) =>
    props.priority === 'High'
      ? props.theme.colors.error.light
      : props.priority === 'Medium'
        ? props.theme.colors.warning.light
        : props.theme.colors.success.light};
  color: ${(props) =>
    props.priority === 'High'
      ? props.theme.colors.error.dark
      : props.priority === 'Medium'
        ? props.theme.colors.warning.dark
        : props.theme.colors.success.dark};
`;

export default NorthDirectionTracker;
