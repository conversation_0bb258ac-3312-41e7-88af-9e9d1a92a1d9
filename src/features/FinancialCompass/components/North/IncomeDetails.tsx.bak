import React, { useState, useEffect, useCallback } from 'react';
import { useTheme } from '@mui/material/styles';
import {
  Box,
  Button,
  TextField,
  Typography,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Divider,
  IconButton,
  InputAdornment,
  Card,
  CardContent,
  Snackbar,
  Alert,
  useMediaQuery,
  SelectChangeEvent
} from '@mui/material';
import { Save, ArrowBack, Add, Delete } from '@mui/icons-material';

// =============================================
// Types and Interfaces
// =============================================
type FilingStatus = 'SINGLE' | 'MARRIED_FILING_JOINTLY' | 'MARRIED_FILING_SEPARATELY' | 'HEAD_OF_HOUSEHOLD';
type IncomeFrequency = 'yearly' | 'monthly' | 'bi-weekly' | 'weekly';

interface IncomeSource {
  id: string;
  source: string;
  amount: string;
  frequency: IncomeFrequency;
}

interface IncomeFormData {
  primaryIncome: string;
  filingStatus: FilingStatus;
  additionalIncome: IncomeSource[];
  state: string;
  otherIncome: string;
  otherIncomeDescription: string;
}

interface TaxCalculationResult {
  federal: number;
  state: number;
  fica: number;
  effectiveRate: number;
}

interface IncomeDetailsProps {
  onComplete?: () => void;
  onSave?: (data: IncomeFormData) => Promise<void>;
  onBack?: () => void;
  data?: Partial<IncomeFormData>;
  updateData?: (data: IncomeFormData) => void;
}

// =============================================
// Constants
// =============================================
const STATE_OPTIONS = [
  { value: 'CA', label: 'California' },
  { value: 'NY', label: 'New York' },
  { value: 'TX', label: 'Texas' },
  { value: 'FL', label: 'Florida' },
  { value: 'IL', label: 'Illinois' },
];

const FREQUENCY_OPTIONS = [
  { value: 'yearly' as const, label: 'Yearly' },
  { value: 'monthly' as const, label: 'Monthly' },
  { value: 'bi-weekly' as const, label: 'Bi-Weekly' },
  { value: 'weekly' as const, label: 'Weekly' },
];

// =============================================
// Utility Functions
// =============================================
const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);
};

const parseCurrencyInput = (value: string): number => {
  return parseFloat(value.replace(/[^0-9.-]+/g, '')) || 0;
};

// =============================================
// Tax Calculation Utilities
// =============================================
const calculateFederalTax = (income: number, status: FilingStatus = 'SINGLE'): number => {
  const brackets = {
    SINGLE: [
      { min: 0, max: 10275, rate: 0.10 },
      { min: 10276, max: 41775, rate: 0.12 },
      { min: 41776, max: 89075, rate: 0.22 },
      { min: 89076, max: 170050, rate: 0.24 },
      { min: 170051, max: 215950, rate: 0.32 },
      { min: 215951, max: 539900, rate: 0.35 },
      { min: 539901, max: Infinity, rate: 0.37 }
    ],
    MARRIED_FILING_JOINTLY: [
      { min: 0, max: 20550, rate: 0.10 },
      { min: 20551, max: 83550, rate: 0.12 },
      { min: 83551, max: 178150, rate: 0.22 },
      { min: 178151, max: 340100, rate: 0.24 },
      { min: 340101, max: 431900, rate: 0.32 },
      { min: 431901, max: 647850, rate: 0.35 },
      { min: 647851, max: Infinity, rate: 0.37 }
    ],
    MARRIED_FILING_SEPARATELY: [
      { min: 0, max: 10275, rate: 0.10 },
      { min: 10276, max: 41775, rate: 0.12 },
      { min: 41776, max: 89075, rate: 0.22 },
      { min: 89076, max: 170050, rate: 0.24 },
      { min: 170051, max: 215950, rate: 0.32 },
      { min: 215951, max: 323925, rate: 0.35 },
      { min: 323926, max: Infinity, rate: 0.37 }
    ],
    HEAD_OF_HOUSEHOLD: [
      { min: 0, max: 14650, rate: 0.10 },
      { min: 14651, max: 55900, rate: 0.12 },
      { min: 55901, max: 89050, rate: 0.22 },
      { min: 89051, max: 170050, rate: 0.24 },
      { min: 170051, max: 215950, rate: 0.32 },
      { min: 215951, max: 539900, rate: 0.35 },
      { min: 539901, max: Infinity, rate: 0.37 }
    ]
  };

  const bracket = brackets[status] || brackets.SINGLE;
  let tax = 0;
  let remainingIncome = income;

  for (const { min, max, rate } of bracket) {
    if (remainingIncome <= 0) break;
    const taxableInBracket = Math.min(remainingIncome, max === Infinity ? remainingIncome : max - min + 1);
    tax += taxableInBracket * rate;
    remainingIncome -= taxableInBracket;
  }

  return Math.round(tax * 100) / 100;
};

const calculateStateTax = (income: number, state: string = ''): number => {
  const stateTaxRates: Record<string, number> = {
    'CA': 0.093,
    'NY': 0.0882,
    'TX': 0,
    'FL': 0,
    'IL': 0.0495
  };
  const rate = stateTaxRates[state] || 0.05;
  return Math.round(income * rate * 100) / 100;
};

const calculateFICATax = (income: number): number => {
  const socialSecurityTax = Math.min(income, 147000) * 0.062;
  let medicareTax = income * 0.0145;
  if (income > 200000) {
    medicareTax += (income - 200000) * 0.009;
  }
  return Math.round((socialSecurityTax + medicareTax) * 100) / 100;
};

const calculateTaxLiability = (data: IncomeFormData): TaxCalculationResult => {
  const grossIncome = parseCurrencyInput(data.primaryIncome);
  const federalTax = calculateFederalTax(grossIncome, data.filingStatus);
  const stateTax = calculateStateTax(grossIncome, data.state);
  const ficaTax = calculateFICATax(grossIncome);
  const totalTax = federalTax + stateTax + ficaTax;
  
  return {
    federal: federalTax,
    state: stateTax,
    fica: ficaTax,
    effectiveRate: grossIncome > 0 ? (totalTax / grossIncome) * 100 : 0
  };
};

const calculateNetIncome = (data: IncomeFormData): number => {
  const grossIncome = parseCurrencyInput(data.primaryIncome);
  const taxes = calculateTaxLiability(data);
  return grossIncome - (taxes.federal + taxes.state + taxes.fica);
};

// =============================================
// Form Validation
// =============================================
const validateIncomeForm = (data: IncomeFormData): Record<string, string> => {
  const errors: Record<string, string> = {};
  const income = parseCurrencyInput(data.primaryIncome);
  
  if (isNaN(income) || income <= 0) {
    errors.primaryIncome = 'Please enter a valid income amount';
  }
  
  if (!data.filingStatus) {
    errors.filingStatus = 'Please select a filing status';
  }
  
  return errors;
};

const IncomeDetails: React.FC<IncomeDetailsProps> = ({
  onComplete = () => {},
  onSave = async () => {},
  onBack,
  data = {},
  updateData = () => {}
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  // Form state
  const [formValues, setFormValues] = useState<IncomeFormData>({
    primaryIncome: '',
    filingStatus: 'SINGLE',
    additionalIncome: [],
    state: '',
    otherIncome: '',
    otherIncomeDescription: '',
    ...data
  });
  
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error'
  });

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormValues(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle select changes
  const handleSelectChange = (e: React.ChangeEvent<{ name?: string; value: unknown }>) => {
    const name = e.target.name as string;
    const value = e.target.value;
    
    setFormValues(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle income source changes
  const handleIncomeSourceChange = (id: string, field: keyof IncomeSource, value: string) => {
    setFormValues(prev => ({
      ...prev,
      additionalIncome: prev.additionalIncome.map(source => 
        source.id === id ? { ...source, [field]: value } : source
      )
    }));
  };

  // Add new income source
  const addIncomeSource = () => {
    setFormValues(prev => ({
      ...prev,
      additionalIncome: [
        ...prev.additionalIncome,
        { id: Date.now().toString(), source: '', amount: '', frequency: 'yearly' }
      ]
    }));
  };

  // Remove income source
  const removeIncomeSource = (id: string) => {
    setFormValues(prev => ({
      ...prev,
      additionalIncome: prev.additionalIncome.filter(source => source.id !== id)
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    const errors = validateIncomeForm(formValues);
    setFormErrors(errors);
    
    if (Object.keys(errors).length > 0) {
      setSnackbar({
        open: true,
        message: 'Please fix the errors in the form',
        severity: 'error'
      });
      return;
    }
    
    try {
      setIsSubmitting(true);
      await onSave(formValues);
      updateData(formValues);
      onComplete();
    } catch (error) {
      setSnackbar({
        open: true,
        message: 'Failed to save income details. Please try again.',
        severity: 'error'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render the form
  return (
    <Box component="form" onSubmit={handleSubmit} noValidate>
      <Grid container spacing={3}>
        {/* Primary Income */}
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Primary Annual Income"
            name="primaryIncome"
            type="number"
            value={formValues.primaryIncome}
            onChange={handleInputChange}
            error={!!formErrors.primaryIncome}
            helperText={formErrors.primaryIncome}
            InputProps={{
              startAdornment: <InputAdornment position="start">$</InputAdornment>,
            }}
            variant="outlined"
          />
        </Grid>

        {/* Filing Status */}
        <Grid item xs={12} md={6}>
          <FormControl fullWidth variant="outlined" error={!!formErrors.filingStatus}>
            <InputLabel>Filing Status</InputLabel>
            <Select
              name="filingStatus"
              value={formValues.filingStatus}
              onChange={handleSelectChange}
              label="Filing Status"
            >
              <MenuItem value="SINGLE">Single</MenuItem>
              <MenuItem value="MARRIED_FILING_JOINTLY">Married Filing Jointly</MenuItem>
              <MenuItem value="MARRIED_FILING_SEPARATELY">Married Filing Separately</MenuItem>
              <MenuItem value="HEAD_OF_HOUSEHOLD">Head of Household</MenuItem>
            </Select>
            {formErrors.filingStatus && (
              <FormHelperText>{formErrors.filingStatus}</FormHelperText>
            )}
          </FormControl>
        </Grid>

        {/* State */}
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="State (for state tax calculation)"
            name="state"
            value={formValues.state}
            onChange={handleInputChange}
            variant="outlined"
          />
        </Grid>

        {/* Additional Income Sources */}
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            Additional Income Sources
          </Typography>
          {formValues.additionalIncome.map((source, index) => (
            <Grid container spacing={2} key={source.id} alignItems="center" sx={{ mb: 2 }}>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Source"
                  value={source.source}
                  onChange={(e) => handleIncomeSourceChange(source.id, 'source', e.target.value)}
                  variant="outlined"
                />
              </Grid>
              <Grid item xs={12} sm={3}>
                <TextField
                  fullWidth
                  label="Amount"
                  type="number"
                  value={source.amount}
                  onChange={(e) => handleIncomeSourceChange(source.id, 'amount', e.target.value)}
                  InputProps={{
                    startAdornment: <InputAdornment position="start">$</InputAdornment>,
                  }}
                  variant="outlined"
                />
              </Grid>
              <Grid item xs={12} sm={3}>
                <FormControl fullWidth variant="outlined">
                  <InputLabel>Frequency</InputLabel>
                  <Select
                    value={source.frequency}
                    onChange={(e) => handleIncomeSourceChange(source.id, 'frequency', e.target.value as IncomeFrequency)}
                    label="Frequency"
                  >
                    <MenuItem value="yearly">Yearly</MenuItem>
                    <MenuItem value="monthly">Monthly</MenuItem>
                    <MenuItem value="bi-weekly">Bi-Weekly</MenuItem>
                    <MenuItem value="weekly">Weekly</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={2}>
                <IconButton 
                  onClick={() => removeIncomeSource(source.id)}
                  color="error"
                >
                  <Delete />
                </IconButton>
              </Grid>
            </Grid>
          ))}
          <Button
            variant="outlined"
            startIcon={<Add />}
            onClick={addIncomeSource}
            sx={{ mt: 1 }}
          >
            Add Income Source
          </Button>
        </Grid>

        {/* Tax Summary */}
        <Grid item xs={12}>
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Tax Summary
              </Typography>
              {formValues.primaryIncome && (
                <>
                  <Typography>
                    <strong>Gross Income:</strong> {formatCurrency(Number(formValues.primaryIncome) || 0)}
                  </Typography>
                  <Typography>
                    <strong>Federal Tax:</strong> {formatCurrency(calculateFederalTax(Number(formValues.primaryIncome) || 0, formValues.filingStatus))}
                  </Typography>
                  <Typography>
                    <strong>State Tax:</strong> {formatCurrency(calculateStateTax(Number(formValues.primaryIncome) || 0, formValues.state))}
                  </Typography>
                  <Typography>
                    <strong>FICA Tax:</strong> {formatCurrency(calculateFICATax(Number(formValues.primaryIncome) || 0))}
                  </Typography>
                  <Divider sx={{ my: 2 }} />
                  <Typography variant="subtitle1">
                    <strong>Net Income:</strong> {formatCurrency(calculateNetIncome(formValues))}
                  </Typography>
                </>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Form Actions */}
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
            {onBack && (
              <Button
                onClick={onBack}
                startIcon={<ArrowBack />}
                disabled={isSubmitting}
              >
                Back
              </Button>
            )}
            <Box sx={{ flex: 1 }} />
            <Button
              type="submit"
              variant="contained"
              color="primary"
              disabled={isSubmitting}
              startIcon={isSubmitting ? null : <Save />}
            >
              {isSubmitting ? 'Saving...' : 'Save & Continue'}
            </Button>
          </Box>
        </Grid>
      </Grid>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
      >
        <Alert 
          onClose={() => setSnackbar(prev => ({ ...prev, open: false }))} 
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default IncomeDetails;

// Types
type FilingStatusType = 'SINGLE' | 'MARRIED_FILING_JOINTLY' | 'MARRIED_FILING_SEPARATELY' | 'HEAD_OF_HOUSEHOLD';

interface IncomeSource {
  id: string;
  source: string;
  amount: string;
  frequency: 'yearly' | 'monthly' | 'bi-weekly' | 'weekly';
}

interface IncomeFormData {
  primaryIncome: string;
  filingStatus: FilingStatusType;
  additionalIncome: IncomeSource[];
  state: string;
  otherIncome: string;
  otherIncomeDescription: string;
}

interface IncomeDetailsProps {
  onComplete?: () => void;
  onSave?: (data: any) => Promise<void>;
  onBack?: () => void;
  data?: any;
  updateData?: (data: any) => void;
}

// Tax calculation utilities
const calculateFederalTax = (income: number, status: FilingStatusType = 'SINGLE'): number => {
  // Simplified tax brackets for 2023
  const brackets = {
    SINGLE: [
      { min: 0, max: 10275, rate: 0.10 },
      { min: 10276, max: 41775, rate: 0.12 },
      { min: 41776, max: 89075, rate: 0.22 },
      { min: 89076, max: 170050, rate: 0.24 },
      { min: 170051, max: 215950, rate: 0.32 },
      { min: 215951, max: 539900, rate: 0.35 },
      { min: 539901, max: Infinity, rate: 0.37 }
    ],
    MARRIED_FILING_JOINTLY: [
      { min: 0, max: 20550, rate: 0.10 },
      { min: 20551, max: 83550, rate: 0.12 },
      { min: 83551, max: 178150, rate: 0.22 },
      { min: 178151, max: 340100, rate: 0.24 },
      { min: 340101, max: 431900, rate: 0.32 },
      { min: 431901, max: 647850, rate: 0.35 },
      { min: 647851, max: Infinity, rate: 0.37 }
    ]
  };

  const bracket = brackets[status] || brackets.SINGLE;
  let tax = 0;
  let remainingIncome = income;

  for (let i = 0; i < bracket.length; i++) {
    const { min, max, rate } = bracket[i];
    if (remainingIncome <= 0) break;

    const taxableInBracket = Math.min(remainingIncome, max === Infinity ? remainingIncome : max - min + 1);
    tax += taxableInBracket * rate;
    remainingIncome -= taxableInBracket;
  }

  return Math.round(tax * 100) / 100;
};

const calculateStateTax = (income: number, state: string = ''): number => {
  const stateTaxRates: Record<string, number> = {
    'CA': 0.093,
    'NY': 0.0882,
    'TX': 0,
    'FL': 0,
    'IL': 0.0495
  };
  const rate = stateTaxRates[state] || 0.05;
  return Math.round(income * rate * 100) / 100;
};

const calculateFICATax = (income: number): number => {
  const socialSecurityTax = Math.min(income, 147000) * 0.062;
  let medicareTax = income * 0.0145;
  if (income > 200000) {
    medicareTax += (income - 200000) * 0.009;
  }
  return Math.round((socialSecurityTax + medicareTax) * 100) / 100;
};

const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value);
};

const validateIncomeForm = (data: IncomeFormData): Record<string, string> => {
  const errors: Record<string, string> = {};
  if (!data.primaryIncome || isNaN(Number(data.primaryIncome)) || Number(data.primaryIncome) < 0) {
    errors.primaryIncome = 'Please enter a valid income amount';
  }
  return errors;
};

// Types
type FilingStatusType = 'SINGLE' | 'MARRIED_FILING_JOINTLY' | 'MARRIED_FILING_SEPARATELY' | 'HEAD_OF_HOUSEHOLD';

interface IncomeSource {
  id: string;
  source: string;
  amount: string;
  frequency: 'yearly' | 'monthly' | 'bi-weekly' | 'weekly';
}

interface IncomeFormData {
  primaryIncome: string;
  filingStatus: FilingStatusType;
  additionalIncome: IncomeSource[];
  state: string;
  otherIncome: string;
  otherIncomeDescription: string;
}

interface IncomeDetailsProps {
  onComplete?: () => void;
  onSave?: (data: any) => Promise<void>;
  onBack?: () => void;
  data?: any;
  updateData?: (data: any) => void;
}

// Tax calculation utilities
const calculateFederalTax = (income: number, status: FilingStatusType = 'SINGLE'): number => {
  // Simplified tax brackets for 2023
  const brackets = {
    SINGLE: [
      { min: 0, max: 10275, rate: 0.10 },
      { min: 10276, max: 41775, rate: 0.12 },
      { min: 41776, max: 89075, rate: 0.22 },
      { min: 89076, max: 170050, rate: 0.24 },
      { min: 170051, max: 215950, rate: 0.32 },
      { min: 215951, max: 539900, rate: 0.35 },
      { min: 539901, max: Infinity, rate: 0.37 }
    ],
    MARRIED_FILING_JOINTLY: [
      { min: 0, max: 20550, rate: 0.10 },
      { min: 20551, max: 83550, rate: 0.12 },
      { min: 83551, max: 178150, rate: 0.22 },
      { min: 178151, max: 340100, rate: 0.24 },
      { min: 340101, max: 431900, rate: 0.32 },
      { min: 431901, max: 647850, rate: 0.35 },
      { min: 647851, max: Infinity, rate: 0.37 }
    ],
    MARRIED_FILING_SEPARATELY: [
      { min: 0, max: 10275, rate: 0.10 },
      { min: 10276, max: 41775, rate: 0.12 },
      { min: 41776, max: 89075, rate: 0.22 },
      { min: 89076, max: 170050, rate: 0.24 },
      { min: 170051, max: 215950, rate: 0.32 },
      { min: 215951, max: 323925, rate: 0.35 },
      { min: 323926, max: Infinity, rate: 0.37 }
    ],
    HEAD_OF_HOUSEHOLD: [
      { min: 0, max: 14650, rate: 0.10 },
      { min: 14651, max: 55900, rate: 0.12 },
      { min: 55901, max: 89050, rate: 0.22 },
      { min: 89051, max: 170050, rate: 0.24 },
      { min: 170051, max: 215950, rate: 0.32 },
      { min: 215951, max: 539900, rate: 0.35 },
      { min: 539901, max: Infinity, rate: 0.37 }
    ]
  };

  const bracket = brackets[status] || brackets.SINGLE;
  let tax = 0;
  let remainingIncome = income;

  for (let i = 0; i < bracket.length; i++) {
    const { min, max, rate } = bracket[i];
    if (remainingIncome <= 0) break;

    const taxableInBracket = Math.min(remainingIncome, max === Infinity ? remainingIncome : max - min + 1);
    tax += taxableInBracket * rate;
    remainingIncome -= taxableInBracket;
  }

  return Math.round(tax * 100) / 100;
};

const calculateStateTax = (income: number, state: string = ''): number => {
  const stateTaxRates: Record<string, number> = {
    'CA': 0.093,
    'NY': 0.0882,
    'TX': 0,
    'FL': 0,
    'IL': 0.0495
  };

  const rate = stateTaxRates[state] || 0.05;
  return Math.round(income * rate * 100) / 100;
};

const calculateFICATax = (income: number): number => {
  const socialSecurityTax = Math.min(income, 147000) * 0.062;
  let medicareTax = income * 0.0145;
  if (income > 200000) {
    medicareTax += (income - 200000) * 0.009;
  }
  return Math.round((socialSecurityTax + medicareTax) * 100) / 100;
};

const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value);
};

const validateIncomeForm = (data: IncomeFormData): Record<string, string> => {
  const errors: Record<string, string> = {};
  if (!data.primaryIncome || isNaN(Number(data.primaryIncome)) || Number(data.primaryIncome) < 0) {
    errors.primaryIncome = 'Please enter a valid income amount';
  }
  return errors;
};

// Tax calculation utilities
const calculateFederalTax = (income: number, status: FilingStatusType = 'SINGLE'): number => {
  // Map FilingStatusType to the expected status keys
  const statusMap: Record<FilingStatusType, string> = {
    'SINGLE': 'SINGLE',
    'MARRIED_JOINT': 'MARRIED_FILING_JOINTLY',
    'HEAD_OF_HOUSEHOLD': 'HEAD_OF_HOUSEHOLD',
    'MARRIED_SEPARATE': 'SINGLE' // Simplified for this example
  };
  
  const statusKey = statusMap[status] || 'SINGLE';
  const brackets = {
    SINGLE: [
      { max: 10275, rate: 0.10 },
      { max: 41775, rate: 0.12 },
      { max: 89075, rate: 0.22 },
      { max: 170050, rate: 0.24 },
      { max: 215950, rate: 0.32 },
      { max: 539900, rate: 0.35 },
      { max: Infinity, rate: 0.37 }
    ],
    MARRIED_FILING_JOINTLY: [
      { max: 20550, rate: 0.10 },
      { max: 83550, rate: 0.12 },
      { max: 178150, rate: 0.22 },
      { max: 340100, rate: 0.24 },
      { max: 431900, rate: 0.32 },
      { max: 647850, rate: 0.35 },
      { max: Infinity, rate: 0.37 }
    ],
    HEAD_OF_HOUSEHOLD: [
      { max: 14650, rate: 0.10 },
      { max: 55900, rate: 0.12 },
      { max: 89050, rate: 0.22 },
      { max: 170050, rate: 0.24 },
      { max: 215950, rate: 0.32 },
      { max: 539900, rate: 0.35 },
      { max: Infinity, rate: 0.37 }
    ]
  } as const;

  const bracket = brackets[statusKey as keyof typeof brackets] || brackets.SINGLE;
  let tax = 0;
  let remainingIncome = income;
  
  for (const { max, rate } of bracket) {
    if (remainingIncome <= 0) break;
    const taxable = Math.min(remainingIncome, max);
    tax += taxable * rate;
    remainingIncome -= max;
  }
  
  return tax;
};

const calculateStateTax = (income: number, state: string = ''): number => {
  // Simple flat rate for demonstration
  return income * 0.05;
};

const calculateFICATax = (income: number): number => {
  const socialSecurity = Math.min(income, 147000) * 0.062;
  const medicare = income * 0.0145;
  return socialSecurity + medicare;
};


  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value);
};


  const errors: Record<string, string> = {};
  if (!data.primaryIncome) {
    errors.primaryIncome = 'Primary income is required';
  }
  return errors;
};

// Define types for tax calculation result
interface TaxCalculationResult {
  federal: number;
  state: number;
  fica: number;
  effectiveRate: number;
}


  const { annualIncome, filingStatus, state } = data;
  
  const federalTax = calculateFederalTax(annualIncome, filingStatus);
  const stateTax = calculateStateTax(annualIncome, state || '');
  const ficaTax = calculateFICATax(annualIncome);
  
  const totalTax = federalTax + stateTax + ficaTax;
  const effectiveRate = annualIncome > 0 ? (totalTax / annualIncome) * 100 : 0;
  
  return {
    federal: federalTax,
    state: stateTax,
    fica: ficaTax,
    effectiveRate
  };
};


  const { annualIncome } = data;
  const { federal, state, fica } = calculateTaxLiability(data);
  return annualIncome - (federal + state + fica);
};

// TabPanel component
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`income-tabpanel-${index}`}
      aria-labelledby={`income-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const IncomeDetails: React.FC<IncomeDetailsProps> = ({
  onComplete = () => {},
  onSave = async () => {},
  onBack,
  data = {},
  updateData = () => {}
}) => {
  const { enqueueSnackbar } = useSnackbar();
  const [activeTab, setActiveTab] = useState('income');
  
  // Form state
  const [formErrors, setFormErrors] = React.useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [submitSuccess, setSubmitSuccess] = React.useState(false);
  
  // Initialize form state with proper type safety
  const [formValues, setFormValues] = React.useState<IncomeFormData>(() => ({
    ...initialIncomeFormValues,
    ...data,
    incomeSources: (data as IncomeFormData).incomeSources || [],
    budgetAllocation: (data as IncomeFormData).budgetAllocation || {
      needs: 0,
      wants: 0,
      savings: 0,
      needsPercentage: 0,
      wantsPercentage: 0,
      savingsPercentage: 0,
      status: 'needs_adjustment'
    }
  }));
  
  // Calculate total income
  const calculateTotalIncome = useCallback((values: IncomeFormData): number => {
    const primaryIncome = Number(values.primaryIncome) || 0;
    const additionalIncome = (values.incomeSources || []).reduce(
      (sum: number, source: IncomeSource) => sum + (Number(source.amount) || 0), 
      0
    );
    return primaryIncome + additionalIncome;
  }, []);
  const theme = useTheme();

  const handleRemoveIncomeSource = (id: string) => {
    setFormValues(prev => {
      const updatedSources = (prev.incomeSources || []).filter(source => source.id !== id);
      return {
        ...prev,
        incomeSources: updatedSources
      };
    });
  };

  // Handle input changes for form fields
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    setFormValues(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user types
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Handle updates to income source fields
  const handleIncomeSourceChange = (id: string, field: keyof IncomeSource, value: string | number | boolean) => {
    setFormValues(prev => {
      const updatedSources = (prev.incomeSources || []).map(source => {
        if (source.id === id) {
          return { ...source, [field]: value };
        }
        return source;
      });
      
      return {
        ...prev,
        incomeSources: updatedSources as IncomeSource[]
      };
    });
  };

  // Alias for backward compatibility
  const handleUpdateIncomeSource = handleIncomeSourceChange;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    const errors = validateIncomeForm(formValues);
    setFormErrors(errors);
    
    if (Object.keys(errors).length > 0) {
      return;
    }
    
    try {
      setIsSubmitting(true);
      
      // Calculate total income from all sources
      const totalIncome = calculateTotalIncome(formValues);
      
      // Calculate taxes
      const taxLiability = calculateTaxLiability({
        ...formValues,
        annualIncome: totalIncome,
        filingStatus: formValues.filingStatus,
        state: formValues.state || ''
      });
      
      const totalTax = taxLiability.federal + taxLiability.state + taxLiability.fica;
      const netIncome = totalIncome - totalTax;
      
      // Create updated values with proper typing
      const updatedValues: IncomeFormData = {
        ...formValues,
        annualIncome: totalIncome,
        additionalIncome: totalIncome - (Number(formValues.primaryIncome) || 0),
        federalTax: taxLiability.federal,
        stateTax: taxLiability.state,
        ficaTax: taxLiability.fica,
        totalTax,
        effectiveTaxRate: parseFloat(taxLiability.effectiveRate.toFixed(2)),
        afterTaxIncome: netIncome,
        lastUpdated: new Date().toISOString()
      };
      
      // Update form state
      setFormValues(updatedValues);
      
      try {
        // Call save handler if provided
        await onSave(updatedValues);
        setSubmitSuccess(true);
        enqueueSnackbar('Income details saved successfully!', { variant: 'success' });
        
        // Call completion handler if provided
        onComplete();
      } catch (error) {
        console.error('Error saving income details:', error);
        enqueueSnackbar('Failed to save income details. Please try again.', {
          variant: 'error',
        });
      }
    } catch (error) {
      console.error('Error processing form:', error);
      enqueueSnackbar('An error occurred while processing the form.', {
        variant: 'error',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAddIncomeSource = () => {
    const newSource: IncomeSource = {
      id: `source-${Date.now()}`,
      source: 'New Income Source',
      type: 'other',
      amount: '0',
      frequency: 'monthly',
      description: '',
      isTaxable: true,
      isPreTax: false
    };
    
    setFormValues(prev => ({
      ...prev,
      incomeSources: [...(prev.incomeSources || []), newSource]
    }));
  };

  return (
              startAdornment: <InputAdornment position="start">$</InputAdornment>,
            }}
          />
        </Grid>
        
        {/* Income Frequency */}
        <Grid item xs={12} md={6}>
          <FormControl fullWidth error={!!formErrors.primaryIncomeFrequency}>
            <InputLabel>Income Frequency</InputLabel>
            <Select
              name="primaryIncomeFrequency"
              value={formValues.primaryIncomeFrequency}
              onChange={handleSelectChange}
              label="Income Frequency"
            >
              <MenuItem value="weekly">Weekly</MenuItem>
              <MenuItem value="biweekly">Bi-weekly</MenuItem>
              <MenuItem value="monthly">Monthly</MenuItem>
              <MenuItem value="annually">Annually</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        
        {/* Additional Income Sources */}
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            Additional Income Sources
          </Typography>
          {formValues.incomeSources.map((source) => (
            <Box key={source.id} sx={{ mb: 2, display: 'flex', gap: 2 }}>
              <TextField
                label="Source"
                value={source.source}
                onChange={(e) => handleUpdateIncomeSource(source.id, 'source', e.target.value)}
                fullWidth
              />
              <TextField
                label="Amount"
                value={source.amount}
                onChange={(e) => handleUpdateIncomeSource(source.id, 'amount', e.target.value)}
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                }}
              />
              <IconButton 
                onClick={() => handleRemoveIncomeSource(source.id)}
                color="error"
              >
                <Delete />
              </IconButton>
            </Box>
          ))}
          <Button
            variant="outlined"
            startIcon={<Add />}
            onClick={handleAddIncomeSource}
            sx={{ mt: 1 }}
          >
            Add Income Source
          </Button>
        </Grid>
        
        {/* Summary Section */}
        <Grid item xs={12}>
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Income Summary
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={4}>
                  <Typography variant="body1">
                    <strong>Total Annual Income:</strong> {formatCurrency(formValues.totalAnnualIncome)}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Typography variant="body1">
                    <strong>Total Taxes:</strong> {formatCurrency(formValues.totalTax)}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Typography variant="body1">
                    <strong>After-Tax Income:</strong> {formatCurrency(formValues.afterTaxIncome)}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="body2" color="textSecondary">
                    Effective Tax Rate: {formValues.effectiveTaxRate.toFixed(2)}%
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
        
        {/* Form Actions */}
        <Grid item xs={12}>
          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between' }}>
            {onBack && (
              <Button
                variant="outlined"
                onClick={onBack}
                startIcon={<ArrowBack />}
                disabled={isSubmitting}
              >
                Back
              </Button>
            )}
            <Button
              type="submit"
              variant="contained"
              color="primary"
              disabled={isSubmitting}
              startIcon={<Save />}
            >
              {isSubmitting ? 'Saving...' : 'Save & Continue'}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <FormContainer>
      <h2>Income Details</h2>
      <p>Please provide information about your primary income source and any additional income.</p>
      
      <form onSubmit={handleSubmit}>
        <h3>Primary Income</h3>
        <FormRow>
          <FormGroup>
            <Label htmlFor="primaryIncome">Amount ($)</Label>
            <Input
              type="number"
              id="primaryIncome"
              name="primaryIncome"
              value={formValues.primaryIncome}
              onChange={handleChange}
              onBlur={handleBlur}
              placeholder="0.00"
              min="0"
              step="0.01"
            />
            {errors.primaryIncome && <ErrorMessage>{errors.primaryIncome}</ErrorMessage>}
          </FormGroup>

          <FormGroup>
            <Label htmlFor="primaryIncomeFrequency">Frequency</Label>
            <Select
              id="primaryIncomeFrequency"
              name="primaryIncomeFrequency"
              value={formValues.primaryIncomeFrequency}
              onChange={handleChange}
              onBlur={handleBlur}
            >
              {INCOME_FREQUENCIES.map((freq) => (
                <option key={freq.value} value={freq.value}>
                  {freq.label}
                </option>
              ))}
            </Select>
          </FormGroup>

          <FormGroup>
            <Label htmlFor="primaryIncomeType">Income Type</Label>
            <Select
              id="primaryIncomeType"
              name="primaryIncomeType"
              value={formValues.primaryIncomeType}
              onChange={handleChange}
              onBlur={handleBlur}
            >
              {INCOME_TYPES.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </Select>
          </FormGroup>
        </FormRow>

        <h3>Tax Information</h3>
        <FormRow>
          <FormGroup>
            <Label htmlFor="filingStatus">Filing Status</Label>
            <Select
              id="filingStatus"
              name="filingStatus"
              value={formValues.filingStatus}
              onChange={handleChange}
              onBlur={handleBlur}
            >
              {FILING_STATUS_OPTIONS.map(status => (
                <option key={status.value} value={status.value}>
                  {status.label}
                </option>
              ))}
            </Select>
            {errors.filingStatus && <ErrorMessage>{errors.filingStatus}</ErrorMessage>}
          </FormGroup>

          <FormGroup>
            <Label htmlFor="state">State (for state tax)</Label>
            <Input
              type="text"
              id="state"
              name="state"
              value={formValues.state}
              onChange={handleChange}
              onBlur={handleBlur}
              placeholder="e.g., CA"
              maxLength={2}
              style={{ textTransform: 'uppercase' }}
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="dependents">Dependents</Label>
            <Input
              type="number"
              id="dependents"
              name="dependents"
              value={formValues.dependents}
              onChange={handleChange}
              onBlur={handleBlur}
              min="0"
              step="1"
            />
          </FormGroup>
        </FormRow>

        <h3>Additional Income & Deductions</h3>
        <FormRow>
          <FormGroup>
            <Label htmlFor="otherIncome">Other Income ($)</Label>
            <Input
              type="number"
              id="otherIncome"
              name="otherIncome"
              value={formValues.otherIncome}
              onChange={handleChange}
              onBlur={handleBlur}
              placeholder="0.00"
              min="0"
              step="0.01"
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="otherIncomeFrequency">Frequency</Label>
            <Select
              id="otherIncomeFrequency"
              name="otherIncomeFrequency"
              value={formValues.otherIncomeFrequency}
              onChange={handleChange}
              onBlur={handleBlur}
            >
              {INCOME_FREQUENCIES.map((freq) => (
                <option key={freq.value} value={freq.value}>
                  {freq.label}
                </option>
              ))}
            </Select>
          </FormGroup>

          <FormGroup>
            <Label htmlFor="preTaxDeductions">Pre-tax Deductions ($/year)</Label>
            <Input
              type="number"
              id="preTaxDeductions"
              name="preTaxDeductions"
              value={formValues.preTaxDeductions}
              onChange={handleChange}
              onBlur={handleBlur}
              placeholder="0.00"
              min="0"
              step="0.01"
            />
          </FormGroup>
        </FormRow>

        <FormRow>
          <FormGroup>
            <Label htmlFor="postTaxDeductions">Post-tax Deductions ($/year)</Label>
            <Input
              type="number"
              id="postTaxDeductions"
              name="postTaxDeductions"
              value={formValues.postTaxDeductions}
              onChange={handleChange}
              onBlur={handleBlur}
              placeholder="0.00"
              min="0"
              step="0.01"
            />
          </FormGroup>
        </FormRow>

        {/* Summary Section */}
        <h3>Income Summary</h3>
        <SummaryContainer>
          <SummaryRow>
            <span>Annual Income:</span>
            <span>{formatCurrency(formValues.annualIncome)}</span>
          </SummaryRow>
          <SummaryRow>
            <span>Additional Income:</span>
            <span>{formatCurrency(formValues.additionalIncome)}</span>
          </SummaryRow>
          <SummaryRow>
            <span>Pre-tax Deductions:</span>
            <span>-{formatCurrency(Number(formValues.preTaxDeductions) || 0)}</span>
          </SummaryRow>
          <SummaryDivider />
          <SummaryRow className="highlight">
            <span>Total Annual Income:</span>
            <span>{formatCurrency(formValues.totalAnnualIncome)}</span>
          </SummaryRow>
          
          <SummaryDivider />
          
          <SummaryRow>
            <span>Federal Tax:</span>
            <span>-{formatCurrency(formValues.federalTax)}</span>
          </SummaryRow>
          <SummaryRow>
            <span>State Tax:</span>
            <span>-{formatCurrency(formValues.stateTax)}</span>
          </SummaryRow>
          <SummaryRow>
            <span>FICA Tax:</span>
            <span>-{formatCurrency(formValues.ficaTax)}</span>
          </SummaryRow>
          <SummaryRow>
            <span>Post-tax Deductions:</span>
            <span>-{formatCurrency(Number(formValues.postTaxDeductions) || 0)}</span>
          </SummaryRow>
          <SummaryDivider />
          <SummaryRow className="highlight">
            <span>After-tax Income:</span>
            <span>{formatCurrency(formValues.afterTaxIncome)}</span>
          </SummaryRow>
          <SummaryRow>
            <span>Effective Tax Rate:</span>
            <span>{formatPercentage(formValues.effectiveTaxRate)}</span>
          </SummaryRow>
        </SummaryContainer>

        <ButtonGroup>
          {onBack && (
            <Button type="button" onClick={onBack} variant="secondary">
              Back
            </Button>
          )}
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? 'Saving...' : 'Save & Continue'}
          </Button>
        </ButtonGroup>
        {errors.submit && <ErrorMessage>{errors.submit}</ErrorMessage>}
      </form>
    </FormContainer>
  );
};

export default IncomeDetails;
