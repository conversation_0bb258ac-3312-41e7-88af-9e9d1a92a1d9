import React, { useState, useEffect, useCallback } from 'react';
import { useTheme } from '@mui/material/styles';
import {
  Box,
  Button,
  TextField,
  Typography,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Divider,
  IconButton,
  InputAdornment,
  Card,
  CardContent,
  Snackbar,
  Alert,
  useMediaQuery,
  SelectChangeEvent
} from '@mui/material';
import { Save, ArrowBack, Add, Delete } from '@mui/icons-material';

// Types
type FilingStatus = 'SINGLE' | 'MARRIED_FILING_JOINTLY' | 'MARRIED_FILING_SEPARATELY' | 'HEAD_OF_HOUSEHOLD';
type IncomeFrequency = 'yearly' | 'monthly' | 'bi-weekly' | 'weekly';

interface IncomeSource {
  id: string;
  source: string;
  amount: string;
  frequency: IncomeFrequency;
}

interface IncomeFormData {
  primaryIncome: string;
  filingStatus: FilingStatus;
  additionalIncome: IncomeSource[];
  state: string;
  otherIncome: string;
  otherIncomeDescription: string;
}

interface TaxCalculationResult {
  federal: number;
  state: number;
  fica: number;
  effectiveRate: number;
}

interface IncomeDetailsProps {
  onComplete?: () => void;
  onSave?: (data: IncomeFormData) => Promise<void>;
  onBack?: () => void;
  data?: Partial<IncomeFormData>;
  updateData?: (data: IncomeFormData) => void;
}

// Constants
const STATE_OPTIONS = [
  { value: 'CA', label: 'California' },
  { value: 'NY', label: 'New York' },
  { value: 'TX', label: 'Texas' },
  { value: 'FL', label: 'Florida' },
  { value: 'IL', label: 'Illinois' },
];

const FREQUENCY_OPTIONS = [
  { value: 'yearly' as const, label: 'Yearly' },
  { value: 'monthly' as const, label: 'Monthly' },
  { value: 'bi-weekly' as const, label: 'Bi-Weekly' },
  { value: 'weekly' as const, label: 'Weekly' },
];

// Utility Functions
const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);
};

const parseCurrencyInput = (value: string): number => {
  return parseFloat(value.replace(/[^0-9.-]+/g, '')) || 0;
};

// Tax Calculation Utilities
const calculateFederalTax = (income: number, status: FilingStatus = 'SINGLE'): number => {
  const brackets = {
    SINGLE: [
      { min: 0, max: 10275, rate: 0.10 },
      { min: 10276, max: 41775, rate: 0.12 },
      { min: 41776, max: 89075, rate: 0.22 },
      { min: 89076, max: 170050, rate: 0.24 },
      { min: 170051, max: 215950, rate: 0.32 },
      { min: 215951, max: 539900, rate: 0.35 },
      { min: 539901, max: Infinity, rate: 0.37 }
    ],
    MARRIED_FILING_JOINTLY: [
      { min: 0, max: 20550, rate: 0.10 },
      { min: 20551, max: 83550, rate: 0.12 },
      { min: 83551, max: 178150, rate: 0.22 },
      { min: 178151, max: 340100, rate: 0.24 },
      { min: 340101, max: 431900, rate: 0.32 },
      { min: 431901, max: 647850, rate: 0.35 },
      { min: 647851, max: Infinity, rate: 0.37 }
    ],
    MARRIED_FILING_SEPARATELY: [
      { min: 0, max: 10275, rate: 0.10 },
      { min: 10276, max: 41775, rate: 0.12 },
      { min: 41776, max: 89075, rate: 0.22 },
      { min: 89076, max: 170050, rate: 0.24 },
      { min: 170051, max: 215950, rate: 0.32 },
      { min: 215951, max: 323925, rate: 0.35 },
      { min: 323926, max: Infinity, rate: 0.37 }
    ],
    HEAD_OF_HOUSEHOLD: [
      { min: 0, max: 14650, rate: 0.10 },
      { min: 14651, max: 55900, rate: 0.12 },
      { min: 55901, max: 89050, rate: 0.22 },
      { min: 89051, max: 170050, rate: 0.24 },
      { min: 170051, max: 215950, rate: 0.32 },
      { min: 215951, max: 539900, rate: 0.35 },
      { min: 539901, max: Infinity, rate: 0.37 }
    ]
  };

  const bracket = brackets[status] || brackets.SINGLE;
  let tax = 0;
  let remainingIncome = income;

  for (const { min, max, rate } of bracket) {
    if (remainingIncome <= 0) break;
    const taxableInBracket = Math.min(remainingIncome, max === Infinity ? remainingIncome : max - min + 1);
    tax += taxableInBracket * rate;
    remainingIncome -= taxableInBracket;
  }

  return Math.round(tax * 100) / 100;
};

const calculateStateTax = (income: number, state: string = ''): number => {
  const stateTaxRates: Record<string, number> = {
    'CA': 0.093,
  };
  const rate = stateTaxRates[state] || 0.05;
  return Math.round(income * rate * 100) / 100;
};

const calculateFICATax = (income: number): number => {
  const socialSecurityTax = Math.min(income, 147000) * 0.062;
  let medicareTax = income * 0.0145;
  if (income > 200000) {
    medicareTax += (income - 200000) * 0.009;
  }
  return Math.round((socialSecurityTax + medicareTax) * 100) / 100;
};

const calculateTaxLiability = (data: IncomeFormData): TaxCalculationResult => {
  const grossIncome = parseCurrencyInput(data.primaryIncome);
  const federalTax = calculateFederalTax(grossIncome, data.filingStatus);
  const stateTax = calculateStateTax(grossIncome, data.state);
  const ficaTax = calculateFICATax(grossIncome);
  const totalTax = federalTax + stateTax + ficaTax;
  
  return {
    federal: federalTax,
    state: stateTax,
    fica: ficaTax,
    effectiveRate: grossIncome > 0 ? (totalTax / grossIncome) * 100 : 0
  };
};

const calculateNetIncome = (data: IncomeFormData): number => {
  const grossIncome = parseCurrencyInput(data.primaryIncome);
  const taxes = calculateTaxLiability(data);
  return grossIncome - (taxes.federal + taxes.state + taxes.fica);
};

const validateIncomeForm = (data: IncomeFormData): Record<string, string> => {
  const errors: Record<string, string> = {};
  const income = parseCurrencyInput(data.primaryIncome);
  
  if (isNaN(income) || income <= 0) {
    errors.primaryIncome = 'Please enter a valid income amount';
  }
  
  if (!data.filingStatus) {
    errors.filingStatus = 'Please select a filing status';
  }
  
  return errors;
};

// Component
const IncomeDetails: React.FC<IncomeDetailsProps> = ({
  onComplete = () => {},
  onSave = async () => {},
  onBack,
  data = {},
  updateData = () => {}
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  // Form state
  const [formValues, setFormValues] = useState<IncomeFormData>({
    primaryIncome: data.primaryIncome || '',
    filingStatus: data.filingStatus || 'SINGLE',
    additionalIncome: data.additionalIncome || [],
    state: data.state || '',
    otherIncome: data.otherIncome || '',
    otherIncomeDescription: data.otherIncomeDescription || ''
  });
  
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error'
  });

  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormValues(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSelectChange = (e: SelectChangeEvent<FilingStatus>) => {
    const { name, value } = e.target;
    setFormValues(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle currency input formatting
  const handleCurrencyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const numericValue = parseCurrencyInput(value);
    setFormValues(prev => ({
      ...prev,
      [name]: isNaN(numericValue) ? '' : formatCurrency(numericValue)
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const errors = validateIncomeForm(formValues);
    setFormErrors(errors);
    
    if (Object.keys(errors).length === 0) {
      setIsSubmitting(true);
      try {
        await onSave(formValues);
        updateData(formValues);
        setSnackbar({
          open: true,
          message: 'Income details saved successfully!',
          severity: 'success'
        });
        onComplete();
      } catch (error) {
        setSnackbar({
          open: true,
          message: 'Failed to save income details. Please try again.',
          severity: 'error'
        });
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  // Handle snackbar close
  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({
      ...prev,
      open: false
    }));
  };

  return (
    <Box component="form" onSubmit={handleSubmit} noValidate sx={{ mt: 2 }}>
      <Grid container spacing={3}>
        {/* Primary Income */}
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Primary Income"
            name="primaryIncome"
            value={formValues.primaryIncome}
            onChange={handleCurrencyChange}
            error={!!formErrors.primaryIncome}
            helperText={formErrors.primaryIncome}
            InputProps={{
              startAdornment: <InputAdornment position="start">$</InputAdornment>,
            }}
          />
        </Grid>

        {/* Filing Status */}
        <Grid item xs={12} md={6}>
          <FormControl fullWidth error={!!formErrors.filingStatus}>
            <InputLabel>Filing Status</InputLabel>
            <Select
              name="filingStatus"
              value={formValues.filingStatus}
              onChange={handleSelectChange}
              label="Filing Status"
            >
              <MenuItem value="SINGLE">Single</MenuItem>
              <MenuItem value="MARRIED_FILING_JOINTLY">Married Filing Jointly</MenuItem>
              <MenuItem value="MARRIED_FILING_SEPARATELY">Married Filing Separately</MenuItem>
              <MenuItem value="HEAD_OF_HOUSEHOLD">Head of Household</MenuItem>
            </Select>
            {formErrors.filingStatus && (
              <FormHelperText>{formErrors.filingStatus}</FormHelperText>
            )}
          </FormControl>
        </Grid>

        {/* State */}
        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <InputLabel>State</InputLabel>
            <Select
              name="state"
              value={formValues.state}
              onChange={handleSelectChange}
              label="State"
            >
              {STATE_OPTIONS.map((state) => (
                <MenuItem key={state.value} value={state.value}>
                  {state.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Additional Income Sources */}
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            Additional Income Sources
          </Typography>
          <Divider sx={{ mb: 2 }} />
          
          {formValues.additionalIncome.map((source, index) => (
            <Grid container spacing={2} key={source.id} sx={{ mb: 2 }}>
              <Grid item xs={12} sm={5}>
                <TextField
                  fullWidth
                  label="Income Source"
                  value={source.source}
                  onChange={(e) => {
                    const newSources = [...formValues.additionalIncome];
                    newSources[index].source = e.target.value;
                    setFormValues(prev => ({
                      ...prev,
                      additionalIncome: newSources
                    }));
                  }}
                />
              </Grid>
              <Grid item xs={8} sm={4}>
                <TextField
                  fullWidth
                  label="Amount"
                  value={source.amount}
                  onChange={(e) => {
                    const newSources = [...formValues.additionalIncome];
                    const numericValue = parseCurrencyInput(e.target.value);
                    newSources[index].amount = isNaN(numericValue) ? '' : formatCurrency(numericValue);
                    setFormValues(prev => ({
                      ...prev,
                      additionalIncome: newSources
                    }));
                  }}
                  InputProps={{
                    startAdornment: <InputAdornment position="start">$</InputAdornment>,
                  }}
                />
              </Grid>
              <Grid item xs={3} sm={2}>
                <FormControl fullWidth>
                  <Select
                    value={source.frequency}
                    onChange={(e) => {
                      const newSources = [...formValues.additionalIncome];
                      newSources[index].frequency = e.target.value as IncomeFrequency;
                      setFormValues(prev => ({
                        ...prev,
                        additionalIncome: newSources
                      }));
                    }}
                  >
                    {FREQUENCY_OPTIONS.map((freq) => (
                      <MenuItem key={freq.value} value={freq.value}>
                        {freq.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={1} sx={{ display: 'flex', alignItems: 'center' }}>
                <IconButton
                  onClick={() => {
                    const newSources = formValues.additionalIncome.filter((_, i) => i !== index);
                    setFormValues(prev => ({
                      ...prev,
                      additionalIncome: newSources
                    }));
                  }}
                >
                  <Delete />
                </IconButton>
              </Grid>
            </Grid>
          ))}

          <Button
            variant="outlined"
            startIcon={<Add />}
            onClick={() => {
              setFormValues(prev => ({
                ...prev,
                additionalIncome: [
                  ...prev.additionalIncome,
                  {
                    id: Date.now().toString(),
                    source: '',
                    amount: '',
                    frequency: 'yearly'
                  }
                ]
              }));
            }}
            sx={{ mt: 1 }}
          >
            Add Income Source
          </Button>
        </Grid>

        {/* Other Income */}
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            Other Income
          </Typography>
          <Divider sx={{ mb: 2 }} />
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Other Income Amount"
                name="otherIncome"
                value={formValues.otherIncome}
                onChange={handleCurrencyChange}
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description (e.g., Alimony, Child Support, etc.)"
                name="otherIncomeDescription"
                value={formValues.otherIncomeDescription}
                onChange={handleChange}
                multiline
                rows={2}
              />
            </Grid>
          </Grid>
        </Grid>

        {/* Summary Card */}
        <Grid item xs={12}>
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Income Summary
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography>Annual Gross Income: {formatCurrency(parseCurrencyInput(formValues.primaryIncome))}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Filing Status: {formValues.filingStatus.replace(/_/g, ' ')}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    State: {STATE_OPTIONS.find(s => s.value === formValues.state)?.label || 'N/A'}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography>Tax Liability: {formatCurrency(calculateTaxLiability(formValues).federal)}</Typography>
                  <Typography>State Tax: {formatCurrency(calculateTaxLiability(formValues).state)}</Typography>
                  <Typography>FICA: {formatCurrency(calculateTaxLiability(formValues).fica)}</Typography>
                  <Typography variant="subtitle1" sx={{ mt: 1, fontWeight: 'bold' }}>
                    Net Annual Income: {formatCurrency(calculateNetIncome(formValues))}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Form Actions */}
        <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
          <Button
            variant="outlined"
            onClick={onBack}
            startIcon={<ArrowBack />}
            disabled={isSubmitting}
          >
            Back
          </Button>
          <Button
            type="submit"
            variant="contained"
            color="primary"
            disabled={isSubmitting}
            startIcon={<Save />}
          >
            {isSubmitting ? 'Saving...' : 'Save & Continue'}
          </Button>
        </Grid>
      </Grid>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default IncomeDetails;
