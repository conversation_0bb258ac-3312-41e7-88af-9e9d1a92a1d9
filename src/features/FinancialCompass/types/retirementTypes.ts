import { RetirementPlan } from '../../../utils/retirementPlanningUtils';

export interface RetirementFormData {
  currentAge: number;
  retirementAge: number;
  lifeExpectancy: number;
  currentIncome: number;
  currentSavings: number;
  monthlyContribution: number;
  expectedReturnRate: number;
  desiredAnnualIncome: number;
  inflationRate: number;
  socialSecurityBenefit: number;
  pensionIncome: number;
  otherIncome: number;
  retirementLocation: string;
  riskTolerance: 'conservative' | 'moderate' | 'aggressive';
  investmentStrategy: 'preservation' | 'balanced' | 'growth';
  retirementLifestyle: 'minimal' | 'modest' | 'comfortable' | 'luxurious';
  retirementActivities: string[];
  priorityGoals: string[];
}

export interface RetirementGoalsProps {
  onComplete?: () => void;
  onBack?: () => void;
  initialData?: Partial<RetirementFormData>;
}

export interface RetirementFormState {
  data: RetirementFormData;
  errors: Record<keyof RetirementFormData, string>;
  isSubmitting: boolean;
  retirementPlan: RetirementPlan | null;
}
