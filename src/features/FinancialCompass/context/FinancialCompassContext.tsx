/**
 * Financial Compass Context
 *
 * This context provides state and functions for the Financial Compass feature.
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import autoSaveService from '../../DataPortability/services/autoSaveService';

/**
 * Direction type
 */
export type Direction = 'north' | 'east' | 'south' | 'west';

/**
 * Sub-feature type
 */
export interface SubFeature {
  id: string;
  name: string;
  direction: Direction;
  completed: boolean;
  path: string;
}

/**
 * Import the FinancialCompassData interface from types
 */
import { FinancialCompassData } from '../../../types/compass';

/**
 * Section type for direction trackers
 */
interface DirectionSection {
  id: string;
  title: string;
  description?: string;
  icon?: string;
  isCompleted: boolean;
  isActive: boolean;
  completionDate?: string;
}

/**
 * Financial Compass context type
 */
interface FinancialCompassContextType {
  activeDirection: Direction;
  setActiveDirection: (direction: Direction) => void;
  activeSubFeature: string | null;
  setActiveSubFeature: (subFeatureId: string | null) => void;
  subFeatures: SubFeature[];
  data: FinancialCompassData;
  updateData: (direction: Direction, key: string, value: unknown) => void;
  getCompletionPercentage: () => number;
  getDirectionCompletionPercentage: (direction: Direction) => number;
  resetData: () => void;

  // Common functions
  updateActiveDirection: (direction: Direction) => void;

  // North Direction progress tracking
  northProgress: number;
  updateNorthProgress: (progress: number) => void;
  updateNorthSectionStatus: (sectionId: string, isCompleted: boolean, isActive?: boolean) => void;
  northSections: DirectionSection[];

  // East Direction progress tracking
  eastProgress: number;
  updateEastProgress: (progress: number) => void;
  updateEastSectionStatus: (sectionId: string, isCompleted: boolean, isActive?: boolean) => void;
  eastSections: DirectionSection[];

  // South Direction progress tracking
  southProgress: number;
  updateSouthProgress: (progress: number) => void;
  updateSouthSectionStatus: (sectionId: string, isCompleted: boolean, isActive?: boolean) => void;
  southSections: DirectionSection[];

  // West Direction progress tracking
  westProgress: number;
  updateWestProgress: (progress: number) => void;
  updateWestSectionStatus: (sectionId: string, isCompleted: boolean, isActive?: boolean) => void;
  westSections: DirectionSection[];
}

/**
 * Default sub-features
 */
const defaultSubFeatures: SubFeature[] = [
  // North Direction
  {
    id: 'personal_information',
    name: 'Personal Information',
    direction: 'north',
    completed: false,
    path: '/compass/north/personal-information',
  },
  {
    id: 'family_information',
    name: 'Family Information',
    direction: 'north',
    completed: false,
    path: '/compass/north/family-information',
  },
  {
    id: 'income_details',
    name: 'Income Details',
    direction: 'north',
    completed: false,
    path: '/compass/north/income-details',
  },
  {
    id: 'expense_details',
    name: 'Expense Details',
    direction: 'north',
    completed: false,
    path: '/compass/north/expense-details',
  },
  {
    id: 'cash_flow_analysis',
    name: 'Cash Flow Analysis',
    direction: 'north',
    completed: false,
    path: '/compass/north/cash-flow-analysis',
  },
  {
    id: 'assets_investments',
    name: 'Assets & Investments',
    direction: 'north',
    completed: false,
    path: '/compass/north/assets-investments',
  },
  {
    id: 'liabilities_debt',
    name: 'Liabilities & Debt',
    direction: 'north',
    completed: false,
    path: '/compass/north/liabilities-debt',
  },

  // East Direction
  {
    id: 'retirement_goals',
    name: 'Retirement Goals',
    direction: 'east',
    completed: false,
    path: '/compass/east/retirement-goals',
  },
  {
    id: 'social_security',
    name: 'Social Security Planning',
    direction: 'east',
    completed: false,
    path: '/compass/east/social-security',
  },
  {
    id: 'investment_preferences',
    name: 'Investment Preferences',
    direction: 'east',
    completed: false,
    path: '/compass/east/investment-preferences',
  },
  {
    id: 'risk_assessment',
    name: 'Risk Assessment',
    direction: 'east',
    completed: false,
    path: '/compass/east/risk-assessment',
  },

  // South Direction
  {
    id: 'insurance_coverage',
    name: 'Insurance Coverage',
    direction: 'south',
    completed: false,
    path: '/compass/south/insurance-coverage',
  },
  {
    id: 'healthcare_planning',
    name: 'Healthcare Planning',
    direction: 'south',
    completed: false,
    path: '/compass/south/healthcare-planning',
  },
  {
    id: 'risk_tolerance',
    name: 'Risk Tolerance',
    direction: 'south',
    completed: false,
    path: '/compass/south/risk-tolerance',
  },

  // West Direction
  {
    id: 'tax_planning',
    name: 'Tax Planning',
    direction: 'west',
    completed: false,
    path: '/compass/west/tax-planning',
  },
  {
    id: 'estate_planning',
    name: 'Estate Planning',
    direction: 'west',
    completed: false,
    path: '/compass/west/estate-planning',
  },
];

/**
 * Default financial compass data
 */
const defaultData: FinancialCompassData = {
  north: {
    personalInformation: {
      firstName: '',
      lastName: '',
      dateOfBirth: '',
      email: '',
      phone: '',
      address: {
        street: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'United States',
      },
      occupation: '',
      employmentStatus: 'employed',
    },
    familyInformation: {
      maritalStatus: 'single',
      familyMembers: [],
    },
    incomeDetails: {
      primaryIncome: '',
      primaryIncomeType: '',
      primaryIncomeFrequency: '',
      taxRate: '',
      incomeSources: [],
    },
    expenseDetails: {
      totalMonthlyExpenses: '',
      expenseCategories: {},
    },
    assets: {},
    liabilities: {},
    netWorthDetails: {
      totalAssets: '',
      totalLiabilities: '',
    },
    riskAssessment: {
      answers: {},
      riskScore: 0,
      riskProfile: 'moderate',
    },
    cashFlowAnalysis: {},
  },
  east: {},
  south: {},
  west: {},
};

/**
 * Storage key for financial compass data
 */
const STORAGE_KEY = 'lifecompass_financial_compass';

/**
 * Financial Compass context
 */
const FinancialCompassContext = createContext<FinancialCompassContextType | undefined>(undefined);

/**
 * Financial Compass Provider props
 */
interface FinancialCompassProviderProps {
  children: ReactNode;
}

/**
 * Default direction sections
 */
const defaultNorthSections: DirectionSection[] = [
  { id: 'personal_information', title: 'Personal Information', isCompleted: false, isActive: true },
  { id: 'family_information', title: 'Family Information', isCompleted: false, isActive: false },
  { id: 'income_details', title: 'Income Details', isCompleted: false, isActive: false },
  { id: 'expense_details', title: 'Expense Details', isCompleted: false, isActive: false },
  { id: 'cash_flow_analysis', title: 'Cash Flow Analysis', isCompleted: false, isActive: false },
  { id: 'assets_investments', title: 'Assets & Investments', isCompleted: false, isActive: false },
  { id: 'liabilities_debt', title: 'Liabilities & Debt', isCompleted: false, isActive: false },
];

const defaultEastSections: DirectionSection[] = [
  { id: 'retirement_goals', title: 'Retirement Goals', isCompleted: false, isActive: true },
  { id: 'retirement_income', title: 'Retirement Income', isCompleted: false, isActive: false },
  { id: 'retirement_expenses', title: 'Retirement Expenses', isCompleted: false, isActive: false },
  { id: 'retirement_timeline', title: 'Retirement Timeline', isCompleted: false, isActive: false },
];

const defaultSouthSections: DirectionSection[] = [
  { id: 'insurance_coverage', title: 'Insurance Coverage', isCompleted: false, isActive: true },
  {
    id: 'insurance_calculator',
    title: 'Insurance Calculator',
    isCompleted: false,
    isActive: false,
  },
  { id: 'healthcare_planning', title: 'Healthcare Planning', isCompleted: false, isActive: false },
  {
    id: 'healthcare_projections',
    title: 'Healthcare Projections',
    isCompleted: false,
    isActive: false,
  },
  { id: 'risk_tolerance', title: 'Risk Tolerance', isCompleted: false, isActive: false },
  { id: 'protection_gap', title: 'Protection Gap', isCompleted: false, isActive: false },
];

const defaultWestSections: DirectionSection[] = [
  { id: 'tax_planning', title: 'Tax Planning', isCompleted: false, isActive: true },
  { id: 'estate_planning', title: 'Estate Planning', isCompleted: false, isActive: false },
  { id: 'estate_documents', title: 'Estate Documents', isCompleted: false, isActive: false },
  { id: 'charitable_giving', title: 'Charitable Giving', isCompleted: false, isActive: false },
  { id: 'legacy_planning', title: 'Legacy Planning', isCompleted: false, isActive: false },
  { id: 'legacy_messages', title: 'Legacy Messages', isCompleted: false, isActive: false },
  { id: 'values_goals', title: 'Values & Goals', isCompleted: false, isActive: false },
];

/**
 * Financial Compass Provider
 */
export const FinancialCompassProvider: React.FC<FinancialCompassProviderProps> = ({ children }) => {
  // State
  const [activeDirection, setActiveDirection] = useState<Direction>('north');
  const [activeSubFeature, setActiveSubFeature] = useState<string | null>(null);
  const [subFeatures, setSubFeatures] = useState<SubFeature[]>(defaultSubFeatures);
  const [data, setData] = useState<FinancialCompassData>(() => {
    // Load data from localStorage if available
    const storedData = localStorage.getItem(STORAGE_KEY);
    return storedData ? JSON.parse(storedData) : defaultData;
  });

  // Direction sections state
  const [northSections, setNorthSections] = useState<DirectionSection[]>(defaultNorthSections);
  const [eastSections, setEastSections] = useState<DirectionSection[]>(defaultEastSections);
  const [southSections, setSouthSections] = useState<DirectionSection[]>(defaultSouthSections);
  const [westSections, setWestSections] = useState<DirectionSection[]>(defaultWestSections);

  // Direction progress state
  const [northProgress, setNorthProgress] = useState<number>(0);
  const [eastProgress, setEastProgress] = useState<number>(0);
  const [southProgress, setSouthProgress] = useState<number>(0);
  const [westProgress, setWestProgress] = useState<number>(0);

  // Rehydrate subFeatures from localStorage if present (e.g., after import)
  useEffect(() => {
    // Try to load subFeatures completion from localStorage (import process may have set this)
    const subFeaturesStr = localStorage.getItem('lifecompass_subfeatures');
    if (subFeaturesStr) {
      try {
        const importedSubFeatures = JSON.parse(subFeaturesStr);
        if (Array.isArray(importedSubFeatures) && importedSubFeatures.length > 0) {
          setSubFeatures(importedSubFeatures);
        }
      } catch (e) {
        // Ignore parse errors, fallback to default
      }
    }

    // Try to load direction section completion from localStorage (import process may have set these)
    const northSectionsStr = localStorage.getItem('lifecompass_north_sections');
    if (northSectionsStr) {
      try {
        const importedNorthSections = JSON.parse(northSectionsStr);
        if (Array.isArray(importedNorthSections) && importedNorthSections.length > 0) {
          setNorthSections(importedNorthSections);
        }
      } catch (e) {}
    }
    const eastSectionsStr = localStorage.getItem('lifecompass_east_sections');
    if (eastSectionsStr) {
      try {
        const importedEastSections = JSON.parse(eastSectionsStr);
        if (Array.isArray(importedEastSections) && importedEastSections.length > 0) {
          setEastSections(importedEastSections);
        }
      } catch (e) {}
    }
    const southSectionsStr = localStorage.getItem('lifecompass_south_sections');
    if (southSectionsStr) {
      try {
        const importedSouthSections = JSON.parse(southSectionsStr);
        if (Array.isArray(importedSouthSections) && importedSouthSections.length > 0) {
          setSouthSections(importedSouthSections);
        }
      } catch (e) {}
    }
    const westSectionsStr = localStorage.getItem('lifecompass_west_sections');
    if (westSectionsStr) {
      try {
        const importedWestSections = JSON.parse(westSectionsStr);
        if (Array.isArray(importedWestSections) && importedWestSections.length > 0) {
          setWestSections(importedWestSections);
        }
      } catch (e) {}
    }

    // Recalculate progress for each direction after hydration
    // Use a timeout to ensure state is updated before calculation
    setTimeout(() => {
      setNorthProgress(
        (() => {
          const sections = northSectionsStr ? JSON.parse(northSectionsStr) : defaultNorthSections;
          const completed = sections.filter((s: any) => s.isCompleted).length;
          return Math.round((completed / sections.length) * 100);
        })()
      );
      setEastProgress(
        (() => {
          const sections = eastSectionsStr ? JSON.parse(eastSectionsStr) : defaultEastSections;
          const completed = sections.filter((s: any) => s.isCompleted).length;
          return Math.round((completed / sections.length) * 100);
        })()
      );
      setSouthProgress(
        (() => {
          const sections = southSectionsStr ? JSON.parse(southSectionsStr) : defaultSouthSections;
          const completed = sections.filter((s: any) => s.isCompleted).length;
          return Math.round((completed / sections.length) * 100);
        })()
      );
      setWestProgress(
        (() => {
          const sections = westSectionsStr ? JSON.parse(westSectionsStr) : defaultWestSections;
          const completed = sections.filter((s: any) => s.isCompleted).length;
          return Math.round((completed / sections.length) * 100);
        })()
      );
    }, 0);
  }, []);

  // Set up auto-save
  useEffect(() => {
    const autoSave = autoSaveService.createAutoSave(
      () => ({
        data,
        subFeatures,
      }),
      {
        storageKey: STORAGE_KEY,
        interval: 5000, // 5 seconds
        onAfterSave: () => console.log('Financial Compass data auto-saved'),
        onError: (error) => console.error('Error auto-saving Financial Compass data:', error),
      }
    );

    // Start auto-save
    autoSave.start();

    // Clean up on unmount
    return () => {
      autoSave.stop();
    };
  }, [data, subFeatures]);

  /**
   * Update data for a specific direction
   */
  const updateData = (direction: Direction, key: string, value: unknown) => {
    setData((prevData) => ({
      ...prevData,
      [direction]: {
        ...prevData[direction],
        [key]: value,
      },
    }));

    // Update sub-feature completion status if needed
    // This is a simplified example - in a real implementation, you would have more complex logic
    if (key === 'completed' && typeof value === 'boolean') {
      setSubFeatures((prevSubFeatures) =>
        prevSubFeatures.map((subFeature) =>
          subFeature.id === activeSubFeature ? { ...subFeature, completed: value } : subFeature
        )
      );
    }
  };

  /**
   * Get overall completion percentage
   */
  const getCompletionPercentage = (): number => {
    const completedCount = subFeatures.filter((subFeature) => subFeature.completed).length;
    return Math.round((completedCount / subFeatures.length) * 100);
  };

  /**
   * Get completion percentage for a specific direction
   */
  const getDirectionCompletionPercentage = (direction: Direction): number => {
    const directionSubFeatures = subFeatures.filter(
      (subFeature) => subFeature.direction === direction
    );
    const completedCount = directionSubFeatures.filter((subFeature) => subFeature.completed).length;
    return Math.round((completedCount / directionSubFeatures.length) * 100);
  };

  /**
   * Reset all data
   */
  const resetData = () => {
    setData(defaultData);
    setSubFeatures(defaultSubFeatures);
    localStorage.removeItem(STORAGE_KEY);
  };

  /**
   * Update North section status
   */
  const updateNorthSectionStatus = (
    sectionId: string,
    isCompleted: boolean,
    isActive: boolean = false
  ) => {
    setNorthSections((prevSections) =>
      prevSections.map((section) =>
        section.id === sectionId ? { ...section, isCompleted, isActive } : section
      )
    );
  };

  /**
   * Update East section status
   */
  const updateEastSectionStatus = (
    sectionId: string,
    isCompleted: boolean,
    isActive: boolean = false
  ) => {
    setEastSections((prevSections) =>
      prevSections.map((section) =>
        section.id === sectionId ? { ...section, isCompleted, isActive } : section
      )
    );
  };

  /**
   * Update South section status
   */
  const updateSouthSectionStatus = (
    sectionId: string,
    isCompleted: boolean,
    isActive: boolean = false
  ) => {
    setSouthSections((prevSections) =>
      prevSections.map((section) =>
        section.id === sectionId ? { ...section, isCompleted, isActive } : section
      )
    );
  };

  /**
   * Update West section status
   */
  const updateWestSectionStatus = (
    sectionId: string,
    isCompleted: boolean,
    isActive: boolean = false
  ) => {
    setWestSections((prevSections) =>
      prevSections.map((section) =>
        section.id === sectionId ? { ...section, isCompleted, isActive } : section
      )
    );
  };

  // Context value
  const value: FinancialCompassContextType = {
    activeDirection,
    setActiveDirection: (direction: Direction) => {
      setActiveDirection(direction);
      // You could add additional logic here if needed
    },
    activeSubFeature,
    setActiveSubFeature,
    subFeatures,
    data,
    updateData,
    getCompletionPercentage,
    getDirectionCompletionPercentage,
    resetData,

    // North direction
    northProgress,
    updateNorthProgress: setNorthProgress,
    updateNorthSectionStatus,
    northSections,

    // East direction
    eastProgress,
    updateEastProgress: setEastProgress,
    updateEastSectionStatus,
    eastSections,

    // South direction
    southProgress,
    updateSouthProgress: setSouthProgress,
    updateSouthSectionStatus,
    southSections,

    // West direction
    westProgress,
    updateWestProgress: setWestProgress,
    updateWestSectionStatus,
    westSections,

    // Common functions
    updateActiveDirection: setActiveDirection,
  };

  return (
    <FinancialCompassContext.Provider value={value}>{children}</FinancialCompassContext.Provider>
  );
};

/**
 * Hook to use the Financial Compass context
 */
export const useFinancialCompass = (): FinancialCompassContextType => {
  const context = useContext(FinancialCompassContext);

  if (context === undefined) {
    throw new Error('useFinancialCompass must be used within a FinancialCompassProvider');
  }

  return context;
};
