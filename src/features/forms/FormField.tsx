import React, { useId } from 'react';
import styled from 'styled-components';
import { useFormContext } from './FormContext';

const FieldContainer = styled.div`
  margin-bottom: 1rem;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
`;

const Input = styled.input<{ hasError: boolean }>`
  width: 100%;
  padding: 0.5rem;
  border: 1px solid ${({ hasError }) => (hasError ? '#dc3545' : '#ced4da')};
  border-radius: 4px;
  font-size: 1rem;
  transition:
    border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;

  &:focus {
    border-color: ${({ hasError }) => (hasError ? '#dc3545' : '#80bdff')};
    outline: 0;
    box-shadow: 0 0 0 0.2rem
      ${({ hasError }) => (hasError ? 'rgba(220, 53, 69, 0.25)' : 'rgba(0, 123, 255, 0.25)')};
  }
`;

const Select = styled.select<{ hasError: boolean }>`
  width: 100%;
  padding: 0.5rem;
  border: 1px solid ${({ hasError }) => (hasError ? '#dc3545' : '#ced4da')};
  border-radius: 4px;
  font-size: 1rem;
  background-color: white;

  &:focus {
    border-color: ${({ hasError }) => (hasError ? '#dc3545' : '#80bdff')};
    outline: 0;
    box-shadow: 0 0 0 0.2rem
      ${({ hasError }) => (hasError ? 'rgba(220, 53, 69, 0.25)' : 'rgba(0, 123, 255, 0.25)')};
  }
`;

const TextArea = styled.textarea<{ hasError: boolean }>`
  width: 100%;
  padding: 0.5rem;
  border: 1px solid ${({ hasError }) => (hasError ? '#dc3545' : '#ced4da')};
  border-radius: 4px;
  font-size: 1rem;
  min-height: 100px;
  resize: vertical;

  &:focus {
    border-color: ${({ hasError }) => (hasError ? '#dc3545' : '#80bdff')};
    outline: 0;
    box-shadow: 0 0 0 0.2rem
      ${({ hasError }) => (hasError ? 'rgba(220, 53, 69, 0.25)' : 'rgba(0, 123, 255, 0.25)')};
  }
`;

const ErrorMessage = styled.div`
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.25rem;
`;

type BaseFieldProps = {
  name: string;
  label?: string;
  required?: boolean;
  className?: string;
  helperText?: string;
};

type InputFieldProps = BaseFieldProps &
  Omit<React.InputHTMLAttributes<HTMLInputElement>, 'name'> & {
    as?: 'input';
  };

type SelectFieldProps = BaseFieldProps &
  Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'name'> & {
    as: 'select';
    options: { value: string; label: string }[];
  };

type TextAreaFieldProps = BaseFieldProps &
  Omit<React.TextareaHTMLAttributes<HTMLTextAreaElement>, 'name'> & {
    as: 'textarea';
  };

type FieldProps = InputFieldProps | SelectFieldProps | TextAreaFieldProps;

export const FormField: React.FC<FieldProps> = (props) => {
  const { name, label, required, className, helperText, ...rest } = props;
  const { values, errors, setFieldValue } = useFormContext();
  const id = useId();
  const hasError = !!errors[name];
  const value = values[name] ?? '';

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target;

    // Handle number inputs
    if (type === 'number' || type === 'range') {
      const numValue = (e.target as HTMLInputElement).valueAsNumber;
      setFieldValue(name, isNaN(numValue) ? undefined : numValue);
    }
    // Handle checkboxes
    else if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFieldValue(name, checked);
    }
    // Handle regular inputs
    else {
      setFieldValue(name, value === '' ? undefined : value);
    }
  };

  const renderInput = () => {
    if ('as' in rest && rest.as === 'select') {
      const { as, options, ...selectProps } = rest as SelectFieldProps;
      return (
        <Select
          id={id}
          name={name}
          value={value as string}
          onChange={handleChange}
          hasError={hasError}
          {...selectProps}
        >
          <option value="">Select an option</option>
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </Select>
      );
    }

    if ('as' in rest && rest.as === 'textarea') {
      const { as, ...textAreaProps } = rest as TextAreaFieldProps;
      return (
        <TextArea
          id={id}
          name={name}
          value={value as string}
          onChange={handleChange}
          hasError={hasError}
          {...textAreaProps}
        />
      );
    }

    const { as = 'input', ...inputProps } = rest as InputFieldProps;

    // Handle checkboxes and radio buttons
    if (inputProps.type === 'checkbox' || inputProps.type === 'radio') {
      return (
        <Input
          id={id}
          name={name}
          checked={!!value}
          onChange={handleChange}
          hasError={hasError}
          {...inputProps}
        />
      );
    }

    // Handle regular inputs
    return (
      <Input
        id={id}
        name={name}
        value={value as string | number | readonly string[] | undefined}
        onChange={handleChange}
        hasError={hasError}
        {...inputProps}
      />
    );
  };

  return (
    <FieldContainer className={className}>
      {label && (
        <Label htmlFor={id}>
          {label}
          {required && <span style={{ color: '#dc3545' }}> *</span>}
        </Label>
      )}
      {renderInput()}
      {hasError && <ErrorMessage>{errors[name]}</ErrorMessage>}
      {helperText && !hasError && (
        <div style={{ fontSize: '0.75rem', color: '#6c757d' }}>{helperText}</div>
      )}
    </FieldContainer>
  );
};

type FormSubmitButtonProps = {
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
};

export const FormSubmitButton: React.FC<FormSubmitButtonProps> = ({
  children,
  className,
  disabled = false,
  ...props
}) => {
  const { isSubmitting } = useFormContext();

  return (
    <button type="submit" className={className} disabled={disabled || isSubmitting} {...props}>
      {isSubmitting ? 'Submitting...' : children}
    </button>
  );
};

type FormResetButtonProps = {
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
  onReset?: () => void;
};

export const FormResetButton: React.FC<FormResetButtonProps> = ({
  children,
  className,
  disabled = false,
  onReset,
  ...props
}) => {
  const { resetForm, isSubmitting } = useFormContext();

  const handleClick = () => {
    resetForm();
    if (onReset) onReset();
  };

  return (
    <button
      type="button"
      className={className}
      onClick={handleClick}
      disabled={disabled || isSubmitting}
      {...props}
    >
      {children}
    </button>
  );
};
