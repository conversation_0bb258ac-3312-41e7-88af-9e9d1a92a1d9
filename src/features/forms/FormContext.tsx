import React, { createContext, useContext, useReducer, useCallback } from 'react';
import { z } from 'zod';
import { validateFormData } from '../../../utils/validation';

type FormState<T> = {
  values: Partial<T>;
  errors: Record<string, string>;
  isSubmitting: boolean;
  isDirty: boolean;
  isValid: boolean;
};

type FormAction<T> =
  | { type: 'SET_FIELD'; field: string; value: any }
  | { type: 'SET_ERRORS'; errors: Record<string, string> }
  | { type: 'SET_IS_SUBMITTING'; isSubmitting: boolean }
  | { type: 'RESET'; initialValues: Partial<T> }
  | { type: 'SET_VALUES'; values: Partial<T> };

function formReducer<T>(state: FormState<T>, action: FormAction<T>): FormState<T> {
  switch (action.type) {
    case 'SET_FIELD':
      return {
        ...state,
        values: {
          ...state.values,
          [action.field]: action.value,
        },
        isDirty: true,
      };
    case 'SET_ERRORS':
      return {
        ...state,
        errors: action.errors,
        isValid: Object.keys(action.errors).length === 0,
      };
    case 'SET_IS_SUBMITTING':
      return {
        ...state,
        isSubmitting: action.isSubmitting,
      };
    case 'RESET':
      return {
        ...state,
        values: { ...action.initialValues },
        errors: {},
        isDirty: false,
        isValid: false,
      };
    case 'SET_VALUES':
      return {
        ...state,
        values: { ...action.values },
        isDirty: true,
      };
    default:
      return state;
  }
}

type FormContextType<T> = {
  values: Partial<T>;
  errors: Record<string, string>;
  isSubmitting: boolean;
  isDirty: boolean;
  isValid: boolean;
  setFieldValue: (field: string, value: any) => void;
  setErrors: (errors: Record<string, string>) => void;
  resetForm: () => void;
  setValues: (values: Partial<T>) => void;
  validateForm: () => boolean;
};

const FormContext = createContext<FormContextType<any> | undefined>(undefined);

type FormProviderProps<T> = {
  children: React.ReactNode;
  initialValues: Partial<T>;
  onSubmit: (values: T) => Promise<void> | void;
  validationSchema?: z.ZodSchema<T>;
};

export function FormProvider<T>({
  children,
  initialValues,
  onSubmit,
  validationSchema,
}: FormProviderProps<T>) {
  const [state, dispatch] = useReducer(formReducer<T>, {
    values: { ...initialValues },
    errors: {},
    isSubmitting: false,
    isDirty: false,
    isValid: false,
  });

  const setFieldValue = useCallback((field: string, value: any) => {
    dispatch({ type: 'SET_FIELD', field, value });
  }, []);

  const setErrors = useCallback((errors: Record<string, string>) => {
    dispatch({ type: 'SET_ERRORS', errors });
  }, []);

  const resetForm = useCallback(() => {
    dispatch({ type: 'RESET', initialValues });
  }, [initialValues]);

  const setValues = useCallback((values: Partial<T>) => {
    dispatch({ type: 'SET_VALUES', values });
  }, []);

  const validateForm = useCallback((): boolean => {
    if (!validationSchema) return true;

    const { success, errors } = validateFormData(validationSchema, state.values);

    if (!success) {
      setErrors(errors || {});
      return false;
    }

    setErrors({});
    return true;
  }, [validationSchema, state.values, setErrors]);

  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();

      if (validationSchema && !validateForm()) {
        return;
      }

      try {
        dispatch({ type: 'SET_IS_SUBMITTING', isSubmitting: true });
        await onSubmit(state.values as T);
      } catch (error) {
        console.error('Form submission error:', error);
        // Handle API validation errors if needed
        if ((error as any).response?.data?.errors) {
          setErrors((error as any).response.data.errors);
        }
      } finally {
        dispatch({ type: 'SET_IS_SUBMITTING', isSubmitting: false });
      }
    },
    [onSubmit, state.values, validateForm, validationSchema]
  );

  const value = {
    ...state,
    setFieldValue,
    setErrors,
    resetForm,
    setValues,
    validateForm,
  };

  return (
    <FormContext.Provider value={value}>
      <form onSubmit={handleSubmit}>{children}</form>
    </FormContext.Provider>
  );
}

export function useFormContext<T = any>() {
  const context = useContext(FormContext) as FormContextType<T>;
  if (!context) {
    throw new Error('useFormContext must be used within a FormProvider');
  }
  return context;
}
