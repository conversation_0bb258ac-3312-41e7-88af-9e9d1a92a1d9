import { useState, useCallback } from 'react';
import { z } from 'zod';
import { validateFormData } from '../../../utils/validation';

type UseFormOptions<T> = {
  initialValues: Partial<T>;
  validationSchema?: z.ZodSchema<T>;
  onSubmit: (values: T) => Promise<void> | void;
  onSuccess?: () => void;
  onError?: (error: any) => void;
};

export function useForm<T>({
  initialValues,
  validationSchema,
  onSubmit,
  onSuccess,
  onError,
}: UseFormOptions<T>) {
  const [values, setValues] = useState<Partial<T>>(initialValues);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDirty, setIsDirty] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState<boolean>(false);

  const setFieldValue = useCallback(
    (field: string, value: any) => {
      setValues((prev) => ({
        ...prev,
        [field]: value,
      }));
      setIsDirty(true);

      // Clear error for this field when it changes
      if (errors[field]) {
        setErrors((prev) => {
          const newErrors = { ...prev };
          delete newErrors[field];
          return newErrors;
        });
      }
    },
    [errors]
  );

  const setFormValues = useCallback((newValues: Partial<T>) => {
    setValues((prev) => ({
      ...prev,
      ...newValues,
    }));
    setIsDirty(true);
  }, []);

  const resetForm = useCallback(() => {
    setValues(initialValues);
    setErrors({});
    setIsDirty(false);
    setSubmitError(null);
    setSubmitSuccess(false);
  }, [initialValues]);

  const validate = useCallback((): boolean => {
    if (!validationSchema) return true;

    const { success, errors } = validateFormData(validationSchema, values);

    if (!success && errors) {
      setErrors(errors);
      return false;
    }

    setErrors({});
    return true;
  }, [validationSchema, values]);

  const handleSubmit = useCallback(
    async (e?: React.FormEvent) => {
      if (e) {
        e.preventDefault();
      }

      setSubmitError(null);
      setSubmitSuccess(false);

      if (validationSchema && !validate()) {
        return false;
      }

      try {
        setIsSubmitting(true);
        await onSubmit(values as T);
        setSubmitSuccess(true);
        if (onSuccess) onSuccess();
        return true;
      } catch (error) {
        console.error('Form submission error:', error);

        // Handle API validation errors
        if ((error as any).response?.data?.errors) {
          setErrors((error as any).response.data.errors);
        } else {
          setSubmitError(
            (error as Error).message || 'An error occurred while submitting the form.'
          );
        }

        if (onError) onError(error);
        return false;
      } finally {
        setIsSubmitting(false);
      }
    },
    [onSubmit, onError, onSuccess, validate, validationSchema, values]
  );

  return {
    // State
    values,
    errors,
    isSubmitting,
    isDirty,
    submitError,
    submitSuccess,

    // Actions
    setFieldValue,
    setFormValues,
    setErrors,
    resetForm,
    handleSubmit,
    validate,

    // Form helpers
    getFieldProps: (field: string) => ({
      name: field,
      value: values[field as keyof T] ?? '',
      onChange: (value: any) => setFieldValue(field, value),
      error: errors[field],
    }),

    // Field render helpers
    createTextField: (field: string, label: string, options: any = {}) => ({
      name: field,
      label,
      value: values[field as keyof T] ?? '',
      onChange: (e: React.ChangeEvent<HTMLInputElement>) => setFieldValue(field, e.target.value),
      error: errors[field],
      ...options,
    }),

    createNumberField: (field: string, label: string, options: any = {}) => ({
      name: field,
      label,
      type: 'number',
      value: values[field as keyof T] ?? '',
      onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value === '' ? undefined : Number(e.target.value);
        setFieldValue(field, value);
      },
      error: errors[field],
      ...options,
    }),

    createSelectField: (field: string, label: string, options: any = {}) => ({
      name: field,
      label,
      value: values[field as keyof T] ?? '',
      onChange: (e: React.ChangeEvent<HTMLSelectElement>) =>
        setFieldValue(field, e.target.value || undefined),
      error: errors[field],
      ...options,
    }),

    createCheckboxField: (field: string, label: string, options: any = {}) => ({
      name: field,
      label,
      type: 'checkbox',
      checked: Boolean(values[field as keyof T]),
      onChange: (e: React.ChangeEvent<HTMLInputElement>) => setFieldValue(field, e.target.checked),
      error: errors[field],
      ...options,
    }),
  };
}
