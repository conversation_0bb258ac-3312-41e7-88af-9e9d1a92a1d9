import styled from 'styled-components';

export const FormContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

export const FormHeader = styled.div`
  margin-bottom: 2rem;
  text-align: center;
`;

export const FormTitle = styled.h1`
  font-size: 1.75rem;
  margin-bottom: 0.5rem;
  color: #333;
`;

export const FormDescription = styled.p`
  color: #666;
  margin-bottom: 0;
`;

export const FormSection = styled.div`
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #007bff;
`;

export const SectionTitle = styled.h2`
  font-size: 1.25rem;
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: #333;
`;

export const FormRow = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;

  & > * {
    flex: 1 1 200px;
    min-width: 200px;
  }
`;

export const FormActions = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
`;

export const Button = styled.button<{ variant?: 'primary' | 'secondary' | 'danger' }>`
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;

  ${({ variant = 'primary' }) => {
    switch (variant) {
      case 'secondary':
        return `
          background-color: #6c757d;
          color: white;
          &:hover {
            background-color: #5a6268;
          }
          &:disabled {
            background-color: #6c757d;
            opacity: 0.65;
            cursor: not-allowed;
          }
        `;
      case 'danger':
        return `
          background-color: #dc3545;
          color: white;
          &:hover {
            background-color: #c82333;
          }
          &:disabled {
            background-color: #dc3545;
            opacity: 0.65;
            cursor: not-allowed;
          }
        `;
      default:
        return `
          background-color: #007bff;
          color: white;
          &:hover {
            background-color: #0069d9;
          }
          &:disabled {
            background-color: #007bff;
            opacity: 0.65;
            cursor: not-allowed;
          }
        `;
    }
  }}
`;

export const FormError = styled.div`
  padding: 1rem;
  margin-bottom: 1.5rem;
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
`;

export const FormSuccess = styled.div`
  padding: 1rem;
  margin-bottom: 1.5rem;
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
  border-radius: 4px;
`;

export const FormNote = styled.div`
  font-size: 0.875rem;
  color: #6c757d;
  margin-top: 0.5rem;
`;
