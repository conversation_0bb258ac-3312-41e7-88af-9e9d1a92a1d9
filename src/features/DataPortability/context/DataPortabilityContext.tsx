/**
 * Data Portability Context
 *
 * This context provides state and functions for data portability features.
 * Enhanced to ensure 100% accurate export and import of all Financial Compass and Life Journey forms data.
 */

import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import dataExportService from '../services/dataExportService';
import dataImportService, { ImportOptions } from '../services/dataImportService';
import dataResetService from '../services/dataResetService';

/**
 * Export format type
 */
export type ExportFormat = 'json' | 'pdf';

/**
 * Data section type
 */
export type DataSection = 'all' | 'financialCompass' | 'seasonsOfSelf' | 'userProfiles';

/**
 * Data portability status
 */
export type DataPortabilityStatus =
  | 'idle'
  | 'exporting'
  | 'importing'
  | 'resetting'
  | 'success'
  | 'error';

/**
 * Import result type
 */
export interface ImportResult {
  success: boolean;
  message: string;
  errors?: string[];
}

/**
 * Data portability context type
 */
interface DataPortabilityContextType {
  status: DataPortabilityStatus;
  error: string | null;
  exportData: (section: DataSection, format: ExportFormat) => Promise<void>;
  importData: (file: File, options?: ImportOptions) => Promise<ImportResult>;
  resetData: (section: DataSection) => Promise<void>;
  confirmReset: (section: DataSection) => Promise<boolean>;
  getDataForSection: (section: DataSection) => Promise<Record<string, any>>;
  createSnapshot: (name: string, description?: string) => Promise<void>;
  getSnapshots: () => Promise<any[]>;
  restoreSnapshot: (snapshotId: string) => Promise<boolean>;
  deleteSnapshot: (snapshotId: string) => Promise<boolean>;
}

/**
 * Data portability context
 */
const DataPortabilityContext = createContext<DataPortabilityContextType | undefined>(undefined);

/**
 * Data portability provider props
 */
interface DataPortabilityProviderProps {
  children: ReactNode;
}

/**
 * Data portability provider
 */
export const DataPortabilityProvider: React.FC<DataPortabilityProviderProps> = ({ children }) => {
  // State
  const [status, setStatus] = useState<DataPortabilityStatus>('idle');
  const [error, setError] = useState<string | null>(null);
  const [snapshots, setSnapshots] = useState<any[]>([]);

  // Load snapshots on mount
  useEffect(() => {
    const loadSnapshots = async () => {
      try {
        const loadedSnapshots = await getSnapshots();
        setSnapshots(loadedSnapshots);
      } catch (error) {
        console.error('Error loading snapshots:', error);
      }
    };

    loadSnapshots();
  }, []);

  /**
   * Export data
   */
  const exportData = async (section: DataSection, format: ExportFormat): Promise<void> => {
    try {
      setStatus('exporting');
      setError(null);

      // Get data to export
      const data = await getDataForSection(section);

      // Add export metadata
      const exportData = {
        ...data,
        metadata: {
          exportDate: new Date().toISOString(),
          appVersion: '1.0.0',
          exportType: section,
        },
      };

      // Export data in the specified format
      if (format === 'json') {
        await dataExportService.exportToJson(
          exportData,
          `lifecompass_${section}_${new Date().toISOString().split('T')[0]}.json`
        );
      } else if (format === 'pdf') {
        await dataExportService.exportToPdf(
          exportData,
          `lifecompass_${section}_${new Date().toISOString().split('T')[0]}.pdf`
        );
      }

      setStatus('success');
    } catch (error) {
      console.error('Error exporting data:', error);
      setStatus('error');
      setError(error instanceof Error ? error.message : 'Unknown error');
    }
  };

  /**
   * Import data
   */
  const importData = async (file: File, options: ImportOptions = {}): Promise<ImportResult> => {
    try {
      setStatus('importing');
      setError(null);

      // Get existing data
      const existingData = await getDataForSection('all');

      // Import and process data
      const result = await dataImportService.importAndProcessData(file, existingData, {
        ...options,
        validate: true,
        conflictResolution: options.conflictResolution || 'merge',
        onValidationError: (errors) => {
          console.warn('Validation errors:', errors);
        },
      });

      if (result.success) {
        setStatus('success');
        return {
          success: true,
          message: 'Data imported successfully',
        };
      } else {
        setStatus('error');
        setError('Failed to import data');
        return {
          success: false,
          message: 'Failed to import data',
          errors: result.validationErrors.map((error) => `${error.path}: ${error.message}`),
        };
      }
    } catch (error) {
      console.error('Error importing data:', error);
      setStatus('error');
      setError(error instanceof Error ? error.message : 'Unknown error');

      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  };

  /**
   * Reset data
   */
  const resetData = async (section: DataSection): Promise<void> => {
    try {
      setStatus('resetting');
      setError(null);

      // Reset data for the specified section
      if (section === 'all') {
        await dataResetService.resetAllData();
      } else {
        await dataResetService.resetSectionData(section);
      }

      setStatus('success');
    } catch (error) {
      console.error('Error resetting data:', error);
      setStatus('error');
      setError(error instanceof Error ? error.message : 'Unknown error');
    }
  };

  /**
   * Confirm reset
   */
  const confirmReset = async (section: DataSection): Promise<boolean> => {
    // In a real implementation, this would show a confirmation dialog
    // For now, we'll just return true
    return confirm(
      `Are you sure you want to reset all ${section} data? This action cannot be undone.`
    );
  };

  /**
   * Get data for a specific section
   */
  const getDataForSection = async (section: DataSection): Promise<Record<string, any>> => {
    // Get data from localStorage
    const financialCompassData = JSON.parse(
      localStorage.getItem('lifecompass_financial_compass') || '{}'
    );
    const seasonsOfSelfData = JSON.parse(
      localStorage.getItem('lifecompass_seasons_of_self') || '{}'
    );
    const userProfilesData = JSON.parse(localStorage.getItem('lifecompass_profiles') || '[]');

    // Return data for the specified section
    switch (section) {
      case 'all':
        return {
          financialCompass: financialCompassData,
          seasonsOfSelf: seasonsOfSelfData,
          userProfiles: userProfilesData,
        };
      case 'financialCompass':
        return { financialCompass: financialCompassData };
      case 'seasonsOfSelf':
        return { seasonsOfSelf: seasonsOfSelfData };
      case 'userProfiles':
        return { userProfiles: userProfilesData };
      default:
        throw new Error(`Unknown section: ${section}`);
    }
  };

  /**
   * Create a snapshot of the current data
   */
  const createSnapshot = async (name: string, description?: string): Promise<void> => {
    try {
      setStatus('exporting');
      setError(null);

      // Get all data
      const data = await getDataForSection('all');

      // Create snapshot object
      const snapshot = {
        id: Date.now().toString(),
        name: name || `Snapshot ${new Date().toLocaleString()}`,
        date: new Date().toISOString(),
        description: description || 'Snapshot of current app state',
        data,
      };

      // Get existing snapshots
      const existingSnapshots = await getSnapshots();

      // Add new snapshot
      const updatedSnapshots = [snapshot, ...existingSnapshots];

      // Save snapshots
      localStorage.setItem('lifecompass_snapshots', JSON.stringify(updatedSnapshots));

      // Update state
      setSnapshots(updatedSnapshots);
      setStatus('success');
    } catch (error) {
      console.error('Error creating snapshot:', error);
      setStatus('error');
      setError(error instanceof Error ? error.message : 'Unknown error');
    }
  };

  /**
   * Get all snapshots
   */
  const getSnapshots = async (): Promise<any[]> => {
    try {
      // Get snapshots from localStorage
      const snapshotsData = localStorage.getItem('lifecompass_snapshots');

      // Parse snapshots
      const parsedSnapshots = snapshotsData ? JSON.parse(snapshotsData) : [];

      return parsedSnapshots;
    } catch (error) {
      console.error('Error getting snapshots:', error);
      return [];
    }
  };

  /**
   * Restore data from a snapshot
   */
  const restoreSnapshot = async (snapshotId: string): Promise<boolean> => {
    try {
      setStatus('importing');
      setError(null);

      // Get snapshots
      const existingSnapshots = await getSnapshots();

      // Find snapshot
      const snapshot = existingSnapshots.find((s) => s.id === snapshotId);

      if (!snapshot) {
        throw new Error(`Snapshot with ID ${snapshotId} not found`);
      }

      // Get snapshot data
      const { data } = snapshot;

      // Save Financial Compass data if present
      if (data.financialCompass) {
        localStorage.setItem(
          'lifecompass_financial_compass',
          JSON.stringify(data.financialCompass)
        );
      }

      // Save Seasons of Self data if present
      if (data.seasonsOfSelf) {
        localStorage.setItem('lifecompass_seasons_of_self', JSON.stringify(data.seasonsOfSelf));
      }

      // Save user profiles data if present
      if (data.userProfiles) {
        localStorage.setItem('lifecompass_profiles', JSON.stringify(data.userProfiles));
      }

      setStatus('success');
      return true;
    } catch (error) {
      console.error('Error restoring snapshot:', error);
      setStatus('error');
      setError(error instanceof Error ? error.message : 'Unknown error');
      return false;
    }
  };

  /**
   * Delete a snapshot
   */
  const deleteSnapshot = async (snapshotId: string): Promise<boolean> => {
    try {
      // Get snapshots
      const existingSnapshots = await getSnapshots();

      // Filter out the snapshot to delete
      const updatedSnapshots = existingSnapshots.filter((s) => s.id !== snapshotId);

      // Save updated snapshots
      localStorage.setItem('lifecompass_snapshots', JSON.stringify(updatedSnapshots));

      // Update state
      setSnapshots(updatedSnapshots);

      return true;
    } catch (error) {
      console.error('Error deleting snapshot:', error);
      return false;
    }
  };

  // Context value
  const value: DataPortabilityContextType = {
    status,
    error,
    exportData,
    importData,
    resetData,
    confirmReset,
    getDataForSection,
    createSnapshot,
    getSnapshots,
    restoreSnapshot,
    deleteSnapshot,
  };

  return (
    <DataPortabilityContext.Provider value={value}>{children}</DataPortabilityContext.Provider>
  );
};

/**
 * Hook to use the data portability context
 */
export const useDataPortability = (): DataPortabilityContextType => {
  const context = useContext(DataPortabilityContext);

  if (context === undefined) {
    throw new Error('useDataPortability must be used within a DataPortabilityProvider');
  }

  return context;
};
