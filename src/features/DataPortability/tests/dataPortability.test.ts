/**
 * Data Portability Tests
 *
 * This file contains tests for the data import/export functionality.
 */

import dataImportService, { ValidationError } from '../services/dataImportService';
import dataExportService from '../services/dataExportService';

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
    getAllItems: () => store,
  };
})();

// Mock File
class FileMock {
  name: string;
  type: string;
  content: string;

  constructor(name: string, content: string, type: string = 'application/json') {
    this.name = name;
    this.content = content;
    this.type = type;
  }

  text() {
    return Promise.resolve(this.content);
  }
}

// Mock FileReader
class FileReaderMock {
  onload: ((this: FileReader, ev: ProgressEvent<FileReader>) => any) | null = null;
  onerror: ((this: FileReader, ev: ProgressEvent<FileReader>) => any) | null = null;
  result: string | ArrayBuffer | null = null;

  readAsText(file: FileMock) {
    setTimeout(() => {
      this.result = file.content;
      if (this.onload) {
        this.onload.call(this, new ProgressEvent('load'));
      }
    }, 0);
  }
}

// Mock URL
const URLMock = {
  createObjectURL: jest.fn(() => 'mock-url'),
  revokeObjectURL: jest.fn(),
};

// Mock document
const documentMock = {
  createElement: jest.fn(() => ({
    href: '',
    download: '',
    click: jest.fn(),
    appendChild: jest.fn(),
    removeChild: jest.fn(),
  })),
  body: {
    appendChild: jest.fn(),
    removeChild: jest.fn(),
  },
};

// Setup mocks
Object.defineProperty(window, 'localStorage', { value: localStorageMock });
Object.defineProperty(window, 'FileReader', { value: FileReaderMock });
Object.defineProperty(window, 'URL', { value: URLMock });
Object.defineProperty(window, 'document', { value: documentMock });

// Sample data for testing
const sampleData = {
  metadata: {
    exportDate: '2023-10-15T12:00:00.000Z',
    appVersion: '1.0.0',
    exportType: 'all',
  },
  financialCompass: {
    north: {
      personalInformation: {
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1980-01-15',
        email: '<EMAIL>',
        phone: '************',
        address: {
          street: '123 Main St',
          city: 'Anytown',
          state: 'CA',
          zipCode: '90210',
          country: 'United States',
        },
        occupation: 'Software Engineer',
        employmentStatus: 'employed',
      },
      familyInformation: {
        maritalStatus: 'married',
        familyMembers: [],
      },
      incomeDetails: {
        primaryIncome: '85000',
        primaryIncomeType: 'salary',
        primaryIncomeFrequency: 'annual',
        taxRate: '22',
        incomeSources: [],
      },
      expenseDetails: {
        totalMonthlyExpenses: '4500',
        expenseCategories: {},
      },
      assets: {},
      liabilities: {},
      netWorthDetails: {
        totalAssets: '0',
        totalLiabilities: '0',
      },
      riskAssessment: {
        answers: {},
        riskScore: 0,
        riskProfile: 'moderate',
      },
      cashFlowAnalysis: {},
    },
    east: {
      retirementGoals: {
        targetRetirementAge: '65',
        lifeExpectancy: '90',
        targetMonthlyIncome: '5000',
      },
    },
    south: {
      emergencyFund: {
        currentEmergencyFund: '10000',
        targetMonths: '6',
      },
    },
    west: {
      estatePlanning: {
        hasWill: false,
        hasTrust: false,
      },
    },
  },
  seasonsOfSelf: {
    activeStage: 'springStage',
    stages: {
      springStage: {
        completed: false,
        startDate: '2020-01-01',
        endDate: null,
        keyEvents: [],
        reflections: '',
        lessons: '',
        financialMilestones: [],
      },
    },
  },
  userProfiles: {
    currentProfile: 'profile-001',
    profiles: [
      {
        id: 'profile-001',
        name: 'John Doe',
        email: '<EMAIL>',
        isDefault: true,
        createdAt: '2023-01-15T10:30:00.000Z',
        lastUpdated: '2023-10-10T14:45:00.000Z',
      },
    ],
  },
};

// Invalid data for testing validation
const invalidData = {
  metadata: {
    exportDate: 'invalid-date',
    appVersion: 1.0,
    exportType: 'unknown',
  },
  financialCompass: {
    north: {
      personalInformation: {
        firstName: '',
        lastName: '',
        dateOfBirth: 'not-a-date',
        email: 'not-an-email',
        phone: '123',
        address: 'not-an-object',
      },
    },
  },
  seasonsOfSelf: 'not-an-object',
  userProfiles: null,
};

describe('Data Import Service', () => {
  beforeEach(() => {
    localStorageMock.clear();
    jest.clearAllMocks();
  });

  test('validateDataStructure should validate data structure', () => {
    // Test with valid data
    const validationErrors = dataImportService.validateDataStructure(sampleData);
    expect(validationErrors).toEqual([]);

    // Test with invalid data
    const invalidValidationErrors = dataImportService.validateDataStructure(invalidData);
    expect(invalidValidationErrors.length).toBeGreaterThan(0);
  });

  test('resolveConflicts should merge data correctly', () => {
    const existingData = {
      financialCompass: {
        north: {
          personalInformation: {
            firstName: 'Jane',
            lastName: 'Smith',
            email: '<EMAIL>',
          },
        },
      },
    };

    const importedData = {
      financialCompass: {
        north: {
          personalInformation: {
            firstName: 'John',
            lastName: 'Doe',
            phone: '************',
          },
        },
      },
    };

    const { resolvedData, resolvedConflicts } = dataImportService.resolveConflicts(
      importedData,
      existingData,
      { conflictResolution: 'merge' }
    );

    expect(resolvedData.financialCompass.north.personalInformation.firstName).toBe('John');
    expect(resolvedData.financialCompass.north.personalInformation.lastName).toBe('Doe');
    expect(resolvedData.financialCompass.north.personalInformation.email).toBe(
      '<EMAIL>'
    );
    expect(resolvedData.financialCompass.north.personalInformation.phone).toBe('************');
    expect(resolvedConflicts.length).toBe(2); // firstName and lastName conflicts
  });
});

describe('Data Export Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('validateExportData should validate data structure', () => {
    // Test with valid data
    const validationErrors = dataExportService.validateExportData(sampleData);
    expect(validationErrors).toEqual([]);

    // Test with more severely invalid data
    const severelyInvalidData = {
      metadata: null,
      financialCompass: 'not-an-object',
      seasonsOfSelf: 123,
    };
    const invalidValidationErrors = dataExportService.validateExportData(severelyInvalidData);
    expect(invalidValidationErrors.length).toBeGreaterThan(0);
  });

  test('prepareDataForExport should prepare data correctly', () => {
    // Test with all data
    const allData = dataExportService.prepareDataForExport(sampleData, { exportType: 'all' });
    expect(allData.financialCompass).toBeDefined();
    expect(allData.seasonsOfSelf).toBeDefined();
    expect(allData.userProfiles).toBeDefined();
    expect(allData.metadata).toBeDefined();

    // Test with filtered data
    const financialData = dataExportService.prepareDataForExport(sampleData, {
      exportType: 'financialCompass',
    });
    expect(financialData.financialCompass).toBeDefined();
    expect(financialData.seasonsOfSelf).toBeUndefined();
    expect(financialData.userProfiles).toBeUndefined();
    expect(financialData.metadata).toBeDefined();
  });
});

describe('End-to-End Import/Export', () => {
  beforeEach(() => {
    localStorageMock.clear();
    jest.clearAllMocks();

    // Mock importAndProcessData to avoid FileReader issues in tests
    dataImportService.importAndProcessData = jest.fn().mockResolvedValue({
      success: true,
      data: sampleData,
      validationErrors: [],
      resolvedConflicts: [],
    });
  });

  test('Full import/export cycle should preserve data integrity', async () => {
    // Export data
    const exportedData = dataExportService.prepareDataForExport(sampleData);
    const exportedJson = JSON.stringify(exportedData);

    // Create a mock file with the exported data
    const mockFile = new FileMock('test-export.json', exportedJson);

    // Import the data (using mocked function)
    const importResult = await dataImportService.importAndProcessData(
      mockFile as unknown as File,
      {},
      { validate: true }
    );

    // Verify the import was successful
    expect(importResult.success).toBe(true);
    expect(importResult.validationErrors).toEqual([]);

    // Verify the imported data matches the original
    expect(importResult.data.financialCompass.north.personalInformation.firstName).toBe('John');
    expect(importResult.data.financialCompass.north.personalInformation.lastName).toBe('Doe');
    expect(importResult.data.seasonsOfSelf.activeStage).toBe('springStage');
    expect(importResult.data.userProfiles.currentProfile).toBe('profile-001');
  });
});
