/**
 * Auto Save Service
 *
 * This service handles automatic saving of user data with enhanced functionality
 * for local storage and server synchronization.
 */

import debounce from 'lodash/debounce';

/**
 * Default auto-save interval in milliseconds (5 seconds)
 */
const DEFAULT_AUTOSAVE_INTERVAL = 5000;

/**
 * Default debounce time in milliseconds (1 second)
 */
const DEFAULT_DEBOUNCE_TIME = 1000;

/**
 * Auto-save configuration
 */
interface AutoSaveConfig {
  /**
   * The key to use for storing data
   */
  storageKey: string;

  /**
   * The interval between auto-saves in milliseconds
   */
  interval?: number;

  /**
   * Function to call before saving data
   */
  onBeforeSave?: () => void;

  /**
   * Function to call after saving data
   */
  onAfterSave?: () => void;

  /**
   * Function to call if an error occurs during saving
   */
  onError?: (error: Error) => void;
}

/**
 * Auto-save controller
 */
interface AutoSaveController {
  /**
   * Start auto-saving
   */
  start: () => void;

  /**
   * Stop auto-saving
   */
  stop: () => void;

  /**
   * Save data immediately
   */
  saveNow: () => Promise<void>;

  /**
   * Check if auto-save is active
   */
  isActive: () => boolean;

  /**
   * Load saved data
   */
  loadSavedData: () => any;
}

/**
 * Create an auto-save controller
 *
 * @param getData - Function that returns the data to save
 * @param config - Auto-save configuration
 * @returns An auto-save controller
 */
export const createAutoSave = (
  getData: () => Record<string, any>,
  config: AutoSaveConfig
): AutoSaveController => {
  let intervalId: number | null = null;
  const interval = config.interval || DEFAULT_AUTOSAVE_INTERVAL;

  /**
   * Save data to storage
   */
  const saveData = async (): Promise<void> => {
    try {
      // Call onBeforeSave if provided
      if (config.onBeforeSave) {
        config.onBeforeSave();
      }

      // Get data to save
      const data = getData();

      // Save data to localStorage
      localStorage.setItem(config.storageKey, JSON.stringify(data));

      // Call onAfterSave if provided
      if (config.onAfterSave) {
        config.onAfterSave();
      }
    } catch (error) {
      console.error('Error auto-saving data:', error);

      // Call onError if provided
      if (config.onError && error instanceof Error) {
        config.onError(error);
      }
    }
  };

  /**
   * Start auto-saving
   */
  const start = (): void => {
    if (intervalId !== null) {
      return; // Already started
    }

    // Start interval
    intervalId = window.setInterval(saveData, interval);
  };

  /**
   * Stop auto-saving
   */
  const stop = (): void => {
    if (intervalId === null) {
      return; // Already stopped
    }

    // Clear interval
    window.clearInterval(intervalId);
    intervalId = null;
  };

  /**
   * Save data immediately
   */
  const saveNow = async (): Promise<void> => {
    await saveData();
  };

  /**
   * Check if auto-save is active
   */
  const isActive = (): boolean => {
    return intervalId !== null;
  };

  /**
   * Load saved data from localStorage
   */
  const loadSavedData = (): any => {
    try {
      const savedData = localStorage.getItem(config.storageKey);
      return savedData ? JSON.parse(savedData) : null;
    } catch (error) {
      console.error('Error loading saved data:', error);

      // Call onError if provided
      if (config.onError && error instanceof Error) {
        config.onError(error);
      }

      return null;
    }
  };

  return {
    start,
    stop,
    saveNow,
    isActive,
    loadSavedData,
  };
};

// Enhanced Auto Save Types
export interface AutoSaveOptions {
  key: string;
  debounceTime?: number;
  onSave?: (data: any) => void;
  onError?: (error: Error) => void;
  localStorage?: boolean;
  serverSync?: boolean;
}

export interface AutoSaveInstance {
  save: (data: any) => Promise<void>;
  load: () => Promise<any>;
  clear: () => Promise<void>;
  getLastSaved: () => Date | null;
}

/**
 * Create an enhanced auto-save instance for a specific data key
 *
 * @param options - Configuration options for the auto-save instance
 * @returns An auto-save instance with save, load, and clear methods
 */
export const createEnhancedAutoSave = (options: AutoSaveOptions): AutoSaveInstance => {
  const {
    key,
    debounceTime = DEFAULT_DEBOUNCE_TIME,
    onSave,
    onError,
    localStorage = true,
    serverSync = false,
  } = options;

  let lastSaved: Date | null = null;

  // Save data to local storage
  const saveToLocalStorage = (data: any): void => {
    try {
      const serializedData = JSON.stringify(data);
      window.localStorage.setItem(`lifecompass_${key}`, serializedData);
    } catch (error) {
      console.error('Error saving to local storage:', error);
      if (onError) {
        onError(error as Error);
      }
    }
  };

  // Load data from local storage
  const loadFromLocalStorage = (): any => {
    try {
      const serializedData = window.localStorage.getItem(`lifecompass_${key}`);
      if (serializedData === null) {
        return null;
      }
      return JSON.parse(serializedData);
    } catch (error) {
      console.error('Error loading from local storage:', error);
      if (onError) {
        onError(error as Error);
      }
      return null;
    }
  };

  // Save data to server (mock implementation)
  const saveToServer = async (data: any): Promise<void> => {
    try {
      // This is a mock implementation - in a real app, you would call an API
      console.log(`Saving ${key} data to server:`, data);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Simulate successful save
      return Promise.resolve();
    } catch (error) {
      console.error('Error saving to server:', error);
      if (onError) {
        onError(error as Error);
      }
      return Promise.reject(error);
    }
  };

  // Load data from server (mock implementation)
  const loadFromServer = async (): Promise<any> => {
    try {
      // This is a mock implementation - in a real app, you would call an API
      console.log(`Loading ${key} data from server`);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Simulate successful load
      return Promise.resolve(null);
    } catch (error) {
      console.error('Error loading from server:', error);
      if (onError) {
        onError(error as Error);
      }
      return Promise.reject(error);
    }
  };

  // Debounced save function
  const debouncedSave = debounce(async (data: any) => {
    try {
      // Save to local storage if enabled
      if (localStorage) {
        saveToLocalStorage(data);
      }

      // Save to server if enabled
      if (serverSync) {
        await saveToServer(data);
      }

      // Update last saved timestamp
      lastSaved = new Date();

      // Call onSave callback if provided
      if (onSave) {
        onSave(data);
      }
    } catch (error) {
      console.error('Error in auto-save:', error);
      if (onError) {
        onError(error as Error);
      }
    }
  }, debounceTime);

  // Public methods
  return {
    // Save data
    save: async (data: any): Promise<void> => {
      debouncedSave(data);
      return Promise.resolve();
    },

    // Load data
    load: async (): Promise<any> => {
      // Try to load from server first if server sync is enabled
      if (serverSync) {
        const serverData = await loadFromServer();
        if (serverData !== null) {
          return serverData;
        }
      }

      // Fall back to local storage if server data is not available
      if (localStorage) {
        return loadFromLocalStorage();
      }

      return null;
    },

    // Clear data
    clear: async (): Promise<void> => {
      if (localStorage) {
        window.localStorage.removeItem(`lifecompass_${key}`);
      }

      if (serverSync) {
        // In a real app, you would call an API to clear server data
        console.log(`Clearing ${key} data from server`);
      }

      lastSaved = null;
      return Promise.resolve();
    },

    // Get last saved timestamp
    getLastSaved: (): Date | null => {
      return lastSaved;
    },
  };
};

/**
 * Create enhanced auto-save instances for multiple data keys
 *
 * @param keys - Array of data keys to create auto-save instances for
 * @param options - Common options for all auto-save instances
 * @returns An object with auto-save instances for each key
 */
export const createMultiAutoSave = (
  keys: string[],
  options: Omit<AutoSaveOptions, 'key'> = {}
): Record<string, AutoSaveInstance> => {
  const instances: Record<string, AutoSaveInstance> = {};

  keys.forEach((key) => {
    instances[key] = createEnhancedAutoSave({
      ...options,
      key,
    });
  });

  return instances;
};

/**
 * Auto Save Service
 */
const autoSaveService = {
  createAutoSave,
  createEnhancedAutoSave,
  createMultiAutoSave,
};

export default autoSaveService;
