/**
 * Data Import Service
 *
 * This service handles importing user data from JSON format with validation
 * and conflict resolution.
 */

// Types
export interface ImportOptions {
  /**
   * How to handle conflicts between imported data and existing data
   */
  conflictResolution?: 'overwrite' | 'merge' | 'skip';

  /**
   * Whether to validate the imported data structure
   */
  validate?: boolean;

  /**
   * Callback for handling validation errors
   */
  onValidationError?: (errors: ValidationError[]) => void;
}

export interface ValidationError {
  path: string;
  message: string;
  value?: any;
}

export interface ImportResult {
  /**
   * The imported data after validation and conflict resolution
   */
  data: Record<string, any>;

  /**
   * Any validation errors that occurred
   */
  validationErrors: ValidationError[];

  /**
   * Whether the import was successful
   */
  success: boolean;

  /**
   * Any conflicts that were resolved
   */
  resolvedConflicts: {
    path: string;
    existingValue: any;
    importedValue: any;
    resolution: 'overwrite' | 'merge' | 'skip';
  }[];
}

/**
 * Import data from JSON file
 *
 * @param file - The JSON file to import
 * @returns A Promise that resolves with the imported data
 */
export const importFromJson = async (file: File): Promise<Record<string, any>> => {
  try {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (event) => {
        try {
          if (!event.target?.result) {
            throw new Error('Failed to read file');
          }

          const jsonData = JSON.parse(event.target.result as string);
          resolve(jsonData);
        } catch (error) {
          reject(new Error('Invalid JSON file'));
        }
      };

      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };

      reader.readAsText(file);
    });
  } catch (error) {
    console.error('Error importing data from JSON:', error);
    throw error;
  }
};

/**
 * Validate imported data
 *
 * @param data - The data to validate
 * @returns A Promise that resolves with the validated data or rejects with an error
 */
export const validateImportedData = async (
  data: Record<string, any>
): Promise<Record<string, any>> => {
  try {
    // Check if the data has the expected structure
    if (!data || typeof data !== 'object') {
      throw new Error('Invalid data format: Expected an object');
    }

    // Validate Financial Compass data if present
    if (data.financialCompass) {
      // Check if financialCompass is an object
      if (typeof data.financialCompass !== 'object') {
        throw new Error('Invalid Financial Compass data: Expected an object');
      }

      // Check for required directions
      const directions = ['north', 'east', 'south', 'west'];
      directions.forEach((direction) => {
        if (
          data.financialCompass[direction] &&
          typeof data.financialCompass[direction] !== 'object'
        ) {
          throw new Error(
            `Invalid Financial Compass ${direction} direction data: Expected an object`
          );
        }
      });
    }

    // Validate Seasons of Self data if present
    if (data.seasonsOfSelf) {
      // Check if seasonsOfSelf is an object
      if (typeof data.seasonsOfSelf !== 'object') {
        throw new Error('Invalid Seasons of Self data: Expected an object');
      }

      // Check for required seasons
      const seasons = ['spring', 'summer', 'autumn', 'winter'];
      seasons.forEach((season) => {
        if (data.seasonsOfSelf[season] && typeof data.seasonsOfSelf[season] !== 'object') {
          throw new Error(`Invalid Seasons of Self ${season} season data: Expected an object`);
        }
      });
    }

    // Validate metadata if present
    if (data.metadata) {
      if (typeof data.metadata !== 'object') {
        throw new Error('Invalid metadata: Expected an object');
      }
    }

    return data;
  } catch (error) {
    console.error('Error validating imported data:', error);
    throw error;
  }
};

/**
 * Perform detailed validation of imported data
 *
 * @param data - The data to validate
 * @returns An array of validation errors
 */
export const validateDataStructure = (data: Record<string, any>): ValidationError[] => {
  const errors: ValidationError[] = [];

  // Check if the data has the expected structure
  if (!data || typeof data !== 'object') {
    errors.push({
      path: '',
      message: 'Invalid data format: expected an object',
    });
    return errors;
  }

  // Check for required sections - note that profile is optional
  const requiredSections = ['financialCompass', 'seasonsOfSelf'];

  requiredSections.forEach((section) => {
    if (!data[section]) {
      errors.push({
        path: section,
        message: `Missing required section: ${section}`,
      });
    }
  });

  // Validate metadata if present
  if (data.metadata) {
    if (typeof data.metadata !== 'object' || Array.isArray(data.metadata)) {
      errors.push({
        path: 'metadata',
        message: 'Invalid metadata: Expected an object',
      });
    } else {
      // Validate metadata fields
      if (
        data.metadata.exportDate &&
        !/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z$/.test(data.metadata.exportDate)
      ) {
        errors.push({
          path: 'metadata.exportDate',
          message: 'Invalid export date format: Expected ISO date string',
          value: data.metadata.exportDate,
        });
      }

      if (data.metadata.appVersion && typeof data.metadata.appVersion !== 'string') {
        errors.push({
          path: 'metadata.appVersion',
          message: 'Invalid app version: Expected string',
          value: data.metadata.appVersion,
        });
      }

      if (
        data.metadata.exportType &&
        !['all', 'financialCompass', 'seasonsOfSelf', 'userProfiles'].includes(
          data.metadata.exportType
        )
      ) {
        errors.push({
          path: 'metadata.exportType',
          message:
            'Invalid export type: Expected "all", "financialCompass", "seasonsOfSelf", or "userProfiles"',
          value: data.metadata.exportType,
        });
      }
    }
  }

  // Validate Financial Compass structure if present
  if (data.financialCompass) {
    const compass = data.financialCompass;

    // Check if financialCompass is an object
    if (typeof compass !== 'object' || Array.isArray(compass)) {
      errors.push({
        path: 'financialCompass',
        message: 'Invalid Financial Compass data: Expected an object',
      });
    } else {
      // Check for required directions
      const directions = ['north', 'east', 'south', 'west'];
      directions.forEach((direction) => {
        if (!compass[direction]) {
          errors.push({
            path: `financialCompass.${direction}`,
            message: `Missing required direction: ${direction}`,
          });
        } else if (typeof compass[direction] !== 'object' || Array.isArray(compass[direction])) {
          errors.push({
            path: `financialCompass.${direction}`,
            message: `Invalid ${direction} direction data: Expected an object`,
          });
        }
      });

      // Validate North direction structure if present
      if (compass.north && typeof compass.north === 'object') {
        const northSections = [
          'personalInformation',
          'familyInformation',
          'incomeDetails',
          'expenseDetails',
          'assets',
          'liabilities',
          'netWorthDetails',
          'riskAssessment',
          'cashFlowAnalysis',
        ];

        northSections.forEach((section) => {
          if (compass.north[section] && typeof compass.north[section] !== 'object') {
            errors.push({
              path: `financialCompass.north.${section}`,
              message: `Invalid ${section} data: Expected an object`,
            });
          }
        });

        // Validate personal information
        if (compass.north.personalInformation) {
          const personalInfo = compass.north.personalInformation;

          // Check required fields
          const requiredPersonalFields = [
            'firstName',
            'lastName',
            'email',
            'phone',
            'address',
            'occupation',
            'employmentStatus',
          ];
          requiredPersonalFields.forEach((field) => {
            if (!personalInfo[field]) {
              errors.push({
                path: `financialCompass.north.personalInformation.${field}`,
                message: `Missing required field: ${field}`,
              });
            }
          });

          // Validate address
          if (personalInfo.address && typeof personalInfo.address === 'object') {
            const requiredAddressFields = ['street', 'city', 'state', 'zipCode', 'country'];
            requiredAddressFields.forEach((field) => {
              if (!personalInfo.address[field]) {
                errors.push({
                  path: `financialCompass.north.personalInformation.address.${field}`,
                  message: `Missing required field: ${field}`,
                });
              }
            });
          } else if (personalInfo.address) {
            errors.push({
              path: `financialCompass.north.personalInformation.address`,
              message: `Invalid address data: Expected an object`,
            });
          }

          // Validate email format
          if (
            personalInfo.email &&
            typeof personalInfo.email === 'string' &&
            !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(personalInfo.email)
          ) {
            errors.push({
              path: `financialCompass.north.personalInformation.email`,
              message: `Invalid email format`,
              value: personalInfo.email,
            });
          }

          // Validate date of birth
          if (
            personalInfo.dateOfBirth &&
            typeof personalInfo.dateOfBirth === 'string' &&
            !/^\d{4}-\d{2}-\d{2}$/.test(personalInfo.dateOfBirth)
          ) {
            errors.push({
              path: `financialCompass.north.personalInformation.dateOfBirth`,
              message: `Invalid date format: Expected YYYY-MM-DD`,
              value: personalInfo.dateOfBirth,
            });
          }

          // Validate employment status
          if (
            personalInfo.employmentStatus &&
            !['employed', 'self-employed', 'unemployed', 'retired', 'student'].includes(
              personalInfo.employmentStatus
            )
          ) {
            errors.push({
              path: `financialCompass.north.personalInformation.employmentStatus`,
              message: `Invalid employment status: Expected one of 'employed', 'self-employed', 'unemployed', 'retired', 'student'`,
              value: personalInfo.employmentStatus,
            });
          }
        }

        // Validate income details
        if (compass.north.incomeDetails) {
          const incomeDetails = compass.north.incomeDetails;

          // Check required fields
          const requiredIncomeFields = [
            'primaryIncome',
            'primaryIncomeType',
            'primaryIncomeFrequency',
            'taxRate',
          ];
          requiredIncomeFields.forEach((field) => {
            if (!incomeDetails[field]) {
              errors.push({
                path: `financialCompass.north.incomeDetails.${field}`,
                message: `Missing required field: ${field}`,
              });
            }
          });

          // Validate income sources
          if (incomeDetails.incomeSources) {
            if (!Array.isArray(incomeDetails.incomeSources)) {
              errors.push({
                path: `financialCompass.north.incomeDetails.incomeSources`,
                message: `Invalid income sources: Expected an array`,
              });
            } else {
              // Validate each income source
              incomeDetails.incomeSources.forEach((source, index) => {
                if (typeof source !== 'object' || Array.isArray(source)) {
                  errors.push({
                    path: `financialCompass.north.incomeDetails.incomeSources[${index}]`,
                    message: `Invalid income source: Expected an object`,
                  });
                } else {
                  // Check required fields for each income source
                  const requiredSourceFields = ['id', 'type', 'amount', 'frequency'];
                  requiredSourceFields.forEach((field) => {
                    if (!source[field]) {
                      errors.push({
                        path: `financialCompass.north.incomeDetails.incomeSources[${index}].${field}`,
                        message: `Missing required field: ${field}`,
                      });
                    }
                  });

                  // Validate amount is a number or numeric string
                  if (source.amount && isNaN(Number(source.amount))) {
                    errors.push({
                      path: `financialCompass.north.incomeDetails.incomeSources[${index}].amount`,
                      message: `Invalid amount: Expected a number or numeric string`,
                      value: source.amount,
                    });
                  }
                }
              });
            }
          }

          // Validate primary income is a number or numeric string
          if (incomeDetails.primaryIncome && isNaN(Number(incomeDetails.primaryIncome))) {
            errors.push({
              path: `financialCompass.north.incomeDetails.primaryIncome`,
              message: `Invalid primary income: Expected a number or numeric string`,
              value: incomeDetails.primaryIncome,
            });
          }

          // Validate tax rate is a number or numeric string
          if (incomeDetails.taxRate && isNaN(Number(incomeDetails.taxRate))) {
            errors.push({
              path: `financialCompass.north.incomeDetails.taxRate`,
              message: `Invalid tax rate: Expected a number or numeric string`,
              value: incomeDetails.taxRate,
            });
          }
        }
      }

      // Validate East direction structure if present
      if (compass.east && typeof compass.east === 'object') {
        const eastSections = [
          'retirementGoals',
          'retirementIncome',
          'retirementExpenses',
          'retirementTimeline',
          'socialSecurityPlanning',
        ];

        eastSections.forEach((section) => {
          if (compass.east[section] && typeof compass.east[section] !== 'object') {
            errors.push({
              path: `financialCompass.east.${section}`,
              message: `Invalid ${section} data: Expected an object`,
            });
          }
        });

        // Validate retirement goals
        if (compass.east.retirementGoals) {
          const retirementGoals = compass.east.retirementGoals;

          // Check required fields
          const requiredGoalsFields = [
            'targetRetirementAge',
            'lifeExpectancy',
            'targetMonthlyIncome',
          ];
          requiredGoalsFields.forEach((field) => {
            if (!retirementGoals[field]) {
              errors.push({
                path: `financialCompass.east.retirementGoals.${field}`,
                message: `Missing required field: ${field}`,
              });
            }
          });

          // Validate numeric fields
          const numericFields = [
            'targetRetirementAge',
            'currentAge',
            'lifeExpectancy',
            'targetMonthlyIncome',
            'currentRetirementSavings',
            'annualContribution',
            'expectedReturnRate',
          ];
          numericFields.forEach((field) => {
            if (retirementGoals[field] && isNaN(Number(retirementGoals[field]))) {
              errors.push({
                path: `financialCompass.east.retirementGoals.${field}`,
                message: `Invalid ${field}: Expected a number or numeric string`,
                value: retirementGoals[field],
              });
            }
          });

          // Validate retirement activities is an array if present
          if (
            retirementGoals.retirementActivities &&
            !Array.isArray(retirementGoals.retirementActivities)
          ) {
            errors.push({
              path: `financialCompass.east.retirementGoals.retirementActivities`,
              message: `Invalid retirement activities: Expected an array`,
            });
          }
        }

        // Validate social security planning
        if (compass.east.socialSecurityPlanning) {
          const socialSecurity = compass.east.socialSecurityPlanning;

          // Check required fields
          const requiredSSFields = ['selectedClaimingAge'];
          requiredSSFields.forEach((field) => {
            if (!socialSecurity[field]) {
              errors.push({
                path: `financialCompass.east.socialSecurityPlanning.${field}`,
                message: `Missing required field: ${field}`,
              });
            }
          });

          // Validate numeric fields
          const numericFields = [
            'selectedClaimingAge',
            'birthYear',
            'fullRetirementAge',
            'earlyClaimingAge',
            'delayedClaimingAge',
            'estimatedMonthlyBenefitAtFRA',
            'earlyClaimingReduction',
            'delayedClaimingIncrease',
            'spouseBenefit',
            'yearsOfCoveredEmployment',
            'averageIndexedMonthlyEarnings',
            'primaryInsuranceAmount',
          ];
          numericFields.forEach((field) => {
            if (socialSecurity[field] && isNaN(Number(socialSecurity[field]))) {
              errors.push({
                path: `financialCompass.east.socialSecurityPlanning.${field}`,
                message: `Invalid ${field}: Expected a number or numeric string`,
                value: socialSecurity[field],
              });
            }
          });

          // Validate hasWorkingHistory is a boolean if present
          if (
            socialSecurity.hasWorkingHistory !== undefined &&
            typeof socialSecurity.hasWorkingHistory !== 'boolean'
          ) {
            errors.push({
              path: `financialCompass.east.socialSecurityPlanning.hasWorkingHistory`,
              message: `Invalid hasWorkingHistory: Expected a boolean`,
              value: socialSecurity.hasWorkingHistory,
            });
          }
        }
      }

      // Validate South direction structure if present
      if (compass.south && typeof compass.south === 'object') {
        const southSections = [
          'insuranceAnalysis',
          'emergencyFund',
          'riskManagement',
          'debtManagement',
          'creditProfile',
        ];

        southSections.forEach((section) => {
          if (compass.south[section] && typeof compass.south[section] !== 'object') {
            errors.push({
              path: `financialCompass.south.${section}`,
              message: `Invalid ${section} data: Expected an object`,
            });
          }
        });
      }

      // Validate West direction structure if present
      if (compass.west && typeof compass.west === 'object') {
        const westSections = [
          'estatePlanning',
          'estateDocuments',
          'charitableGiving',
          'taxPlanning',
          'taxResults',
          'valuesGoals',
          'legacyPlanning',
        ];

        westSections.forEach((section) => {
          if (compass.west[section] && typeof compass.west[section] !== 'object') {
            errors.push({
              path: `financialCompass.west.${section}`,
              message: `Invalid ${section} data: Expected an object`,
            });
          }
        });
      }
    }
  }

  // Validate Seasons of Self structure if present
  if (data.seasonsOfSelf) {
    const seasons = data.seasonsOfSelf;

    // Check if seasonsOfSelf is an object
    if (typeof seasons !== 'object' || Array.isArray(seasons)) {
      errors.push({
        path: 'seasonsOfSelf',
        message: 'Invalid Seasons of Self data: Expected an object',
      });
    } else {
      // Check for activeStage
      if (!seasons.activeStage) {
        errors.push({
          path: 'seasonsOfSelf.activeStage',
          message: 'Missing required field: activeStage',
        });
      } else if (typeof seasons.activeStage !== 'string') {
        errors.push({
          path: 'seasonsOfSelf.activeStage',
          message: 'Invalid activeStage: Expected a string',
          value: seasons.activeStage,
        });
      }

      // Check for stages object
      if (!seasons.stages) {
        errors.push({
          path: 'seasonsOfSelf.stages',
          message: 'Missing required field: stages',
        });
      } else if (typeof seasons.stages !== 'object' || Array.isArray(seasons.stages)) {
        errors.push({
          path: 'seasonsOfSelf.stages',
          message: 'Invalid stages: Expected an object',
        });
      } else {
        // Validate each stage if present
        const stageNames = [
          'springStage',
          'joyStage',
          'momentumStage',
          'pivotStage',
          'goalSeekingStage',
          'callingStage',
          'purposeStage',
          'fulfillmentStage',
        ];

        stageNames.forEach((stageName) => {
          const stage = seasons.stages[stageName];
          if (stage) {
            if (typeof stage !== 'object' || Array.isArray(stage)) {
              errors.push({
                path: `seasonsOfSelf.stages.${stageName}`,
                message: `Invalid ${stageName} data: Expected an object`,
              });
            } else {
              // Check required fields for each stage
              const requiredStageFields = ['completed', 'startDate'];
              requiredStageFields.forEach((field) => {
                if (stage[field] === undefined) {
                  errors.push({
                    path: `seasonsOfSelf.stages.${stageName}.${field}`,
                    message: `Missing required field: ${field}`,
                  });
                }
              });

              // Validate completed is a boolean
              if (stage.completed !== undefined && typeof stage.completed !== 'boolean') {
                errors.push({
                  path: `seasonsOfSelf.stages.${stageName}.completed`,
                  message: `Invalid completed: Expected a boolean`,
                  value: stage.completed,
                });
              }

              // Validate dates
              if (
                stage.startDate &&
                typeof stage.startDate === 'string' &&
                !/^\d{4}-\d{2}-\d{2}$/.test(stage.startDate)
              ) {
                errors.push({
                  path: `seasonsOfSelf.stages.${stageName}.startDate`,
                  message: `Invalid date format: Expected YYYY-MM-DD`,
                  value: stage.startDate,
                });
              }

              if (
                stage.endDate &&
                stage.endDate !== null &&
                typeof stage.endDate === 'string' &&
                !/^\d{4}-\d{2}-\d{2}$/.test(stage.endDate)
              ) {
                errors.push({
                  path: `seasonsOfSelf.stages.${stageName}.endDate`,
                  message: `Invalid date format: Expected YYYY-MM-DD or null`,
                  value: stage.endDate,
                });
              }

              // Validate keyEvents is an array if present
              if (stage.keyEvents && !Array.isArray(stage.keyEvents)) {
                errors.push({
                  path: `seasonsOfSelf.stages.${stageName}.keyEvents`,
                  message: `Invalid keyEvents: Expected an array`,
                });
              } else if (Array.isArray(stage.keyEvents)) {
                // Validate each key event
                stage.keyEvents.forEach((event, index) => {
                  if (typeof event !== 'object' || Array.isArray(event)) {
                    errors.push({
                      path: `seasonsOfSelf.stages.${stageName}.keyEvents[${index}]`,
                      message: `Invalid key event: Expected an object`,
                    });
                  } else {
                    // Check required fields for each key event
                    const requiredEventFields = ['id', 'date', 'title', 'description'];
                    requiredEventFields.forEach((field) => {
                      if (!event[field]) {
                        errors.push({
                          path: `seasonsOfSelf.stages.${stageName}.keyEvents[${index}].${field}`,
                          message: `Missing required field: ${field}`,
                        });
                      }
                    });

                    // Validate date format
                    if (
                      event.date &&
                      typeof event.date === 'string' &&
                      !/^\d{4}-\d{2}-\d{2}$/.test(event.date)
                    ) {
                      errors.push({
                        path: `seasonsOfSelf.stages.${stageName}.keyEvents[${index}].date`,
                        message: `Invalid date format: Expected YYYY-MM-DD`,
                        value: event.date,
                      });
                    }
                  }
                });
              }

              // Validate financialMilestones is an array if present
              if (stage.financialMilestones && !Array.isArray(stage.financialMilestones)) {
                errors.push({
                  path: `seasonsOfSelf.stages.${stageName}.financialMilestones`,
                  message: `Invalid financialMilestones: Expected an array`,
                });
              }
            }
          }
        });
      }
    }
  }

  // Validate user profiles if present
  if (data.userProfiles) {
    const profiles = data.userProfiles;

    // Check if userProfiles is an object
    if (typeof profiles !== 'object' || Array.isArray(profiles)) {
      errors.push({
        path: 'userProfiles',
        message: 'Invalid user profiles data: Expected an object',
      });
    } else {
      // Check for currentProfile
      if (!profiles.currentProfile) {
        errors.push({
          path: 'userProfiles.currentProfile',
          message: 'Missing required field: currentProfile',
        });
      } else if (typeof profiles.currentProfile !== 'string') {
        errors.push({
          path: 'userProfiles.currentProfile',
          message: 'Invalid currentProfile: Expected a string',
          value: profiles.currentProfile,
        });
      }

      // Check for profiles array
      if (!profiles.profiles) {
        errors.push({
          path: 'userProfiles.profiles',
          message: 'Missing required field: profiles',
        });
      } else if (!Array.isArray(profiles.profiles)) {
        errors.push({
          path: 'userProfiles.profiles',
          message: 'Invalid profiles: Expected an array',
        });
      } else {
        // Validate each profile
        profiles.profiles.forEach((profile, index) => {
          if (typeof profile !== 'object' || Array.isArray(profile)) {
            errors.push({
              path: `userProfiles.profiles[${index}]`,
              message: `Invalid profile: Expected an object`,
            });
          } else {
            // Check required fields for each profile
            const requiredProfileFields = ['id', 'name', 'email', 'isDefault'];
            requiredProfileFields.forEach((field) => {
              if (profile[field] === undefined) {
                errors.push({
                  path: `userProfiles.profiles[${index}].${field}`,
                  message: `Missing required field: ${field}`,
                });
              }
            });

            // Validate isDefault is a boolean
            if (profile.isDefault !== undefined && typeof profile.isDefault !== 'boolean') {
              errors.push({
                path: `userProfiles.profiles[${index}].isDefault`,
                message: `Invalid isDefault: Expected a boolean`,
                value: profile.isDefault,
              });
            }

            // Validate email format
            if (
              profile.email &&
              typeof profile.email === 'string' &&
              !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(profile.email)
            ) {
              errors.push({
                path: `userProfiles.profiles[${index}].email`,
                message: `Invalid email format`,
                value: profile.email,
              });
            }

            // Validate dates
            if (
              profile.createdAt &&
              typeof profile.createdAt === 'string' &&
              !/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z$/.test(profile.createdAt)
            ) {
              errors.push({
                path: `userProfiles.profiles[${index}].createdAt`,
                message: `Invalid date format: Expected ISO date string`,
                value: profile.createdAt,
              });
            }

            if (
              profile.lastUpdated &&
              typeof profile.lastUpdated === 'string' &&
              !/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z$/.test(profile.lastUpdated)
            ) {
              errors.push({
                path: `userProfiles.profiles[${index}].lastUpdated`,
                message: `Invalid date format: Expected ISO date string`,
                value: profile.lastUpdated,
              });
            }
          }
        });
      }
    }
  }

  return errors;
};

/**
 * Resolve conflicts between imported data and existing data
 *
 * @param importedData - The data being imported
 * @param existingData - The existing data
 * @param options - Import options
 * @returns The resolved data and a list of resolved conflicts
 */
export const resolveConflicts = (
  importedData: Record<string, any>,
  existingData: Record<string, any>,
  options: ImportOptions = {}
): {
  resolvedData: Record<string, any>;
  resolvedConflicts: {
    path: string;
    existingValue: any;
    importedValue: any;
    resolution: 'overwrite' | 'merge' | 'skip';
  }[];
} => {
  const { conflictResolution = 'merge' } = options;
  const resolvedConflicts: {
    path: string;
    existingValue: any;
    importedValue: any;
    resolution: 'overwrite' | 'merge' | 'skip';
  }[] = [];

  // Helper function to recursively merge objects
  const deepMerge = (imported: any, existing: any, path: string = ''): any => {
    // If either value is not an object, handle according to conflict resolution strategy
    if (
      typeof imported !== 'object' ||
      imported === null ||
      typeof existing !== 'object' ||
      existing === null ||
      Array.isArray(imported) ||
      Array.isArray(existing)
    ) {
      // If values are different, we have a conflict
      if (JSON.stringify(imported) !== JSON.stringify(existing)) {
        const resolution = conflictResolution;

        resolvedConflicts.push({
          path,
          existingValue: existing,
          importedValue: imported,
          resolution,
        });

        // Return the appropriate value based on the resolution strategy
        switch (resolution) {
          case 'overwrite':
            return imported;
          case 'skip':
            return existing;
          case 'merge':
          default:
            // For non-objects, 'merge' is the same as 'overwrite'
            return imported;
        }
      }

      // No conflict, return the imported value
      return imported;
    }

    // Both values are objects, merge them
    const result: Record<string, any> = { ...existing };

    // Process all keys in the imported object
    Object.keys(imported).forEach((key) => {
      const newPath = path ? `${path}.${key}` : key;

      if (key in existing) {
        // Key exists in both objects, recursively merge
        result[key] = deepMerge(imported[key], existing[key], newPath);
      } else {
        // Key only exists in imported object, add it
        result[key] = imported[key];
      }
    });

    return result;
  };

  // Merge the data
  const resolvedData = deepMerge(importedData, existingData);

  return { resolvedData, resolvedConflicts };
};

/**
 * Import and process data with validation and conflict resolution
 *
 * @param file - The JSON file to import
 * @param existingData - The existing data
 * @param options - Import options
 * @returns A Promise that resolves with the import result
 */
export const importAndProcessData = async (
  file: File,
  existingData: Record<string, any>,
  options: ImportOptions = {}
): Promise<ImportResult> => {
  try {
    console.log('Starting import process...');

    // Import data from file
    const importedData = await importFromJson(file);
    console.log('Data imported from file successfully');

    // Log imported data structure (without sensitive values)
    console.log('Imported data structure:', Object.keys(importedData));
    if (importedData.financialCompass) {
      console.log('Financial Compass directions:', Object.keys(importedData.financialCompass));
    }
    if (importedData.seasonsOfSelf) {
      console.log('Seasons of Self seasons:', Object.keys(importedData.seasonsOfSelf));
    }

    // Validate data structure if requested
    let validationErrors: ValidationError[] = [];
    if (options.validate !== false) {
      console.log('Validating data structure...');
      validationErrors = validateDataStructure(importedData);

      // Call validation error callback if provided
      if (options.onValidationError && validationErrors.length > 0) {
        console.log('Validation errors found, calling error callback');
        options.onValidationError(validationErrors);
      }

      // If there are validation errors and they are critical, return failure
      if (validationErrors.some((error) => error.path === '')) {
        console.error('Critical validation errors found, aborting import');
        return {
          data: existingData,
          validationErrors,
          success: false,
          resolvedConflicts: [],
        };
      }

      if (validationErrors.length > 0) {
        console.warn(`Found ${validationErrors.length} non-critical validation issues`);
      } else {
        console.log('Data validation successful');
      }
    }

    // Resolve conflicts between imported and existing data
    console.log('Resolving conflicts between imported and existing data...');
    const { resolvedData, resolvedConflicts } = resolveConflicts(
      importedData,
      existingData,
      options
    );

    if (resolvedConflicts.length > 0) {
      console.log(`Resolved ${resolvedConflicts.length} conflicts`);
    } else {
      console.log('No conflicts found');
    }

    // Save the imported data to localStorage
    try {
      console.log('Saving imported data to localStorage...');

      // Save Financial Compass data if present
      if (resolvedData.financialCompass) {
        localStorage.setItem(
          'lifecompass_financial_compass',
          JSON.stringify(resolvedData.financialCompass)
        );
      }

      // Save Seasons of Self data if present
      if (resolvedData.seasonsOfSelf) {
        localStorage.setItem(
          'lifecompass_seasons_of_self',
          JSON.stringify(resolvedData.seasonsOfSelf)
        );
      }

      // Save user profiles data if present
      if (resolvedData.userProfiles) {
        localStorage.setItem('lifecompass_profiles', JSON.stringify(resolvedData.userProfiles));
      }

      console.log('Data saved to localStorage successfully');

      // Update completion status based on imported data
      updateCompletionStatus(resolvedData);
    } catch (saveError) {
      console.error('Error saving imported data to localStorage:', saveError);
      validationErrors.push({
        path: '',
        message: 'Failed to save imported data to localStorage',
      });
    }

    console.log('Import process completed successfully');
    return {
      data: resolvedData,
      validationErrors,
      success: true,
      resolvedConflicts,
    };
  } catch (error) {
    console.error('Error importing and processing data:', error);

    return {
      data: existingData,
      validationErrors: [
        {
          path: '',
          message: error instanceof Error ? error.message : 'Unknown error',
        },
      ],
      success: false,
      resolvedConflicts: [],
    };
  }
};

/**
 * Update completion status in localStorage based on imported data
 *
 * This function analyzes the imported data and updates the completion status
 * of steps in the Guided Journey based on the presence of data in each section.
 *
 * @param importedData - The imported data
 */
export const updateCompletionStatus = (importedData: Record<string, any>): void => {
  try {
    console.log('Updating completion status based on imported data...');

    // Get existing subFeatures from Financial Compass
    const financialCompassData = localStorage.getItem('lifecompass_financial_compass');
    if (financialCompassData && importedData.financialCompass) {
      const importedCompass = importedData.financialCompass;

      // Create a map to track which sections have data
      const completedSections: Record<string, boolean> = {};

      // Check North direction sections
      if (importedCompass.north) {
        if (
          importedCompass.north.personalInformation &&
          Object.keys(importedCompass.north.personalInformation).length > 3
        ) {
          completedSections['personal_information'] = true;
        }

        if (
          importedCompass.north.familyInformation &&
          Object.keys(importedCompass.north.familyInformation).length > 1
        ) {
          completedSections['family_information'] = true;
        }

        if (
          importedCompass.north.incomeDetails &&
          Object.keys(importedCompass.north.incomeDetails).length > 2
        ) {
          completedSections['income_details'] = true;
        }

        if (
          importedCompass.north.expenseDetails &&
          Object.keys(importedCompass.north.expenseDetails).length > 1
        ) {
          completedSections['expense_details'] = true;
        }

        if (importedCompass.north.assets && Object.keys(importedCompass.north.assets).length > 0) {
          completedSections['assets_investments'] = true;
        }

        if (
          importedCompass.north.liabilities &&
          Object.keys(importedCompass.north.liabilities).length > 0
        ) {
          completedSections['liabilities_debt'] = true;
        }

        if (
          importedCompass.north.netWorthDetails ||
          (importedCompass.north.assets && importedCompass.north.liabilities)
        ) {
          completedSections['cash_flow_analysis'] = true;
        }
      }

      // Check East direction sections
      if (importedCompass.east) {
        if (
          importedCompass.east.retirementGoals &&
          Object.keys(importedCompass.east.retirementGoals).length > 2
        ) {
          completedSections['retirement_goals'] = true;
        }

        if (
          importedCompass.east.retirementIncome &&
          Object.keys(importedCompass.east.retirementIncome).length > 1
        ) {
          completedSections['retirement_income'] = true;
        }

        if (
          importedCompass.east.retirementExpenses &&
          Object.keys(importedCompass.east.retirementExpenses).length > 1
        ) {
          completedSections['retirement_expenses'] = true;
        }

        if (
          importedCompass.east.retirementTimeline &&
          Object.keys(importedCompass.east.retirementTimeline).length > 1
        ) {
          completedSections['retirement_timeline'] = true;
        }

        if (
          importedCompass.east.socialSecurityPlanning &&
          Object.keys(importedCompass.east.socialSecurityPlanning).length > 1
        ) {
          completedSections['social_security_planning'] = true;
        }
      }

      // Check South direction sections
      if (importedCompass.south) {
        if (importedCompass.south.insuranceAnalysis || importedCompass.south.insuranceCoverage) {
          completedSections['insurance_analysis'] = true;
        }

        if (importedCompass.south.emergencyFund) {
          completedSections['emergency_fund'] = true;
        }

        if (importedCompass.south.riskManagement || importedCompass.south.riskTolerance) {
          completedSections['risk_management'] = true;
        }

        if (importedCompass.south.debtManagement) {
          completedSections['debt_management'] = true;
        }

        if (importedCompass.south.creditProfile) {
          completedSections['credit_profile'] = true;
        }
      }

      // Check West direction sections
      if (importedCompass.west) {
        if (importedCompass.west.estatePlanning) {
          completedSections['estate_planning'] = true;
        }

        if (importedCompass.west.estateDocuments) {
          completedSections['estate_documents'] = true;
        }

        if (importedCompass.west.charitableGiving) {
          completedSections['charitable_giving'] = true;
        }

        if (importedCompass.west.taxPlanning) {
          completedSections['tax_planning'] = true;
        }

        if (importedCompass.west.valuesGoals) {
          completedSections['values_goals'] = true;
        }

        if (importedCompass.west.legacyPlanning) {
          completedSections['legacy_planning'] = true;
        }
      }

      // Get existing subFeatures
      const subFeaturesStr = localStorage.getItem('lifecompass_subfeatures');
      if (subFeaturesStr) {
        try {
          const subFeatures = JSON.parse(subFeaturesStr);

          // Update completion status based on completed sections
          const updatedSubFeatures = subFeatures.map((feature: any) => {
            // Extract the section ID from the feature ID
            const sectionId = feature.id.replace('compass_', '');

            // Update completion status if this section has data
            if (completedSections[sectionId]) {
              return { ...feature, completed: true };
            }

            return feature;
          });

          // Save updated subFeatures
          localStorage.setItem('lifecompass_subfeatures', JSON.stringify(updatedSubFeatures));
          console.log('Updated subFeatures completion status in localStorage');
        } catch (error) {
          console.error('Error updating subFeatures:', error);
        }
      }

      // Update direction sections completion status
      ['north', 'east', 'south', 'west'].forEach((direction) => {
        const sectionsKey = `lifecompass_${direction}_sections`;
        const sectionsStr = localStorage.getItem(sectionsKey);

        if (sectionsStr) {
          try {
            const sections = JSON.parse(sectionsStr);

            // Update completion status based on completed sections
            const updatedSections = sections.map((section: any) => {
              if (completedSections[section.id]) {
                return { ...section, isCompleted: true };
              }
              return section;
            });

            // Save updated sections
            localStorage.setItem(sectionsKey, JSON.stringify(updatedSections));
            console.log(`Updated ${direction} sections completion status in localStorage`);
          } catch (error) {
            console.error(`Error updating ${direction} sections:`, error);
          }
        }
      });
    }

    // Update Seasons of Self completion status if needed
    if (importedData.seasonsOfSelf) {
      // Try to get existing stages from localStorage
      let stagesStr = localStorage.getItem('lifecompass_stages');
      let stages;
      if (stagesStr) {
        try {
          stages = JSON.parse(stagesStr);
        } catch (error) {
          stages = undefined;
        }
      }
      // If no existing stages, initialize from imported data
      if (!stages) {
        // Use the defaultStages structure, but set 'completed' from importedData.seasonsOfSelf.stages
        const defaultStages = [
          {
            id: 'pleasure',
            name: 'Pleasure',
            season: 'spring',
            description: '',
            completed: false,
            score: 0,
            path: '',
          },
          {
            id: 'happiness',
            name: 'Happiness',
            season: 'spring',
            description: '',
            completed: false,
            score: 0,
            path: '',
          },
          {
            id: 'joy',
            name: 'Joy',
            season: 'summer',
            description: '',
            completed: false,
            score: 0,
            path: '',
          },
          {
            id: 'momentum',
            name: 'Momentum',
            season: 'summer',
            description: '',
            completed: false,
            score: 0,
            path: '',
          },
          {
            id: 'pivot',
            name: 'Pivot',
            season: 'autumn',
            description: '',
            completed: false,
            score: 0,
            path: '',
          },
          {
            id: 'goal_seeking',
            name: 'Goal Seeking',
            season: 'autumn',
            description: '',
            completed: false,
            score: 0,
            path: '',
          },
          {
            id: 'calling',
            name: 'Calling',
            season: 'winter',
            description: '',
            completed: false,
            score: 0,
            path: '',
          },
          {
            id: 'purpose',
            name: 'Purpose',
            season: 'winter',
            description: '',
            completed: false,
            score: 0,
            path: '',
          },
          {
            id: 'fulfillment',
            name: 'Fulfillment',
            season: 'winter',
            description: '',
            completed: false,
            score: 0,
            path: '',
          },
        ];
        const importedStagesObj = importedData.seasonsOfSelf.stages || {};
        const mappedStages = defaultStages.map((stage) => {
          // Map imported stage keys to default stage ids
          const keyMap = {
            pleasure: 'springStage',
            happiness: 'happinessStage',
            joy: 'joyStage',
            momentum: 'momentumStage',
            pivot: 'pivotStage',
            goal_seeking: 'goalSeekingStage',
            calling: 'callingStage',
            purpose: 'purposeStage',
            fulfillment: 'fulfillmentStage',
          };
          const importedKey = keyMap[stage.id];
          const importedStage = importedStagesObj[importedKey];
          return importedStage ? { ...stage, completed: !!importedStage.completed } : stage;
        });
        localStorage.setItem('lifecompass_stages', JSON.stringify(mappedStages));
        stages = mappedStages;
      } else {
        // Mark stages as completed if corresponding data exists in importedData.seasonsOfSelf
        const updatedStages = stages.map((stage) => {
          // Heuristic: if the imported season data for this stage's season has any keys, mark as completed
          const seasonData = importedData.seasonsOfSelf[stage.season];
          if (seasonData && Object.keys(seasonData).length > 0) {
            return { ...stage, completed: true };
          }
          return stage;
        });
        localStorage.setItem('lifecompass_stages', JSON.stringify(updatedStages));
      }
      console.log('Updated stages completion status in localStorage');
    }

    console.log('Completion status update completed');
  } catch (error) {
    console.error('Error updating completion status:', error);
  }
};

/**
 * Data Import Service
 */
const dataImportService = {
  importFromJson,
  validateImportedData,
  validateDataStructure,
  resolveConflicts,
  importAndProcessData,
  updateCompletionStatus,
};

export default dataImportService;
