/**
 * Connection Service
 *
 * This service handles connections to financial institutions.
 */

/**
 * Financial institution information
 */
export interface FinancialInstitution {
  id: string;
  name: string;
  logo: string;
  type: 'bank' | 'investment' | 'credit' | 'other';
  products: string[];
}

/**
 * Connection status
 */
export type ConnectionStatus = 'connected' | 'disconnected' | 'connecting' | 'error';

/**
 * Connection information
 */
export interface Connection {
  id: string;
  institutionId: string;
  status: ConnectionStatus;
  lastUpdated: string;
  error?: string;
}

/**
 * Mock financial institutions data
 */
const mockInstitutions: FinancialInstitution[] = [
  {
    id: 'inst_1',
    name: 'Example Bank',
    logo: 'https://via.placeholder.com/150',
    type: 'bank',
    products: ['checking', 'savings', 'credit_cards'],
  },
  {
    id: 'inst_2',
    name: 'Investment Co.',
    logo: 'https://via.placeholder.com/150',
    type: 'investment',
    products: ['investments', 'retirement'],
  },
  {
    id: 'inst_3',
    name: 'Credit Union',
    logo: 'https://via.placeholder.com/150',
    type: 'bank',
    products: ['checking', 'savings', 'loans'],
  },
];

/**
 * Get a list of supported financial institutions
 *
 * @returns A Promise that resolves with the list of institutions
 */
export const getInstitutions = async (): Promise<FinancialInstitution[]> => {
  try {
    // In a real implementation, this would fetch from an API
    // For now, we'll return mock data
    return mockInstitutions;
  } catch (error) {
    console.error('Error getting institutions:', error);
    throw new Error('Failed to get institutions');
  }
};

/**
 * Connect to a financial institution
 *
 * @param institutionId - The ID of the institution to connect to
 * @param credentials - The credentials to use for the connection
 * @returns A Promise that resolves with the connection information
 */
export const connectToInstitution = async (
  institutionId: string,
  credentials: Record<string, string>
): Promise<Connection> => {
  try {
    // In a real implementation, this would use OAuth or a secure API
    // For now, we'll simulate a connection

    console.log(`Connecting to institution ${institutionId} with credentials:`, credentials);

    // Simulate a delay for the connection process
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Create a new connection
    const connection: Connection = {
      id: `conn_${Date.now()}`,
      institutionId,
      status: 'connected',
      lastUpdated: new Date().toISOString(),
    };

    // Store the connection in localStorage
    const connections = JSON.parse(localStorage.getItem('financialConnections') || '[]');
    connections.push(connection);
    localStorage.setItem('financialConnections', JSON.stringify(connections));

    return connection;
  } catch (error) {
    console.error('Error connecting to institution:', error);
    throw new Error('Failed to connect to institution');
  }
};

/**
 * Disconnect from a financial institution
 *
 * @param connectionId - The ID of the connection to disconnect
 * @returns A Promise that resolves when the disconnection is complete
 */
export const disconnectFromInstitution = async (connectionId: string): Promise<void> => {
  try {
    // In a real implementation, this would use an API to revoke access
    // For now, we'll simulate a disconnection

    console.log(`Disconnecting from connection ${connectionId}`);

    // Simulate a delay for the disconnection process
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Remove the connection from localStorage
    const connections = JSON.parse(localStorage.getItem('financialConnections') || '[]');
    const updatedConnections = connections.filter((conn: Connection) => conn.id !== connectionId);
    localStorage.setItem('financialConnections', JSON.stringify(updatedConnections));
  } catch (error) {
    console.error('Error disconnecting from institution:', error);
    throw new Error('Failed to disconnect from institution');
  }
};

/**
 * Get all connections
 *
 * @returns A Promise that resolves with the list of connections
 */
export const getConnections = async (): Promise<Connection[]> => {
  try {
    // Get connections from localStorage
    const connections = JSON.parse(localStorage.getItem('financialConnections') || '[]');
    return connections;
  } catch (error) {
    console.error('Error getting connections:', error);
    throw new Error('Failed to get connections');
  }
};

/**
 * Connection Service
 */
const connectionService = {
  getInstitutions,
  connectToInstitution,
  disconnectFromInstitution,
  getConnections,
};

export default connectionService;
