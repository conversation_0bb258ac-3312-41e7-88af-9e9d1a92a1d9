/**
 * Transaction Service
 *
 * This service handles financial transactions from connected accounts.
 */

/**
 * Transaction category
 */
export type TransactionCategory =
  | 'income'
  | 'transfer'
  | 'food_and_drink'
  | 'shopping'
  | 'housing'
  | 'transportation'
  | 'travel'
  | 'entertainment'
  | 'health'
  | 'education'
  | 'personal_care'
  | 'utilities'
  | 'fees_and_charges'
  | 'other';

/**
 * Transaction information
 */
export interface Transaction {
  id: string;
  accountId: string;
  date: string;
  amount: number;
  description: string;
  category: TransactionCategory;
  pending: boolean;
  merchant?: {
    name: string;
    logoUrl?: string;
  };
  location?: {
    address?: string;
    city?: string;
    region?: string;
    postalCode?: string;
    country?: string;
    lat?: number;
    lon?: number;
  };
}

/**
 * Mock transactions data
 */
const mockTransactions: Transaction[] = [
  {
    id: 'txn_1',
    accountId: 'acc_1',
    date: '2023-05-15T10:30:00Z',
    amount: -45.67,
    description: 'Grocery Store',
    category: 'food_and_drink',
    pending: false,
    merchant: {
      name: 'Whole Foods',
    },
    location: {
      city: 'San Francisco',
      region: 'CA',
      country: 'US',
    },
  },
  {
    id: 'txn_2',
    accountId: 'acc_1',
    date: '2023-05-14T08:15:00Z',
    amount: -25.0,
    description: 'Coffee Shop',
    category: 'food_and_drink',
    pending: false,
    merchant: {
      name: 'Starbucks',
    },
  },
  {
    id: 'txn_3',
    accountId: 'acc_1',
    date: '2023-05-13T14:45:00Z',
    amount: -120.5,
    description: 'Clothing Store',
    category: 'shopping',
    pending: false,
    merchant: {
      name: 'Nordstrom',
    },
  },
  {
    id: 'txn_4',
    accountId: 'acc_2',
    date: '2023-05-10T11:30:00Z',
    amount: 1000.0,
    description: 'Deposit',
    category: 'income',
    pending: false,
  },
  {
    id: 'txn_5',
    accountId: 'acc_3',
    date: '2023-05-05T09:00:00Z',
    amount: -500.0,
    description: 'Investment Purchase',
    category: 'transfer',
    pending: false,
  },
];

/**
 * Get transactions for an account
 *
 * @param accountId - The ID of the account
 * @param options - Options for filtering transactions
 * @returns A Promise that resolves with the list of transactions
 */
export const getTransactionsByAccount = async (
  accountId: string,
  options?: {
    startDate?: string;
    endDate?: string;
    limit?: number;
    offset?: number;
  }
): Promise<Transaction[]> => {
  try {
    // In a real implementation, this would fetch from an API
    // For now, we'll filter mock data
    let transactions = mockTransactions.filter((txn) => txn.accountId === accountId);

    // Apply date filters if provided
    if (options?.startDate) {
      transactions = transactions.filter((txn) => txn.date >= options.startDate!);
    }

    if (options?.endDate) {
      transactions = transactions.filter((txn) => txn.date <= options.endDate!);
    }

    // Apply pagination if provided
    if (options?.limit) {
      const offset = options.offset || 0;
      transactions = transactions.slice(offset, offset + options.limit);
    }

    return transactions;
  } catch (error) {
    console.error('Error getting transactions:', error);
    throw new Error('Failed to get transactions');
  }
};

/**
 * Get transaction by ID
 *
 * @param transactionId - The ID of the transaction
 * @returns A Promise that resolves with the transaction information
 */
export const getTransactionById = async (transactionId: string): Promise<Transaction | null> => {
  try {
    // In a real implementation, this would fetch from an API
    // For now, we'll find the transaction in mock data
    const transaction = mockTransactions.find((txn) => txn.id === transactionId);
    return transaction || null;
  } catch (error) {
    console.error('Error getting transaction:', error);
    throw new Error('Failed to get transaction');
  }
};

/**
 * Get transactions by category
 *
 * @param category - The transaction category
 * @param options - Options for filtering transactions
 * @returns A Promise that resolves with the list of transactions
 */
export const getTransactionsByCategory = async (
  category: TransactionCategory,
  options?: {
    startDate?: string;
    endDate?: string;
    limit?: number;
    offset?: number;
  }
): Promise<Transaction[]> => {
  try {
    // In a real implementation, this would fetch from an API
    // For now, we'll filter mock data
    let transactions = mockTransactions.filter((txn) => txn.category === category);

    // Apply date filters if provided
    if (options?.startDate) {
      transactions = transactions.filter((txn) => txn.date >= options.startDate!);
    }

    if (options?.endDate) {
      transactions = transactions.filter((txn) => txn.date <= options.endDate!);
    }

    // Apply pagination if provided
    if (options?.limit) {
      const offset = options.offset || 0;
      transactions = transactions.slice(offset, offset + options.limit);
    }

    return transactions;
  } catch (error) {
    console.error('Error getting transactions by category:', error);
    throw new Error('Failed to get transactions by category');
  }
};

/**
 * Transaction Service
 */
const transactionService = {
  getTransactionsByAccount,
  getTransactionById,
  getTransactionsByCategory,
};

export default transactionService;
