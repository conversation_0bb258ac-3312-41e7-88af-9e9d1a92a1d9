/**
 * Account Service
 *
 * This service handles financial accounts from connected institutions.
 */

/**
 * Account type
 */
export type AccountType =
  | 'checking'
  | 'savings'
  | 'credit_card'
  | 'investment'
  | 'retirement'
  | 'loan'
  | 'mortgage'
  | 'other';

/**
 * Account information
 */
export interface Account {
  id: string;
  connectionId: string;
  institutionId: string;
  name: string;
  type: AccountType;
  subtype?: string;
  mask?: string;
  balance: {
    current: number;
    available?: number;
    limit?: number;
  };
  currency: string;
  lastUpdated: string;
}

/**
 * Mock accounts data
 */
const mockAccounts: Account[] = [
  {
    id: 'acc_1',
    connectionId: 'conn_1',
    institutionId: 'inst_1',
    name: 'Checking Account',
    type: 'checking',
    mask: '1234',
    balance: {
      current: 2500.75,
      available: 2300.5,
    },
    currency: 'USD',
    lastUpdated: new Date().toISOString(),
  },
  {
    id: 'acc_2',
    connectionId: 'conn_1',
    institutionId: 'inst_1',
    name: 'Savings Account',
    type: 'savings',
    mask: '5678',
    balance: {
      current: 15000.25,
      available: 15000.25,
    },
    currency: 'USD',
    lastUpdated: new Date().toISOString(),
  },
  {
    id: 'acc_3',
    connectionId: 'conn_2',
    institutionId: 'inst_2',
    name: 'Investment Account',
    type: 'investment',
    balance: {
      current: 45000.0,
    },
    currency: 'USD',
    lastUpdated: new Date().toISOString(),
  },
];

/**
 * Get accounts for a connection
 *
 * @param connectionId - The ID of the connection
 * @returns A Promise that resolves with the list of accounts
 */
export const getAccountsByConnection = async (connectionId: string): Promise<Account[]> => {
  try {
    // In a real implementation, this would fetch from an API
    // For now, we'll return mock data filtered by connectionId
    return mockAccounts.filter((account) => account.connectionId === connectionId);
  } catch (error) {
    console.error('Error getting accounts:', error);
    throw new Error('Failed to get accounts');
  }
};

/**
 * Get all accounts
 *
 * @returns A Promise that resolves with the list of all accounts
 */
export const getAllAccounts = async (): Promise<Account[]> => {
  try {
    // In a real implementation, this would fetch from an API
    // For now, we'll return all mock data
    return mockAccounts;
  } catch (error) {
    console.error('Error getting all accounts:', error);
    throw new Error('Failed to get all accounts');
  }
};

/**
 * Get account by ID
 *
 * @param accountId - The ID of the account
 * @returns A Promise that resolves with the account information
 */
export const getAccountById = async (accountId: string): Promise<Account | null> => {
  try {
    // In a real implementation, this would fetch from an API
    // For now, we'll find the account in mock data
    const account = mockAccounts.find((acc) => acc.id === accountId);
    return account || null;
  } catch (error) {
    console.error('Error getting account:', error);
    throw new Error('Failed to get account');
  }
};

/**
 * Refresh account data
 *
 * @param accountId - The ID of the account to refresh
 * @returns A Promise that resolves when the refresh is complete
 */
export const refreshAccount = async (accountId: string): Promise<void> => {
  try {
    // In a real implementation, this would fetch updated data from an API
    // For now, we'll simulate a refresh
    console.log(`Refreshing account ${accountId}`);

    // Simulate a delay for the refresh process
    await new Promise((resolve) => setTimeout(resolve, 1500));

    console.log(`Account ${accountId} refreshed`);
  } catch (error) {
    console.error('Error refreshing account:', error);
    throw new Error('Failed to refresh account');
  }
};

/**
 * Account Service
 */
const accountService = {
  getAccountsByConnection,
  getAllAccounts,
  getAccountById,
  refreshAccount,
};

export default accountService;
