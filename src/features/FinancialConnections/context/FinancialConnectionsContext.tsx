/**
 * Financial Connections Context
 *
 * This context provides state and functions for financial institution connections.
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import connectionService, { Connection, FinancialInstitution } from '../services/connectionService';
import accountService, { Account } from '../services/accountService';
import transactionService, { Transaction } from '../services/transactionService';

/**
 * Connection status type
 */
export type ConnectionStatus = 'idle' | 'connecting' | 'refreshing' | 'disconnecting' | 'error';

/**
 * Financial connections context type
 */
interface FinancialConnectionsContextType {
  status: ConnectionStatus;
  error: string | null;
  institutions: FinancialInstitution[];
  connections: Connection[];
  accounts: Account[];
  selectedConnection: Connection | null;
  selectedAccount: Account | null;
  transactions: Transaction[];
  loadInstitutions: () => Promise<void>;
  connectToInstitution: (
    institutionId: string,
    credentials: Record<string, string>
  ) => Promise<Connection>;
  disconnectFromInstitution: (connectionId: string) => Promise<void>;
  loadConnections: () => Promise<void>;
  selectConnection: (connectionId: string | null) => Promise<void>;
  loadAccounts: (connectionId: string) => Promise<void>;
  selectAccount: (accountId: string | null) => Promise<void>;
  loadTransactions: (
    accountId: string,
    options?: { startDate?: string; endDate?: string }
  ) => Promise<void>;
  refreshConnection: (connectionId: string) => Promise<void>;
}

/**
 * Financial connections context
 */
const FinancialConnectionsContext = createContext<FinancialConnectionsContextType | undefined>(
  undefined
);

/**
 * Financial connections provider props
 */
interface FinancialConnectionsProviderProps {
  children: ReactNode;
}

/**
 * Financial connections provider
 */
export const FinancialConnectionsProvider: React.FC<FinancialConnectionsProviderProps> = ({
  children,
}) => {
  // State
  const [status, setStatus] = useState<ConnectionStatus>('idle');
  const [error, setError] = useState<string | null>(null);
  const [institutions, setInstitutions] = useState<FinancialInstitution[]>([]);
  const [connections, setConnections] = useState<Connection[]>([]);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [selectedConnection, setSelectedConnection] = useState<Connection | null>(null);
  const [selectedAccount, setSelectedAccount] = useState<Account | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);

  // Load connections on mount
  useEffect(() => {
    loadConnections();
  }, []);

  /**
   * Load financial institutions
   */
  const loadInstitutions = async (): Promise<void> => {
    try {
      setStatus('idle');
      setError(null);

      const institutionsData = await connectionService.getInstitutions();
      setInstitutions(institutionsData);
    } catch (error) {
      console.error('Error loading institutions:', error);
      setStatus('error');
      setError(error instanceof Error ? error.message : 'Unknown error');
    }
  };

  /**
   * Connect to a financial institution
   */
  const connectToInstitution = async (
    institutionId: string,
    credentials: Record<string, string>
  ): Promise<Connection> => {
    try {
      setStatus('connecting');
      setError(null);

      const connection = await connectionService.connectToInstitution(institutionId, credentials);

      // Refresh connections list
      await loadConnections();

      setStatus('idle');
      return connection;
    } catch (error) {
      console.error('Error connecting to institution:', error);
      setStatus('error');
      setError(error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  };

  /**
   * Disconnect from a financial institution
   */
  const disconnectFromInstitution = async (connectionId: string): Promise<void> => {
    try {
      setStatus('disconnecting');
      setError(null);

      await connectionService.disconnectFromInstitution(connectionId);

      // Refresh connections list
      await loadConnections();

      // Clear selected connection if it was disconnected
      if (selectedConnection?.id === connectionId) {
        setSelectedConnection(null);
        setAccounts([]);
        setSelectedAccount(null);
        setTransactions([]);
      }

      setStatus('idle');
    } catch (error) {
      console.error('Error disconnecting from institution:', error);
      setStatus('error');
      setError(error instanceof Error ? error.message : 'Unknown error');
    }
  };

  /**
   * Load connections
   */
  const loadConnections = async (): Promise<void> => {
    try {
      setStatus('idle');
      setError(null);

      const connectionsData = await connectionService.getConnections();
      setConnections(connectionsData);
    } catch (error) {
      console.error('Error loading connections:', error);
      setStatus('error');
      setError(error instanceof Error ? error.message : 'Unknown error');
    }
  };

  /**
   * Select a connection
   */
  const selectConnection = async (connectionId: string | null): Promise<void> => {
    try {
      setStatus('idle');
      setError(null);

      if (!connectionId) {
        setSelectedConnection(null);
        setAccounts([]);
        setSelectedAccount(null);
        setTransactions([]);
        return;
      }

      const connection = connections.find((conn) => conn.id === connectionId);

      if (!connection) {
        throw new Error(`Connection with ID ${connectionId} not found`);
      }

      setSelectedConnection(connection);

      // Load accounts for the selected connection
      await loadAccounts(connectionId);
    } catch (error) {
      console.error('Error selecting connection:', error);
      setStatus('error');
      setError(error instanceof Error ? error.message : 'Unknown error');
    }
  };

  /**
   * Load accounts for a connection
   */
  const loadAccounts = async (connectionId: string): Promise<void> => {
    try {
      setStatus('idle');
      setError(null);

      const accountsData = await accountService.getAccountsByConnection(connectionId);
      setAccounts(accountsData);

      // Clear selected account
      setSelectedAccount(null);
      setTransactions([]);
    } catch (error) {
      console.error('Error loading accounts:', error);
      setStatus('error');
      setError(error instanceof Error ? error.message : 'Unknown error');
    }
  };

  /**
   * Select an account
   */
  const selectAccount = async (accountId: string | null): Promise<void> => {
    try {
      setStatus('idle');
      setError(null);

      if (!accountId) {
        setSelectedAccount(null);
        setTransactions([]);
        return;
      }

      const account = accounts.find((acc) => acc.id === accountId);

      if (!account) {
        throw new Error(`Account with ID ${accountId} not found`);
      }

      setSelectedAccount(account);

      // Load transactions for the selected account
      await loadTransactions(accountId);
    } catch (error) {
      console.error('Error selecting account:', error);
      setStatus('error');
      setError(error instanceof Error ? error.message : 'Unknown error');
    }
  };

  /**
   * Load transactions for an account
   */
  const loadTransactions = async (
    accountId: string,
    options?: { startDate?: string; endDate?: string }
  ): Promise<void> => {
    try {
      setStatus('idle');
      setError(null);

      const transactionsData = await transactionService.getTransactionsByAccount(
        accountId,
        options
      );
      setTransactions(transactionsData);
    } catch (error) {
      console.error('Error loading transactions:', error);
      setStatus('error');
      setError(error instanceof Error ? error.message : 'Unknown error');
    }
  };

  /**
   * Refresh a connection
   */
  const refreshConnection = async (connectionId: string): Promise<void> => {
    try {
      setStatus('refreshing');
      setError(null);

      // In a real implementation, this would refresh the connection data from the financial institution
      // For now, we'll just simulate a refresh
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Refresh connections list
      await loadConnections();

      // Refresh accounts if this is the selected connection
      if (selectedConnection?.id === connectionId) {
        await loadAccounts(connectionId);
      }

      setStatus('idle');
    } catch (error) {
      console.error('Error refreshing connection:', error);
      setStatus('error');
      setError(error instanceof Error ? error.message : 'Unknown error');
    }
  };

  // Context value
  const value: FinancialConnectionsContextType = {
    status,
    error,
    institutions,
    connections,
    accounts,
    selectedConnection,
    selectedAccount,
    transactions,
    loadInstitutions,
    connectToInstitution,
    disconnectFromInstitution,
    loadConnections,
    selectConnection,
    loadAccounts,
    selectAccount,
    loadTransactions,
    refreshConnection,
  };

  return (
    <FinancialConnectionsContext.Provider value={value}>
      {children}
    </FinancialConnectionsContext.Provider>
  );
};

/**
 * Hook to use the financial connections context
 */
export const useFinancialConnections = (): FinancialConnectionsContextType => {
  const context = useContext(FinancialConnectionsContext);

  if (context === undefined) {
    throw new Error('useFinancialConnections must be used within a FinancialConnectionsProvider');
  }

  return context;
};
