/**
 * Financial Institution Selector Component
 *
 * This component allows users to search for and select financial institutions
 * to connect to their LifeCompass account.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from '../../../theme/SimpleThemeProvider';

// Types
export interface FinancialInstitution {
  id: string;
  name: string;
  logo: string;
  type: 'bank' | 'investment' | 'credit' | 'other';
  connectionStatus?: 'connected' | 'disconnected' | 'connecting' | 'error';
  lastUpdated?: string;
}

interface FinancialInstitutionSelectorProps {
  onSelect: (institution: FinancialInstitution) => void;
  onCancel: () => void;
  recentInstitutions?: FinancialInstitution[];
}

// Mock data for financial institutions
const mockInstitutions: FinancialInstitution[] = [
  {
    id: 'chase',
    name: 'Chase Bank',
    logo: 'https://logo.clearbit.com/chase.com',
    type: 'bank',
  },
  {
    id: 'bofa',
    name: 'Bank of America',
    logo: 'https://logo.clearbit.com/bankofamerica.com',
    type: 'bank',
  },
  {
    id: 'wells',
    name: 'Wells Fargo',
    logo: 'https://logo.clearbit.com/wellsfargo.com',
    type: 'bank',
  },
  {
    id: 'citi',
    name: 'Citibank',
    logo: 'https://logo.clearbit.com/citi.com',
    type: 'bank',
  },
  {
    id: 'fidelity',
    name: 'Fidelity Investments',
    logo: 'https://logo.clearbit.com/fidelity.com',
    type: 'investment',
  },
  {
    id: 'vanguard',
    name: 'Vanguard',
    logo: 'https://logo.clearbit.com/vanguard.com',
    type: 'investment',
  },
  {
    id: 'schwab',
    name: 'Charles Schwab',
    logo: 'https://logo.clearbit.com/schwab.com',
    type: 'investment',
  },
  {
    id: 'amex',
    name: 'American Express',
    logo: 'https://logo.clearbit.com/americanexpress.com',
    type: 'credit',
  },
  {
    id: 'discover',
    name: 'Discover',
    logo: 'https://logo.clearbit.com/discover.com',
    type: 'credit',
  },
  {
    id: 'capital',
    name: 'Capital One',
    logo: 'https://logo.clearbit.com/capitalone.com',
    type: 'credit',
  },
];

// Styled Components
const Container = styled(motion.div)`
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: ${({ theme }) => theme.borderRadius.large};
  box-shadow: ${({ theme }) => theme.shadows.large};
  width: 100%;
  max-width: 600px;
  overflow: hidden;
`;

const Header = styled.div`
  padding: 1.5rem;
  border-bottom: 1px solid ${({ theme }) => theme.colors.divider};
`;

const Title = styled.h2`
  font-size: ${({ theme }) => theme.typography.fontSize.heading3};
  color: ${({ theme }) => theme.colors.neutral.primaryText};
  margin: 0 0 0.5rem;
`;

const Subtitle = styled.p`
  font-size: ${({ theme }) => theme.typography.fontSize.body};
  color: ${({ theme }) => theme.colors.neutral.secondaryText};
  margin: 0;
`;

const SearchContainer = styled.div`
  padding: 1rem 1.5rem;
  border-bottom: 1px solid ${({ theme }) => theme.colors.divider};
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: ${({ theme }) => theme.borderRadius.medium};
  border: 1px solid ${({ theme }) => theme.colors.divider};
  background-color: ${({ theme }) => theme.colors.background.elevated};
  color: ${({ theme }) => theme.colors.neutral.primaryText};
  font-size: ${({ theme }) => theme.typography.fontSize.body};
  transition: all 0.2s ease;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.seasons[theme.season].primary};
    box-shadow: 0 0 0 2px ${({ theme }) => `${theme.colors.seasons[theme.season].primary}40`};
  }

  &::placeholder {
    color: ${({ theme }) => theme.colors.neutral.secondaryText};
    opacity: 0.7;
  }
`;

const InstitutionList = styled.div`
  max-height: 400px;
  overflow-y: auto;
  padding: 0.5rem 0;
`;

const InstitutionCategory = styled.div`
  padding: 0.5rem 1.5rem;
`;

const CategoryTitle = styled.h3`
  font-size: ${({ theme }) => theme.typography.fontSize.bodySmall};
  color: ${({ theme }) => theme.colors.neutral.secondaryText};
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 1rem 0 0.5rem;
`;

const InstitutionItem = styled(motion.div)`
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${({ theme }) =>
      theme.mode === 'light' ? 'rgba(0, 0, 0, 0.05)' : 'rgba(255, 255, 255, 0.05)'};
  }
`;

const InstitutionLogo = styled.div`
  width: 40px;
  height: 40px;
  border-radius: ${({ theme }) => theme.borderRadius.medium};
  background-color: ${({ theme }) => theme.colors.background.elevated};
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin-right: 1rem;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
`;

const InstitutionInfo = styled.div`
  flex: 1;
`;

const InstitutionName = styled.div`
  font-size: ${({ theme }) => theme.typography.fontSize.body};
  color: ${({ theme }) => theme.colors.neutral.primaryText};
  font-weight: 500;
`;

const InstitutionType = styled.div`
  font-size: ${({ theme }) => theme.typography.fontSize.bodySmall};
  color: ${({ theme }) => theme.colors.neutral.secondaryText};
`;

const Footer = styled.div`
  padding: 1rem 1.5rem;
  border-top: 1px solid ${({ theme }) => theme.colors.divider};
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
`;

const Button = styled.button`
  padding: 0.75rem 1.5rem;
  border-radius: ${({ theme }) => theme.borderRadius.medium};
  font-size: ${({ theme }) => theme.typography.fontSize.body};
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:focus {
    outline: none;
  }
`;

const CancelButton = styled(Button)`
  background-color: transparent;
  color: ${({ theme }) => theme.colors.neutral.primaryText};
  border: 1px solid ${({ theme }) => theme.colors.divider};

  &:hover {
    background-color: ${({ theme }) =>
      theme.mode === 'light' ? 'rgba(0, 0, 0, 0.05)' : 'rgba(255, 255, 255, 0.05)'};
  }
`;

const ConnectButton = styled(Button)<{ disabled: boolean }>`
  background-color: ${({ theme, disabled }) =>
    disabled
      ? theme.mode === 'light'
        ? '#e0e0e0'
        : '#424242'
      : theme.colors.seasons[theme.season].primary};
  color: ${({ disabled }) => (disabled ? '#9e9e9e' : 'white')};
  border: none;
  opacity: ${({ disabled }) => (disabled ? 0.7 : 1)};
  pointer-events: ${({ disabled }) => (disabled ? 'none' : 'auto')};

  &:hover {
    background-color: ${({ theme, disabled }) =>
      disabled
        ? theme.mode === 'light'
          ? '#e0e0e0'
          : '#424242'
        : theme.colors.seasons[theme.season].accent ||
          theme.colors.seasons[theme.season].secondary};
    transform: translateY(-1px);
  }
`;

const EmptyState = styled.div`
  padding: 2rem;
  text-align: center;
`;

const EmptyStateText = styled.p`
  font-size: ${({ theme }) => theme.typography.fontSize.body};
  color: ${({ theme }) => theme.colors.neutral.secondaryText};
`;

/**
 * Financial Institution Selector Component
 */
const FinancialInstitutionSelector: React.FC<FinancialInstitutionSelectorProps> = ({
  onSelect,
  onCancel,
  recentInstitutions = [],
}) => {
  const { theme } = useTheme();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedInstitution, setSelectedInstitution] = useState<FinancialInstitution | null>(null);
  const [filteredInstitutions, setFilteredInstitutions] =
    useState<FinancialInstitution[]>(mockInstitutions);

  // Filter institutions based on search term
  useEffect(() => {
    if (!searchTerm) {
      setFilteredInstitutions(mockInstitutions);
      return;
    }

    const filtered = mockInstitutions.filter((institution) =>
      institution.name.toLowerCase().includes(searchTerm.toLowerCase())
    );

    setFilteredInstitutions(filtered);
  }, [searchTerm]);

  // Group institutions by type
  const groupedInstitutions = filteredInstitutions.reduce(
    (acc, institution) => {
      if (!acc[institution.type]) {
        acc[institution.type] = [];
      }

      acc[institution.type].push(institution);
      return acc;
    },
    {} as Record<string, FinancialInstitution[]>
  );

  // Handle institution selection
  const handleSelect = (institution: FinancialInstitution) => {
    setSelectedInstitution(institution);
  };

  // Handle connect button click
  const handleConnect = () => {
    if (selectedInstitution) {
      onSelect(selectedInstitution);
    }
  };

  // Get type label
  const getTypeLabel = (type: string): string => {
    switch (type) {
      case 'bank':
        return 'Banks';
      case 'investment':
        return 'Investment Accounts';
      case 'credit':
        return 'Credit Cards';
      case 'other':
        return 'Other Institutions';
      default:
        return 'Financial Institutions';
    }
  };

  return (
    <Container
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      transition={{ duration: 0.3 }}
    >
      <Header>
        <Title>Connect Your Financial Accounts</Title>
        <Subtitle>
          Securely connect your financial accounts to automatically import your financial data. We
          use bank-level encryption to keep your information safe.
        </Subtitle>
      </Header>

      <SearchContainer>
        <SearchInput
          type="text"
          placeholder="Search for your bank or financial institution..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          aria-label="Search for financial institutions"
        />
      </SearchContainer>

      <InstitutionList>
        {recentInstitutions.length > 0 && (
          <InstitutionCategory>
            <CategoryTitle>Recently Connected</CategoryTitle>
            {recentInstitutions.map((institution) => (
              <InstitutionItem
                key={institution.id}
                onClick={() => handleSelect(institution)}
                whileHover={{ x: 5 }}
                transition={{ type: 'spring', stiffness: 300, damping: 20 }}
                style={{
                  backgroundColor:
                    selectedInstitution?.id === institution.id
                      ? theme.mode === 'light'
                        ? 'rgba(0, 0, 0, 0.05)'
                        : 'rgba(255, 255, 255, 0.05)'
                      : 'transparent',
                }}
              >
                <InstitutionLogo>
                  <img src={institution.logo} alt={`${institution.name} logo`} />
                </InstitutionLogo>
                <InstitutionInfo>
                  <InstitutionName>{institution.name}</InstitutionName>
                  <InstitutionType>
                    {institution.connectionStatus === 'connected' ? 'Connected' : 'Disconnected'}
                    {institution.lastUpdated && ` • Last updated ${institution.lastUpdated}`}
                  </InstitutionType>
                </InstitutionInfo>
              </InstitutionItem>
            ))}
          </InstitutionCategory>
        )}

        {Object.keys(groupedInstitutions).length > 0 ? (
          Object.entries(groupedInstitutions).map(([type, institutions]) => (
            <InstitutionCategory key={type}>
              <CategoryTitle>{getTypeLabel(type)}</CategoryTitle>
              {institutions.map((institution) => (
                <InstitutionItem
                  key={institution.id}
                  onClick={() => handleSelect(institution)}
                  whileHover={{ x: 5 }}
                  transition={{ type: 'spring', stiffness: 300, damping: 20 }}
                  style={{
                    backgroundColor:
                      selectedInstitution?.id === institution.id
                        ? theme.mode === 'light'
                          ? 'rgba(0, 0, 0, 0.05)'
                          : 'rgba(255, 255, 255, 0.05)'
                        : 'transparent',
                  }}
                >
                  <InstitutionLogo>
                    <img src={institution.logo} alt={`${institution.name} logo`} />
                  </InstitutionLogo>
                  <InstitutionInfo>
                    <InstitutionName>{institution.name}</InstitutionName>
                    <InstitutionType>{getTypeLabel(institution.type).slice(0, -1)}</InstitutionType>
                  </InstitutionInfo>
                </InstitutionItem>
              ))}
            </InstitutionCategory>
          ))
        ) : (
          <EmptyState>
            <EmptyStateText>
              No financial institutions found matching "{searchTerm}". Try a different search term
              or browse the categories.
            </EmptyStateText>
          </EmptyState>
        )}
      </InstitutionList>

      <Footer>
        <CancelButton onClick={onCancel}>Cancel</CancelButton>
        <ConnectButton disabled={!selectedInstitution} onClick={handleConnect}>
          Connect
        </ConnectButton>
      </Footer>
    </Container>
  );
};

export default FinancialInstitutionSelector;
