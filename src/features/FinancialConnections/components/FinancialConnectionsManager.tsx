/**
 * Financial Connections Manager Component
 *
 * This component manages the user's financial institution connections,
 * allowing them to connect, disconnect, and view their connected accounts.
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '../../../theme/SimpleThemeProvider';
import FinancialInstitutionSelector, { FinancialInstitution } from './FinancialInstitutionSelector';
import OAuthConnectionFlow from './OAuthConnectionFlow';

// Types
interface FinancialConnectionsManagerProps {
  onClose?: () => void;
}

// Styled Components
const Container = styled.div`
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
`;

const Header = styled.div`
  margin-bottom: 2rem;
`;

const Title = styled.h1`
  font-size: ${({ theme }) => theme.typography.fontSize.heading2};
  color: ${({ theme }) => theme.colors.neutral.primaryText};
  margin: 0 0 0.5rem;
`;

const Subtitle = styled.p`
  font-size: ${({ theme }) => theme.typography.fontSize.bodyLarge};
  color: ${({ theme }) => theme.colors.neutral.secondaryText};
  margin: 0;
  max-width: 800px;
`;

const ActionBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
`;

const ConnectButton = styled.button`
  padding: 0.75rem 1.5rem;
  border-radius: ${({ theme }) => theme.borderRadius.medium};
  background-color: ${({ theme }) => theme.colors.seasons[theme.season].primary};
  color: white;
  border: none;
  font-size: ${({ theme }) => theme.typography.fontSize.body};
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &:hover {
    background-color: ${({ theme }) =>
      theme.colors.seasons[theme.season].accent || theme.colors.seasons[theme.season].secondary};
    transform: translateY(-1px);
    box-shadow: ${({ theme }) => theme.shadows.sm};
  }

  &:focus {
    outline: none;
  }
`;

const ConnectionsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
`;

const ConnectionCard = styled(motion.div)`
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: ${({ theme }) => theme.borderRadius.large};
  box-shadow: ${({ theme }) => theme.shadows.medium};
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: ${({ theme }) => theme.shadows.large};
  }
`;

const ConnectionHeader = styled.div`
  padding: 1.5rem;
  border-bottom: 1px solid ${({ theme }) => theme.colors.divider};
  display: flex;
  align-items: center;
  gap: 1rem;
`;

const ConnectionLogo = styled.div`
  width: 48px;
  height: 48px;
  border-radius: ${({ theme }) => theme.borderRadius.medium};
  background-color: ${({ theme }) => theme.colors.background.elevated};
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
`;

const ConnectionInfo = styled.div`
  flex: 1;
`;

const ConnectionName = styled.h3`
  font-size: ${({ theme }) => theme.typography.fontSize.bodyLarge};
  color: ${({ theme }) => theme.colors.neutral.primaryText};
  margin: 0 0 0.25rem;
`;

const ConnectionStatus = styled.div<{
  status: 'connected' | 'disconnected' | 'connecting' | 'error';
}>`
  font-size: ${({ theme }) => theme.typography.fontSize.bodySmall};
  color: ${({ theme, status }) => {
    switch (status) {
      case 'connected':
        return theme.colors.success.main;
      case 'disconnected':
        return theme.colors.neutral.secondaryText;
      case 'connecting':
        return theme.colors.info.main;
      case 'error':
        return theme.colors.error.main;
      default:
        return theme.colors.neutral.secondaryText;
    }
  }};
  display: flex;
  align-items: center;
  gap: 0.25rem;
`;

const ConnectionBody = styled.div`
  padding: 1.5rem;
`;

const AccountsList = styled.div`
  margin-bottom: 1.5rem;
`;

const AccountsTitle = styled.h4`
  font-size: ${({ theme }) => theme.typography.fontSize.body};
  color: ${({ theme }) => theme.colors.neutral.primaryText};
  margin: 0 0 1rem;
`;

const AccountItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid ${({ theme }) => theme.colors.divider};

  &:last-child {
    border-bottom: none;
  }
`;

const AccountName = styled.div`
  font-size: ${({ theme }) => theme.typography.fontSize.body};
  color: ${({ theme }) => theme.colors.neutral.primaryText};
`;

const AccountBalance = styled.div`
  font-size: ${({ theme }) => theme.typography.fontSize.body};
  color: ${({ theme }) => theme.colors.neutral.primaryText};
  font-weight: 600;
`;

const ConnectionFooter = styled.div`
  padding: 1rem 1.5rem;
  border-top: 1px solid ${({ theme }) => theme.colors.divider};
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const LastUpdated = styled.div`
  font-size: ${({ theme }) => theme.typography.fontSize.bodySmall};
  color: ${({ theme }) => theme.colors.neutral.secondaryText};
`;

const DisconnectButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.error.main};
  font-size: ${({ theme }) => theme.typography.fontSize.bodySmall};
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: ${({ theme }) => theme.borderRadius.small};
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${({ theme }) =>
      theme.mode === 'light' ? 'rgba(244, 67, 54, 0.1)' : 'rgba(244, 67, 54, 0.2)'};
  }

  &:focus {
    outline: none;
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem;
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: ${({ theme }) => theme.borderRadius.large};
  box-shadow: ${({ theme }) => theme.shadows.medium};
`;

const EmptyStateIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1.5rem;
  color: ${({ theme }) => theme.colors.seasons[theme.season].primary};
`;

const EmptyStateTitle = styled.h2`
  font-size: ${({ theme }) => theme.typography.fontSize.heading3};
  color: ${({ theme }) => theme.colors.neutral.primaryText};
  margin: 0 0 1rem;
`;

const EmptyStateText = styled.p`
  font-size: ${({ theme }) => theme.typography.fontSize.body};
  color: ${({ theme }) => theme.colors.neutral.secondaryText};
  margin: 0 0 2rem;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
`;

const ModalContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
`;

const ModalBackdrop = styled(motion.div)`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
`;

// Mock data for connected accounts
interface Account {
  id: string;
  name: string;
  type: string;
  balance: number;
  currency: string;
}

interface ConnectedInstitution extends FinancialInstitution {
  accounts: Account[];
}

// Mock connected institutions
const mockConnectedInstitutions: ConnectedInstitution[] = [
  {
    id: 'chase',
    name: 'Chase Bank',
    logo: 'https://logo.clearbit.com/chase.com',
    type: 'bank',
    connectionStatus: 'connected',
    lastUpdated: '05/10/2024',
    accounts: [
      {
        id: 'chase-checking',
        name: 'Chase Total Checking',
        type: 'checking',
        balance: 2543.87,
        currency: 'USD',
      },
      {
        id: 'chase-savings',
        name: 'Chase Savings',
        type: 'savings',
        balance: 15750.42,
        currency: 'USD',
      },
    ],
  },
  {
    id: 'fidelity',
    name: 'Fidelity Investments',
    logo: 'https://logo.clearbit.com/fidelity.com',
    type: 'investment',
    connectionStatus: 'connected',
    lastUpdated: '05/09/2024',
    accounts: [
      {
        id: 'fidelity-401k',
        name: 'Fidelity 401(k)',
        type: 'retirement',
        balance: 87432.19,
        currency: 'USD',
      },
      {
        id: 'fidelity-ira',
        name: 'Fidelity Roth IRA',
        type: 'retirement',
        balance: 42156.78,
        currency: 'USD',
      },
      {
        id: 'fidelity-brokerage',
        name: 'Fidelity Brokerage',
        type: 'investment',
        balance: 31245.92,
        currency: 'USD',
      },
    ],
  },
];

/**
 * Financial Connections Manager Component
 */
const FinancialConnectionsManager: React.FC<FinancialConnectionsManagerProps> = ({ onClose }) => {
  const { theme } = useTheme();
  const [showSelector, setShowSelector] = useState(false);
  const [selectedInstitution, setSelectedInstitution] = useState<FinancialInstitution | null>(null);
  const [connectedInstitutions, setConnectedInstitutions] =
    useState<ConnectedInstitution[]>(mockConnectedInstitutions);

  // Handle add connection
  const handleAddConnection = () => {
    setShowSelector(true);
  };

  // Handle institution selection
  const handleInstitutionSelect = (institution: FinancialInstitution) => {
    setSelectedInstitution(institution);
    setShowSelector(false);
  };

  // Handle connection success
  const handleConnectionSuccess = (institution: FinancialInstitution, accessToken: string) => {
    // In a real implementation, you would use the access token to fetch accounts
    // For this demo, we'll add mock accounts
    const newConnectedInstitution: ConnectedInstitution = {
      ...institution,
      accounts: [
        {
          id: `${institution.id}-checking`,
          name: `${institution.name} Checking`,
          type: 'checking',
          balance: Math.random() * 5000,
          currency: 'USD',
        },
        {
          id: `${institution.id}-savings`,
          name: `${institution.name} Savings`,
          type: 'savings',
          balance: Math.random() * 20000,
          currency: 'USD',
        },
      ],
    };

    setConnectedInstitutions((prev) => [...prev, newConnectedInstitution]);
    setSelectedInstitution(null);
  };

  // Handle connection error
  const handleConnectionError = (error: Error) => {
    console.error('Connection error:', error);
    // In a real implementation, you would handle the error appropriately
  };

  // Handle disconnect
  const handleDisconnect = (institutionId: string) => {
    setConnectedInstitutions((prev) => prev.filter((inst) => inst.id !== institutionId));
  };

  // Format currency
  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
    }).format(amount);
  };

  // Get status icon
  const getStatusIcon = (status: 'connected' | 'disconnected' | 'connecting' | 'error') => {
    switch (status) {
      case 'connected':
        return '✅';
      case 'disconnected':
        return '⚠️';
      case 'connecting':
        return '🔄';
      case 'error':
        return '❌';
      default:
        return '';
    }
  };

  return (
    <Container>
      <Header>
        <Title>Financial Connections</Title>
        <Subtitle>
          Connect your financial accounts to automatically import your financial data. We use
          bank-level encryption to keep your information safe.
        </Subtitle>
      </Header>

      <ActionBar>
        <ConnectButton onClick={handleAddConnection}>
          <span>+</span> Add Financial Connection
        </ConnectButton>
      </ActionBar>

      {connectedInstitutions.length > 0 ? (
        <ConnectionsGrid>
          {connectedInstitutions.map((institution) => (
            <ConnectionCard
              key={institution.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <ConnectionHeader>
                <ConnectionLogo>
                  <img src={institution.logo} alt={`${institution.name} logo`} />
                </ConnectionLogo>
                <ConnectionInfo>
                  <ConnectionName>{institution.name}</ConnectionName>
                  <ConnectionStatus status={institution.connectionStatus || 'disconnected'}>
                    <span>{getStatusIcon(institution.connectionStatus || 'disconnected')}</span>
                    {institution.connectionStatus === 'connected' ? 'Connected' : 'Disconnected'}
                  </ConnectionStatus>
                </ConnectionInfo>
              </ConnectionHeader>

              <ConnectionBody>
                <AccountsList>
                  <AccountsTitle>Accounts</AccountsTitle>
                  {institution.accounts.map((account) => (
                    <AccountItem key={account.id}>
                      <AccountName>{account.name}</AccountName>
                      <AccountBalance>
                        {formatCurrency(account.balance, account.currency)}
                      </AccountBalance>
                    </AccountItem>
                  ))}
                </AccountsList>
              </ConnectionBody>

              <ConnectionFooter>
                <LastUpdated>Last updated: {institution.lastUpdated}</LastUpdated>
                <DisconnectButton onClick={() => handleDisconnect(institution.id)}>
                  Disconnect
                </DisconnectButton>
              </ConnectionFooter>
            </ConnectionCard>
          ))}
        </ConnectionsGrid>
      ) : (
        <EmptyState>
          <EmptyStateIcon>🏦</EmptyStateIcon>
          <EmptyStateTitle>No Financial Connections</EmptyStateTitle>
          <EmptyStateText>
            Connect your financial accounts to automatically import your financial data. This helps
            you get a complete picture of your finances without manual data entry.
          </EmptyStateText>
          <ConnectButton onClick={handleAddConnection}>
            <span>+</span> Add Your First Connection
          </ConnectButton>
        </EmptyState>
      )}

      <AnimatePresence>
        {showSelector && (
          <ModalContainer>
            <ModalBackdrop
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setShowSelector(false)}
            />
            <FinancialInstitutionSelector
              onSelect={handleInstitutionSelect}
              onCancel={() => setShowSelector(false)}
              recentInstitutions={connectedInstitutions}
            />
          </ModalContainer>
        )}

        {selectedInstitution && (
          <OAuthConnectionFlow
            institution={selectedInstitution}
            onSuccess={handleConnectionSuccess}
            onCancel={() => setSelectedInstitution(null)}
            onError={handleConnectionError}
          />
        )}
      </AnimatePresence>
    </Container>
  );
};

export default FinancialConnectionsManager;
