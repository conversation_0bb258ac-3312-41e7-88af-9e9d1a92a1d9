/**
 * OAuth Connection Flow Component
 *
 * This component handles the OAuth flow for connecting to financial institutions.
 * It displays a modal with the institution's OAuth page and handles the callback.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { FinancialInstitution } from './FinancialInstitutionSelector';

// Types
interface OAuthConnectionFlowProps {
  institution: FinancialInstitution;
  onSuccess: (institution: FinancialInstitution, accessToken: string) => void;
  onCancel: () => void;
  onError: (error: Error) => void;
}

// Styled Components
const Overlay = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
`;

const Modal = styled(motion.div)`
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: ${({ theme }) => theme.borderRadius.large};
  box-shadow: ${({ theme }) => theme.shadows.large};
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  padding: 1rem 1.5rem;
  border-bottom: 1px solid ${({ theme }) => theme.colors.divider};
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const Title = styled.h3`
  font-size: ${({ theme }) => theme.typography.fontSize.bodyLarge};
  color: ${({ theme }) => theme.colors.neutral.primaryText};
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
`;

const InstitutionLogo = styled.div`
  width: 24px;
  height: 24px;
  border-radius: ${({ theme }) => theme.borderRadius.small};
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.neutral.secondaryText};
  cursor: pointer;
  font-size: 1.5rem;
  padding: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s ease;

  &:hover {
    color: ${({ theme }) => theme.colors.neutral.primaryText};
  }

  &:focus {
    outline: none;
  }
`;

const Content = styled.div`
  flex: 1;
  overflow: hidden;
  position: relative;
`;

const IframeContainer = styled.div`
  width: 100%;
  height: 500px;
  max-height: 60vh;
`;

const Iframe = styled.iframe`
  width: 100%;
  height: 100%;
  border: none;
`;

const LoadingOverlay = styled(motion.div)`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: ${({ theme }) => theme.colors.background.paper};
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
`;

const LoadingSpinner = styled(motion.div)`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid ${({ theme }) => theme.colors.divider};
  border-top-color: ${({ theme }) => theme.colors.seasons[theme.season].primary};
  margin-bottom: 1rem;
`;

const LoadingText = styled.p`
  font-size: ${({ theme }) => theme.typography.fontSize.body};
  color: ${({ theme }) => theme.colors.neutral.secondaryText};
  margin: 0;
`;

const ErrorContainer = styled.div`
  padding: 2rem;
  text-align: center;
`;

const ErrorTitle = styled.h3`
  font-size: ${({ theme }) => theme.typography.fontSize.bodyLarge};
  color: ${({ theme }) => theme.colors.error.main};
  margin: 0 0 1rem;
`;

const ErrorMessage = styled.p`
  font-size: ${({ theme }) => theme.typography.fontSize.body};
  color: ${({ theme }) => theme.colors.neutral.secondaryText};
  margin: 0 0 1.5rem;
`;

const RetryButton = styled.button`
  padding: 0.75rem 1.5rem;
  border-radius: ${({ theme }) => theme.borderRadius.medium};
  background-color: ${({ theme }) => theme.colors.seasons[theme.season].primary};
  color: white;
  border: none;
  font-size: ${({ theme }) => theme.typography.fontSize.body};
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${({ theme }) =>
      theme.colors.seasons[theme.season].accent || theme.colors.seasons[theme.season].secondary};
    transform: translateY(-1px);
  }

  &:focus {
    outline: none;
  }
`;

const Footer = styled.div`
  padding: 1rem 1.5rem;
  border-top: 1px solid ${({ theme }) => theme.colors.divider};
  text-align: center;
`;

const SecurityNote = styled.p`
  font-size: ${({ theme }) => theme.typography.fontSize.bodySmall};
  color: ${({ theme }) => theme.colors.neutral.secondaryText};
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
`;

const LockIcon = styled.span`
  font-size: 1rem;
`;

/**
 * OAuth Connection Flow Component
 */
const OAuthConnectionFlow: React.FC<OAuthConnectionFlowProps> = ({
  institution,
  onSuccess,
  onCancel,
  onError,
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [oauthUrl, setOauthUrl] = useState('');

  // Simulate OAuth URL generation
  useEffect(() => {
    const generateOAuthUrl = async () => {
      try {
        setLoading(true);

        // In a real implementation, this would be an API call to generate an OAuth URL
        // For this demo, we'll simulate a delay and then use a mock URL
        await new Promise((resolve) => setTimeout(resolve, 1500));

        // Mock OAuth URL (in a real app, this would come from your backend)
        const mockOAuthUrl = `https://example.com/oauth/${institution.id}`;

        setOauthUrl(mockOAuthUrl);
        setLoading(false);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to generate OAuth URL'));
        setLoading(false);
        onError(err instanceof Error ? err : new Error('Failed to generate OAuth URL'));
      }
    };

    generateOAuthUrl();
  }, [institution, onError]);

  // Handle OAuth callback
  const handleOAuthCallback = (event: MessageEvent) => {
    // In a real implementation, you would validate the origin and handle the OAuth callback
    // For this demo, we'll simulate a successful connection after a delay
    if (event.data && event.data.type === 'oauth_callback') {
      const { accessToken } = event.data;

      // Update the institution with connected status
      const updatedInstitution: FinancialInstitution = {
        ...institution,
        connectionStatus: 'connected',
        lastUpdated: new Date().toLocaleDateString(),
      };

      onSuccess(updatedInstitution, accessToken);
    }
  };

  // Add message listener for OAuth callback
  useEffect(() => {
    window.addEventListener('message', handleOAuthCallback);

    return () => {
      window.removeEventListener('message', handleOAuthCallback);
    };
  }, [institution]);

  // Handle retry
  const handleRetry = () => {
    setError(null);
    setLoading(true);

    // Simulate retry
    setTimeout(() => {
      const mockOAuthUrl = `https://example.com/oauth/${institution.id}?retry=true`;
      setOauthUrl(mockOAuthUrl);
      setLoading(false);
    }, 1500);
  };

  // For demo purposes, simulate a successful connection after 5 seconds
  useEffect(() => {
    if (!loading && !error) {
      const timer = setTimeout(() => {
        const updatedInstitution: FinancialInstitution = {
          ...institution,
          connectionStatus: 'connected',
          lastUpdated: new Date().toLocaleDateString(),
        };

        onSuccess(updatedInstitution, 'mock_access_token');
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [loading, error, institution, onSuccess]);

  return (
    <Overlay
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={onCancel}
    >
      <Modal
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        transition={{ type: 'spring', damping: 25, stiffness: 300 }}
        onClick={(e) => e.stopPropagation()}
      >
        <Header>
          <Title>
            <InstitutionLogo>
              <img src={institution.logo} alt={`${institution.name} logo`} />
            </InstitutionLogo>
            Connect to {institution.name}
          </Title>
          <CloseButton onClick={onCancel} aria-label="Close">
            ×
          </CloseButton>
        </Header>

        <Content>
          {error ? (
            <ErrorContainer>
              <ErrorTitle>Connection Error</ErrorTitle>
              <ErrorMessage>
                We encountered an error while trying to connect to {institution.name}. Please try
                again or contact support if the issue persists.
              </ErrorMessage>
              <RetryButton onClick={handleRetry}>Try Again</RetryButton>
            </ErrorContainer>
          ) : (
            <IframeContainer>
              {!loading && (
                <Iframe
                  src={oauthUrl}
                  title={`Connect to ${institution.name}`}
                  sandbox="allow-same-origin allow-scripts allow-forms allow-popups"
                />
              )}

              <AnimatePresence>
                {loading && (
                  <LoadingOverlay
                    initial={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <LoadingSpinner
                      animate={{ rotate: 360 }}
                      transition={{ repeat: Infinity, duration: 1, ease: 'linear' }}
                    />
                    <LoadingText>Connecting to {institution.name}...</LoadingText>
                  </LoadingOverlay>
                )}
              </AnimatePresence>
            </IframeContainer>
          )}
        </Content>

        <Footer>
          <SecurityNote>
            <LockIcon>🔒</LockIcon>
            Your credentials are securely transmitted using bank-level encryption
          </SecurityNote>
        </Footer>
      </Modal>
    </Overlay>
  );
};

export default OAuthConnectionFlow;
