/**
 * Guided Journey Feature Index
 *
 * This file exports all components and contexts related to the guided journey experience.
 */

// Components
export { default as GuidedJourneyNavigator } from './components/GuidedJourneyNavigator';
export { default as ProgressTracker } from './components/ProgressTracker';
export { default as ContextualGuide } from './components/ContextualGuide';
export { default as SeasonalTransition } from './components/SeasonalTransition';
export { default as StepWizard } from './components/StepWizard';

// Context
export { GuidedJourneyProvider, useGuidedJourney } from './context/GuidedJourneyContext';

// Pages
export { default as GuidedJourneyPage } from './pages/GuidedJourneyPage';
