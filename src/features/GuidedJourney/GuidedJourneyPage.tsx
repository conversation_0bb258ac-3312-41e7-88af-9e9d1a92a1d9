/**
 * Guided Journey Page
 *
 * This component provides a guided journey experience for users to plan their retirement.
 * It uses the JourneyContext to manage state and navigation between steps.
 */

import React from 'react';
import { Link } from 'react-router-dom';
import { useJourney, JourneyProvider } from './GuidedJourneyContext';
import { useTheme } from '../../theme/ThemeProvider'; // Import the theme hook from ThemeProvider

// Step components
import RetirementGoalsStep from './steps/RetirementGoalsStep';
import IncomeSourcesStep from './steps/IncomeSourcesStep';
import ExpenseCategoriesStep from './steps/ExpenseCategoriesStep';
import TimelineStep from './steps/TimelineStep';

// Styled components for the journey page
const JourneyContainer: React.FC<{ children: React.ReactNode; isDarkMode: boolean }> = ({
  children,
  isDarkMode,
}) => (
  <div
    style={{
      padding: '20px',
      maxWidth: '1200px',
      margin: '0 auto',
      backgroundColor: isDarkMode ? '#121212' : '#f5f5f5',
      color: isDarkMode ? '#e0e0e0' : '#333',
      minHeight: 'calc(100vh - 60px)', // Account for navigation height
      transition: 'background-color 0.3s ease, color 0.3s ease',
    }}
  >
    {children}
  </div>
);

const JourneyHeader: React.FC<{ title: string; isDarkMode: boolean }> = ({ title, isDarkMode }) => (
  <div
    style={{
      marginBottom: '30px',
      borderBottom: isDarkMode ? '1px solid #444' : '1px solid #ddd',
      paddingBottom: '20px',
      transition: 'border-color 0.3s ease',
    }}
  >
    <h1
      style={{
        color: isDarkMode ? '#81C784' : '#4CAF50',
        marginBottom: '10px',
        transition: 'color 0.3s ease',
      }}
    >
      {title}
    </h1>
    <p>Plan your retirement journey with our guided process</p>
  </div>
);

const StepNavigation: React.FC<{ isDarkMode: boolean }> = ({ isDarkMode }) => {
  const { steps, currentStep, goToStep, isStepCompleted } = useJourney();

  return (
    <div
      style={{
        display: 'flex',
        marginBottom: '30px',
        overflowX: 'auto',
        padding: '10px 0',
        borderBottom: isDarkMode ? '1px solid #444' : '1px solid #ddd',
        transition: 'border-color 0.3s ease',
      }}
    >
      {steps.map((step, index) => (
        <div
          key={step.id}
          onClick={() => goToStep(step.id)}
          style={{
            padding: '10px 20px',
            marginRight: '10px',
            cursor: 'pointer',
            backgroundColor:
              currentStep?.id === step.id ? (isDarkMode ? '#2e7d32' : '#4CAF50') : 'transparent',
            color: currentStep?.id === step.id ? 'white' : isDarkMode ? '#e0e0e0' : '#333',
            borderRadius: '4px',
            position: 'relative',
            transition: 'background-color 0.3s ease, color 0.3s ease',
          }}
        >
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <div
              style={{
                width: '24px',
                height: '24px',
                borderRadius: '50%',
                backgroundColor: isStepCompleted(step.id)
                  ? isDarkMode
                    ? '#81C784'
                    : '#4CAF50'
                  : isDarkMode
                    ? '#555'
                    : '#ddd',
                color: isStepCompleted(step.id) ? 'white' : isDarkMode ? '#e0e0e0' : '#333',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: '10px',
                fontWeight: 'bold',
                fontSize: '14px',
                transition: 'background-color 0.3s ease, color 0.3s ease',
              }}
            >
              {index + 1}
            </div>
            {step.title}
          </div>
        </div>
      ))}
    </div>
  );
};

const StepContent: React.FC = () => {
  const { currentStep } = useJourney();

  if (!currentStep) {
    return <div>No step selected</div>;
  }

  switch (currentStep.id) {
    case 'retirement-goals':
      return <RetirementGoalsStep />;
    case 'income-sources':
      return <IncomeSourcesStep />;
    case 'expense-categories':
      return <ExpenseCategoriesStep />;
    case 'timeline':
      return <TimelineStep />;
    default:
      return <div>Unknown step</div>;
  }
};

const StepActions: React.FC<{ isDarkMode: boolean }> = ({ isDarkMode }) => {
  const { goToNextStep, goToPreviousStep, currentStep, steps, saveProgress } = useJourney();

  const isFirstStep = currentStep?.id === steps[0]?.id;
  const isLastStep = currentStep?.id === steps[steps.length - 1]?.id;

  const handleSave = async () => {
    try {
      await saveProgress();
      alert('Progress saved successfully!');
    } catch (error) {
      alert('Failed to save progress');
    }
  };

  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'space-between',
        marginTop: '30px',
        paddingTop: '20px',
        borderTop: isDarkMode ? '1px solid #444' : '1px solid #ddd',
        transition: 'border-color 0.3s ease',
      }}
    >
      <button
        onClick={goToPreviousStep}
        disabled={isFirstStep}
        style={{
          padding: '10px 20px',
          backgroundColor: isFirstStep
            ? isDarkMode
              ? '#555'
              : '#ddd'
            : isDarkMode
              ? '#81C784'
              : '#4CAF50',
          color: isFirstStep ? (isDarkMode ? '#999' : '#999') : 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: isFirstStep ? 'not-allowed' : 'pointer',
          transition: 'background-color 0.3s ease, color 0.3s ease',
        }}
      >
        Previous
      </button>

      <div>
        <button
          onClick={handleSave}
          style={{
            padding: '10px 20px',
            backgroundColor: isDarkMode ? '#64B5F6' : '#2196F3',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            marginRight: '10px',
            cursor: 'pointer',
            transition: 'background-color 0.3s ease',
          }}
        >
          Save Progress
        </button>

        {isLastStep ? (
          <Link
            to="/"
            style={{
              padding: '10px 20px',
              backgroundColor: isDarkMode ? '#81C784' : '#4CAF50',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              textDecoration: 'none',
              display: 'inline-block',
              transition: 'background-color 0.3s ease',
            }}
          >
            Complete Journey
          </Link>
        ) : (
          <button
            onClick={goToNextStep}
            style={{
              padding: '10px 20px',
              backgroundColor: isDarkMode ? '#81C784' : '#4CAF50',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              transition: 'background-color 0.3s ease',
            }}
          >
            Next
          </button>
        )}
      </div>
    </div>
  );
};

// Main journey content component
const JourneyContent: React.FC = () => {
  const { mode } = useTheme();
  const isDarkMode = mode === 'dark';
  const { currentStep } = useJourney();

  return (
    <JourneyContainer isDarkMode={isDarkMode}>
      <JourneyHeader title="Retirement Planning Journey" isDarkMode={isDarkMode} />
      <StepNavigation isDarkMode={isDarkMode} />

      <div
        style={{
          backgroundColor: isDarkMode ? '#1e1e1e' : 'white',
          padding: '30px',
          borderRadius: '8px',
          boxShadow: isDarkMode ? '0 2px 4px rgba(0,0,0,0.3)' : '0 2px 4px rgba(0,0,0,0.1)',
          transition: 'background-color 0.3s ease, box-shadow 0.3s ease',
        }}
      >
        <h2
          style={{
            color: isDarkMode ? '#81C784' : '#4CAF50',
            marginBottom: '20px',
            transition: 'color 0.3s ease',
          }}
        >
          {currentStep?.title}
        </h2>
        <p style={{ marginBottom: '20px' }}>{currentStep?.description}</p>

        <StepContent />
      </div>

      <StepActions isDarkMode={isDarkMode} />
    </JourneyContainer>
  );
};

// Wrapper component that provides the journey context
export const GuidedJourneyPage: React.FC = () => {
  return (
    <JourneyProvider>
      <JourneyContent />
    </JourneyProvider>
  );
};

export default GuidedJourneyPage;
