/**
 * Guided Journey Context
 *
 * This context provides state management for the guided journey experience.
 * It tracks the user's progress through the journey, manages step navigation,
 * and stores user inputs for each step.
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Define the journey step types
export type JourneyDirection = 'north' | 'east' | 'south' | 'west';

export interface JourneyStep {
  id: string;
  title: string;
  description: string;
  direction: JourneyDirection;
  order: number;
  isCompleted: boolean;
}

// Define the user data structure
export interface RetirementGoal {
  description: string;
  priority: 'high' | 'medium' | 'low';
  estimatedCost: number;
}

export interface IncomeSource {
  name: string;
  amount: number;
  frequency: 'monthly' | 'annual' | 'one-time';
  startAge: number;
  endAge: number | null;
}

export interface ExpenseCategory {
  name: string;
  amount: number;
  frequency: 'monthly' | 'annual';
  startAge: number;
  endAge: number | null;
  isEssential: boolean;
}

export interface RetirementTimeline {
  currentAge: number;
  retirementAge: number;
  lifeExpectancy: number;
}

// Define the journey data structure
export interface JourneyData {
  retirementGoals: RetirementGoal[];
  incomeSources: IncomeSource[];
  expenseCategories: ExpenseCategory[];
  timeline: RetirementTimeline;
  lastUpdated: Date | null;
}

// Define the context value type
interface JourneyContextValue {
  currentStep: JourneyStep | null;
  steps: JourneyStep[];
  journeyData: JourneyData;
  isLoading: boolean;
  error: string | null;

  // Navigation methods
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  goToStep: (stepId: string) => void;

  // Data methods
  updateJourneyData: (data: Partial<JourneyData>) => void;
  saveProgress: () => Promise<void>;
  resetJourney: () => void;

  // Step methods
  markStepAsCompleted: (stepId: string) => void;
  isStepCompleted: (stepId: string) => boolean;
}

// Create the context
const JourneyContext = createContext<JourneyContextValue | undefined>(undefined);

// Default journey data
const defaultJourneyData: JourneyData = {
  retirementGoals: [],
  incomeSources: [],
  expenseCategories: [],
  timeline: {
    currentAge: 30,
    retirementAge: 65,
    lifeExpectancy: 90,
  },
  lastUpdated: null,
};

// Default journey steps
const defaultJourneySteps: JourneyStep[] = [
  {
    id: 'retirement-goals',
    title: 'Retirement Goals',
    description: 'Define what you want to achieve in retirement',
    direction: 'east',
    order: 1,
    isCompleted: false,
  },
  {
    id: 'income-sources',
    title: 'Income Sources',
    description: 'Identify your sources of income during retirement',
    direction: 'east',
    order: 2,
    isCompleted: false,
  },
  {
    id: 'expense-categories',
    title: 'Expense Categories',
    description: 'Estimate your expenses during retirement',
    direction: 'east',
    order: 3,
    isCompleted: false,
  },
  {
    id: 'timeline',
    title: 'Retirement Timeline',
    description: 'Plan your retirement timeline',
    direction: 'east',
    order: 4,
    isCompleted: false,
  },
];

// Provider props
interface JourneyProviderProps {
  children: ReactNode;
}

// Journey Provider component
export const JourneyProvider: React.FC<JourneyProviderProps> = ({ children }) => {
  // State
  const [steps, setSteps] = useState<JourneyStep[]>(defaultJourneySteps);
  const [currentStep, setCurrentStep] = useState<JourneyStep | null>(steps[0] || null);
  const [journeyData, setJourneyData] = useState<JourneyData>(defaultJourneyData);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Load saved data on mount
  useEffect(() => {
    const loadSavedData = async () => {
      setIsLoading(true);
      try {
        // In a real app, this would load from localStorage, IndexedDB, or an API
        const savedData = localStorage.getItem('journeyData');
        const savedSteps = localStorage.getItem('journeySteps');

        if (savedData) {
          setJourneyData(JSON.parse(savedData));
        }

        if (savedSteps) {
          const parsedSteps = JSON.parse(savedSteps);
          setSteps(parsedSteps);
          setCurrentStep(parsedSteps[0] || null);
        }
      } catch (err) {
        setError('Failed to load saved journey data');
        console.error('Error loading journey data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadSavedData();
  }, []);

  // Navigation methods
  const goToNextStep = () => {
    if (!currentStep) return;

    const currentIndex = steps.findIndex((step) => step.id === currentStep.id);
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1]);
    }
  };

  const goToPreviousStep = () => {
    if (!currentStep) return;

    const currentIndex = steps.findIndex((step) => step.id === currentStep.id);
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1]);
    }
  };

  const goToStep = (stepId: string) => {
    const step = steps.find((s) => s.id === stepId);
    if (step) {
      setCurrentStep(step);
    }
  };

  // Data methods
  const updateJourneyData = (data: Partial<JourneyData>) => {
    setJourneyData((prevData) => ({
      ...prevData,
      ...data,
      lastUpdated: new Date(),
    }));
  };

  const saveProgress = async () => {
    setIsLoading(true);
    try {
      // In a real app, this would save to localStorage, IndexedDB, or an API
      localStorage.setItem('journeyData', JSON.stringify(journeyData));
      localStorage.setItem('journeySteps', JSON.stringify(steps));
      return Promise.resolve();
    } catch (err) {
      setError('Failed to save journey data');
      console.error('Error saving journey data:', err);
      return Promise.reject(err);
    } finally {
      setIsLoading(false);
    }
  };

  const resetJourney = () => {
    setJourneyData(defaultJourneyData);
    setSteps(defaultJourneySteps);
    setCurrentStep(defaultJourneySteps[0]);
    localStorage.removeItem('journeyData');
    localStorage.removeItem('journeySteps');
  };

  // Step methods
  const markStepAsCompleted = (stepId: string) => {
    setSteps((prevSteps) =>
      prevSteps.map((step) => (step.id === stepId ? { ...step, isCompleted: true } : step))
    );
  };

  const isStepCompleted = (stepId: string) => {
    const step = steps.find((s) => s.id === stepId);
    return step ? step.isCompleted : false;
  };

  // Context value
  const contextValue: JourneyContextValue = {
    currentStep,
    steps,
    journeyData,
    isLoading,
    error,
    goToNextStep,
    goToPreviousStep,
    goToStep,
    updateJourneyData,
    saveProgress,
    resetJourney,
    markStepAsCompleted,
    isStepCompleted,
  };

  return <JourneyContext.Provider value={contextValue}>{children}</JourneyContext.Provider>;
};

// Hook to use journey context
export const useJourney = () => {
  const context = useContext(JourneyContext);
  if (context === undefined) {
    throw new Error('useJourney must be used within a JourneyProvider');
  }
  return context;
};

export default {
  Provider: JourneyProvider,
  useJourney,
};
