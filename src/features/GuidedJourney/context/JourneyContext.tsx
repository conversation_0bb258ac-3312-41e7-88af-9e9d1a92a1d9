/**
 * Journey Context
 *
 * This context provides state and functions for the guided journey experience.
 * It's specifically designed for the East Direction (Retirement Planning) journey.
 */

import React, { createContext, useContext, useState, ReactNode } from 'react';

// Types
export interface RetirementGoal {
  id: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  targetAge?: number;
  estimatedCost: number;
}

export interface IncomeSource {
  id: string;
  name: string;
  amount: number;
  frequency: 'monthly' | 'annual' | 'one-time';
  startAge?: number;
  endAge?: number;
}

export interface ExpenseCategory {
  id: string;
  name: string;
  amount: number;
  frequency: 'monthly' | 'annual' | 'one-time';
  startAge?: number;
  endAge?: number;
  isEssential: boolean;
}

export interface RetirementTimeline {
  currentAge: number;
  retirementAge: number;
  lifeExpectancy: number;
  milestones: {
    id: string;
    age: number;
    description: string;
  }[];
}

export interface JourneyData {
  retirementGoals: RetirementGoal[];
  incomeSources: IncomeSource[];
  expenseCategories: ExpenseCategory[];
  timeline: RetirementTimeline;
}

export interface JourneyStep {
  id: string;
  title: string;
  description: string;
  completed: boolean;
}

interface JourneyContextType {
  currentStep: JourneyStep | null;
  steps: JourneyStep[];
  journeyData: JourneyData;
  updateJourneyData: (data: Partial<JourneyData>) => void;
  goToStep: (stepId: string) => void;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  isStepCompleted: (stepId: string) => boolean;
  markStepAsCompleted: (stepId: string) => void;
  saveProgress: () => Promise<void>;
}

// Default journey data
const defaultJourneyData: JourneyData = {
  retirementGoals: [],
  incomeSources: [],
  expenseCategories: [],
  timeline: {
    currentAge: 35,
    retirementAge: 65,
    lifeExpectancy: 90,
    milestones: [],
  },
};

// Default steps
const defaultSteps: JourneyStep[] = [
  {
    id: 'retirement-goals',
    title: 'Retirement Goals',
    description: 'Define your retirement vision and goals',
    completed: false,
  },
  {
    id: 'income-sources',
    title: 'Income Sources',
    description: 'Identify and estimate your retirement income sources',
    completed: false,
  },
  {
    id: 'expense-categories',
    title: 'Expense Categories',
    description: 'Estimate your expenses during retirement',
    completed: false,
  },
  {
    id: 'timeline',
    title: 'Retirement Timeline',
    description: 'Visualize your retirement journey with key milestones',
    completed: false,
  },
];

// Create context
const JourneyContext = createContext<JourneyContextType | undefined>(undefined);

// Provider props
interface JourneyProviderProps {
  children: ReactNode;
}

/**
 * Journey Provider Component
 */
export const JourneyProvider: React.FC<JourneyProviderProps> = ({ children }) => {
  // State
  const [currentStep, setCurrentStep] = useState<JourneyStep | null>(defaultSteps[0]);
  const [steps, setSteps] = useState<JourneyStep[]>(defaultSteps);
  const [journeyData, setJourneyData] = useState<JourneyData>(defaultJourneyData);

  // Go to a specific step
  const goToStep = (stepId: string) => {
    const step = steps.find((s) => s.id === stepId);
    if (step) {
      setCurrentStep(step);
    }
  };

  // Go to the next step
  const goToNextStep = () => {
    if (currentStep) {
      const currentIndex = steps.findIndex((s) => s.id === currentStep.id);
      if (currentIndex < steps.length - 1) {
        setCurrentStep(steps[currentIndex + 1]);
      }
    }
  };

  // Go to the previous step
  const goToPreviousStep = () => {
    if (currentStep) {
      const currentIndex = steps.findIndex((s) => s.id === currentStep.id);
      if (currentIndex > 0) {
        setCurrentStep(steps[currentIndex - 1]);
      }
    }
  };

  // Check if a step is completed
  const isStepCompleted = (stepId: string): boolean => {
    const step = steps.find((s) => s.id === stepId);
    return step?.completed || false;
  };

  // Mark a step as completed
  const markStepAsCompleted = (stepId: string) => {
    setSteps((prevSteps) =>
      prevSteps.map((step) => (step.id === stepId ? { ...step, completed: true } : step))
    );
  };

  // Update journey data
  const updateJourneyData = (data: Partial<JourneyData>) => {
    setJourneyData((prevData) => ({
      ...prevData,
      ...data,
    }));
  };

  // Save progress
  const saveProgress = async (): Promise<void> => {
    return new Promise((resolve) => {
      // Simulate saving data to a server or local storage
      localStorage.setItem('journey_data', JSON.stringify(journeyData));
      localStorage.setItem('journey_steps', JSON.stringify(steps));

      // Simulate network delay
      setTimeout(() => {
        resolve();
      }, 500);
    });
  };

  // Context value
  const value: JourneyContextType = {
    currentStep,
    steps,
    journeyData,
    updateJourneyData,
    goToStep,
    goToNextStep,
    goToPreviousStep,
    isStepCompleted,
    markStepAsCompleted,
    saveProgress,
  };

  return <JourneyContext.Provider value={value}>{children}</JourneyContext.Provider>;
};

/**
 * Hook to use the journey context
 */
export const useJourney = (): JourneyContextType => {
  const context = useContext(JourneyContext);

  if (context === undefined) {
    throw new Error('useJourney must be used within a JourneyProvider');
  }

  return context;
};
