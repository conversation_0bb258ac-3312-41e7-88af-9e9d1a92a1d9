/**
 * Timeline Step
 *
 * This component allows users to define their retirement timeline.
 */

import React, { useState, useEffect } from 'react';
import { useJourney, RetirementTimeline } from '../GuidedJourneyContext';
import { useTheme } from '../../../theme/ThemeProvider';

const TimelineStep: React.FC = () => {
  const { mode } = useTheme();
  const isDarkMode = mode === 'dark';
  const { journeyData, updateJourneyData, markStepAsCompleted } = useJourney();

  // Local state for the form
  const [timeline, setTimeline] = useState<RetirementTimeline>(journeyData.timeline);

  // Derived state
  const yearsUntilRetirement = timeline.retirementAge - timeline.currentAge;
  const retirementDuration = timeline.lifeExpectancy - timeline.retirementAge;

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Update the journey data
    updateJourneyData({
      timeline: timeline,
    });

    // Mark the step as completed
    markStepAsCompleted('timeline');
  };

  // Handle input changes
  const handleInputChange = (field: keyof RetirementTimeline, value: number) => {
    setTimeline({
      ...timeline,
      [field]: value,
    });
  };

  // Timeline visualization
  const TimelineVisualization: React.FC = () => {
    const totalYears = timeline.lifeExpectancy - timeline.currentAge;
    const preRetirementWidth = (yearsUntilRetirement / totalYears) * 100;
    const retirementWidth = (retirementDuration / totalYears) * 100;

    return (
      <div
        style={{
          marginTop: '30px',
          marginBottom: '30px',
        }}
      >
        <div
          style={{
            display: 'flex',
            height: '40px',
            borderRadius: '4px',
            overflow: 'hidden',
            marginBottom: '10px',
          }}
        >
          <div
            style={{
              width: `${preRetirementWidth}%`,
              backgroundColor: isDarkMode ? '#64B5F6' : '#2196F3',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontWeight: 'bold',
              transition: 'background-color 0.3s ease, width 0.5s ease',
            }}
          >
            {yearsUntilRetirement > 0 ? `${yearsUntilRetirement} years` : ''}
          </div>
          <div
            style={{
              width: `${retirementWidth}%`,
              backgroundColor: isDarkMode ? '#81C784' : '#4CAF50',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontWeight: 'bold',
              transition: 'background-color 0.3s ease, width 0.5s ease',
            }}
          >
            {retirementDuration > 0 ? `${retirementDuration} years` : ''}
          </div>
        </div>

        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            marginBottom: '20px',
          }}
        >
          <div>Current Age: {timeline.currentAge}</div>
          <div>Retirement Age: {timeline.retirementAge}</div>
          <div>Life Expectancy: {timeline.lifeExpectancy}</div>
        </div>

        <div
          style={{
            display: 'flex',
            marginBottom: '10px',
          }}
        >
          <div
            style={{
              width: `${preRetirementWidth}%`,
              textAlign: 'center',
              color: isDarkMode ? '#64B5F6' : '#2196F3',
              fontWeight: 'bold',
              transition: 'color 0.3s ease, width 0.5s ease',
            }}
          >
            Working Years
          </div>
          <div
            style={{
              width: `${retirementWidth}%`,
              textAlign: 'center',
              color: isDarkMode ? '#81C784' : '#4CAF50',
              fontWeight: 'bold',
              transition: 'color 0.3s ease, width 0.5s ease',
            }}
          >
            Retirement Years
          </div>
        </div>
      </div>
    );
  };

  return (
    <div>
      <form onSubmit={handleSubmit}>
        <div style={{ marginBottom: '20px' }}>
          <h3
            style={{
              color: isDarkMode ? '#81C784' : '#4CAF50',
              marginBottom: '10px',
              transition: 'color 0.3s ease',
            }}
          >
            Your Retirement Timeline
          </h3>
          <p>Define your retirement timeline to help plan your financial journey.</p>
        </div>

        {/* Timeline visualization */}
        <div
          style={{
            backgroundColor: isDarkMode ? '#2d2d2d' : '#f9f9f9',
            padding: '20px',
            borderRadius: '4px',
            marginBottom: '30px',
            transition: 'background-color 0.3s ease',
          }}
        >
          <h4
            style={{
              marginBottom: '15px',
              color: isDarkMode ? '#81C784' : '#4CAF50',
              transition: 'color 0.3s ease',
            }}
          >
            Your Life Timeline
          </h4>

          <TimelineVisualization />

          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              marginTop: '20px',
              padding: '15px',
              backgroundColor: isDarkMode ? '#3d3d3d' : 'white',
              borderRadius: '4px',
              boxShadow: isDarkMode ? '0 1px 3px rgba(0,0,0,0.3)' : '0 1px 3px rgba(0,0,0,0.1)',
              transition: 'background-color 0.3s ease, box-shadow 0.3s ease',
            }}
          >
            <div>
              <div style={{ fontWeight: 'bold' }}>Years until retirement:</div>
              <div
                style={{
                  fontSize: '1.5em',
                  color: isDarkMode ? '#64B5F6' : '#2196F3',
                  transition: 'color 0.3s ease',
                }}
              >
                {yearsUntilRetirement}
              </div>
            </div>

            <div>
              <div style={{ fontWeight: 'bold' }}>Years in retirement:</div>
              <div
                style={{
                  fontSize: '1.5em',
                  color: isDarkMode ? '#81C784' : '#4CAF50',
                  transition: 'color 0.3s ease',
                }}
              >
                {retirementDuration}
              </div>
            </div>
          </div>
        </div>

        {/* Timeline form */}
        <div
          style={{
            backgroundColor: isDarkMode ? '#2d2d2d' : '#f9f9f9',
            padding: '20px',
            borderRadius: '4px',
            marginBottom: '20px',
            transition: 'background-color 0.3s ease',
          }}
        >
          <h4
            style={{
              marginBottom: '15px',
              color: isDarkMode ? '#81C784' : '#4CAF50',
              transition: 'color 0.3s ease',
            }}
          >
            Adjust Your Timeline
          </h4>

          <div style={{ marginBottom: '15px' }}>
            <label
              style={{
                display: 'block',
                marginBottom: '5px',
                fontWeight: 'bold',
              }}
            >
              Current Age:
            </label>
            <input
              type="number"
              value={timeline.currentAge}
              onChange={(e) => handleInputChange('currentAge', parseInt(e.target.value) || 0)}
              style={{
                width: '100%',
                padding: '10px',
                borderRadius: '4px',
                border: isDarkMode ? '1px solid #555' : '1px solid #ddd',
                backgroundColor: isDarkMode ? '#3d3d3d' : 'white',
                color: isDarkMode ? '#e0e0e0' : 'inherit',
                transition: 'background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease',
              }}
              min="18"
              max={timeline.retirementAge - 1}
            />
          </div>

          <div style={{ marginBottom: '15px' }}>
            <label
              style={{
                display: 'block',
                marginBottom: '5px',
                fontWeight: 'bold',
              }}
            >
              Retirement Age:
            </label>
            <input
              type="number"
              value={timeline.retirementAge}
              onChange={(e) => handleInputChange('retirementAge', parseInt(e.target.value) || 0)}
              style={{
                width: '100%',
                padding: '10px',
                borderRadius: '4px',
                border: isDarkMode ? '1px solid #555' : '1px solid #ddd',
                backgroundColor: isDarkMode ? '#3d3d3d' : 'white',
                color: isDarkMode ? '#e0e0e0' : 'inherit',
                transition: 'background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease',
              }}
              min={timeline.currentAge + 1}
              max={timeline.lifeExpectancy - 1}
            />
          </div>

          <div style={{ marginBottom: '15px' }}>
            <label
              style={{
                display: 'block',
                marginBottom: '5px',
                fontWeight: 'bold',
              }}
            >
              Life Expectancy:
            </label>
            <input
              type="number"
              value={timeline.lifeExpectancy}
              onChange={(e) => handleInputChange('lifeExpectancy', parseInt(e.target.value) || 0)}
              style={{
                width: '100%',
                padding: '10px',
                borderRadius: '4px',
                border: isDarkMode ? '1px solid #555' : '1px solid #ddd',
                backgroundColor: isDarkMode ? '#3d3d3d' : 'white',
                color: isDarkMode ? '#e0e0e0' : 'inherit',
                transition: 'background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease',
              }}
              min={timeline.retirementAge + 1}
              max="120"
            />
          </div>
        </div>

        <button
          type="submit"
          style={{
            backgroundColor: isDarkMode ? '#64B5F6' : '#2196F3',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            padding: '10px 20px',
            cursor: 'pointer',
            transition: 'background-color 0.3s ease',
          }}
        >
          Save Timeline
        </button>
      </form>
    </div>
  );
};

export default TimelineStep;
