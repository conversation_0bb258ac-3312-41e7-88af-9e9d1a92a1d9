/**
 * Income Sources Step
 *
 * This component allows users to define their income sources during retirement.
 */

import React, { useState } from 'react';
import { useJourney, IncomeSource } from '../GuidedJourneyContext';
import { useTheme } from '../../../theme/ThemeProvider';

const IncomeSourcesStep: React.FC = () => {
  const { mode } = useTheme();
  const isDarkMode = mode === 'dark';
  const { journeyData, updateJourneyData, markStepAsCompleted } = useJourney();

  // Local state for the form
  const [incomeSources, setIncomeSources] = useState<IncomeSource[]>(journeyData.incomeSources);
  const [newSource, setNewSource] = useState<Omit<IncomeSource, 'id'> & { id?: string }>({
    name: '',
    amount: 0,
    frequency: 'monthly',
    startAge: journeyData.timeline.retirementAge,
    endAge: undefined,
  });

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Update the journey data
    updateJourneyData({
      incomeSources: incomeSources,
    });

    // Mark the step as completed
    markStepAsCompleted('income-sources');
  };

  // Add a new income source
  const handleAddSource = () => {
    if (newSource.name.trim() === '') {
      alert('Please enter an income source name');
      return;
    }

    // Generate a unique ID for the new income source
    const newSourceWithId = {
      ...newSource,
      id: `income-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
    };
    setIncomeSources([...incomeSources, newSourceWithId]);
    setNewSource({
      name: '',
      amount: 0,
      frequency: 'monthly',
      startAge: journeyData.timeline.retirementAge,
      endAge: undefined,
    });
  };

  // Remove an income source
  const handleRemoveSource = (index: number) => {
    const updatedSources = [...incomeSources];
    updatedSources.splice(index, 1);
    setIncomeSources(updatedSources);
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
  };

  // Calculate annual amount
  const calculateAnnualAmount = (source: IncomeSource) => {
    switch (source.frequency) {
      case 'monthly':
        return source.amount * 12;
      case 'annual':
        return source.amount;
      case 'one-time':
        return (
          source.amount /
          ((source.endAge || journeyData.timeline.lifeExpectancy) -
            (source.startAge || journeyData.timeline.currentAge))
        );
      default:
        return source.amount;
    }
  };

  return (
    <div>
      <form onSubmit={handleSubmit}>
        <div style={{ marginBottom: '20px' }}>
          <h3
            style={{
              color: isDarkMode ? '#81C784' : '#4CAF50',
              marginBottom: '10px',
              transition: 'color 0.3s ease',
            }}
          >
            Your Retirement Income Sources
          </h3>
          <p>What sources of income will you have during retirement? Add them below.</p>
        </div>

        {/* List of income sources */}
        {incomeSources.length > 0 ? (
          <div
            style={{
              marginBottom: '30px',
              backgroundColor: isDarkMode ? '#2d2d2d' : '#f9f9f9',
              padding: '15px',
              borderRadius: '4px',
              transition: 'background-color 0.3s ease',
            }}
          >
            {incomeSources.map((source, index) => (
              <div
                key={index}
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '10px',
                  marginBottom: '10px',
                  backgroundColor: isDarkMode ? '#3d3d3d' : 'white',
                  borderRadius: '4px',
                  boxShadow: isDarkMode ? '0 1px 3px rgba(0,0,0,0.3)' : '0 1px 3px rgba(0,0,0,0.1)',
                  transition: 'background-color 0.3s ease, box-shadow 0.3s ease',
                }}
              >
                <div>
                  <div style={{ fontWeight: 'bold' }}>{source.name}</div>
                  <div
                    style={{
                      fontSize: '0.9em',
                      color: isDarkMode ? '#aaa' : '#666',
                      transition: 'color 0.3s ease',
                    }}
                  >
                    {formatCurrency(source.amount)} ({source.frequency})
                  </div>
                  <div
                    style={{
                      fontSize: '0.9em',
                      color: isDarkMode ? '#aaa' : '#666',
                      transition: 'color 0.3s ease',
                    }}
                  >
                    Ages {source.startAge} to {source.endAge || 'end of life'}
                  </div>
                </div>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div style={{ marginRight: '15px' }}>
                    {formatCurrency(calculateAnnualAmount(source))}/year
                  </div>
                  <button
                    type="button"
                    onClick={() => handleRemoveSource(index)}
                    style={{
                      backgroundColor: isDarkMode ? '#d32f2f' : '#f44336',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      padding: '5px 10px',
                      cursor: 'pointer',
                      transition: 'background-color 0.3s ease',
                    }}
                  >
                    Remove
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div
            style={{
              padding: '20px',
              textAlign: 'center',
              backgroundColor: isDarkMode ? '#2d2d2d' : '#f9f9f9',
              borderRadius: '4px',
              marginBottom: '20px',
              transition: 'background-color 0.3s ease',
            }}
          >
            No income sources added yet. Add your first income source below.
          </div>
        )}

        {/* Add new income source form */}
        <div
          style={{
            backgroundColor: isDarkMode ? '#2d2d2d' : '#f9f9f9',
            padding: '20px',
            borderRadius: '4px',
            marginBottom: '20px',
            transition: 'background-color 0.3s ease',
          }}
        >
          <h4
            style={{
              marginBottom: '15px',
              color: isDarkMode ? '#81C784' : '#4CAF50',
              transition: 'color 0.3s ease',
            }}
          >
            Add a New Income Source
          </h4>

          <div style={{ marginBottom: '15px' }}>
            <label
              style={{
                display: 'block',
                marginBottom: '5px',
                fontWeight: 'bold',
              }}
            >
              Source Name:
            </label>
            <input
              type="text"
              value={newSource.name}
              onChange={(e) => setNewSource({ ...newSource, name: e.target.value })}
              style={{
                width: '100%',
                padding: '10px',
                borderRadius: '4px',
                border: isDarkMode ? '1px solid #555' : '1px solid #ddd',
                backgroundColor: isDarkMode ? '#3d3d3d' : 'white',
                color: isDarkMode ? '#e0e0e0' : 'inherit',
                transition: 'background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease',
              }}
              placeholder="e.g., Social Security, Pension, 401(k)"
            />
          </div>

          <div style={{ marginBottom: '15px' }}>
            <label
              style={{
                display: 'block',
                marginBottom: '5px',
                fontWeight: 'bold',
              }}
            >
              Amount:
            </label>
            <input
              type="number"
              value={newSource.amount}
              onChange={(e) =>
                setNewSource({ ...newSource, amount: parseFloat(e.target.value) || 0 })
              }
              style={{
                width: '100%',
                padding: '10px',
                borderRadius: '4px',
                border: isDarkMode ? '1px solid #555' : '1px solid #ddd',
                backgroundColor: isDarkMode ? '#3d3d3d' : 'white',
                color: isDarkMode ? '#e0e0e0' : 'inherit',
                transition: 'background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease',
              }}
              placeholder="0"
              min="0"
              step="100"
            />
          </div>

          <div style={{ marginBottom: '15px' }}>
            <label
              style={{
                display: 'block',
                marginBottom: '5px',
                fontWeight: 'bold',
              }}
            >
              Frequency:
            </label>
            <select
              value={newSource.frequency}
              onChange={(e) =>
                setNewSource({
                  ...newSource,
                  frequency: e.target.value as 'monthly' | 'annual' | 'one-time',
                })
              }
              style={{
                width: '100%',
                padding: '10px',
                borderRadius: '4px',
                border: isDarkMode ? '1px solid #555' : '1px solid #ddd',
                backgroundColor: isDarkMode ? '#3d3d3d' : 'white',
                color: isDarkMode ? '#e0e0e0' : 'inherit',
                transition: 'background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease',
              }}
            >
              <option value="monthly">Monthly</option>
              <option value="annual">Annual</option>
              <option value="one-time">One-time</option>
            </select>
          </div>

          <div style={{ display: 'flex', gap: '15px', marginBottom: '15px' }}>
            <div style={{ flex: 1 }}>
              <label
                style={{
                  display: 'block',
                  marginBottom: '5px',
                  fontWeight: 'bold',
                }}
              >
                Start Age:
              </label>
              <input
                type="number"
                value={newSource.startAge}
                onChange={(e) =>
                  setNewSource({
                    ...newSource,
                    startAge: parseInt(e.target.value) || journeyData.timeline.retirementAge,
                  })
                }
                style={{
                  width: '100%',
                  padding: '10px',
                  borderRadius: '4px',
                  border: isDarkMode ? '1px solid #555' : '1px solid #ddd',
                  backgroundColor: isDarkMode ? '#3d3d3d' : 'white',
                  color: isDarkMode ? '#e0e0e0' : 'inherit',
                  transition: 'background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease',
                }}
                min={journeyData.timeline.currentAge}
                max={journeyData.timeline.lifeExpectancy}
              />
            </div>

            <div style={{ flex: 1 }}>
              <label
                style={{
                  display: 'block',
                  marginBottom: '5px',
                  fontWeight: 'bold',
                }}
              >
                End Age (optional):
              </label>
              <input
                type="number"
                value={newSource.endAge || ''}
                onChange={(e) => {
                  const value = e.target.value === '' ? null : parseInt(e.target.value);
                  setNewSource({ ...newSource, endAge: value === null ? undefined : value });
                }}
                style={{
                  width: '100%',
                  padding: '10px',
                  borderRadius: '4px',
                  border: isDarkMode ? '1px solid #555' : '1px solid #ddd',
                  backgroundColor: isDarkMode ? '#3d3d3d' : 'white',
                  color: isDarkMode ? '#e0e0e0' : 'inherit',
                  transition: 'background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease',
                }}
                placeholder="Leave blank for lifetime"
                min={newSource.startAge}
                max={journeyData.timeline.lifeExpectancy}
              />
            </div>
          </div>

          <button
            type="button"
            onClick={handleAddSource}
            style={{
              backgroundColor: isDarkMode ? '#81C784' : '#4CAF50',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              padding: '10px 15px',
              cursor: 'pointer',
              transition: 'background-color 0.3s ease',
            }}
          >
            Add Income Source
          </button>
        </div>

        <button
          type="submit"
          style={{
            backgroundColor: isDarkMode ? '#64B5F6' : '#2196F3',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            padding: '10px 20px',
            cursor: 'pointer',
            transition: 'background-color 0.3s ease',
          }}
        >
          Save Income Sources
        </button>
      </form>
    </div>
  );
};

export default IncomeSourcesStep;
