/**
 * Retirement Goals Step
 *
 * This component allows users to define their retirement goals.
 */

import React, { useState } from 'react';
import { useJourney, RetirementGoal } from '../GuidedJourneyContext';
import { useTheme } from '../../../theme/ThemeProvider';

const RetirementGoalsStep: React.FC = () => {
  const { mode } = useTheme();
  const isDarkMode = mode === 'dark';
  const { journeyData, updateJourneyData, markStepAsCompleted } = useJourney();

  // Local state for the form
  const [goals, setGoals] = useState<RetirementGoal[]>(journeyData.retirementGoals);
  const [newGoal, setNewGoal] = useState<Omit<RetirementGoal, 'id'> & { id?: string }>({
    description: '',
    priority: 'medium',
    estimatedCost: 0,
  });

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Update the journey data
    updateJourneyData({
      retirementGoals: goals,
    });

    // Mark the step as completed
    markStepAsCompleted('retirement-goals');
  };

  // Add a new goal
  const handleAddGoal = () => {
    if (newGoal.description.trim() === '') {
      alert('Please enter a goal description');
      return;
    }

    // Generate a unique ID for the new goal
    const newGoalWithId = {
      ...newGoal,
      id: `goal-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
    };
    setGoals([...goals, newGoalWithId]);
    setNewGoal({
      description: '',
      priority: 'medium',
      estimatedCost: 0,
    });
  };

  // Remove a goal
  const handleRemoveGoal = (index: number) => {
    const updatedGoals = [...goals];
    updatedGoals.splice(index, 1);
    setGoals(updatedGoals);
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
  };

  return (
    <div>
      <form onSubmit={handleSubmit}>
        <div style={{ marginBottom: '20px' }}>
          <h3
            style={{
              color: isDarkMode ? '#81C784' : '#4CAF50',
              marginBottom: '10px',
              transition: 'color 0.3s ease',
            }}
          >
            Your Retirement Goals
          </h3>
          <p>What do you want to achieve during retirement? Add your goals below.</p>
        </div>

        {/* List of goals */}
        {goals.length > 0 ? (
          <div
            style={{
              marginBottom: '30px',
              backgroundColor: isDarkMode ? '#2d2d2d' : '#f9f9f9',
              padding: '15px',
              borderRadius: '4px',
              transition: 'background-color 0.3s ease',
            }}
          >
            {goals.map((goal, index) => (
              <div
                key={index}
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '10px',
                  marginBottom: '10px',
                  backgroundColor: isDarkMode ? '#3d3d3d' : 'white',
                  borderRadius: '4px',
                  boxShadow: isDarkMode ? '0 1px 3px rgba(0,0,0,0.3)' : '0 1px 3px rgba(0,0,0,0.1)',
                  transition: 'background-color 0.3s ease, box-shadow 0.3s ease',
                }}
              >
                <div>
                  <div style={{ fontWeight: 'bold' }}>{goal.description}</div>
                  <div
                    style={{
                      fontSize: '0.9em',
                      color: isDarkMode ? '#aaa' : '#666',
                      transition: 'color 0.3s ease',
                    }}
                  >
                    Priority: {goal.priority.charAt(0).toUpperCase() + goal.priority.slice(1)}
                  </div>
                </div>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div style={{ marginRight: '15px' }}>{formatCurrency(goal.estimatedCost)}</div>
                  <button
                    type="button"
                    onClick={() => handleRemoveGoal(index)}
                    style={{
                      backgroundColor: isDarkMode ? '#d32f2f' : '#f44336',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      padding: '5px 10px',
                      cursor: 'pointer',
                      transition: 'background-color 0.3s ease',
                    }}
                  >
                    Remove
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div
            style={{
              padding: '20px',
              textAlign: 'center',
              backgroundColor: isDarkMode ? '#2d2d2d' : '#f9f9f9',
              borderRadius: '4px',
              marginBottom: '20px',
              transition: 'background-color 0.3s ease',
            }}
          >
            No goals added yet. Add your first retirement goal below.
          </div>
        )}

        {/* Add new goal form */}
        <div
          style={{
            backgroundColor: isDarkMode ? '#2d2d2d' : '#f9f9f9',
            padding: '20px',
            borderRadius: '4px',
            marginBottom: '20px',
            transition: 'background-color 0.3s ease',
          }}
        >
          <h4
            style={{
              marginBottom: '15px',
              color: isDarkMode ? '#81C784' : '#4CAF50',
              transition: 'color 0.3s ease',
            }}
          >
            Add a New Goal
          </h4>

          <div style={{ marginBottom: '15px' }}>
            <label
              style={{
                display: 'block',
                marginBottom: '5px',
                fontWeight: 'bold',
              }}
            >
              Goal Description:
            </label>
            <input
              type="text"
              value={newGoal.description}
              onChange={(e) => setNewGoal({ ...newGoal, description: e.target.value })}
              style={{
                width: '100%',
                padding: '10px',
                borderRadius: '4px',
                border: isDarkMode ? '1px solid #555' : '1px solid #ddd',
                backgroundColor: isDarkMode ? '#3d3d3d' : 'white',
                color: isDarkMode ? '#e0e0e0' : 'inherit',
                transition: 'background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease',
              }}
              placeholder="e.g., Travel the world, Buy a vacation home"
            />
          </div>

          <div style={{ marginBottom: '15px' }}>
            <label
              style={{
                display: 'block',
                marginBottom: '5px',
                fontWeight: 'bold',
              }}
            >
              Priority:
            </label>
            <select
              value={newGoal.priority}
              onChange={(e) =>
                setNewGoal({ ...newGoal, priority: e.target.value as 'high' | 'medium' | 'low' })
              }
              style={{
                width: '100%',
                padding: '10px',
                borderRadius: '4px',
                border: isDarkMode ? '1px solid #555' : '1px solid #ddd',
                backgroundColor: isDarkMode ? '#3d3d3d' : 'white',
                color: isDarkMode ? '#e0e0e0' : 'inherit',
                transition: 'background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease',
              }}
            >
              <option value="high">High Priority</option>
              <option value="medium">Medium Priority</option>
              <option value="low">Low Priority</option>
            </select>
          </div>

          <div style={{ marginBottom: '15px' }}>
            <label
              style={{
                display: 'block',
                marginBottom: '5px',
                fontWeight: 'bold',
              }}
            >
              Estimated Cost:
            </label>
            <input
              type="number"
              value={newGoal.estimatedCost}
              onChange={(e) =>
                setNewGoal({ ...newGoal, estimatedCost: parseFloat(e.target.value) || 0 })
              }
              style={{
                width: '100%',
                padding: '10px',
                borderRadius: '4px',
                border: isDarkMode ? '1px solid #555' : '1px solid #ddd',
                backgroundColor: isDarkMode ? '#3d3d3d' : 'white',
                color: isDarkMode ? '#e0e0e0' : 'inherit',
                transition: 'background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease',
              }}
              placeholder="0"
              min="0"
              step="1000"
            />
          </div>

          <button
            type="button"
            onClick={handleAddGoal}
            style={{
              backgroundColor: isDarkMode ? '#81C784' : '#4CAF50',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              padding: '10px 15px',
              cursor: 'pointer',
              transition: 'background-color 0.3s ease',
            }}
          >
            Add Goal
          </button>
        </div>

        <button
          type="submit"
          style={{
            backgroundColor: isDarkMode ? '#64B5F6' : '#2196F3',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            padding: '10px 20px',
            cursor: 'pointer',
            transition: 'background-color 0.3s ease',
          }}
        >
          Save Goals
        </button>
      </form>
    </div>
  );
};

export default RetirementGoalsStep;
