/**
 * Expense Categories Step
 *
 * This component allows users to define their expense categories during retirement.
 */

import React, { useState } from 'react';
import { useJourney, ExpenseCategory } from '../GuidedJourneyContext';
import { useTheme } from '../../../theme/ThemeProvider';

const ExpenseCategoriesStep: React.FC = () => {
  const { mode } = useTheme();
  const isDarkMode = mode === 'dark';
  const { journeyData, updateJourneyData, markStepAsCompleted } = useJourney();

  // Local state for the form
  const [expenses, setExpenses] = useState<ExpenseCategory[]>(journeyData.expenseCategories);
  const [newExpense, setNewExpense] = useState<Omit<ExpenseCategory, 'id'> & { id?: string }>({
    name: '',
    amount: 0,
    frequency: 'monthly',
    startAge: journeyData.timeline.retirementAge,
    endAge: undefined,
    isEssential: true,
  });

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Update the journey data
    updateJourneyData({
      expenseCategories: expenses,
    });

    // Mark the step as completed
    markStepAsCompleted('expense-categories');
  };

  // Add a new expense category
  const handleAddExpense = () => {
    if (newExpense.name.trim() === '') {
      alert('Please enter an expense category name');
      return;
    }

    // Generate a unique ID for the new expense
    const newExpenseWithId = {
      ...newExpense,
      id: `expense-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
    };
    setExpenses([...expenses, newExpenseWithId]);
    setNewExpense({
      name: '',
      amount: 0,
      frequency: 'monthly',
      startAge: journeyData.timeline.retirementAge,
      endAge: undefined,
      isEssential: true,
    });
  };

  // Remove an expense category
  const handleRemoveExpense = (index: number) => {
    const updatedExpenses = [...expenses];
    updatedExpenses.splice(index, 1);
    setExpenses(updatedExpenses);
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
  };

  // Calculate annual amount
  const calculateAnnualAmount = (expense: ExpenseCategory) => {
    switch (expense.frequency) {
      case 'monthly':
        return expense.amount * 12;
      case 'annual':
        return expense.amount;
      default:
        return expense.amount;
    }
  };

  // Calculate total annual expenses
  const totalAnnualExpenses = expenses.reduce((total, expense) => {
    return total + calculateAnnualAmount(expense);
  }, 0);

  // Calculate total essential expenses
  const totalEssentialExpenses = expenses
    .filter((expense) => expense.isEssential)
    .reduce((total, expense) => {
      return total + calculateAnnualAmount(expense);
    }, 0);

  return (
    <div>
      <form onSubmit={handleSubmit}>
        <div style={{ marginBottom: '20px' }}>
          <h3
            style={{
              color: isDarkMode ? '#81C784' : '#4CAF50',
              marginBottom: '10px',
              transition: 'color 0.3s ease',
            }}
          >
            Your Retirement Expenses
          </h3>
          <p>What expenses do you expect to have during retirement? Add them below.</p>
        </div>

        {/* Summary */}
        <div
          style={{
            backgroundColor: isDarkMode ? '#2d2d2d' : '#f9f9f9',
            padding: '15px',
            borderRadius: '4px',
            marginBottom: '20px',
            transition: 'background-color 0.3s ease',
          }}
        >
          <h4
            style={{
              marginBottom: '10px',
              color: isDarkMode ? '#81C784' : '#4CAF50',
              transition: 'color 0.3s ease',
            }}
          >
            Expense Summary
          </h4>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px' }}>
            <div>Total Annual Expenses:</div>
            <div style={{ fontWeight: 'bold' }}>{formatCurrency(totalAnnualExpenses)}</div>
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <div>Essential Expenses:</div>
            <div style={{ fontWeight: 'bold' }}>{formatCurrency(totalEssentialExpenses)}</div>
          </div>
        </div>

        {/* List of expense categories */}
        {expenses.length > 0 ? (
          <div
            style={{
              marginBottom: '30px',
              backgroundColor: isDarkMode ? '#2d2d2d' : '#f9f9f9',
              padding: '15px',
              borderRadius: '4px',
              transition: 'background-color 0.3s ease',
            }}
          >
            {expenses.map((expense, index) => (
              <div
                key={index}
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '10px',
                  marginBottom: '10px',
                  backgroundColor: isDarkMode ? '#3d3d3d' : 'white',
                  borderRadius: '4px',
                  boxShadow: isDarkMode ? '0 1px 3px rgba(0,0,0,0.3)' : '0 1px 3px rgba(0,0,0,0.1)',
                  borderLeft: `4px solid ${expense.isEssential ? (isDarkMode ? '#f44336' : '#f44336') : isDarkMode ? '#4CAF50' : '#4CAF50'}`,
                  transition: 'background-color 0.3s ease, box-shadow 0.3s ease',
                }}
              >
                <div>
                  <div style={{ fontWeight: 'bold' }}>{expense.name}</div>
                  <div
                    style={{
                      fontSize: '0.9em',
                      color: isDarkMode ? '#aaa' : '#666',
                      transition: 'color 0.3s ease',
                    }}
                  >
                    {formatCurrency(expense.amount)} ({expense.frequency})
                  </div>
                  <div
                    style={{
                      fontSize: '0.9em',
                      color: isDarkMode ? '#aaa' : '#666',
                      transition: 'color 0.3s ease',
                    }}
                  >
                    {expense.isEssential ? 'Essential' : 'Discretionary'} expense
                  </div>
                </div>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div style={{ marginRight: '15px' }}>
                    {formatCurrency(calculateAnnualAmount(expense))}/year
                  </div>
                  <button
                    type="button"
                    onClick={() => handleRemoveExpense(index)}
                    style={{
                      backgroundColor: isDarkMode ? '#d32f2f' : '#f44336',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      padding: '5px 10px',
                      cursor: 'pointer',
                      transition: 'background-color 0.3s ease',
                    }}
                  >
                    Remove
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div
            style={{
              padding: '20px',
              textAlign: 'center',
              backgroundColor: isDarkMode ? '#2d2d2d' : '#f9f9f9',
              borderRadius: '4px',
              marginBottom: '20px',
              transition: 'background-color 0.3s ease',
            }}
          >
            No expense categories added yet. Add your first expense category below.
          </div>
        )}

        {/* Add new expense category form */}
        <div
          style={{
            backgroundColor: isDarkMode ? '#2d2d2d' : '#f9f9f9',
            padding: '20px',
            borderRadius: '4px',
            marginBottom: '20px',
            transition: 'background-color 0.3s ease',
          }}
        >
          <h4
            style={{
              marginBottom: '15px',
              color: isDarkMode ? '#81C784' : '#4CAF50',
              transition: 'color 0.3s ease',
            }}
          >
            Add a New Expense Category
          </h4>

          <div style={{ marginBottom: '15px' }}>
            <label
              style={{
                display: 'block',
                marginBottom: '5px',
                fontWeight: 'bold',
              }}
            >
              Category Name:
            </label>
            <input
              type="text"
              value={newExpense.name}
              onChange={(e) => setNewExpense({ ...newExpense, name: e.target.value })}
              style={{
                width: '100%',
                padding: '10px',
                borderRadius: '4px',
                border: isDarkMode ? '1px solid #555' : '1px solid #ddd',
                backgroundColor: isDarkMode ? '#3d3d3d' : 'white',
                color: isDarkMode ? '#e0e0e0' : 'inherit',
                transition: 'background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease',
              }}
              placeholder="e.g., Housing, Healthcare, Travel"
            />
          </div>

          <div style={{ marginBottom: '15px' }}>
            <label
              style={{
                display: 'block',
                marginBottom: '5px',
                fontWeight: 'bold',
              }}
            >
              Amount:
            </label>
            <input
              type="number"
              value={newExpense.amount}
              onChange={(e) =>
                setNewExpense({ ...newExpense, amount: parseFloat(e.target.value) || 0 })
              }
              style={{
                width: '100%',
                padding: '10px',
                borderRadius: '4px',
                border: isDarkMode ? '1px solid #555' : '1px solid #ddd',
                backgroundColor: isDarkMode ? '#3d3d3d' : 'white',
                color: isDarkMode ? '#e0e0e0' : 'inherit',
                transition: 'background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease',
              }}
              placeholder="0"
              min="0"
              step="100"
            />
          </div>

          <div style={{ marginBottom: '15px' }}>
            <label
              style={{
                display: 'block',
                marginBottom: '5px',
                fontWeight: 'bold',
              }}
            >
              Frequency:
            </label>
            <select
              value={newExpense.frequency}
              onChange={(e) =>
                setNewExpense({ ...newExpense, frequency: e.target.value as 'monthly' | 'annual' })
              }
              style={{
                width: '100%',
                padding: '10px',
                borderRadius: '4px',
                border: isDarkMode ? '1px solid #555' : '1px solid #ddd',
                backgroundColor: isDarkMode ? '#3d3d3d' : 'white',
                color: isDarkMode ? '#e0e0e0' : 'inherit',
                transition: 'background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease',
              }}
            >
              <option value="monthly">Monthly</option>
              <option value="annual">Annual</option>
            </select>
          </div>

          <div style={{ marginBottom: '15px' }}>
            <label
              style={{
                display: 'flex',
                alignItems: 'center',
                cursor: 'pointer',
              }}
            >
              <input
                type="checkbox"
                checked={newExpense.isEssential}
                onChange={(e) => setNewExpense({ ...newExpense, isEssential: e.target.checked })}
                style={{ marginRight: '10px' }}
              />
              <span>This is an essential expense (required for basic needs)</span>
            </label>
          </div>

          <button
            type="button"
            onClick={handleAddExpense}
            style={{
              backgroundColor: isDarkMode ? '#81C784' : '#4CAF50',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              padding: '10px 15px',
              cursor: 'pointer',
              transition: 'background-color 0.3s ease',
            }}
          >
            Add Expense Category
          </button>
        </div>

        <button
          type="submit"
          style={{
            backgroundColor: isDarkMode ? '#64B5F6' : '#2196F3',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            padding: '10px 20px',
            cursor: 'pointer',
            transition: 'background-color 0.3s ease',
          }}
        >
          Save Expense Categories
        </button>
      </form>
    </div>
  );
};

export default ExpenseCategoriesStep;
