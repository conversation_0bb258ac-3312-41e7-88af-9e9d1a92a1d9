/**
 * Direction Card Component
 *
 * This component displays a card for a compass direction with its features and progress.
 */

import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import getSafeTheme from '../../../theme/withSafeTheme';

// Types
export interface DirectionFeature {
  id: string;
  title: string;
  description: string;
  icon?: string;
  isCompleted?: boolean;
}

export interface DirectionCardProps {
  title: string;
  description: string;
  color: string;
  progress: number;
  features: DirectionFeature[];
  onClick?: () => void;
}

// Styled Components
const Card = styled(motion.div)<{ cardColor: string }>`
  border-radius: ${({ theme }) => getSafeTheme(theme, (t) => t.borderRadius.lg)};
  background-color: ${({ theme }) => getSafeTheme(theme, (t) => t.colors.background.paper)};
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border-top: 4px solid ${({ cardColor }) => cardColor};
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
  }
`;

const CardHeader = styled.div<{ cardColor: string }>`
  padding: 1.5rem;
  background-color: ${({ theme }) => getSafeTheme(theme, (t) => t.colors.background.default)};
  border-bottom: 1px solid ${({ theme }) => getSafeTheme(theme, (t) => t.colors.divider)};
`;

const CardTitle = styled.h3`
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: ${({ theme }) => getSafeTheme(theme, (t) => t.colors.text.primary)};
`;

const CardDescription = styled.p`
  margin: 0;
  color: ${({ theme }) => getSafeTheme(theme, (t) => t.colors.text.secondary)};
  font-size: 0.875rem;
  line-height: 1.5;
`;

const CardContent = styled.div`
  padding: 1.5rem;
`;

const ProgressContainer = styled.div`
  margin-bottom: 1.5rem;
`;

const ProgressLabel = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
`;

const ProgressText = styled.span`
  font-size: 0.875rem;
  color: ${({ theme }) => getSafeTheme(theme, (t) => t.colors.text.secondary)};
`;

const ProgressBar = styled.div`
  height: 8px;
  background-color: ${({ theme }) => getSafeTheme(theme, (t) => t.colors.background.default)};
  border-radius: 4px;
  overflow: hidden;
`;

const ProgressFill = styled.div<{ progress: number; color: string }>`
  height: 100%;
  width: ${({ progress }) => `${progress}%`};
  background-color: ${({ color }) => color};
  transition: width 0.5s ease;
`;

const FeaturesList = styled.ul`
  margin: 0;
  padding: 0;
  list-style: none;
`;

const FeatureItem = styled.li<{ isCompleted?: boolean }>`
  display: flex;
  align-items: flex-start;
  padding: 0.75rem 0;
  border-bottom: 1px solid ${({ theme }) => getSafeTheme(theme, (t) => t.colors.divider)};
  opacity: ${({ isCompleted }) => (isCompleted ? 0.7 : 1)};

  &:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }
`;

const FeatureIcon = styled.div<{ isCompleted?: boolean; color: string }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 0.75rem;
  background-color: ${({ isCompleted, color, theme }) =>
    isCompleted ? color : getSafeTheme(theme, (t) => t.colors.background.default)};
  color: ${({ isCompleted, theme }) =>
    isCompleted ? '#FFFFFF' : getSafeTheme(theme, (t) => t.colors.text.secondary)};
  font-size: 0.75rem;
`;

const FeatureContent = styled.div`
  flex: 1;
`;

const FeatureTitle = styled.h4<{ isCompleted?: boolean }>`
  margin: 0 0 0.25rem 0;
  font-size: 0.9375rem;
  font-weight: ${({ isCompleted }) => (isCompleted ? 'normal' : 'medium')};
  color: ${({ theme }) => getSafeTheme(theme, (t) => t.colors.text.primary)};
  text-decoration: ${({ isCompleted }) => (isCompleted ? 'line-through' : 'none')};
`;

const FeatureDescription = styled.p`
  margin: 0;
  font-size: 0.8125rem;
  color: ${({ theme }) => getSafeTheme(theme, (t) => t.colors.text.secondary)};
  line-height: 1.4;
`;

/**
 * Direction Card Component
 */
const DirectionCard: React.FC<DirectionCardProps> = ({
  title,
  description,
  color,
  progress,
  features,
  onClick,
}) => {
  return (
    <Card cardColor={color} onClick={onClick} whileHover={{ y: -4 }} transition={{ duration: 0.2 }}>
      <CardHeader cardColor={color}>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>

      <CardContent>
        <ProgressContainer>
          <ProgressLabel>
            <ProgressText>Progress</ProgressText>
            <ProgressText>{progress}%</ProgressText>
          </ProgressLabel>
          <ProgressBar>
            <ProgressFill progress={progress} color={color} />
          </ProgressBar>
        </ProgressContainer>

        <FeaturesList>
          {features.map((feature) => (
            <FeatureItem key={feature.id} isCompleted={feature.isCompleted}>
              <FeatureIcon isCompleted={feature.isCompleted} color={color}>
                {feature.isCompleted ? '✓' : feature.icon || '○'}
              </FeatureIcon>
              <FeatureContent>
                <FeatureTitle isCompleted={feature.isCompleted}>{feature.title}</FeatureTitle>
                <FeatureDescription>{feature.description}</FeatureDescription>
              </FeatureContent>
            </FeatureItem>
          ))}
        </FeaturesList>
      </CardContent>
    </Card>
  );
};

export default DirectionCard;
