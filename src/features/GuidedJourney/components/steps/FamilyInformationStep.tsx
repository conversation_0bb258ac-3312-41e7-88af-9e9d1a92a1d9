/**
 * Family Information Step Component
 *
 * This component collects information about the user's family as part of the guided journey.
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { useGuidedJourney } from '../../context/GuidedJourneyContext';
import { useFinancialCompass } from '../../../FinancialCompass/context/FinancialCompassContext';
import { FamilyMember, FamilyInformationData } from '../../../../types/northDirection';

// Styled Components
const StepContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Label = styled.label`
  font-weight: 500;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const Input = styled.input`
  padding: 0.75rem;
  border: 1px solid ${({ theme }) => theme.colors.divider};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: 1rem;
  width: 100%;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary.main};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light};
  }
`;

const Select = styled.select`
  padding: 0.75rem;
  border: 1px solid ${({ theme }) => theme.colors.divider};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: 1rem;
  width: 100%;
  background-color: ${({ theme }) => theme.colors.background.paper};

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary.main};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light};
  }
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
`;

const Checkbox = styled.input`
  margin-right: 0.5rem;
`;

const CheckboxLabel = styled.label`
  display: flex;
  align-items: center;
  font-size: 1rem;
  color: ${({ theme }) => theme.colors.text.primary};
  cursor: pointer;
`;

const Button = styled.button<{ variant?: 'primary' | 'secondary' | 'danger' }>`
  padding: 0.75rem 1.5rem;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;

  ${({ theme, variant }) => {
    switch (variant) {
      case 'primary':
        return `
          background-color: ${theme.colors.primary.main};
          color: ${theme.colors.primary.contrastText};

          &:hover {
            background-color: ${theme.colors.primary.dark};
          }
        `;
      case 'secondary':
        return `
          background-color: transparent;
          color: ${theme.colors.primary.main};
          border: 1px solid ${theme.colors.primary.main};

          &:hover {
            background-color: ${theme.colors.action.hover};
          }
        `;
      case 'danger':
        return `
          background-color: ${theme.colors.error.main};
          color: ${theme.colors.error.contrastText};

          &:hover {
            background-color: ${theme.colors.error.dark};
          }
        `;
      default:
        return `
          background-color: ${theme.colors.primary.main};
          color: ${theme.colors.primary.contrastText};

          &:hover {
            background-color: ${theme.colors.primary.dark};
          }
        `;
    }
  }}
`;

const FamilyMemberCard = styled.div`
  border: 1px solid ${({ theme }) => theme.colors.divider};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: 1rem;
  margin-bottom: 1rem;
  background-color: ${({ theme }) => theme.colors.background.paper};
  box-shadow: ${({ theme }) => theme.shadows.sm};
`;

const FamilyMemberHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
`;

const FamilyMemberTitle = styled.h3`
  margin: 0;
  font-size: 1.1rem;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const FamilyMemberActions = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const AddMemberSection = styled.div`
  margin-top: 1.5rem;
  padding: 1.5rem;
  border: 1px dashed ${({ theme }) => theme.colors.divider};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  background-color: ${({ theme }) => theme.colors.background.default};
`;

const SectionTitle = styled.h3`
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const NoFamilyMembers = styled.div`
  padding: 2rem;
  text-align: center;
  color: ${({ theme }) => theme.colors.text.secondary};
  background-color: ${({ theme }) => theme.colors.background.default};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  margin-bottom: 1.5rem;
`;

const FamilyInformationStep: React.FC = () => {
  // We'll use the context hooks directly when needed
  useGuidedJourney();
  const { data, updateData } = useFinancialCompass();

  // Initialize form state from context or with defaults
  const familyInfo = data.north?.familyInformation || {
    maritalStatus: 'single',
    familyMembers: [],
  };
  const [familyMembers, setFamilyMembers] = useState<FamilyMember[]>(
    familyInfo.familyMembers || []
  );

  const [newMember, setNewMember] = useState<Omit<FamilyMember, 'id'>>({
    relationship: '',
    firstName: '',
    lastName: '',
    dateOfBirth: '',
    isDependent: false,
  });

  // Handle input changes for new member form
  const handleNewMemberChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setNewMember((prev) => ({
        ...prev,
        [name]: checked,
      }));
    } else {
      setNewMember((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  // Add new family member
  const handleAddMember = () => {
    if (!newMember.firstName || !newMember.relationship) return;

    const newFamilyMember: FamilyMember = {
      ...newMember,
      id: `member-${Date.now()}`,
    };

    const updatedMembers = [...familyMembers, newFamilyMember];
    setFamilyMembers(updatedMembers);

    // Reset form
    setNewMember({
      relationship: '',
      firstName: '',
      lastName: '',
      dateOfBirth: '',
      isDependent: false,
    });

    // Update context
    const familyInfo = data.north?.familyInformation || {
      maritalStatus: 'single',
      familyMembers: [],
    };
    updateData('north', 'familyInformation', {
      ...familyInfo,
      familyMembers: updatedMembers,
    });
  };

  // Remove family member
  const handleRemoveMember = (id: string) => {
    const updatedMembers = familyMembers.filter((member) => member.id !== id);
    setFamilyMembers(updatedMembers);

    // Update context
    const familyInfo = data.north?.familyInformation || {
      maritalStatus: 'single',
      familyMembers: [],
    };
    updateData('north', 'familyInformation', {
      ...familyInfo,
      familyMembers: updatedMembers,
    });
  };

  return (
    <StepContainer>
      <SectionTitle>Family Members</SectionTitle>

      {familyMembers.length === 0 ? (
        <NoFamilyMembers>
          <p>You haven&apos;t added any family members yet.</p>
          <p>Use the form below to add your spouse, children, or other dependents.</p>
        </NoFamilyMembers>
      ) : (
        familyMembers.map((member) => (
          <FamilyMemberCard key={member.id}>
            <FamilyMemberHeader>
              <FamilyMemberTitle>
                {member.firstName} {member.lastName} ({member.relationship})
              </FamilyMemberTitle>
              <FamilyMemberActions>
                <Button variant="danger" onClick={() => handleRemoveMember(member.id)}>
                  Remove
                </Button>
              </FamilyMemberActions>
            </FamilyMemberHeader>

            <FormRow>
              <div>
                <strong>Date of Birth:</strong> {member.dateOfBirth || 'Not specified'}
              </div>
              <div>
                <strong>Dependent:</strong> {member.isDependent ? 'Yes' : 'No'}
              </div>
            </FormRow>
          </FamilyMemberCard>
        ))
      )}

      <AddMemberSection>
        <SectionTitle>Add Family Member</SectionTitle>

        <FormRow>
          <FormGroup>
            <Label htmlFor="relationship">Relationship</Label>
            <Select
              id="relationship"
              name="relationship"
              value={newMember.relationship}
              onChange={handleNewMemberChange}
              required
            >
              <option value="">Select relationship</option>
              <option value="spouse">Spouse</option>
              <option value="child">Child</option>
              <option value="parent">Parent</option>
              <option value="sibling">Sibling</option>
              <option value="other">Other</option>
            </Select>
          </FormGroup>

          <FormGroup>
            <Label htmlFor="firstName">First Name</Label>
            <Input
              type="text"
              id="firstName"
              name="firstName"
              value={newMember.firstName}
              onChange={handleNewMemberChange}
              placeholder="Enter first name"
              required
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="lastName">Last Name</Label>
            <Input
              type="text"
              id="lastName"
              name="lastName"
              value={newMember.lastName}
              onChange={handleNewMemberChange}
              placeholder="Enter last name"
            />
          </FormGroup>
        </FormRow>

        <FormRow>
          <FormGroup>
            <Label htmlFor="dateOfBirth">Date of Birth</Label>
            <Input
              type="date"
              id="dateOfBirth"
              name="dateOfBirth"
              value={newMember.dateOfBirth}
              onChange={handleNewMemberChange}
            />
          </FormGroup>

          <FormGroup>
            <CheckboxLabel>
              <Checkbox
                type="checkbox"
                id="isDependent"
                name="isDependent"
                checked={newMember.isDependent}
                onChange={handleNewMemberChange}
              />
              Is a dependent
            </CheckboxLabel>
          </FormGroup>
        </FormRow>

        <Button variant="primary" onClick={handleAddMember} style={{ marginTop: '1rem' }}>
          Add Family Member
        </Button>
      </AddMemberSection>
    </StepContainer>
  );
};

export default FamilyInformationStep;
