/**
 * Risk Assessment Step Component
 *
 * This component assesses the user's risk tolerance as part of the guided journey.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useGuidedJourney } from '../../context/GuidedJourneyContext';
import { useFinancialCompass } from '../../../FinancialCompass/context/FinancialCompassContext';

// Types
interface RiskQuestion {
  id: string;
  question: string;
  options: {
    id: string;
    text: string;
    score: number;
  }[];
}

interface RiskAssessmentData {
  answers: Record<string, string>;
  riskScore: number;
  riskProfile:
    | 'conservative'
    | 'moderate-conservative'
    | 'moderate'
    | 'moderate-aggressive'
    | 'aggressive';
}

// Styled Components
const StepContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2rem;
`;

const QuestionCard = styled.div`
  border: 1px solid ${({ theme }) => theme.colors.divider};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: 1.5rem;
  background-color: ${({ theme }) => theme.colors.background.paper};
  box-shadow: ${({ theme }) => theme.shadows.sm};
`;

const QuestionText = styled.h3`
  margin: 0 0 1.5rem 0;
  font-size: 1.1rem;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const OptionsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
`;

const OptionLabel = styled.label`
  display: flex;
  align-items: flex-start;
  padding: 0.75rem;
  border: 1px solid ${({ theme }) => theme.colors.divider};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${({ theme }) => theme.colors.action.hover};
  }

  &.selected {
    border-color: ${({ theme }) => theme.colors.primary.main};
    background-color: ${({ theme }) => theme.colors.primary.light};
  }
`;

const Radio = styled.input`
  margin-right: 0.75rem;
  margin-top: 0.25rem;
`;

const OptionText = styled.span`
  color: ${({ theme }) => theme.colors.text.primary};
`;

const ResultsContainer = styled.div`
  margin-top: 2rem;
  padding: 1.5rem;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  background-color: ${({ theme }) => theme.colors.background.default};
`;

const ResultsTitle = styled.h3`
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const ScoreDisplay = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
`;

const ScoreLabel = styled.span`
  font-weight: 500;
  margin-right: 1rem;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const ScoreValue = styled.span`
  font-size: 1.5rem;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.primary.main};
`;

const ProfileContainer = styled.div`
  margin-bottom: 1rem;
`;

const ProfileLabel = styled.span`
  font-weight: 500;
  margin-right: 1rem;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const ProfileValue = styled.span`
  font-weight: 600;
  color: ${({ theme }) => theme.colors.primary.dark};
`;

const ProfileDescription = styled.p`
  margin: 0.5rem 0 0 0;
  color: ${({ theme }) => theme.colors.text.secondary};
  line-height: 1.5;
`;

// Risk assessment questions
const riskQuestions: RiskQuestion[] = [
  {
    id: 'time_horizon',
    question:
      'How long do you plan to invest before you need to access a significant portion of your money?',
    options: [
      { id: 'a', text: 'Less than 3 years', score: 1 },
      { id: 'b', text: '3-5 years', score: 2 },
      { id: 'c', text: '6-10 years', score: 3 },
      { id: 'd', text: '11-20 years', score: 4 },
      { id: 'e', text: 'More than 20 years', score: 5 },
    ],
  },
  {
    id: 'market_decline',
    question:
      'If your investment portfolio lost 20% of its value in a short period, what would you do?',
    options: [
      { id: 'a', text: 'Sell all my investments and move to cash', score: 1 },
      { id: 'b', text: 'Sell some of my investments to reduce risk', score: 2 },
      { id: 'c', text: 'Do nothing and wait for recovery', score: 3 },
      { id: 'd', text: 'Buy more investments at lower prices', score: 5 },
    ],
  },
  {
    id: 'investment_goal',
    question: 'Which statement best describes your primary investment goal?',
    options: [
      {
        id: 'a',
        text: 'Preserving my capital is my primary goal, even if it means lower returns',
        score: 1,
      },
      { id: 'b', text: 'I want my investments to generate income with minimal risk', score: 2 },
      { id: 'c', text: 'I want a balance between growth and income', score: 3 },
      {
        id: 'd',
        text: 'I want my investments to grow and am willing to accept moderate fluctuations',
        score: 4,
      },
      {
        id: 'e',
        text: 'I want maximum growth and am willing to accept significant fluctuations',
        score: 5,
      },
    ],
  },
  {
    id: 'risk_comfort',
    question: 'How comfortable are you with investment risk?',
    options: [
      { id: 'a', text: 'Not comfortable at all - I worry about any losses', score: 1 },
      { id: 'b', text: 'Somewhat uncomfortable - I prefer stability', score: 2 },
      { id: 'c', text: 'Moderately comfortable - I can accept some fluctuations', score: 3 },
      { id: 'd', text: 'Comfortable - I understand markets fluctuate', score: 4 },
      { id: 'e', text: 'Very comfortable - I embrace risk for potential higher returns', score: 5 },
    ],
  },
  {
    id: 'income_stability',
    question: 'How stable is your current and future income?',
    options: [
      { id: 'a', text: 'Very unstable - My income fluctuates significantly', score: 1 },
      { id: 'b', text: 'Somewhat unstable - My income varies occasionally', score: 2 },
      { id: 'c', text: 'Moderately stable - My income is generally reliable', score: 3 },
      { id: 'd', text: 'Stable - My income is very reliable', score: 4 },
      { id: 'e', text: 'Very stable - My income is guaranteed or highly secure', score: 5 },
    ],
  },
];

// Risk profile descriptions
const riskProfiles = {
  conservative:
    'You prefer stability and preservation of capital over higher returns. Your portfolio should focus on lower-risk investments like bonds and cash equivalents.',
  'moderate-conservative':
    'You prefer stability but are willing to accept some risk for potential growth. Your portfolio should include a mix of bonds and some stocks.',
  moderate:
    'You seek a balance between growth and stability. Your portfolio should include a balanced mix of stocks and bonds.',
  'moderate-aggressive':
    'You prioritize growth and are comfortable with market fluctuations. Your portfolio should include a higher allocation to stocks with some bonds for stability.',
  aggressive:
    'You seek maximum growth and can tolerate significant market fluctuations. Your portfolio should be heavily weighted toward stocks and other growth investments.',
};

const RiskAssessmentStep: React.FC = () => {
  // We'll use the context hooks directly when needed
  useGuidedJourney();
  const { data, updateData } = useFinancialCompass();

  // Initialize state from context or with defaults
  const riskAssessment = data.north?.riskAssessment || {
    answers: {},
    riskScore: 0,
    riskProfile: 'moderate' as RiskAssessmentData['riskProfile'],
  };

  const [answers, setAnswers] = useState<Record<string, string>>(riskAssessment.answers || {});

  const [riskScore, setRiskScore] = useState<number>(riskAssessment.riskScore || 0);

  const [riskProfile, setRiskProfile] = useState<RiskAssessmentData['riskProfile']>(
    riskAssessment.riskProfile || 'moderate'
  );

  // Handle answer selection
  const handleAnswerSelect = (questionId: string, optionId: string) => {
    setAnswers((prev) => ({
      ...prev,
      [questionId]: optionId,
    }));
  };

  // Calculate risk score and profile when answers change
  useEffect(() => {
    if (Object.keys(answers).length === 0) return;

    // Calculate total score
    let totalScore = 0;
    let answeredQuestions = 0;

    riskQuestions.forEach((question) => {
      const selectedOptionId = answers[question.id];
      if (selectedOptionId) {
        const selectedOption = question.options.find((option) => option.id === selectedOptionId);
        if (selectedOption) {
          totalScore += selectedOption.score;
          answeredQuestions++;
        }
      }
    });

    // Calculate average score if at least one question is answered
    if (answeredQuestions > 0) {
      const averageScore = totalScore / answeredQuestions;
      setRiskScore(Math.round(averageScore * 20)); // Scale to 0-100

      // Determine risk profile based on average score
      let profile: RiskAssessmentData['riskProfile'];
      if (averageScore < 1.5) {
        profile = 'conservative';
      } else if (averageScore < 2.5) {
        profile = 'moderate-conservative';
      } else if (averageScore < 3.5) {
        profile = 'moderate';
      } else if (averageScore < 4.5) {
        profile = 'moderate-aggressive';
      } else {
        profile = 'aggressive';
      }

      setRiskProfile(profile);

      // Update context
      updateData('north', 'riskAssessment', {
        answers,
        riskScore: Math.round(averageScore * 20),
        riskProfile: profile,
      });
    }
  }, [answers, updateData]);

  // Format risk profile for display
  const formatRiskProfile = (profile: string) => {
    return profile
      .split('-')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join('-');
  };

  return (
    <StepContainer>
      {riskQuestions.map((question) => (
        <QuestionCard key={question.id}>
          <QuestionText>{question.question}</QuestionText>
          <OptionsContainer>
            {question.options.map((option) => (
              <OptionLabel
                key={option.id}
                className={answers[question.id] === option.id ? 'selected' : ''}
              >
                <Radio
                  type="radio"
                  name={question.id}
                  value={option.id}
                  checked={answers[question.id] === option.id}
                  onChange={() => handleAnswerSelect(question.id, option.id)}
                />
                <OptionText>{option.text}</OptionText>
              </OptionLabel>
            ))}
          </OptionsContainer>
        </QuestionCard>
      ))}

      {Object.keys(answers).length > 0 && (
        <ResultsContainer>
          <ResultsTitle>Your Risk Assessment Results</ResultsTitle>

          <ScoreDisplay>
            <ScoreLabel>Risk Score:</ScoreLabel>
            <ScoreValue>{riskScore}/100</ScoreValue>
          </ScoreDisplay>

          <ProfileContainer>
            <ProfileLabel>Risk Profile:</ProfileLabel>
            <ProfileValue>{formatRiskProfile(riskProfile)}</ProfileValue>
            <ProfileDescription>{riskProfiles[riskProfile]}</ProfileDescription>
          </ProfileContainer>
        </ResultsContainer>
      )}
    </StepContainer>
  );
};

export default RiskAssessmentStep;
