/**
 * Summary Step Component
 *
 * This component displays a summary of all the information collected during the guided journey.
 */

import React from 'react';
import styled from 'styled-components';
import { useGuidedJourney } from '../../context/GuidedJourneyContext';
import { useFinancialCompass } from '../../../FinancialCompass/context/FinancialCompassContext';
import {
  FamilyMember as FamilyMemberType,
  PersonalInformationData,
  RiskAssessmentData,
} from '../../../../types/northDirection';

// Styled Components
const StepContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2rem;
`;

const SectionCard = styled.div`
  border: 1px solid ${({ theme }) => theme.colors.divider};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  overflow: hidden;
  background-color: ${({ theme }) => theme.colors.background.paper};
  box-shadow: ${({ theme }) => theme.shadows.sm};
`;

const SectionHeader = styled.div`
  padding: 1rem 1.5rem;
  background-color: ${({ theme }) => theme.colors.background.default};
  border-bottom: 1px solid ${({ theme }) => theme.colors.divider};
`;

const SectionTitle = styled.h3`
  margin: 0;
  font-size: 1.1rem;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const SectionContent = styled.div`
  padding: 1.5rem;
`;

const InfoRow = styled.div`
  display: flex;
  margin-bottom: 0.75rem;

  &:last-child {
    margin-bottom: 0;
  }
`;

const InfoLabel = styled.div`
  width: 40%;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const InfoValue = styled.div`
  width: 60%;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const FamilyMembersList = styled.ul`
  margin: 0;
  padding: 0;
  list-style: none;
`;

const FamilyMemberItem = styled.li`
  padding: 0.75rem;
  border-bottom: 1px solid ${({ theme }) => theme.colors.divider};

  &:last-child {
    border-bottom: none;
  }
`;

const RiskProfileContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const RiskScore = styled.div`
  font-size: 1.25rem;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.primary.main};
`;

const RiskProfile = styled.div`
  font-weight: 500;
`;

const RiskDescription = styled.p`
  margin: 0.5rem 0 0 0;
  color: ${({ theme }) => theme.colors.text.secondary};
  line-height: 1.5;
`;

const NextStepsContainer = styled.div`
  margin-top: 2rem;
  padding: 1.5rem;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  background-color: ${({ theme }) => theme.colors.background.default};
`;

const NextStepsTitle = styled.h3`
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const NextStepsList = styled.ol`
  margin: 0;
  padding: 0 0 0 1.5rem;
`;

const NextStepItem = styled.li`
  margin-bottom: 0.75rem;
  color: ${({ theme }) => theme.colors.text.primary};
  line-height: 1.5;

  &:last-child {
    margin-bottom: 0;
  }
`;

const Button = styled.button`
  padding: 0.75rem 1.5rem;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  background-color: ${({ theme }) => theme.colors.primary.main};
  color: ${({ theme }) => theme.colors.primary.contrastText};
  margin-top: 1.5rem;

  &:hover {
    background-color: ${({ theme }) => theme.colors.primary.dark};
  }
`;

// Types
interface FamilyMember {
  id: string;
  firstName: string;
  lastName: string;
  relationship: string;
  dateOfBirth?: string;
  isDependent: boolean;
}

interface RiskAssessment {
  riskScore: number;
  riskProfile:
    | 'conservative'
    | 'moderate-conservative'
    | 'moderate'
    | 'moderate-aggressive'
    | 'aggressive';
}

// Risk profile descriptions
const riskProfiles = {
  conservative:
    'You prefer stability and preservation of capital over higher returns. Your portfolio should focus on lower-risk investments like bonds and cash equivalents.',
  'moderate-conservative':
    'You prefer stability but are willing to accept some risk for potential growth. Your portfolio should include a mix of bonds and some stocks.',
  moderate:
    'You seek a balance between growth and stability. Your portfolio should include a balanced mix of stocks and bonds.',
  'moderate-aggressive':
    'You prioritize growth and are comfortable with market fluctuations. Your portfolio should include a higher allocation to stocks with some bonds for stability.',
  aggressive:
    'You seek maximum growth and can tolerate significant market fluctuations. Your portfolio should be heavily weighted toward stocks and other growth investments.',
};

const SummaryStep: React.FC = () => {
  const { navigateToDirection } = useGuidedJourney();
  const { data } = useFinancialCompass();

  const personalInfo =
    data.north?.personalInformation ||
    ({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      dateOfBirth: '',
      address: {
        street: '',
        city: '',
        state: '',
        zipCode: '',
        country: '',
      },
      occupation: '',
      employmentStatus: 'employed',
    } as PersonalInformationData);

  const familyInfo = data.north?.familyInformation || {
    maritalStatus: 'single',
    familyMembers: [],
  };

  const riskAssessment =
    data.north?.riskAssessment ||
    ({
      answers: {},
      riskScore: 0,
      riskProfile: 'moderate',
    } as RiskAssessmentData);

  // Format risk profile for display
  const formatRiskProfile = (profile: string) => {
    return profile
      .split('-')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join('-');
  };

  // Handle navigation to compass directions
  const handleNavigateToDirection = (direction: 'north' | 'east' | 'south' | 'west') => {
    if (navigateToDirection) {
      navigateToDirection(direction);
    }
  };

  return (
    <StepContainer>
      <SectionCard>
        <SectionHeader>
          <SectionTitle>Personal Information</SectionTitle>
        </SectionHeader>
        <SectionContent>
          <InfoRow>
            <InfoLabel>Name</InfoLabel>
            <InfoValue>
              {personalInfo.firstName} {personalInfo.lastName}
            </InfoValue>
          </InfoRow>
          <InfoRow>
            <InfoLabel>Email</InfoLabel>
            <InfoValue>{personalInfo.email}</InfoValue>
          </InfoRow>
          <InfoRow>
            <InfoLabel>Phone</InfoLabel>
            <InfoValue>{personalInfo.phone}</InfoValue>
          </InfoRow>
          <InfoRow>
            <InfoLabel>Date of Birth</InfoLabel>
            <InfoValue>{personalInfo.dateOfBirth}</InfoValue>
          </InfoRow>
          <InfoRow>
            <InfoLabel>Gender</InfoLabel>
            <InfoValue>{personalInfo.gender}</InfoValue>
          </InfoRow>
          <InfoRow>
            <InfoLabel>Marital Status</InfoLabel>
            <InfoValue>{personalInfo.maritalStatus}</InfoValue>
          </InfoRow>
          <InfoRow>
            <InfoLabel>Occupation</InfoLabel>
            <InfoValue>{personalInfo.occupation}</InfoValue>
          </InfoRow>
          <InfoRow>
            <InfoLabel>Annual Income</InfoLabel>
            <InfoValue>
              {personalInfo.annualIncome
                ? `$${parseInt(personalInfo.annualIncome).toLocaleString()}`
                : 'Not specified'}
            </InfoValue>
          </InfoRow>
          <InfoRow>
            <InfoLabel>Address</InfoLabel>
            <InfoValue>
              {personalInfo.address?.street}
              <br />
              {personalInfo.address?.city}, {personalInfo.address?.state}{' '}
              {personalInfo.address?.zipCode}
              <br />
              {personalInfo.address?.country}
            </InfoValue>
          </InfoRow>
        </SectionContent>
      </SectionCard>

      <SectionCard>
        <SectionHeader>
          <SectionTitle>Family Information</SectionTitle>
        </SectionHeader>
        <SectionContent>
          {familyInfo.familyMembers && familyInfo.familyMembers.length > 0 ? (
            <FamilyMembersList>
              {familyInfo.familyMembers.map((member: FamilyMemberType) => (
                <FamilyMemberItem key={member.id}>
                  <strong>
                    {member.firstName} {member.lastName}
                  </strong>{' '}
                  ({member.relationship})
                  {member.dateOfBirth && <div>Date of Birth: {member.dateOfBirth}</div>}
                  <div>Dependent: {member.isDependent ? 'Yes' : 'No'}</div>
                </FamilyMemberItem>
              ))}
            </FamilyMembersList>
          ) : (
            <div>No family members added.</div>
          )}
        </SectionContent>
      </SectionCard>

      <SectionCard>
        <SectionHeader>
          <SectionTitle>Risk Assessment</SectionTitle>
        </SectionHeader>
        <SectionContent>
          {riskAssessment.riskScore ? (
            <RiskProfileContainer>
              <RiskScore>Risk Score: {riskAssessment.riskScore}/100</RiskScore>
              <RiskProfile>
                Risk Profile: {formatRiskProfile(riskAssessment.riskProfile)}
              </RiskProfile>
              <RiskDescription>
                {riskProfiles[riskAssessment.riskProfile as keyof typeof riskProfiles]}
              </RiskDescription>
            </RiskProfileContainer>
          ) : (
            <div>Risk assessment not completed.</div>
          )}
        </SectionContent>
      </SectionCard>

      <NextStepsContainer>
        <NextStepsTitle>Next Steps</NextStepsTitle>
        <NextStepsList>
          <NextStepItem>
            Explore the North Direction (Where You Are) to see a comprehensive view of your current
            financial situation.
          </NextStepItem>
          <NextStepItem>
            Visit the East Direction (Where You&apos;re Going) to set retirement goals and plan your
            future.
          </NextStepItem>
          <NextStepItem>
            Check the South Direction (What Protects You) to assess your insurance coverage and risk
            management.
          </NextStepItem>
          <NextStepItem>
            Explore the West Direction (What You&apos;ll Leave Behind) to plan your legacy and
            estate.
          </NextStepItem>
        </NextStepsList>

        <Button onClick={() => handleNavigateToDirection('north')}>
          Continue to Financial Compass
        </Button>
      </NextStepsContainer>
    </StepContainer>
  );
};

export default SummaryStep;
