/**
 * Progress Tracker Component
 *
 * This component provides a visual representation of the user's progress through the journey.
 */

import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';

// Types
interface ProgressSection {
  id: string;
  title: string;
  progress: number;
  color?: string;
}

interface ProgressTrackerProps {
  sections: ProgressSection[];
  overallProgress: number;
  showLabels?: boolean;
  size?: 'small' | 'medium' | 'large';
  animated?: boolean;
}

// Styled Components
const TrackerContainer = styled.div<{ size: 'small' | 'medium' | 'large' }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: ${({ size }) => (size === 'small' ? '200px' : size === 'medium' ? '300px' : '400px')};
  margin: 0 auto;
`;

const CircleContainer = styled.div<{ size: 'small' | 'medium' | 'large' }>`
  position: relative;
  width: ${({ size }) => (size === 'small' ? '150px' : size === 'medium' ? '250px' : '350px')};
  height: ${({ size }) => (size === 'small' ? '150px' : size === 'medium' ? '250px' : '350px')};
`;

const CircleBackground = styled.circle`
  fill: none;
  stroke: ${({ theme }) => theme.colors.background.elevated};
  stroke-width: 10;
`;

const CircleProgress = styled(motion.circle)<{ color?: string }>`
  fill: none;
  stroke: ${({ theme, color }) => color || theme.colors.primary.main};
  stroke-width: 10;
  stroke-linecap: round;
  transform-origin: center;
  transform: rotate(-90deg);
`;

const CenterText = styled.div<{ size: 'small' | 'medium' | 'large' }>`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;

  h2 {
    font-size: ${({ size }) =>
      size === 'small' ? '1.5rem' : size === 'medium' ? '2rem' : '2.5rem'};
    margin: 0;
    color: ${({ theme }) => theme.colors.text.primary};
  }

  p {
    font-size: ${({ size }) =>
      size === 'small' ? '0.875rem' : size === 'medium' ? '1rem' : '1.25rem'};
    margin: 0;
    color: ${({ theme }) => theme.colors.text.secondary};
  }
`;

const Legend = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 1.5rem;
`;

const LegendItem = styled.div`
  display: flex;
  align-items: center;
  margin: 0.5rem;
`;

const LegendColor = styled.div<{ color: string }>`
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background-color: ${({ color, theme }) => color || theme.colors.primary.main};
  margin-right: 0.5rem;
`;

const LegendText = styled.span`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const LegendProgress = styled.span`
  font-size: 0.875rem;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text.primary};
  margin-left: 0.5rem;
`;

/**
 * Progress Tracker Component
 */
const ProgressTracker: React.FC<ProgressTrackerProps> = ({
  sections,
  overallProgress,
  showLabels = true,
  size = 'medium',
  animated = true,
}) => {
  // Calculate dimensions based on size
  const getDimensions = () => {
    switch (size) {
      case 'small':
        return { radius: 70, circumference: 439.8 };
      case 'medium':
        return { radius: 120, circumference: 753.6 };
      case 'large':
        return { radius: 170, circumference: 1067.9 };
      default:
        return { radius: 120, circumference: 753.6 };
    }
  };

  const { radius, circumference } = getDimensions();

  // Calculate stroke dasharray and dashoffset for each section
  const calculateStrokeDasharray = (progress: number) => {
    return `${(progress / 100) * circumference} ${circumference}`;
  };

  // Calculate stroke dashoffset for each section
  const calculateStrokeDashoffset = (index: number) => {
    // Calculate the offset based on the progress of previous sections
    const previousSectionsProgress = sections
      .slice(0, index)
      .reduce((total, section) => total + (section.progress / 100) * circumference, 0);

    return circumference - previousSectionsProgress;
  };

  return (
    <TrackerContainer size={size}>
      <CircleContainer size={size}>
        <svg width="100%" height="100%" viewBox={`0 0 ${radius * 2 + 10} ${radius * 2 + 10}`}>
          <CircleBackground cx={radius + 5} cy={radius + 5} r={radius} />

          {sections.map((section, index) => (
            <CircleProgress
              key={section.id}
              cx={radius + 5}
              cy={radius + 5}
              r={radius}
              color={section.color}
              strokeDasharray={calculateStrokeDasharray(section.progress)}
              strokeDashoffset={calculateStrokeDashoffset(index)}
              initial={animated ? { strokeDasharray: '0 753.6' } : undefined}
              animate={
                animated
                  ? { strokeDasharray: calculateStrokeDasharray(section.progress) }
                  : undefined
              }
              transition={animated ? { duration: 1, delay: index * 0.2 } : undefined}
            />
          ))}
        </svg>

        <CenterText size={size}>
          <h2>{overallProgress}%</h2>
          <p>Complete</p>
        </CenterText>
      </CircleContainer>

      {showLabels && (
        <Legend>
          {sections.map((section) => (
            <LegendItem key={section.id}>
              <LegendColor color={section.color || ''} />
              <LegendText>{section.title}</LegendText>
              <LegendProgress>{section.progress}%</LegendProgress>
            </LegendItem>
          ))}
        </Legend>
      )}
    </TrackerContainer>
  );
};

export default ProgressTracker;
