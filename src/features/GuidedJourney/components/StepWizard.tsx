/**
 * Step Wizard Component
 *
 * This component provides a step-by-step wizard interface for guiding users through complex forms.
 */

import React, { useState, ReactNode, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';

// Types
export interface Step {
  id: string;
  title: string;
  description?: string;
  component: ReactNode;
  isOptional?: boolean;
  isValid?: boolean;
  validationMessage?: string;
}

interface StepWizardProps {
  steps: Step[];
  onComplete: () => void;
  onCancel?: () => void;
  initialStep?: number;
  showSummary?: boolean;
  autoSave?: boolean;
}

// Styled Components
const WizardContainer = styled.div`
  display: flex;
  flex-direction: column;
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  box-shadow: ${({ theme }) => theme.shadows[2]};
  overflow: hidden;
  max-width: 1000px;
  margin: 0 auto;
`;

const WizardHeader = styled.div`
  padding: 1.5rem;
  border-bottom: 1px solid ${({ theme }) => theme.colors.divider};
`;

const WizardTitle = styled.h2`
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const WizardDescription = styled.p`
  color: ${({ theme }) => theme.colors.text.secondary};
  margin: 0;
`;

const StepsIndicator = styled.div`
  display: flex;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid ${({ theme }) => theme.colors.divider};
  overflow-x: auto;
  scrollbar-width: thin;

  &::-webkit-scrollbar {
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    background: ${({ theme }) => theme.colors.background.default};
  }

  &::-webkit-scrollbar-thumb {
    background-color: ${({ theme }) => theme.colors.divider};
    border-radius: 4px;
  }
`;

const StepIndicator = styled.div<{ active: boolean; completed: boolean; optional: boolean }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 1rem;
  position: relative;
  flex-shrink: 0;

  &:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 1rem;
    left: calc(50% + 1rem);
    width: calc(100% - 2rem);
    height: 2px;
    background-color: ${({ theme, completed }) =>
      completed ? theme.colors.primary.main : theme.colors.divider};
  }

  ${({ optional, theme }) =>
    optional &&
    `
    &::before {
      content: '(Optional)';
      position: absolute;
      top: -1.25rem;
      font-size: 0.75rem;
      color: ${theme.colors.text.secondary};
    }
  `}
`;

const StepNumber = styled.div<{ active: boolean; completed: boolean }>`
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-bottom: 0.5rem;
  background-color: ${({ theme, active, completed }) =>
    completed
      ? theme.colors.primary.main
      : active
        ? theme.colors.primary.light
        : theme.colors.background.elevated};
  color: ${({ theme, active, completed }) =>
    completed || active ? theme.colors.primary.contrastText : theme.colors.text.secondary};
  transition: all 0.2s ease;

  ${({ completed }) =>
    completed &&
    `
    &::after {
      content: '✓';
    }
  `}
`;

const StepLabel = styled.span<{ active: boolean }>`
  font-size: 0.875rem;
  color: ${({ theme, active }) =>
    active ? theme.colors.primary.main : theme.colors.text.secondary};
  text-align: center;
  max-width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const StepContent = styled(motion.div)`
  padding: 2rem 1.5rem;
  min-height: 300px;
`;

const WizardFooter = styled.div`
  display: flex;
  justify-content: space-between;
  padding: 1.5rem;
  border-top: 1px solid ${({ theme }) => theme.colors.divider};
`;

const Button = styled.button<{ variant?: 'primary' | 'secondary' | 'text' }>`
  padding: 0.75rem 1.5rem;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;

  ${({ theme, variant }) => {
    switch (variant) {
      case 'primary':
        return `
          background-color: ${theme.colors.primary.main};
          color: ${theme.colors.primary.contrastText};

          &:hover {
            background-color: ${theme.colors.primary.dark};
          }

          &:disabled {
            background-color: ${theme.colors.action.disabledBackground};
            color: ${theme.colors.action.disabled};
            cursor: not-allowed;
          }
        `;
      case 'secondary':
        return `
          background-color: transparent;
          color: ${theme.colors.primary.main};
          border: 1px solid ${theme.colors.primary.main};

          &:hover {
            background-color: ${theme.colors.action.hover};
          }

          &:disabled {
            border-color: ${theme.colors.action.disabled};
            color: ${theme.colors.action.disabled};
            cursor: not-allowed;
          }
        `;
      case 'text':
      default:
        return `
          background-color: transparent;
          color: ${theme.colors.text.primary};

          &:hover {
            background-color: ${theme.colors.action.hover};
          }

          &:disabled {
            color: ${theme.colors.action.disabled};
            cursor: not-allowed;
          }
        `;
    }
  }}
`;

const ValidationMessage = styled.div`
  color: ${({ theme }) => theme.colors.error.main};
  font-size: 0.875rem;
  margin-top: 0.5rem;
`;

const AutoSaveIndicator = styled.div<{ saving: boolean }>`
  display: flex;
  align-items: center;
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.875rem;

  &::before {
    content: '';
    display: inline-block;
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    margin-right: 0.5rem;
    background-color: ${({ theme, saving }) =>
      saving ? theme.colors.warning.main : theme.colors.success.main};
  }
`;

/**
 * Step Wizard Component
 */
const StepWizard: React.FC<StepWizardProps> = ({
  steps,
  onComplete,
  onCancel,
  initialStep = 0,
  showSummary = true,
  autoSave = true,
}) => {
  const [currentStepIndex, setCurrentStepIndex] = useState(initialStep);
  const [completedSteps, setCompletedSteps] = useState<string[]>([]);
  const [isSaving, setIsSaving] = useState(false);

  const currentStep = steps[currentStepIndex];
  const isFirstStep = currentStepIndex === 0;
  const isLastStep = currentStepIndex === steps.length - 1;

  // Auto-save effect
  useEffect(() => {
    if (autoSave && completedSteps.includes(currentStep.id)) {
      const saveTimer = setTimeout(() => {
        setIsSaving(true);

        // Simulate saving data
        setTimeout(() => {
          setIsSaving(false);
        }, 1000);
      }, 500);

      return () => clearTimeout(saveTimer);
    }
  }, [currentStep, completedSteps, autoSave]);

  // Handle next step
  const handleNext = () => {
    // Mark current step as completed
    if (!completedSteps.includes(currentStep.id)) {
      setCompletedSteps([...completedSteps, currentStep.id]);
    }

    // If last step, call onComplete
    if (isLastStep) {
      onComplete();
      return;
    }

    // Otherwise, go to next step
    setCurrentStepIndex(currentStepIndex + 1);
  };

  // Handle previous step
  const handlePrevious = () => {
    if (isFirstStep) {
      return;
    }

    setCurrentStepIndex(currentStepIndex - 1);
  };

  // Handle cancel
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  // Check if current step is valid
  const isCurrentStepValid = currentStep.isValid !== false;

  return (
    <WizardContainer>
      <WizardHeader>
        <WizardTitle>{currentStep.title}</WizardTitle>
        {currentStep.description && (
          <WizardDescription>{currentStep.description}</WizardDescription>
        )}
      </WizardHeader>

      <StepsIndicator>
        {steps.map((step, index) => (
          <StepIndicator
            key={step.id}
            active={index === currentStepIndex}
            completed={completedSteps.includes(step.id)}
            optional={!!step.isOptional}
          >
            <StepNumber
              active={index === currentStepIndex}
              completed={completedSteps.includes(step.id)}
            >
              {!completedSteps.includes(step.id) && index + 1}
            </StepNumber>
            <StepLabel active={index === currentStepIndex}>{step.title}</StepLabel>
          </StepIndicator>
        ))}
      </StepsIndicator>

      <AnimatePresence mode="wait">
        <StepContent
          key={currentStep.id}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
        >
          {currentStep.component}

          {!isCurrentStepValid && currentStep.validationMessage && (
            <ValidationMessage>{currentStep.validationMessage}</ValidationMessage>
          )}
        </StepContent>
      </AnimatePresence>

      <WizardFooter>
        <div>
          {onCancel && (
            <Button variant="text" onClick={handleCancel}>
              Cancel
            </Button>
          )}

          {autoSave && (
            <AutoSaveIndicator saving={isSaving}>
              {isSaving ? 'Saving...' : 'Saved'}
            </AutoSaveIndicator>
          )}
        </div>

        <div>
          {!isFirstStep && (
            <Button variant="secondary" onClick={handlePrevious} style={{ marginRight: '0.5rem' }}>
              Previous
            </Button>
          )}

          <Button variant="primary" onClick={handleNext} disabled={!isCurrentStepValid}>
            {isLastStep ? 'Complete' : 'Next'}
          </Button>
        </div>
      </WizardFooter>
    </WizardContainer>
  );
};

export default StepWizard;
