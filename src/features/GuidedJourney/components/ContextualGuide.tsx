/**
 * Contextual Guide Component
 *
 * This component provides contextual AI guidance to users based on their current position in the journey.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';

// Types
export interface GuideMessage {
  id: string;
  text: string;
  type: 'tip' | 'info' | 'warning' | 'success';
  dismissible?: boolean;
}

interface ContextualGuideProps {
  messages: GuideMessage[];
  contextKey?: string;
  position?: 'top-right' | 'bottom-right' | 'bottom-left' | 'top-left';
  showAvatar?: boolean;
  onAskQuestion?: (question: string) => void;
}

// Styled Components
const GuideContainer = styled.div<{ position: string }>`
  position: fixed;
  z-index: ${({ theme }) => theme.zIndex.snackbar};
  max-width: 350px;

  ${({ position }) => {
    switch (position) {
      case 'top-right':
        return 'top: 1.5rem; right: 1.5rem;';
      case 'bottom-right':
        return 'bottom: 1.5rem; right: 1.5rem;';
      case 'bottom-left':
        return 'bottom: 1.5rem; left: 1.5rem;';
      case 'top-left':
        return 'top: 1.5rem; left: 1.5rem;';
      default:
        return 'bottom: 1.5rem; right: 1.5rem;';
    }
  }}
`;

const AvatarButton = styled.button`
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 50%;
  background-color: ${({ theme }) => theme.colors.primary.main};
  color: ${({ theme }) => theme.colors.primary.contrastText};
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  box-shadow: ${({ theme }) => theme.shadows[2]};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${({ theme }) => theme.colors.primary.dark};
    transform: scale(1.05);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors.primary.light};
  }
`;

const MessagesContainer = styled(motion.div)`
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
`;

const MessageCard = styled(motion.div)<{ type: 'tip' | 'info' | 'warning' | 'success' }>`
  padding: 1rem;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  background-color: ${({ theme }) => theme.colors.background.paper};
  box-shadow: ${({ theme }) => theme.shadows[2]};
  position: relative;

  ${({ type, theme }) => {
    switch (type) {
      case 'tip':
        return `border-left: 4px solid ${theme.colors.primary.main};`;
      case 'info':
        return `border-left: 4px solid ${theme.colors.info.main};`;
      case 'warning':
        return `border-left: 4px solid ${theme.colors.warning.main};`;
      case 'success':
        return `border-left: 4px solid ${theme.colors.success.main};`;
      default:
        return `border-left: 4px solid ${theme.colors.primary.main};`;
    }
  }}
`;

const MessageText = styled.p`
  margin: 0;
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 0.875rem;
  line-height: 1.5;
`;

const DismissButton = styled.button`
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.text.secondary};
  cursor: pointer;
  font-size: 1rem;
  padding: 0.25rem;

  &:hover {
    color: ${({ theme }) => theme.colors.text.primary};
  }
`;

const QuestionForm = styled.form`
  margin-top: 0.75rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const QuestionInput = styled.input`
  padding: 0.75rem;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  border: 1px solid ${({ theme }) => theme.colors.divider};
  background-color: ${({ theme }) => theme.colors.background.paper};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 0.875rem;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary.main};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light};
  }
`;

const QuestionButton = styled.button`
  padding: 0.75rem;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  background-color: ${({ theme }) => theme.colors.primary.main};
  color: ${({ theme }) => theme.colors.primary.contrastText};
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;

  &:hover {
    background-color: ${({ theme }) => theme.colors.primary.dark};
  }

  &:disabled {
    background-color: ${({ theme }) => theme.colors.action.disabledBackground};
    color: ${({ theme }) => theme.colors.action.disabled};
    cursor: not-allowed;
  }
`;

/**
 * Contextual Guide Component
 */
const ContextualGuide: React.FC<ContextualGuideProps> = ({
  messages,
  contextKey,
  position = 'bottom-right',
  showAvatar = true,
  onAskQuestion,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [visibleMessages, setVisibleMessages] = useState<GuideMessage[]>(messages);
  const [question, setQuestion] = useState('');

  // Update visible messages when messages prop changes
  useEffect(() => {
    setVisibleMessages(messages);
  }, [messages, contextKey]);

  // Toggle guide open/closed
  const toggleGuide = () => {
    setIsOpen(!isOpen);
  };

  // Dismiss a message
  const dismissMessage = (id: string) => {
    setVisibleMessages(visibleMessages.filter((message) => message.id !== id));
  };

  // Handle question submission
  const handleQuestionSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (question.trim() && onAskQuestion) {
      onAskQuestion(question);
      setQuestion('');
    }
  };

  return (
    <GuideContainer position={position}>
      <AnimatePresence>
        {isOpen && (
          <MessagesContainer
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.2 }}
          >
            {visibleMessages.map((message) => (
              <MessageCard
                key={message.id}
                type={message.type}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
              >
                <MessageText>{message.text}</MessageText>

                {message.dismissible && (
                  <DismissButton onClick={() => dismissMessage(message.id)}>×</DismissButton>
                )}
              </MessageCard>
            ))}

            {onAskQuestion && (
              <QuestionForm onSubmit={handleQuestionSubmit}>
                <QuestionInput
                  type="text"
                  placeholder="Ask a question..."
                  value={question}
                  onChange={(e) => setQuestion(e.target.value)}
                />
                <QuestionButton type="submit" disabled={!question.trim()}>
                  Ask
                </QuestionButton>
              </QuestionForm>
            )}
          </MessagesContainer>
        )}
      </AnimatePresence>

      {showAvatar && <AvatarButton onClick={toggleGuide}>{isOpen ? '×' : '?'}</AvatarButton>}
    </GuideContainer>
  );
};

export default ContextualGuide;
