import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../theme/SimpleThemeProvider';
import { useGuidedJourney } from '../context/GuidedJourneyContext';

interface GuidedFlowNavigatorProps {
  steps: {
    id: string;
    label: string;
    description?: string;
    isCompleted?: boolean;
    isActive?: boolean;
  }[];
  currentStepId: string;
  onStepChange: (stepId: string) => void;
  direction?: 'horizontal' | 'vertical';
  allowSkip?: boolean;
  showProgress?: boolean;
}

const GuidedFlowNavigator: React.FC<GuidedFlowNavigatorProps> = ({
  steps,
  currentStepId,
  onStepChange,
  direction = 'horizontal',
  allowSkip = false,
  showProgress = true,
}) => {
  const { theme } = useTheme();
  const { journeyProgress, updateJourneyProgress } = useGuidedJourney();
  const [animating, setAnimating] = useState(false);

  // Find the current step index
  const currentStepIndex = steps.findIndex((step) => step.id === currentStepId);

  // Calculate progress percentage
  const progressPercentage = ((currentStepIndex + 1) / steps.length) * 100;

  useEffect(() => {
    if (showProgress && updateJourneyProgress) {
      // Create a partial JourneyProgress object instead of passing a number
      updateJourneyProgress({
        overallPercentage: progressPercentage,
      });
    }
  }, [progressPercentage, showProgress, updateJourneyProgress]);

  const handleStepClick = (stepId: string, index: number) => {
    // Only allow clicking on completed steps or the next step
    const stepIndex = steps.findIndex((step) => step.id === stepId);
    const isCompleted = steps[stepIndex]?.isCompleted;
    const isNextStep = stepIndex === currentStepIndex + 1;

    if (isCompleted || isNextStep || allowSkip) {
      setAnimating(true);
      setTimeout(() => {
        onStepChange(stepId);
        setAnimating(false);
      }, 300);
    }
  };

  return (
    <NavigatorContainer direction={direction} theme={theme}>
      {showProgress && (
        <ProgressBar
          progress={progressPercentage}
          theme={theme}
          direction={direction}
          data-testid="progress-bar"
        />
      )}
      <StepsContainer direction={direction}>
        {steps.map((step, index) => {
          const isActive = step.id === currentStepId;
          const isCompleted = step.isCompleted || index < currentStepIndex;
          const isClickable = isCompleted || index === currentStepIndex + 1 || allowSkip;

          return (
            <StepItem
              key={step.id}
              isActive={isActive}
              isCompleted={isCompleted}
              isClickable={isClickable}
              onClick={() => isClickable && handleStepClick(step.id, index)}
              theme={theme}
              direction={direction}
              animating={animating && isActive}
            >
              <StepNumber isActive={isActive} isCompleted={isCompleted} theme={theme}>
                {isCompleted ? '✓' : index + 1}
              </StepNumber>
              <StepContent>
                <StepLabel isActive={isActive} theme={theme}>
                  {step.label}
                </StepLabel>
                {step.description && (
                  <StepDescription isActive={isActive} theme={theme}>
                    {step.description}
                  </StepDescription>
                )}
              </StepContent>
            </StepItem>
          );
        })}
      </StepsContainer>
      <NavigationButtons>
        <NavigationButton
          onClick={() =>
            currentStepIndex > 0 &&
            handleStepClick(steps[currentStepIndex - 1].id, currentStepIndex - 1)
          }
          disabled={currentStepIndex === 0}
          theme={theme}
        >
          Previous
        </NavigationButton>
        <NavigationButton
          onClick={() =>
            currentStepIndex < steps.length - 1 &&
            handleStepClick(steps[currentStepIndex + 1].id, currentStepIndex + 1)
          }
          disabled={currentStepIndex === steps.length - 1}
          theme={theme}
          isPrimary
        >
          Next
        </NavigationButton>
      </NavigationButtons>
    </NavigatorContainer>
  );
};

const NavigatorContainer = styled.div<{ direction: 'horizontal' | 'vertical'; theme: any }>`
  display: flex;
  flex-direction: ${(props) => (props.direction === 'horizontal' ? 'column' : 'row')};
  background-color: ${(props) => props.theme.colors.background.secondary};
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  width: 100%;
`;

const ProgressBar = styled.div<{
  progress: number;
  theme: any;
  direction: 'horizontal' | 'vertical';
}>`
  position: absolute;
  ${(props) =>
    props.direction === 'horizontal'
      ? `
      top: 0;
      left: 0;
      height: 4px;
      width: ${props.progress}%;
    `
      : `
      bottom: 0;
      left: 0;
      width: 4px;
      height: ${props.progress}%;
    `}
  background-color: ${(props) => props.theme.colors.primary.main};
  transition: all 0.3s ease;
`;

const StepsContainer = styled.div<{ direction: 'horizontal' | 'vertical' }>`
  display: flex;
  flex-direction: ${(props) => (props.direction === 'horizontal' ? 'row' : 'column')};
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 16px;
  flex-wrap: ${(props) => (props.direction === 'horizontal' ? 'nowrap' : 'wrap')};
  gap: 8px;
`;

const StepItem = styled.div<{
  isActive: boolean;
  isCompleted: boolean;
  isClickable: boolean;
  theme: any;
  direction: 'horizontal' | 'vertical';
  animating: boolean;
}>`
  display: flex;
  flex-direction: ${(props) => (props.direction === 'horizontal' ? 'column' : 'row')};
  align-items: center;
  justify-content: center;
  flex: 1;
  min-width: ${(props) => (props.direction === 'horizontal' ? '100px' : 'auto')};
  max-width: ${(props) => (props.direction === 'horizontal' ? '200px' : 'none')};
  height: ${(props) => (props.direction === 'horizontal' ? '100px' : 'auto')};
  padding: 12px;
  margin: 0 4px;
  cursor: ${(props) => (props.isClickable ? 'pointer' : 'not-allowed')};
  opacity: ${(props) => (props.isActive ? 1 : props.isCompleted ? 0.8 : 0.5)};
  transition: all 0.3s ease;
  border-radius: 8px;
  background-color: ${(props) =>
    props.isActive ? props.theme.colors.background.highlight : 'transparent'};
  transform: ${(props) => (props.animating ? 'scale(1.05)' : 'scale(1)')};
  text-align: center;

  &:hover {
    background-color: ${(props) =>
      props.isClickable ? props.theme.colors.background.hover : 'transparent'};
  }
`;

const StepNumber = styled.div<{ isActive: boolean; isCompleted: boolean; theme: any }>`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: ${(props) =>
    props.isCompleted
      ? props.theme.colors.success.main
      : props.isActive
        ? props.theme.colors.primary.main
        : props.theme.colors.background.tertiary};
  color: ${(props) => props.theme.colors.text.onPrimary};
  font-weight: bold;
  margin: 0 auto 8px auto;
  transition: all 0.3s ease;
`;

const StepContent = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  width: 100%;
`;

const StepLabel = styled.div<{ isActive: boolean; theme: any }>`
  font-weight: ${(props) => (props.isActive ? 'bold' : 'normal')};
  color: ${(props) =>
    props.isActive ? props.theme.colors.text.primary : props.theme.colors.text.secondary};
  margin-bottom: 4px;
  transition: all 0.3s ease;
  text-align: center;
  width: 100%;
`;

const StepDescription = styled.div<{ isActive: boolean; theme: any }>`
  font-size: 0.85rem;
  color: ${(props) => props.theme.colors.text.secondary};
  opacity: ${(props) => (props.isActive ? 1 : 0.7)};
  transition: all 0.3s ease;
  text-align: center;
  width: 100%;
`;

const NavigationButtons = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
`;

const NavigationButton = styled.button<{ disabled: boolean; theme: any; isPrimary?: boolean }>`
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  background-color: ${(props) =>
    props.isPrimary ? props.theme.colors.primary.main : props.theme.colors.background.tertiary};
  color: ${(props) =>
    props.isPrimary ? props.theme.colors.text.onPrimary : props.theme.colors.text.primary};
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  opacity: ${(props) => (props.disabled ? 0.5 : 1)};
  transition: all 0.2s ease;
  font-weight: ${(props) => (props.isPrimary ? 'bold' : 'normal')};

  &:hover {
    background-color: ${(props) =>
      props.disabled
        ? props.isPrimary
          ? props.theme.colors.primary.main
          : props.theme.colors.background.tertiary
        : props.isPrimary
          ? props.theme.colors.primary.dark
          : props.theme.colors.background.hover};
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${(props) => props.theme.colors.primary.light};
  }
`;

export default GuidedFlowNavigator;
