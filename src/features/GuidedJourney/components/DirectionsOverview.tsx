/**
 * Directions Overview Component
 *
 * This component displays an overview of all four compass directions with valuable financial metrics.
 */

import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useGuidedJourney } from '../context/GuidedJourneyContext';
import { useFinancialCompass } from '../../FinancialCompass/context/FinancialCompassContext';
import { formatCurrency, formatPercentage } from '../../../utils/formatters';
import getSafeTheme from '../../../theme/withSafeTheme';

// Styled Components
const Container = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
`;

const DirectionInsightCard = styled(motion.div)<{ cardColor: string }>`
  border-radius: ${({ theme }) => getSafeTheme(theme, (t) => t.borderRadius.lg)};
  background-color: ${({ theme }) => getSafeTheme(theme, (t) => t.colors.background.paper)};
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border-top: 4px solid ${({ cardColor }) => cardColor};
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
  }
`;

const CardHeader = styled.div<{ cardColor: string }>`
  padding: 1.5rem;
  background-color: ${({ theme }) => getSafeTheme(theme, (t) => t.colors.background.default)};
  border-bottom: 1px solid ${({ theme }) => getSafeTheme(theme, (t) => t.colors.divider)};
`;

const CardTitle = styled.h3`
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: ${({ theme }) => getSafeTheme(theme, (t) => t.colors.text.primary)};
`;

const CardDescription = styled.p`
  margin: 0;
  color: ${({ theme }) => getSafeTheme(theme, (t) => t.colors.text.secondary)};
  font-size: 0.875rem;
  line-height: 1.5;
`;

const CardContent = styled.div`
  padding: 1.5rem;
`;

const MetricsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const MetricItem = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
`;

const MetricLabel = styled.div`
  font-size: 0.875rem;
  color: ${({ theme }) => getSafeTheme(theme, (t) => t.colors.text.secondary)};
`;

const MetricValue = styled.div<{ isPositive?: boolean }>`
  font-size: 1.25rem;
  font-weight: 600;
  color: ${({ isPositive, theme }) =>
    isPositive === undefined
      ? getSafeTheme(theme, (t) => t.colors.text.primary)
      : isPositive
        ? getSafeTheme(theme, (t) => t.colors.success.main)
        : getSafeTheme(theme, (t) => t.colors.error.main)};
`;

const MetricDescription = styled.div`
  font-size: 0.8125rem;
  color: ${({ theme }) => getSafeTheme(theme, (t) => t.colors.text.secondary)};
  line-height: 1.4;
`;

const InsightsList = styled.div`
  margin-top: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
`;

const InsightItem = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  background-color: ${({ theme }) => getSafeTheme(theme, (t) => t.colors.background.default)};
  border-radius: ${({ theme }) => getSafeTheme(theme, (t) => t.borderRadius.md)};
`;

const InsightIcon = styled.div`
  font-size: 1.25rem;
  line-height: 1;
`;

const InsightContent = styled.div`
  flex: 1;
`;

const InsightText = styled.div`
  font-size: 0.875rem;
  color: ${({ theme }) => getSafeTheme(theme, (t) => t.colors.text.primary)};
  line-height: 1.5;
`;

const ActionButton = styled.div<{ color: string }>`
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 1.5rem;
  padding: 0.75rem;
  background-color: ${({ color }) => color};
  color: white;
  border-radius: ${({ theme }) => getSafeTheme(theme, (t) => t.borderRadius.md)};
  font-weight: 500;
  font-size: 0.875rem;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 0.9;
  }
`;

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 },
  },
};

/**
 * Directions Overview Component
 */
const DirectionsOverview: React.FC = () => {
  const { navigateToDirection } = useGuidedJourney();
  const { data } = useFinancialCompass();

  // Extract data from all directions
  const northData = data.north || {};
  const eastData = data.east || {};
  const southData = data.south || {};
  const westData = data.west || {};

  // Calculate key financial metrics
  // North Direction (Current Position)
  const incomeDetails = northData.incomeDetails || {};
  const totalMonthlyIncome = parseFloat((incomeDetails as any)?.totalMonthlyIncome || '0');

  // Expenses calculation
  const expenseDetails = northData.expenseDetails || {};
  const totalMonthlyExpenses = parseFloat((expenseDetails as any)?.totalMonthlyExpenses || '0');

  // Calculate monthly cash flow
  const monthlyCashFlow = totalMonthlyIncome - totalMonthlyExpenses;

  // Assets and liabilities
  const assetData = northData.assets || {};
  const liabilities = northData.liabilities || {};

  const totalAssets = parseFloat(
    (northData.netWorthDetails as any)?.totalAssets || (assetData as any)?.totalAssets || '0'
  );

  const totalLiabilities = parseFloat(
    (northData.netWorthDetails as any)?.totalLiabilities ||
      (liabilities as any)?.totalLiabilities ||
      '0'
  );

  const netWorth = totalAssets - totalLiabilities;

  // Calculate savings rate
  const savingsRate = totalMonthlyIncome > 0 ? (monthlyCashFlow / totalMonthlyIncome) * 100 : 0;

  // East Direction (Retirement Vision)
  const retirementGoals = eastData.retirementGoals || {};
  const retirementIncome = eastData.retirementIncome || {};

  // Get age information
  const personalInfo = northData.personalInformation || {};
  const currentAge = parseInt((personalInfo as any)?.age) || 30;
  const retirementAge = parseInt((retirementGoals as any)?.targetRetirementAge) || 65;
  const yearsUntilRetirement = Math.max(0, retirementAge - currentAge);

  // Get savings goal and current retirement savings
  const savingsGoal = parseFloat((retirementGoals as any)?.savingsGoal || '1000000');
  const currentRetirementSavings = parseFloat(
    (retirementGoals as any)?.currentRetirementSavings || '0'
  );
  const retirementProgress = savingsGoal > 0 ? (currentRetirementSavings / savingsGoal) * 100 : 0;

  // South Direction (Protection & Risks)
  const insuranceCoverage = southData.insuranceCoverage || {};
  const protectionGap = southData.protectionGap || {};

  // Extract protection gap amount
  let protectionGapAmount = 0;
  if (typeof protectionGap === 'object') {
    if ('totalGap' in protectionGap && protectionGap.totalGap) {
      protectionGapAmount = parseFloat(protectionGap.totalGap as string);
    } else if ('lifeInsuranceGap' in protectionGap && protectionGap.lifeInsuranceGap) {
      protectionGapAmount = parseFloat(protectionGap.lifeInsuranceGap as string);
    }
  }

  // Check if we have insurance policies
  const hasLifeInsurance =
    typeof insuranceCoverage === 'object' &&
    (('lifeInsurance' in insuranceCoverage && insuranceCoverage.lifeInsurance) ||
      ('hasLifeInsurance' in insuranceCoverage && insuranceCoverage.hasLifeInsurance));

  const hasHealthInsurance =
    typeof insuranceCoverage === 'object' &&
    (('healthInsurance' in insuranceCoverage && insuranceCoverage.healthInsurance) ||
      ('hasHealthInsurance' in insuranceCoverage && insuranceCoverage.hasHealthInsurance));

  const hasDisabilityInsurance =
    typeof insuranceCoverage === 'object' &&
    (('disabilityInsurance' in insuranceCoverage && insuranceCoverage.disabilityInsurance) ||
      ('hasDisabilityInsurance' in insuranceCoverage && insuranceCoverage.hasDisabilityInsurance));

  // West Direction (Legacy Planning)
  const estatePlanning = westData.estatePlanning || {};
  const legacyPlanning = westData.legacyPlanning || {};

  // Check for estate planning documents
  const hasWill =
    typeof estatePlanning === 'object' && 'hasWill' in estatePlanning && estatePlanning.hasWill;

  const hasPOA =
    typeof estatePlanning === 'object' &&
    'hasPowerOfAttorney' in estatePlanning &&
    estatePlanning.hasPowerOfAttorney;

  const hasHealthcareDirective =
    typeof estatePlanning === 'object' &&
    'hasHealthcareDirective' in estatePlanning &&
    estatePlanning.hasHealthcareDirective;

  // Check if legacy planning is defined
  const hasLegacyPlan = legacyPlanning && Object.keys(legacyPlanning).length > 0;

  // Handle direction card click
  const handleDirectionClick = (directionId: string) => {
    if (navigateToDirection) {
      navigateToDirection(directionId as 'north' | 'east' | 'south' | 'west');
    }
  };

  // Direction data with insights
  const directionsData = [
    {
      id: 'north',
      title: 'North: Where You Are',
      description: 'Your current financial position and cash flow.',
      color: '#81C784', // Green
      metrics: [
        {
          label: 'Cash Flow',
          value: formatCurrency(monthlyCashFlow) + '/mo',
          isPositive: monthlyCashFlow >= 0,
          description:
            monthlyCashFlow >= 0
              ? 'Positive cash flow is healthy'
              : 'Negative cash flow needs attention',
        },
        {
          label: 'Net Worth',
          value: formatCurrency(netWorth),
          isPositive: netWorth >= 0,
          description: `Total assets: ${formatCurrency(totalAssets)}, Total liabilities: ${formatCurrency(totalLiabilities)}`,
        },
        {
          label: 'Savings Rate',
          value: formatPercentage(savingsRate),
          isPositive: savingsRate >= 15,
          description:
            savingsRate >= 20
              ? 'Excellent savings rate'
              : savingsRate >= 15
                ? 'Good savings rate'
                : 'Consider increasing your savings rate',
        },
      ],
      insights: [
        monthlyCashFlow < 0
          ? {
              icon: '⚠️',
              text: `Your expenses exceed your income by ${formatCurrency(Math.abs(monthlyCashFlow))} monthly.`,
            }
          : {
              icon: '✅',
              text: `You have a positive cash flow of ${formatCurrency(monthlyCashFlow)} monthly.`,
            },
        savingsRate < 15
          ? { icon: '💡', text: 'Aim for a savings rate of at least 15-20% of your income.' }
          : { icon: '🎯', text: 'Your savings rate is on target. Keep it up!' },
      ],
    },
    {
      id: 'east',
      title: "East: Where You're Going",
      description: 'Your retirement planning and future goals.',
      color: '#FFB74D', // Orange
      metrics: [
        {
          label: 'Retirement Progress',
          value: formatPercentage(retirementProgress),
          isPositive: retirementProgress >= 50,
          description: `${formatCurrency(currentRetirementSavings)} of ${formatCurrency(savingsGoal)} goal`,
        },
        {
          label: 'Years to Retirement',
          value: yearsUntilRetirement.toString(),
          description: `Planning to retire at age ${retirementAge}`,
        },
        {
          label: 'Monthly Savings Needed',
          value:
            yearsUntilRetirement > 0 && savingsGoal > currentRetirementSavings
              ? formatCurrency(
                  (savingsGoal - currentRetirementSavings) / (yearsUntilRetirement * 12)
                )
              : '$0',
          description: 'Estimated monthly savings needed to reach goal',
        },
      ],
      insights: [
        yearsUntilRetirement > 0 && savingsGoal > currentRetirementSavings
          ? {
              icon: '📊',
              text: `You need to save ${formatCurrency((savingsGoal - currentRetirementSavings) / (yearsUntilRetirement * 12))} monthly to reach your retirement goal.`,
            }
          : { icon: '🎉', text: 'You are on track to meet your retirement savings goal!' },
        {
          icon: '🧠',
          text: `Consider a ${yearsUntilRetirement > 15 ? 'growth-oriented' : yearsUntilRetirement > 5 ? 'balanced' : 'conservative'} investment approach based on your time horizon.`,
        },
      ],
    },
    {
      id: 'south',
      title: 'South: What Protects You',
      description: 'Your insurance coverage and risk management.',
      color: '#FF8A65', // Red-Orange
      metrics: [
        {
          label: 'Protection Gap',
          value: protectionGapAmount > 0 ? formatCurrency(protectionGapAmount) : 'None',
          isPositive: protectionGapAmount === 0,
          description:
            protectionGapAmount > 0
              ? 'Additional coverage needed'
              : 'Your coverage appears adequate',
        },
        {
          label: 'Life Insurance',
          value: hasLifeInsurance ? 'In Place' : 'Needed',
          isPositive: hasLifeInsurance,
          description: hasLifeInsurance
            ? 'You have life insurance coverage'
            : 'Consider getting life insurance',
        },
        {
          label: 'Disability Insurance',
          value: hasDisabilityInsurance ? 'In Place' : 'Needed',
          isPositive: hasDisabilityInsurance,
          description: hasDisabilityInsurance
            ? 'You have disability coverage'
            : 'Consider disability insurance',
        },
      ],
      insights: [
        protectionGapAmount > 0
          ? {
              icon: '🛡️',
              text: `You have an insurance protection gap of ${formatCurrency(protectionGapAmount)}.`,
            }
          : { icon: '✅', text: 'Your insurance protection appears adequate.' },
        !hasLifeInsurance || !hasDisabilityInsurance || !hasHealthInsurance
          ? {
              icon: '💡',
              text: `Consider adding ${!hasLifeInsurance ? 'life' : ''}${!hasLifeInsurance && !hasDisabilityInsurance ? ' and ' : ''}${!hasDisabilityInsurance ? 'disability' : ''} insurance to your protection plan.`,
            }
          : {
              icon: '🔍',
              text: 'Review your coverage annually to ensure it meets your changing needs.',
            },
      ],
    },
    {
      id: 'west',
      title: "West: What You'll Leave Behind",
      description: 'Your estate planning and legacy.',
      color: '#90CAF9', // Blue
      metrics: [
        {
          label: 'Estate Documents',
          value: hasWill && hasPOA ? 'Complete' : 'Incomplete',
          isPositive: hasWill && hasPOA,
          description: `${hasWill ? 'Will ✓' : 'Will ✗'} ${hasPOA ? 'POA ✓' : 'POA ✗'} ${hasHealthcareDirective ? 'Healthcare Directive ✓' : 'Healthcare Directive ✗'}`,
        },
        {
          label: 'Legacy Planning',
          value: hasLegacyPlan ? 'Defined' : 'Not Defined',
          isPositive: hasLegacyPlan,
          description: hasLegacyPlan
            ? 'You have defined your legacy vision'
            : 'Consider defining your legacy vision',
        },
        {
          label: 'Tax Strategy',
          value:
            typeof westData.taxPlanning === 'object' &&
            westData.taxPlanning &&
            'hasTaxStrategy' in westData.taxPlanning &&
            westData.taxPlanning.hasTaxStrategy === 'yes'
              ? 'In Place'
              : 'Needed',
          isPositive:
            typeof westData.taxPlanning === 'object' &&
            westData.taxPlanning &&
            'hasTaxStrategy' in westData.taxPlanning &&
            westData.taxPlanning.hasTaxStrategy === 'yes',
          description: 'Tax-efficient estate transfer strategy',
        },
      ],
      insights: [
        !hasWill || !hasPOA
          ? {
              icon: '📝',
              text: `You need to complete your ${!hasWill ? 'will' : ''}${!hasWill && !hasPOA ? ' and ' : ''}${!hasPOA ? 'power of attorney' : ''} documents.`,
            }
          : { icon: '✅', text: 'Your essential estate planning documents are in place.' },
        !hasLegacyPlan
          ? {
              icon: '💡',
              text: 'Consider creating a legacy letter to share your values and wishes with future generations.',
            }
          : {
              icon: '🌱',
              text: 'You have defined your legacy vision. Review it periodically as your values evolve.',
            },
      ],
    },
  ];

  return (
    <motion.div variants={containerVariants} initial="hidden" animate="visible">
      <Container>
        {directionsData.map((direction) => (
          <motion.div key={direction.id} variants={itemVariants}>
            <DirectionInsightCard
              cardColor={direction.color}
              onClick={() => handleDirectionClick(direction.id)}
              whileHover={{ y: -4 }}
              transition={{ duration: 0.2 }}
            >
              <CardHeader cardColor={direction.color}>
                <CardTitle>{direction.title}</CardTitle>
                <CardDescription>{direction.description}</CardDescription>
              </CardHeader>

              <CardContent>
                <MetricsContainer>
                  {direction.metrics.map((metric, index) => (
                    <MetricItem key={index}>
                      <MetricLabel>{metric.label}</MetricLabel>
                      <MetricValue isPositive={metric.isPositive}>{metric.value}</MetricValue>
                      <MetricDescription>{metric.description}</MetricDescription>
                    </MetricItem>
                  ))}
                </MetricsContainer>

                <InsightsList>
                  {direction.insights.map((insight, index) => (
                    <InsightItem key={index}>
                      <InsightIcon>{insight.icon}</InsightIcon>
                      <InsightContent>
                        <InsightText>{insight.text}</InsightText>
                      </InsightContent>
                    </InsightItem>
                  ))}
                </InsightsList>

                <ActionButton color={direction.color}>
                  Explore {direction.title.split(':')[0]} Direction
                </ActionButton>
              </CardContent>
            </DirectionInsightCard>
          </motion.div>
        ))}
      </Container>
    </motion.div>
  );
};

export default DirectionsOverview;
