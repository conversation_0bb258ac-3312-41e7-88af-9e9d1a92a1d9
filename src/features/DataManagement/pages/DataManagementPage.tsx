/**
 * Data Management Page
 *
 * This page provides a user interface for data export, import, backup, and reset.
 */

import React from 'react';
import styled from 'styled-components';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '../../../context/ThemeContext';
import DataManagementUI from '../components/DataManagementUI';

const DataManagementPage: React.FC = () => {
  const { theme } = useTheme();
  const navigate = useNavigate();

  const handleClose = () => {
    navigate('/dashboard');
  };

  return (
    <Container theme={theme}>
      <DataManagementUI onClose={handleClose} />
    </Container>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 24px;
  background-color: ${(props) => props.theme.colors.background.default};
`;

export default DataManagementPage;
