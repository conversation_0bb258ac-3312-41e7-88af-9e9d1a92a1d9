/**
 * Data Validator Component
 *
 * This component provides a user interface for validating and correcting data.
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../context/ThemeContext';
import DataManagementService, {
  DataValidationResult,
} from '../../../services/DataManagementService';

// Theme interface for styled components
interface ThemeProps {
  theme: {
    colors: {
      background: {
        paper: string;
        default: string;
        hover: string;
      };
      primary: {
        main: string;
        dark: string;
        contrastText: string;
      };
      error: {
        main: string;
        dark: string;
        light: string;
        contrastText: string;
      };
      success: {
        light: string;
        dark: string;
      };
      warning: {
        light: string;
        dark: string;
      };
      text: {
        secondary: string;
      };
      border: string;
    };
  };
}

interface DataValidatorProps {
  onValidationComplete?: (result: DataValidationResult) => void;
}

const DataValidator: React.FC<DataValidatorProps> = ({ onValidationComplete }) => {
  const { theme } = useTheme();
  // We'll use this service in a real implementation
  // const dataService = DataManagementService.getInstance();

  // State for validation
  const [isValidating, setIsValidating] = useState(false);
  const [validationResult, setValidationResult] = useState<DataValidationResult | null>(null);
  const [selectedDataTypes, setSelectedDataTypes] = useState({
    personalData: true,
    financialData: true,
    connectionData: true,
  });

  // Handle validation
  const handleValidate = async () => {
    setIsValidating(true);

    try {
      // In a real implementation, we would call a method on the service
      // For this demo, we'll create a mock validation result
      const mockValidationResult: DataValidationResult = {
        isValid: false,
        errors: [
          {
            path: 'lifecompass_financial_data.income[0].amount',
            message: 'Income amount cannot be negative',
            severity: 'error',
          },
          {
            path: 'lifecompass_financial_data.expenses[2].category',
            message: 'Invalid expense category',
            severity: 'warning',
          },
        ],
        warnings: [
          {
            path: 'lifecompass_user_data.profile.email',
            message: 'Email address may be invalid',
          },
          {
            path: 'lifecompass_connections_data.connections[0].lastSync',
            message: 'Connection has not been synced in over 30 days',
          },
        ],
      };

      setValidationResult(mockValidationResult);

      if (onValidationComplete) {
        onValidationComplete(mockValidationResult);
      }
    } catch (err) {
      console.error('Error validating data:', err);
    } finally {
      setIsValidating(false);
    }
  };

  // Handle fix all
  const handleFixAll = async () => {
    // In a real implementation, we would call a method on the service
    // For this demo, we'll just update the validation result
    if (validationResult) {
      const updatedResult: DataValidationResult = {
        ...validationResult,
        isValid: true,
        errors: [],
        warnings: validationResult.warnings, // Keep warnings
      };

      setValidationResult(updatedResult);

      if (onValidationComplete) {
        onValidationComplete(updatedResult);
      }
    }
  };

  // Handle fix individual error
  const handleFixError = (index: number) => {
    if (validationResult) {
      const updatedErrors = [...validationResult.errors];
      updatedErrors.splice(index, 1);

      const updatedResult: DataValidationResult = {
        ...validationResult,
        isValid: updatedErrors.length === 0,
        errors: updatedErrors,
      };

      setValidationResult(updatedResult);

      if (onValidationComplete) {
        onValidationComplete(updatedResult);
      }
    }
  };

  // Handle ignore warning
  const handleIgnoreWarning = (index: number) => {
    if (validationResult) {
      const updatedWarnings = [...validationResult.warnings];
      updatedWarnings.splice(index, 1);

      const updatedResult: DataValidationResult = {
        ...validationResult,
        warnings: updatedWarnings,
      };

      setValidationResult(updatedResult);

      if (onValidationComplete) {
        onValidationComplete(updatedResult);
      }
    }
  };

  // Handle data type selection
  const handleDataTypeChange = (dataType: 'personalData' | 'financialData' | 'connectionData') => {
    setSelectedDataTypes({
      ...selectedDataTypes,
      [dataType]: !selectedDataTypes[dataType],
    });
  };

  // Extract theme properties needed by styled components
  const themeProps = {
    theme: {
      colors: {
        background: {
          paper: theme.colors.background.paper,
          default: theme.colors.background.default,
          hover: '#f5f5f5',
        },
        primary: {
          main: theme.colors.primary.main,
          dark: theme.colors.primary.dark,
          contrastText: theme.colors.primary.contrastText,
        },
        error: {
          main: theme.colors.error.main,
          dark: theme.colors.error.dark,
          light: theme.colors.error.light,
          contrastText: theme.colors.error.contrastText,
        },
        success: {
          light: theme.colors.success.light,
          dark: theme.colors.success.dark,
        },
        warning: {
          light: theme.colors.warning.light,
          dark: theme.colors.warning.dark,
        },
        text: {
          secondary: theme.colors.text.secondary,
        },
        border: theme.colors.divider || '#e0e0e0',
      },
    },
  };

  return (
    <Container {...themeProps}>
      <Header>
        <Title>Data Validator</Title>
      </Header>

      <OptionGroup>
        <SectionSubtitle>Select Data Types to Validate</SectionSubtitle>
        <CheckboxOption>
          <Checkbox
            type="checkbox"
            id="validatePersonalData"
            checked={selectedDataTypes.personalData}
            onChange={() => handleDataTypeChange('personalData')}
          />
          <CheckboxLabel htmlFor="validatePersonalData">Personal Data</CheckboxLabel>
        </CheckboxOption>

        <CheckboxOption>
          <Checkbox
            type="checkbox"
            id="validateFinancialData"
            checked={selectedDataTypes.financialData}
            onChange={() => handleDataTypeChange('financialData')}
          />
          <CheckboxLabel htmlFor="validateFinancialData">Financial Data</CheckboxLabel>
        </CheckboxOption>

        <CheckboxOption>
          <Checkbox
            type="checkbox"
            id="validateConnectionData"
            checked={selectedDataTypes.connectionData}
            onChange={() => handleDataTypeChange('connectionData')}
          />
          <CheckboxLabel htmlFor="validateConnectionData">Connection Data</CheckboxLabel>
        </CheckboxOption>
      </OptionGroup>

      <ActionButton
        onClick={handleValidate}
        disabled={
          isValidating ||
          (!selectedDataTypes.personalData &&
            !selectedDataTypes.financialData &&
            !selectedDataTypes.connectionData)
        }
        {...themeProps}
      >
        {isValidating ? 'Validating...' : 'Validate Data'}
      </ActionButton>

      {validationResult && (
        <ResultContainer {...themeProps}>
          <ResultHeader>
            <ResultTitle>
              Validation Result: {validationResult.isValid ? 'Valid' : 'Invalid'}
            </ResultTitle>
            {validationResult.errors.length > 0 && (
              <FixAllButton onClick={handleFixAll} {...themeProps}>
                Fix All Errors
              </FixAllButton>
            )}
          </ResultHeader>

          {validationResult.errors.length > 0 && (
            <ErrorsContainer>
              <SectionSubtitle>Errors ({validationResult.errors.length})</SectionSubtitle>
              {validationResult.errors.map((error, index) => (
                <ErrorItem key={index} {...themeProps}>
                  <ErrorInfo>
                    <ErrorPath>{error.path}</ErrorPath>
                    <ErrorMessage severity={error.severity}>{error.message}</ErrorMessage>
                  </ErrorInfo>
                  <FixButton onClick={() => handleFixError(index)} {...themeProps}>
                    Fix
                  </FixButton>
                </ErrorItem>
              ))}
            </ErrorsContainer>
          )}

          {validationResult.warnings.length > 0 && (
            <WarningsContainer>
              <SectionSubtitle>Warnings ({validationResult.warnings.length})</SectionSubtitle>
              {validationResult.warnings.map((warning, index) => (
                <WarningItem key={index} {...themeProps}>
                  <WarningInfo>
                    <WarningPath>{warning.path}</WarningPath>
                    <WarningMessage {...themeProps}>{warning.message}</WarningMessage>
                  </WarningInfo>
                  <IgnoreButton onClick={() => handleIgnoreWarning(index)} {...themeProps}>
                    Ignore
                  </IgnoreButton>
                </WarningItem>
              ))}
            </WarningsContainer>
          )}

          {validationResult.isValid && validationResult.warnings.length === 0 && (
            <SuccessMessage {...themeProps}>All data is valid! No issues found.</SuccessMessage>
          )}
        </ResultContainer>
      )}
    </Container>
  );
};

// Styled components
const Container = styled.div<ThemeProps>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
  margin-top: 24px;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const Title = styled.h3`
  margin: 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const OptionGroup = styled.div`
  margin-bottom: 24px;
`;

const SectionSubtitle = styled.h4`
  margin: 0 0 8px 0;
  font-size: 1rem;
  font-weight: 500;
  color: #666;
`;

const CheckboxOption = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 8px;
`;

const Checkbox = styled.input`
  margin-right: 8px;
`;

const CheckboxLabel = styled.label`
  cursor: pointer;
`;

const ActionButton = styled.button<ThemeProps & { disabled?: boolean }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  font-weight: 500;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  opacity: ${(props) => (props.disabled ? 0.6 : 1)};
  transition: all 0.2s;
  width: 100%;

  &:hover:not(:disabled) {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

const ResultContainer = styled.div<ThemeProps>`
  margin-top: 24px;
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  padding: 16px;
`;

const ResultHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const ResultTitle = styled.h4`
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
`;

const FixAllButton = styled.button<ThemeProps>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

const ErrorsContainer = styled.div`
  margin-bottom: 24px;
`;

const ErrorItem = styled.div<ThemeProps>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid ${(props) => props.theme.colors.error.light};
  border-radius: 4px;
  margin-bottom: 8px;
  background-color: ${(props) => props.theme.colors.error.light};
`;

const ErrorInfo = styled.div`
  flex: 1;
`;

const ErrorPath = styled.div`
  font-weight: 500;
  margin-bottom: 4px;
  font-family: monospace;
  font-size: 0.9rem;
`;

const ErrorMessage = styled.div<{ severity: 'error' | 'warning' | 'info' }>`
  color: ${(props) =>
    props.severity === 'error' ? '#d32f2f' : props.severity === 'warning' ? '#f57c00' : '#1976d2'};
`;

const FixButton = styled.button<ThemeProps>`
  background-color: ${(props) => props.theme.colors.error.main};
  color: ${(props) => props.theme.colors.error.contrastText};
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  margin-left: 16px;

  &:hover {
    background-color: ${(props) => props.theme.colors.error.dark};
  }
`;

const WarningsContainer = styled.div`
  margin-bottom: 24px;
`;

const WarningItem = styled.div<ThemeProps>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid ${(props) => props.theme.colors.warning.light};
  border-radius: 4px;
  margin-bottom: 8px;
  background-color: ${(props) => props.theme.colors.warning.light};
`;

const WarningInfo = styled.div`
  flex: 1;
`;

const WarningPath = styled.div`
  font-weight: 500;
  margin-bottom: 4px;
  font-family: monospace;
  font-size: 0.9rem;
`;

const WarningMessage = styled.div<ThemeProps>`
  color: ${(props) => props.theme.colors.warning.dark};
`;

const IgnoreButton = styled.button<ThemeProps>`
  background-color: ${(props) => props.theme.colors.warning.dark};
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  margin-left: 16px;

  &:hover {
    background-color: ${(props) => props.theme.colors.warning.dark};
    opacity: 0.9;
  }
`;

const SuccessMessage = styled.div<ThemeProps>`
  background-color: ${(props) => props.theme.colors.success.light};
  color: ${(props) => props.theme.colors.success.dark};
  padding: 12px 16px;
  border-radius: 4px;
  text-align: center;
  font-weight: 500;
`;

export default DataValidator;
