/**
 * Data Management UI Component
 *
 * This component provides a user interface for data export, import, backup, and reset.
 * Enhanced with backup management and data validation.
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../context/ThemeContext';
import { useFinancialCompass } from '../../FinancialCompass/context/FinancialCompassContext';
import { useSeasonsOfSelf } from '../../SeasonsOfSelf/context/SeasonsOfSelfContext';
import DataManagementService, {
  DataExportOptions,
  DataImportOptions,
  DataBackupOptions,
  DataResetOptions,
  DataImportResult,
  DataExportResult,
  DataBackupResult,
  DataResetResult,
  DataConflict,
  DataValidationResult,
} from '../../../services/DataManagementService';
import BackupManager from './BackupManager';
import DataValidator from './DataValidator';

interface DataManagementUIProps {
  onClose?: () => void;
}

const DataManagementUI: React.FC<DataManagementUIProps> = ({ onClose }) => {
  const { theme } = useTheme();
  const { data: financialCompassData, refetchData } = useFinancialCompass();
  const { data: seasonsOfSelfData } = useSeasonsOfSelf();

  // Get the data management service
  const dataService = DataManagementService.getInstance();

  // State for active tab
  const [activeTab, setActiveTab] = useState<'export' | 'import' | 'backup' | 'reset' | 'privacy'>(
    'export'
  );

  // State for export options
  const [exportOptions, setExportOptions] = useState<DataExportOptions>({
    includePersonalData: true,
    includeFinancialData: true,
    includeConnectionData: true,
    format: 'json',
    encryptData: false,
  });

  // State for import options
  const [importOptions, setImportOptions] = useState<DataImportOptions>({
    validateBeforeImport: true,
    resolveConflicts: 'manual',
    mergeStrategy: 'merge',
  });

  // State for backup options
  const [backupOptions, setBackupOptions] = useState<DataBackupOptions>({
    autoBackup: true,
    backupFrequency: 'daily',
    maxBackups: 5,
    encryptBackups: true,
  });

  // State for reset options
  const [resetOptions, setResetOptions] = useState<DataResetOptions>({
    resetPersonalData: false,
    resetFinancialData: false,
    resetConnectionData: false,
    createBackupBeforeReset: true,
  });

  // State for results
  const [exportResult, setExportResult] = useState<DataExportResult | null>(null);
  const [importResult, setImportResult] = useState<DataImportResult | null>(null);
  const [backupResult, setBackupResult] = useState<DataBackupResult | null>(null);
  const [resetResult, setResetResult] = useState<DataResetResult | null>(null);

  // State for import file
  const [importFile, setImportFile] = useState<File | null>(null);

  // State for conflicts
  const [conflicts, setConflicts] = useState<DataConflict[]>([]);

  // State for confirmation
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [confirmationAction, setConfirmationAction] = useState<
    'reset' | 'import' | 'delete' | null
  >(null);

  // State for loading indicator
  const [isImporting, setIsImporting] = useState(false);

  // Handle export
  const handleExport = async () => {
    const result = await dataService.exportData(exportOptions);
    setExportResult(result);

    if (result.success && result.data) {
      // Create a download link
      if (typeof result.data === 'string') {
        const blob = new Blob([result.data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `lifecompass_export_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      } else if (result.data instanceof Blob) {
        const url = URL.createObjectURL(result.data);
        const a = document.createElement('a');
        a.href = url;
        a.download = `lifecompass_export_${new Date().toISOString().split('T')[0]}.pdf`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    }
  };

  // Handle import file selection
  const handleImportFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setImportFile(e.target.files[0]);
      setImportResult(null); // Clear previous result on new file selection
      setConflicts([]); // Clear previous conflicts
    }
  };

  // Handle import
  const handleImport = async () => {
    if (!importFile) return;

    // Show confirmation for import
    setConfirmationAction('import');
    setShowConfirmation(true);
  };

  // Handle import confirmation
  const handleImportConfirm = async () => {
    if (!importFile) return;

    setIsImporting(true); // Add state for loading indicator
    setImportResult(null); // Clear previous result
    setConflicts([]); // Clear previous conflicts

    // Read the file
    const reader = new FileReader();
    reader.onload = async (e) => {
      if (e.target && e.target.result) {
        const fileContent = e.target.result as string;
        const result = await dataService.importData(fileContent, importOptions);
        setImportResult(result);

        if (!result.success && result.conflicts && result.conflicts.length > 0) {
          setConflicts(result.conflicts);
        } else if (result.success) {
          // Optionally, refetch data into contexts after successful import
          refetchData(); // Assuming FinancialCompassContext has this method
          // TODO: Need to refetch data for other contexts like SeasonsOfSelf if needed
        }
      }
      setIsImporting(false); // Hide loading indicator
    };
    reader.onerror = () => {
      console.error('Error reading file');
      setImportResult({
        success: false,
        conflicts: [],
        validationResult: {
          isValid: false,
          errors: [{ path: 'file', message: 'Failed to read file.', severity: 'error' }],
          warnings: [],
        },
        error: 'Failed to read file.',
      });
      setIsImporting(false); // Hide loading indicator
    };
    reader.readAsText(importFile);

    setShowConfirmation(false);
  };

  // Handle backup
  const handleBackup = async () => {
    const result = await dataService.createBackup(backupOptions);
    setBackupResult(result);
  };

  // Handle reset
  const handleReset = async () => {
    // Show confirmation for reset
    setConfirmationAction('reset');
    setShowConfirmation(true);
  };

  // Handle reset confirmation
  const handleResetConfirm = async () => {
    const result = await dataService.resetData(resetOptions);
    setResetResult(result);
    setShowConfirmation(false);
  };

  // Handle conflict resolution
  const handleResolveConflict = (index: number, resolution: 'existing' | 'imported' | 'manual') => {
    const updatedConflicts = [...conflicts];
    updatedConflicts[index].resolution = resolution;
    setConflicts(updatedConflicts);
  };

  // Handle manual conflict resolution
  const handleManualConflictResolution = (index: number, value: any) => {
    const updatedConflicts = [...conflicts];
    updatedConflicts[index].manualValue = value;
    updatedConflicts[index].resolution = 'manual';
    setConflicts(updatedConflicts);
  };

  // Handle apply conflict resolutions
  const handleApplyConflictResolutions = async () => {
    // In a real implementation, we would apply the conflict resolutions
    // For this demo, we'll just clear the conflicts
    setConflicts([]);
  };

  // Handle cancel confirmation
  const handleCancelConfirmation = () => {
    setShowConfirmation(false);
    setConfirmationAction(null);
  };

  // Handle delete all data
  const handleDeleteAllData = async () => {
    // Show confirmation for delete
    setConfirmationAction('delete');
    setShowConfirmation(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    const result = await dataService.deleteAllData();
    // TODO: Handle result and provide feedback to user
    setShowConfirmation(false);
    // Optionally, navigate to a logout or home page after deletion
    // onClose?.(); // Close modal after deletion
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Data Management</Title>
        <CloseButton onClick={onClose} theme={theme}>
          ×
        </CloseButton>
      </Header>

      <TabContainer theme={theme}>
        <Tab isActive={activeTab === 'export'} onClick={() => setActiveTab('export')} theme={theme}>
          Export
        </Tab>
        <Tab isActive={activeTab === 'import'} onClick={() => setActiveTab('import')} theme={theme}>
          Import
        </Tab>
        <Tab isActive={activeTab === 'backup'} onClick={() => setActiveTab('backup')} theme={theme}>
          Backup
        </Tab>
        <Tab isActive={activeTab === 'reset'} onClick={() => setActiveTab('reset')} theme={theme}>
          Reset
        </Tab>
        <Tab
          isActive={activeTab === 'privacy'}
          onClick={() => setActiveTab('privacy')}
          theme={theme}
        >
          Privacy
        </Tab>
      </TabContainer>

      <ContentContainer theme={theme}>
        {activeTab === 'export' && (
          <div>
            <SectionTitle>Export Options</SectionTitle>
            <OptionGroup>
              <CheckboxOption>
                <Checkbox
                  type="checkbox"
                  id="includePersonalData"
                  checked={exportOptions.includePersonalData}
                  onChange={(e) =>
                    setExportOptions({
                      ...exportOptions,
                      includePersonalData: e.target.checked,
                    })
                  }
                />
                <CheckboxLabel htmlFor="includePersonalData">Include Personal Data</CheckboxLabel>
              </CheckboxOption>

              <CheckboxOption>
                <Checkbox
                  type="checkbox"
                  id="includeFinancialData"
                  checked={exportOptions.includeFinancialData}
                  onChange={(e) =>
                    setExportOptions({
                      ...exportOptions,
                      includeFinancialData: e.target.checked,
                    })
                  }
                />
                <CheckboxLabel htmlFor="includeFinancialData">Include Financial Data</CheckboxLabel>
              </CheckboxOption>

              <CheckboxOption>
                <Checkbox
                  type="checkbox"
                  id="includeConnectionData"
                  checked={exportOptions.includeConnectionData}
                  onChange={(e) =>
                    setExportOptions({
                      ...exportOptions,
                      includeConnectionData: e.target.checked,
                    })
                  }
                />
                <CheckboxLabel htmlFor="includeConnectionData">
                  Include Connection Data
                </CheckboxLabel>
              </CheckboxOption>
            </OptionGroup>

            <OptionGroup>
              <RadioOption>
                <Radio
                  type="radio"
                  id="formatJson"
                  name="format"
                  value="json"
                  checked={exportOptions.format === 'json'}
                  onChange={() =>
                    setExportOptions({
                      ...exportOptions,
                      format: 'json',
                    })
                  }
                />
                <RadioLabel htmlFor="formatJson">JSON Format</RadioLabel>
              </RadioOption>

              <RadioOption>
                <Radio
                  type="radio"
                  id="formatPdf"
                  name="format"
                  value="pdf"
                  checked={exportOptions.format === 'pdf'}
                  onChange={() =>
                    setExportOptions({
                      ...exportOptions,
                      format: 'pdf',
                    })
                  }
                />
                <RadioLabel htmlFor="formatPdf">PDF Format</RadioLabel>
              </RadioOption>
            </OptionGroup>

            <OptionGroup>
              <CheckboxOption>
                <Checkbox
                  type="checkbox"
                  id="encryptData"
                  checked={exportOptions.encryptData}
                  onChange={(e) =>
                    setExportOptions({
                      ...exportOptions,
                      encryptData: e.target.checked,
                    })
                  }
                />
                <CheckboxLabel htmlFor="encryptData">Encrypt Data</CheckboxLabel>
              </CheckboxOption>
            </OptionGroup>

            <ActionButton onClick={handleExport} theme={theme}>
              Export Data
            </ActionButton>

            {exportResult && (
              <ResultContainer success={exportResult.success} theme={theme}>
                {exportResult.success ? (
                  <p>Export successful! Your data has been downloaded.</p>
                ) : (
                  <p>Export failed: {exportResult.error}</p>
                )}
              </ResultContainer>
            )}
          </div>
        )}

        {activeTab === 'import' && (
          <div>
            <SectionTitle>Import Options</SectionTitle>
            <OptionGroup>
              <CheckboxOption>
                <Checkbox
                  type="checkbox"
                  id="validateBeforeImport"
                  checked={importOptions.validateBeforeImport}
                  onChange={(e) =>
                    setImportOptions({
                      ...importOptions,
                      validateBeforeImport: e.target.checked,
                    })
                  }
                />
                <CheckboxLabel htmlFor="validateBeforeImport">Validate Before Import</CheckboxLabel>
              </CheckboxOption>
            </OptionGroup>

            <OptionGroup>
              <SectionSubtitle>Conflict Resolution</SectionSubtitle>
              <RadioOption>
                <Radio
                  type="radio"
                  id="resolveManual"
                  name="resolveConflicts"
                  value="manual"
                  checked={importOptions.resolveConflicts === 'manual'}
                  onChange={() =>
                    setImportOptions({
                      ...importOptions,
                      resolveConflicts: 'manual',
                    })
                  }
                />
                <RadioLabel htmlFor="resolveManual">Manual Resolution</RadioLabel>
              </RadioOption>

              <RadioOption>
                <Radio
                  type="radio"
                  id="resolveNewer"
                  name="resolveConflicts"
                  value="newer"
                  checked={importOptions.resolveConflicts === 'newer'}
                  onChange={() =>
                    setImportOptions({
                      ...importOptions,
                      resolveConflicts: 'newer',
                    })
                  }
                />
                <RadioLabel htmlFor="resolveNewer">Use Newer Data</RadioLabel>
              </RadioOption>

              <RadioOption>
                <Radio
                  type="radio"
                  id="resolveExisting"
                  name="resolveConflicts"
                  value="existing"
                  checked={importOptions.resolveConflicts === 'existing'}
                  onChange={() =>
                    setImportOptions({
                      ...importOptions,
                      resolveConflicts: 'existing',
                    })
                  }
                />
                <RadioLabel htmlFor="resolveExisting">Keep Existing Data</RadioLabel>
              </RadioOption>

              <RadioOption>
                <Radio
                  type="radio"
                  id="resolveImported"
                  name="resolveConflicts"
                  value="imported"
                  checked={importOptions.resolveConflicts === 'imported'}
                  onChange={() =>
                    setImportOptions({
                      ...importOptions,
                      resolveConflicts: 'imported',
                    })
                  }
                />
                <RadioLabel htmlFor="resolveImported">Use Imported Data</RadioLabel>
              </RadioOption>
            </OptionGroup>

            <OptionGroup>
              <SectionSubtitle>Merge Strategy</SectionSubtitle>
              <RadioOption>
                <Radio
                  type="radio"
                  id="mergeReplace"
                  name="mergeStrategy"
                  value="replace"
                  checked={importOptions.mergeStrategy === 'replace'}
                  onChange={() =>
                    setImportOptions({
                      ...importOptions,
                      mergeStrategy: 'replace',
                    })
                  }
                />
                <RadioLabel htmlFor="mergeReplace">Replace Existing Data</RadioLabel>
              </RadioOption>

              <RadioOption>
                <Radio
                  type="radio"
                  id="mergeMerge"
                  name="mergeStrategy"
                  value="merge"
                  checked={importOptions.mergeStrategy === 'merge'}
                  onChange={() =>
                    setImportOptions({
                      ...importOptions,
                      mergeStrategy: 'merge',
                    })
                  }
                />
                <RadioLabel htmlFor="mergeMerge">Merge with Existing Data</RadioLabel>
              </RadioOption>

              <RadioOption>
                <Radio
                  type="radio"
                  id="mergeAppend"
                  name="mergeStrategy"
                  value="append"
                  checked={importOptions.mergeStrategy === 'append'}
                  onChange={() =>
                    setImportOptions({
                      ...importOptions,
                      mergeStrategy: 'append',
                    })
                  }
                />
                <RadioLabel htmlFor="mergeAppend">Append to Existing Data</RadioLabel>
              </RadioOption>
            </OptionGroup>

            <FileInputContainer theme={theme}>
              <FileInputLabel htmlFor="importFile" theme={theme}>
                {importFile ? importFile.name : 'Select File to Import'}
              </FileInputLabel>
              <FileInput
                type="file"
                id="importFile"
                accept=".json"
                onChange={handleImportFileChange}
              />
            </FileInputContainer>

            <ActionButton
              onClick={handleImport}
              disabled={!importFile || isImporting}
              theme={theme}
            >
              {isImporting ? 'Importing...' : 'Import Data'}
            </ActionButton>

            {importResult && (
              <ResultContainer success={importResult.success} theme={theme}>
                {importResult.success ? (
                  <p>Import successful!</p>
                ) : (
                  <p>Import failed: {importResult.error}</p>
                )}

                {importResult.validationResult &&
                  importResult.validationResult.errors.length > 0 && (
                    <div>
                      <p>Validation errors:</p>
                      <ul>
                        {importResult.validationResult.errors.map((error, index) => (
                          <li key={index}>
                            {error.path}: {error.message}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
              </ResultContainer>
            )}

            {conflicts.length > 0 && (
              <ConflictsContainer theme={theme}>
                <SectionSubtitle>Conflicts</SectionSubtitle>
                {conflicts.map((conflict, index) => (
                  <ConflictItem key={index} theme={theme}>
                    <ConflictPath>{conflict.path}</ConflictPath>
                    <ConflictResolutionOptions>
                      <RadioOption>
                        <Radio
                          type="radio"
                          id={`conflict-${index}-existing`}
                          name={`conflict-${index}`}
                          value="existing"
                          checked={conflict.resolution === 'existing'}
                          onChange={() => handleResolveConflict(index, 'existing')}
                        />
                        <RadioLabel htmlFor={`conflict-${index}-existing`}>
                          Keep Existing
                        </RadioLabel>
                      </RadioOption>

                      <RadioOption>
                        <Radio
                          type="radio"
                          id={`conflict-${index}-imported`}
                          name={`conflict-${index}`}
                          value="imported"
                          checked={conflict.resolution === 'imported'}
                          onChange={() => handleResolveConflict(index, 'imported')}
                        />
                        <RadioLabel htmlFor={`conflict-${index}-imported`}>Use Imported</RadioLabel>
                      </RadioOption>

                      <RadioOption>
                        <Radio
                          type="radio"
                          id={`conflict-${index}-manual`}
                          name={`conflict-${index}`}
                          value="manual"
                          checked={conflict.resolution === 'manual'}
                          onChange={() => handleResolveConflict(index, 'manual')}
                        />
                        <RadioLabel htmlFor={`conflict-${index}-manual`}>Manual Edit</RadioLabel>
                      </RadioOption>
                    </ConflictResolutionOptions>

                    {conflict.resolution === 'manual' && (
                      <ConflictManualEdit>
                        <textarea
                          value={
                            conflict.manualValue
                              ? JSON.stringify(conflict.manualValue, null, 2)
                              : ''
                          }
                          onChange={(e) => {
                            try {
                              const value = JSON.parse(e.target.value);
                              handleManualConflictResolution(index, value);
                            } catch (error) {
                              // Invalid JSON, ignore
                            }
                          }}
                          rows={5}
                        />
                      </ConflictManualEdit>
                    )}
                  </ConflictItem>
                ))}

                <ActionButton onClick={handleApplyConflictResolutions} theme={theme}>
                  Apply Resolutions
                </ActionButton>
              </ConflictsContainer>
            )}
          </div>
        )}

        {activeTab === 'backup' && (
          <div>
            <SectionTitle>Backup Options</SectionTitle>
            <OptionGroup>
              <CheckboxOption>
                <Checkbox
                  type="checkbox"
                  id="autoBackup"
                  checked={backupOptions.autoBackup}
                  onChange={(e) =>
                    setBackupOptions({
                      ...backupOptions,
                      autoBackup: e.target.checked,
                    })
                  }
                />
                <CheckboxLabel htmlFor="autoBackup">Enable Auto Backup</CheckboxLabel>
              </CheckboxOption>
            </OptionGroup>

            <OptionGroup>
              <SectionSubtitle>Backup Frequency</SectionSubtitle>
              <RadioOption>
                <Radio
                  type="radio"
                  id="frequencyDaily"
                  name="backupFrequency"
                  value="daily"
                  checked={backupOptions.backupFrequency === 'daily'}
                  onChange={() =>
                    setBackupOptions({
                      ...backupOptions,
                      backupFrequency: 'daily',
                    })
                  }
                  disabled={!backupOptions.autoBackup}
                />
                <RadioLabel htmlFor="frequencyDaily">Daily</RadioLabel>
              </RadioOption>

              <RadioOption>
                <Radio
                  type="radio"
                  id="frequencyWeekly"
                  name="backupFrequency"
                  value="weekly"
                  checked={backupOptions.backupFrequency === 'weekly'}
                  onChange={() =>
                    setBackupOptions({
                      ...backupOptions,
                      backupFrequency: 'weekly',
                    })
                  }
                  disabled={!backupOptions.autoBackup}
                />
                <RadioLabel htmlFor="frequencyWeekly">Weekly</RadioLabel>
              </RadioOption>

              <RadioOption>
                <Radio
                  type="radio"
                  id="frequencyMonthly"
                  name="backupFrequency"
                  value="monthly"
                  checked={backupOptions.backupFrequency === 'monthly'}
                  onChange={() =>
                    setBackupOptions({
                      ...backupOptions,
                      backupFrequency: 'monthly',
                    })
                  }
                  disabled={!backupOptions.autoBackup}
                />
                <RadioLabel htmlFor="frequencyMonthly">Monthly</RadioLabel>
              </RadioOption>

              <RadioOption>
                <Radio
                  type="radio"
                  id="frequencyOnChange"
                  name="backupFrequency"
                  value="onchange"
                  checked={backupOptions.backupFrequency === 'onchange'}
                  onChange={() =>
                    setBackupOptions({
                      ...backupOptions,
                      backupFrequency: 'onchange',
                    })
                  }
                  disabled={!backupOptions.autoBackup}
                />
                <RadioLabel htmlFor="frequencyOnChange">On Change</RadioLabel>
              </RadioOption>
            </OptionGroup>

            <OptionGroup>
              <SectionSubtitle>Max Backups</SectionSubtitle>
              <RangeContainer>
                <RangeInput
                  type="range"
                  min="1"
                  max="10"
                  value={backupOptions.maxBackups}
                  onChange={(e) =>
                    setBackupOptions({
                      ...backupOptions,
                      maxBackups: parseInt(e.target.value),
                    })
                  }
                />
                <RangeValue>{backupOptions.maxBackups}</RangeValue>
              </RangeContainer>
            </OptionGroup>

            <OptionGroup>
              <CheckboxOption>
                <Checkbox
                  type="checkbox"
                  id="encryptBackups"
                  checked={backupOptions.encryptBackups}
                  onChange={(e) =>
                    setBackupOptions({
                      ...backupOptions,
                      encryptBackups: e.target.checked,
                    })
                  }
                />
                <CheckboxLabel htmlFor="encryptBackups">Encrypt Backups</CheckboxLabel>
              </CheckboxOption>
            </OptionGroup>

            <ActionButton onClick={handleBackup} theme={theme}>
              Create Backup Now
            </ActionButton>

            {backupResult && (
              <ResultContainer success={backupResult.success} theme={theme}>
                {backupResult.success ? (
                  <p>Backup created successfully! Backup ID: {backupResult.backupId}</p>
                ) : (
                  <p>Backup failed: {backupResult.error}</p>
                )}
              </ResultContainer>
            )}

            {/* Enhanced Backup Manager */}
            <BackupManager
              onBackupRestored={() => {
                // Refresh the UI after a backup is restored
                setBackupResult(null);
              }}
            />
          </div>
        )}

        {activeTab === 'reset' && (
          <div>
            <SectionTitle>Reset Options</SectionTitle>
            <WarningMessage theme={theme}>
              Warning: Resetting data will permanently delete the selected data types. This action
              cannot be undone unless you create a backup.
            </WarningMessage>

            <OptionGroup>
              <CheckboxOption>
                <Checkbox
                  type="checkbox"
                  id="resetPersonalData"
                  checked={resetOptions.resetPersonalData}
                  onChange={(e) =>
                    setResetOptions({
                      ...resetOptions,
                      resetPersonalData: e.target.checked,
                    })
                  }
                />
                <CheckboxLabel htmlFor="resetPersonalData">Reset Personal Data</CheckboxLabel>
              </CheckboxOption>

              <CheckboxOption>
                <Checkbox
                  type="checkbox"
                  id="resetFinancialData"
                  checked={resetOptions.resetFinancialData}
                  onChange={(e) =>
                    setResetOptions({
                      ...resetOptions,
                      resetFinancialData: e.target.checked,
                    })
                  }
                />
                <CheckboxLabel htmlFor="resetFinancialData">Reset Financial Data</CheckboxLabel>
              </CheckboxOption>

              <CheckboxOption>
                <Checkbox
                  type="checkbox"
                  id="resetConnectionData"
                  checked={resetOptions.resetConnectionData}
                  onChange={(e) =>
                    setResetOptions({
                      ...resetOptions,
                      resetConnectionData: e.target.checked,
                    })
                  }
                />
                <CheckboxLabel htmlFor="resetConnectionData">Reset Connection Data</CheckboxLabel>
              </CheckboxOption>
            </OptionGroup>

            <OptionGroup>
              <CheckboxOption>
                <Checkbox
                  type="checkbox"
                  id="createBackupBeforeReset"
                  checked={resetOptions.createBackupBeforeReset}
                  onChange={(e) =>
                    setResetOptions({
                      ...resetOptions,
                      createBackupBeforeReset: e.target.checked,
                    })
                  }
                />
                <CheckboxLabel htmlFor="createBackupBeforeReset">
                  Create Backup Before Reset
                </CheckboxLabel>
              </CheckboxOption>
            </OptionGroup>

            <ActionButton
              onClick={handleReset}
              disabled={
                !resetOptions.resetPersonalData &&
                !resetOptions.resetFinancialData &&
                !resetOptions.resetConnectionData
              }
              theme={theme}
              danger
            >
              Reset Data
            </ActionButton>

            {resetResult && (
              <ResultContainer success={resetResult.success} theme={theme}>
                {resetResult.success ? (
                  <p>
                    Data reset successfully!
                    {resetResult.backupCreated && (
                      <span> Backup created with ID: {resetResult.backupId}</span>
                    )}
                  </p>
                ) : (
                  <p>Reset failed: {resetResult.error}</p>
                )}
              </ResultContainer>
            )}

            {/* Data Validator */}
            <SectionTitle style={{ marginTop: '32px' }}>Data Validation</SectionTitle>
            <p>
              Validate your data to identify and fix potential issues before they cause problems.
              This can help ensure data integrity and prevent errors.
            </p>
            <DataValidator
              onValidationComplete={(result: DataValidationResult) => {
                // Handle validation result
                console.log('Validation result:', result);
              }}
            />
          </div>
        )}

        {activeTab === 'privacy' && (
          <div>
            <SectionTitle>Data Privacy</SectionTitle>
            <InfoBox>
              <InfoTitle>
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-5h2v2h-2v-2zm0-8h2v6h-2V7z" />
                </svg>
                Manage Your Data Privacy
              </InfoTitle>
              <InfoText>
                You have control over your personal data. Use the options below to manage how your
                data is handled.
              </InfoText>
            </InfoBox>

            <OptionGroup>
              <ActionButton onClick={handleExport} theme={theme}>
                Export All My Data (JSON)
              </ActionButton>
              <InfoText>Download a copy of all your data in JSON format.</InfoText>
            </OptionGroup>

            <OptionGroup>
              <ActionButton onClick={handleDeleteAllData} theme={theme} danger>
                Delete All My Data
              </ActionButton>
              <InfoText>
                Permanently delete all your data from this application. This action cannot be
                undone.
              </InfoText>
            </OptionGroup>

            {/* TODO: Add user consent management here */}
            {/* TODO: Link to Privacy Policy */}
          </div>
        )}
      </ContentContainer>

      {showConfirmation && (
        <ConfirmationOverlay>
          <ConfirmationDialog theme={theme}>
            <ConfirmationTitle>
              Confirm{' '}
              {confirmationAction === 'reset'
                ? 'Reset'
                : confirmationAction === 'import'
                  ? 'Import'
                  : 'Delete Data'}
            </ConfirmationTitle>
            <ConfirmationMessage>
              {confirmationAction === 'reset'
                ? 'Are you sure you want to reset the selected data? This action cannot be undone.'
                : confirmationAction === 'import'
                  ? 'Are you sure you want to import this data? This may overwrite existing data.'
                  : 'Are you sure you want to permanently delete ALL of your data? This action cannot be undone.'}
            </ConfirmationMessage>
            <ConfirmationButtons>
              <CancelButton onClick={handleCancelConfirmation} theme={theme}>
                Cancel
              </CancelButton>
              <ConfirmButton
                onClick={
                  confirmationAction === 'reset'
                    ? handleResetConfirm
                    : confirmationAction === 'import'
                      ? handleImportConfirm
                      : handleDeleteConfirm
                }
                theme={theme}
                danger={confirmationAction === 'reset' || confirmationAction === 'delete'}
              >
                {confirmationAction === 'reset'
                  ? 'Reset'
                  : confirmationAction === 'import'
                    ? 'Import'
                    : 'Delete Data'}
              </ConfirmButton>
            </ConfirmationButtons>
          </ConfirmationDialog>
        </ConfirmationOverlay>
      )}
    </Container>
  );
};

// Styled components
const Container = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  overflow: hidden;
`;

const Header = styled.div<{ theme: any }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
`;

const Title = styled.h2<{ theme: any }>`
  margin: 0;
  font-size: 1.5rem;
  font-weight: 500;
  color: ${(props) => props.theme.colors.primary.contrastText};
`;

const CloseButton = styled.button<{ theme: any }>`
  background: none;
  border: none;
  color: ${(props) => props.theme.colors.primary.contrastText};
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;

const TabContainer = styled.div<{ theme: any }>`
  display: flex;
  border-bottom: 1px solid ${(props) => props.theme.colors.border};
`;

const Tab = styled.button<{ isActive: boolean; theme: any }>`
  padding: 12px 24px;
  background-color: ${(props) =>
    props.isActive ? props.theme.colors.background.paper : 'transparent'};
  color: ${(props) =>
    props.isActive ? props.theme.colors.primary.main : props.theme.colors.text.secondary};
  border: none;
  border-bottom: 2px solid
    ${(props) => (props.isActive ? props.theme.colors.primary.main : 'transparent')};
  cursor: pointer;
  font-weight: ${(props) => (props.isActive ? '500' : '400')};
  flex: 1;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
    color: ${(props) => props.theme.colors.primary.main};
  }
`;

const ContentContainer = styled.div<{ theme: any }>`
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const SectionSubtitle = styled.h4`
  margin: 0 0 8px 0;
  font-size: 1rem;
  font-weight: 500;
  color: #666;
`;

const OptionGroup = styled.div`
  margin-bottom: 24px;
`;

const CheckboxOption = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 8px;
`;

const Checkbox = styled.input`
  margin-right: 8px;
`;

const CheckboxLabel = styled.label`
  cursor: pointer;
`;

const RadioOption = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 8px;
`;

const Radio = styled.input`
  margin-right: 8px;
`;

const RadioLabel = styled.label`
  cursor: pointer;
`;

const RangeContainer = styled.div`
  display: flex;
  align-items: center;
`;

const RangeInput = styled.input`
  flex: 1;
  margin-right: 16px;
`;

const RangeValue = styled.span`
  min-width: 24px;
  text-align: center;
`;

const FileInputContainer = styled.div<{ theme: any }>`
  margin-bottom: 24px;
  display: flex;
  flex-direction: column;
`;

const FileInputLabel = styled.label<{ theme: any }>`
  display: block;
  padding: 12px 16px;
  background-color: ${(props) => props.theme.colors.background.default};
  border: 1px dashed ${(props) => props.theme.colors.border};
  border-radius: 4px;
  text-align: center;
  cursor: pointer;
  margin-bottom: 8px;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.hover};
  }
`;

const FileInput = styled.input`
  display: none;
`;

const ActionButton = styled.button<{ theme: any; disabled?: boolean; danger?: boolean }>`
  background-color: ${(props) =>
    props.danger ? props.theme.colors.error.main : props.theme.colors.primary.main};
  color: ${(props) =>
    props.danger ? props.theme.colors.error.contrastText : props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 500;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  opacity: ${(props) => (props.disabled ? 0.6 : 1)};
  transition: all 0.2s;

  &:hover:not(:disabled) {
    background-color: ${(props) =>
      props.danger ? props.theme.colors.error.dark : props.theme.colors.primary.dark};
  }
`;

const ResultContainer = styled.div<{ success: boolean; theme: any }>`
  margin-top: 16px;
  padding: 12px 16px;
  border-radius: 4px;
  background-color: ${(props) =>
    props.success ? props.theme.colors.success.light : props.theme.colors.error.light};
  color: ${(props) =>
    props.success ? props.theme.colors.success.dark : props.theme.colors.error.dark};
`;

const ConflictsContainer = styled.div<{ theme: any }>`
  margin-top: 24px;
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  padding: 16px;
`;

const ConflictItem = styled.div<{ theme: any }>`
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid ${(props) => props.theme.colors.border};

  &:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }
`;

const ConflictPath = styled.div`
  font-weight: 500;
  margin-bottom: 8px;
`;

const ConflictResolutionOptions = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 8px;
`;

const ConflictManualEdit = styled.div`
  margin-top: 8px;

  textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-family: monospace;
    font-size: 0.9rem;
  }
`;

const WarningMessage = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.warning.light};
  color: ${(props) => props.theme.colors.warning.dark};
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 24px;
`;

const ConfirmationOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ConfirmationDialog = styled.div<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 24px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
`;

const ConfirmationTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const ConfirmationMessage = styled.p`
  margin: 0 0 24px 0;
  line-height: 1.5;
`;

const ConfirmationButtons = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 12px;
`;

const CancelButton = styled.button<{ theme: any }>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.text.secondary};
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const ConfirmButton = styled.button<{ theme: any; danger?: boolean }>`
  background-color: ${(props) =>
    props.danger ? props.theme.colors.error.main : props.theme.colors.primary.main};
  color: ${(props) =>
    props.danger ? props.theme.colors.error.contrastText : props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) =>
      props.danger ? props.theme.colors.error.dark : props.theme.colors.primary.dark};
  }
`;

const Input = styled.input`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
`;

// Add styled components for import feedback and conflicts
const FileName = styled.div`
  margin-top: 8px;
  font-size: 0.9rem;
  color: #555;
`;

const ResultContainer = styled.div<{ success: boolean }>`
  margin-top: 20px;
  padding: 15px;
  border-radius: 4px;
  background-color: ${(props) =>
    props.success ? '#e8f5e9' : '#ffebee'}; /* Green or Red background */
  color: ${(props) => (props.success ? '#2e7d32' : '#c62828')}; /* Green or Red text */
  border: 1px solid ${(props) => (props.success ? '#a5d6a7' : '#ef9a9a')}; /* Green or Red border */
  font-size: 0.9rem;

  ul {
    margin-top: 10px;
    padding-left: 20px;
  }

  li {
    margin-bottom: 5px;
  }

  pre {
    background-color: #f4f4f4;
    padding: 10px;
    border-radius: 4px;
    white-space: pre-wrap; /* Handle long strings */
    word-break: break-all; /* Break long words */
    font-size: 0.8rem;
  }
`;

const ConflictsContainer = styled.div<{ theme: any }>`
  margin-top: 20px;
  padding: 20px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.background.paper};
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const ConflictsTitle = styled.h4`
  margin: 0 0 15px 0;
  font-size: 1.1rem;
  color: ${(props) => props.theme.colors.warning.main};
`;

const ConflictsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 20px;
`;

const ConflictItem = styled.div`
  border: 1px solid #ffecb3; /* Light orange border */
  border-left: 4px solid ${(props) => props.theme.colors.warning.main}; /* Orange accent */
  border-radius: 4px;
  padding: 15px;
  background-color: #fff9c4; /* Light yellow background */
`;

const ConflictValues = styled.div`
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 10px;
  margin-bottom: 10px;
  font-family: monospace;
  font-size: 0.9rem;
  white-space: pre-wrap; /* Preserve formatting */
  word-break: break-all; /* Break long words */

  pre {
    background-color: #fff;
    padding: 8px;
    border: 1px dashed #ccc;
    border-radius: 4px;
    overflow-x: auto; /* Enable horizontal scrolling if needed */
  }
`;

const ConflictValueLabel = styled.div`
  font-weight: bold;
`;

const ConflictResolution = styled.div`
  margin-top: 15px;

  select,
  input[type='text'] {
    margin-top: 8px;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 0.9rem;
  }
`;

const ManualResolutionInput = styled.input`
  width: calc(100% - 16px); /* Adjust width considering padding */
  display: block; /* Ensure it takes its own line */
`;

const Select = styled.select`
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  background-color: white;
  cursor: pointer;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
`;

const OptionsGroup = styled.div`
  background-color: ${(props) => props.theme.colors.background.default};
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
`;

const OptionsTitle = styled.h4`
  margin: 0 0 15px 0;
  font-size: 1.1rem;
  font-weight: 600;
`;

const CheckboxLabel = styled.label`
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 0.9rem;

  input[type='checkbox'] {
    margin-right: 8px;
  }
`;

export default DataManagementUI;
