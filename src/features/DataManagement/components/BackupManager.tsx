/**
 * Backup Manager Component
 *
 * This component provides a user interface for managing backups,
 * including viewing, restoring, and deleting backups.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../context/ThemeContext';
import DataManagementService, { DataImportResult } from '../../../services/DataManagementService';

interface Backup {
  id: string;
  timestamp: number;
  size: string;
  description?: string;
}

interface BackupManagerProps {
  onBackupRestored?: () => void;
}

const BackupManager: React.FC<BackupManagerProps> = ({ onBackupRestored }) => {
  const { theme } = useTheme();
  const dataService = DataManagementService.getInstance();

  // State for backups
  const [backups, setBackups] = useState<Backup[]>([]);
  const [selectedBackupId, setSelectedBackupId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [restoreResult, setRestoreResult] = useState<DataImportResult | null>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [confirmationAction, setConfirmationAction] = useState<'restore' | 'delete' | null>(null);

  // Load backups on mount
  useEffect(() => {
    loadBackups();
  }, []);

  // Load backups from the service
  const loadBackups = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // In a real implementation, we would get this from the service
      // For this demo, we'll create some mock backups
      const mockBackups: Backup[] = [
        {
          id: 'backup_1620000000000',
          timestamp: 1620000000000,
          size: '256 KB',
          description: 'Auto backup',
        },
        {
          id: 'backup_1625000000000',
          timestamp: 1625000000000,
          size: '312 KB',
          description: 'Before financial data update',
        },
        {
          id: 'backup_1630000000000',
          timestamp: 1630000000000,
          size: '328 KB',
          description: 'Manual backup',
        },
      ];

      setBackups(mockBackups);
    } catch (err) {
      setError('Failed to load backups');
      console.error('Error loading backups:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Create a new backup
  const handleCreateBackup = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await dataService.createBackup({
        autoBackup: false,
        backupFrequency: 'onchange',
        maxBackups: 10,
        encryptBackups: true,
      });

      if (result.success) {
        // Add the new backup to the list
        const newBackup: Backup = {
          id: result.backupId,
          timestamp: result.timestamp,
          size: 'Unknown', // In a real implementation, we would get this from the service
          description: 'Manual backup',
        };

        setBackups([newBackup, ...backups]);
      } else {
        setError(`Failed to create backup: ${result.error}`);
      }
    } catch (err) {
      setError('Failed to create backup');
      console.error('Error creating backup:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Restore a backup
  const handleRestoreBackup = async () => {
    if (!selectedBackupId) return;

    setConfirmationAction('restore');
    setShowConfirmation(true);
  };

  // Delete a backup
  const handleDeleteBackup = async () => {
    if (!selectedBackupId) return;

    setConfirmationAction('delete');
    setShowConfirmation(true);
  };

  // Handle confirmation
  const handleConfirm = async () => {
    if (!selectedBackupId) return;

    setIsLoading(true);
    setError(null);

    try {
      if (confirmationAction === 'restore') {
        const result = await dataService.restoreBackup(selectedBackupId);
        setRestoreResult(result);

        if (result.success) {
          if (onBackupRestored) {
            onBackupRestored();
          }
        } else {
          setError(`Failed to restore backup: ${result.error}`);
        }
      } else if (confirmationAction === 'delete') {
        // In a real implementation, we would call a method on the service
        // For this demo, we'll just remove it from the list
        setBackups(backups.filter((backup) => backup.id !== selectedBackupId));
        setSelectedBackupId(null);
      }
    } catch (err) {
      setError(`Failed to ${confirmationAction} backup`);
      console.error(`Error ${confirmationAction}ing backup:`, err);
    } finally {
      setIsLoading(false);
      setShowConfirmation(false);
      setConfirmationAction(null);
    }
  };

  // Handle cancel confirmation
  const handleCancelConfirmation = () => {
    setShowConfirmation(false);
    setConfirmationAction(null);
  };

  // Format date
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  // Extract theme properties needed by styled components
  const themeProps = {
    theme: {
      colors: {
        background: {
          paper: theme.colors.background.paper,
          default: theme.colors.background.default,
          hover: '#f5f5f5',
        },
        primary: {
          main: theme.colors.primary.main,
          dark: theme.colors.primary.dark,
          contrastText: theme.colors.primary.contrastText,
        },
        error: {
          main: theme.colors.error.main,
          dark: theme.colors.error.dark,
          light: theme.colors.error.light,
          contrastText: theme.colors.error.contrastText,
        },
        success: {
          light: theme.colors.success.light,
          dark: theme.colors.success.dark,
        },
        text: {
          secondary: theme.colors.text.secondary,
        },
        border: theme.colors.divider || '#e0e0e0',
      },
    },
  };

  return (
    <Container {...themeProps}>
      <Header>
        <Title>Backup Manager</Title>
        <CreateButton onClick={handleCreateBackup} disabled={isLoading} {...themeProps}>
          Create New Backup
        </CreateButton>
      </Header>

      {error && <ErrorMessage {...themeProps}>{error}</ErrorMessage>}

      {restoreResult && restoreResult.success && (
        <SuccessMessage {...themeProps}>Backup restored successfully!</SuccessMessage>
      )}

      <BackupList {...themeProps}>
        {isLoading ? (
          <LoadingMessage>Loading backups...</LoadingMessage>
        ) : backups.length === 0 ? (
          <EmptyMessage>No backups found</EmptyMessage>
        ) : (
          backups.map((backup) => (
            <BackupItem
              key={backup.id}
              isSelected={selectedBackupId === backup.id}
              onClick={() => setSelectedBackupId(backup.id)}
              {...themeProps}
            >
              <BackupInfo>
                <BackupDate>{formatDate(backup.timestamp)}</BackupDate>
                <BackupSize>{backup.size}</BackupSize>
                {backup.description && <BackupDescription>{backup.description}</BackupDescription>}
              </BackupInfo>
              <BackupId>ID: {backup.id}</BackupId>
            </BackupItem>
          ))
        )}
      </BackupList>

      <ActionBar>
        <ActionButton
          onClick={handleRestoreBackup}
          disabled={!selectedBackupId || isLoading}
          {...themeProps}
        >
          Restore Selected Backup
        </ActionButton>

        <ActionButton
          onClick={handleDeleteBackup}
          disabled={!selectedBackupId || isLoading}
          {...themeProps}
          danger
        >
          Delete Selected Backup
        </ActionButton>
      </ActionBar>

      {showConfirmation && (
        <ConfirmationOverlay>
          <ConfirmationDialog {...themeProps}>
            <ConfirmationTitle>
              Confirm {confirmationAction === 'restore' ? 'Restore' : 'Delete'}
            </ConfirmationTitle>
            <ConfirmationMessage>
              {confirmationAction === 'restore'
                ? 'Are you sure you want to restore this backup? This will overwrite your current data.'
                : 'Are you sure you want to delete this backup? This action cannot be undone.'}
            </ConfirmationMessage>
            <ConfirmationButtons>
              <CancelButton onClick={handleCancelConfirmation} {...themeProps}>
                Cancel
              </CancelButton>
              <ConfirmButton
                onClick={handleConfirm}
                {...themeProps}
                danger={confirmationAction === 'delete'}
              >
                {confirmationAction === 'restore' ? 'Restore' : 'Delete'}
              </ConfirmButton>
            </ConfirmationButtons>
          </ConfirmationDialog>
        </ConfirmationOverlay>
      )}
    </Container>
  );
};

// Styled components
interface ThemeProps {
  theme: {
    colors: {
      background: {
        paper: string;
        default: string;
        hover: string;
      };
      primary: {
        main: string;
        dark: string;
        contrastText: string;
      };
      error: {
        main: string;
        dark: string;
        light: string;
        contrastText: string;
      };
      success: {
        light: string;
        dark: string;
      };
      text: {
        secondary: string;
      };
      border: string;
    };
  };
}

const Container = styled.div<ThemeProps>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
  margin-top: 24px;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const Title = styled.h3`
  margin: 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const CreateButton = styled.button<ThemeProps & { disabled?: boolean }>`
  background-color: ${(props) => props.theme.colors.primary.main};
  color: ${(props) => props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  opacity: ${(props) => (props.disabled ? 0.6 : 1)};
  transition: all 0.2s;

  &:hover:not(:disabled) {
    background-color: ${(props) => props.theme.colors.primary.dark};
  }
`;

const BackupList = styled.div<ThemeProps>`
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 16px;
`;

const BackupItem = styled.div<ThemeProps & { isSelected: boolean }>`
  padding: 12px 16px;
  border-bottom: 1px solid ${(props) => props.theme.colors.border};
  background-color: ${(props) =>
    props.isSelected ? props.theme.colors.background.hover : 'transparent'};
  cursor: pointer;
  transition: all 0.2s;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: ${(props) => props.theme.colors.background.hover};
  }
`;

const BackupInfo = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 4px;
`;

const BackupDate = styled.div`
  font-weight: 500;
  margin-right: 12px;
`;

const BackupSize = styled.div`
  font-size: 0.9rem;
  color: #666;
  margin-right: 12px;
`;

const BackupDescription = styled.div`
  font-size: 0.9rem;
  color: #666;
  font-style: italic;
`;

const BackupId = styled.div`
  font-size: 0.8rem;
  color: #999;
`;

const ActionBar = styled.div`
  display: flex;
  justify-content: space-between;
  gap: 12px;
`;

const ActionButton = styled.button<ThemeProps & { disabled?: boolean; danger?: boolean }>`
  flex: 1;
  background-color: ${(props) =>
    props.danger ? props.theme.colors.error.main : props.theme.colors.primary.main};
  color: ${(props) =>
    props.danger ? props.theme.colors.error.contrastText : props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  opacity: ${(props) => (props.disabled ? 0.6 : 1)};
  transition: all 0.2s;

  &:hover:not(:disabled) {
    background-color: ${(props) =>
      props.danger ? props.theme.colors.error.dark : props.theme.colors.primary.dark};
  }
`;

const LoadingMessage = styled.div`
  padding: 16px;
  text-align: center;
  color: #666;
`;

const EmptyMessage = styled.div`
  padding: 16px;
  text-align: center;
  color: #666;
`;

const ErrorMessage = styled.div<ThemeProps>`
  background-color: ${(props) => props.theme.colors.error.light};
  color: ${(props) => props.theme.colors.error.dark};
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 16px;
`;

const SuccessMessage = styled.div<ThemeProps>`
  background-color: ${(props) => props.theme.colors.success.light};
  color: ${(props) => props.theme.colors.success.dark};
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 16px;
`;

const ConfirmationOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ConfirmationDialog = styled.div<ThemeProps>`
  background-color: ${(props) => props.theme.colors.background.paper};
  border-radius: 8px;
  padding: 24px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
`;

const ConfirmationTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

const ConfirmationMessage = styled.p`
  margin: 0 0 24px 0;
  line-height: 1.5;
`;

const ConfirmationButtons = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 12px;
`;

const CancelButton = styled.button<ThemeProps>`
  background-color: transparent;
  color: ${(props) => props.theme.colors.text.secondary};
  border: 1px solid ${(props) => props.theme.colors.border};
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => props.theme.colors.background.default};
  }
`;

const ConfirmButton = styled.button<ThemeProps & { danger?: boolean }>`
  background-color: ${(props) =>
    props.danger ? props.theme.colors.error.main : props.theme.colors.primary.main};
  color: ${(props) =>
    props.danger ? props.theme.colors.error.contrastText : props.theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) =>
      props.danger ? props.theme.colors.error.dark : props.theme.colors.primary.dark};
  }
`;

export default BackupManager;
