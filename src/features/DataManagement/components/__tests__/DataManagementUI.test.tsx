/**
 * Tests for DataManagementUI Component
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import DataManagementUI from '../DataManagementUI';

// Mock the necessary context providers
jest.mock('../../../../context/ThemeContext', () => ({
  useTheme: () => ({
    theme: {
      colors: {
        background: { paper: '#fff', default: '#f5f5f5', hover: '#eee' },
        text: { primary: '#000', secondary: '#666' },
        primary: { main: '#1976d2', dark: '#115293', contrastText: '#fff' },
        error: { main: '#d32f2f', dark: '#c62828', contrastText: '#fff', light: '#ffebee' },
        success: { light: '#e8f5e9', dark: '#2e7d32' },
        warning: { light: '#fff8e1', dark: '#f57c00' },
        divider: '#e0e0e0',
        border: '#e0e0e0',
      },
    },
  }),
}));

jest.mock('../../../FinancialCompass/context/FinancialCompassContext', () => ({
  useFinancialCompass: () => ({
    data: {},
  }),
}));

jest.mock('../../../SeasonsOfSelf/context/SeasonsOfSelfContext', () => ({
  useSeasonsOfSelf: () => ({
    data: {},
  }),
}));

// Mock the DataManagementService
jest.mock('../../../../services/DataManagementService', () => ({
  getInstance: () => ({
    exportData: jest.fn().mockResolvedValue({
      success: true,
      data: JSON.stringify({ test: 'data' }),
    }),
    importData: jest.fn().mockResolvedValue({
      success: true,
    }),
    createBackup: jest.fn().mockResolvedValue({
      success: true,
      backupId: 'test-backup-id',
    }),
    resetData: jest.fn().mockResolvedValue({
      success: true,
      backupCreated: true,
      backupId: 'test-backup-id',
    }),
  }),
}));

describe('DataManagementUI Component', () => {
  it('renders the component', () => {
    render(<DataManagementUI />);
    expect(screen.getByText('Data Management')).toBeInTheDocument();
  });
});
