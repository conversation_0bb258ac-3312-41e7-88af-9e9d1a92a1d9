/**
 * Tests for BackupManager Component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ThemeProvider } from '../../../../context/ThemeContext';
import BackupManager from '../BackupManager';
import DataManagementService from '../../../../services/DataManagementService';

// Mock the ThemeContext
jest.mock('../../../../context/ThemeContext', () => ({
  useTheme: () => ({
    theme: {
      colors: {
        background: {
          paper: '#ffffff',
          default: '#f5f5f5',
          hover: '#f0f0f0',
        },
        primary: {
          main: '#1976d2',
          dark: '#115293',
          contrastText: '#ffffff',
        },
        error: {
          main: '#d32f2f',
          dark: '#c62828',
          light: '#ffebee',
          contrastText: '#ffffff',
        },
        success: {
          light: '#e8f5e9',
          dark: '#2e7d32',
        },
        warning: {
          light: '#fff8e1',
          dark: '#f57c00',
        },
        text: {
          secondary: '#757575',
        },
        border: '#e0e0e0',
      },
    },
  }),
}));

// Mock the DataManagementService
jest.mock('../../../../services/DataManagementService', () => {
  const mockService = {
    createBackup: jest.fn().mockResolvedValue({
      success: true,
      backupId: 'test-backup-id',
      timestamp: Date.now(),
    }),
    restoreBackup: jest.fn().mockResolvedValue({
      success: true,
      conflicts: [],
      validationResult: {
        isValid: true,
        errors: [],
        warnings: [],
      },
    }),
  };

  return {
    getInstance: jest.fn(() => mockService),
  };
});

describe('BackupManager Component', () => {
  const renderComponent = () => {
    return render(
      <ThemeProvider>
        <BackupManager onBackupRestored={jest.fn()} />
      </ThemeProvider>
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the component with title and buttons', () => {
    renderComponent();

    expect(screen.getByText('Backup Manager')).toBeInTheDocument();
    expect(screen.getByText('Create New Backup')).toBeInTheDocument();
    expect(screen.getByText('Restore Selected Backup')).toBeDisabled();
    expect(screen.getByText('Delete Selected Backup')).toBeDisabled();
  });

  it('displays mock backups after loading', async () => {
    renderComponent();

    // Wait for the backups to load
    await waitFor(() => {
      expect(screen.getByText(/Auto backup/)).toBeInTheDocument();
    });

    expect(screen.getByText(/Before financial data update/)).toBeInTheDocument();
    expect(screen.getByText(/Manual backup/)).toBeInTheDocument();
  });

  it('enables action buttons when a backup is selected', async () => {
    renderComponent();

    // Wait for the backups to load
    await waitFor(() => {
      expect(screen.getByText(/Auto backup/)).toBeInTheDocument();
    });

    // Click on a backup to select it
    fireEvent.click(screen.getByText(/Auto backup/));

    // Check that the action buttons are enabled
    expect(screen.getByText('Restore Selected Backup')).not.toBeDisabled();
    expect(screen.getByText('Delete Selected Backup')).not.toBeDisabled();
  });

  it('shows confirmation dialog when restore is clicked', async () => {
    renderComponent();

    // Wait for the backups to load
    await waitFor(() => {
      expect(screen.getByText(/Auto backup/)).toBeInTheDocument();
    });

    // Click on a backup to select it
    fireEvent.click(screen.getByText(/Auto backup/));

    // Click restore button
    fireEvent.click(screen.getByText('Restore Selected Backup'));

    // Check that the confirmation dialog is shown
    expect(screen.getByText('Confirm Restore')).toBeInTheDocument();
    expect(screen.getByText(/Are you sure you want to restore this backup/)).toBeInTheDocument();
  });

  it('shows confirmation dialog when delete is clicked', async () => {
    renderComponent();

    // Wait for the backups to load
    await waitFor(() => {
      expect(screen.getByText(/Auto backup/)).toBeInTheDocument();
    });

    // Click on a backup to select it
    fireEvent.click(screen.getByText(/Auto backup/));

    // Click delete button
    fireEvent.click(screen.getByText('Delete Selected Backup'));

    // Check that the confirmation dialog is shown
    expect(screen.getByText('Confirm Delete')).toBeInTheDocument();
    expect(screen.getByText(/Are you sure you want to delete this backup/)).toBeInTheDocument();
  });

  it('calls createBackup when create button is clicked', async () => {
    renderComponent();

    // Click create button
    fireEvent.click(screen.getByText('Create New Backup'));

    // Check that createBackup was called
    await waitFor(() => {
      expect(DataManagementService.getInstance().createBackup).toHaveBeenCalled();
    });
  });

  it('calls restoreBackup when restore is confirmed', async () => {
    renderComponent();

    // Wait for the backups to load
    await waitFor(() => {
      expect(screen.getByText(/Auto backup/)).toBeInTheDocument();
    });

    // Click on a backup to select it
    fireEvent.click(screen.getByText(/Auto backup/));

    // Click restore button
    fireEvent.click(screen.getByText('Restore Selected Backup'));

    // Click confirm button
    fireEvent.click(screen.getByText('Restore'));

    // Check that restoreBackup was called
    await waitFor(() => {
      expect(DataManagementService.getInstance().restoreBackup).toHaveBeenCalled();
    });
  });
});
