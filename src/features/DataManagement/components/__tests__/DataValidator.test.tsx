/**
 * Tests for DataValidator Component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ThemeProvider } from '../../../../context/ThemeContext';
import DataValidator from '../DataValidator';
import DataManagementService from '../../../../services/DataManagementService';

// Mock the ThemeContext
jest.mock('../../../../context/ThemeContext', () => ({
  useTheme: () => ({
    theme: {
      colors: {
        background: {
          paper: '#ffffff',
          default: '#f5f5f5',
          hover: '#f0f0f0',
        },
        primary: {
          main: '#1976d2',
          dark: '#115293',
          contrastText: '#ffffff',
        },
        error: {
          main: '#d32f2f',
          dark: '#c62828',
          light: '#ffebee',
          contrastText: '#ffffff',
        },
        success: {
          light: '#e8f5e9',
          dark: '#2e7d32',
        },
        warning: {
          light: '#fff8e1',
          dark: '#f57c00',
        },
        text: {
          secondary: '#757575',
        },
        border: '#e0e0e0',
      },
    },
  }),
}));

// Mock the DataManagementService
jest.mock('../../../../services/DataManagementService', () => {
  return {
    getInstance: jest.fn(() => ({
      // No methods needed for this test
    })),
  };
});

describe('DataValidator Component', () => {
  const mockOnValidationComplete = jest.fn();

  const renderComponent = () => {
    return render(
      <ThemeProvider>
        <DataValidator onValidationComplete={mockOnValidationComplete} />
      </ThemeProvider>
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the component with title and options', () => {
    renderComponent();

    expect(screen.getByText('Data Validator')).toBeInTheDocument();
    expect(screen.getByText('Select Data Types to Validate')).toBeInTheDocument();
    expect(screen.getByText('Personal Data')).toBeInTheDocument();
    expect(screen.getByText('Financial Data')).toBeInTheDocument();
    expect(screen.getByText('Connection Data')).toBeInTheDocument();
    expect(screen.getByText('Validate Data')).toBeInTheDocument();
  });

  it('enables validate button when at least one data type is selected', () => {
    renderComponent();

    // All data types are selected by default
    expect(screen.getByText('Validate Data')).not.toBeDisabled();

    // Uncheck all data types
    fireEvent.click(screen.getByLabelText('Personal Data'));
    fireEvent.click(screen.getByLabelText('Financial Data'));
    fireEvent.click(screen.getByLabelText('Connection Data'));

    // Button should be disabled
    expect(screen.getByText('Validate Data')).toBeDisabled();

    // Check one data type
    fireEvent.click(screen.getByLabelText('Personal Data'));

    // Button should be enabled
    expect(screen.getByText('Validate Data')).not.toBeDisabled();
  });

  it('shows validation results when validate button is clicked', async () => {
    renderComponent();

    // Click validate button
    fireEvent.click(screen.getByText('Validate Data'));

    // Wait for validation results
    await waitFor(() => {
      expect(screen.getByText('Validation Result: Invalid')).toBeInTheDocument();
    });

    // Check that errors and warnings are displayed
    expect(screen.getByText('Errors (2)')).toBeInTheDocument();
    expect(screen.getByText('Income amount cannot be negative')).toBeInTheDocument();
    expect(screen.getByText('Invalid expense category')).toBeInTheDocument();

    expect(screen.getByText('Warnings (2)')).toBeInTheDocument();
    expect(screen.getByText('Email address may be invalid')).toBeInTheDocument();
    expect(screen.getByText('Connection has not been synced in over 30 days')).toBeInTheDocument();
  });

  it('calls onValidationComplete with validation results', async () => {
    renderComponent();

    // Click validate button
    fireEvent.click(screen.getByText('Validate Data'));

    // Wait for validation to complete
    await waitFor(() => {
      expect(mockOnValidationComplete).toHaveBeenCalled();
    });

    // Check that onValidationComplete was called with the correct results
    expect(mockOnValidationComplete).toHaveBeenCalledWith(
      expect.objectContaining({
        isValid: false,
        errors: expect.arrayContaining([
          expect.objectContaining({
            message: 'Income amount cannot be negative',
          }),
          expect.objectContaining({
            message: 'Invalid expense category',
          }),
        ]),
        warnings: expect.arrayContaining([
          expect.objectContaining({
            message: 'Email address may be invalid',
          }),
          expect.objectContaining({
            message: 'Connection has not been synced in over 30 days',
          }),
        ]),
      })
    );
  });

  it('fixes errors when fix buttons are clicked', async () => {
    renderComponent();

    // Click validate button
    fireEvent.click(screen.getByText('Validate Data'));

    // Wait for validation results
    await waitFor(() => {
      expect(screen.getByText('Validation Result: Invalid')).toBeInTheDocument();
    });

    // Click fix button for first error
    const fixButtons = screen.getAllByText('Fix');
    fireEvent.click(fixButtons[0]);

    // Check that the error was removed
    await waitFor(() => {
      expect(screen.getByText('Errors (1)')).toBeInTheDocument();
    });

    // Click fix button for second error
    fireEvent.click(screen.getByText('Fix'));

    // Check that all errors are fixed
    await waitFor(() => {
      expect(screen.getByText('Validation Result: Valid')).toBeInTheDocument();
    });

    // Warnings should still be there
    expect(screen.getByText('Warnings (2)')).toBeInTheDocument();
  });

  it('fixes all errors when fix all button is clicked', async () => {
    renderComponent();

    // Click validate button
    fireEvent.click(screen.getByText('Validate Data'));

    // Wait for validation results
    await waitFor(() => {
      expect(screen.getByText('Validation Result: Invalid')).toBeInTheDocument();
    });

    // Click fix all button
    fireEvent.click(screen.getByText('Fix All Errors'));

    // Check that all errors are fixed
    await waitFor(() => {
      expect(screen.getByText('Validation Result: Valid')).toBeInTheDocument();
    });

    // Warnings should still be there
    expect(screen.getByText('Warnings (2)')).toBeInTheDocument();
  });

  it('ignores warnings when ignore buttons are clicked', async () => {
    renderComponent();

    // Click validate button
    fireEvent.click(screen.getByText('Validate Data'));

    // Wait for validation results
    await waitFor(() => {
      expect(screen.getByText('Validation Result: Invalid')).toBeInTheDocument();
    });

    // Fix all errors
    fireEvent.click(screen.getByText('Fix All Errors'));

    // Click ignore button for first warning
    const ignoreButtons = screen.getAllByText('Ignore');
    fireEvent.click(ignoreButtons[0]);

    // Check that the warning was removed
    await waitFor(() => {
      expect(screen.getByText('Warnings (1)')).toBeInTheDocument();
    });

    // Click ignore button for second warning
    fireEvent.click(screen.getByText('Ignore'));

    // Check that all warnings are ignored
    await waitFor(() => {
      expect(screen.getByText('All data is valid! No issues found.')).toBeInTheDocument();
    });
  });
});
