/**
 * Profile Context
 *
 * This context provides state and functions for user profile management.
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import profileService, { UserProfile } from '../services/profileService';

/**
 * Profile status type
 */
export type ProfileStatus = 'idle' | 'loading' | 'saving' | 'deleting' | 'error';

/**
 * Profile context type
 */
interface ProfileContextType {
  status: ProfileStatus;
  error: string | null;
  profiles: UserProfile[];
  currentProfile: UserProfile | null;
  loadProfiles: () => Promise<void>;
  createProfile: (profileData: Partial<UserProfile>) => Promise<UserProfile>;
  updateProfile: (profileId: string, profileData: Partial<UserProfile>) => Promise<UserProfile>;
  deleteProfile: (profileId: string) => Promise<void>;
  setCurrentProfile: (profileId: string) => Promise<void>;
  getCurrentProfile: () => Promise<UserProfile | null>;
}

/**
 * Profile context
 */
const ProfileContext = createContext<ProfileContextType | undefined>(undefined);

/**
 * Profile provider props
 */
interface ProfileProviderProps {
  children: ReactNode;
}

/**
 * Profile provider
 */
export const ProfileProvider: React.FC<ProfileProviderProps> = ({ children }) => {
  // State
  const [status, setStatus] = useState<ProfileStatus>('idle');
  const [error, setError] = useState<string | null>(null);
  const [profiles, setProfiles] = useState<UserProfile[]>([]);
  const [currentProfile, setCurrentProfileState] = useState<UserProfile | null>(null);

  // Load profiles and current profile on mount
  useEffect(() => {
    const initializeProfiles = async () => {
      await loadProfiles();
      await getCurrentProfile();
    };

    initializeProfiles();
  }, []);

  /**
   * Load all profiles
   */
  const loadProfiles = async (): Promise<void> => {
    try {
      setStatus('loading');
      setError(null);

      const profilesData = await profileService.getAllProfiles();
      setProfiles(profilesData);

      setStatus('idle');
    } catch (error) {
      console.error('Error loading profiles:', error);
      setStatus('error');
      setError(error instanceof Error ? error.message : 'Unknown error');
    }
  };

  /**
   * Create a new profile
   */
  const createProfile = async (profileData: Partial<UserProfile>): Promise<UserProfile> => {
    try {
      setStatus('saving');
      setError(null);

      const newProfile = await profileService.createProfile(profileData);

      // Refresh profiles list
      await loadProfiles();

      setStatus('idle');
      return newProfile;
    } catch (error) {
      console.error('Error creating profile:', error);
      setStatus('error');
      setError(error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  };

  /**
   * Update a profile
   */
  const updateProfile = async (
    profileId: string,
    profileData: Partial<UserProfile>
  ): Promise<UserProfile> => {
    try {
      setStatus('saving');
      setError(null);

      const updatedProfile = await profileService.updateProfile(profileId, profileData);

      // Refresh profiles list
      await loadProfiles();

      // Update current profile if it was updated
      if (currentProfile?.id === profileId) {
        setCurrentProfileState(updatedProfile);
      }

      setStatus('idle');
      return updatedProfile;
    } catch (error) {
      console.error('Error updating profile:', error);
      setStatus('error');
      setError(error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  };

  /**
   * Delete a profile
   */
  const deleteProfile = async (profileId: string): Promise<void> => {
    try {
      setStatus('deleting');
      setError(null);

      await profileService.deleteProfile(profileId);

      // Refresh profiles list
      await loadProfiles();

      // Clear current profile if it was deleted
      if (currentProfile?.id === profileId) {
        setCurrentProfileState(null);
      }

      setStatus('idle');
    } catch (error) {
      console.error('Error deleting profile:', error);
      setStatus('error');
      setError(error instanceof Error ? error.message : 'Unknown error');
    }
  };

  /**
   * Set the current profile
   */
  const setCurrentProfile = async (profileId: string): Promise<void> => {
    try {
      setStatus('loading');
      setError(null);

      const profile = await profileService.setCurrentProfile(profileId);
      setCurrentProfileState(profile);

      setStatus('idle');
    } catch (error) {
      console.error('Error setting current profile:', error);
      setStatus('error');
      setError(error instanceof Error ? error.message : 'Unknown error');
    }
  };

  /**
   * Get the current profile
   */
  const getCurrentProfile = async (): Promise<UserProfile | null> => {
    try {
      setStatus('loading');
      setError(null);

      const profile = await profileService.getCurrentProfile();
      setCurrentProfileState(profile);

      setStatus('idle');
      return profile;
    } catch (error) {
      console.error('Error getting current profile:', error);
      setStatus('error');
      setError(error instanceof Error ? error.message : 'Unknown error');
      return null;
    }
  };

  // Context value
  const value: ProfileContextType = {
    status,
    error,
    profiles,
    currentProfile,
    loadProfiles,
    createProfile,
    updateProfile,
    deleteProfile,
    setCurrentProfile,
    getCurrentProfile,
  };

  return <ProfileContext.Provider value={value}>{children}</ProfileContext.Provider>;
};

/**
 * Hook to use the profile context
 */
export const useProfile = (): ProfileContextType => {
  const context = useContext(ProfileContext);

  if (context === undefined) {
    throw new Error('useProfile must be used within a ProfileProvider');
  }

  return context;
};
