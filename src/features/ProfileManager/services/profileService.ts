/**
 * Profile Service
 *
 * This service handles user profile management.
 */

/**
 * User profile information
 */
export interface UserProfile {
  id: string;
  name: string;
  email: string; // required, unique
  passwordHash: string; // required, never store plaintext
  role: 'user' | 'admin'; // default 'user'
  createdAt: string;
  updatedAt: string;
  personalInfo?: {
    firstName?: string;
    lastName?: string;
    birthDate?: string;
    maritalStatus?: 'single' | 'married' | 'divorced' | 'widowed' | 'other';
  };
  financialInfo?: Record<string, any>;
  seasonsOfSelfInfo?: Record<string, any>;
}

/**
 * Storage key for user profiles
 */
const PROFILES_STORAGE_KEY = 'lifecompass_profiles';

/**
 * Storage key for current profile ID
 */
const CURRENT_PROFILE_ID_KEY = 'lifecompass_current_profile_id';

/**
 * Get all user profiles
 *
 * @returns A Promise that resolves with the list of user profiles
 */
export const getAllProfiles = async (): Promise<UserProfile[]> => {
  try {
    const profilesJson = localStorage.getItem(PROFILES_STORAGE_KEY);
    return profilesJson ? JSON.parse(profilesJson) : [];
  } catch (error) {
    console.error('Error getting profiles:', error);
    throw new Error('Failed to get profiles');
  }
};

/**
 * Get a user profile by ID
 *
 * @param profileId - The ID of the profile to get
 * @returns A Promise that resolves with the user profile or null if not found
 */
export const getProfileById = async (profileId: string): Promise<UserProfile | null> => {
  try {
    const profiles = await getAllProfiles();
    return profiles.find((profile) => profile.id === profileId) || null;
  } catch (error) {
    console.error('Error getting profile:', error);
    throw new Error('Failed to get profile');
  }
};

/**
 * Create a new user profile
 *
 * @param profileData - The profile data to create
 * @returns A Promise that resolves with the created user profile
 */
export const createProfile = async (profileData: Partial<UserProfile>): Promise<UserProfile> => {
  try {
    const profiles = await getAllProfiles();
    // Require email and passwordHash
    if (!profileData.email || !profileData.passwordHash) {
      throw new Error('Email and password are required');
    }
    // Check for unique email
    if (profiles.some((p) => p.email === profileData.email)) {
      throw new Error('A user with this email already exists');
    }
    // Create a new profile with required fields
    const newProfile: UserProfile = {
      id: `profile_${Date.now()}`,
      name: profileData.name || 'New Profile',
      email: profileData.email,
      passwordHash: profileData.passwordHash,
      role: profileData.role || 'user',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...profileData,
    };
    // Add the new profile to the list
    profiles.push(newProfile);
    // Save the updated list
    localStorage.setItem(PROFILES_STORAGE_KEY, JSON.stringify(profiles));
    return newProfile;
  } catch (error) {
    console.error('Error creating profile:', error);
    throw new Error('Failed to create profile');
  }
};

/**
 * Update a user profile
 *
 * @param profileId - The ID of the profile to update
 * @param profileData - The profile data to update
 * @returns A Promise that resolves with the updated user profile
 */
export const updateProfile = async (
  profileId: string,
  profileData: Partial<UserProfile>
): Promise<UserProfile> => {
  try {
    const profiles = await getAllProfiles();
    const profileIndex = profiles.findIndex((profile) => profile.id === profileId);
    if (profileIndex === -1) {
      throw new Error(`Profile with ID ${profileId} not found`);
    }
    // Prevent email duplication
    if (
      profileData.email &&
      profiles.some((p, i) => i !== profileIndex && p.email === profileData.email)
    ) {
      throw new Error('A user with this email already exists');
    }
    // Update the profile
    const updatedProfile: UserProfile = {
      ...profiles[profileIndex],
      ...profileData,
      updatedAt: new Date().toISOString(),
    };
    // Replace the old profile with the updated one
    profiles[profileIndex] = updatedProfile;
    // Save the updated list
    localStorage.setItem(PROFILES_STORAGE_KEY, JSON.stringify(profiles));
    return updatedProfile;
  } catch (error) {
    console.error('Error updating profile:', error);
    throw new Error('Failed to update profile');
  }
};

/**
 * Delete a user profile
 *
 * @param profileId - The ID of the profile to delete
 * @returns A Promise that resolves when the profile is deleted
 */
export const deleteProfile = async (profileId: string): Promise<void> => {
  try {
    const profiles = await getAllProfiles();
    const filteredProfiles = profiles.filter((profile) => profile.id !== profileId);

    // Save the updated list
    localStorage.setItem(PROFILES_STORAGE_KEY, JSON.stringify(filteredProfiles));

    // If the deleted profile was the current profile, clear the current profile
    const currentProfileId = localStorage.getItem(CURRENT_PROFILE_ID_KEY);
    if (currentProfileId === profileId) {
      localStorage.removeItem(CURRENT_PROFILE_ID_KEY);
    }
  } catch (error) {
    console.error('Error deleting profile:', error);
    throw new Error('Failed to delete profile');
  }
};

/**
 * Get the current active profile
 *
 * @returns A Promise that resolves with the current profile or null if none is set
 */
export const getCurrentProfile = async (): Promise<UserProfile | null> => {
  try {
    const currentProfileId = localStorage.getItem(CURRENT_PROFILE_ID_KEY);

    if (!currentProfileId) {
      return null;
    }

    return getProfileById(currentProfileId);
  } catch (error) {
    console.error('Error getting current profile:', error);
    throw new Error('Failed to get current profile');
  }
};

/**
 * Set the current active profile
 *
 * @param profileId - The ID of the profile to set as current
 * @returns A Promise that resolves with the current profile
 */
export const setCurrentProfile = async (profileId: string): Promise<UserProfile> => {
  try {
    const profile = await getProfileById(profileId);

    if (!profile) {
      throw new Error(`Profile with ID ${profileId} not found`);
    }

    // Set the current profile ID
    localStorage.setItem(CURRENT_PROFILE_ID_KEY, profileId);

    return profile;
  } catch (error) {
    console.error('Error setting current profile:', error);
    throw new Error('Failed to set current profile');
  }
};

/**
 * Profile Service
 */
const profileService = {
  getAllProfiles,
  getProfileById,
  createProfile,
  updateProfile,
  deleteProfile,
  getCurrentProfile,
  setCurrentProfile,
};

export default profileService;
