import React from 'react';
import { HashRouter as Router, Routes, Route, Link } from 'react-router-dom';
import { SimpleThemeProviderMinimal, useThemeMinimal } from './theme/SimpleThemeProviderMinimal';

// Simple Home component
const Home = () => {
  const { theme } = useThemeMinimal();

  return (
    <div style={{ padding: '20px', color: theme.colors.text }}>
      <h1>Home Page</h1>
      <p>Welcome to the LifeCompass app!</p>
      <p>Current theme: {theme.mode}</p>
    </div>
  );
};

// Simple About component
const About = () => {
  const { theme } = useThemeMinimal();

  return (
    <div style={{ padding: '20px', color: theme.colors.text }}>
      <h1>About Page</h1>
      <p>This is a simplified version of the LifeCompass app for debugging.</p>
      <p>Current theme: {theme.mode}</p>
    </div>
  );
};

// Simple Navigation component
const Navigation = () => {
  const { theme, toggleTheme } = useThemeMinimal();

  return (
    <nav
      style={{
        backgroundColor: theme.mode === 'light' ? '#333' : '#111',
        padding: '10px',
        display: 'flex',
        justifyContent: 'space-between',
      }}
    >
      <div>
        <Link to="/" style={{ color: 'white', marginRight: '15px', textDecoration: 'none' }}>
          Home
        </Link>
        <Link to="/about" style={{ color: 'white', textDecoration: 'none' }}>
          About
        </Link>
      </div>
      <div>
        <button
          onClick={toggleTheme}
          style={{
            backgroundColor: theme.colors.primary,
            color: 'white',
            border: 'none',
            padding: '5px 10px',
            borderRadius: '4px',
            cursor: 'pointer',
            marginRight: '10px',
          }}
        >
          Toggle Theme
        </button>
        <span style={{ color: 'white' }}>LifeCompass</span>
      </div>
    </nav>
  );
};

// App Content component
const AppContent = () => {
  const { theme } = useThemeMinimal();

  return (
    <div
      style={{
        fontFamily: 'Arial, sans-serif',
        minHeight: '100vh',
        backgroundColor: theme.colors.background,
        transition: 'background-color 0.3s ease, color 0.3s ease',
      }}
    >
      <Navigation />
      <div style={{ padding: '20px' }}>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/about" element={<About />} />
        </Routes>
      </div>
    </div>
  );
};

// Main App component with router and theme provider
const App: React.FC = () => {
  console.log('App.themed component rendering');

  return (
    <Router>
      <SimpleThemeProviderMinimal>
        <AppContent />
      </SimpleThemeProviderMinimal>
    </Router>
  );
};

export default App;
