{"metadata": {"exportDate": "2023-10-15T12:00:00.000Z", "appVersion": "1.0.0", "exportType": "all"}, "financialCompass": {"north": {"personalInformation": {"firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>-<PERSON>", "dateOfBirth": "1980-01-15", "email": "<EMAIL>", "phone": "+****************", "gender": "non-binary", "maritalStatus": "domestic_partnership", "annualIncome": "85000.50", "age": "43", "address": {"street": "123 Main St, Apt #42", "city": "San José", "state": "CA", "zipCode": "90210-1234", "country": "United States"}, "occupation": "Software Engineer & Consultant", "employmentStatus": "self-employed"}, "familyInformation": {"maritalStatus": "domestic_partnership", "familyMembers": [{"id": "fm-001", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>-<PERSON>", "relationship": "partner", "dateOfBirth": "1982-05-20", "isDependent": false, "occupation": "Graphic Designer", "notes": "Works remotely for international clients"}, {"id": "fm-002", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>-<PERSON>", "relationship": "child (adopted)", "dateOfBirth": "2010-08-12", "isDependent": true, "notes": "Special needs education"}]}, "incomeDetails": {"primaryIncome": "85000.50", "primaryIncomeType": "self_employment", "primaryIncomeFrequency": "variable", "taxRate": "22.5", "totalMonthlyIncome": "", "totalAnnualIncome": "115000.75", "incomeSources": [{"id": "is-001", "type": "self_employment", "amount": "85000.50", "frequency": "variable", "description": "Consulting work - varies by project", "name": "Software Consulting", "isPassive": false}, {"id": "is-002", "type": "rental_income", "amount": "24000", "frequency": "monthly", "description": "Rental property income", "name": "Rental Property", "isPassive": true}, {"id": "is-003", "type": "dividends", "amount": "6000.25", "frequency": "quarterly", "description": "Stock dividends from tech investments", "name": "Investment Income", "isPassive": true}]}, "expenseDetails": {"totalMonthlyExpenses": "7500.75", "totalAnnualExpenses": "90009", "expenseCategories": {"housing": {"mortgage": 2500.5, "utilities": 450.25, "maintenance": 300}, "transportation": {"carPayment": 0, "fuel": 250.75, "insurance": 175.5, "maintenance": 125.25, "publicTransport": 100}, "food": {"groceries": 950.25, "diningOut": 550.5}, "healthcare": {"insurance": 750.25, "outOfPocket": 200.5}, "personal": {"entertainment": 400.25, "clothing": 250.5, "education": 400}, "debt": {"creditCards": 0, "studentLoans": 0}}}, "assets": {"cashAccounts": {"checking": 25000.5, "savings": 50000.75, "moneyMarket": 35000.25}, "investments": {"stocks": 150000.5, "bonds": 75000.25, "mutualFunds": 100000.75, "cryptocurrency": 25000.5}, "retirement": {"401k": 0, "sepIra": 300000.5, "rothIra": 85000.25}, "realEstate": {"primaryResidence": 650000.75, "rentalProperty": 450000.5}, "other": {"vehicles": 45000.25, "personalProperty": 75000.5, "businessEquity": 200000}}, "liabilities": {"mortgage": {"primaryResidence": 450000.5, "rentalProperty": 350000.25}, "loans": {"businessLoan": 100000.5}, "creditCards": {}}, "netWorthDetails": {"totalAssets": "2266000.75", "totalLiabilities": "900001.25"}, "riskAssessment": {"answers": {"timeHorizon": "15+ years", "riskTolerance": "aggressive", "investmentExperience": "very_experienced", "incomeStability": "somewhat_variable", "emergencyFund": "12+ months"}, "riskScore": 85, "riskProfile": "aggressive"}, "cashFlowAnalysis": {"monthlyCashFlow": 2083.33, "annualCashFlow": 24999.96, "savingsRate": 21.74}}}, "seasonsOfSelf": {"activeStage": "pivotStage", "stages": {"springStage": {"completed": true, "startDate": "1998-06-15", "endDate": "2008-08-20", "keyEvents": [{"id": "spring-event-1", "date": "2001-05-10", "title": "Graduated with Honors", "description": "Summa Cum Laude in Computer Science & Philosophy"}, {"id": "spring-event-2", "date": "2003-03-15", "title": "First Startup", "description": "Co-founded tech startup (failed after 2 years)"}, {"id": "spring-event-3", "date": "2006-09-22", "title": "Met Partner", "description": "Met Alex at industry conference"}], "reflections": "A period of ambitious experimentation, some failures, and finding my authentic path.", "lessons": "Failure is part of growth. Technical skills alone aren't enough for success.", "financialMilestones": ["Paid off student loans early", "First major investment loss", "Started retirement savings late"]}, "pivotStage": {"completed": false, "startDate": "2020-07-01", "endDate": null, "keyEvents": [{"id": "pivot-event-1", "date": "2020-02-15", "title": "Career Transition", "description": "Left corporate job to start consulting business"}, {"id": "pivot-event-2", "date": "2021-06-10", "title": "Adopted Child", "description": "Completed adoption process for <PERSON>"}, {"id": "pivot-event-3", "date": "2022-11-05", "title": "Purchased Investment Property", "description": "Bought first rental property"}], "reflections": "Redefining success on my own terms and creating more life flexibility.", "lessons": "Taking calculated risks can lead to both financial and personal freedom.", "financialMilestones": ["Established emergency fund covering 12 months", "Diversified investments across multiple asset classes", "Created multiple income streams"]}}}, "userProfiles": {"currentProfile": "profile-001", "profiles": [{"id": "profile-001", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "isDefault": true, "createdAt": "2023-01-15T10:30:00.000Z", "lastUpdated": "2023-10-10T14:45:00.000Z"}]}}