{"metadata": {"exportDate": "2023-10-15T12:00:00.000Z", "appVersion": "1.0.0", "exportType": "all"}, "financialCompass": {"north": {"personalInformation": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "dateOfBirth": "1980-01-15", "email": "<EMAIL>", "phone": "************", "gender": "male", "maritalStatus": "married", "annualIncome": 85000, "age": 43, "address": {"street": "123 Main St", "city": "Anytown", "state": "CA", "zipCode": "90210", "country": "United States"}, "occupation": "Software Engineer", "employmentStatus": "employed"}, "familyInformation": {"maritalStatus": "married", "familyMembers": [{"id": "fm-001", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "relationship": "spouse", "dateOfBirth": "1982-05-20", "isDependent": false, "occupation": "Teacher", "notes": "High school math teacher"}, {"id": "fm-002", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "relationship": "child", "dateOfBirth": "2010-08-12", "isDependent": true, "notes": "Middle school student"}], "spouseFirstName": "<PERSON>", "spouseLastName": "<PERSON><PERSON>", "spouseDateOfBirth": "1982-05-20", "spouseOccupation": "Teacher"}, "incomeDetails": {"primaryIncome": 85000, "primaryIncomeType": "salary", "primaryIncomeFrequency": "annual", "taxRate": 22, "totalMonthlyIncome": 9583.33, "totalAnnualIncome": 115000, "incomeSources": [{"id": "is-001", "type": "salary", "amount": 85000, "frequency": "annual", "description": "Primary job", "name": "Software Engineer <PERSON><PERSON>", "isPassive": false}, {"id": "is-002", "type": "salary", "amount": 65000, "frequency": "annual", "description": "Spouse income", "name": "Teaching Salary", "isPassive": false}, {"id": "is-003", "type": "dividends", "amount": 5000, "frequency": "annual", "description": "Stock dividends", "name": "Investment Income", "isPassive": true}]}, "expenseDetails": {"totalMonthlyExpenses": 6500, "totalAnnualExpenses": 78000, "expenseCategories": {"housing": {"mortgage": 2000, "utilities": 350, "maintenance": 200}, "transportation": {"carPayment": 400, "fuel": 200, "insurance": 150, "maintenance": 100}, "food": {"groceries": 800, "diningOut": 400}, "healthcare": {"insurance": 500, "outOfPocket": 100}, "personal": {"entertainment": 300, "clothing": 200, "education": 300}, "debt": {"creditCards": 200, "studentLoans": 300}}}, "assets": {"cashAccounts": {"checking": 15000, "savings": 35000, "moneyMarket": 25000}, "investments": {"stocks": 120000, "bonds": 50000, "mutualFunds": 80000}, "retirement": {"401k": 250000, "ira": 85000, "rothIra": 65000}, "realEstate": {"primaryResidence": 450000}, "other": {"vehicles": 35000, "personalProperty": 50000}}, "liabilities": {"mortgage": {"primaryResidence": 320000}, "loans": {"autoLoan": 25000, "studentLoan": 40000}, "creditCards": {"visa": 5000, "mastercard": 3000}}, "netWorthDetails": {"totalAssets": 1260000, "totalLiabilities": 393000}, "riskAssessment": {"answers": {"timeHorizon": "10-15 years", "riskTolerance": "moderate", "investmentExperience": "experienced", "incomeStability": "stable", "emergencyFund": "6+ months"}, "riskScore": 65, "riskProfile": "moderate-aggressive"}, "cashFlowAnalysis": {"monthlyCashFlow": 3083.33, "annualCashFlow": 37000, "savingsRate": 32.17}}, "east": {"retirementGoals": {"targetRetirementAge": 65, "currentAge": 43, "lifeExpectancy": 90, "targetMonthlyIncome": 8000, "currentRetirementSavings": 400000, "annualContribution": 25000, "expectedReturnRate": 7, "savingsGoal": 2000000, "desiredAnnualIncome": 96000, "retirementLifestyle": "active", "retirementLocation": "coastal", "retirementActivities": ["travel", "hobbies", "family time"], "priorityGoals": ["health", "travel", "family support"]}, "retirementIncome": {"socialSecurity": 3000, "pension": 1500, "annuities": 0, "investments": 4000, "otherIncome": 500, "detailedSocialSecurity": {"estimatedMonthlyBenefit": 3000, "startAge": 67}, "detailedPension": {"hasEmployerPension": true, "estimatedMonthlyBenefit": 1500, "startAge": 65}, "incomeSources": [{"id": "ri-001", "type": "socialSecurity", "amount": 3000, "frequency": "monthly", "startAge": 67, "endAge": 90, "notes": "Based on current earnings record"}, {"id": "ri-002", "type": "pension", "amount": 1500, "frequency": "monthly", "startAge": 65, "endAge": 90, "notes": "Company pension plan"}, {"id": "ri-003", "type": "investments", "amount": 4000, "frequency": "monthly", "startAge": 65, "endAge": 90, "notes": "4% withdrawal rate from retirement accounts"}]}, "retirementExpenses": {"housingExpense": 2500, "healthcareExpense": 1200, "foodExpense": 1000, "transportationExpense": 600, "travelExpense": 1000, "entertainmentExpense": 800, "otherExpense": 900, "expenseCategories": [{"id": "re-001", "name": "Housing", "monthlyAmount": 2500, "isEssential": true, "notes": "Mortgage will be paid off by retirement"}, {"id": "re-002", "name": "Healthcare", "monthlyAmount": 1200, "isEssential": true, "notes": "Includes Medicare supplemental insurance"}], "housingPlan": "Stay in current home", "healthcareCosts": "Estimated including Medicare", "travelBudget": 12000, "hobbiesBudget": 5000, "inflationAssumption": 2.5}, "retirementTimeline": {"events": [{"id": "rt-001", "age": 59, "year": 2039, "event": "Access retirement accounts penalty-free", "description": "Begin strategic withdrawals from retirement accounts", "type": "financial"}, {"id": "rt-002", "age": 65, "year": 2045, "event": "Full retirement", "description": "Stop working full-time and begin retirement", "type": "lifestyle"}, {"id": "rt-003", "age": 67, "year": 2047, "event": "Begin Social Security", "description": "Start collecting Social Security benefits", "type": "financial"}]}, "socialSecurityPlanning": {"selectedClaimingAge": 67, "birthYear": 1980, "fullRetirementAge": 67, "earlyClaimingAge": 62, "delayedClaimingAge": 70, "estimatedMonthlyBenefitAtFRA": 3000, "earlyClaimingReduction": 30, "delayedClaimingIncrease": 24, "spouseBenefit": 1500, "hasWorkingHistory": true, "yearsOfCoveredEmployment": 25, "averageIndexedMonthlyEarnings": 7500, "primaryInsuranceAmount": 3000, "maximizationStrategy": "Claim at FRA"}}, "south": {"insuranceAnalysis": {"healthInsurance": {"hasInsurance": true, "type": "employer", "monthlyPremium": 500, "annualDeductible": 2000, "outOfPocketMax": 5000, "coverageDetails": "Family PPO plan with dental and vision", "adequacyAssessment": "adequate"}, "lifeInsurance": {"hasInsurance": true, "type": "term", "coverageAmount": 1000000, "monthlyPremium": 75, "beneficiaries": "Spouse and children", "termLength": 20, "adequacyAssessment": "adequate"}, "disabilityInsurance": {"hasInsurance": true, "type": "long-term", "coverageAmount": 0.6, "monthlyPremium": 50, "eliminationPeriod": 90, "benefitPeriod": 65, "adequacyAssessment": "adequate"}, "propertyInsurance": {"hasHomeInsurance": true, "homeMonthlyPremium": 150, "homeCoverageAmount": 450000, "hasAutoInsurance": true, "autoMonthlyPremium": 120, "autoCoverageAmount": 100000, "adequacyAssessment": "adequate"}, "umbrellaCoverage": {"hasUmbrella": true, "coverageAmount": 1000000, "monthlyPremium": 30, "adequacyAssessment": "adequate"}}, "emergencyFund": {"monthlyExpenses": 6500, "currentEmergencyFund": 50000, "targetMonths": 6, "targetAmount": 39000, "fundingStatus": "fully_funded", "fundingPlan": "Maintain current level", "fundLocation": "High-yield savings account"}, "riskManagement": {"personalRisks": [{"id": "pr-001", "riskType": "job_loss", "likelihood": "low", "impact": "high", "mitigationStrategy": "Emergency fund and updated resume", "notes": "Industry is stable but keeping skills current"}, {"id": "pr-002", "riskType": "health_crisis", "likelihood": "medium", "impact": "high", "mitigationStrategy": "Comprehensive health insurance and emergency fund", "notes": "Family history of heart disease"}], "financialRisks": [{"id": "fr-001", "riskType": "market_downturn", "likelihood": "medium", "impact": "medium", "mitigationStrategy": "Diversified portfolio and long time horizon", "notes": "Asset allocation aligned with risk tolerance"}, {"id": "fr-002", "riskType": "inflation", "likelihood": "high", "impact": "medium", "mitigationStrategy": "Investments in inflation-protected securities and real assets", "notes": "Monitoring inflation trends"}], "overallRiskAssessment": "well_managed"}, "debtManagement": {"totalDebt": 393000, "highInterestDebt": 8000, "lowInterestDebt": 385000, "debtToIncomeRatio": 3.42, "monthlyDebtPayments": 3200, "debtFreeDate": "2045-01-15", "debtReductionStrategy": "Paying off high-interest debt first", "debtItems": [{"id": "debt-001", "type": "mortgage", "balance": 320000, "interestRate": 3.5, "minimumPayment": 2000, "actualPayment": 2000, "payoffDate": "2045-01-15"}, {"id": "debt-002", "type": "auto_loan", "balance": 25000, "interestRate": 4.2, "minimumPayment": 500, "actualPayment": 500, "payoffDate": "2028-06-20"}, {"id": "debt-003", "type": "student_loan", "balance": 40000, "interestRate": 5.0, "minimumPayment": 400, "actualPayment": 400, "payoffDate": "2033-08-10"}, {"id": "debt-004", "type": "credit_card", "balance": 8000, "interestRate": 18.99, "minimumPayment": 300, "actualPayment": 800, "payoffDate": "2024-05-15"}]}, "creditProfile": {"creditScore": 780, "creditScoreRange": "excellent", "creditUtilization": 15, "totalCreditLimit": 50000, "totalCreditUsed": 8000, "numberOfAccounts": 8, "numberOfInquiries": 1, "oldestAccountAge": 15, "improvementPlan": "Maintain low utilization and pay off credit card debt"}}, "west": {"estatePlanning": {"hasWill": true, "hasTrust": false, "hasPowerOfAttorney": true, "hasHealthcareDirective": true, "lastUpdated": "2022-05-10", "executor": "<PERSON>", "guardianForMinors": "<PERSON>", "estateValue": 1260000, "inheritancePlan": "Equal distribution to children with specific bequests", "documentsLocation": "Safe deposit box and with attorney"}, "estateDocuments": {"will": {"exists": true, "lastUpdated": "2022-05-10", "location": "Safe deposit box and with attorney", "executor": "<PERSON>", "notes": "Includes specific bequests to family members"}, "trust": {"exists": false, "type": "", "beneficiaries": "", "trustee": "", "notes": "Considering establishing a revocable living trust"}, "powerOfAttorney": {"exists": true, "type": "Durable", "agent": "<PERSON>", "alternateAgent": "<PERSON>", "lastUpdated": "2022-05-10", "notes": "Covers financial and legal matters"}, "healthcareDirective": {"exists": true, "agent": "<PERSON>", "alternateAgent": "<PERSON>", "lastUpdated": "2022-05-10", "preferences": "No extraordinary measures if terminal condition", "notes": "Includes specific medical preferences"}}, "charitableGiving": {"annualGivingTarget": 5000, "currentAnnualGiving": 3500, "favoriteOrganizations": [{"id": "org-001", "name": "Local Food Bank", "category": "Hunger Relief", "annualDonation": 1200, "notes": "Monthly recurring donation"}, {"id": "org-002", "name": "Environmental Defense Fund", "category": "Environment", "annualDonation": 1000, "notes": "Annual donation"}, {"id": "org-003", "name": "Education Scholarship Fund", "category": "Education", "annualDonation": 1300, "notes": "Quarterly donations"}], "givingStrategy": "Direct donations to established charities", "plannedGiving": "Considering a donor-advised fund", "legacyGiving": "10% of estate to charitable causes"}, "taxPlanning": {"filingStatus": "married", "income": 115000, "retirementContribution": 25000, "retirementBalance": 400000, "age": 43, "capitalGains": 5000, "estateValue": 1260000, "hasTaxStrategy": "yes", "usesTaxDeferredAccounts": "yes", "usesRothAccounts": "yes", "hasTaxLossHarvesting": "no"}, "taxResults": {"currentTax": 18500, "retirementSavings": 25000, "rmd": 0, "capitalGainsTax": 750, "estateTax": 0, "effectiveTaxRate": 16.1}, "valuesGoals": {"coreValues": [{"id": "cv-001", "value": "Family", "description": "Prioritizing time and resources for family well-being", "importance": 10}, {"id": "cv-002", "value": "Financial Security", "description": "Building stability and freedom through sound financial management", "importance": 9}, {"id": "cv-003", "value": "Education", "description": "Lifelong learning and educational opportunities", "importance": 8}, {"id": "cv-004", "value": "Giving Back", "description": "Contributing to community and causes", "importance": 7}], "lifeGoals": [{"id": "lg-001", "goal": "<PERSON><PERSON><PERSON> comfortably at 65", "category": "Financial", "targetDate": "2045-01-15", "status": "in_progress", "notes": "On track with current savings rate"}, {"id": "lg-002", "goal": "Fund children's college education", "category": "Family", "targetDate": "2028-08-01", "status": "in_progress", "notes": "529 plans established and growing"}, {"id": "lg-003", "goal": "Travel to 20 countries", "category": "Personal", "targetDate": "2040-12-31", "status": "in_progress", "notes": "8 countries visited so far"}], "personalMission": "To create financial security for my family while living a balanced life that allows for personal growth, meaningful experiences, and giving back to the community.", "valuesNotes": "These values guide financial and life decisions."}, "legacyPlanning": {"legacyGoals": [{"id": "legacy-001", "goal": "Provide financial security for spouse and children", "priority": "high", "timeframe": "long_term", "notes": "Life insurance and investments in place"}, {"id": "legacy-002", "goal": "Fund grandchildren's education", "priority": "medium", "timeframe": "long_term", "notes": "Planning to establish education trusts"}, {"id": "legacy-003", "goal": "Support charitable causes", "priority": "medium", "timeframe": "long_term", "notes": "Planned giving through estate"}], "legacyLetters": true, "ethicalWill": false, "digitalLegacy": {"hasPlan": true, "digitalAssetInventory": true, "passwordManager": true, "socialMediaInstructions": true, "notes": "Password manager information with executor"}, "legacyNotes": "Working with estate attorney to formalize legacy plans"}}}, "seasonsOfSelf": {"activeStage": "momentumStage", "stages": {"springStage": {"completed": true, "startDate": "2000-06-15", "endDate": "2010-08-20", "keyEvents": [], "reflections": "", "lessons": "", "financialMilestones": []}, "happinessStage": {"completed": true, "startDate": "2010-01-01", "endDate": "2010-08-20", "keyEvents": [], "reflections": "", "lessons": "", "financialMilestones": []}, "joyStage": {"completed": true, "startDate": "2010-08-21", "endDate": "2015-06-30", "keyEvents": [], "reflections": "", "lessons": "", "financialMilestones": []}, "momentumStage": {"completed": false, "startDate": "2015-07-01", "endDate": null, "keyEvents": [], "reflections": "", "lessons": "", "financialMilestones": []}, "pivotStage": {"completed": false, "startDate": null, "endDate": null, "keyEvents": [], "reflections": "", "lessons": "", "financialMilestones": []}, "goalSeekingStage": {"completed": false, "startDate": null, "endDate": null, "keyEvents": [], "reflections": "", "lessons": "", "financialMilestones": []}, "callingStage": {"completed": false, "startDate": null, "endDate": null, "keyEvents": [], "reflections": "", "lessons": "", "financialMilestones": []}, "purposeStage": {"completed": false, "startDate": null, "endDate": null, "keyEvents": [], "reflections": "", "lessons": "", "financialMilestones": []}, "fulfillmentStage": {"completed": false, "startDate": null, "endDate": null, "keyEvents": [], "reflections": "", "lessons": "", "financialMilestones": []}}, "lifeJourneyIntegration": {"financialAlignment": {"springStage": "Building foundation", "joyStage": "Family focus", "momentumStage": "Career growth and wealth building", "pivotStage": "Not yet reached", "goalSeekingStage": "Not yet reached", "callingStage": "Not yet reached", "purposeStage": "Not yet reached", "fulfillmentStage": "Not yet reached"}, "nextStagePreparation": {"nextStage": "pivotStage", "financialPreparation": "Increasing savings rate and diversifying investments", "personalPreparation": "Exploring new skills and interests", "timeframe": "3-5 years"}}}, "userProfiles": {"currentProfile": "profile-001", "profiles": [{"id": "profile-001", "name": "<PERSON>", "email": "<EMAIL>", "isDefault": true, "createdAt": "2023-01-15T10:30:00.000Z", "lastUpdated": "2023-10-10T14:45:00.000Z"}]}}