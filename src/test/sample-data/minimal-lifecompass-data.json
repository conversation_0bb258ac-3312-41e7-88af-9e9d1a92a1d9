{"metadata": {"exportDate": "2025-01-15T12:00:00.000Z", "appVersion": "1.2.0", "exportType": "minimal"}, "financialCompass": {"north": {"personalInformation": {"firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "1990-06-15", "email": "<EMAIL>", "phone": "************", "address": {"street": "789 Tech Drive", "city": "San Francisco", "state": "CA", "zipCode": "94105", "country": "United States"}, "occupation": "Software Developer", "employmentStatus": "employed"}, "familyInformation": {"maritalStatus": "single", "familyMembers": []}, "incomeDetails": {"primaryIncome": "120000", "primaryIncomeType": "salary", "primaryIncomeFrequency": "annual", "taxRate": "24", "incomeSources": [{"id": "is-001", "type": "salary", "amount": 120000, "frequency": "annual", "description": "Primary job", "name": "Software Developer Salary", "isPassive": false}]}, "expenseDetails": {"totalMonthlyExpenses": "6500", "expenseCategories": {"housing": {"rent": 3200}, "food": {"groceries": 600, "diningOut": 400}, "transportation": {"publicTransit": 150, "rideshare": 200}, "personal": {"entertainment": 300, "subscriptions": 150}}}, "assets": {"cash": {"checking": 8000, "savings": 25000}, "investments": {"stocks": 45000, "mutualFunds": 30000}, "retirement": {"401k": 85000}}, "liabilities": {"creditCards": {"visa": 2500}, "loans": {"studentLoan": 35000}}, "netWorthDetails": {"totalAssets": 193000, "totalLiabilities": 37500}, "riskAssessment": {"answers": {"timeHorizon": "20+ years", "riskTolerance": "moderate-aggressive", "investmentExperience": "intermediate"}, "riskScore": 70, "riskProfile": "moderate-aggressive"}, "cashFlowAnalysis": {"monthlyCashFlow": 3500, "annualCashFlow": 42000, "savingsRate": 35}}, "east": {"retirementGoals": {"targetRetirementAge": 60, "currentAge": 34, "targetMonthlyIncome": 8000, "currentRetirementSavings": 85000, "annualContribution": 20000}, "investmentPreferences": {"riskTolerance": "moderate", "investmentHorizon": "26", "preferredAssetAllocation": {"stocks": 70, "bonds": 20, "cash": 5, "alternatives": 5}, "investmentGoals": ["Capital Growth", "Retirement Income"], "monthlyInvestmentAmount": "1500", "preferredInvestmentTypes": ["Index Funds", "ETFs"], "esgPreferences": false, "rebalancingFrequency": "annually", "notes": "Focus on low-cost index funds for long-term growth."}}}, "seasonsOfSelf": {"activeStage": "springStage", "stages": {"springStage": {"completed": false, "startDate": "2020-01-01", "endDate": null, "keyEvents": [], "reflections": "", "lessons": "", "financialMilestones": []}}}, "userProfiles": {"currentProfile": "profile-001", "profiles": [{"id": "profile-001", "name": "<PERSON>", "email": "<EMAIL>", "isDefault": true, "createdAt": "2024-06-15T10:30:00.000Z", "lastUpdated": "2025-01-15T14:45:00.000Z"}]}}