/**
 * Notification Service
 *
 * This service provides functionality for managing and displaying notifications.
 * It integrates with the user preferences to respect notification settings.
 */

import { ToastType, ToastPosition } from '../components/ui/Toast';

// Types
export type NotificationType =
  | 'milestone'
  | 'update'
  | 'reminder'
  | 'system'
  | 'error'
  | 'success'
  | 'info'
  | 'warning';

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  timestamp: number;
  read: boolean;
  actionUrl?: string;
  actionLabel?: string;
  icon?: string;
  dismissible: boolean;
}

export interface NotificationOptions {
  title?: string;
  type?: NotificationType;
  actionUrl?: string;
  actionLabel?: string;
  icon?: string;
  dismissible?: boolean;
  toast?: boolean;
  toastOptions?: {
    type?: ToastType;
    duration?: number;
    position?: ToastPosition;
    showIcon?: boolean;
    showCloseButton?: boolean;
  };
}

// Storage key for notifications
const STORAGE_KEY = 'lifecompass_notifications';

/**
 * Notification Service Class
 */
class NotificationService {
  private static instance: NotificationService;
  private notifications: Notification[] = [];
  private showToastFn: ((message: string, options?: any) => string) | null = null;
  private isNotificationAllowedFn: ((type: 'milestone' | 'update' | 'reminder') => boolean) | null =
    null;

  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor() {
    this.loadNotifications();
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  /**
   * Set the toast function
   * @param fn The function to show a toast
   */
  public setToastFunction(fn: (message: string, options?: any) => string): void {
    this.showToastFn = fn;
  }

  /**
   * Set the notification permission function
   * @param fn The function to check if a notification is allowed
   */
  public setNotificationPermissionFunction(
    fn: (type: 'milestone' | 'update' | 'reminder') => boolean
  ): void {
    this.isNotificationAllowedFn = fn;
  }

  /**
   * Load notifications from storage
   */
  private loadNotifications(): void {
    try {
      const storedNotifications = localStorage.getItem(STORAGE_KEY);
      if (storedNotifications) {
        this.notifications = JSON.parse(storedNotifications);
      }
    } catch (error) {
      console.error('Error loading notifications from localStorage:', error);
    }
  }

  /**
   * Save notifications to storage
   */
  private saveNotifications(): void {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(this.notifications));
    } catch (error) {
      console.error('Error saving notifications to localStorage:', error);
    }
  }

  /**
   * Create a new notification
   * @param message The notification message
   * @param options The notification options
   * @returns The notification ID
   */
  public createNotification(message: string, options: NotificationOptions = {}): string | null {
    // Check if notification is allowed based on type and user preferences
    if (
      (options.type === 'milestone' || options.type === 'update' || options.type === 'reminder') &&
      this.isNotificationAllowedFn &&
      !this.isNotificationAllowedFn(options.type)
    ) {
      return null;
    }

    // Create notification
    const id = Math.random().toString(36).substring(2, 9);
    const notification: Notification = {
      id,
      title: options.title || '',
      message,
      type: options.type || 'info',
      timestamp: Date.now(),
      read: false,
      actionUrl: options.actionUrl,
      actionLabel: options.actionLabel,
      icon: options.icon,
      dismissible: options.dismissible !== undefined ? options.dismissible : true,
    };

    // Add to notifications list
    this.notifications.unshift(notification);

    // Limit to 100 notifications
    if (this.notifications.length > 100) {
      this.notifications = this.notifications.slice(0, 100);
    }

    // Save to storage
    this.saveNotifications();

    // Show toast if requested
    if (options.toast && this.showToastFn) {
      const toastType =
        options.toastOptions?.type || this.mapNotificationTypeToToastType(options.type || 'info');
      this.showToastFn(message, {
        type: toastType,
        duration: options.toastOptions?.duration || 5000,
        position: options.toastOptions?.position || 'top-right',
        showIcon:
          options.toastOptions?.showIcon !== undefined ? options.toastOptions.showIcon : true,
        showCloseButton:
          options.toastOptions?.showCloseButton !== undefined
            ? options.toastOptions.showCloseButton
            : true,
      });
    }

    return id;
  }

  /**
   * Map notification type to toast type
   * @param notificationType The notification type
   * @returns The toast type
   */
  private mapNotificationTypeToToastType(notificationType: NotificationType): ToastType {
    switch (notificationType) {
      case 'error':
        return 'error';
      case 'success':
        return 'success';
      case 'warning':
        return 'warning';
      case 'info':
      case 'system':
      case 'milestone':
      case 'update':
      case 'reminder':
      default:
        return 'info';
    }
  }

  /**
   * Get all notifications
   * @returns All notifications
   */
  public getNotifications(): Notification[] {
    return [...this.notifications];
  }

  /**
   * Get unread notifications
   * @returns Unread notifications
   */
  public getUnreadNotifications(): Notification[] {
    return this.notifications.filter((notification) => !notification.read);
  }

  /**
   * Mark a notification as read
   * @param id The notification ID
   */
  public markAsRead(id: string): void {
    const notification = this.notifications.find((n) => n.id === id);
    if (notification) {
      notification.read = true;
      this.saveNotifications();
    }
  }

  /**
   * Mark all notifications as read
   */
  public markAllAsRead(): void {
    this.notifications.forEach((notification) => {
      notification.read = true;
    });
    this.saveNotifications();
  }

  /**
   * Delete a notification
   * @param id The notification ID
   */
  public deleteNotification(id: string): void {
    this.notifications = this.notifications.filter((n) => n.id !== id);
    this.saveNotifications();
  }

  /**
   * Delete all notifications
   */
  public deleteAllNotifications(): void {
    this.notifications = [];
    this.saveNotifications();
  }

  /**
   * Delete read notifications
   */
  public deleteReadNotifications(): void {
    this.notifications = this.notifications.filter((n) => !n.read);
    this.saveNotifications();
  }

  /**
   * Create a milestone notification
   * @param message The notification message
   * @param options The notification options
   * @returns The notification ID
   */
  public createMilestoneNotification(
    message: string,
    options: Omit<NotificationOptions, 'type'> = {}
  ): string | null {
    return this.createNotification(message, {
      ...options,
      type: 'milestone',
      icon: options.icon || '🏆',
      toast: options.toast !== undefined ? options.toast : true,
    });
  }

  /**
   * Create an update notification
   * @param message The notification message
   * @param options The notification options
   * @returns The notification ID
   */
  public createUpdateNotification(
    message: string,
    options: Omit<NotificationOptions, 'type'> = {}
  ): string | null {
    return this.createNotification(message, {
      ...options,
      type: 'update',
      icon: options.icon || '🔄',
      toast: options.toast !== undefined ? options.toast : true,
    });
  }

  /**
   * Create a reminder notification
   * @param message The notification message
   * @param options The notification options
   * @returns The notification ID
   */
  public createReminderNotification(
    message: string,
    options: Omit<NotificationOptions, 'type'> = {}
  ): string | null {
    return this.createNotification(message, {
      ...options,
      type: 'reminder',
      icon: options.icon || '⏰',
      toast: options.toast !== undefined ? options.toast : true,
    });
  }
}

export default NotificationService;
