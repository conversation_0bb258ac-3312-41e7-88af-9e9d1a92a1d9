/**
 * Data Management Service
 *
 * This service provides functionality for data export, import, backup, recovery, and reset.
 * It handles conflict resolution, data validation, and error correction.
 */

import { encryptData as aesEncryptData, decryptData as aesDecryptData } from '../utils/crypto';
import localforage from 'localforage';
import { saveData, loadData, deleteData } from '../utils/storage';

// Types
export interface DataExportOptions {
  includePersonalData: boolean;
  includeFinancialData: boolean;
  includeConnectionData: boolean;
  format: 'json' | 'pdf';
  encryptData: boolean;
}

export interface DataImportOptions {
  validateBeforeImport: boolean;
  resolveConflicts: 'manual' | 'newer' | 'existing' | 'imported';
  mergeStrategy: 'replace' | 'merge' | 'append';
}

export interface DataBackupOptions {
  autoBackup: boolean;
  backupFrequency: 'daily' | 'weekly' | 'monthly' | 'onchange';
  maxBackups: number;
  encryptBackups: boolean;
}

export interface DataResetOptions {
  resetPersonalData: boolean;
  resetFinancialData: boolean;
  resetConnectionData: boolean;
  createBackupBeforeReset: boolean;
}

export interface DataValidationResult {
  isValid: boolean;
  errors: Array<{
    path: string;
    message: string;
    severity: 'error' | 'warning' | 'info';
  }>;
  warnings: Array<{
    path: string;
    message: string;
  }>;
}

export interface DataConflict {
  path: string;
  existingValue: any;
  importedValue: any;
  existingTimestamp?: number;
  importedTimestamp?: number;
  resolution?: 'existing' | 'imported' | 'manual' | 'merged';
  manualValue?: any;
}

export interface DataExportResult {
  success: boolean;
  data?: string | Blob;
  format: 'json' | 'pdf';
  timestamp: number;
  error?: string;
}

export interface DataImportResult {
  success: boolean;
  conflicts: DataConflict[];
  validationResult: DataValidationResult;
  error?: string;
}

export interface DataBackupResult {
  success: boolean;
  backupId: string;
  timestamp: number;
  error?: string;
}

export interface DataResetResult {
  success: boolean;
  backupCreated: boolean;
  backupId?: string;
  error?: string;
}

/**
 * Data Management Service
 */
class DataManagementService {
  private static instance: DataManagementService;
  private storageKeys: string[] = [];
  private backups: Record<string, { data: any; timestamp: number }> = {};

  /**
   * Get the singleton instance of the DataManagementService
   */
  public static getInstance(): DataManagementService {
    if (!DataManagementService.instance) {
      DataManagementService.instance = new DataManagementService();
    }
    return DataManagementService.instance;
  }

  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor() {
    // Initialize storage keys
    this.storageKeys = [
      'lifecompass_user_data',
      'lifecompass_financial_data',
      'lifecompass_connections_data',
      'lifecompass_settings',
      'lifecompass_seasons_of_self',
      'lifecompass_guided_journey',
    ];
  }

  /**
   * Export data from local storage
   */
  public async exportData(options: DataExportOptions): Promise<DataExportResult> {
    try {
      const allData = await loadData('all'); // Load all data
      if (!allData) {
        console.warn('No data available to export.');
        return {
          success: false,
          format: options.format,
          timestamp: Date.now(),
          error: 'No data available to export',
        };
      }

      // Encrypt the data before exporting
      const encryptedData = options.encryptData
        ? await this.encryptData(JSON.stringify(allData))
        : JSON.stringify(allData, null, 2);

      // Add metadata
      const metadata = {
        exportTimestamp: Date.now(),
        exportVersion: '1.0.0',
        exportOptions: options,
      };

      const fullExport = {
        metadata,
        data: encryptedData,
      };

      // Format the export based on options
      if (options.format === 'json') {
        return {
          success: true,
          data: encryptedData,
          format: 'json',
          timestamp: metadata.exportTimestamp,
        };
      } else if (options.format === 'pdf') {
        // In a real implementation, we would generate a PDF here
        // For this demo, we'll just return a placeholder
        return {
          success: true,
          data: new Blob(['PDF data would be generated here'], { type: 'application/pdf' }),
          format: 'pdf',
          timestamp: metadata.exportTimestamp,
        };
      }

      throw new Error(`Unsupported export format: ${options.format}`);
    } catch (error) {
      console.error('Error exporting data:', error);
      return {
        success: false,
        format: options.format,
        timestamp: Date.now(),
        error: error instanceof Error ? error.message : 'Unknown error during export',
      };
    }
  }

  /**
   * Import data into local storage
   */
  public async importData(
    importedDataString: string,
    options: DataImportOptions
  ): Promise<DataImportResult> {
    try {
      let dataToImport: any;

      // Attempt to parse as JSON first
      try {
        dataToImport = JSON.parse(importedDataString);
      } catch (jsonError) {
        // If JSON parsing fails, assume it's encrypted and try to decrypt
        try {
          const decryptedDataString = await this.decryptData(importedDataString);
          dataToImport = JSON.parse(decryptedDataString);
          console.log('Data decrypted successfully for import.');
        } catch (decryptError) {
          console.error('Failed to parse or decrypt imported data:', decryptError);
          return {
            success: false,
            conflicts: [],
            validationResult: {
              isValid: false,
              errors: [
                {
                  path: 'root',
                  message:
                    'Failed to parse or decrypt imported data. Please ensure it is a valid export file.',
                  severity: 'error',
                },
              ],
              warnings: [],
            },
            error:
              'Failed to parse or decrypt imported data. Please ensure it is a valid export file.',
          };
        }
      }

      // Basic validation: Check for expected structure (e.g., metadata and data fields)
      if (
        !dataToImport ||
        typeof dataToImport !== 'object' ||
        !dataToImport.metadata ||
        !dataToImport.data
      ) {
        return {
          success: false,
          conflicts: [],
          validationResult: {
            isValid: false,
            errors: [
              {
                path: 'root',
                message: 'Invalid data format. Missing metadata or data fields.',
                severity: 'error',
              },
            ],
            warnings: [],
          },
          error: 'Invalid data format. Missing metadata or data fields.',
        };
      }

      // Validate metadata (optional, but good practice)
      if (
        typeof dataToImport.metadata.exportDate !== 'string' ||
        typeof dataToImport.metadata.appVersion !== 'string'
      ) {
        console.warn('Imported data metadata appears incomplete or invalid.');
        // Decide if this should fail the import or just warn
        // For now, we'll just warn and continue if data exists
      }

      // Assuming dataToImport.data contains the actual data structure to save
      const actualData = dataToImport.data;

      let conflicts: DataConflict[] = [];
      let validationResult: DataValidationResult = { isValid: true, errors: [], warnings: [] }; // Initialize validation result

      // Perform deeper validation if requested
      if (options.validateBeforeImport) {
        validationResult = this.validateImportData(actualData); // Use the validateImportData method
        if (!validationResult.isValid) {
          return {
            // Return immediately if validation fails
            success: false,
            conflicts: [], // No conflicts to resolve if data is invalid
            validationResult: validationResult,
            error: 'Imported data failed validation.',
          };
        }
      }

      // Detect conflicts if merge strategy is not 'replace'
      if (options.mergeStrategy !== 'replace') {
        conflicts = await this.detectConflicts(actualData); // Call detectConflicts

        if (conflicts.length > 0) {
          // If conflicts exist and manual resolution is required, return conflicts to the UI
          if (options.resolveConflicts === 'manual') {
            return {
              success: false,
              conflicts: conflicts, // Return detected conflicts
              validationResult: validationResult, // Include validation result
              error: 'Conflicts detected. Manual resolution required.',
            };
          } else {
            // Auto-resolve conflicts based on the chosen strategy
            this.resolveConflicts(conflicts, options.resolveConflicts);
            console.log(`Conflicts auto-resolved using strategy: ${options.resolveConflicts}`);
            // The saving logic below will use the imported data, effectively applying 'imported' resolution
            // For 'newer' and 'existing', the saving logic would need to be more complex to apply the resolved values.
            // TODO: Enhance saving logic to respect resolvedConflicts for 'newer' and 'existing' strategies
          }
        }
      }

      // Reset current data if replace option is true or merge/append are used (current simple implementation clears data)
      // The TODO below indicates the merge/append logic needs to be more sophisticated.
      // For now, clear existing data if not replacing, to simulate saving the potentially resolved data.
      if (options.mergeStrategy === 'replace' || conflicts.length > 0) {
        // Clear existing data if replacing or conflicts were detected (for simplified merge/append sim)
        await this.resetData({
          resetPersonalData: true,
          resetFinancialData: true,
          resetConnectionData: true,
          createBackupBeforeReset: true,
        });
        console.log('Existing data reset before import/merge simulation.');
      }

      // Save the imported data (or the data after auto-resolution)
      // TODO: Implement proper deep merge/append logic here for mergeStrategy 'merge' and 'append'
      // The current implementation iterates through top-level keys and saves them, behaving somewhat like a deep replace after the initial clear.

      const compassData = actualData.financialCompass; // Assuming this structure
      const seasonsData = actualData.seasonsOfSelf; // Assuming this structure
      const userProfilesData = actualData.userProfiles; // Assuming this structure

      if (compassData) {
        for (const direction in compassData) {
          if (compassData.hasOwnProperty(direction)) {
            // For 'newer'/'existing' auto-resolution, we'd need to apply the resolved value here
            // For now, saving the imported data for this key.
            await saveData(direction, compassData[direction]);
          }
        }
        console.log('Financial Compass data imported/merged.');
      }
      if (seasonsData) {
        // For 'newer'/'existing' auto-resolution, apply resolved value if any.
        await saveData('seasonsOfSelf', seasonsData);
        console.log('Seasons of Self data imported/merged.');
      }
      if (userProfilesData) {
        // For 'newer'/'existing' auto-resolution, apply resolved value if any.
        await saveData('userProfiles', userProfilesData);
        console.log('User Profiles data imported/merged.');
      }

      return {
        success: true,
        conflicts: conflicts, // Return conflicts even if auto-resolved (for user info)
        validationResult: validationResult, // Include validation result
      };
    } catch (error) {
      console.error('Error importing data:', error);
      return {
        success: false,
        conflicts: [],
        validationResult: {
          isValid: false,
          errors: [
            {
              path: 'root',
              message: `An unexpected error occurred during data import: ${error.message}`,
              severity: 'error',
            },
          ],
          warnings: [],
        },
        error: `An unexpected error occurred during data import: ${error.message}`,
      };
    }
  }

  /**
   * Create a backup of the current data
   */
  public async createBackup(options: DataBackupOptions): Promise<DataBackupResult> {
    try {
      // Export all data
      const exportResult = await this.exportData({
        includePersonalData: true,
        includeFinancialData: true,
        includeConnectionData: true,
        format: 'json',
        encryptData: options.encryptBackups,
      });

      if (!exportResult.success || !exportResult.data) {
        throw new Error(exportResult.error || 'Failed to export data for backup');
      }

      // Generate a backup ID
      const backupId = `backup_${Date.now()}`;

      // Store the backup
      this.backups[backupId] = {
        data: exportResult.data,
        timestamp: exportResult.timestamp,
      };

      // Manage backup retention
      this.manageBackups(options.maxBackups);

      return {
        success: true,
        backupId,
        timestamp: exportResult.timestamp,
      };
    } catch (error) {
      console.error('Error creating backup:', error);
      return {
        success: false,
        backupId: '',
        timestamp: Date.now(),
        error: error instanceof Error ? error.message : 'Unknown error during backup',
      };
    }
  }

  /**
   * Restore data from a backup
   */
  public async restoreBackup(backupId: string): Promise<DataImportResult> {
    try {
      // Check if the backup exists
      if (!this.backups[backupId]) {
        throw new Error(`Backup not found: ${backupId}`);
      }

      // Import the backup data
      return await this.importData(
        typeof this.backups[backupId].data === 'string'
          ? this.backups[backupId].data
          : JSON.stringify(this.backups[backupId].data),
        {
          validateBeforeImport: true,
          resolveConflicts: 'imported',
          mergeStrategy: 'replace',
        }
      );
    } catch (error) {
      console.error('Error restoring backup:', error);
      return {
        success: false,
        conflicts: [],
        validationResult: {
          isValid: false,
          errors: [
            {
              path: 'root',
              message: error instanceof Error ? error.message : 'Unknown error during restore',
              severity: 'error',
            },
          ],
          warnings: [],
        },
        error: error instanceof Error ? error.message : 'Unknown error during restore',
      };
    }
  }

  /**
   * Reset data based on options
   */
  public async resetData(options: DataResetOptions): Promise<DataResetResult> {
    try {
      let backupId = '';
      let backupCreated = false;

      // Create a backup before reset if requested
      if (options.createBackupBeforeReset) {
        const backupResult = await this.createBackup({
          autoBackup: false,
          backupFrequency: 'onchange',
          maxBackups: 10,
          encryptBackups: true,
        });

        if (backupResult.success) {
          backupId = backupResult.backupId;
          backupCreated = true;
        } else {
          console.warn('Failed to create backup before reset:', backupResult.error);
        }
      }

      // Reset data based on options
      for (const key of this.storageKeys) {
        if (
          (options.resetPersonalData && key.includes('user_data')) ||
          (options.resetFinancialData && key.includes('financial_data')) ||
          (options.resetConnectionData && key.includes('connections_data'))
        ) {
          localStorage.removeItem(key);
        }
      }

      return {
        success: true,
        backupCreated,
        backupId: backupCreated ? backupId : undefined,
      };
    } catch (error) {
      console.error('Error resetting data:', error);
      return {
        success: false,
        backupCreated: false,
        error: error instanceof Error ? error.message : 'Unknown error during reset',
      };
    }
  }

  /**
   * Validate imported data
   */
  private validateImportData(data: any): DataValidationResult {
    const errors: Array<{
      path: string;
      message: string;
      severity: 'error' | 'warning' | 'info';
    }> = [];
    const warnings: Array<{
      path: string;
      message: string;
    }> = [];

    // Check for required metadata
    if (!data.metadata) {
      errors.push({
        path: 'metadata',
        message: 'Missing metadata',
        severity: 'error',
      });
    } else {
      // Check metadata fields
      if (!data.metadata.exportTimestamp) {
        warnings.push({
          path: 'metadata.exportTimestamp',
          message: 'Missing export timestamp',
        });
      }

      if (!data.metadata.exportVersion) {
        warnings.push({
          path: 'metadata.exportVersion',
          message: 'Missing export version',
        });
      }
    }

    // Check for data object
    if (!data.data) {
      errors.push({
        path: 'data',
        message: 'Missing data object',
        severity: 'error',
      });
    } else {
      // Validate each data key
      for (const key in data.data) {
        if (Object.prototype.hasOwnProperty.call(data.data, key)) {
          if (!this.storageKeys.includes(key)) {
            warnings.push({
              path: `data.${key}`,
              message: `Unknown data key: ${key}`,
            });
          }
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Detect conflicts between imported data and existing data
   */
  private async detectConflicts(importedData: Record<string, any>): Promise<DataConflict[]> {
    const conflicts: DataConflict[] = [];

    // Iterate over the keys we manage that exist in the imported data
    for (const key of this.storageKeys) {
      if (importedData.hasOwnProperty(key)) {
        const existingData = await loadData(key); // Use loadData to get existing data
        const importedValue = importedData[key];

        // If existing data for this key exists and is different from imported data
        // Note: A deep comparison would be more robust for complex objects/arrays
        if (
          existingData !== null &&
          JSON.stringify(existingData) !== JSON.stringify(importedValue)
        ) {
          conflicts.push({
            path: key,
            existingValue: existingData,
            importedValue: importedValue,
            // Attempt to get timestamps for newer/existing resolution
            existingTimestamp: this.getDataTimestamp(existingData),
            importedTimestamp: this.getDataTimestamp(importedValue),
            // resolution will be determined later based on import options
          });
        }
      }
    }

    return conflicts;
  }

  /**
   * Resolve conflicts based on strategy
   */
  private resolveConflicts(
    conflicts: DataConflict[],
    strategy: 'newer' | 'existing' | 'imported'
  ): void {
    for (const conflict of conflicts) {
      switch (strategy) {
        case 'newer':
          if (conflict.existingTimestamp && conflict.importedTimestamp) {
            conflict.resolution =
              conflict.importedTimestamp > conflict.existingTimestamp ? 'imported' : 'existing';
          } else {
            // Default to imported if timestamps are not available
            conflict.resolution = 'imported';
          }
          break;
        case 'existing':
          conflict.resolution = 'existing';
          break;
        case 'imported':
          conflict.resolution = 'imported';
          break;
      }
    }
  }

  /**
   * Manage backups based on max backups
   */
  private manageBackups(maxBackups: number): void {
    const backupIds = Object.keys(this.backups);

    if (backupIds.length > maxBackups) {
      // Sort backups by timestamp (oldest first)
      const sortedBackups = backupIds.sort(
        (a, b) => this.backups[a].timestamp - this.backups[b].timestamp
      );

      // Remove oldest backups
      const backupsToRemove = sortedBackups.slice(0, backupIds.length - maxBackups);

      for (const backupId of backupsToRemove) {
        delete this.backups[backupId];
      }
    }
  }

  /**
   * Get timestamp from data
   */
  private getDataTimestamp(data: any): number | undefined {
    // Try to find a timestamp in the data
    // This is a simplified implementation
    if (data && typeof data === 'object') {
      if (data.timestamp) return data.timestamp;
      if (data.updatedAt) return new Date(data.updatedAt).getTime();
      if (data.createdAt) return new Date(data.createdAt).getTime();
      if (data.lastModified) return new Date(data.lastModified).getTime();
    }

    return undefined;
  }

  /**
   * Encrypt data (secure implementation)
   */
  private async encryptData(data: string): Promise<string> {
    // TODO: Use a user-specific password/secret in production
    return aesEncryptData(data, 'lifecompass-secure');
  }

  /**
   * Decrypt data (secure implementation)
   */
  private async decryptData(data: string): Promise<string> {
    // TODO: Use a user-specific password/secret in production
    return aesDecryptData(data.replace('ENCRYPTED:', ''), 'lifecompass-secure');
  }

  /**
   * Delete all user data
   */
  public async deleteAllData(): Promise<{ success: boolean; error?: string }> {
    try {
      // Clear all items from local storage based on registered keys
      for (const key of this.storageKeys) {
        localStorage.removeItem(key);
      }
      // Optionally, clear session data if stored separately from storageKeys
      localStorage.removeItem('lifecompass_session'); // Assuming session key is stored like this

      // TODO: In a real application with a backend, this would also involve
      // sending a request to the server to delete data from the database.

      return { success: true };
    } catch (error) {
      console.error('Error deleting all data:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error during data deletion',
      };
    }
  }
}

export default DataManagementService;
