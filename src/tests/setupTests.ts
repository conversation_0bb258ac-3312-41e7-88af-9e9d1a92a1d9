// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';

// Mock for styled-components theme
jest.mock('styled-components', () => {
  const originalModule = jest.requireActual('styled-components');

  return {
    ...originalModule,
    useTheme: () => ({
      mode: 'light',
      season: 'spring',
      colors: {
        seasons: {
          spring: {
            primary: '#4CAF50',
            secondary: '#8BC34A',
            accent: '#FFEB3B',
            background: {
              light: '#F1F8E9',
              dark: '#1B5E20',
            },
          },
          summer: {
            primary: '#2196F3',
            secondary: '#03A9F4',
            accent: '#FF9800',
            background: {
              light: '#E3F2FD',
              dark: '#0D47A1',
            },
          },
          autumn: {
            primary: '#FF5722',
            secondary: '#FF9800',
            accent: '#795548',
            background: {
              light: '#FBE9E7',
              dark: '#BF360C',
            },
          },
          winter: {
            primary: '#9C27B0',
            secondary: '#673AB7',
            accent: '#00BCD4',
            background: {
              light: '#F3E5F5',
              dark: '#4A148C',
            },
          },
        },
        neutral: {
          background: '#FFFFFF',
          surface: '#F5F5F5',
          primaryText: '#212121',
          secondaryText: '#757575',
          divider: '#BDBDBD',
        },
        semantic: {
          success: '#4CAF50',
          warning: '#FFC107',
          error: '#F44336',
          info: '#2196F3',
        },
      },
      typography: {
        fontFamily: {
          primary: '"Playfair Display", serif',
          secondary: '"Lato", sans-serif',
          tertiary: '"Montserrat", sans-serif',
        },
        fontSize: {
          display: '3rem',
          h1: '2rem',
          h2: '1.5rem',
          h3: '1.25rem',
          bodyLarge: '1.125rem',
          body: '1rem',
          bodySmall: '0.875rem',
          caption: '0.75rem',
        },
        fontWeight: {
          light: 300,
          regular: 400,
          medium: 500,
          bold: 700,
        },
        lineHeight: {
          tight: 1.2,
          normal: 1.5,
          relaxed: 1.8,
        },
      },
      spacing: {
        micro: '0.25rem',
        tiny: '0.5rem',
        small: '1rem',
        medium: '1.5rem',
        large: '2rem',
        xLarge: '3rem',
        xxLarge: '4rem',
        huge: '6rem',
      },
      borderRadius: {
        small: '0.25rem',
        medium: '0.5rem',
        large: '1rem',
        circular: '50%',
      },
      shadows: {
        subtle: '0 2px 4px rgba(0,0,0,0.05)',
        medium: '0 4px 8px rgba(0,0,0,0.1)',
        strong: '0 8px 16px rgba(0,0,0,0.15)',
        dramatic: '0 16px 32px rgba(0,0,0,0.2)',
      },
    }),
  };
});
