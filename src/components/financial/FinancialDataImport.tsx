/**
 * Financial Data Import Component
 *
 * A component for importing financial data from various sources.
 */

import React, { useState, useRef } from 'react';
import styled from 'styled-components';
import Modal from '../ui/Modal';

// Types
interface FinancialDataImportProps {
  isOpen: boolean;
  onClose: () => void;
}

// Styled Components
const ImportContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const TabsContainer = styled.div`
  display: flex;
  border-bottom: 1px solid #eee;
`;

const Tab = styled.button<{ isActive: boolean }>`
  padding: 10px 16px;
  background: none;
  border: none;
  border-bottom: 2px solid ${({ isActive }) => (isActive ? '#4a90e2' : 'transparent')};
  color: ${({ isActive }) => (isActive ? '#4a90e2' : '#666')};
  font-weight: ${({ isActive }) => (isActive ? '600' : '400')};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    color: #4a90e2;
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
  }
`;

const TabContent = styled.div`
  padding: 10px 0;
`;

const FileUploadArea = styled.div`
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 30px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #4a90e2;
    background-color: rgba(74, 144, 226, 0.05);
  }
`;

const UploadIcon = styled.div`
  margin-bottom: 16px;
  color: #666;

  svg {
    width: 48px;
    height: 48px;
  }
`;

const UploadText = styled.div`
  font-size: 16px;
  color: #666;
  margin-bottom: 8px;
`;

const UploadSubtext = styled.div`
  font-size: 14px;
  color: #999;
`;

const FileInput = styled.input`
  display: none;
`;

const ConnectButton = styled.button`
  padding: 12px 20px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  width: 100%;
  margin-top: 20px;

  &:hover {
    background-color: #3a80d2;
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.4);
  }
`;

const BankList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const BankItem = styled.div`
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #eee;
  cursor: pointer;

  &:hover {
    background-color: #f9f9f9;
  }
`;

const BankLogo = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-weight: 600;
  color: #666;
`;

const BankInfo = styled.div`
  flex: 1;
`;

const BankName = styled.div`
  font-size: 16px;
  font-weight: 500;
  color: #333;
`;

const BankDescription = styled.div`
  font-size: 14px;
  color: #666;
  margin-top: 4px;
`;

/**
 * Financial Data Import Component
 */
const FinancialDataImport: React.FC<FinancialDataImportProps> = ({ isOpen, onClose }) => {
  const [activeTab, setActiveTab] = useState<'file' | 'bank'>('file');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0]);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      setSelectedFile(e.dataTransfer.files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const handleUploadClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleConnectBank = (bankName: string) => {
    // In a real app, this would initiate a bank connection flow
    console.log(`Connecting to ${bankName}`);
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Import Financial Data" width="600px">
      <ImportContainer>
        <TabsContainer>
          <Tab isActive={activeTab === 'file'} onClick={() => setActiveTab('file')}>
            Upload File
          </Tab>
          <Tab isActive={activeTab === 'bank'} onClick={() => setActiveTab('bank')}>
            Connect Bank
          </Tab>
        </TabsContainer>

        <TabContent>
          {activeTab === 'file' && (
            <>
              <FileUploadArea
                onClick={handleUploadClick}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
              >
                <UploadIcon>
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM14 13v4h-4v-4H7l5-5 5 5h-3z" />
                  </svg>
                </UploadIcon>
                <UploadText>
                  {selectedFile
                    ? `Selected: ${selectedFile.name}`
                    : 'Drag & drop a file here or click to browse'}
                </UploadText>
                <UploadSubtext>Supported formats: CSV, OFX, QFX, PDF</UploadSubtext>
                <FileInput
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileChange}
                  accept=".csv,.ofx,.qfx,.pdf"
                />
              </FileUploadArea>

              <ConnectButton disabled={!selectedFile}>
                {selectedFile ? 'Import Data' : 'Select a File to Import'}
              </ConnectButton>
            </>
          )}

          {activeTab === 'bank' && (
            <BankList>
              {['Chase', 'Bank of America', 'Wells Fargo', 'Citibank', 'Capital One'].map(
                (bank) => (
                  <BankItem key={bank} onClick={() => handleConnectBank(bank)}>
                    <BankLogo>{bank.charAt(0)}</BankLogo>
                    <BankInfo>
                      <BankName>{bank}</BankName>
                      <BankDescription>Connect securely to import transactions</BankDescription>
                    </BankInfo>
                  </BankItem>
                )
              )}
            </BankList>
          )}
        </TabContent>
      </ImportContainer>
    </Modal>
  );
};

export default FinancialDataImport;
