import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import Modal from '../ui/Modal';
import { useTheme } from '../../theme/SimpleThemeProvider';

// Define the key for storing consent status in localStorage
const CONSENT_STORAGE_KEY = 'lifecompass_data_consent';

interface DataConsentModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const Container = styled.div`
  padding: 20px;
  line-height: 1.6;
`;

const Title = styled.h2`
  margin-top: 0;
  color: ${({ theme }) => theme.colors.primary};
`;

const Paragraph = styled.p`
  margin-bottom: 15px;
  color: ${({ theme }) => theme.colors.text};
`;

const CheckboxLabel = styled.label`
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  cursor: pointer;
  color: ${({ theme }) => theme.colors.text};
`;

const Checkbox = styled.input`
  margin-right: 10px;
`;

const ButtonGroup = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
`;

const AcceptButton = styled.button`
  padding: 10px 20px;
  background-color: ${({ theme }) => theme.colors.primary};
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background-color: ${({ theme }) => theme.colors.primaryHover};
  }
`;

const DataConsentModal: React.FC<DataConsentModalProps> = ({ isOpen, onClose }) => {
  const { theme } = useTheme();
  const [consentGiven, setConsentGiven] = useState(false);

  // Check initial consent status from localStorage
  useEffect(() => {
    const consent = localStorage.getItem(CONSENT_STORAGE_KEY);
    if (consent === 'true') {
      setConsentGiven(true);
      // If consent is already given, close the modal immediately if it was opened
      if (isOpen) {
        onClose();
      }
    }
  }, [isOpen, onClose]); // Re-run effect if modal open state changes

  const handleAcceptConsent = () => {
    localStorage.setItem(CONSENT_STORAGE_KEY, 'true');
    setConsentGiven(true);
    onClose();
  };

  // If consent is already given, don't render the modal content
  if (consentGiven && !isOpen) {
    return null;
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={() => {
        /* Prevent closing via backdrop click */
      }}
      title="Data Privacy Consent"
      width="500px"
    >
      <Container theme={theme}>
        <Title theme={theme}>Data Privacy and Usage</Title>
        <Paragraph theme={theme}>
          LifeCompass requires access to your personal and financial data to provide its services.
          This data is stored locally on your device and is encrypted for your privacy. We do not
          transmit your sensitive data to external servers without your explicit consent for
          specific integrations.
        </Paragraph>
        <Paragraph theme={theme}>
          By using this application, you consent to the local storage and processing of your data as
          described in our Privacy Policy (link to be added).
        </Paragraph>
        <CheckboxLabel theme={theme}>
          <Checkbox
            type="checkbox"
            checked={consentGiven}
            onChange={() => setConsentGiven(!consentGiven)}
          />
          I have read and agree to the data privacy terms.
        </CheckboxLabel>
        <ButtonGroup>
          <AcceptButton onClick={handleAcceptConsent} disabled={!consentGiven} theme={theme}>
            Accept and Continue
          </AcceptButton>
        </ButtonGroup>
      </Container>
    </Modal>
  );
};

export default DataConsentModal;
