/**
 * Header Component
 *
 * A premium header component with theme toggle, navigation, and seasonal styling.
 */

import React from 'react';
import styled from 'styled-components';
import { Link, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import ThemeToggle from '../ui/ThemeToggle';
import { NotificationButton } from '../../features/Notifications';
import { useTheme } from '../../theme/SimpleThemeProvider';

// Types
interface HeaderProps {
  className?: string;
}

// Styled Components
const HeaderContainer = styled.header`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  background-color: ${({ theme }) => theme.colors.background.paper};
  box-shadow: ${({ theme }) => theme.shadows.md};
  position: sticky;
  top: 0;
  z-index: ${({ theme }) => theme.zIndex.appBar};
  transition: all 0.3s ease;
`;

const LogoContainer = styled(Link)`
  display: flex;
  align-items: center;
  text-decoration: none;
  color: ${({ theme }) => theme.colors.seasons[theme.season].primary};
  font-weight: 700;
  font-size: 1.5rem;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }
`;

const LogoText = styled.span`
  margin-left: 0.5rem;
  font-family: ${({ theme }) => theme.typography.fontFamily.secondary};
`;

const SeasonIndicator = styled.div`
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: ${({ theme }) => {
    const season = theme.season;
    return `radial-gradient(circle, ${theme.colors.seasons[season].background.light} 0%, ${theme.colors.seasons[season].primary} 100%)`;
  }};
  margin-right: 0.5rem;
  box-shadow: ${({ theme }) => theme.shadows.sm};
`;

const Nav = styled.nav`
  display: flex;
  align-items: center;
`;

const NavLinks = styled.div`
  display: flex;
  margin-right: 2rem;
`;

const NavLink = styled(Link)<{ $active: boolean }>`
  color: ${({ theme, $active }) =>
    $active ? theme.colors.seasons[theme.season].primary : theme.colors.text.primary};
  text-decoration: none;
  padding: 0.5rem 1rem;
  font-weight: ${({ $active }) => ($active ? '600' : '400')};
  position: relative;
  transition: all 0.3s ease;

  &:hover {
    color: ${({ theme }) => theme.colors.seasons[theme.season].primary};
  }

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: ${({ $active }) => ($active ? '50%' : '0')};
    height: 2px;
    background-color: ${({ theme }) => theme.colors.seasons[theme.season].primary};
    transition: width 0.3s ease;
  }

  &:hover::after {
    width: 50%;
  }
`;

const Controls = styled.div`
  display: flex;
  align-items: center;
`;

// Animation variants
const logoVariants = {
  hidden: { opacity: 0, y: -20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      type: 'spring',
      stiffness: 300,
      damping: 20,
    },
  },
};

const navVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
    },
  },
};

const linkVariants = {
  hidden: { opacity: 0, y: -10 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      type: 'spring',
      stiffness: 300,
      damping: 20,
    },
  },
};

/**
 * Header Component
 */
const Header: React.FC<HeaderProps> = ({ className }) => {
  const location = useLocation();
  const { theme } = useTheme();

  // Get season emoji
  const getSeasonEmoji = () => {
    switch (theme.season) {
      case 'spring':
        return '🌱';
      case 'summer':
        return '☀️';
      case 'autumn':
        return '🍂';
      case 'winter':
        return '❄️';
      default:
        return '🌱';
    }
  };

  return (
    <HeaderContainer className={className}>
      <motion.div initial="hidden" animate="visible" variants={logoVariants}>
        <LogoContainer to="/">
          <SeasonIndicator />
          <LogoText>LifeCompass {getSeasonEmoji()}</LogoText>
        </LogoContainer>
      </motion.div>

      <Nav>
        <motion.div initial="hidden" animate="visible" variants={navVariants}>
          <NavLinks>
            <motion.div variants={linkVariants}>
              <NavLink to="/" $active={location.pathname === '/'}>
                Home
              </NavLink>
            </motion.div>
            <motion.div variants={linkVariants}>
              <NavLink to="/journey" $active={location.pathname.includes('/journey')}>
                Guided Journey
              </NavLink>
            </motion.div>
            <motion.div variants={linkVariants}>
              <NavLink
                to="/compass/north/personal-information"
                $active={location.pathname.includes('/compass')}
              >
                Financial Compass
              </NavLink>
            </motion.div>
            <motion.div variants={linkVariants}>
              <NavLink
                to="/seasons/spring/pleasure"
                $active={location.pathname.includes('/seasons')}
              >
                Life Stages Journey
              </NavLink>
            </motion.div>
            <motion.div variants={linkVariants}>
              <NavLink
                to="/financial-connections"
                $active={location.pathname.includes('/financial-connections')}
              >
                Financial Connections
              </NavLink>
            </motion.div>
          </NavLinks>
        </motion.div>

        <Controls>
          <div style={{ display: 'flex', marginRight: '16px' }}>
            <button
              onClick={() => console.log('Opening settings panel')}
              aria-label="Settings"
              title="Settings"
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                border: 'none',
                backgroundColor:
                  theme.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.04)',
                color: theme.colors.text.primary,
                cursor: 'pointer',
                margin: '0 4px',
                transition: 'all 0.2s ease',
              }}
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z" />
              </svg>
            </button>

            <button
              onClick={() => console.log('Opening profile manager')}
              aria-label="Profile Manager"
              title="Profile Manager"
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                border: 'none',
                backgroundColor:
                  theme.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.04)',
                color: theme.colors.text.primary,
                cursor: 'pointer',
                margin: '0 4px',
                transition: 'all 0.2s ease',
              }}
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
              </svg>
            </button>

            <button
              onClick={() => console.log('Starting financial data import')}
              aria-label="Import Financial Data"
              title="Import Financial Data"
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                border: 'none',
                backgroundColor:
                  theme.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.04)',
                color: theme.colors.text.primary,
                cursor: 'pointer',
                margin: '0 4px',
                transition: 'all 0.2s ease',
              }}
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" />
              </svg>
            </button>
          </div>

          <NotificationButton />
          <div style={{ width: '12px' }} />
          <ThemeToggle />
        </Controls>
      </Nav>
    </HeaderContainer>
  );
};

export default Header;
