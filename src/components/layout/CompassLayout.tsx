/**
 * Compass Layout Component
 *
 * This component provides a consistent layout for all compass direction pages.
 * It includes the sidebar navigation and main content area.
 */

import React, { ReactNode } from 'react';
import styled from 'styled-components';
import { useLocation } from 'react-router-dom';
import GuidedJourneyNavigator from '../../features/GuidedJourney/components/GuidedJourneyNavigator';

interface CompassLayoutProps {
  children: ReactNode;
  title?: string;
  description?: string;
}

// Styled Components
const LayoutContainer = styled.div`
  display: flex;
  min-height: 100vh;
  background-color: ${({ theme }) => theme.colors.background.default};
  transition: background-color 0.3s ease;
`;

const SidebarContainer = styled.div`
  width: 300px;
  padding: ${({ theme }) => theme.spacing(6)};
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-right: 1px solid ${({ theme }) => theme.colors.divider};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  z-index: 1;
  transition:
    background-color 0.3s ease,
    box-shadow 0.3s ease;

  @media (max-width: 768px) {
    width: 100%;
    position: fixed;
    height: 100%;
    transform: translateX(-100%);
    transition: transform 0.3s ease;

    &.open {
      transform: translateX(0);
    }
  }
`;

const MainContent = styled.div`
  flex: 1;
  padding: ${({ theme }) => theme.spacing(8)};
  overflow-y: auto;
  transition: background-color 0.3s ease;

  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing(4)};
  }
`;

const MobileMenuButton = styled.button`
  display: none;
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: ${({ theme }) => theme.colors.primary.main};
  color: ${({ theme }) => theme.colors.primary.contrastText};
  border: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  font-size: 24px;
  cursor: pointer;

  @media (max-width: 768px) {
    display: flex;
    align-items: center;
    justify-content: center;
  }
`;

/**
 * Compass Layout Component
 */
const CompassLayout: React.FC<CompassLayoutProps> = ({ children, title, description }) => {
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = React.useState(false);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <LayoutContainer>
      <SidebarContainer className={sidebarOpen ? 'open' : ''}>
        <GuidedJourneyNavigator currentPath={location.pathname} />
      </SidebarContainer>

      <MainContent>{children}</MainContent>

      <MobileMenuButton onClick={toggleSidebar}>{sidebarOpen ? '×' : '≡'}</MobileMenuButton>
    </LayoutContainer>
  );
};

export default CompassLayout;
