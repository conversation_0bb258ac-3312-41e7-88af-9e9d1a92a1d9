import React, { useId } from 'react';
import {
  TextField,
  TextFieldProps,
  FormControl,
  FormLabel,
  FormHelperText,
  Select,
  MenuItem,
  Checkbox,
  FormControlLabel,
  RadioGroup,
  Radio,
  Slider,
  Switch,
  InputLabel,
  SelectChangeEvent,
  OutlinedInput,
  InputAdornment,
  SxProps,
  Theme,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { Controller, useFormContext, FieldError } from 'react-hook-form';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

// Type for supported input types
type InputType =
  | 'text'
  | 'number'
  | 'email'
  | 'password'
  | 'tel'
  | 'date'
  | 'select'
  | 'multiselect'
  | 'checkbox'
  | 'radio'
  | 'slider'
  | 'switch'
  | 'currency';

type OptionType = {
  label: string;
  value: string | number | boolean;
  disabled?: boolean;
};

export interface FormFieldProps extends Omit<TextFieldProps, 'name' | 'error'> {
  name: string;
  label: string;
  type?: InputType;
  options?: OptionType[];
  helperText?: string;
  showError?: boolean;
  fullWidth?: boolean;
  required?: boolean;
  disabled?: boolean;
  readOnly?: boolean;
  autoComplete?: string;
  autoFocus?: boolean;
  startAdornment?: React.ReactNode;
  endAdornment?: React.ReactNode;
  min?: number;
  max?: number;
  step?: number | string;
  minRows?: number | string;
  maxRows?: number | string;
  multiline?: boolean;
  rows?: number | string;
  placeholder?: string;
  format?: string;
  parse?: (value: any) => any;
  sx?: SxProps<Theme>;
  containerSx?: SxProps<Theme>;
  labelSx?: SxProps<Theme>;
  inputSx?: SxProps<Theme>;
  helperTextSx?: SxProps<Theme>;
  transform?: {
    input?: (value: any) => any;
    output?: (value: any) => any;
  };
  rules?: any;
  defaultValue?: any;
  value?: any;
  onChange?: (value: any) => void;
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  onFocus?: (event: React.FocusEvent<HTMLInputElement>) => void;
  onKeyDown?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  onKeyUp?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  onKeyPress?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  InputLabelProps?: any;
  InputProps?: any;
  SelectProps?: any;
  FormHelperTextProps?: any;
  FormControlProps?: any;
  FormLabelProps?: any;
  FormControlLabelProps?: any;
  RadioGroupProps?: any;
  SliderProps?: any;
  SwitchProps?: any;
  CheckboxProps?: any;
  MenuProps?: any;
  renderInput?: (props: any) => React.ReactNode;
  renderOption?: (option: OptionType) => React.ReactNode;
  renderValue?: (value: any) => React.ReactNode;
  getOptionLabel?: (option: OptionType) => string;
  getOptionValue?: (option: OptionType) => string | number | boolean;
  isOptionEqualToValue?: (option: OptionType, value: any) => boolean;
  loading?: boolean;
  loadingText?: string;
  noOptionsText?: string;
  multiple?: boolean;
  disableClearable?: boolean;
  freeSolo?: boolean;
  autoHighlight?: boolean;
  autoSelect?: boolean;
  clearOnBlur?: boolean;
  clearOnEscape?: boolean;
  handleHomeEndKeys?: boolean;
  selectOnFocus?: boolean;
  blurOnSelect?: boolean | 'touch' | 'mouse';
  ChipProps?: any;
  limitTags?: number;
  disableCloseOnSelect?: boolean;
  disablePortal?: boolean;
  filterSelectedOptions?: boolean;
  forcePopupIcon?: boolean | 'auto';
  fullWidth?: boolean;
  groupBy?: (option: OptionType) => string;
  includeInputInList?: boolean;
  inputValue?: string;
  onInputChange?: (event: React.SyntheticEvent, value: string, reason: string) => void;
  onClose?: (event: React.SyntheticEvent) => void;
  onOpen?: (event: React.SyntheticEvent) => void;
  open?: boolean;
  PaperComponent?: React.ElementType;
  PopperComponent?: React.ElementType;
  popupIcon?: React.ReactNode;
  renderGroup?: (params: any) => React.ReactNode;
  renderTags?: (value: any[], getTagProps: any) => React.ReactNode;
  size?: 'small' | 'medium';
}

const FormField: React.FC<FormFieldProps> = ({
  name,
  label,
  type = 'text',
  options = [],
  helperText,
  showError = true,
  required = false,
  disabled = false,
  readOnly = false,
  fullWidth = true,
  autoComplete,
  autoFocus = false,
  startAdornment,
  endAdornment,
  min,
  max,
  step,
  minRows,
  maxRows,
  multiline = false,
  rows,
  placeholder,
  format,
  parse,
  sx = {},
  containerSx = {},
  labelSx = {},
  inputSx = {},
  helperTextSx = {},
  transform,
  rules = {},
  defaultValue,
  value: externalValue,
  onChange: externalOnChange,
  onBlur: externalOnBlur,
  onFocus: externalOnFocus,
  onKeyDown: externalOnKeyDown,
  onKeyUp: externalOnKeyUp,
  onKeyPress: externalOnKeyPress,
  InputLabelProps = {},
  InputProps = {},
  SelectProps = {},
  FormHelperTextProps = {},
  FormControlProps = {},
  FormLabelProps = {},
  FormControlLabelProps = {},
  RadioGroupProps = {},
  SliderProps = {},
  SwitchProps = {},
  CheckboxProps = {},
  MenuProps = {},
  renderInput,
  renderOption,
  renderValue,
  getOptionLabel = (option) => String(option.label),
  getOptionValue = (option) => option.value,
  isOptionEqualToValue = (option, value) => {
    if (value === undefined || value === null) return false;
    return getOptionValue(option) === value;
  },
  loading = false,
  loadingText = 'Loading...',
  noOptionsText = 'No options',
  multiple = false,
  disableClearable = false,
  freeSolo = false,
  autoHighlight = false,
  autoSelect = false,
  clearOnBlur = false,
  clearOnEscape = false,
  handleHomeEndKeys = false,
  selectOnFocus = false,
  blurOnSelect = false,
  ChipProps = {},
  limitTags = -1,
  disableCloseOnSelect = false,
  disablePortal = false,
  filterSelectedOptions = false,
  forcePopupIcon = 'auto',
  includeInputInList = false,
  inputValue: externalInputValue,
  onInputChange: externalOnInputChange,
  onClose: externalOnClose,
  onOpen: externalOnOpen,
  open: externalOpen,
  PaperComponent,
  PopperComponent,
  popupIcon,
  renderGroup,
  renderTags,
  size = 'medium',
  ...rest
}) => {
  const id = useId();
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const error = errors[name] as FieldError | undefined;
  const errorMessage = error?.message || '';

  const renderField = (field: any) => {
    const { onChange, onBlur, value, ref, ...fieldProps } = field;

    const handleChange = (
      event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | any
    ) => {
      let newValue = event?.target?.value ?? event;

      // Handle different input types
      if (type === 'number' && newValue !== '') {
        newValue = parseFloat(newValue);
      } else if (type === 'checkbox') {
        newValue = event?.target?.checked ?? event;
      }

      // Apply transform if provided
      if (transform?.input) {
        newValue = transform.input(newValue);
      }

      // Call form's onChange handler
      onChange(newValue);

      // Call external onChange if provided
      if (externalOnChange) {
        externalOnChange(newValue);
      }
    };

    const handleBlur = (event: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      onBlur();
      if (externalOnBlur) {
        externalOnBlur(event);
      }
    };

    const commonProps = {
      ...fieldProps,
      id: id || name,
      name,
      label,
      value: value ?? '',
      onChange: handleChange,
      onBlur: handleBlur,
      onFocus: externalOnFocus,
      onKeyDown: externalOnKeyDown,
      onKeyUp: externalOnKeyUp,
      onKeyPress: externalOnKeyPress,
      error: !!error,
      disabled,
      required,
      fullWidth,
      autoComplete,
      autoFocus,
      placeholder,
      inputRef: ref,
      InputLabelProps: {
        ...InputLabelProps,
        sx: { ...labelSx, ...InputLabelProps.sx },
      },
      InputProps: {
        ...InputProps,
        startAdornment: startAdornment ? (
          <InputAdornment position="start">{startAdornment}</InputAdornment>
        ) : undefined,
        endAdornment: endAdornment ? (
          <InputAdornment position="end">{endAdornment}</InputAdornment>
        ) : undefined,
        readOnly,
        ...(type === 'number' && { inputProps: { min, max, step } }),
        ...(multiline && { multiline: true, minRows, maxRows, rows }),
        sx: { ...inputSx, ...InputProps.sx },
      },
      sx: {
        '& .MuiOutlinedInput-root': {
          '&:hover fieldset': {
            borderColor: 'primary.main',
          },
          '&.Mui-focused fieldset': {
            borderColor: 'primary.main',
            borderWidth: 1,
          },
        },
        ...sx,
      },
      ...rest,
    };

    switch (type) {
      case 'select':
        return (
          <FormControl
            fullWidth={fullWidth}
            error={!!error}
            disabled={disabled}
            required={required}
            size={size}
            {...FormControlProps}
          >
            <InputLabel id={`${id}-label`}>{label}</InputLabel>
            <Select
              labelId={`${id}-label`}
              id={id}
              value={value ?? ''}
              onChange={handleChange}
              onBlur={handleBlur}
              input={<OutlinedInput label={label} />}
              MenuProps={{
                ...MenuProps,
                PaperProps: {
                  ...MenuProps.PaperProps,
                  sx: { maxHeight: 300, ...MenuProps.PaperProps?.sx },
                },
              }}
              {...SelectProps}
            >
              {options.map((option) => (
                <MenuItem key={String(option.value)} value={option.value as any}>
                  {renderOption ? renderOption(option) : getOptionLabel(option)}
                </MenuItem>
              ))}
            </Select>
            {showError && errorMessage && <FormHelperText error>{errorMessage}</FormHelperText>}
            {helperText && !errorMessage && <FormHelperText>{helperText}</FormHelperText>}
          </FormControl>
        );

      case 'checkbox':
        return (
          <FormControlLabel
            control={
              <Checkbox
                checked={!!value}
                onChange={handleChange}
                onBlur={handleBlur}
                disabled={disabled}
                required={required}
                {...CheckboxProps}
              />
            }
            label={label}
            {...FormControlLabelProps}
          />
        );

      case 'radio':
        return (
          <FormControl component="fieldset" error={!!error} {...FormControlProps}>
            <FormLabel component="legend" {...FormLabelProps}>
              {label}
              {required && ' *'}
            </FormLabel>
            <RadioGroup
              row
              name={name}
              value={value ?? ''}
              onChange={handleChange}
              onBlur={handleBlur}
              {...RadioGroupProps}
            >
              {options.map((option) => (
                <FormControlLabel
                  key={String(option.value)}
                  value={option.value}
                  control={<Radio />}
                  label={getOptionLabel(option)}
                  disabled={disabled || option.disabled}
                />
              ))}
            </RadioGroup>
            {showError && errorMessage && <FormHelperText error>{errorMessage}</FormHelperText>}
            {helperText && !errorMessage && <FormHelperText>{helperText}</FormHelperText>}
          </FormControl>
        );

      case 'slider':
        return (
          <FormControl fullWidth={fullWidth} error={!!error} {...FormControlProps}>
            <FormLabel component="legend" {...FormLabelProps}>
              {label}
              {required && ' *'}
            </FormLabel>
            <Slider
              value={value ?? 0}
              onChange={handleChange}
              valueLabelDisplay="auto"
              min={min}
              max={max}
              step={step}
              disabled={disabled}
              marks={[
                { value: min as number, label: min },
                { value: max as number, label: max },
              ]}
              {...SliderProps}
            />
            {showError && errorMessage && <FormHelperText error>{errorMessage}</FormHelperText>}
            {helperText && !errorMessage && <FormHelperText>{helperText}</FormHelperText>}
          </FormControl>
        );

      case 'switch':
        return (
          <FormControlLabel
            control={
              <Switch
                checked={!!value}
                onChange={handleChange}
                onBlur={handleBlur}
                disabled={disabled}
                required={required}
                {...SwitchProps}
              />
            }
            label={
              <>
                {label}
                {required && ' *'}
              </>
            }
            {...FormControlLabelProps}
          />
        );

      case 'date':
        return (
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DatePicker
              label={label}
              value={value || null}
              onChange={(date) => handleChange(date)}
              renderInput={(params) => (
                <TextField
                  {...params}
                  fullWidth={fullWidth}
                  error={!!error}
                  helperText={errorMessage || helperText}
                  required={required}
                  disabled={disabled}
                  {...commonProps}
                />
              )}
            />
          </LocalizationProvider>
        );

      case 'currency':
        return (
          <TextField
            type="number"
            InputProps={{
              ...commonProps.InputProps,
              startAdornment: <InputAdornment position="start">$</InputAdornment>,
            }}
            inputProps={{
              min: 0,
              step: '0.01',
              ...commonProps.inputProps,
            }}
            {...commonProps}
          />
        );

      default:
        return <TextField type={type} helperText={errorMessage || helperText} {...commonProps} />;
    }
  };

  return (
    <Controller
      name={name}
      control={control}
      defaultValue={defaultValue}
      rules={{
        required: required ? `${label} is required` : false,
        ...rules,
      }}
      render={({ field }) => <Box sx={{ mb: 2, ...containerSx }}>{renderField(field)}</Box>}
    />
  );
};

export default FormField;
