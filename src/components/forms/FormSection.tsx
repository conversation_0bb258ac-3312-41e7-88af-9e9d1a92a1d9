import React, { ReactNode } from 'react';
import { Box, Typography, Divider, SxProps, Theme } from '@mui/material';

interface FormSectionProps {
  /** Title of the section */
  title?: string;
  /** Subtitle or description for the section */
  subtitle?: string;
  /** Content of the section */
  children: ReactNode;
  /** Custom styles for the section container */
  sx?: SxProps<Theme>;
  /** Custom styles for the title */
  titleSx?: SxProps<Theme>;
  /** Custom styles for the subtitle */
  subtitleSx?: SxProps<Theme>;
  /** Custom styles for the content container */
  contentSx?: SxProps<Theme>;
  /** Show a divider at the bottom of the section */
  divider?: boolean;
  /** Show a top divider */
  topDivider?: boolean;
  /** Custom divider component */
  dividerComponent?: ReactNode;
  /** Additional props for the section container */
  containerProps?: any;
  /** Additional props for the title */
  titleProps?: any;
  /** Additional props for the subtitle */
  subtitleProps?: any;
  /** Additional props for the content */
  contentProps?: any;
}

/**
 * A component that groups related form fields into a section with a title and optional divider
 */
const FormSection: React.FC<FormSectionProps> = ({
  title,
  subtitle,
  children,
  sx = {},
  titleSx = {},
  subtitleSx = {},
  contentSx = {},
  divider = false,
  topDivider = false,
  dividerComponent,
  containerProps = {},
  titleProps = {},
  subtitleProps = {},
  contentProps = {},
}) => {
  return (
    <Box
      component="section"
      sx={{
        mb: 4,
        '&:last-child': {
          mb: 0,
        },
        ...sx,
      }}
      {...containerProps}
    >
      {topDivider && (dividerComponent || <Divider sx={{ mb: 3 }} />)}

      {(title || subtitle) && (
        <Box sx={{ mb: 2 }}>
          {title && (
            <Typography
              variant="h6"
              component="h3"
              sx={{
                fontWeight: 600,
                color: 'text.primary',
                ...titleSx,
              }}
              {...titleProps}
            >
              {title}
            </Typography>
          )}
          {subtitle && (
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                mt: title ? 0.5 : 0,
                ...subtitleSx,
              }}
              {...subtitleProps}
            >
              {subtitle}
            </Typography>
          )}
        </Box>
      )}

      <Box
        sx={{
          '& > *:not(:last-child)': {
            mb: 2,
          },
          ...contentSx,
        }}
        {...contentProps}
      >
        {children}
      </Box>

      {divider && (dividerComponent || <Divider sx={{ mt: 3 }} />)}
    </Box>
  );
};

export default FormSection;
