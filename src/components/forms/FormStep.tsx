import React, { ReactNode, useEffect } from 'react';
import { Box, BoxProps } from '@mui/material';
import { useFormContext } from '../../contexts/FormContext';

interface FormStepProps extends BoxProps {
  children: ReactNode;
  step: number;
  validationFields?: string[];
  onStepChange?: (step: number) => void;
}

/**
 * A component that wraps form steps and handles step validation
 */
export const FormStep: React.FC<FormStepProps> = ({
  children,
  step,
  validationFields = [],
  onStepChange,
  ...rest
}) => {
  const { formMethods, activeStep } = useFormContext();
  const {
    trigger,
    formState: { errors },
  } = formMethods;

  // Notify parent when step changes
  useEffect(() => {
    if (onStepChange) {
      onStepChange(step);
    }
  }, [onStepChange, step]);

  // Only render the current step
  if (activeStep !== step) {
    return null;
  }

  // Validate fields when step is active
  useEffect(() => {
    if (validationFields.length > 0) {
      trigger(validationFields);
    }
  }, [trigger, validationFields]);

  return (
    <Box
      component="div"
      role="tabpanel"
      id={`form-step-${step}`}
      aria-labelledby={`step-${step}`}
      className={`form-step form-step-${step}`}
      {...rest}
    >
      {children}
    </Box>
  );
};

export default FormStep;
