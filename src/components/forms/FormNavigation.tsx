import React from 'react';
import { Box, Button, BoxProps, ButtonProps } from '@mui/material';
import { useFormContext } from '../../contexts/FormContext';

interface FormNavigationProps extends BoxProps {
  nextButtonText?: string;
  backButtonText?: string;
  submitButtonText?: string;
  showBackButton?: boolean;
  showNextButton?: boolean;
  showSubmitButton?: boolean;
  onBack?: () => void;
  onNext?: () => Promise<boolean> | boolean | void;
  nextButtonProps?: ButtonProps;
  backButtonProps?: ButtonProps;
  submitButtonProps?: ButtonProps;
  containerProps?: BoxProps;
  showProgress?: boolean;
}

/**
 * A navigation component for multi-step forms
 */
export const FormNavigation: React.FC<FormNavigationProps> = ({
  nextButtonText = 'Next',
  backButtonText = 'Back',
  submitButtonText = 'Submit',
  showBackButton = true,
  showNextButton = true,
  showSubmitButton = true,
  onBack,
  onNext,
  nextButtonProps = {},
  backButtonProps = {},
  submitButtonProps = {},
  containerProps = {},
  showProgress = true,
  ...rest
}) => {
  const {
    isSubmitting,
    isFirstStep,
    isLastStep,
    nextStep,
    prevStep,
    activeStep,
    totalSteps,
    submitForm,
  } = useFormContext();

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      prevStep();
    }
  };

  const handleNext = async () => {
    if (onNext) {
      const shouldContinue = await onNext();
      if (shouldContinue !== false) {
        nextStep();
      }
    } else {
      nextStep();
    }
  };

  const handleSubmit = async () => {
    await submitForm();
  };

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mt: 4,
        pt: 2,
        borderTop: '1px solid',
        borderColor: 'divider',
        ...containerProps.sx,
      }}
      {...containerProps}
    >
      <Box>
        {showBackButton && !isFirstStep && (
          <Button
            onClick={handleBack}
            disabled={isSubmitting}
            variant="outlined"
            {...backButtonProps}
          >
            {backButtonText}
          </Button>
        )}
      </Box>

      {showProgress && totalSteps > 1 && (
        <Box sx={{ flex: 1, textAlign: 'center' }}>
          Step {activeStep + 1} of {totalSteps}
        </Box>
      )}

      <Box>
        {showNextButton && !isLastStep && (
          <Button
            onClick={handleNext}
            disabled={isSubmitting}
            variant="contained"
            color="primary"
            {...nextButtonProps}
          >
            {nextButtonText}
          </Button>
        )}

        {showSubmitButton && isLastStep && (
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting}
            variant="contained"
            color="primary"
            {...submitButtonProps}
          >
            {isSubmitting ? 'Submitting...' : submitButtonText}
          </Button>
        )}
      </Box>
    </Box>
  );
};

export default FormNavigation;
