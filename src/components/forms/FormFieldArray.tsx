import React, { useCallback } from 'react';
import {
  Box,
  Button,
  IconButton,
  Typography,
  Divider,
  SxProps,
  Theme,
  BoxProps,
  Tooltip,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  <PERSON><PERSON><PERSON><PERSON><PERSON> as Drag<PERSON><PERSON>leIcon,
} from '@mui/icons-material';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { DragDropContext, Draggable, Droppable, DropResult } from 'react-beautiful-dnd';

interface FormFieldArrayProps {
  /** Name of the field array in the form */
  name: string;
  /** Title of the field array section */
  title?: string;
  /** Description or helper text */
  description?: string;
  /** Component to render for each item in the array */
  renderItem: (index: number, methods: any) => React.ReactNode;
  /** Default values for new items */
  defaultItem?: any;
  /** Whether to show the add button */
  showAddButton?: boolean;
  /** Text for the add button */
  addButtonText?: string;
  /** Whether to show the remove button */
  showRemoveButton?: boolean;
  /** Whether to show the move (drag handle) button */
  showMoveButtons?: boolean;
  /** Minimum number of items */
  minItems?: number;
  /** Maximum number of items */
  maxItems?: number;
  /** Whether the field is required */
  required?: boolean;
  /** Error message to display */
  error?: string;
  /** Custom styles */
  sx?: SxProps<Theme>;
  /** Custom styles for the container */
  containerSx?: SxProps<Theme>;
  /** Custom styles for the header */
  headerSx?: SxProps<Theme>;
  /** Custom styles for the items container */
  itemsContainerSx?: SxProps<Theme>;
  /** Custom styles for each item */
  itemSx?: SxProps<Theme>;
  /** Custom styles for item actions */
  itemActionsSx?: SxProps<Theme>;
  /** Custom styles for the add button */
  addButtonSx?: SxProps<Theme>;
  /** Custom styles for the remove button */
  removeButtonSx?: SxProps<Theme>;
  /** Custom styles for the move button */
  moveButtonSx?: SxProps<Theme>;
  /** Custom props for the add button */
  addButtonProps?: any;
  /** Custom props for the remove button */
  removeButtonProps?: any;
  /** Custom props for the move button */
  moveButtonProps?: any;
  /** Callback when items are reordered */
  onReorder?: (items: any[]) => void;
  /** Callback when an item is added */
  onAdd?: (item: any) => void;
  /** Callback when an item is removed */
  onRemove?: (item: any, index: number) => void;
  /** Whether the field array is disabled */
  disabled?: boolean;
  /** Whether the field array is read-only */
  readOnly?: boolean;
  /** Custom render function for the header */
  renderHeader?: (methods: any) => React.ReactNode;
  /** Custom render function for the empty state */
  renderEmptyState?: () => React.ReactNode;
  /** Whether to show a divider between items */
  divider?: boolean;
  /** Props for the divider */
  dividerProps?: any;
  /** Whether to show item numbers */
  showItemNumbers?: boolean;
  /** Custom render function for item numbers */
  renderItemNumber?: (index: number) => React.ReactNode;
  /** Additional content to render after the items */
  footer?: React.ReactNode;
}

/**
 * A component for managing arrays of form fields with add/remove/reorder functionality
 */
const FormFieldArray: React.FC<FormFieldArrayProps> = ({
  name,
  title,
  description,
  renderItem,
  defaultItem = {},
  showAddButton = true,
  addButtonText = 'Add Item',
  showRemoveButton = true,
  showMoveButtons = true,
  minItems = 0,
  maxItems = Infinity,
  required = false,
  error,
  sx = {},
  containerSx = {},
  headerSx = {},
  itemsContainerSx = {},
  itemSx = {},
  itemActionsSx = {},
  addButtonSx = {},
  removeButtonSx = {},
  moveButtonSx = {},
  addButtonProps = {},
  removeButtonProps = {},
  moveButtonProps = {},
  onReorder,
  onAdd,
  onRemove,
  disabled = false,
  readOnly = false,
  renderHeader,
  renderEmptyState,
  divider = true,
  dividerProps = {},
  showItemNumbers = false,
  renderItemNumber,
  footer,
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();
  const { fields, append, remove, move } = useFieldArray({
    control,
    name,
    rules: {
      required: required ? 'At least one item is required' : false,
      minLength:
        minItems > 0
          ? {
              value: minItems,
              message: `At least ${minItems} ${minItems === 1 ? 'item' : 'items'} are required`,
            }
          : undefined,
      maxLength:
        maxItems < Infinity
          ? {
              value: maxItems,
              message: `Maximum ${maxItems} ${maxItems === 1 ? 'item' : 'items'} allowed`,
            }
          : undefined,
    },
  });

  const fieldError = errors?.[name];
  const displayError = error || fieldError?.message;

  // Handle adding a new item
  const handleAdd = useCallback(() => {
    const newItem = typeof defaultItem === 'function' ? defaultItem() : { ...defaultItem };
    append(newItem);
    onAdd?.(newItem);
  }, [append, defaultItem, onAdd]);

  // Handle removing an item
  const handleRemove = useCallback(
    (index: number) => {
      const removedItem = fields[index];
      remove(index);
      onRemove?.(removedItem, index);
    },
    [fields, onRemove, remove]
  );

  // Handle drag and drop reordering
  const handleDragEnd = useCallback(
    (result: DropResult) => {
      if (!result.destination) return;

      const fromIndex = result.source.index;
      const toIndex = result.destination.index;

      if (fromIndex === toIndex) return;

      move(fromIndex, toIndex);

      if (onReorder) {
        const newItems = [...fields];
        const [movedItem] = newItems.splice(fromIndex, 1);
        newItems.splice(toIndex, 0, movedItem);
        onReorder(newItems);
      }
    },
    [fields, move, onReorder]
  );

  // Render the default empty state
  const renderDefaultEmptyState = () => (
    <Box
      sx={{
        p: 3,
        textAlign: 'center',
        color: 'text.secondary',
        border: '1px dashed',
        borderColor: 'divider',
        borderRadius: 1,
      }}
    >
      <Typography variant="body2">No items added yet</Typography>
    </Box>
  );

  // Render the default header
  const renderDefaultHeader = () => (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 2,
        ...headerSx,
      }}
    >
      <Box>
        {title && (
          <Typography variant="subtitle1" fontWeight={500}>
            {title}
            {required && <span style={{ color: 'red' }}> *</span>}
          </Typography>
        )}
        {description && (
          <Typography variant="body2" color="text.secondary">
            {description}
          </Typography>
        )}
      </Box>
      {showAddButton && fields.length < maxItems && (
        <Button
          variant="outlined"
          size="small"
          startIcon={<AddIcon />}
          onClick={handleAdd}
          disabled={disabled || readOnly || fields.length >= maxItems}
          sx={{
            minWidth: 'auto',
            ...addButtonSx,
          }}
          {...addButtonProps}
        >
          {addButtonText}
        </Button>
      )}
    </Box>
  );

  // Render item actions (remove, move buttons)
  const renderItemActions = (index: number) => (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 1,
        ...itemActionsSx,
      }}
    >
      {showMoveButtons && fields.length > 1 && (
        <Tooltip title="Drag to reorder">
          <span>
            <IconButton
              size="small"
              disabled={disabled || readOnly}
              sx={{
                cursor: 'grab',
                '&:active': { cursor: 'grabbing' },
                ...moveButtonSx,
              }}
              {...moveButtonProps}
            >
              <DragHandleIcon />
            </IconButton>
          </span>
        </Tooltip>
      )}
      {showRemoveButton && fields.length > minItems && (
        <Tooltip title="Remove">
          <span>
            <IconButton
              size="small"
              onClick={() => handleRemove(index)}
              disabled={disabled || readOnly || fields.length <= minItems}
              color="error"
              sx={removeButtonSx}
              {...removeButtonProps}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </span>
        </Tooltip>
      )}
    </Box>
  );

  // Render item number
  const renderItemNumber = (index: number) => {
    if (!showItemNumbers) return null;

    return (
      <Typography
        variant="body2"
        color="text.secondary"
        sx={{
          minWidth: 24,
          textAlign: 'center',
          mr: 1,
        }}
      >
        {renderItemNumber ? renderItemNumber(index) : `${index + 1}.`}
      </Typography>
    );
  };

  return (
    <Box sx={{ width: '100%', ...sx }}>
      {renderHeader ? renderHeader({ fields, append, remove, move }) : renderDefaultHeader()}

      {displayError && (
        <Typography color="error" variant="body2" sx={{ mb: 1 }}>
          {displayError}
        </Typography>
      )}

      {fields.length === 0 && !readOnly ? (
        renderEmptyState ? (
          renderEmptyState()
        ) : (
          renderDefaultEmptyState()
        )
      ) : (
        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId={name}>
            {(provided) => (
              <Box
                ref={provided.innerRef}
                {...provided.droppableProps}
                sx={{
                  '& > *:not(:last-child)': {
                    mb: 2,
                  },
                  ...itemsContainerSx,
                }}
              >
                {fields.map((field, index) => (
                  <Draggable
                    key={field.id}
                    draggableId={field.id}
                    index={index}
                    isDragDisabled={disabled || readOnly || fields.length <= 1}
                  >
                    {(provided) => (
                      <Box
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        sx={{
                          p: 2,
                          border: '1px solid',
                          borderColor: 'divider',
                          borderRadius: 1,
                          backgroundColor: 'background.paper',
                          display: 'flex',
                          flexDirection: 'column',
                          gap: 2,
                          ...itemSx,
                        }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'flex-start',
                            gap: 2,
                          }}
                        >
                          {renderItemNumber(index)}
                          <Box sx={{ flex: 1 }}>{renderItem(index, { control, field, index })}</Box>
                          {!readOnly && renderItemActions(index)}
                        </Box>
                        {divider && index < fields.length - 1 && <Divider {...dividerProps} />}
                      </Box>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </Box>
            )}
          </Droppable>
        </DragDropContext>
      )}

      {footer}

      {showAddButton && fields.length < maxItems && !readOnly && (
        <Box sx={{ mt: 2, textAlign: 'left' }}>
          <Button
            variant="outlined"
            size="small"
            startIcon={<AddIcon />}
            onClick={handleAdd}
            disabled={disabled || readOnly || fields.length >= maxItems}
            sx={{
              minWidth: 'auto',
              ...addButtonSx,
            }}
            {...addButtonProps}
          >
            {addButtonText}
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default FormFieldArray;
