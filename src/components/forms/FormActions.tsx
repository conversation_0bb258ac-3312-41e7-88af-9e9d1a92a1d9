import React, { ReactNode } from 'react';
import { Box, Button, ButtonProps, SxProps, Theme, Typography } from '@mui/material';
import { LoadingButton } from '@mui/lab';

interface ActionButtonProps extends ButtonProps {
  /** Button text */
  label: string;
  /** Show loading state */
  loading?: boolean;
  /** Custom button component */
  component?: React.ElementType;
  /** Custom styles */
  sx?: SxProps<Theme>;
}

interface FormActionsProps {
  /** Whether to show the back button */
  showBack?: boolean;
  /** Back button props */
  backButtonProps?: ActionButtonProps;
  /** Back button text */
  backButtonText?: string;
  /** Whether to show the next button */
  showNext?: boolean;
  /** Next button props */
  nextButtonProps?: ActionButtonProps;
  /** Next button text */
  nextButtonText?: string;
  /** Whether to show the submit button */
  showSubmit?: boolean;
  /** Submit button props */
  submitButtonProps?: ActionButtonProps;
  /** Submit button text */
  submitButtonText?: string;
  /** Whether to show the reset button */
  showReset?: boolean;
  /** Reset button props */
  resetButtonProps?: ActionButtonProps;
  /** Reset button text */
  resetButtonText?: string;
  /** Whether the form is in a loading state */
  isLoading?: boolean;
  /** Whether the form is in a submitting state */
  isSubmitting?: boolean;
  /** Whether the form is valid */
  isValid?: boolean;
  /** Custom action buttons */
  actions?: ReactNode;
  /** Custom container styles */
  containerSx?: SxProps<Theme>;
  /** Custom button container styles */
  buttonsContainerSx?: SxProps<Theme>;
  /** Error message to display */
  error?: string | null;
  /** Success message to display */
  success?: string | null;
  /** Info message to display */
  info?: string | null;
  /** Warning message to display */
  warning?: string | null;
  /** Additional content to render */
  children?: ReactNode;
  /** Alignment of buttons */
  align?: 'left' | 'center' | 'right' | 'space-between' | 'space-around';
  /** Direction of buttons */
  direction?: 'row' | 'column';
  /** Gap between buttons */
  gap?: number | string;
  /** Whether to show loading state on all buttons when form is submitting */
  disableAllWhenSubmitting?: boolean;
}

const FormActions: React.FC<FormActionsProps> = ({
  showBack = true,
  backButtonProps = {},
  backButtonText = 'Back',
  showNext = true,
  nextButtonProps = {},
  nextButtonText = 'Next',
  showSubmit = true,
  submitButtonProps = {},
  submitButtonText = 'Submit',
  showReset = false,
  resetButtonProps = {},
  resetButtonText = 'Reset',
  isLoading = false,
  isSubmitting = false,
  isValid = true,
  actions,
  containerSx = {},
  buttonsContainerSx = {},
  error = null,
  success = null,
  info = null,
  warning = null,
  children,
  align = 'right',
  direction = 'row',
  gap = 2,
  disableAllWhenSubmitting = false,
}) => {
  // Helper function to render a button
  const renderButton = ({
    label,
    loading = false,
    component: ButtonComponent = Button,
    ...buttonProps
  }: ActionButtonProps) => {
    const isDisabled = buttonProps.disabled || (disableAllWhenSubmitting && isSubmitting);

    return (
      <LoadingButton
        component={ButtonComponent as any}
        loading={loading || (disableAllWhenSubmitting && isSubmitting)}
        disabled={isDisabled}
        {...buttonProps}
        sx={{
          minWidth: 120,
          ...buttonProps.sx,
        }}
      >
        {label}
      </LoadingButton>
    );
  };

  // Get alignment styles based on align prop
  const getAlignmentStyles = () => {
    switch (align) {
      case 'left':
        return { justifyContent: 'flex-start' };
      case 'center':
        return { justifyContent: 'center' };
      case 'right':
        return { justifyContent: 'flex-end' };
      case 'space-between':
        return { justifyContent: 'space-between' };
      case 'space-around':
        return { justifyContent: 'space-around' };
      default:
        return { justifyContent: 'flex-end' };
    }
  };

  // Render status message if any
  const renderStatusMessage = () => {
    const message = error || success || info || warning;
    const color = error ? 'error' : success ? 'success' : info ? 'info' : 'warning';

    if (!message) return null;

    return (
      <Box sx={{ mb: 2, width: '100%' }}>
        <Typography
          variant="body2"
          color={color}
          sx={{
            p: 1.5,
            borderRadius: 1,
            backgroundColor: `${color}.light`,
            borderLeft: `4px solid`,
            borderColor: `${color}.main`,
          }}
        >
          {message}
        </Typography>
      </Box>
    );
  };

  return (
    <Box
      sx={{
        mt: 4,
        width: '100%',
        ...containerSx,
      }}
    >
      {renderStatusMessage()}

      <Box
        sx={{
          display: 'flex',
          flexDirection: direction,
          alignItems: 'center',
          flexWrap: 'wrap',
          gap,
          ...getAlignmentStyles(),
          ...buttonsContainerSx,
        }}
      >
        {showBack && (
          <Button
            variant="outlined"
            color="inherit"
            disabled={isLoading || isSubmitting}
            {...backButtonProps}
            label={backButtonText}
          />
        )}

        {showReset && (
          <Button
            variant="outlined"
            color="error"
            type="button"
            disabled={isLoading || isSubmitting}
            {...resetButtonProps}
            label={resetButtonText}
          />
        )}

        {showNext && !showSubmit && (
          <Button
            variant="contained"
            color="primary"
            type="submit"
            loading={isLoading || isSubmitting}
            disabled={!isValid}
            {...nextButtonProps}
            label={nextButtonText}
          />
        )}

        {showSubmit && (
          <Button
            variant="contained"
            color="primary"
            type="submit"
            loading={isLoading || isSubmitting}
            disabled={!isValid}
            {...submitButtonProps}
            label={submitButtonText}
          />
        )}

        {actions}
      </Box>

      {children}
    </Box>
  );
};

// Helper Button component that uses the renderButton function
const Button: React.FC<ActionButtonProps> = (props) => renderButton(props);

export default FormActions;
