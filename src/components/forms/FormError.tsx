import React from 'react';
import { <PERSON><PERSON>, Al<PERSON>Title, <PERSON>, SxProps, Theme, Typography } from '@mui/material';
import { ErrorOutline, WarningAmber, InfoOutlined, CheckCircleOutline } from '@mui/icons-material';

type Severity = 'error' | 'warning' | 'info' | 'success';

interface FormErrorProps {
  /** Error message to display */
  message?: string | string[] | null;
  /** Title of the error message */
  title?: string;
  /** Severity of the message */
  severity?: Severity;
  /** Whether to show the icon */
  showIcon?: boolean;
  /** Whether the message can be dismissed */
  dismissible?: boolean;
  /** Callback when the message is dismissed */
  onDismiss?: () => void;
  /** Custom styles */
  sx?: SxProps<Theme>;
  /** Custom styles for the title */
  titleSx?: SxProps<Theme>;
  /** Custom styles for the message */
  messageSx?: SxProps<Theme>;
  /** Custom icon */
  icon?: React.ReactNode;
  /** Additional content to display below the message */
  children?: React.ReactNode;
  /** Whether to show a border */
  outlined?: boolean;
  /** Whether to show a filled background */
  filled?: boolean;
  /** Elevation level */
  elevation?: number;
  /** Border radius */
  borderRadius?: number | string;
  /** Custom component to render the message */
  component?: React.ElementType;
}

const severityIcons = {
  error: <ErrorOutline fontSize="inherit" />,
  warning: <WarningAmber fontSize="inherit" />,
  info: <InfoOutlined fontSize="inherit" />,
  success: <CheckCircleOutline fontSize="inherit" />,
};

const defaultTitles = {
  error: 'Error',
  warning: 'Warning',
  info: 'Info',
  success: 'Success',
};

/**
 * A component to display form errors, warnings, info, or success messages
 */
const FormError: React.FC<FormErrorProps> = ({
  message,
  title,
  severity = 'error',
  showIcon = true,
  dismissible = false,
  onDismiss,
  sx = {},
  titleSx = {},
  messageSx = {},
  icon,
  children,
  outlined = false,
  filled = false,
  elevation = 0,
  borderRadius = 1,
  component: Component = 'div',
  ...rest
}) => {
  // Don't render if there's no message
  if (!message || (Array.isArray(message) && message.length === 0)) {
    return null;
  }

  // Convert single message to array for consistent handling
  const messages = Array.isArray(message) ? message : [message];
  const displayTitle = title || defaultTitles[severity];
  const displayIcon = icon !== undefined ? icon : severityIcons[severity];

  // Handle dismiss
  const handleDismiss = () => {
    if (onDismiss) {
      onDismiss();
    }
  };

  // Render message content
  const renderMessage = (msg: string, index: number) => (
    <Typography
      key={index}
      variant="body2"
      component="div"
      sx={{
        '&:not(:last-child)': { mb: 1 },
        ...messageSx,
      }}
    >
      {msg}
    </Typography>
  );

  return (
    <Alert
      component={Component}
      severity={severity}
      icon={showIcon ? displayIcon : false}
      onClose={dismissible ? handleDismiss : undefined}
      variant={outlined ? 'outlined' : filled ? 'filled' : 'standard'}
      elevation={elevation}
      sx={{
        borderRadius,
        alignItems: 'flex-start',
        '& .MuiAlert-message': { width: '100%' },
        ...sx,
      }}
      {...rest}
    >
      {displayTitle && (
        <AlertTitle
          sx={{
            mb: messages.length > 0 ? 1 : 0,
            ...titleSx,
          }}
        >
          {displayTitle}
        </AlertTitle>
      )}

      <Box>
        {messages.map((msg, index) => renderMessage(msg, index))}
        {children}
      </Box>
    </Alert>
  );
};

export default FormError;
