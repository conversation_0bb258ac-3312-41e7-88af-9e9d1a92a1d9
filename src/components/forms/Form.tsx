import React, { ReactNode, FormEvent } from 'react';
import { Box, Button, CircularProgress, Typography } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { FormProvider, useForm, SubmitHandler, UseFormReturn } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z, ZodTypeAny } from 'zod';

type FormProps<T extends Record<string, any>> = {
  /** Form schema for validation */
  schema: ZodTypeAny;
  /** Default form values */
  defaultValues?: Partial<T>;
  /** Form submission handler */
  onSubmit: SubmitHandler<T>;
  /** Form children */
  children: (methods: UseFormReturn<T>) => ReactNode;
  /** Submit button text */
  submitText?: string;
  /** Show loading state */
  isLoading?: boolean;
  /** Form title */
  title?: string;
  /** Form description */
  description?: string;
  /** Custom submit button */
  submitButton?: ReactNode;
  /** Custom footer content */
  footerContent?: ReactNode;
  /** Custom class name */
  className?: string;
  /** Disable form submission */
  disabled?: boolean;
  /** Reset form after successful submission */
  resetOnSubmit?: boolean;
};

/**
 * A reusable form component with validation and submission handling
 */
const Form = <T extends Record<string, any>>({
  schema,
  defaultValues,
  onSubmit,
  children,
  submitText = 'Submit',
  isLoading = false,
  title,
  description,
  submitButton,
  footerContent,
  className = '',
  disabled = false,
  resetOnSubmit = false,
}: FormProps<T>) => {
  const theme = useTheme();

  const methods = useForm<T>({
    resolver: zodResolver(schema),
    defaultValues: defaultValues as any,
    mode: 'onChange',
  });

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting, isValid, isSubmitted, errors },
  } = methods;

  const onSubmitHandler = async (data: T) => {
    try {
      await onSubmit(data);
      if (resetOnSubmit) {
        reset();
      }
    } catch (error) {
      // Error handling is done by the parent component
      console.error('Form submission error:', error);
    }
  };

  const renderSubmitButton = () => {
    if (submitButton) return submitButton;

    return (
      <Button
        type="submit"
        variant="contained"
        color="primary"
        disabled={disabled || isSubmitting || isLoading || (isSubmitted && !isValid)}
        fullWidth
        sx={{
          mt: 2,
          py: 1.5,
          fontSize: '1rem',
          fontWeight: 600,
          textTransform: 'none',
          borderRadius: 2,
          boxShadow: 'none',
          '&:hover': {
            boxShadow: 'none',
          },
        }}
      >
        {isSubmitting || isLoading ? <CircularProgress size={24} color="inherit" /> : submitText}
      </Button>
    );
  };

  return (
    <FormProvider {...methods}>
      <Box
        component="form"
        onSubmit={handleSubmit(onSubmitHandler) as any}
        className={`form ${className}`}
        noValidate
        sx={{
          width: '100%',
          maxWidth: '100%',
          '& .MuiFormHelperText-root': {
            mt: 0.5,
            ml: 1.5,
            fontSize: '0.75rem',
          },
        }}
      >
        {title && (
          <Typography
            variant="h5"
            component="h2"
            gutterBottom
            sx={{
              fontWeight: 600,
              color: 'text.primary',
              mb: description ? 0.5 : 3,
            }}
          >
            {title}
          </Typography>
        )}

        {description && (
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            {description}
          </Typography>
        )}

        <Box sx={{ mb: 3 }}>{children(methods)}</Box>

        {renderSubmitButton()}

        {footerContent && <Box sx={{ mt: 3, textAlign: 'center' }}>{footerContent}</Box>}
      </Box>
    </FormProvider>
  );
};

export default Form;
