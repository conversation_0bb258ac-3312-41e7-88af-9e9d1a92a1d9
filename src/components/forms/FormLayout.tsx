import React, { ReactNode } from 'react';
import { Box, BoxProps, SxProps, Theme, Typography, useTheme } from '@mui/material';

interface FormLayoutProps extends BoxProps {
  /** Form title */
  title?: string;
  /** Form description or subtitle */
  description?: string;
  /** Form content */
  children: ReactNode;
  /** Actions to display at the bottom of the form */
  actions?: ReactNode;
  /** Maximum width of the form */
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | false | string | number;
  /** Whether to show a card-like appearance */
  card?: boolean;
  /** Whether the form is loading */
  loading?: boolean;
  /** Custom styles */
  sx?: SxProps<Theme>;
  /** Custom styles for the header */
  headerSx?: SxProps<Theme>;
  /** Custom styles for the content */
  contentSx?: SxProps<Theme>;
  /** Custom styles for the actions */
  actionsSx?: SxProps<Theme>;
  /** Custom component for the form */
  component?: React.ElementType;
  /** Whether to center the form */
  center?: boolean;
  /** Whether to show a border */
  border?: boolean;
  /** Border radius */
  borderRadius?: number | string;
  /** Elevation level */
  elevation?: number;
  /** Padding */
  padding?: number | string;
  /** Margin */
  margin?: number | string;
  /** Background color */
  backgroundColor?: string;
  /** Whether to show a loading overlay */
  showLoadingOverlay?: boolean;
  /** Loading overlay opacity */
  loadingOverlayOpacity?: number;
  /** Loading indicator */
  loadingIndicator?: ReactNode;
  /** Custom footer content */
  footer?: ReactNode;
  /** Whether to show a divider between sections */
  divider?: boolean;
  /** Custom divider component */
  dividerComponent?: ReactNode;
}

/**
 * A layout component for forms that provides consistent styling and structure
 */
const FormLayout: React.FC<FormLayoutProps> = ({
  title,
  description,
  children,
  actions,
  maxWidth = 'md',
  card = true,
  loading = false,
  sx = {},
  headerSx = {},
  contentSx = {},
  actionsSx = {},
  component: Component = 'form',
  center = true,
  border = true,
  borderRadius = 1,
  elevation = 0,
  padding = 3,
  margin,
  backgroundColor,
  showLoadingOverlay = true,
  loadingOverlayOpacity = 0.7,
  loadingIndicator,
  footer,
  divider = true,
  dividerComponent,
  ...rest
}) => {
  const theme = useTheme();

  // Calculate max width
  const getMaxWidth = () => {
    if (maxWidth === false) return 'none';
    if (typeof maxWidth === 'string' && !['xs', 'sm', 'md', 'lg', 'xl'].includes(maxWidth)) {
      return maxWidth;
    }

    switch (maxWidth) {
      case 'xs':
        return 400;
      case 'sm':
        return 600;
      case 'md':
        return 900;
      case 'lg':
        return 1200;
      case 'xl':
        return 1536;
      default:
        return maxWidth;
    }
  };

  return (
    <Box
      component={Component}
      sx={{
        width: '100%',
        maxWidth: getMaxWidth(),
        margin: margin || (center ? '0 auto' : 0),
        ...(card && {
          backgroundColor: backgroundColor || theme.palette.background.paper,
          borderRadius: theme.shape.borderRadius * borderRadius,
          boxShadow: theme.shadows[elevation],
          border: border ? `1px solid ${theme.palette.divider}` : 'none',
          overflow: 'hidden',
        }),
        position: 'relative',
        ...sx,
      }}
      {...rest}
    >
      {/* Loading overlay */}
      {loading && showLoadingOverlay && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: `rgba(255, 255, 255, ${loadingOverlayOpacity})`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: theme.zIndex.modal,
            backdropFilter: 'blur(2px)',
          }}
        >
          {loadingIndicator || (
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                Loading...
              </Typography>
              <Box sx={{ width: 50, height: 50, mx: 'auto' }}>
                {/* You can replace this with a proper loading spinner */}
                <Box
                  sx={{
                    width: '100%',
                    height: '100%',
                    border: `3px solid ${theme.palette.primary.main}20`,
                    borderTopColor: theme.palette.primary.main,
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite',
                    '@keyframes spin': {
                      '0%': { transform: 'rotate(0deg)' },
                      '100%': { transform: 'rotate(360deg)' },
                    },
                  }}
                />
              </Box>
            </Box>
          )}
        </Box>
      )}

      {/* Header */}
      {(title || description) && (
        <Box
          sx={{
            p: padding,
            pb: 2,
            borderBottom: divider ? `1px solid ${theme.palette.divider}` : 'none',
            ...headerSx,
          }}
        >
          {title && (
            <Typography
              variant="h5"
              component="h2"
              sx={{
                fontWeight: 600,
                color: 'text.primary',
                mb: description ? 1 : 0,
              }}
            >
              {title}
            </Typography>
          )}
          {description && (
            <Typography variant="body2" color="text.secondary">
              {description}
            </Typography>
          )}
        </Box>
      )}

      {/* Content */}
      <Box
        sx={{
          p: padding,
          ...(divider && title && { pt: 0 }),
          ...contentSx,
        }}
      >
        {children}
      </Box>

      {/* Divider */}
      {divider && (dividerComponent || <Divider sx={{ my: 1 }} />)}

      {/* Actions */}
      {actions && (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'flex-end',
            gap: 2,
            p: padding,
            pt: 2,
            ...actionsSx,
          }}
        >
          {actions}
        </Box>
      )}

      {/* Footer */}
      {footer && (
        <Box
          sx={{
            p: padding,
            pt: 1,
            borderTop: divider ? `1px solid ${theme.palette.divider}` : 'none',
            backgroundColor: theme.palette.background.default,
          }}
        >
          {footer}
        </Box>
      )}
    </Box>
  );
};

export default FormLayout;
