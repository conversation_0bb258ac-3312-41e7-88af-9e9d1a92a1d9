import React, { ReactNode, FormEvent } from 'react';
import { Box, BoxProps } from '@mui/material';
import { useFormContext } from '../../contexts/FormContext';

interface FormContainerProps extends BoxProps {
  children: ReactNode;
  onSubmit?: (e: FormEvent) => Promise<void> | void;
  autoComplete?: string;
  noValidate?: boolean;
  className?: string;
}

/**
 * A container component that provides form context and handles form submission
 */
export const FormContainer: React.FC<FormContainerProps> = ({
  children,
  onSubmit,
  autoComplete = 'on',
  noValidate = true,
  className = '',
  ...rest
}) => {
  const { formMethods, submitForm } = useFormContext();
  const { handleSubmit } = formMethods;

  const handleFormSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (onSubmit) {
      await onSubmit(e);
    } else {
      await handleSubmit(async (data) => {
        await submitForm();
      })(e);
    }
  };

  return (
    <Box
      component="form"
      onSubmit={handleFormSubmit}
      autoComplete={autoComplete}
      noValidate={noValidate}
      className={`form-container ${className}`}
      {...rest}
    >
      {children}
    </Box>
  );
};

export default FormContainer;
