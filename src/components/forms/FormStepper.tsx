import React, { ReactNode } from 'react';
import {
  Box,
  Stepper,
  Step,
  StepLabel,
  StepConnector,
  StepIconProps,
  Typography,
  SxProps,
  Theme,
} from '@mui/material';
import { styled } from '@mui/material/styles';

// Custom step icon component
const StepIconRoot = styled('div')<{ ownerState: { completed?: boolean; active?: boolean } }>(
  ({ theme, ownerState }) => ({
    color: theme.palette.mode === 'dark' ? theme.palette.grey[700] : theme.palette.grey[300],
    display: 'flex',
    height: 22,
    alignItems: 'center',
    ...(ownerState.active && {
      color: theme.palette.primary.main,
    }),
    '& .StepIcon-completedIcon': {
      color: theme.palette.primary.main,
      zIndex: 1,
      fontSize: 18,
    },
    '& .StepIcon-circle': {
      width: 8,
      height: 8,
      borderRadius: '50%',
      backgroundColor: 'currentColor',
    },
  })
);

const StepIcon = (props: StepIconProps) => {
  const { active, completed, className, icon } = props;

  return (
    <StepIconRoot ownerState={{ completed, active }} className={className}>
      {completed ? (
        <div className="StepIcon-completedIcon">✓</div>
      ) : (
        <div className="StepIcon-circle" />
      )}
    </StepIconRoot>
  );
};

// Custom connector for steps
const ColorlibConnector = styled(StepConnector)(({ theme }) => ({
  '& .MuiStepConnector-line': {
    borderColor: theme.palette.mode === 'dark' ? theme.palette.grey[800] : theme.palette.grey[300],
    borderTopWidth: 1,
    minHeight: 20,
  },
  '&.Mui-active, &.Mui-completed': {
    '& .MuiStepConnector-line': {
      borderColor: theme.palette.primary.main,
    },
  },
}));

interface StepItem {
  label: string;
  description?: string;
  optional?: boolean;
  error?: boolean;
  icon?: ReactNode;
}

interface FormStepperProps {
  /** Current active step (0-based) */
  activeStep: number;
  /** Array of step objects */
  steps: StepItem[];
  /** Callback when a step is clicked */
  onStepClick?: (step: number) => void;
  /** Custom styles */
  sx?: SxProps<Theme>;
  /** Custom styles for the stepper */
  stepperSx?: SxProps<Theme>;
  /** Custom styles for the step */
  stepSx?: SxProps<Theme>;
  /** Custom styles for the step label */
  stepLabelSx?: SxProps<Theme>;
  /** Custom styles for the step description */
  stepDescriptionSx?: SxProps<Theme>;
  /** Show step numbers */
  showStepNumbers?: boolean;
  /** Show step descriptions */
  showStepDescriptions?: boolean;
  /** Allow clicking on steps to navigate */
  clickableSteps?: boolean;
  /** Custom connector component */
  connector?: ReactNode;
  /** Custom step icon component */
  stepIconComponent?: React.ComponentType<StepIconProps>;
  /** Orientation of the stepper */
  orientation?: 'horizontal' | 'vertical';
  /** Alternative label for horizontal stepper */
  alternativeLabel?: boolean;
}

/**
 * A customizable stepper component for multi-step forms
 */
const FormStepper: React.FC<FormStepperProps> = ({
  activeStep,
  steps,
  onStepClick,
  sx = {},
  stepperSx = {},
  stepSx = {},
  stepLabelSx = {},
  stepDescriptionSx = {},
  showStepNumbers = true,
  showStepDescriptions = true,
  clickableSteps = true,
  connector,
  stepIconComponent: StepIconComponent = StepIcon,
  orientation = 'horizontal',
  alternativeLabel = true,
}) => {
  const handleStepClick = (step: number) => {
    if (clickableSteps && onStepClick) {
      onStepClick(step);
    }
  };

  return (
    <Box sx={{ width: '100%', ...sx }}>
      <Stepper
        activeStep={activeStep}
        orientation={orientation}
        alternativeLabel={orientation === 'horizontal' ? alternativeLabel : undefined}
        connector={connector || <ColorlibConnector />}
        sx={{
          '& .MuiStepLabel-iconContainer': {
            p: 0,
          },
          ...stepperSx,
        }}
      >
        {steps.map((step, index) => {
          const isCompleted = index < activeStep;
          const isActive = index === activeStep;
          const isError = step.error;

          return (
            <Step
              key={step.label}
              completed={isCompleted}
              active={isActive}
              onClick={() => handleStepClick(index)}
              sx={{
                cursor: clickableSteps ? 'pointer' : 'default',
                '& .MuiStepLabel-label': {
                  color: isError ? 'error.main' : 'text.primary',
                  '&.Mui-active, &.Mui-completed': {
                    color: isError ? 'error.main' : 'primary.main',
                  },
                },
                ...stepSx,
              }}
            >
              <StepLabel
                StepIconComponent={StepIconComponent}
                optional={
                  showStepDescriptions && step.description ? (
                    <Typography
                      variant="caption"
                      color={isError ? 'error' : 'text.secondary'}
                      sx={stepDescriptionSx}
                    >
                      {step.description}
                    </Typography>
                  ) : null
                }
                error={isError}
                sx={{
                  '& .MuiStepLabel-label': {
                    mt: 0.5,
                    ...stepLabelSx,
                  },
                }}
              >
                {showStepNumbers ? `${index + 1}. ${step.label}` : step.label}
              </StepLabel>
            </Step>
          );
        })}
      </Stepper>
    </Box>
  );
};

export default FormStepper;
