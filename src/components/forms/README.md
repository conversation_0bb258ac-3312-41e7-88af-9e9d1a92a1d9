# Form Components

This directory contains reusable form components built with React Hook Form and Material-UI, designed to work seamlessly with the form context.

## Components

### FormContainer

A wrapper component that provides form context and handles form submission.

**Props:**

- `onSubmit`: Function to handle form submission
- `autoComplete`: HTML form autocomplete attribute (default: 'on')
- `noValidate`: Disable HTML5 validation (default: true)
- `className`: Additional CSS class names

### FormStep

A container for form steps in a multi-step form.

**Props:**

- `step`: The step number (0-based index)
- `validationFields`: Array of field names to validate when the step becomes active
- `onStepChange`: Callback when the step changes

### FormNavigation

Navigation controls for multi-step forms.

**Props:**

- `nextButtonText`: Text for the next button (default: 'Next')
- `backButtonText`: Text for the back button (default: 'Back')
- `submitButtonText`: Text for the submit button (default: 'Submit')
- `showBackButton`: Show/hide back button (default: true)
- `showNextButton`: Show/hide next button (default: true)
- `showSubmitButton`: Show/hide submit button (default: true)
- `onBack`: Custom back button handler
- `onNext`: Custom next button handler
- `nextButtonProps`: Additional props for the next button
- `backButtonProps`: Additional props for the back button
- `submitButtonProps`: Additional props for the submit button
- `containerProps`: Additional props for the container
- `showProgress`: Show/hide step progress (default: true)

## Usage Example

```tsx
import { FormProvider, FormContainer, FormStep, FormNavigation } from './components/forms';

const MyForm = () => {
  const handleSubmit = async (values) => {
    console.log('Form submitted:', values);
    // Handle form submission
  };

  return (
    <FormProvider
      defaultValues={initialValues}
      validationSchema={validationSchema}
      totalSteps={3}
      onSubmit={handleSubmit}
    >
      <FormContainer>
        <FormStep step={0} validationFields={['field1', 'field2']}>
          {/* Form fields for step 1 */}
          <FormNavigation />
        </FormStep>

        <FormStep step={1} validationFields={['field3', 'field4']}>
          {/* Form fields for step 2 */}
          <FormNavigation />
        </FormStep>

        <FormStep step={2}>
          {/* Review and submit */}
          <FormNavigation submitButtonText="Submit Form" />
        </FormStep>
      </FormContainer>
    </FormProvider>
  );
};
```

## Form Context

The form context provides access to form state and methods:

```tsx
import { useFormContext } from './contexts/FormContext';

const MyComponent = () => {
  const {
    // State
    values,
    errors,
    isSubmitting,
    activeStep,
    totalSteps,
    isFirstStep,
    isLastStep,

    // Methods
    setFieldValue,
    setValues,
    setErrors,
    nextStep,
    prevStep,
    goToStep,
    submitForm,

    // React Hook Form methods
    formMethods,
  } = useFormContext();

  // ...
};
```

## Validation

Use Yup for form validation. Define a validation schema and pass it to the FormProvider:

```tsx
import * as yup from 'yup';

const validationSchema = yup.object().shape({
  firstName: yup.string().required('First name is required'),
  email: yup.string().email('Invalid email').required('Email is required'),
  // ...
});

// In your component:
<FormProvider validationSchema={validationSchema}>{/* ... */}</FormProvider>;
```

## Styling

All components use Material-UI's `sx` prop for styling. You can override styles by passing your own styles to the `sx` prop of any component.

## Accessibility

Components include proper ARIA attributes and keyboard navigation. Make sure to provide appropriate labels and error messages for screen readers.

## Testing

When testing components that use the form context, wrap them in a `FormProvider` with the required props:

```tsx
test('should render form', () => {
  render(
    <FormProvider defaultValues={{}}>
      <MyFormComponent />
    </FormProvider>
  );

  // Your test assertions
});
```
