import React, { ReactNode, useMemo } from 'react';
import { Box, BoxProps, FormHelperText, SxProps, Theme, Typography } from '@mui/material';

interface FormFieldWrapperProps extends BoxProps {
  /** Field name for associating with form state */
  name?: string;
  /** Field label */
  label?: string;
  /** Field description or helper text */
  description?: string;
  /** Whether the field is required */
  required?: boolean;
  /** Whether the field is disabled */
  disabled?: boolean;
  /** Whether the field is read-only */
  readOnly?: boolean;
  /** Error message or boolean indicating error state */
  error?: string | boolean | null;
  /** Whether the field has been touched */
  touched?: boolean;
  /** Whether to show the error message */
  showError?: boolean;
  /** Custom styles */
  sx?: SxProps<Theme>;
  /** Custom styles for the label */
  labelSx?: SxProps<Theme>;
  /** Custom styles for the description */
  descriptionSx?: SxProps<Theme>;
  /** Custom styles for the error message */
  errorSx?: SxProps<Theme>;
  /** Custom styles for the field container */
  fieldContainerSx?: SxProps<Theme>;
  /** Custom styles for the content */
  contentSx?: SxProps<Theme>;
  /** Whether to show the required asterisk */
  showRequiredAsterisk?: boolean;
  /** Custom required indicator */
  requiredIndicator?: ReactNode;
  /** Custom optional indicator */
  optionalIndicator?: ReactNode;
  /** Whether to show the optional indicator */
  showOptionalIndicator?: boolean;
  /** Custom label component */
  labelComponent?: React.ElementType;
  /** Custom description component */
  descriptionComponent?: React.ElementType;
  /** Custom error component */
  errorComponent?: React.ElementType;
  /** Whether to show character counter */
  showCharacterCounter?: boolean;
  /** Maximum length for character counter */
  maxLength?: number;
  /** Current value length for character counter */
  valueLength?: number;
  /** Whether to show the label in a tooltip */
  tooltipLabel?: boolean;
  /** Tooltip placement */
  tooltipPlacement?: 'top' | 'bottom' | 'left' | 'right';
  /** Whether to show a success state */
  success?: boolean;
  /** Success message */
  successMessage?: string;
  /** Custom styles for the success message */
  successSx?: SxProps<Theme>;
  /** Whether to show a warning state */
  warning?: boolean;
  /** Warning message */
  warningMessage?: string;
  /** Custom styles for the warning message */
  warningSx?: SxProps<Theme>;
  /** Whether to show an info state */
  info?: boolean;
  /** Info message */
  infoMessage?: string;
  /** Custom styles for the info message */
  infoSx?: SxProps<Theme>;
  /** Whether to show a loading state */
  loading?: boolean;
  /** Loading indicator */
  loadingIndicator?: ReactNode;
  /** Custom styles for the loading indicator */
  loadingSx?: SxProps<Theme>;
  /** Whether to show the loading indicator */
  showLoadingIndicator?: boolean;
  /** Whether to show a border */
  border?: boolean;
  /** Border color */
  borderColor?: string;
  /** Border radius */
  borderRadius?: number | string;
  /** Background color */
  backgroundColor?: string;
  /** Padding */
  padding?: number | string;
  /** Margin */
  margin?: number | string;
  /** Width */
  width?: number | string;
  /** Full width */
  fullWidth?: boolean;
  /** Children */
  children: ReactNode;
  /** Additional props */
  [key: string]: any;
}

/**
 * A wrapper component for form fields that provides consistent styling and behavior
 */
const FormFieldWrapper: React.FC<FormFieldWrapperProps> = ({
  name,
  label,
  description,
  required = false,
  disabled = false,
  readOnly = false,
  error: errorProp,
  touched,
  showError = true,
  sx = {},
  labelSx = {},
  descriptionSx = {},
  errorSx = {},
  fieldContainerSx = {},
  contentSx = {},
  showRequiredAsterisk = true,
  requiredIndicator = ' *',
  optionalIndicator = ' (optional)',
  showOptionalIndicator = false,
  labelComponent: LabelComponent = 'label',
  descriptionComponent: DescriptionComponent = 'div',
  errorComponent: ErrorComponent = FormHelperText,
  showCharacterCounter = false,
  maxLength,
  valueLength,
  tooltipLabel = false,
  tooltipPlacement = 'top',
  success = false,
  successMessage,
  successSx = {},
  warning = false,
  warningMessage,
  warningSx = {},
  info = false,
  infoMessage,
  infoSx = {},
  loading = false,
  loadingIndicator,
  loadingSx = {},
  showLoadingIndicator = true,
  border = false,
  borderColor,
  borderRadius = 1,
  backgroundColor,
  padding = 1,
  margin,
  width,
  fullWidth = false,
  children,
  ...rest
}) => {
  // Determine if we should show the error
  const showErrorState = useMemo(() => {
    if (typeof errorProp === 'boolean') return errorProp;
    return !!errorProp && showError && (touched === undefined || touched);
  }, [errorProp, showError, touched]);

  // Get the error message
  const errorMessage = useMemo(() => {
    if (typeof errorProp === 'string') return errorProp;
    return null;
  }, [errorProp]);

  // Render the label
  const renderLabel = () => {
    if (!label) return null;

    const labelText = (
      <>
        {label}
        {required && showRequiredAsterisk && requiredIndicator}
        {!required && showOptionalIndicator && optionalIndicator}
      </>
    );

    if (tooltipLabel) {
      // You can implement a tooltip here if needed
      return (
        <Typography
          component={LabelComponent}
          htmlFor={name}
          variant="body2"
          sx={{
            display: 'block',
            mb: 0.5,
            color: 'text.primary',
            fontWeight: 500,
            ...labelSx,
          }}
          {...rest}
        >
          {labelText}
        </Typography>
      );
    }

    return (
      <Typography
        component={LabelComponent}
        htmlFor={name}
        variant="body2"
        sx={{
          display: 'block',
          mb: 0.5,
          color: 'text.primary',
          fontWeight: 500,
          ...labelSx,
        }}
        {...rest}
      >
        {labelText}
      </Typography>
    );
  };

  // Render the description
  const renderDescription = () => {
    if (!description) return null;

    return (
      <Typography
        component={DescriptionComponent}
        variant="caption"
        sx={{
          display: 'block',
          mt: 0.5,
          color: 'text.secondary',
          ...descriptionSx,
        }}
      >
        {description}
      </Typography>
    );
  };

  // Render the error message
  const renderError = () => {
    if (!showErrorState || !errorMessage) return null;

    return (
      <ErrorComponent
        error
        sx={{
          mt: 0.5,
          color: 'error.main',
          ...errorSx,
        }}
      >
        {errorMessage}
      </ErrorComponent>
    );
  };

  // Render the character counter
  const renderCharacterCounter = () => {
    if (!showCharacterCounter || !maxLength) return null;

    const currentLength = valueLength || 0;
    const remaining = maxLength - currentLength;
    const isError = remaining < 0;

    return (
      <Typography
        variant="caption"
        sx={{
          display: 'block',
          textAlign: 'right',
          mt: 0.5,
          color: isError ? 'error.main' : 'text.secondary',
        }}
      >
        {remaining} {remaining === 1 ? 'character' : 'characters'} remaining
      </Typography>
    );
  };

  // Render the loading indicator
  const renderLoadingIndicator = () => {
    if (!loading || !showLoadingIndicator) return null;

    return (
      <Box
        sx={{
          position: 'absolute',
          top: '50%',
          right: 8,
          transform: 'translateY(-50%)',
          ...loadingSx,
        }}
      >
        {loadingIndicator || (
          <Box
            sx={{
              width: 16,
              height: 16,
              border: '2px solid #f3f3f3',
              borderTop: '2px solid #3498db',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
              '@keyframes spin': {
                '0%': { transform: 'rotate(0deg)' },
                '100%': { transform: 'rotate(360deg)' },
              },
            }}
          />
        )}
      </Box>
    );
  };

  // Render status messages
  const renderStatusMessages = () => {
    if (success) {
      return (
        <Typography
          variant="caption"
          sx={{
            display: 'block',
            mt: 0.5,
            color: 'success.main',
            ...successSx,
          }}
        >
          {successMessage || 'Success'}
        </Typography>
      );
    }

    if (warning) {
      return (
        <Typography
          variant="caption"
          sx={{
            display: 'block',
            mt: 0.5,
            color: 'warning.main',
            ...warningSx,
          }}
        >
          {warningMessage || 'Warning'}
        </Typography>
      );
    }

    if (info) {
      return (
        <Typography
          variant="caption"
          sx={{
            display: 'block',
            mt: 0.5,
            color: 'info.main',
            ...infoSx,
          }}
        >
          {infoMessage || 'Info'}
        </Typography>
      );
    }

    return null;
  };

  return (
    <Box
      sx={{
        width: fullWidth ? '100%' : width || 'auto',
        mb: 2,
        position: 'relative',
        ...sx,
      }}
    >
      {renderLabel()}

      <Box
        sx={{
          position: 'relative',
          ...(border && {
            border: `1px solid ${borderColor || 'divider'}`,
            borderRadius: typeof borderRadius === 'number' ? `${borderRadius}px` : borderRadius,
            backgroundColor: backgroundColor || 'background.paper',
            p: padding,
            ...fieldContainerSx,
          }),
          ...(disabled && {
            opacity: 0.7,
            pointerEvents: 'none',
          }),
        }}
      >
        <Box
          sx={{
            position: 'relative',
            ...contentSx,
          }}
        >
          {children}
          {renderLoadingIndicator()}
        </Box>
      </Box>

      {renderDescription()}
      {renderError()}
      {renderStatusMessages()}
      {renderCharacterCounter()}
    </Box>
  );
};

export default FormFieldWrapper;
