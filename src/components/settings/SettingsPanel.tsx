/**
 * Settings Panel Component
 *
 * A component for managing application settings.
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import Modal from '../ui/Modal';

// Types
interface SettingsPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

// Styled Components
const SettingsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const SettingSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
`;

const SectionTitle = styled.h3`
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
`;

const SettingRow = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
`;

const SettingLabel = styled.label`
  font-size: 14px;
  color: #555;
`;

const ToggleSwitch = styled.label`
  position: relative;
  display: inline-block;
  width: 40px;
  height: 24px;

  input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  span {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;

    &:before {
      position: absolute;
      content: '';
      height: 16px;
      width: 16px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      transition: 0.4s;
      border-radius: 50%;
    }
  }

  input:checked + span {
    background-color: #4a90e2;
  }

  input:focus + span {
    box-shadow: 0 0 1px #4a90e2;
  }

  input:checked + span:before {
    transform: translateX(16px);
  }
`;

const Select = styled.select`
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #ddd;
  background-color: white;
  font-size: 14px;
  color: #333;

  &:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
  }
`;

const Button = styled.button`
  padding: 10px 16px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  align-self: flex-end;
  margin-top: 20px;

  &:hover {
    background-color: #3a80d2;
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.4);
  }
`;

/**
 * Settings Panel Component
 */
const SettingsPanel: React.FC<SettingsPanelProps> = ({ isOpen, onClose }) => {
  const [darkMode, setDarkMode] = useState(false);
  const [notifications, setNotifications] = useState(true);
  const [language, setLanguage] = useState('en');
  const [currency, setCurrency] = useState('USD');

  const handleSave = () => {
    // Save settings logic would go here
    console.log('Saving settings:', { darkMode, notifications, language, currency });
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Settings">
      <SettingsContainer>
        <SettingSection>
          <SectionTitle>Appearance</SectionTitle>
          <SettingRow>
            <SettingLabel>Dark Mode</SettingLabel>
            <ToggleSwitch>
              <input
                type="checkbox"
                checked={darkMode}
                onChange={(e) => setDarkMode(e.target.checked)}
              />
              <span />
            </ToggleSwitch>
          </SettingRow>
        </SettingSection>

        <SettingSection>
          <SectionTitle>Notifications</SectionTitle>
          <SettingRow>
            <SettingLabel>Enable Notifications</SettingLabel>
            <ToggleSwitch>
              <input
                type="checkbox"
                checked={notifications}
                onChange={(e) => setNotifications(e.target.checked)}
              />
              <span />
            </ToggleSwitch>
          </SettingRow>
        </SettingSection>

        <SettingSection>
          <SectionTitle>Regional</SectionTitle>
          <SettingRow>
            <SettingLabel>Language</SettingLabel>
            <Select value={language} onChange={(e) => setLanguage(e.target.value)}>
              <option value="en">English</option>
              <option value="es">Spanish</option>
              <option value="fr">French</option>
              <option value="de">German</option>
            </Select>
          </SettingRow>
          <SettingRow>
            <SettingLabel>Currency</SettingLabel>
            <Select value={currency} onChange={(e) => setCurrency(e.target.value)}>
              <option value="USD">USD ($)</option>
              <option value="EUR">EUR (€)</option>
              <option value="GBP">GBP (£)</option>
              <option value="JPY">JPY (¥)</option>
            </Select>
          </SettingRow>
        </SettingSection>

        <Button onClick={handleSave}>Save Changes</Button>
      </SettingsContainer>
    </Modal>
  );
};

export default SettingsPanel;
