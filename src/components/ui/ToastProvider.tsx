/**
 * Toast Provider
 *
 * A provider for managing toast notifications throughout the application.
 */

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import Toast, { ToastType, ToastPosition } from './Toast';

// Types
interface ToastOptions {
  type?: ToastType;
  duration?: number;
  position?: ToastPosition;
  showIcon?: boolean;
  showCloseButton?: boolean;
}

interface ToastContextType {
  showToast: (message: string, options?: ToastOptions) => string;
  hideToast: (id: string) => void;
}

interface ToastItem {
  id: string;
  message: string;
  type: ToastType;
  duration: number;
  position: ToastPosition;
  showIcon: boolean;
  showCloseButton: boolean;
}

interface ToastProviderProps {
  children: ReactNode;
}

// Create context
const ToastContext = createContext<ToastContextType | undefined>(undefined);

/**
 * Toast Provider Component
 */
export const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {
  const [toasts, setToasts] = useState<ToastItem[]>([]);

  // Show toast
  const showToast = useCallback((message: string, options?: ToastOptions): string => {
    const id = Math.random().toString(36).substring(2, 9);

    setToasts((prevToasts) => [
      ...prevToasts,
      {
        id,
        message,
        type: options?.type || 'info',
        duration: options?.duration || 5000,
        position: options?.position || 'top-right',
        showIcon: options?.showIcon !== undefined ? options.showIcon : true,
        showCloseButton: options?.showCloseButton !== undefined ? options.showCloseButton : true,
      },
    ]);

    return id;
  }, []);

  // Hide toast
  const hideToast = useCallback((id: string) => {
    setToasts((prevToasts) => prevToasts.filter((toast) => toast.id !== id));
  }, []);

  return (
    <ToastContext.Provider value={{ showToast, hideToast }}>
      {children}

      {toasts.map((toast) => (
        <Toast
          key={toast.id}
          type={toast.type}
          message={toast.message}
          duration={toast.duration}
          position={toast.position}
          showIcon={toast.showIcon}
          showCloseButton={toast.showCloseButton}
          onClose={() => hideToast(toast.id)}
        />
      ))}
    </ToastContext.Provider>
  );
};

/**
 * Custom hook to use the toast context
 */
export const useToast = (): ToastContextType => {
  const context = useContext(ToastContext);
  if (context === undefined) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

export default ToastProvider;
