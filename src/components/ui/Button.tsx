/**
 * Button Component
 *
 * A standardized button component with support for different variants,
 * sizes, and states. Fully supports dark and light themes with proper
 * accessibility features.
 */

import React from 'react';
import styled, { css } from 'styled-components';
import getSafeTheme from '../../theme/withSafeTheme';

// Types
export type ButtonVariant = 'primary' | 'secondary' | 'text' | 'outlined';
export type ButtonSize = 'small' | 'medium' | 'large';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  fullWidth?: boolean;
  loading?: boolean;
  disabled?: boolean;
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  children: React.ReactNode;
}

// Styled Components
const StyledButton = styled.button<{
  variant: ButtonVariant;
  size: ButtonSize;
  $fullWidth: boolean;
  $hasStartIcon: boolean;
  $hasEndIcon: boolean;
}>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  outline: 0;
  border: 0;
  margin: 0;
  cursor: pointer;
  user-select: none;
  vertical-align: middle;
  text-decoration: none;
  font-family: ${({ theme }) => getSafeTheme(theme, (t) => t.typography.fontFamily.primary)};
  font-weight: 500;
  line-height: 1.75;
  letter-spacing: 0.02em;
  min-width: 64px;
  border-radius: ${({ theme }) => getSafeTheme(theme, (t) => t.borderRadius.md)};
  transition: all 0.2s ease-in-out;
  width: ${({ $fullWidth }) => ($fullWidth ? '100%' : 'auto')};

  /* Disabled state */
  &:disabled {
    cursor: not-allowed;
    pointer-events: none;
    opacity: 0.6;
  }

  /* Size styles */
  ${({ size }) => {
    switch (size) {
      case 'small':
        return css`
          padding: 4px 10px;
          font-size: 0.8125rem;
        `;
      case 'large':
        return css`
          padding: 10px 22px;
          font-size: 1rem;
        `;
      case 'medium':
      default:
        return css`
          padding: 6px 16px;
          font-size: 0.875rem;
        `;
    }
  }}

  /* Variant styles */
  ${({ theme, variant }) => {
    switch (variant) {
      case 'primary':
        return css`
          background-color: ${getSafeTheme(theme, (t) => t.colors.primary.main)};
          color: ${getSafeTheme(theme, (t) => t.colors.primary.contrastText)};
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);

          &:hover {
            background-color: ${getSafeTheme(theme, (t) => t.colors.primary.dark)};
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
          }

          &:active {
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
          }

          &:focus-visible {
            box-shadow: 0 0 0 3px ${getSafeTheme(theme, (t) => t.colors.primary.light)}40;
          }
        `;
      case 'secondary':
        return css`
          background-color: ${getSafeTheme(theme, (t) => t.colors.secondary.main)};
          color: ${getSafeTheme(theme, (t) => t.colors.secondary.contrastText)};
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);

          &:hover {
            background-color: ${getSafeTheme(theme, (t) => t.colors.secondary.dark)};
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
          }

          &:active {
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
          }

          &:focus-visible {
            box-shadow: 0 0 0 3px ${getSafeTheme(theme, (t) => t.colors.secondary.light)}40;
          }
        `;
      case 'outlined':
        return css`
          background-color: transparent;
          color: ${getSafeTheme(theme, (t) => t.colors.primary.main)};
          border: 1px solid ${getSafeTheme(theme, (t) => t.colors.primary.main)};

          &:hover {
            background-color: ${getSafeTheme(theme, (t) => t.colors.primary.main)}10;
          }

          &:focus-visible {
            box-shadow: 0 0 0 3px ${getSafeTheme(theme, (t) => t.colors.primary.light)}40;
          }
        `;
      case 'text':
        return css`
          background-color: transparent;
          color: ${getSafeTheme(theme, (t) => t.colors.primary.main)};

          &:hover {
            background-color: ${getSafeTheme(theme, (t) => t.colors.primary.main)}10;
          }

          &:focus-visible {
            box-shadow: 0 0 0 3px ${getSafeTheme(theme, (t) => t.colors.primary.light)}40;
          }
        `;
    }
  }}

  /* Icon spacing */
  ${({ $hasStartIcon }) =>
    $hasStartIcon &&
    css`
      & > .start-icon {
        margin-right: 8px;
      }
    `}

  ${({ $hasEndIcon }) =>
    $hasEndIcon &&
    css`
      & > .end-icon {
        margin-left: 8px;
      }
    `}
`;

const LoadingSpinner = styled.div`
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 0.8s linear infinite;
  margin-right: 8px;

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
`;

/**
 * Button Component
 */
const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'medium',
  startIcon,
  endIcon,
  fullWidth = false,
  loading = false,
  disabled = false,
  onClick,
  children,
  ...props
}) => {
  // Handle button click with error handling
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    try {
      if (onClick && !disabled && !loading) {
        onClick(event);
      }
    } catch (error) {
      console.error('Error in button click handler:', error);
      // In a real app, you might want to show a toast notification or other feedback
    }
  };

  return (
    <StyledButton
      variant={variant}
      size={size}
      $fullWidth={fullWidth}
      $hasStartIcon={!!startIcon}
      $hasEndIcon={!!endIcon}
      disabled={disabled || loading}
      onClick={handleClick}
      {...props}
    >
      {loading && <LoadingSpinner />}
      {!loading && startIcon && <span className="start-icon">{startIcon}</span>}
      {children}
      {!loading && endIcon && <span className="end-icon">{endIcon}</span>}
    </StyledButton>
  );
};

export default Button;
