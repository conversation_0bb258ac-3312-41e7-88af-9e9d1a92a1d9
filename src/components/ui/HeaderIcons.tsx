/**
 * HeaderIcons Component
 *
 * A simple component that displays a row of icons with tooltips.
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../theme/SimpleThemeProvider';
import SettingsPanel from '../settings/SettingsPanel';
import ProfileManager from '../profile/ProfileManager';
import FinancialDataImport from '../financial/FinancialDataImport';
import DataManagement from '../data/DataManagement';

// Styled Components
const Container = styled.div`
  position: fixed;
  top: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  z-index: 1000;
  padding: 8px;
  border-radius: 50px;
  backdrop-filter: blur(8px);
  background-color: ${({ theme }) =>
    theme.mode === 'dark' ? 'rgba(30, 30, 30, 0.7)' : 'rgba(255, 255, 255, 0.7)'};
  box-shadow: ${({ theme }) =>
    theme.mode === 'dark' ? '0 4px 12px rgba(0, 0, 0, 0.3)' : '0 4px 12px rgba(0, 0, 0, 0.1)'};
  transition: all 0.3s ease;

  @media (max-width: 768px) {
    top: auto;
    bottom: 20px;
    right: 50%;
    transform: translateX(50%);
  }
`;

const IconButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 42px;
  height: 42px;
  border-radius: 50%;
  border: none;
  background-color: ${({ theme }) =>
    theme.mode === 'dark' ? 'rgba(60, 60, 60, 0.8)' : 'rgba(240, 240, 240, 0.8)'};
  color: ${({ theme }) => (theme.mode === 'dark' ? theme.colors.text.primary : '#333')};
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  box-shadow: ${({ theme }) =>
    theme.mode === 'dark' ? '0 2px 8px rgba(0, 0, 0, 0.3)' : '0 2px 8px rgba(0, 0, 0, 0.1)'};

  &:hover {
    background-color: ${({ theme }) =>
      theme.mode === 'dark' ? 'rgba(80, 80, 80, 0.9)' : 'rgba(220, 220, 220, 0.9)'};
    transform: translateY(-3px) scale(1.05);
    box-shadow: ${({ theme }) =>
      theme.mode === 'dark' ? '0 6px 12px rgba(0, 0, 0, 0.4)' : '0 6px 12px rgba(0, 0, 0, 0.15)'};
  }

  &:active {
    transform: translateY(0) scale(0.95);
    box-shadow: ${({ theme }) =>
      theme.mode === 'dark' ? '0 2px 4px rgba(0, 0, 0, 0.4)' : '0 2px 4px rgba(0, 0, 0, 0.1)'};
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.main};
  }

  /* Tooltip */
  &:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: -36px;
    left: 50%;
    transform: translateX(-50%);
    padding: 6px 10px;
    background-color: ${({ theme }) =>
      theme.mode === 'dark' ? 'rgba(40, 40, 40, 0.95)' : 'rgba(50, 50, 50, 0.95)'};
    color: white;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    letter-spacing: 0.3px;
    white-space: nowrap;
    z-index: 1000;
    box-shadow: ${({ theme }) =>
      theme.mode === 'dark' ? '0 4px 8px rgba(0, 0, 0, 0.3)' : '0 4px 8px rgba(0, 0, 0, 0.2)'};
    backdrop-filter: blur(4px);
  }

  /* Add a subtle arrow to the tooltip */
  &:hover::before {
    content: '';
    position: absolute;
    bottom: -12px;
    left: 50%;
    transform: translateX(-50%);
    border-width: 6px;
    border-style: solid;
    border-color: transparent transparent
      ${({ theme }) =>
        theme.mode === 'dark' ? 'rgba(40, 40, 40, 0.95)' : 'rgba(50, 50, 50, 0.95)'}
      transparent;
    z-index: 1000;
  }

  svg {
    transition: all 0.2s ease;
    fill: ${({ theme }) => (theme.mode === 'dark' ? theme.colors.text.primary : '#333')};
  }
`;

/**
 * HeaderIcons Component
 */
const HeaderIcons: React.FC = () => {
  const { theme } = useTheme();

  // State for modals
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isImportOpen, setIsImportOpen] = useState(false);
  const [isDataManagementOpen, setIsDataManagementOpen] = useState(false);

  // Handler functions
  const handleSettingsClick = () => {
    setIsSettingsOpen(true);
  };

  const handleProfileClick = () => {
    setIsProfileOpen(true);
  };

  const handleImportClick = () => {
    setIsImportOpen(true);
  };

  const handleDataManagementClick = () => {
    setIsDataManagementOpen(true);
  };

  return (
    <>
      <Container>
        <IconButton onClick={handleSettingsClick} aria-label="Settings" data-tooltip="Settings">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.***********.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.***********.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z" />
          </svg>
        </IconButton>

        <IconButton
          onClick={handleProfileClick}
          aria-label="Profile Manager"
          data-tooltip="Profile Manager"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
          </svg>
        </IconButton>

        <IconButton
          onClick={handleImportClick}
          aria-label="Import Financial Data"
          data-tooltip="Import Financial Data"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" />
          </svg>
        </IconButton>

        <IconButton
          onClick={handleDataManagementClick}
          aria-label="Data Management"
          data-tooltip="Data Management"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h4l3 3 3-3h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 16h-4.83l-1.17 1.17-1.17-1.17H5V4h14v14z" />
            <path d="M7 7h10v2H7zm0 4h10v2H7zm0 4h7v2H7z" />
          </svg>
        </IconButton>
      </Container>

      {/* Modals */}
      <SettingsPanel isOpen={isSettingsOpen} onClose={() => setIsSettingsOpen(false)} />

      <ProfileManager isOpen={isProfileOpen} onClose={() => setIsProfileOpen(false)} />

      <FinancialDataImport isOpen={isImportOpen} onClose={() => setIsImportOpen(false)} />

      <DataManagement
        isOpen={isDataManagementOpen}
        onClose={() => setIsDataManagementOpen(false)}
      />
    </>
  );
};

export default HeaderIcons;
