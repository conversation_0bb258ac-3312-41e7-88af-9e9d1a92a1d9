/**
 * IconButton Component
 *
 * A premium icon button component with hover and active states.
 * Supports dark and light themes.
 */

import React from 'react';
import styled, { css } from 'styled-components';
import { useTheme } from '../../theme/SimpleThemeProvider';

// Types
export type IconButtonSize = 'small' | 'medium' | 'large';
export type IconButtonVariant = 'default' | 'primary' | 'secondary' | 'text';

export interface IconButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  size?: IconButtonSize;
  variant?: IconButtonVariant;
  children: React.ReactNode;
  active?: boolean;
}

// Styled Components
const StyledButton = styled.button<{
  $size: IconButtonSize;
  $variant: IconButtonVariant;
  $isDarkMode: boolean;
  $active: boolean;
}>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;

  /* Size styles */
  ${({ $size }) => {
    switch ($size) {
      case 'small':
        return css`
          width: 32px;
          height: 32px;
          font-size: 16px;
        `;
      case 'large':
        return css`
          width: 48px;
          height: 48px;
          font-size: 24px;
        `;
      case 'medium':
      default:
        return css`
          width: 40px;
          height: 40px;
          font-size: 20px;
        `;
    }
  }}

  /* Variant styles */
  ${({ $variant, $isDarkMode, theme, $active }) => {
    const getBackgroundColor = () => {
      if ($active) {
        return $isDarkMode ? theme.colors.primary.dark : theme.colors.primary.light;
      }

      switch ($variant) {
        case 'primary':
          return $isDarkMode ? theme.colors.primary.main : theme.colors.primary.main;
        case 'secondary':
          return $isDarkMode ? theme.colors.secondary.main : theme.colors.secondary.main;
        case 'text':
          return 'transparent';
        case 'default':
        default:
          return $isDarkMode ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.04)';
      }
    };

    const getColor = () => {
      if ($variant === 'primary' || $variant === 'secondary' || $active) {
        return theme.colors.primary.contrastText;
      }

      return $isDarkMode ? theme.colors.text.primary : theme.colors.text.primary;
    };

    return css`
      background-color: ${getBackgroundColor()};
      color: ${getColor()};

      &:hover {
        background-color: ${$variant === 'text'
          ? $isDarkMode
            ? 'rgba(255, 255, 255, 0.08)'
            : 'rgba(0, 0, 0, 0.04)'
          : getBackgroundColor()};
        filter: brightness(${$isDarkMode ? 1.2 : 0.9});
      }

      &:active {
        transform: scale(0.95);
      }

      &:disabled {
        cursor: not-allowed;
        opacity: 0.6;
        background-color: ${$isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)'};
        color: ${$isDarkMode ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)'};
      }
    `;
  }}

  /* Focus styles */
  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.main};
  }

  /* Ensure SVG icons inherit the button's color */
  svg {
    fill: currentColor;
    width: 1em;
    height: 1em;
  }
`;

/**
 * IconButton Component
 */
const IconButton: React.FC<IconButtonProps> = ({
  size = 'medium',
  variant = 'default',
  children,
  active = false,
  ...props
}) => {
  const { theme } = useTheme();
  const isDarkMode = theme.mode === 'dark';

  return (
    <StyledButton
      type="button"
      $size={size}
      $variant={variant}
      $isDarkMode={isDarkMode}
      $active={active}
      {...props}
    >
      {children}
    </StyledButton>
  );
};

export default IconButton;
