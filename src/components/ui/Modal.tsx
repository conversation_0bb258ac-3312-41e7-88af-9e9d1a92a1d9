/**
 * Modal Component
 *
 * A reusable modal dialog component with backdrop.
 */

import React, { useEffect, useRef } from 'react';
import styled from 'styled-components';
import { createPortal } from 'react-dom';
import { useTheme } from '../../theme/SimpleThemeProvider';
import { motion, AnimatePresence } from 'framer-motion';

// Types
interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  width?: string;
}

// Styled Components
const Backdrop = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: ${({ theme }) =>
    theme.mode === 'dark' ? 'rgba(0, 0, 0, 0.7)' : 'rgba(0, 0, 0, 0.5)'};
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
`;

const ModalContainer = styled(motion.div)<{ width: string }>`
  background-color: ${({ theme }) =>
    theme.mode === 'dark' ? 'rgba(30, 30, 35, 0.95)' : 'rgba(255, 255, 255, 0.95)'};
  border-radius: 12px;
  box-shadow: ${({ theme }) =>
    theme.mode === 'dark' ? '0 8px 32px rgba(0, 0, 0, 0.5)' : '0 8px 32px rgba(0, 0, 0, 0.15)'};
  border: ${({ theme }) =>
    theme.mode === 'dark'
      ? '1px solid rgba(60, 60, 70, 0.5)'
      : '1px solid rgba(240, 240, 240, 0.8)'};
  width: ${({ width }) => width};
  max-width: 90%;
  max-height: 85vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  backdrop-filter: blur(10px);

  /* Custom scrollbar */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: ${({ theme }) =>
      theme.mode === 'dark' ? 'rgba(30, 30, 35, 0.2)' : 'rgba(240, 240, 240, 0.5)'};
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background: ${({ theme }) =>
      theme.mode === 'dark' ? 'rgba(80, 80, 90, 0.5)' : 'rgba(180, 180, 180, 0.7)'};
    border-radius: 10px;

    &:hover {
      background: ${({ theme }) =>
        theme.mode === 'dark' ? 'rgba(100, 100, 110, 0.7)' : 'rgba(160, 160, 160, 0.9)'};
    }
  }
`;

const ModalHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: ${({ theme }) =>
    theme.mode === 'dark'
      ? '1px solid rgba(60, 60, 70, 0.5)'
      : '1px solid rgba(240, 240, 240, 0.8)'};
`;

const ModalTitle = styled.h2`
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: ${({ theme }) => (theme.mode === 'dark' ? theme.colors.text.primary : '#333')};
  letter-spacing: 0.3px;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: ${({ theme }) =>
    theme.mode === 'dark' ? 'rgba(200, 200, 210, 0.8)' : 'rgba(100, 100, 100, 0.8)'};
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${({ theme }) =>
      theme.mode === 'dark' ? 'rgba(80, 80, 90, 0.3)' : 'rgba(0, 0, 0, 0.05)'};
    color: ${({ theme }) =>
      theme.mode === 'dark' ? 'rgba(255, 255, 255, 0.9)' : 'rgba(60, 60, 60, 0.9)'};
    transform: rotate(90deg);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.main};
  }
`;

const ModalContent = styled.div`
  padding: 24px;
  color: ${({ theme }) =>
    theme.mode === 'dark' ? theme.colors.text.primary : theme.colors.text.primary};
`;

/**
 * Modal Component
 */
const Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children, width = '500px' }) => {
  const { theme } = useTheme();
  const modalRef = useRef<HTMLDivElement>(null);

  // Close modal when clicking outside
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (modalRef.current && !modalRef.current.contains(e.target as Node)) {
      onClose();
    }
  };

  // Close modal when pressing Escape key
  useEffect(() => {
    const handleEscKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscKey);

    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [isOpen, onClose]);

  // Prevent body scrolling when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  // Animation variants
  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.2 } },
  };

  const modalVariants = {
    hidden: {
      opacity: 0,
      y: 20,
      scale: 0.98,
      transition: { duration: 0.2 },
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: 'spring',
        damping: 25,
        stiffness: 500,
        duration: 0.3,
      },
    },
  };

  return createPortal(
    <AnimatePresence>
      {isOpen && (
        <Backdrop
          onClick={handleBackdropClick}
          initial="hidden"
          animate="visible"
          exit="hidden"
          variants={backdropVariants}
        >
          <ModalContainer
            ref={modalRef}
            width={width}
            initial="hidden"
            animate="visible"
            exit="hidden"
            variants={modalVariants}
          >
            <ModalHeader>
              <ModalTitle>{title}</ModalTitle>
              <CloseButton onClick={onClose} aria-label="Close">
                ×
              </CloseButton>
            </ModalHeader>
            <ModalContent>{children}</ModalContent>
          </ModalContainer>
        </Backdrop>
      )}
    </AnimatePresence>,
    document.body
  );
};

export default Modal;
