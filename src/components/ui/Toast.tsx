/**
 * Toast Component
 *
 * A toast notification component for displaying success, error, warning,
 * and info messages. Supports dark and light themes.
 */

import React, { useState, useEffect } from 'react';
import styled, { css } from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';

// Types
export type ToastType = 'success' | 'error' | 'warning' | 'info';
export type ToastPosition =
  | 'top-right'
  | 'top-left'
  | 'bottom-right'
  | 'bottom-left'
  | 'top-center'
  | 'bottom-center';

export interface ToastProps {
  type?: ToastType;
  message: string;
  duration?: number;
  position?: ToastPosition;
  onClose?: () => void;
  showIcon?: boolean;
  showCloseButton?: boolean;
}

// Styled Components
const ToastContainer = styled(motion.div)<{
  type: ToastType;
  position: ToastPosition;
}>`
  display: flex;
  align-items: center;
  min-width: 250px;
  max-width: 450px;
  padding: ${({ theme }) => theme.spacing(3)} ${({ theme }) => theme.spacing(4)};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  box-shadow: ${({ theme }) => theme.shadows.lg};
  position: fixed;
  z-index: ${({ theme }) => theme.zIndex.snackbar};

  ${({ position }) => {
    switch (position) {
      case 'top-right':
        return css`
          top: ${({ theme }) => theme.spacing(4)};
          right: ${({ theme }) => theme.spacing(4)};
        `;
      case 'top-left':
        return css`
          top: ${({ theme }) => theme.spacing(4)};
          left: ${({ theme }) => theme.spacing(4)};
        `;
      case 'bottom-right':
        return css`
          bottom: ${({ theme }) => theme.spacing(4)};
          right: ${({ theme }) => theme.spacing(4)};
        `;
      case 'bottom-left':
        return css`
          bottom: ${({ theme }) => theme.spacing(4)};
          left: ${({ theme }) => theme.spacing(4)};
        `;
      case 'top-center':
        return css`
          top: ${({ theme }) => theme.spacing(4)};
          left: 50%;
          transform: translateX(-50%);
        `;
      case 'bottom-center':
        return css`
          bottom: ${({ theme }) => theme.spacing(4)};
          left: 50%;
          transform: translateX(-50%);
        `;
      default:
        return css`
          top: ${({ theme }) => theme.spacing(4)};
          right: ${({ theme }) => theme.spacing(4)};
        `;
    }
  }}

  ${({ theme, type }) => {
    switch (type) {
      case 'success':
        return css`
          background-color: ${theme.colors.success.main};
          color: ${theme.colors.success.contrastText};
        `;
      case 'error':
        return css`
          background-color: ${theme.colors.error.main};
          color: ${theme.colors.error.contrastText};
        `;
      case 'warning':
        return css`
          background-color: ${theme.colors.warning.main};
          color: ${theme.colors.warning.contrastText};
        `;
      case 'info':
        return css`
          background-color: ${theme.colors.info.main};
          color: ${theme.colors.info.contrastText};
        `;
      default:
        return css`
          background-color: ${theme.colors.info.main};
          color: ${theme.colors.info.contrastText};
        `;
    }
  }}
`;

const IconContainer = styled.div`
  margin-right: ${({ theme }) => theme.spacing(3)};
  display: flex;
  align-items: center;
  justify-content: center;
`;

const MessageContainer = styled.div`
  flex: 1;
  font-size: ${({ theme }) => theme.typography.fontSize.bodySmall};
  font-weight: ${({ theme }) => theme.typography.fontWeight.regular};
  line-height: ${({ theme }) => theme.typography.lineHeight.normal};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  margin-left: ${({ theme }) => theme.spacing(2)};
  padding: ${({ theme }) => theme.spacing(1)};
  font-size: 1.25rem;
  line-height: 1;
  opacity: 0.7;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 1;
  }

  &:focus {
    outline: none;
    opacity: 1;
  }
`;

// Icons
const SuccessIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM8 15L3 10L4.41 8.59L8 12.17L15.59 4.58L17 6L8 15Z"
      fill="currentColor"
    />
  </svg>
);

const ErrorIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM11 15H9V13H11V15ZM11 11H9V5H11V11Z"
      fill="currentColor"
    />
  </svg>
);

const WarningIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM11 15H9V13H11V15ZM11 11H9V5H11V11Z"
      fill="currentColor"
    />
  </svg>
);

const InfoIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM11 15H9V9H11V15ZM11 7H9V5H11V7Z"
      fill="currentColor"
    />
  </svg>
);

/**
 * Toast Component
 */
const Toast: React.FC<ToastProps> = ({
  type = 'info',
  message,
  duration = 5000,
  position = 'top-right',
  onClose,
  showIcon = true,
  showCloseButton = true,
}) => {
  const [isVisible, setIsVisible] = useState(true);

  // Auto-close after duration
  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [duration]);

  // Handle close
  const handleClose = () => {
    setIsVisible(false);
    if (onClose) {
      onClose();
    }
  };

  // Handle animation complete
  const handleAnimationComplete = () => {
    if (!isVisible && onClose) {
      onClose();
    }
  };

  // Get icon based on type
  const getIcon = () => {
    switch (type) {
      case 'success':
        return <SuccessIcon />;
      case 'error':
        return <ErrorIcon />;
      case 'warning':
        return <WarningIcon />;
      case 'info':
        return <InfoIcon />;
      default:
        return <InfoIcon />;
    }
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <ToastContainer
          type={type}
          position={position}
          initial={{
            opacity: 0,
            y: position.includes('top') ? -20 : 20,
            x: position.includes('center') ? '-50%' : 0,
          }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: position.includes('top') ? -20 : 20 }}
          transition={{ duration: 0.3 }}
          onAnimationComplete={handleAnimationComplete}
        >
          {showIcon && <IconContainer>{getIcon()}</IconContainer>}
          <MessageContainer>{message}</MessageContainer>
          {showCloseButton && (
            <CloseButton onClick={handleClose} aria-label="Close">
              ×
            </CloseButton>
          )}
        </ToastContainer>
      )}
    </AnimatePresence>
  );
};

export default Toast;
