/**
 * Card Component
 *
 * A premium card component with support for different elevations,
 * header, content, and actions. Fully supports dark and light themes.
 */

import React from 'react';
import styled, { css } from 'styled-components';
import { motion } from 'framer-motion';
import getSafeTheme from '../../theme/withSafeTheme';

// Types
export type CardElevation = 'flat' | 'low' | 'medium' | 'high';

export interface CardProps {
  elevation?: CardElevation;
  fullWidth?: boolean;
  className?: string;
  children: React.ReactNode;
  onClick?: () => void;
  interactive?: boolean;
}

export interface CardHeaderProps {
  title: React.ReactNode;
  subheader?: React.ReactNode;
  avatar?: React.ReactNode;
  action?: React.ReactNode;
  className?: string;
}

export interface CardContentProps {
  className?: string;
  children: React.ReactNode;
}

export interface CardActionsProps {
  className?: string;
  children: React.ReactNode;
  align?: 'start' | 'end' | 'center' | 'space-between';
}

// Styled Components
const StyledCard = styled(motion.div)<{
  elevation: CardElevation;
  $fullWidth: boolean;
  $interactive: boolean;
}>`
  background-color: ${({ theme }) => getSafeTheme(theme, (t) => t.colors.background.paper)};
  color: ${({ theme }) => getSafeTheme(theme, (t) => t.colors.text.primary)};
  border-radius: ${({ theme }) => getSafeTheme(theme, (t) => t.borderRadius.md)};
  overflow: hidden;
  width: ${({ $fullWidth }) => ($fullWidth ? '100%' : 'auto')};
  transition: all 0.2s ease-in-out;

  ${({ theme, elevation }) => {
    switch (elevation) {
      case 'flat':
        return css`
          box-shadow: none;
          border: 1px solid ${getSafeTheme(theme, (t) => t.colors.divider)};
        `;
      case 'low':
        return css`
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
        `;
      case 'high':
        return css`
          box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        `;
      case 'medium':
      default:
        return css`
          box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        `;
    }
  }}

  ${({ $interactive }) =>
    $interactive &&
    css`
      cursor: pointer;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
      }

      &:active {
        transform: translateY(0);
      }
    `}
`;

const StyledCardHeader = styled.div`
  display: flex;
  align-items: center;
  padding: 16px;

  .avatar {
    margin-right: 16px;
  }

  .content {
    flex: 1;
  }

  .title {
    font-size: 1.25rem;
    font-weight: 500;
    margin: 0;
    color: ${({ theme }) => getSafeTheme(theme, (t) => t.colors.text.primary)};
  }

  .subheader {
    font-size: 0.875rem;
    color: ${({ theme }) => getSafeTheme(theme, (t) => t.colors.text.secondary)};
    margin: 4px 0 0 0;
  }

  .action {
    margin-left: auto;
  }
`;

const StyledCardContent = styled.div`
  padding: 16px;
  color: ${({ theme }) => getSafeTheme(theme, (t) => t.colors.text.primary)};

  &:last-child {
    padding-bottom: 24px;
  }
`;

const StyledCardActions = styled.div<{ $align: string }>`
  display: flex;
  align-items: center;
  padding: 8px;

  ${({ $align }) => {
    switch ($align) {
      case 'start':
        return css`
          justify-content: flex-start;
        `;
      case 'end':
        return css`
          justify-content: flex-end;
        `;
      case 'center':
        return css`
          justify-content: center;
        `;
      case 'space-between':
        return css`
          justify-content: space-between;
        `;
      default:
        return css`
          justify-content: flex-start;
        `;
    }
  }}

  & > * {
    margin: 0 4px;
  }

  & > *:first-child {
    margin-left: 0;
  }

  & > *:last-child {
    margin-right: 0;
  }
`;

/**
 * Card Component
 */
const Card: React.FC<CardProps> = ({
  elevation = 'medium',
  fullWidth = false,
  className,
  children,
  onClick,
  interactive = false,
}) => {
  return (
    <StyledCard
      elevation={elevation}
      $fullWidth={fullWidth}
      $interactive={interactive || !!onClick}
      className={className}
      onClick={onClick}
      whileHover={interactive || onClick ? { y: -2 } : {}}
      whileTap={interactive || onClick ? { y: 0 } : {}}
    >
      {children}
    </StyledCard>
  );
};

/**
 * Card Header Component
 */
export const CardHeader: React.FC<CardHeaderProps> = ({
  title,
  subheader,
  avatar,
  action,
  className,
}) => {
  return (
    <StyledCardHeader className={className}>
      {avatar && <div className="avatar">{avatar}</div>}
      <div className="content">
        <h3 className="title">{title}</h3>
        {subheader && <p className="subheader">{subheader}</p>}
      </div>
      {action && <div className="action">{action}</div>}
    </StyledCardHeader>
  );
};

/**
 * Card Content Component
 */
export const CardContent: React.FC<CardContentProps> = ({ className, children }) => {
  return <StyledCardContent className={className}>{children}</StyledCardContent>;
};

/**
 * Card Actions Component
 */
export const CardActions: React.FC<CardActionsProps> = ({
  className,
  children,
  align = 'start',
}) => {
  return (
    <StyledCardActions className={className} $align={align}>
      {children}
    </StyledCardActions>
  );
};

export default Card;
