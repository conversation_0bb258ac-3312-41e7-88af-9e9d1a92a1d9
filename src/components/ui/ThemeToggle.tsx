/**
 * Theme Toggle Component
 *
 * A premium toggle component for switching between light and dark themes.
 * Features smooth animations and seasonal color accents.
 */

import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from '../../theme/SimpleThemeProvider';

// Types
interface ThemeToggleProps {
  size?: 'small' | 'medium' | 'large';
  showLabel?: boolean;
  className?: string;
}

// Styled Components
const ToggleContainer = styled.div<{ size: string }>`
  display: flex;
  align-items: center;
  gap: ${({ size }) => (size === 'small' ? '0.5rem' : size === 'large' ? '1rem' : '0.75rem')};
`;

const ToggleButton = styled.button<{ size: string }>`
  position: relative;
  width: ${({ size }) => (size === 'small' ? '40px' : size === 'large' ? '60px' : '50px')};
  height: ${({ size }) => (size === 'small' ? '20px' : size === 'large' ? '30px' : '25px')};
  background-color: ${({ theme }) =>
    theme.mode === 'dark' ? theme.colors.background.elevated : theme.colors.background.paper};
  border: 2px solid ${({ theme }) => theme.colors.seasons[theme.season].primary};
  border-radius: 999px;
  cursor: pointer;
  padding: 0;
  transition: all 0.3s ease;
  box-shadow: ${({ theme }) => theme.shadows.md};

  &:hover {
    box-shadow: ${({ theme }) => theme.shadows.lg};
    transform: translateY(-1px);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.seasons[theme.season].accent};
  }
`;

const ToggleThumb = styled(motion.div)<{ size: string }>`
  position: absolute;
  top: 2px;
  left: 2px;
  width: ${({ size }) => (size === 'small' ? '16px' : size === 'large' ? '26px' : '21px')};
  height: ${({ size }) => (size === 'small' ? '16px' : size === 'large' ? '26px' : '21px')};
  border-radius: 50%;
  background-color: ${({ theme }) => theme.colors.seasons[theme.season].primary};
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
`;

const ToggleIcon = styled.div<{ size: string }>`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: ${({ theme }) =>
    theme.mode === 'dark' ? theme.colors.background.paper : theme.colors.background.elevated};
  font-size: ${({ size }) => (size === 'small' ? '10px' : size === 'large' ? '16px' : '12px')};
`;

const ToggleLabel = styled.span`
  font-size: ${({ theme }) => theme.typography.fontSize.bodySmall};
  color: ${({ theme }) => theme.colors.text.primary};
  font-weight: 500;
`;

/**
 * Theme Toggle Component
 */
const ThemeToggle: React.FC<ThemeToggleProps> = ({
  size = 'medium',
  showLabel = true,
  className,
}) => {
  const { theme, setMode } = useTheme();
  const isDark = theme.mode === 'dark';

  const toggleTheme = () => {
    setMode(isDark ? 'light' : 'dark');
  };

  // Calculate thumb position based on size
  const getThumbPosition = () => {
    if (size === 'small') return isDark ? 22 : 2;
    if (size === 'large') return isDark ? 32 : 2;
    return isDark ? 27 : 2; // medium
  };

  return (
    <ToggleContainer size={size} className={className}>
      {showLabel && <ToggleLabel>{isDark ? 'Dark' : 'Light'} Mode</ToggleLabel>}
      <ToggleButton
        size={size}
        onClick={toggleTheme}
        aria-label={`Switch to ${isDark ? 'light' : 'dark'} mode`}
        title={`Switch to ${isDark ? 'light' : 'dark'} mode`}
      >
        <ToggleThumb
          size={size}
          initial={false}
          animate={{ x: getThumbPosition() }}
          transition={{ type: 'spring', stiffness: 500, damping: 30 }}
        >
          <ToggleIcon size={size}>{isDark ? '☾' : '☀'}</ToggleIcon>
        </ToggleThumb>
      </ToggleButton>
    </ToggleContainer>
  );
};

export default ThemeToggle;
