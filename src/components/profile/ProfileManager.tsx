/**
 * Profile Manager Component
 *
 * A component for managing user profiles.
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import Modal from '../ui/Modal';
import { useAppContext } from '../../context/AppContextMinimal';

// Types
interface ProfileManagerProps {
  isOpen: boolean;
  onClose: () => void;
}

interface Profile {
  id: string;
  name: string;
  email: string;
  role: string;
  isActive: boolean;
}

// Styled Components
const ProfileContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const ProfileList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: 300px;
  overflow-y: auto;
`;

const ProfileCard = styled.div<{ isActive: boolean }>`
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 6px;
  background-color: ${({ isActive }) => (isActive ? '#f0f7ff' : 'white')};
  border: 1px solid ${({ isActive }) => (isActive ? '#4a90e2' : '#eee')};
  cursor: pointer;

  &:hover {
    background-color: ${({ isActive }) => (isActive ? '#e0f0ff' : '#f9f9f9')};
  }
`;

const ProfileAvatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #4a90e2;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 12px;
`;

const ProfileInfo = styled.div`
  flex: 1;
`;

const ProfileName = styled.h3`
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
`;

const ProfileEmail = styled.p`
  margin: 4px 0 0;
  font-size: 14px;
  color: #666;
`;

const ProfileRole = styled.span`
  font-size: 12px;
  color: #888;
  background-color: #f0f0f0;
  padding: 2px 8px;
  border-radius: 12px;
  margin-left: 8px;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 8px;
`;

const ActionButton = styled.button`
  background: none;
  border: none;
  color: #4a90e2;
  cursor: pointer;
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;

  &:hover {
    background-color: rgba(74, 144, 226, 0.1);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
  }
`;

const AddButton = styled.button`
  padding: 10px 16px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;

  &:hover {
    background-color: #3a80d2;
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.4);
  }
`;

/**
 * Profile Manager Component
 */
const ProfileManager: React.FC<ProfileManagerProps> = ({ isOpen, onClose }) => {
  const { hasRole } = useAppContext();
  // Sample profiles data
  const [profiles, setProfiles] = useState<Profile[]>([
    {
      id: '1',
      name: 'John Doe',
      email: '<EMAIL>',
      role: 'Admin',
      isActive: true,
    },
    {
      id: '2',
      name: 'Jane Smith',
      email: '<EMAIL>',
      role: 'User',
      isActive: false,
    },
    {
      id: '3',
      name: 'Bob Johnson',
      email: '<EMAIL>',
      role: 'User',
      isActive: false,
    },
  ]);

  // Check if the current user is an admin
  const isAdmin = hasRole('admin');

  const handleSetActive = (id: string) => {
    if (!isAdmin) return;
    setProfiles(
      profiles.map((profile) => ({
        ...profile,
        isActive: profile.id === id,
      }))
    );
  };

  const handleAddProfile = () => {
    if (!isAdmin) return;
    // In a real app, this would open a form to add a new profile
    console.log('Add new profile');
  };

  const handleEditProfile = (id: string) => {
    if (!isAdmin) return;
    // In a real app, this would open a form to edit the profile
    console.log('Edit profile', id);
  };

  const handleDeleteProfile = (id: string) => {
    if (!isAdmin) return;
    // In a real app, this would show a confirmation dialog
    setProfiles(profiles.filter((profile) => profile.id !== id));
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Profile Manager" width="600px">
      {!isAdmin ? (
        <div>
          <p>You do not have permission to access the Profile Manager.</p>
        </div>
      ) : (
        <ProfileContainer>
          <AddButton onClick={handleAddProfile}>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
            </svg>
            Add New Profile
          </AddButton>

          <ProfileList>
            {profiles.map((profile) => (
              <ProfileCard
                key={profile.id}
                isActive={profile.isActive}
                onClick={() => handleSetActive(profile.id)}
              >
                <ProfileAvatar>{profile.name.charAt(0)}</ProfileAvatar>
                <ProfileInfo>
                  <ProfileName>
                    {profile.name}
                    <ProfileRole>{profile.role}</ProfileRole>
                  </ProfileName>
                  <ProfileEmail>{profile.email}</ProfileEmail>
                </ProfileInfo>
                <ActionButtons>
                  <ActionButton
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEditProfile(profile.id);
                    }}
                  >
                    Edit
                  </ActionButton>
                  <ActionButton
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteProfile(profile.id);
                    }}
                    style={{ color: '#e53935' }}
                  >
                    Delete
                  </ActionButton>
                </ActionButtons>
              </ProfileCard>
            ))}
          </ProfileList>
        </ProfileContainer>
      )}
    </Modal>
  );
};

export default ProfileManager;
