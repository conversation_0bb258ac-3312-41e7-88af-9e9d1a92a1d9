/**
 * Data Management Component Tests
 *
 * This file contains tests for the DataManagement component.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import DataManagement from '../DataManagement';
import { DataPortabilityProvider } from '../../../features/DataPortability/context/DataPortabilityContext';
import { ThemeProvider } from '../../../theme/SimpleThemeProvider';

// Mock the DataPortabilityContext
jest.mock('../../../features/DataPortability/context/DataPortabilityContext', () => {
  const originalModule = jest.requireActual(
    '../../../features/DataPortability/context/DataPortabilityContext'
  );

  return {
    ...originalModule,
    useDataPortability: () => ({
      exportData: jest.fn().mockResolvedValue(undefined),
      importData: jest.fn().mockResolvedValue({
        success: true,
        data: {},
        validationErrors: [],
        resolvedConflicts: [],
      }),
      status: 'idle',
      error: null,
      resetStatus: jest.fn(),
    }),
  };
});

// Mock FileReader
class FileReaderMock {
  onload: ((this: FileReader, ev: ProgressEvent<FileReader>) => any) | null = null;
  onerror: ((this: FileReader, ev: ProgressEvent<FileReader>) => any) | null = null;
  result: string | ArrayBuffer | null = null;

  readAsText(file: File) {
    setTimeout(() => {
      this.result = JSON.stringify({
        metadata: {
          exportDate: '2023-10-15T12:00:00.000Z',
          appVersion: '1.0.0',
          exportType: 'all',
        },
        financialCompass: {},
        seasonsOfSelf: {},
        userProfiles: {},
      });
      if (this.onload) {
        this.onload.call(this, new ProgressEvent('load'));
      }
    }, 0);
  }
}

// Setup mocks
Object.defineProperty(window, 'FileReader', { value: FileReaderMock });

describe('DataManagement Component', () => {
  const renderComponent = (props = {}) => {
    return render(
      <ThemeProvider>
        <DataPortabilityProvider>
          <DataManagement isOpen={true} onClose={jest.fn()} {...props} />
        </DataPortabilityProvider>
      </ThemeProvider>
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders export tab by default', () => {
    renderComponent();

    expect(screen.getByText('Export App Data')).toBeInTheDocument();
    expect(
      screen.getByText(/This will export your Financial Compass and Life Journey data/)
    ).toBeInTheDocument();
  });

  test('switches to import tab when clicked', () => {
    renderComponent();

    fireEvent.click(screen.getByText('Import'));

    expect(screen.getByText('Import App Data')).toBeInTheDocument();
    expect(screen.getByText(/Import previously exported LifeCompass data/)).toBeInTheDocument();
  });

  test('switches to snapshot tab when clicked', () => {
    renderComponent();

    fireEvent.click(screen.getByText('Snapshot'));

    expect(screen.getByText('Create Data Snapshot')).toBeInTheDocument();
    expect(screen.getByText(/A snapshot is a point-in-time backup/)).toBeInTheDocument();
  });

  test('handles file selection for import', async () => {
    renderComponent();

    // Switch to import tab
    fireEvent.click(screen.getByText('Import'));

    // Create a mock file
    const file = new File(['{}'], 'test.json', { type: 'application/json' });

    // Get the file input
    const fileInput = screen.getByLabelText(/Select File to Import/i);

    // Simulate file selection
    fireEvent.change(fileInput, { target: { files: [file] } });

    // Check if the file was selected
    await waitFor(() => {
      expect(screen.getByText(/Selected: test.json/i)).toBeInTheDocument();
    });
  });

  test('handles export button click', async () => {
    const { container } = renderComponent();

    // Find the export button
    const exportButton = screen.getByText(/Export All Data/i);

    // Click the export button
    fireEvent.click(exportButton);

    // Check if the export function was called
    await waitFor(() => {
      expect(screen.getByText(/Export All Data/i)).toBeInTheDocument();
    });
  });

  test('handles import button click', async () => {
    renderComponent();

    // Switch to import tab
    fireEvent.click(screen.getByText('Import'));

    // Create a mock file
    const file = new File(['{}'], 'test.json', { type: 'application/json' });

    // Get the file input
    const fileInput = screen.getByLabelText(/Select File to Import/i);

    // Simulate file selection
    fireEvent.change(fileInput, { target: { files: [file] } });

    // Find the import button
    await waitFor(() => {
      const importButton = screen.getByText(/Import Data/i);

      // Click the import button
      fireEvent.click(importButton);
    });

    // Check if the import function was called
    await waitFor(() => {
      expect(screen.getByText(/Import Data/i)).toBeInTheDocument();
    });
  });

  test('handles snapshot creation', async () => {
    renderComponent();

    // Switch to snapshot tab
    fireEvent.click(screen.getByText('Snapshot'));

    // Find the snapshot button
    const snapshotButton = screen.getByText(/Create Snapshot/i);

    // Click the snapshot button
    fireEvent.click(snapshotButton);

    // Check if the snapshot function was called
    await waitFor(() => {
      expect(screen.getByText(/Create Snapshot/i)).toBeInTheDocument();
    });
  });
});
