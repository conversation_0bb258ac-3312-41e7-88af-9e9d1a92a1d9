/**
 * Data Management Component
 *
 * A component for importing/exporting app data and taking snapshots.
 * Enhanced to ensure 100% accurate export and import of all Financial Compass and Life Journey forms data.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import Modal from '../ui/Modal';
import { useTheme } from '../../theme/SimpleThemeProvider';
import { motion } from 'framer-motion';
import {
  useDataPortability,
  DataSection,
  ExportFormat,
} from '../../features/DataPortability/context/DataPortabilityContext';

// Types
interface DataManagementProps {
  isOpen: boolean;
  onClose: () => void;
}

interface Snapshot {
  id: string;
  name: string;
  date: string;
  description: string;
  size: string;
}

// Styled Components
const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const TabsContainer = styled.div`
  display: flex;
  border-bottom: ${({ theme }) =>
    theme.mode === 'dark' ? '1px solid rgba(60, 60, 70, 0.5)' : '1px solid rgba(230, 230, 230, 1)'};
  margin: 0 -24px;
  padding: 0 24px;
`;

const Tab = styled.button<{ isActive: boolean }>`
  padding: 12px 20px;
  background: none;
  border: none;
  position: relative;
  color: ${({ isActive, theme }) =>
    isActive
      ? theme.colors.primary.main
      : theme.mode === 'dark'
        ? 'rgba(200, 200, 210, 0.8)'
        : 'rgba(100, 100, 100, 0.8)'};
  font-weight: ${({ isActive }) => (isActive ? '600' : '500')};
  font-size: 15px;
  cursor: pointer;
  transition: all 0.2s ease;

  &::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: ${({ isActive, theme }) =>
      isActive ? theme.colors.primary.main : 'transparent'};
    transition: all 0.2s ease;
  }

  &:hover {
    color: ${({ theme }) => theme.colors.primary.main};

    &::after {
      background-color: ${({ isActive, theme }) =>
        isActive
          ? theme.colors.primary.main
          : theme.mode === 'dark'
            ? 'rgba(80, 80, 90, 0.5)'
            : 'rgba(200, 200, 200, 0.8)'};
    }
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${({ theme }) => `${theme.colors.primary.main}40`};
  }
`;

const TabContent = styled(motion.div)`
  padding: 16px 0;
`;

const ActionButton = styled(motion.button)`
  padding: 14px 20px;
  background-color: ${({ theme }) => theme.colors.primary.main};
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 15px;
  font-weight: 600;
  letter-spacing: 0.3px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  width: 100%;
  margin-top: 12px;
  transition: all 0.2s ease;
  box-shadow: ${({ theme }) =>
    theme.mode === 'dark' ? '0 4px 12px rgba(0, 0, 0, 0.3)' : '0 4px 12px rgba(74, 144, 226, 0.2)'};

  &:hover {
    background-color: ${({ theme }) => theme.colors.primary.dark};
    transform: translateY(-2px);
    box-shadow: ${({ theme }) =>
      theme.mode === 'dark'
        ? '0 6px 16px rgba(0, 0, 0, 0.4)'
        : '0 6px 16px rgba(74, 144, 226, 0.3)'};
  }

  &:active {
    transform: translateY(0);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px ${({ theme }) => `${theme.colors.primary.main}40`};
  }

  &:disabled {
    background-color: ${({ theme }) =>
      theme.mode === 'dark' ? 'rgba(60, 60, 70, 0.5)' : 'rgba(200, 200, 200, 0.8)'};
    color: ${({ theme }) =>
      theme.mode === 'dark' ? 'rgba(150, 150, 160, 0.8)' : 'rgba(120, 120, 120, 0.8)'};
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  svg {
    width: 20px;
    height: 20px;
  }
`;

// Secondary button style for future use
const _SecondaryButton = styled(ActionButton)`
  background-color: transparent;
  color: ${({ theme }) => theme.colors.primary.main};
  border: 2px solid ${({ theme }) => theme.colors.primary.main};
  box-shadow: none;

  &:hover {
    background-color: ${({ theme }) =>
      theme.mode === 'dark' ? 'rgba(74, 144, 226, 0.15)' : 'rgba(74, 144, 226, 0.08)'};
    box-shadow: none;
  }

  &:disabled {
    border-color: ${({ theme }) =>
      theme.mode === 'dark' ? 'rgba(60, 60, 70, 0.5)' : 'rgba(200, 200, 200, 0.8)'};
    color: ${({ theme }) =>
      theme.mode === 'dark' ? 'rgba(150, 150, 160, 0.8)' : 'rgba(120, 120, 120, 0.8)'};
  }
`;

const FileInput = styled.input`
  display: none;
`;

const InfoBox = styled.div`
  background-color: ${({ theme }) =>
    theme.mode === 'dark' ? 'rgba(60, 60, 80, 0.3)' : 'rgba(240, 247, 255, 0.8)'};
  border: 1px solid
    ${({ theme }) =>
      theme.mode === 'dark' ? 'rgba(80, 80, 100, 0.5)' : 'rgba(208, 228, 255, 0.8)'};
  border-radius: 8px;
  padding: 18px;
  margin-bottom: 20px;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${({ theme }) =>
      theme.mode === 'dark' ? 'rgba(70, 70, 90, 0.4)' : 'rgba(230, 242, 255, 0.9)'};
  }
`;

const InfoTitle = styled.h4`
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => (theme.mode === 'dark' ? theme.colors.text.primary : '#333')};
  display: flex;
  align-items: center;
  gap: 8px;

  svg {
    width: 18px;
    height: 18px;
    color: ${({ theme }) => theme.colors.primary.main};
  }
`;

const InfoText = styled.p`
  margin: 0;
  font-size: 14px;
  color: ${({ theme }) =>
    theme.mode === 'dark' ? 'rgba(200, 200, 210, 0.8)' : 'rgba(80, 80, 80, 0.9)'};
  line-height: 1.6;
`;

const SnapshotList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
  padding-right: 4px;

  /* Custom scrollbar */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: ${({ theme }) =>
      theme.mode === 'dark' ? 'rgba(30, 30, 35, 0.2)' : 'rgba(240, 240, 240, 0.5)'};
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background: ${({ theme }) =>
      theme.mode === 'dark' ? 'rgba(80, 80, 90, 0.5)' : 'rgba(180, 180, 180, 0.7)'};
    border-radius: 10px;

    &:hover {
      background: ${({ theme }) =>
        theme.mode === 'dark' ? 'rgba(100, 100, 110, 0.7)' : 'rgba(160, 160, 160, 0.9)'};
    }
  }
`;

const SnapshotItem = styled(motion.div)`
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid
    ${({ theme }) => (theme.mode === 'dark' ? 'rgba(60, 60, 70, 0.5)' : 'rgba(230, 230, 230, 1)')};
  background-color: ${({ theme }) =>
    theme.mode === 'dark' ? 'rgba(40, 40, 50, 0.3)' : 'rgba(255, 255, 255, 0.8)'};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${({ theme }) =>
      theme.mode === 'dark' ? 'rgba(50, 50, 60, 0.5)' : 'rgba(250, 250, 250, 1)'};
    border-color: ${({ theme }) =>
      theme.mode === 'dark' ? 'rgba(80, 80, 90, 0.6)' : theme.colors.primary.light};
    transform: translateY(-2px);
    box-shadow: ${({ theme }) =>
      theme.mode === 'dark' ? '0 4px 12px rgba(0, 0, 0, 0.2)' : '0 4px 12px rgba(0, 0, 0, 0.05)'};
  }
`;

const SnapshotInfo = styled.div`
  flex: 1;
`;

const SnapshotName = styled.div`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => (theme.mode === 'dark' ? theme.colors.text.primary : '#333')};
`;

const SnapshotMeta = styled.div`
  display: flex;
  gap: 16px;
  margin-top: 6px;
`;

const SnapshotDate = styled.div`
  font-size: 14px;
  color: ${({ theme }) =>
    theme.mode === 'dark' ? 'rgba(180, 180, 190, 0.8)' : 'rgba(100, 100, 100, 0.8)'};
  display: flex;
  align-items: center;
  gap: 4px;

  svg {
    width: 14px;
    height: 14px;
    opacity: 0.7;
  }
`;

const SnapshotSize = styled.div`
  font-size: 14px;
  color: ${({ theme }) =>
    theme.mode === 'dark' ? 'rgba(180, 180, 190, 0.8)' : 'rgba(100, 100, 100, 0.8)'};
  display: flex;
  align-items: center;
  gap: 4px;

  svg {
    width: 14px;
    height: 14px;
    opacity: 0.7;
  }
`;

const SnapshotDescription = styled.div`
  font-size: 14px;
  color: ${({ theme }) =>
    theme.mode === 'dark' ? 'rgba(180, 180, 190, 0.8)' : 'rgba(100, 100, 100, 0.8)'};
  margin-top: 6px;
  line-height: 1.4;
`;

const SnapshotActions = styled.div`
  display: flex;
  gap: 8px;
`;

const SnapshotButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.primary.main};
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  padding: 6px 10px;
  border-radius: 6px;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${({ theme }) =>
      theme.mode === 'dark' ? 'rgba(74, 144, 226, 0.15)' : 'rgba(74, 144, 226, 0.08)'};
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${({ theme }) => `${theme.colors.primary.main}40`};
  }
`;

/**
 * Data Management Component
 */
const DataManagement: React.FC<DataManagementProps> = ({ isOpen, onClose }) => {
  const { theme } = useTheme();
  const {
    status,
    error,
    exportData,
    importData,
    createSnapshot,
    getSnapshots,
    restoreSnapshot,
    deleteSnapshot,
  } = useDataPortability();

  const [activeTab, setActiveTab] = useState<'export' | 'import' | 'snapshots'>('export');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [exportSection, setExportSection] = useState<DataSection>('all');
  const [exportFormat, setExportFormat] = useState<ExportFormat>('json');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [statusMessage, setStatusMessage] = useState<string | null>(null);
  const [snapshotsList, setSnapshotsList] = useState<Snapshot[]>([]);
  const [snapshotName, setSnapshotName] = useState<string>('');
  const [snapshotDescription, setSnapshotDescription] = useState<string>('');

  const fileInputRef = React.useRef<HTMLInputElement>(null);

  // Animation variants
  const tabContentVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.3 },
    },
  };

  const buttonVariants = {
    hover: {
      y: -2,
      boxShadow:
        theme.mode === 'dark'
          ? '0 6px 16px rgba(0, 0, 0, 0.4)'
          : '0 6px 16px rgba(74, 144, 226, 0.3)',
    },
    tap: {
      y: 0,
      boxShadow:
        theme.mode === 'dark'
          ? '0 4px 12px rgba(0, 0, 0, 0.3)'
          : '0 4px 12px rgba(74, 144, 226, 0.2)',
    },
  };

  const listItemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.3,
      },
    }),
  };

  // Load snapshots on mount and when tab changes
  useEffect(() => {
    if (activeTab === 'snapshots') {
      loadSnapshots();
    }
  }, [activeTab]);

  // Update status message when status changes
  useEffect(() => {
    if (status === 'exporting') {
      setIsLoading(true);
      setStatusMessage('Exporting data...');
    } else if (status === 'importing') {
      setIsLoading(true);
      setStatusMessage('Importing data...');
    } else if (status === 'success') {
      setIsLoading(false);
      setStatusMessage('Operation completed successfully');

      // Clear status message after 3 seconds
      const timer = setTimeout(() => {
        setStatusMessage(null);
      }, 3000);

      return () => clearTimeout(timer);
    } else if (status === 'error') {
      setIsLoading(false);
      setStatusMessage(error || 'An error occurred');
    } else {
      setIsLoading(false);
      setStatusMessage(null);
    }
  }, [status, error]);

  // Load snapshots
  const loadSnapshots = async () => {
    try {
      setIsLoading(true);
      const snapshots = await getSnapshots();

      // Convert to Snapshot type
      const formattedSnapshots: Snapshot[] = snapshots.map((snapshot: any) => ({
        id: snapshot.id,
        name: snapshot.name,
        date: new Date(snapshot.date).toLocaleString(),
        description: snapshot.description,
        size: calculateSize(snapshot.data),
      }));

      setSnapshotsList(formattedSnapshots);
    } catch (error) {
      console.error('Error loading snapshots:', error);
      setStatusMessage('Failed to load snapshots');
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate size of data
  const calculateSize = (data: any): string => {
    const jsonString = JSON.stringify(data);
    const bytes = new Blob([jsonString]).size;

    if (bytes < 1024) {
      return `${bytes} B`;
    } else if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(1)} KB`;
    } else {
      return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    }
  };

  // Handle export data
  const handleExportData = async () => {
    try {
      setIsLoading(true);
      setStatusMessage(`Exporting ${exportSection} data...`);

      await exportData(exportSection, exportFormat);

      setStatusMessage('Data exported successfully');
      setTimeout(() => setStatusMessage(null), 3000);
    } catch (error) {
      console.error('Error exporting data:', error);
      setStatusMessage('Failed to export data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleImportClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0]);
    }
  };

  const handleImportData = async () => {
    if (!selectedFile) return;

    try {
      setIsLoading(true);
      setStatusMessage(`Importing data from ${selectedFile.name}...`);

      // Import data
      const result = await importData(selectedFile, {
        conflictResolution: 'merge',
        validate: true,
      });

      if (result.success) {
        setStatusMessage('Data imported successfully. Updating progress status...');
        setSelectedFile(null);

        // Force a hard reload to ensure all contexts are properly refreshed
        // This ensures the progress tracking is updated correctly
        setTimeout(() => {
          // Use location.href instead of location.reload() for a complete refresh
          window.location.href = window.location.href.split('#')[0];
        }, 1500);
      } else {
        setStatusMessage(`Import failed: ${result.message}`);

        if (result.errors && result.errors.length > 0) {
          console.error('Import errors:', result.errors);
        }
      }
    } catch (error) {
      console.error('Error importing data:', error);
      setStatusMessage('Failed to import data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateSnapshot = async () => {
    try {
      setIsLoading(true);
      setStatusMessage('Creating snapshot...');

      // Use default name if none provided
      const name = snapshotName || `Snapshot ${new Date().toLocaleString()}`;
      const description = snapshotDescription || 'Snapshot of current app state';

      // Create snapshot
      await createSnapshot(name, description);

      // Reset form
      setSnapshotName('');
      setSnapshotDescription('');

      // Reload snapshots
      await loadSnapshots();

      setStatusMessage('Snapshot created successfully');
    } catch (error) {
      console.error('Error creating snapshot:', error);
      setStatusMessage('Failed to create snapshot');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRestoreSnapshot = async (id: string) => {
    // Confirm before restoring
    if (
      !window.confirm(
        'Are you sure you want to restore this snapshot? This will replace your current data.'
      )
    ) {
      return;
    }

    try {
      setIsLoading(true);
      setStatusMessage('Restoring snapshot...');

      // Restore snapshot
      const success = await restoreSnapshot(id);

      if (success) {
        setStatusMessage('Snapshot restored successfully');

        // Reload the page after a short delay to reflect the restored data
        setTimeout(() => {
          window.location.reload();
        }, 1500);
      } else {
        setStatusMessage('Failed to restore snapshot');
      }
    } catch (error) {
      console.error('Error restoring snapshot:', error);
      setStatusMessage('Failed to restore snapshot');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteSnapshot = async (id: string) => {
    // Confirm before deleting
    if (
      !window.confirm(
        'Are you sure you want to delete this snapshot? This action cannot be undone.'
      )
    ) {
      return;
    }

    try {
      setIsLoading(true);
      setStatusMessage('Deleting snapshot...');

      // Delete snapshot
      const success = await deleteSnapshot(id);

      if (success) {
        // Reload snapshots
        await loadSnapshots();
        setStatusMessage('Snapshot deleted successfully');
      } else {
        setStatusMessage('Failed to delete snapshot');
      }
    } catch (error) {
      console.error('Error deleting snapshot:', error);
      setStatusMessage('Failed to delete snapshot');
    } finally {
      setIsLoading(false);
    }
  };

  // Define additional styled components
  const StatusMessage = styled.div<{ isError?: boolean }>`
    margin-top: 16px;
    padding: 12px;
    border-radius: 6px;
    background-color: ${({ isError, theme }) =>
      isError
        ? theme.mode === 'dark'
          ? 'rgba(244, 67, 54, 0.15)'
          : 'rgba(244, 67, 54, 0.08)'
        : theme.mode === 'dark'
          ? 'rgba(76, 175, 80, 0.15)'
          : 'rgba(76, 175, 80, 0.08)'};
    color: ${({ isError, theme }) =>
      isError
        ? theme.mode === 'dark'
          ? '#ff6b6b'
          : '#d32f2f'
        : theme.mode === 'dark'
          ? '#81c784'
          : '#388e3c'};
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
  `;

  const OptionGroup = styled.div`
    margin-bottom: 20px;
  `;

  const OptionLabel = styled.div`
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
    color: ${({ theme }) => (theme.mode === 'dark' ? theme.colors.text.primary : '#333')};
  `;

  const RadioGroup = styled.div`
    display: flex;
    gap: 16px;
    margin-bottom: 12px;
  `;

  const RadioOption = styled.label`
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    font-size: 14px;
    color: ${({ theme }) =>
      theme.mode === 'dark' ? 'rgba(200, 200, 210, 0.9)' : 'rgba(60, 60, 60, 0.9)'};
  `;

  const Radio = styled.input`
    cursor: pointer;
  `;

  const InputField = styled.input`
    width: 100%;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid
      ${({ theme }) =>
        theme.mode === 'dark' ? 'rgba(80, 80, 90, 0.6)' : 'rgba(200, 200, 200, 0.8)'};
    background-color: ${({ theme }) =>
      theme.mode === 'dark' ? 'rgba(40, 40, 50, 0.3)' : 'rgba(255, 255, 255, 0.8)'};
    color: ${({ theme }) => (theme.mode === 'dark' ? theme.colors.text.primary : '#333')};
    margin-bottom: 12px;
    font-size: 14px;

    &:focus {
      outline: none;
      border-color: ${({ theme }) => theme.colors.primary.main};
      box-shadow: 0 0 0 2px ${({ theme }) => `${theme.colors.primary.main}20`};
    }
  `;

  const LoadingOverlay = styled(motion.div)`
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: ${({ theme }) =>
      theme.mode === 'dark' ? 'rgba(20, 20, 30, 0.7)' : 'rgba(255, 255, 255, 0.7)'};
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 10;
    border-radius: 8px;
  `;

  const LoadingSpinner = styled(motion.div)`
    width: 40px;
    height: 40px;
    border: 3px solid ${({ theme }) => theme.colors.primary.light};
    border-top-color: ${({ theme }) => theme.colors.primary.main};
    border-radius: 50%;
  `;

  const LoadingText = styled.div`
    margin-top: 16px;
    font-size: 16px;
    font-weight: 500;
    color: ${({ theme }) => (theme.mode === 'dark' ? theme.colors.text.primary : '#333')};
  `;

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Data Management" width="600px">
      <Container>
        <TabsContainer>
          <Tab isActive={activeTab === 'export'} onClick={() => setActiveTab('export')}>
            Export Data
          </Tab>
          <Tab isActive={activeTab === 'import'} onClick={() => setActiveTab('import')}>
            Import Data
          </Tab>
          <Tab isActive={activeTab === 'snapshots'} onClick={() => setActiveTab('snapshots')}>
            Snapshots
          </Tab>
        </TabsContainer>

        <TabContent
          key={activeTab}
          initial="hidden"
          animate="visible"
          variants={tabContentVariants}
        >
          {activeTab === 'export' && (
            <>
              <InfoBox>
                <InfoTitle>
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM17 13l-5 5-5-5h3V9h4v4h3z" />
                  </svg>
                  Export App Data
                </InfoTitle>
                <InfoText>
                  This will export your Financial Compass and Life Journey data as a JSON file. You
                  can use this file to backup your data or transfer it to another device. The export
                  includes all forms from both sections with 100% accuracy.
                </InfoText>
              </InfoBox>

              <OptionGroup>
                <OptionLabel>What to Export</OptionLabel>
                <RadioGroup>
                  <RadioOption>
                    <Radio
                      type="radio"
                      name="exportSection"
                      value="all"
                      checked={exportSection === 'all'}
                      onChange={() => setExportSection('all')}
                    />
                    All Data
                  </RadioOption>
                  <RadioOption>
                    <Radio
                      type="radio"
                      name="exportSection"
                      value="financialCompass"
                      checked={exportSection === 'financialCompass'}
                      onChange={() => setExportSection('financialCompass')}
                    />
                    Financial Compass Only
                  </RadioOption>
                  <RadioOption>
                    <Radio
                      type="radio"
                      name="exportSection"
                      value="seasonsOfSelf"
                      checked={exportSection === 'seasonsOfSelf'}
                      onChange={() => setExportSection('seasonsOfSelf')}
                    />
                    Life Journey Only
                  </RadioOption>
                </RadioGroup>
              </OptionGroup>

              <OptionGroup>
                <OptionLabel>Export Format</OptionLabel>
                <RadioGroup>
                  <RadioOption>
                    <Radio
                      type="radio"
                      name="exportFormat"
                      value="json"
                      checked={exportFormat === 'json'}
                      onChange={() => setExportFormat('json')}
                    />
                    JSON (Recommended)
                  </RadioOption>
                  <RadioOption>
                    <Radio
                      type="radio"
                      name="exportFormat"
                      value="pdf"
                      checked={exportFormat === 'pdf'}
                      onChange={() => setExportFormat('pdf')}
                      disabled={true}
                    />
                    PDF (Coming Soon)
                  </RadioOption>
                </RadioGroup>
              </OptionGroup>

              <ActionButton
                onClick={handleExportData}
                whileHover="hover"
                whileTap="tap"
                variants={buttonVariants}
                disabled={isLoading}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" />
                </svg>
                {isLoading
                  ? 'Exporting...'
                  : `Export ${exportSection === 'all' ? 'All' : exportSection === 'financialCompass' ? 'Financial Compass' : 'Life Journey'} Data`}
              </ActionButton>
            </>
          )}

          {activeTab === 'import' && (
            <>
              <InfoBox>
                <InfoTitle>
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path
                      d="M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM17 13l-5 5-5-5h3V9h4v4h3z"
                      transform="rotate(180, 12, 12)"
                    />
                  </svg>
                  Import App Data
                </InfoTitle>
                <InfoText>
                  Import previously exported LifeCompass data with 100% accuracy. This will merge
                  with your current data. The system will validate all forms from both Financial
                  Compass and Life Journey sections. Make sure to create a snapshot of your current
                  data before importing.
                </InfoText>
              </InfoBox>

              <FileInput
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                accept=".json"
              />

              <ActionButton
                onClick={handleImportClick}
                whileHover="hover"
                whileTap="tap"
                variants={buttonVariants}
                disabled={isLoading}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path
                    d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"
                    transform="rotate(180, 12, 12)"
                  />
                </svg>
                {selectedFile ? `Selected: ${selectedFile.name}` : 'Select File to Import'}
              </ActionButton>

              {selectedFile && (
                <ActionButton
                  onClick={handleImportData}
                  style={{ marginTop: '10px' }}
                  whileHover="hover"
                  whileTap="tap"
                  variants={buttonVariants}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  disabled={isLoading}
                >
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z" />
                  </svg>
                  {isLoading ? 'Importing...' : 'Import Data'}
                </ActionButton>
              )}
            </>
          )}

          {activeTab === 'snapshots' && (
            <>
              <InfoBox>
                <InfoTitle>
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9 3H15L17 5H21C22.1 5 23 5.9 23 7V19C23 20.1 22.1 21 21 21H3C1.9 21 1 20.1 1 19V7C1 5.9 1.9 5 3 5H7L9 3ZM12 18C15.31 18 18 15.31 18 12C18 8.69 15.31 6 12 6C8.69 6 6 8.69 6 12C6 15.31 8.69 18 12 18ZM12 8C14.21 8 16 9.79 16 12C16 14.21 14.21 16 12 16C9.79 16 8 14.21 8 12C8 9.79 9.79 8 12 8Z" />
                  </svg>
                  App Snapshots
                </InfoTitle>
                <InfoText>
                  Snapshots allow you to save the current state of your app data and restore it
                  later. Each snapshot captures all forms from both Financial Compass and Life
                  Journey sections with 100% accuracy. This is useful before making significant
                  changes to your financial plans or life journey entries.
                </InfoText>
              </InfoBox>

              <OptionGroup>
                <OptionLabel>Create New Snapshot</OptionLabel>
                <InputField
                  type="text"
                  placeholder="Snapshot Name"
                  value={snapshotName}
                  onChange={(e) => setSnapshotName(e.target.value)}
                  disabled={isLoading}
                />
                <InputField
                  type="text"
                  placeholder="Description (optional)"
                  value={snapshotDescription}
                  onChange={(e) => setSnapshotDescription(e.target.value)}
                  disabled={isLoading}
                />
              </OptionGroup>

              <ActionButton
                onClick={handleCreateSnapshot}
                whileHover="hover"
                whileTap="tap"
                variants={buttonVariants}
                disabled={isLoading}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M9 3H15L17 5H21C22.1 5 23 5.9 23 7V19C23 20.1 22.1 21 21 21H3C1.9 21 1 20.1 1 19V7C1 5.9 1.9 5 3 5H7L9 3ZM12 18C15.31 18 18 15.31 18 12C18 8.69 15.31 6 12 6C8.69 6 6 8.69 6 12C6 15.31 8.69 18 12 18ZM12 8C14.21 8 16 9.79 16 12C16 14.21 14.21 16 12 16C9.79 16 8 14.21 8 12C8 9.79 9.79 8 12 8Z" />
                </svg>
                {isLoading ? 'Creating...' : 'Create New Snapshot'}
              </ActionButton>

              <OptionLabel style={{ marginTop: '20px', marginBottom: '12px' }}>
                Your Snapshots
              </OptionLabel>

              {snapshotsList.length === 0 ? (
                <InfoText style={{ textAlign: 'center', padding: '20px 0' }}>
                  No snapshots found. Create your first snapshot to save your current data.
                </InfoText>
              ) : (
                <SnapshotList>
                  {snapshotsList.map((snapshot, index) => (
                    <SnapshotItem
                      key={snapshot.id}
                      custom={index}
                      initial="hidden"
                      animate="visible"
                      variants={listItemVariants}
                    >
                      <SnapshotInfo>
                        <SnapshotName>{snapshot.name}</SnapshotName>
                        <SnapshotMeta>
                          <SnapshotDate>
                            <svg viewBox="0 0 24 24" fill="currentColor">
                              <path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z" />
                            </svg>
                            {snapshot.date}
                          </SnapshotDate>
                          <SnapshotSize>
                            <svg viewBox="0 0 24 24" fill="currentColor">
                              <path d="M2 20h20v-4H2v4zm2-3h2v2H4v-2zM2 4v4h20V4H2zm4 3H4V5h2v2zm-4 7h20v-4H2v4zm2-3h2v2H4v-2z" />
                            </svg>
                            {snapshot.size}
                          </SnapshotSize>
                        </SnapshotMeta>
                        <SnapshotDescription>{snapshot.description}</SnapshotDescription>
                      </SnapshotInfo>
                      <SnapshotActions>
                        <SnapshotButton
                          onClick={() => handleRestoreSnapshot(snapshot.id)}
                          disabled={isLoading}
                        >
                          Restore
                        </SnapshotButton>
                        <SnapshotButton
                          onClick={() => handleDeleteSnapshot(snapshot.id)}
                          style={{ color: theme.mode === 'dark' ? '#ff5252' : '#e53935' }}
                          disabled={isLoading}
                        >
                          Delete
                        </SnapshotButton>
                      </SnapshotActions>
                    </SnapshotItem>
                  ))}
                </SnapshotList>
              )}
            </>
          )}

          {statusMessage && (
            <StatusMessage isError={status === 'error'}>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                {status === 'error' ? (
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z" />
                ) : (
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                )}
              </svg>
              {statusMessage}
            </StatusMessage>
          )}
        </TabContent>

        {isLoading && (
          <LoadingOverlay initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            <LoadingSpinner
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
            />
            <LoadingText>Processing...</LoadingText>
          </LoadingOverlay>
        )}
      </Container>
    </Modal>
  );
};

export default DataManagement;
