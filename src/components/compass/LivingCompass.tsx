import React from 'react';
import styled from 'styled-components';
import { useTheme, SimpleTheme } from '../../../src/theme/SimpleThemeProvider';
import { Season } from '../../../src/theme/SimpleThemeProvider';

const CompassContainer = styled.div`
  position: relative;
  width: 100%;
  padding-bottom: 100%; /* Maintain aspect ratio */
  border-radius: 50%;
  background-color: ${({ theme }) => theme.colors.neutral.surface};
  box-shadow: ${({ theme }) => theme.shadows.md};
  overflow: hidden;
`;

// Create a separate function to get the background gradient
const getBackgroundGradient = (theme: SimpleTheme, season: Season): string => {
  // Get the background color based on theme mode
  const bgColor =
    theme.mode === 'light'
      ? theme.colors.seasons[season].background.light
      : theme.colors.seasons[season].background.dark;
  const primaryColor = theme.colors.seasons[season].primary;

  // Return the gradient string
  return `radial-gradient(
     circle,
     ${bgColor} 0%,
     ${primaryColor} 100%
   )`;
};

const CompassInner = styled.div<{ season: Season }>`
  position: absolute;
  top: 5%;
  left: 5%;
  right: 5%;
  bottom: 5%;
  border-radius: 50%;
  background: ${({ theme, season }) => {
    // Cast theme to SimpleTheme to fix type issue
    const simpleTheme = theme as unknown as SimpleTheme;
    return getBackgroundGradient(simpleTheme, season);
  }};
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.5s ease;
`;

const CompassCenter = styled.div`
  position: absolute;
  top: 35%;
  left: 35%;
  right: 35%;
  bottom: 35%;
  border-radius: 50%;
  background-color: ${({ theme }) => theme.colors.neutral.surface};
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: ${({ theme }) => theme.shadows.sm};
  z-index: 2;
`;

const CompassCenterText = styled.span`
  font-family: ${({ theme }) => theme.typography.fontFamily.primary};
  font-size: ${({ theme }) => theme.typography.fontSize.h2};
  font-weight: ${({ theme }) => theme.typography.fontWeight.bold};
  color: ${({ theme }) => theme.colors.primary.main};
`;

const DirectionMarker = styled.div<{ direction: 'north' | 'east' | 'south' | 'west' }>`
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20%;
  height: 20%;
  border-radius: 50%;
  background-color: ${({ theme }) => theme.colors.neutral.surface};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  z-index: 1;

  ${({ direction }) => {
    switch (direction) {
      case 'north':
        return 'top: 0; left: 40%;';
      case 'east':
        return 'top: 40%; right: 0;';
      case 'south':
        return 'bottom: 0; left: 40%;';
      case 'west':
        return 'top: 40%; left: 0;';
    }
  }}
`;

const DirectionText = styled.span`
  font-family: ${({ theme }) => theme.typography.fontFamily.tertiary};
  font-size: ${({ theme }) => theme.typography.fontSize.bodySmall};
  font-weight: ${({ theme }) => theme.typography.fontWeight.bold};
  color: ${({ theme }) => theme.colors.primary.main};
`;

const CompassRing = styled.div`
  position: absolute;
  top: 15%;
  left: 15%;
  right: 15%;
  bottom: 15%;
  border-radius: 50%;
  border: 2px dashed ${({ theme }) => theme.colors.neutral.divider};
  z-index: 1;
`;

const LivingCompass: React.FC = () => {
  const { theme } = useTheme();
  const currentSeason = theme.season || 'spring';

  return (
    <CompassContainer>
      <CompassInner season={currentSeason}>
        <CompassRing />

        <DirectionMarker direction="north">
          <DirectionText>NORTH</DirectionText>
        </DirectionMarker>

        <DirectionMarker direction="east">
          <DirectionText>EAST</DirectionText>
        </DirectionMarker>

        <DirectionMarker direction="south">
          <DirectionText>SOUTH</DirectionText>
        </DirectionMarker>

        <DirectionMarker direction="west">
          <DirectionText>WEST</DirectionText>
        </DirectionMarker>

        <CompassCenter>
          <CompassCenterText>LC</CompassCenterText>
        </CompassCenter>
      </CompassInner>
    </CompassContainer>
  );
};

export default LivingCompass;
