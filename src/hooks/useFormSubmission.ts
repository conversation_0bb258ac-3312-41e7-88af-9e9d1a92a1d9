import { useState, useCallback } from 'react';
import { ValidationResult } from '../utils/validationUtils';

type FormSubmitHandler<T> = (values: T) => Promise<{ success: boolean; error?: string }>;

export const useFormSubmission = <T extends object>(
  initialValues: T,
  onSubmit: FormSubmitHandler<T>,
  validate?: (values: T) => Record<string, string> | null
) => {
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  // Handle input changes
  const handleChange = useCallback(
    (name: string, value: any) => {
      setValues((prev) => ({
        ...prev,
        [name]: value,
      }));

      // Clear error for the field being edited
      if (errors[name]) {
        setErrors((prev) => {
          const newErrors = { ...prev };
          delete newErrors[name];
          return newErrors;
        });
      }
    },
    [errors]
  );

  // Handle form submission
  const handleSubmit = useCallback(
    async (e?: React.FormEvent) => {
      e?.preventDefault();
      setSubmitError(null);
      setSubmitSuccess(false);

      // Run validation if provided
      if (validate) {
        const validationErrors = validate(values);
        if (validationErrors) {
          setErrors(validationErrors);
          return { success: false, errors: validationErrors };
        }
      }

      setIsSubmitting(true);

      try {
        const result = await onSubmit(values);

        if (result.success) {
          setSubmitSuccess(true);
          // Optionally reset form on success
          // setValues(initialValues);
          return { success: true };
        } else {
          setSubmitError(result.error || 'Submission failed. Please try again.');
          return { success: false, error: result.error };
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
        setSubmitError(errorMessage);
        return { success: false, error: errorMessage };
      } finally {
        setIsSubmitting(false);
      }
    },
    [onSubmit, validate, values]
  );

  // Reset form to initial values
  const resetForm = useCallback(() => {
    setValues(initialValues);
    setErrors({});
    setSubmitError(null);
    setSubmitSuccess(false);
  }, [initialValues]);

  // Set field value directly
  const setFieldValue = useCallback(<K extends keyof T>(field: K, value: T[K]) => {
    setValues((prev) => ({
      ...prev,
      [field]: value,
    }));
  }, []);

  // Set field error directly
  const setFieldError = useCallback((field: string, error: string) => {
    setErrors((prev) => ({
      ...prev,
      [field]: error,
    }));
  }, []);

  // Validate a single field
  const validateField = useCallback(
    (field: string, value: any) => {
      if (!validate) return true;

      const validationErrors = validate({ ...values, [field]: value });
      const fieldError = validationErrors?.[field];

      if (fieldError) {
        setErrors((prev) => ({
          ...prev,
          [field]: fieldError,
        }));
        return false;
      }

      // Clear error if validation passes
      if (errors[field]) {
        setErrors((prev) => {
          const newErrors = { ...prev };
          delete newErrors[field];
          return newErrors;
        });
      }

      return true;
    },
    [validate, values, errors]
  );

  // Handle blur event for validation
  const handleBlur = useCallback(
    (field: string, value: any) => {
      if (validate) {
        validateField(field, value);
      }
    },
    [validate, validateField]
  );

  // Get field props for form inputs
  const getFieldProps = useCallback(
    (name: string) => ({
      name,
      value: values[name as keyof T] as any,
      onChange: (value: any) => handleChange(name, value),
      onBlur: () => handleBlur(name, values[name as keyof T]),
      error: errors[name],
    }),
    [values, errors, handleChange, handleBlur]
  );

  return {
    // Form state
    values,
    errors,
    isSubmitting,
    submitError,
    submitSuccess,

    // Form actions
    handleChange,
    handleSubmit,
    handleBlur,
    resetForm,
    setFieldValue,
    setFieldError,
    validateField,
    getFieldProps,

    // State setters
    setValues,
    setErrors,
    setIsSubmitting,
    setSubmitError,
    setSubmitSuccess,
  };
};

export default useFormSubmission;
