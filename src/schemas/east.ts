import { z } from 'zod';
import { dateSchema, currencySchema, percentageSchema, validationMessages } from './base';

// Retirement Account Schema
export const retirementAccountSchema = z.object({
  type: z.enum(['401k', '403b', 'IRA', 'Roth IRA', 'Pension', 'Other']),
  balance: currencySchema.optional(),
  employerMatch: percentageSchema.optional(),
  contribution: currencySchema.optional(),
  contributionFrequency: z.enum(['weekly', 'bi-weekly', 'monthly', 'annually']).default('monthly'),
  expectedGrowthRate: percentageSchema.optional(),
  institution: z.string().optional(),
  accountNumber: z.string().optional(),
  beneficiary: z.string().optional(),
});

// Social Security Benefit Schema
export const socialSecurityBenefitSchema = z.object({
  estimatedMonthlyBenefit: currencySchema.optional(),
  fullRetirementAge: z.number().min(62).max(70).optional(),
  plannedClaimingAge: z.number().min(62).max(70).optional(),
  estimatedAnnualIncrease: percentageSchema.optional(),
  lastUpdated: dateSchema.optional(),
});

// Retirement Goal Schema
export const retirementGoalSchema = z.object({
  targetRetirementAge: z.number().min(30).max(100).optional(),
  desiredAnnualIncome: currencySchema.optional(),
  currentSavings: currencySchema.optional(),
  monthlyContribution: currencySchema.optional(),
  expectedReturnRate: percentageSchema.optional(),
  inflationRate: percentageSchema.optional(),
  lifeExpectancy: z.number().min(65).max(120).optional(),
  includeSocialSecurity: z.boolean().default(true),
  includePension: z.boolean().default(false),
  lastUpdated: dateSchema.optional(),
});

// Retirement Expense Schema
export const retirementExpenseSchema = z.object({
  housing: currencySchema.optional(),
  healthcare: currencySchema.optional(),
  food: currencySchema.optional(),
  transportation: currencySchema.optional(),
  travel: currencySchema.optional(),
  entertainment: currencySchema.optional(),
  gifts: currencySchema.optional(),
  other: currencySchema.optional(),
  annualInflationRate: percentageSchema.optional(),
  lastUpdated: dateSchema.optional(),
});

// East Direction Schema
export const eastDirectionSchema = z.object({
  retirementAccounts: z.array(retirementAccountSchema).optional(),
  socialSecurity: socialSecurityBenefitSchema.optional(),
  retirementGoals: retirementGoalSchema.optional(),
  retirementExpenses: retirementExpenseSchema.optional(),
  lastUpdated: dateSchema.optional(),
});

// Type exports
export type RetirementAccount = z.infer<typeof retirementAccountSchema>;
export type SocialSecurityBenefit = z.infer<typeof socialSecurityBenefitSchema>;
export type RetirementGoal = z.infer<typeof retirementGoalSchema>;
export type RetirementExpense = z.infer<typeof retirementExpenseSchema>;
export type EastDirectionData = z.infer<typeof eastDirectionSchema>;
