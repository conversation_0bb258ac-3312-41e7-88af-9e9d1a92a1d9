import { z } from 'zod';

// Common reusable schemas
export const dateSchema = z
  .union([z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format'), z.date()])
  .transform((str) => new Date(str));

export const currencySchema = z
  .number()
  .min(0, 'Amount must be positive')
  .finite('Amount must be a finite number');

export const percentageSchema = z
  .number()
  .min(0, 'Percentage must be at least 0')
  .max(100, 'Percentage cannot exceed 100');

// Common validation messages
export const validationMessages = {
  required: 'This field is required',
  invalidEmail: 'Please enter a valid email address',
  invalidPhone: 'Please enter a valid phone number',
  invalidSSN: 'Please enter a valid SSN (XXX-XX-XXXX)',
  invalidZipCode: 'Please enter a valid ZIP code',
  invalidDate: 'Please enter a valid date (YYYY-MM-DD)',
  invalidUrl: 'Please enter a valid URL',
  positiveNumber: 'Must be a positive number',
  minLength: (min: number) => `Must be at least ${min} characters`,
  maxLength: (max: number) => `Cannot exceed ${max} characters`,
};
