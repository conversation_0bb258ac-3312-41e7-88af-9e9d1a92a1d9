// Main schema exports
import { z } from 'zod';
import { northDirectionSchema, NorthDirectionData } from './north';
import { eastDirectionSchema, EastDirectionData } from './east';
import { southDirectionSchema, SouthDirectionData } from './south';
import { westDirectionSchema, WestDirectionData } from './west';

// Re-export all schemas and types
export * from './base';
export * from './north';
export * from './east';
export * from './south';
export * from './west';

// Main financial compass schema
export const financialCompassSchema = z.object({
  north: northDirectionSchema.optional(),
  east: eastDirectionSchema.optional(),
  south: southDirectionSchema.optional(),
  west: westDirectionSchema.optional(),
  metadata: z
    .object({
      version: z.string().default('1.0.0'),
      lastUpdated: z.date().or(z.string()).optional(),
      userId: z.string().optional(),
    })
    .optional(),
});

// Type for the complete financial compass data
export type FinancialCompassData = z.infer<typeof financialCompassSchema>;

// Validation result type
export type ValidationResult<T> =
  | { success: true; data: T }
  | { success: false; errors: Record<string, string[]> };

/**
 * Validates financial compass data against the schema
 */
export function validateFinancialCompassData(
  data: unknown
): ValidationResult<FinancialCompassData> {
  const result = financialCompassSchema.safeParse(data);

  if (result.success) {
    return { success: true, data: result.data };
  }

  // Format Zod errors into a more usable format
  const errors: Record<string, string[]> = {};

  result.error.errors.forEach((error) => {
    const path = error.path.join('.');
    if (!errors[path]) {
      errors[path] = [];
    }
    errors[path].push(error.message);
  });

  return { success: false, errors };
}

/**
 * Creates a deep partial version of a type for updates
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

/**
 * Creates a partial schema for updates
 */
export function createPartialSchema<T extends z.ZodTypeAny>(
  schema: T
): z.ZodType<Partial<z.infer<T>>> {
  return schema.partial();
}

// Create partial schemas for each direction
export const partialNorthDirectionSchema = createPartialSchema(northDirectionSchema);
export const partialEastDirectionSchema = createPartialSchema(eastDirectionSchema);
export const partialSouthDirectionSchema = createPartialSchema(southDirectionSchema);
export const partialWestDirectionSchema = createPartialSchema(westDirectionSchema);

// Type for partial updates
export type PartialNorthDirectionData = z.infer<typeof partialNorthDirectionSchema>;
export type PartialEastDirectionData = z.infer<typeof partialEastDirectionSchema>;
export type PartialSouthDirectionData = z.infer<typeof partialSouthDirectionSchema>;
export type PartialWestDirectionData = z.infer<typeof partialWestDirectionSchema>;

// Utility function to merge partial updates with existing data
export function mergeWithExisting<T extends object>(existing: T, updates: DeepPartial<T>): T {
  const result = { ...existing };

  for (const key in updates) {
    const value = updates[key];

    if (value !== undefined) {
      if (value && typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {
        // @ts-ignore - TypeScript can't infer the type here
        result[key] = mergeWithExisting(result[key] || {}, value);
      } else {
        // @ts-ignore - TypeScript can't infer the type here
        result[key] = value;
      }
    }
  }

  return result;
}
