import { z } from 'zod';
import { dateSchema, currencySchema, percentageSchema, validationMessages } from './base';

// Income Schema
export const incomeSchema = z.object({
  salary: currencySchema.optional(),
  bonus: currencySchema.optional(),
  otherIncome: currencySchema.optional(),
  frequency: z.enum(['weekly', 'bi-weekly', 'monthly', 'annually']).optional(),
  lastUpdated: dateSchema.optional(),
});

// Expenses Schema
export const expensesSchema = z.object({
  housing: currencySchema.optional(),
  utilities: currencySchema.optional(),
  food: currencySchema.optional(),
  transportation: currencySchema.optional(),
  healthcare: currencySchema.optional(),
  debtPayments: currencySchema.optional(),
  entertainment: currencySchema.optional(),
  other: currencySchema.optional(),
  frequency: z.enum(['weekly', 'bi-weekly', 'monthly', 'annually']).default('monthly'),
});

// Assets Schema
export const assetsSchema = z.object({
  cash: currencySchema.optional(),
  checkingAccounts: currencySchema.optional(),
  savingsAccounts: currencySchema.optional(),
  investmentAccounts: currencySchema.optional(),
  retirementAccounts: currencySchema.optional(),
  realEstate: currencySchema.optional(),
  vehicles: currencySchema.optional(),
  otherAssets: currencySchema.optional(),
  lastUpdated: dateSchema.optional(),
});

// Liabilities Schema
export const liabilitiesSchema = z.object({
  creditCards: currencySchema.optional(),
  studentLoans: currencySchema.optional(),
  mortgages: currencySchema.optional(),
  carLoans: currencySchema.optional(),
  personalLoans: currencySchema.optional(),
  otherDebt: currencySchema.optional(),
  interestRates: z.record(z.number().min(0).max(1)).optional(),
  minimumPayments: z.record(currencySchema).optional(),
  lastUpdated: dateSchema.optional(),
});

// Net Worth Schema
export const netWorthSchema = z
  .object({
    totalAssets: currencySchema.optional(),
    totalLiabilities: currencySchema.optional(),
    netWorth: currencySchema.optional(),
    lastUpdated: dateSchema.optional(),
  })
  .refine(
    (data) => {
      if (
        data.totalAssets !== undefined &&
        data.totalLiabilities !== undefined &&
        data.netWorth !== undefined
      ) {
        return data.netWorth === data.totalAssets - data.totalLiabilities;
      }
      return true;
    },
    {
      message: 'Net worth must equal total assets minus total liabilities',
      path: ['netWorth'],
    }
  );

// North Direction Schema
export const northDirectionSchema = z.object({
  income: incomeSchema.optional(),
  expenses: expensesSchema.optional(),
  assets: assetsSchema.optional(),
  liabilities: liabilitiesSchema.optional(),
  netWorth: netWorthSchema.optional(),
  lastUpdated: dateSchema.optional(),
});

// Type exports
export type Income = z.infer<typeof incomeSchema>;
export type Expenses = z.infer<typeof expensesSchema>;
export type Assets = z.infer<typeof assetsSchema>;
export type Liabilities = z.infer<typeof liabilitiesSchema>;
export type NetWorth = z.infer<typeof netWorthSchema>;
export type NorthDirectionData = z.infer<typeof northDirectionSchema>;
