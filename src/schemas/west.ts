import { z } from 'zod';
import { dateSchema, currencySchema, percentageSchema, validationMessages } from './base';

// Legacy Goal Schema
export const legacyGoalSchema = z.object({
  goal: z.string(),
  targetAmount: currencySchema.optional(),
  currentAmount: currencySchema.optional(),
  targetDate: dateSchema.optional(),
  priority: z.enum(['high', 'medium', 'low']).default('medium'),
  notes: z.string().optional(),
  lastUpdated: dateSchema.optional(),
});

// Charitable Giving Schema
export const charitableGivingSchema = z.object({
  organization: z.string(),
  taxId: z.string().optional(),
  donationAmount: currencySchema.optional(),
  frequency: z.enum(['one-time', 'monthly', 'quarterly', 'annually']).default('one-time'),
  isRecurring: z.boolean().default(false),
  paymentMethod: z.string().optional(),
  notes: z.string().optional(),
  lastUpdated: dateSchema.optional(),
});

// Beneficiary Schema
export const beneficiarySchema = z.object({
  name: z.string(),
  relationship: z.string().optional(),
  percentage: percentageSchema.optional(),
  type: z.enum(['primary', 'contingent']).default('primary'),
  contactInfo: z.string().optional(),
  notes: z.string().optional(),
  lastUpdated: dateSchema.optional(),
});

// Digital Assets Schema
export const digitalAssetSchema = z.object({
  name: z.string(),
  type: z.enum(['email', 'social-media', 'cloud-storage', 'cryptocurrency', 'website', 'other']),
  url: z.string().url().optional(),
  username: z.string().optional(),
  instructions: z.string().optional(),
  lastUpdated: dateSchema.optional(),
});

// West Direction Schema
export const westDirectionSchema = z.object({
  legacyGoals: z.array(legacyGoalSchema).optional(),
  charitableGiving: z.array(charitableGivingSchema).optional(),
  beneficiaries: z.array(beneficiarySchema).optional(),
  digitalAssets: z.array(digitalAssetSchema).optional(),
  ethicalWill: z.string().optional(),
  lastUpdated: dateSchema.optional(),
});

// Type exports
export type LegacyGoal = z.infer<typeof legacyGoalSchema>;
export type CharitableGiving = z.infer<typeof charitableGivingSchema>;
export type Beneficiary = z.infer<typeof beneficiarySchema>;
export type DigitalAsset = z.infer<typeof digitalAssetSchema>;
export type WestDirectionData = z.infer<typeof westDirectionSchema>;
