import { z } from 'zod';
import { dateSchema, currencySchema, percentageSchema, validationMessages } from './base';

// Insurance Policy Schema
export const insurancePolicySchema = z.object({
  type: z.enum([
    'life',
    'health',
    'disability',
    'long-term-care',
    'homeowners',
    'auto',
    'umbrella',
    'other',
  ]),
  provider: z.string().optional(),
  policyNumber: z.string().optional(),
  coverageAmount: currencySchema.optional(),
  premium: currencySchema.optional(),
  premiumFrequency: z.enum(['monthly', 'quarterly', 'semi-annually', 'annually']).optional(),
  startDate: dateSchema.optional(),
  endDate: dateSchema.optional(),
  beneficiaries: z.array(z.string()).optional(),
  notes: z.string().optional(),
  lastUpdated: dateSchema.optional(),
});

// Emergency Fund Schema
export const emergencyFundSchema = z.object({
  targetAmount: currencySchema.optional(),
  currentAmount: currencySchema.optional(),
  monthlyContribution: currencySchema.optional(),
  monthsOfExpenses: z.number().min(0).optional(),
  lastUpdated: dateSchema.optional(),
});

// Risk Assessment Schema
export const riskAssessmentSchema = z.object({
  riskTolerance: z.enum(['conservative', 'moderate', 'aggressive']).optional(),
  riskCapacity: z.enum(['low', 'medium', 'high']).optional(),
  timeHorizon: z.number().min(1).optional(),
  financialKnowledge: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
  lastUpdated: dateSchema.optional(),
});

// Estate Planning Schema
export const estatePlanningSchema = z.object({
  will: z.boolean().default(false),
  trust: z.boolean().default(false),
  powerOfAttorney: z.boolean().default(false),
  healthcareDirective: z.boolean().default(false),
  executor: z.string().optional(),
  attorney: z.string().optional(),
  notes: z.string().optional(),
  lastUpdated: dateSchema.optional(),
});

// South Direction Schema
export const southDirectionSchema = z.object({
  insurancePolicies: z.array(insurancePolicySchema).optional(),
  emergencyFund: emergencyFundSchema.optional(),
  riskAssessment: riskAssessmentSchema.optional(),
  estatePlanning: estatePlanningSchema.optional(),
  lastUpdated: dateSchema.optional(),
});

// Type exports
export type InsurancePolicy = z.infer<typeof insurancePolicySchema>;
export type EmergencyFund = z.infer<typeof emergencyFundSchema>;
export type RiskAssessment = z.infer<typeof riskAssessmentSchema>;
export type EstatePlanning = z.infer<typeof estatePlanningSchema>;
export type SouthDirectionData = z.infer<typeof southDirectionSchema>;
