import React from 'react';
import { HashRouter as Router, Routes, Route, Link } from 'react-router-dom';

// Simple Home component
const Home = () => (
  <div style={{ padding: '20px' }}>
    <h1>Home Page</h1>
    <p>Welcome to the LifeCompass app!</p>
  </div>
);

// Simple About component
const About = () => (
  <div style={{ padding: '20px' }}>
    <h1>About Page</h1>
    <p>This is a simplified version of the LifeCompass app for debugging.</p>
  </div>
);

// Simple Navigation component
const Navigation = () => (
  <nav
    style={{
      backgroundColor: '#333',
      padding: '10px',
      display: 'flex',
      justifyContent: 'space-between',
    }}
  >
    <div>
      <Link to="/" style={{ color: 'white', marginRight: '15px', textDecoration: 'none' }}>
        Home
      </Link>
      <Link to="/about" style={{ color: 'white', textDecoration: 'none' }}>
        About
      </Link>
    </div>
    <div>
      <span style={{ color: 'white' }}>LifeCompass</span>
    </div>
  </nav>
);

// Main App component with router
const App: React.FC = () => {
  console.log('App.router component rendering');

  return (
    <Router>
      <div
        style={{
          fontFamily: 'Arial, sans-serif',
          minHeight: '100vh',
          backgroundColor: '#f5f5f5',
        }}
      >
        <Navigation />
        <div style={{ padding: '20px' }}>
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/about" element={<About />} />
          </Routes>
        </div>
      </div>
    </Router>
  );
};

export default App;
