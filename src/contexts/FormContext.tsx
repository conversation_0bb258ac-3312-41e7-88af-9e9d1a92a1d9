import React, { createContext, useContext, useReducer, useCallback, useMemo } from 'react';
import { useForm, FormProvider as RHFProvider, UseFormReturn } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

type FormValues = Record<string, any>;
type FormErrors = Record<string, any>;
type FormStatus = 'idle' | 'submitting' | 'success' | 'error';

interface FormState {
  values: FormValues;
  errors: FormErrors;
  isSubmitting: boolean;
  status: FormStatus;
  activeStep: number;
  totalSteps: number;
}

type FormAction =
  | { type: 'SET_VALUES'; payload: FormValues }
  | { type: 'SET_FIELD_VALUE'; field: string; value: any }
  | { type: 'SET_ERRORS'; payload: FormErrors }
  | { type: 'SET_IS_SUBMITTING'; isSubmitting: boolean }
  | { type: 'SET_STATUS'; status: FormStatus }
  | { type: 'SET_ACTIVE_STEP'; step: number }
  | { type: 'NEXT_STEP' }
  | { type: 'PREV_STEP' };

interface FormContextType extends FormState {
  formMethods: UseFormReturn<FormValues>;
  setValues: (values: FormValues) => void;
  setFieldValue: (field: string, value: any) => void;
  setErrors: (errors: FormErrors) => void;
  setStatus: (status: FormStatus) => void;
  nextStep: () => void;
  prevStep: () => void;
  goToStep: (step: number) => void;
  isFirstStep: boolean;
  isLastStep: boolean;
  submitForm: () => Promise<void>;
}

const FormContext = createContext<FormContextType | undefined>(undefined);

function formReducer(state: FormState, action: FormAction): FormState {
  switch (action.type) {
    case 'SET_VALUES':
      return { ...state, values: { ...state.values, ...action.payload } };

    case 'SET_FIELD_VALUE':
      return {
        ...state,
        values: { ...state.values, [action.field]: action.value },
      };

    case 'SET_ERRORS':
      return { ...state, errors: { ...action.payload } };

    case 'SET_IS_SUBMITTING':
      return { ...state, isSubmitting: action.isSubmitting };

    case 'SET_STATUS':
      return { ...state, status: action.status };

    case 'SET_ACTIVE_STEP':
      return { ...state, activeStep: Math.min(Math.max(0, action.step), state.totalSteps - 1) };

    case 'NEXT_STEP':
      return { ...state, activeStep: Math.min(state.activeStep + 1, state.totalSteps - 1) };

    case 'PREV_STEP':
      return { ...state, activeStep: Math.max(0, state.activeStep - 1) };

    default:
      return state;
  }
}

interface FormProviderProps {
  defaultValues?: FormValues;
  validationSchema?: yup.AnyObjectSchema;
  totalSteps?: number;
  onSubmit?: (values: FormValues) => Promise<void> | void;
  children: React.ReactNode;
}

export const FormProvider: React.FC<FormProviderProps> = ({
  defaultValues = {},
  validationSchema,
  totalSteps = 1,
  onSubmit,
  children,
}) => {
  const formMethods = useForm<FormValues>({
    defaultValues,
    resolver: validationSchema ? yupResolver(validationSchema) : undefined,
    mode: 'onChange',
  });

  const [state, dispatch] = useReducer(formReducer, {
    values: defaultValues,
    errors: {},
    isSubmitting: false,
    status: 'idle',
    activeStep: 0,
    totalSteps,
  });

  const setValues = useCallback((values: FormValues) => {
    dispatch({ type: 'SET_VALUES', payload: values });
  }, []);

  const setFieldValue = useCallback((field: string, value: any) => {
    dispatch({ type: 'SET_FIELD_VALUE', field, value });
  }, []);

  const setErrors = useCallback((errors: FormErrors) => {
    dispatch({ type: 'SET_ERRORS', payload: errors });
  }, []);

  const setStatus = useCallback((status: FormStatus) => {
    dispatch({ type: 'SET_STATUS', status });
  }, []);

  const nextStep = useCallback(() => {
    dispatch({ type: 'NEXT_STEP' });
  }, []);

  const prevStep = useCallback(() => {
    dispatch({ type: 'PREV_STEP' });
  }, []);

  const goToStep = useCallback((step: number) => {
    dispatch({ type: 'SET_ACTIVE_STEP', step });
  }, []);

  const submitForm = useCallback(async () => {
    if (!onSubmit) return;

    try {
      dispatch({ type: 'SET_IS_SUBMITTING', isSubmitting: true });
      dispatch({ type: 'SET_STATUS', status: 'submitting' });

      await onSubmit(state.values);

      dispatch({ type: 'SET_STATUS', status: 'success' });
    } catch (error) {
      console.error('Form submission error:', error);
      dispatch({ type: 'SET_STATUS', status: 'error' });
      throw error;
    } finally {
      dispatch({ type: 'SET_IS_SUBMITTING', isSubmitting: false });
    }
  }, [onSubmit, state.values]);

  const isFirstStep = state.activeStep === 0;
  const isLastStep = state.activeStep === state.totalSteps - 1;

  const contextValue = useMemo<FormContextType>(
    () => ({
      ...state,
      formMethods,
      setValues,
      setFieldValue,
      setErrors,
      setStatus,
      nextStep,
      prevStep,
      goToStep,
      isFirstStep,
      isLastStep,
      submitForm,
    }),
    [
      state,
      formMethods,
      setValues,
      setFieldValue,
      setErrors,
      setStatus,
      nextStep,
      prevStep,
      goToStep,
      isFirstStep,
      isLastStep,
      submitForm,
    ]
  );

  return (
    <FormContext.Provider value={contextValue}>
      <RHFProvider {...formMethods}>{children}</RHFProvider>
    </FormContext.Provider>
  );
};

export const useFormContext = (): FormContextType => {
  const context = useContext(FormContext);
  if (!context) {
    throw new Error('useFormContext must be used within a FormProvider');
  }
  return context;
};
