import {
  calculateFutureValue,
  calculateRetirementIncomeNeeded,
  calculateRetirementSavingsNeeded,
  calculateRetirementReadiness,
  calculateRetirementIncomeDuration,
} from './retirementCalculator';
import {
  calculatePIA,
  calculateAdjustedBenefit,
  calculateFullRetirementAge,
  formatFullRetirementAge,
} from './socialSecurityCalculator';

interface RetirementPlanningInput {
  currentAge: number;
  retirementAge: number;
  lifeExpectancy: number;
  currentIncome: number;
  currentSavings: number;
  monthlyContribution: number;
  expectedReturnRate: number;
  inflationRate: number;
  socialSecurityEarnings: number[]; // Last 35 years of earnings
  filingStatus: 'single' | 'married' | 'headOfHousehold';
  spouseSocialSecurityEarnings?: number[]; // For spousal benefits
}

export interface RetirementPlan {
  // Inputs
  currentAge: number;
  retirementAge: number;
  lifeExpectancy: number;
  yearsUntilRetirement: number;
  retirementDuration: number;

  // Income and Savings
  currentIncome: number;
  currentSavings: number;
  monthlyContribution: number;
  annualContribution: number;
  expectedReturnRate: number;
  inflationRate: number;

  // Social Security
  fullRetirementAge: string;
  primaryInsuranceAmount: number;
  socialSecurityBenefit: number;

  // Projections
  projectedSavingsAtRetirement: number;
  requiredRetirementIncome: number;
  requiredRetirementSavings: number;
  safeWithdrawalRate: number;
  annualSafeWithdrawal: number;
  monthlySafeWithdrawal: number;

  // Analysis
  readinessScore: number;
  yearsOfSavings: number;
  monthlyIncomeGap: number;
  incomeGapPercentage: number;

  // Recommendations
  recommendations: {
    title: string;
    description: string;
    priority: 'High' | 'Medium' | 'Low';
    action?: string;
  }[];
}

export const generateRetirementPlan = (input: RetirementPlanningInput): RetirementPlan => {
  const {
    currentAge,
    retirementAge,
    currentIncome,
    currentSavings,
    monthlyContribution,
    expectedReturnRate,
    inflationRate,
    socialSecurityEarnings,
  } = input;

  // Calculate years until retirement
  const yearsUntilRetirement = retirementAge - currentAge;

  // Calculate required retirement income (80% of current income adjusted for inflation)
  const requiredRetirementIncome = currentIncome * 0.8 * Math.pow(1 + inflationRate, yearsUntilRetirement);

  // Calculate projected savings at retirement
  const annualContribution = monthlyContribution * 12;
  const projectedSavingsAtRetirement = calculateFutureValue(
    currentSavings,
    monthlyContribution,
    expectedReturnRate,
    yearsUntilRetirement
  );

  // Calculate safe withdrawal rate (4% rule)
  const safeWithdrawalRate = 0.04;

  // Calculate safe withdrawal amount
  const annualSafeWithdrawal = projectedSavingsAtRetirement * safeWithdrawalRate;
  const monthlySafeWithdrawal = annualSafeWithdrawal / 12;

  // Calculate income gap
  const monthlyIncomeGap = requiredRetirementIncome / 12 - monthlySafeWithdrawal;
  const incomeGapPercentage = requiredRetirementIncome > 0 ? (monthlyIncomeGap / (requiredRetirementIncome / 12)) * 100 : 0;

  // Calculate retirement readiness score
  const readinessScore = calculateRetirementReadiness(
    currentSavings,
    monthlyContribution,
    expectedReturnRate,
    yearsUntilRetirement,
    requiredRetirementIncome
  );

  // Calculate years of savings at current spending
  const yearsOfSavings = calculateRetirementIncomeDuration(
    projectedSavingsAtRetirement,
    requiredRetirementIncome,
    expectedReturnRate,
    inflationRate
  );

  // Generate recommendations
  const recommendations = [];

  // Income gap recommendation
  if (incomeGapPercentage > 10) {
    recommendations.push({
      title: 'Close Your Retirement Income Gap',
      description: `You're projected to have a ${Math.round(incomeGapPercentage)}% income gap in retirement. Consider increasing your savings rate or adjusting your retirement lifestyle expectations.`,
      priority: incomeGapPercentage > 30 ? 'High' : 'Medium',
      action: 'increase_savings',
    });
  }

  return {
    // Inputs
    currentAge,
    retirementAge,
    lifeExpectancy: 0, // Assuming life expectancy is not provided in the input
    yearsUntilRetirement,
    retirementDuration: 0, // Assuming retirement duration is not provided in the input

    // Income and Savings
    currentIncome,
    currentSavings,
    monthlyContribution,
    annualContribution,
    expectedReturnRate,
    inflationRate,

    // Social Security
    fullRetirementAge: '', // Assuming full retirement age is not provided in the input
    primaryInsuranceAmount: 0, // Assuming primary insurance amount is not provided in the input
    socialSecurityBenefit: 0, // Assuming social security benefit is not provided in the input

    // Projections
    projectedSavingsAtRetirement,
    requiredRetirementIncome,
    requiredRetirementSavings: 0, // Assuming required retirement savings is not provided in the input
    safeWithdrawalRate,
    annualSafeWithdrawal,
    monthlySafeWithdrawal,

    // Analysis
    readinessScore,
    yearsOfSavings,
    monthlyIncomeGap,
    incomeGapPercentage,

    // Recommendations
    recommendations,
  };
};

// Helper function to format currency with proper localization
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

// Helper function to format percentage
export const formatPercentage = (value: number, decimals: number = 1): string => {
  return `${value.toFixed(decimals)}%`;
};

/**
 * Calculates the retirement savings gap.
 *
 * @param currentSavings - Current retirement savings.
 * @param savingsGoal - The target retirement savings goal.
 * @param yearsUntilRetirement - Number of years until retirement.
 * @param expectedReturnRate - Expected annual return rate on investments (as a decimal).
 * @returns The savings gap (positive if goal is not met, 0 if met or exceeded).
 */
export const calculateSavingsGap = (
  currentSavings: number,
  savingsGoal: number,
  yearsUntilRetirement: number,
  expectedReturnRate: number
): number => {
  // Project current savings to retirement age
  // This is a simplified projection, not accounting for future contributions
  // A more accurate calculation would use calculateFutureValue with contributions
  const projectedCurrentSavings = currentSavings * Math.pow(1 + expectedReturnRate, yearsUntilRetirement);

  // Calculate the gap between the goal and the projected savings
  const gap = savingsGoal - projectedCurrentSavings;

  return Math.max(0, gap); // Return 0 if projected savings meet or exceed the goal
};
