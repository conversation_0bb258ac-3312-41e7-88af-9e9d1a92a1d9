import {
  getTaxOptimizationStrategies,
  calculateOptimalRetirementContribution,
  compareTaxScenarios,
} from './taxOptimization';

describe('Tax Optimization Utility', () => {
  describe('getTaxOptimizationStrategies', () => {
    it('should return strategies for maximizing 401(k) contributions', () => {
      const userData = {
        income: 100000,
        filingStatus: 'single' as const,
        age: 35,
        retirementAccounts: {
          traditionalIRA: 0,
          rothIRA: 0,
          employer401k: 10000,
          employer401kMatch: 5000,
        },
        investments: {
          taxableBrokerage: 50000,
          capitalGains: 10000,
        },
        deductions: {
          itemized: 5000,
          standard: 13850,
        },
        dependents: 0,
        hsaEligible: true,
        hsaContribution: 2000,
        year: 2023,
      };

      const strategies = getTaxOptimizationStrategies(userData);
      expect(strategies.length).toBeGreaterThan(0);
      expect(strategies.some((s) => s.id === 'max-401k')).toBe(true);
    });

    it('should return strategies for IRA contributions', () => {
      const userData = {
        income: 80000,
        filingStatus: 'married' as const,
        age: 40,
        retirementAccounts: {
          traditionalIRA: 2000,
          rothIRA: 0,
          employer401k: 15000,
        },
        investments: {
          taxableBrokerage: 30000,
          capitalGains: 5000,
        },
        deductions: {
          itemized: 15000,
          standard: 27700,
        },
        dependents: 2,
        hsaEligible: false,
        hsaContribution: 0,
        year: 2023,
      };

      const strategies = getTaxOptimizationStrategies(userData);
      expect(strategies.some((s) => s.id === 'max-ira')).toBe(true);
    });
  });

  describe('calculateOptimalRetirementContribution', () => {
    it('should calculate optimal 401(k) contribution', () => {
      const result = calculateOptimalRetirementContribution(100000, 'single', 10000, '401k');

      expect(result.recommendedContribution).toBeGreaterThan(0);
      expect(result.taxSavings).toBeGreaterThan(0);
      expect(result.recommendedContribution).toBeLessThanOrEqual(22500 - 10000);
    });

    it('should not recommend more than 25% of income', () => {
      const result = calculateOptimalRetirementContribution(40000, 'single', 0, '401k');

      expect(result.recommendedContribution).toBe(10000); // 25% of 40,000
    });
  });

  describe('compareTaxScenarios', () => {
    it('should compare different tax scenarios', () => {
      const scenarios = [
        {
          name: 'Current',
          income: 100000,
          deductions: 12000,
          credits: 0,
          filingStatus: 'single' as const,
        },
        {
          name: 'With 401(k) Max',
          income: 100000,
          deductions: 12000 + 19500, // Additional 401(k) contribution
          credits: 0,
          filingStatus: 'single' as const,
        },
      ];

      const comparison = compareTaxScenarios(scenarios);
      expect(comparison.length).toBe(2);
      expect(comparison[0].taxLiability).toBeGreaterThan(comparison[1].taxLiability);
    });
  });
});
