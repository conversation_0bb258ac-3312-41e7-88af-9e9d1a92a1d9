/**
 * PDF Export Utility
 *
 * This utility provides functions for generating PDF reports from financial data.
 * It uses jsPDF and jspdf-autotable libraries.
 *
 * Note: This requires the following dependencies:
 * - jspdf
 * - jspdf-autotable
 */

// Import dependencies
import jsPDF from 'jspdf';
import 'jspdf-autotable';
// import { formatCurrency, formatDate } from './formatters';

/**
 * Generate a financial summary PDF
 * @param data - Financial data from FinancialCompass
 * @param userName - User's name for the report
 * @param reportDate - Date of the report (defaults to current date)
 * @returns PDF document as a blob URL
 */
export const generateFinancialSummaryPDF = (
  data: any,
  userName: string,
  reportDate: Date = new Date()
): string => {
  try {
    // Create a new PDF document
    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.width;

    // Add report title
    doc.setFontSize(20);
    doc.setTextColor(0, 51, 102);
    doc.text('Financial Compass Summary', pageWidth / 2, 20, { align: 'center' });

    // Add report subtitle with date
    doc.setFontSize(12);
    doc.setTextColor(102, 102, 102);
    const formattedDate = reportDate.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
    doc.text(`Prepared for: ${userName} | Date: ${formattedDate}`, pageWidth / 2, 30, {
      align: 'center',
    });

    // Add horizontal line
    doc.setLineWidth(0.5);
    doc.line(20, 35, pageWidth - 20, 35);

    // Extract financial data
    const {
      totalMonthlyIncome = 0,
      totalMonthlyExpenses = 0,
      totalAnnualIncome = 0,
      totalAnnualExpenses = 0,
      totalAssets = 0,
      totalLiabilities = 0,
    } = data.north || {};

    // Calculate derived values
    const monthlyCashFlow = totalMonthlyIncome - totalMonthlyExpenses;
    const annualCashFlow = totalAnnualIncome - totalAnnualExpenses;
    const netWorth = totalAssets - totalLiabilities;
    const savingsRate = totalMonthlyIncome > 0 ? (monthlyCashFlow / totalMonthlyIncome) * 100 : 0;

    // Format currency values
    const formatCurrency = (value: number): string => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(value);
    };

    // Add financial summary section
    doc.setFontSize(16);
    doc.setTextColor(0, 51, 102);
    doc.text('Financial Summary', 20, 50);

    // Create financial summary table
    const summaryData = [
      ['Monthly Income', formatCurrency(totalMonthlyIncome)],
      ['Monthly Expenses', formatCurrency(totalMonthlyExpenses)],
      ['Monthly Cash Flow', formatCurrency(monthlyCashFlow)],
      ['Annual Income', formatCurrency(totalAnnualIncome)],
      ['Annual Expenses', formatCurrency(totalAnnualExpenses)],
      ['Annual Cash Flow', formatCurrency(annualCashFlow)],
      ['Savings Rate', `${savingsRate.toFixed(1)}%`],
      ['Total Assets', formatCurrency(totalAssets)],
      ['Total Liabilities', formatCurrency(totalLiabilities)],
      ['Net Worth', formatCurrency(netWorth)],
    ];

    // Add summary table
    doc.autoTable({
      startY: 55,
      head: [['Metric', 'Value']],
      body: summaryData,
      theme: 'striped',
      headStyles: {
        fillColor: [0, 51, 102],
        textColor: [255, 255, 255],
        fontStyle: 'bold',
      },
      alternateRowStyles: {
        fillColor: [240, 240, 240],
      },
      styles: {
        cellPadding: 5,
      },
    });

    // Add income breakdown section
    const incomeTableY = (doc as any).lastAutoTable.finalY + 15;
    doc.setFontSize(16);
    doc.setTextColor(0, 51, 102);
    doc.text('Income Breakdown', 20, incomeTableY);

    // Extract income data
    const incomeSources = data.north?.income || {};
    const incomeData = Object.entries(incomeSources).map(
      ([sourceId, sourceData]: [string, any]) => {
        const monthlyAmount = sourceData.amount || 0;
        const percentage = totalMonthlyIncome > 0 ? (monthlyAmount / totalMonthlyIncome) * 100 : 0;

        return [
          sourceData.source || sourceId,
          formatCurrency(monthlyAmount),
          `${percentage.toFixed(1)}%`,
        ];
      }
    );

    // Add income table
    if (incomeData.length > 0) {
      doc.autoTable({
        startY: incomeTableY + 5,
        head: [['Source', 'Monthly Amount', 'Percentage']],
        body: incomeData,
        theme: 'striped',
        headStyles: {
          fillColor: [0, 102, 51],
          textColor: [255, 255, 255],
          fontStyle: 'bold',
        },
        alternateRowStyles: {
          fillColor: [240, 240, 240],
        },
        styles: {
          cellPadding: 5,
        },
      });
    } else {
      doc.setFontSize(12);
      doc.setTextColor(102, 102, 102);
      doc.text('No income data available', 20, incomeTableY + 10);
    }

    // Add expense breakdown section
    const expenseTableY = (doc as any).lastAutoTable?.finalY + 15 || incomeTableY + 30;
    doc.setFontSize(16);
    doc.setTextColor(0, 51, 102);
    doc.text('Expense Breakdown', 20, expenseTableY);

    // Extract expense data
    const expenses = data.north?.expenses || {};
    const expenseData = Object.entries(expenses)
      .map(([categoryId, categoryData]: [string, any]) => {
        const categoryTotal = Object.values(categoryData).reduce(
          (sum: number, value: any) => sum + (value || 0),
          0
        );
        const percentage =
          totalMonthlyExpenses > 0 ? (categoryTotal / totalMonthlyExpenses) * 100 : 0;

        return [
          getCategoryLabel(categoryId),
          formatCurrency(categoryTotal),
          `${percentage.toFixed(1)}%`,
        ];
      })
      .filter(([_, amount]) => parseFloat(amount.replace(/[^0-9.-]+/g, '')) > 0);

    // Helper function to get category label
    const getCategoryLabel = (categoryId: string): string => {
      const categoryLabels: Record<string, string> = {
        housing: 'Housing',
        transportation: 'Transportation',
        food: 'Food',
        healthcare: 'Healthcare',
        personal: 'Personal',
        debt: 'Debt Payments',
        other: 'Other Expenses',
      };

      return categoryLabels[categoryId] || categoryId;
    };

    // Add expense table
    if (expenseData.length > 0) {
      doc.autoTable({
        startY: expenseTableY + 5,
        head: [['Category', 'Monthly Amount', 'Percentage']],
        body: expenseData,
        theme: 'striped',
        headStyles: {
          fillColor: [153, 0, 0],
          textColor: [255, 255, 255],
          fontStyle: 'bold',
        },
        alternateRowStyles: {
          fillColor: [240, 240, 240],
        },
        styles: {
          cellPadding: 5,
        },
      });
    } else {
      doc.setFontSize(12);
      doc.setTextColor(102, 102, 102);
      doc.text('No expense data available', 20, expenseTableY + 10);
    }

    // Add assets breakdown section
    const assetsTableY = (doc as any).lastAutoTable?.finalY + 15 || expenseTableY + 30;

    // Check if we need a new page
    if (assetsTableY > doc.internal.pageSize.height - 40) {
      doc.addPage();
      doc.setFontSize(16);
      doc.setTextColor(0, 51, 102);
      doc.text('Assets Breakdown', 20, 20);
    } else {
      doc.setFontSize(16);
      doc.setTextColor(0, 51, 102);
      doc.text('Assets Breakdown', 20, assetsTableY);
    }

    // Extract assets data
    const assets = data.north?.assets || {};
    const assetsData = Object.entries(assets)
      .map(([categoryId, categoryData]: [string, any]) => {
        const categoryTotal = Object.values(categoryData).reduce(
          (sum: number, value: any) => sum + (value || 0),
          0
        );
        const percentage = totalAssets > 0 ? (categoryTotal / totalAssets) * 100 : 0;

        return [
          getAssetCategoryLabel(categoryId),
          formatCurrency(categoryTotal),
          `${percentage.toFixed(1)}%`,
        ];
      })
      .filter(([_, amount]) => parseFloat(amount.replace(/[^0-9.-]+/g, '')) > 0);

    // Helper function to get asset category label
    const getAssetCategoryLabel = (categoryId: string): string => {
      const categoryLabels: Record<string, string> = {
        cash: 'Cash & Equivalents',
        investments: 'Investments',
        realEstate: 'Real Estate',
        personal: 'Personal Property',
        other: 'Other Assets',
      };

      return categoryLabels[categoryId] || categoryId;
    };

    // Add assets table
    const assetsStartY = assetsTableY > doc.internal.pageSize.height - 40 ? 25 : assetsTableY + 5;

    if (assetsData.length > 0) {
      doc.autoTable({
        startY: assetsStartY,
        head: [['Category', 'Value', 'Percentage']],
        body: assetsData,
        theme: 'striped',
        headStyles: {
          fillColor: [0, 102, 153],
          textColor: [255, 255, 255],
          fontStyle: 'bold',
        },
        alternateRowStyles: {
          fillColor: [240, 240, 240],
        },
        styles: {
          cellPadding: 5,
        },
      });
    } else {
      doc.setFontSize(12);
      doc.setTextColor(102, 102, 102);
      doc.text('No assets data available', 20, assetsStartY + 5);
    }

    // Add liabilities breakdown section
    const liabilitiesTableY = (doc as any).lastAutoTable?.finalY + 15 || assetsStartY + 30;

    // Check if we need a new page
    if (liabilitiesTableY > doc.internal.pageSize.height - 40) {
      doc.addPage();
      doc.setFontSize(16);
      doc.setTextColor(0, 51, 102);
      doc.text('Liabilities Breakdown', 20, 20);
    } else {
      doc.setFontSize(16);
      doc.setTextColor(0, 51, 102);
      doc.text('Liabilities Breakdown', 20, liabilitiesTableY);
    }

    // Extract liabilities data
    const liabilities = data.north?.liabilities || {};
    const liabilitiesData = Object.entries(liabilities)
      .map(([categoryId, categoryData]: [string, any]) => {
        const categoryTotal = Object.values(categoryData).reduce(
          (sum: number, value: any) => sum + (value || 0),
          0
        );
        const percentage = totalLiabilities > 0 ? (categoryTotal / totalLiabilities) * 100 : 0;

        return [
          getLiabilityCategoryLabel(categoryId),
          formatCurrency(categoryTotal),
          `${percentage.toFixed(1)}%`,
        ];
      })
      .filter(([_, amount]) => parseFloat(amount.replace(/[^0-9.-]+/g, '')) > 0);

    // Helper function to get liability category label
    const getLiabilityCategoryLabel = (categoryId: string): string => {
      const categoryLabels: Record<string, string> = {
        mortgage: 'Mortgages',
        loans: 'Loans',
        creditCards: 'Credit Cards',
        other: 'Other Debts',
      };

      return categoryLabels[categoryId] || categoryId;
    };

    // Add liabilities table
    const liabilitiesStartY =
      liabilitiesTableY > doc.internal.pageSize.height - 40 ? 25 : liabilitiesTableY + 5;

    if (liabilitiesData.length > 0) {
      doc.autoTable({
        startY: liabilitiesStartY,
        head: [['Category', 'Value', 'Percentage']],
        body: liabilitiesData,
        theme: 'striped',
        headStyles: {
          fillColor: [153, 51, 0],
          textColor: [255, 255, 255],
          fontStyle: 'bold',
        },
        alternateRowStyles: {
          fillColor: [240, 240, 240],
        },
        styles: {
          cellPadding: 5,
        },
      });
    } else {
      doc.setFontSize(12);
      doc.setTextColor(102, 102, 102);
      doc.text('No liabilities data available', 20, liabilitiesStartY + 5);
    }

    // Add footer with date and page numbers
    // Use the pages array length instead of getNumberOfPages()
    const totalPages = doc.internal.pages.length - 1; // -1 because jsPDF adds an empty first page

    for (let i = 1; i <= totalPages; i++) {
      doc.setPage(i);
      doc.setFontSize(10);
      doc.setTextColor(102, 102, 102);
      doc.text(
        `Generated on ${formattedDate} | Page ${i} of ${totalPages}`,
        pageWidth / 2,
        doc.internal.pageSize.height - 10,
        { align: 'center' }
      );
    }

    // Return the PDF as a blob URL
    return doc.output('dataurlstring');
  } catch (error) {
    console.error('Error generating PDF:', error);
    return '';
  }
};

/**
 * Download a financial summary PDF
 * @param data - Financial data from FinancialCompass
 * @param userName - User's name for the report
 * @param reportDate - Date of the report (defaults to current date)
 */
export const downloadFinancialSummaryPDF = (
  data: any,
  userName: string,
  reportDate: Date = new Date()
): void => {
  try {
    // Create a new PDF document
    const doc = new jsPDF();

    // Generate the PDF content
    generateFinancialSummaryPDF(data, userName, reportDate);

    // Generate filename
    const formattedDate = reportDate.toISOString().split('T')[0];
    const fileName = `financial_summary_${formattedDate}.pdf`;

    // Download the PDF
    doc.save(fileName);
  } catch (error) {
    console.error('Error downloading PDF:', error);
  }
};
