/**
 * Retirement Income Calculator Utility
 *
 * This utility provides standardized retirement income source calculations to ensure
 * consistent calculations across retirement income sources.
 */

/**
 * Income frequency types
 */
export enum IncomeFrequency {
  MONTHLY = 'monthly',
  ANNUAL = 'annual',
  ONE_TIME = 'one-time',
  BIWEEKLY = 'biweekly',
  WEEKLY = 'weekly',
}

/**
 * Income source type
 */
export interface IncomeSource {
  id: string;
  type: string;
  amount: number | string;
  frequency: IncomeFrequency;
  startAge: number | string;
  endAge?: number | string;
  notes?: string;
  inflationAdjusted?: boolean;
}

/**
 * Social Security benefit
 */
export interface SocialSecurityBenefit {
  estimatedMonthlyBenefit: number | string;
  startAge: number | string;
  inflationAdjusted?: boolean;
}

/**
 * Pension benefit
 */
export interface PensionBenefit {
  hasEmployerPension: boolean;
  estimatedMonthlyBenefit: number | string;
  startAge: number | string;
  inflationAdjusted?: boolean;
}

/**
 * Convert income to monthly amount
 *
 * @param amount - Income amount
 * @param frequency - Income frequency
 * @returns Monthly amount
 */
export const convertToMonthly = (amount: number | string, frequency: IncomeFrequency): number => {
  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

  if (isNaN(numericAmount)) return 0;

  switch (frequency) {
    case IncomeFrequency.MONTHLY:
      return numericAmount;
    case IncomeFrequency.ANNUAL:
      return numericAmount / 12;
    case IncomeFrequency.BIWEEKLY:
      return (numericAmount * 26) / 12;
    case IncomeFrequency.WEEKLY:
      return (numericAmount * 52) / 12;
    case IncomeFrequency.ONE_TIME:
      // One-time amounts are not included in monthly calculations
      return 0;
    default:
      return numericAmount;
  }
};

/**
 * Convert income to annual amount
 *
 * @param amount - Income amount
 * @param frequency - Income frequency
 * @returns Annual amount
 */
export const convertToAnnual = (amount: number | string, frequency: IncomeFrequency): number => {
  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

  if (isNaN(numericAmount)) return 0;

  switch (frequency) {
    case IncomeFrequency.MONTHLY:
      return numericAmount * 12;
    case IncomeFrequency.ANNUAL:
      return numericAmount;
    case IncomeFrequency.BIWEEKLY:
      return numericAmount * 26;
    case IncomeFrequency.WEEKLY:
      return numericAmount * 52;
    case IncomeFrequency.ONE_TIME:
      // One-time amounts are not included in annual calculations
      return 0;
    default:
      return numericAmount;
  }
};

/**
 * Calculate total monthly income from all sources
 *
 * @param socialSecurity - Social Security benefit
 * @param pension - Pension benefit
 * @param incomeSources - Other income sources
 * @param currentAge - Current age
 * @returns Total monthly income at different ages
 */
export const calculateTotalMonthlyIncome = (
  socialSecurity: SocialSecurityBenefit,
  pension: PensionBenefit,
  incomeSources: IncomeSource[],
  currentAge: number | string
): {
  totalMonthly: number;
  byAge: Record<number, number>;
  incomeBreakdown: Record<string, number>;
} => {
  const currentAgeNum = typeof currentAge === 'string' ? parseInt(currentAge) : currentAge;
  if (isNaN(currentAgeNum)) return { totalMonthly: 0, byAge: {}, incomeBreakdown: {} };

  // Initialize income by age and breakdown
  const byAge: Record<number, number> = {};
  const incomeBreakdown: Record<string, number> = {
    'Social Security': 0,
    Pension: 0,
    'Other Income': 0,
  };

  // Calculate Social Security income
  const ssStartAge =
    typeof socialSecurity.startAge === 'string'
      ? parseInt(socialSecurity.startAge)
      : socialSecurity.startAge;

  const ssMonthlyAmount = convertToMonthly(
    socialSecurity.estimatedMonthlyBenefit,
    IncomeFrequency.MONTHLY
  );

  if (!isNaN(ssStartAge) && ssMonthlyAmount > 0) {
    incomeBreakdown['Social Security'] = ssMonthlyAmount;

    // Add Social Security income to all ages after start age
    for (let age = ssStartAge; age <= 100; age++) {
      byAge[age] = (byAge[age] || 0) + ssMonthlyAmount;
    }
  }

  // Calculate Pension income if applicable
  if (pension.hasEmployerPension) {
    const pensionStartAge =
      typeof pension.startAge === 'string' ? parseInt(pension.startAge) : pension.startAge;

    const pensionMonthlyAmount = convertToMonthly(
      pension.estimatedMonthlyBenefit,
      IncomeFrequency.MONTHLY
    );

    if (!isNaN(pensionStartAge) && pensionMonthlyAmount > 0) {
      incomeBreakdown['Pension'] = pensionMonthlyAmount;

      // Add Pension income to all ages after start age
      for (let age = pensionStartAge; age <= 100; age++) {
        byAge[age] = (byAge[age] || 0) + pensionMonthlyAmount;
      }
    }
  }

  // Calculate other income sources
  incomeSources.forEach((source) => {
    const startAge =
      typeof source.startAge === 'string' ? parseInt(source.startAge) : source.startAge;

    const endAge = source.endAge
      ? typeof source.endAge === 'string'
        ? parseInt(source.endAge)
        : source.endAge
      : 100; // Default to age 100 if no end age specified

    const monthlyAmount = convertToMonthly(source.amount, source.frequency as IncomeFrequency);

    if (!isNaN(startAge) && !isNaN(endAge) && monthlyAmount > 0) {
      // Add to income breakdown
      incomeBreakdown['Other Income'] = (incomeBreakdown['Other Income'] || 0) + monthlyAmount;

      // Add to income by age
      for (let age = startAge; age <= endAge; age++) {
        byAge[age] = (byAge[age] || 0) + monthlyAmount;
      }
    }
  });

  // Calculate total monthly income (at retirement age)
  const ssStartAgeNum =
    typeof socialSecurity.startAge === 'string'
      ? parseInt(socialSecurity.startAge)
      : socialSecurity.startAge;

  const totalMonthly = byAge[ssStartAgeNum] || 0;

  return {
    totalMonthly,
    byAge,
    incomeBreakdown,
  };
};

/**
 * Calculate income replacement ratio
 *
 * @param retirementIncome - Monthly retirement income
 * @param currentIncome - Current monthly income
 * @returns Income replacement ratio (percentage)
 */
export const calculateIncomeReplacementRatio = (
  retirementIncome: number,
  currentIncome: number
): number => {
  if (isNaN(retirementIncome) || isNaN(currentIncome) || currentIncome <= 0) return 0;

  return (retirementIncome / currentIncome) * 100;
};

/**
 * Calculate income gap
 *
 * @param retirementIncome - Monthly retirement income
 * @param desiredIncome - Desired monthly income
 * @returns Income gap (positive if there's a shortfall)
 */
export const calculateIncomeGap = (retirementIncome: number, desiredIncome: number): number => {
  if (isNaN(retirementIncome) || isNaN(desiredIncome)) return 0;

  return Math.max(0, desiredIncome - retirementIncome);
};

/**
 * Apply inflation adjustment to income
 *
 * @param amount - Income amount
 * @param years - Number of years to adjust for
 * @param inflationRate - Annual inflation rate (decimal)
 * @returns Inflation-adjusted amount
 */
export const applyInflationAdjustment = (
  amount: number,
  years: number,
  inflationRate: number = 0.025
): number => {
  if (isNaN(amount) || isNaN(years) || isNaN(inflationRate)) return amount;

  return amount * Math.pow(1 + inflationRate, years);
};

/**
 * Calculate income duration (how long the income will last)
 *
 * @param savings - Total savings amount
 * @param monthlyIncome - Monthly income needed
 * @param annualReturnRate - Annual return rate (decimal)
 * @param inflationRate - Annual inflation rate (decimal)
 * @returns Number of years the income will last
 */
export const calculateIncomeDuration = (
  savings: number,
  monthlyIncome: number,
  annualReturnRate: number = 0.04,
  inflationRate: number = 0.025
): number => {
  if (isNaN(savings) || isNaN(monthlyIncome) || monthlyIncome <= 0) return 0;

  // Calculate real rate of return (adjusted for inflation)
  const realRate = (1 + annualReturnRate) / (1 + inflationRate) - 1;

  // Convert monthly income to annual
  const annualIncome = monthlyIncome * 12;

  // If real rate is zero, use simple division
  if (Math.abs(realRate) < 0.0001) {
    return savings / annualIncome;
  }

  // Calculate duration using the formula for present value of an annuity
  return (Math.log(1 - (savings * realRate) / annualIncome) / Math.log(1 + realRate)) * -1;
};
