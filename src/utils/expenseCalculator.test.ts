/**
 * Expense Calculator Utility Tests
 */

import {
  convertToAnnual,
  convertToMonthly,
  convertToWeekly,
  convertToDaily,
  calculateTotalExpenses,
  calculateBudgetHealth,
  applyInflation,
  EXPENSE_FREQUENCY,
  BUDGET_RULE,
} from './expenseCalculator';

describe('Expense Calculator Utility', () => {
  describe('convertToAnnual', () => {
    it('converts daily expense to annual correctly', () => {
      expect(convertToAnnual(10, 'daily')).toBeCloseTo(3652.5, 1);
    });

    it('converts weekly expense to annual correctly', () => {
      expect(convertToAnnual(100, 'weekly')).toBeCloseTo(5214.29, 2);
    });

    it('converts biweekly expense to annual correctly', () => {
      expect(convertToAnnual(200, 'biweekly')).toBeCloseTo(5214.28, 2);
    });

    it('converts monthly expense to annual correctly', () => {
      expect(convertToAnnual(500, 'monthly')).toBe(6000);
    });

    it('keeps annual expense as is', () => {
      expect(convertToAnnual(12000, 'annual')).toBe(12000);
    });

    it('handles invalid input gracefully', () => {
      expect(convertToAnnual(NaN, 'monthly')).toBe(0);
    });
  });

  describe('convertToMonthly', () => {
    it('converts annual expense to monthly correctly', () => {
      expect(convertToMonthly(12000, 'annual')).toBe(1000);
    });

    it('converts weekly expense to monthly correctly', () => {
      expect(convertToMonthly(100, 'weekly')).toBeCloseTo(434.52, 2);
    });

    it('keeps monthly expense as is', () => {
      expect(convertToMonthly(500, 'monthly')).toBe(500);
    });

    it('handles invalid input gracefully', () => {
      expect(convertToMonthly(NaN, 'annual')).toBe(0);
    });
  });

  describe('convertToWeekly', () => {
    it('converts annual expense to weekly correctly', () => {
      expect(convertToWeekly(5214.29, 'annual')).toBeCloseTo(100, 2);
    });

    it('converts monthly expense to weekly correctly', () => {
      expect(convertToWeekly(434.52, 'monthly')).toBeCloseTo(100, 2);
    });

    it('keeps weekly expense as is', () => {
      expect(convertToWeekly(100, 'weekly')).toBe(100);
    });

    it('handles invalid input gracefully', () => {
      expect(convertToWeekly(NaN, 'annual')).toBe(0);
    });
  });

  describe('convertToDaily', () => {
    it('converts annual expense to daily correctly', () => {
      expect(convertToDaily(3652.5, 'annual')).toBeCloseTo(10, 2);
    });

    it('converts monthly expense to daily correctly', () => {
      expect(convertToDaily(304.38, 'monthly')).toBeCloseTo(10, 2);
    });

    it('keeps daily expense as is', () => {
      expect(convertToDaily(10, 'daily')).toBe(10);
    });

    it('handles invalid input gracefully', () => {
      expect(convertToDaily(NaN, 'annual')).toBe(0);
    });
  });

  describe('calculateTotalExpenses', () => {
    it('calculates total expenses correctly with multiple frequencies', () => {
      const expenses = [
        { amount: 1000, frequency: 'monthly' }, // $12,000 annually
        { amount: 100, frequency: 'weekly' }, // $5,214.29 annually
        { amount: 2000, frequency: 'annual' }, // $2,000 annually
      ];

      const result = calculateTotalExpenses(expenses);
      expect(result.annual).toBeCloseTo(19214.29, 2);
      expect(result.monthly).toBeCloseTo(1601.19, 2);
      expect(result.weekly).toBeCloseTo(368.42, 2);
      expect(result.daily).toBeCloseTo(52.61, 2);
    });

    it('handles invalid inputs gracefully', () => {
      const expenses = [
        { amount: 'invalid', frequency: 'monthly' },
        { amount: 100, frequency: 'weekly' },
      ];

      const result = calculateTotalExpenses(expenses);
      expect(result.annual).toBeCloseTo(5214.29, 2);
    });

    it('handles empty array correctly', () => {
      const result = calculateTotalExpenses([]);
      expect(result.annual).toBe(0);
      expect(result.monthly).toBe(0);
      expect(result.weekly).toBe(0);
      expect(result.daily).toBe(0);
    });
  });

  describe('calculateBudgetHealth', () => {
    it('calculates budget health correctly for a healthy budget', () => {
      // Total: $5,000
      // Needs (essential): $2,400 (48%)
      // Wants (discretionary): $1,400 (28%)
      // Savings: $1,200 (24%)
      const result = calculateBudgetHealth(2400, 1400, 1200, 5000);

      // Targets (50/30/20 rule)
      expect(result.needsTarget).toBe(2500); // 50% of $5,000
      expect(result.wantsTarget).toBe(1500); // 30% of $5,000
      expect(result.savingsTarget).toBe(1000); // 20% of $5,000

      // Ratios (actual / target)
      expect(result.needsRatio).toBeCloseTo(0.96, 2); // $2,400 / $2,500
      expect(result.wantsRatio).toBeCloseTo(0.93, 2); // $1,400 / $1,500
      expect(result.savingsRatio).toBeCloseTo(1.2, 2); // $1,200 / $1,000

      // Percentages
      expect(result.needsPercentage).toBe(48);
      expect(result.wantsPercentage).toBe(28);
      expect(result.savingsPercentage).toBe(24);

      // Status
      expect(result.needsStatus).toBe('on_target');
      expect(result.wantsStatus).toBe('on_target');
      expect(result.savingsStatus).toBe('on_target');
      expect(result.overallStatus).toBe('healthy');
    });

    it('calculates budget health correctly for a budget needing adjustment', () => {
      // Total: $5,000
      // Needs (essential): $2,700 (54%)
      // Wants (discretionary): $1,600 (32%)
      // Savings: $700 (14%)
      const result = calculateBudgetHealth(2700, 1600, 700, 5000);

      // Status
      expect(result.needsStatus).toBe('needs_adjustment');
      expect(result.wantsStatus).toBe('needs_adjustment');
      expect(result.savingsStatus).toBe('needs_adjustment');
      expect(result.overallStatus).toBe('needs_adjustment');
    });

    it('calculates budget health correctly for a budget needing major adjustment', () => {
      // Total: $5,000
      // Needs (essential): $3,200 (64%)
      // Wants (discretionary): $1,600 (32%)
      // Savings: $200 (4%)
      const result = calculateBudgetHealth(3200, 1600, 200, 5000);

      // Status
      expect(result.needsStatus).toBe('over_target');
      expect(result.wantsStatus).toBe('needs_adjustment');
      expect(result.savingsStatus).toBe('under_target');
      expect(result.overallStatus).toBe('needs_major_adjustment');
    });

    it('handles zero total expenses gracefully', () => {
      const result = calculateBudgetHealth(0, 0, 0, 0);
      expect(result.overallStatus).toBe('healthy');
    });

    it('correctly identifies a budget needing adjustment when a ratio is slightly outside the healthy range (e.g., ratio = 1.15)', () => {
      // Total: $10,000
      // Needs: $5,000 (50%)
      // Wants: $3,500 (35%) - ratio = 3500 / (10000 * 0.3) = 3500 / 3000 = 1.16...
      // Savings: $1,500 (15%) - ratio = 1500 / (10000 * 0.2) = 1500 / 2000 = 0.75
      const result = calculateBudgetHealth(5000, 3500, 1500, 10000);
      expect(result.overallStatus).toBe('needs_adjustment'); // Wants > 1.1 AND Savings < 0.9
    });

    it('correctly identifies a budget needing major adjustment when needs are over target and wants are over target or savings are under target', () => {
      // Total: $10,000
      // Needs: $6,000 (60%) - ratio = 6000 / 5000 = 1.2 (over target)
      // Wants: $3,500 (35%) - ratio = 1.16... (over target)
      // Savings: $500 (5%) - ratio = 0.25 (under target)
      const result1 = calculateBudgetHealth(6000, 3500, 500, 10000);
      expect(result1.overallStatus).toBe('needs_major_adjustment'); // Needs > 1.1 AND (Wants > 1.1 OR Savings < 0.9)

      // Total: $10,000
      // Needs: $6,000 (60%) - ratio = 1.2 (over target)
      // Wants: $2,800 (28%) - ratio = 0.93... (on target)
      // Savings: $1,200 (12%) - ratio = 0.6 (under target)
      const result2 = calculateBudgetHealth(6000, 2800, 1200, 10000);
      expect(result2.overallStatus).toBe('needs_major_adjustment'); // Needs > 1.1 AND (Wants > 1.1 (false) OR Savings < 0.9 (true))
    });

    it('handles budgets with zero values in specific categories', () => {
      // Total: $10,000
      // Needs: $10,000 (100%)
      // Wants: $0 (0%)
      // Savings: $0 (0%)
      const result1 = calculateBudgetHealth(10000, 0, 0, 10000);
      expect(result1.overallStatus).toBe('needs_major_adjustment'); // Needs over target, Savings under target

      // Total: $10,000
      // Needs: $0 (0%)
      // Wants: $10,000 (100%)
      // Savings: $0 (0%)
      const result2 = calculateBudgetHealth(0, 10000, 0, 10000);
      expect(result2.overallStatus).toBe('needs_adjustment'); // Wants over target, Savings under target

      // Total: $10,000
      // Needs: $5,000 (50%)
      // Wants: $5,000 (50%)
      // Savings: $0 (0%)
      const result3 = calculateBudgetHealth(5000, 5000, 0, 10000);
      expect(result3.overallStatus).toBe('needs_adjustment'); // Wants over target, Savings under target
    });
  });

  describe('applyInflation', () => {
    it('applies inflation correctly for multiple years', () => {
      // $1,000 with 2.5% inflation for 10 years
      // $1,000 * (1.025)^10 = $1,280.08
      const result = applyInflation(1000, 10, 0.025);
      expect(result).toBeCloseTo(1280.08, 2);
    });

    it('uses default inflation rate when not specified', () => {
      // Default inflation rate is 2.5%
      const result = applyInflation(1000, 10);
      expect(result).toBeCloseTo(1280.08, 2);
    });

    it('handles zero years correctly', () => {
      const result = applyInflation(1000, 0, 0.025);
      expect(result).toBe(1000);
    });

    it('handles invalid inputs gracefully', () => {
      expect(applyInflation(NaN, 10, 0.025)).toBeNaN();
      expect(applyInflation(1000, NaN, 0.025)).toBe(1000);
      expect(applyInflation(1000, 10, NaN)).toBe(1000);
    });

    it('handles zero inflation rate correctly', () => {
      const result = applyInflation(1000, 10, 0);
      expect(result).toBe(1000);
    });

    it('handles negative inflation rate (deflation) correctly', () => {
      // $1,000 with -2.5% inflation for 10 years
      // $1,000 * (1 - 0.025)^10 = $1,000 * (0.975)^10 = $776.33
      const result = applyInflation(1000, 10, -0.025);
      expect(result).toBeCloseTo(776.33, 2);
    });
  });
});
