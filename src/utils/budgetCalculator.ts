/**
 * Budget Calculator Utility
 *
 * This utility provides functions for calculating and analyzing budget allocations
 * based on the 50/30/20 rule and other financial planning principles.
 */

import { BUDGET_RULE } from './expenseCalculator';

export type BudgetAllocationStatus = 'healthy' | 'needs_adjustment' | 'needs_major_adjustment';

export interface BudgetAllocation {
  needs: number;
  wants: number;
  savings: number;
  needsPercentage: number;
  wantsPercentage: number;
  savingsPercentage: number;
  status: BudgetAllocationStatus;
}

/**
 * Calculate budget health based on the 50/30/20 rule
 *
 * @param essentialExpenses - Essential expenses (needs)
 * @param discretionaryExpenses - Discretionary expenses (wants)
 * @param savingsAmount - Savings amount
 * @param totalExpenses - Total expenses including savings
 * @returns Budget health assessment
 */
export const calculateBudgetHealth = (
  essentialExpenses: number,
  discretionaryExpenses: number,
  savingsAmount: number,
  totalExpenses: number
): BudgetAllocation => {
  if (totalExpenses <= 0) {
    return {
      needs: 0,
      wants: 0,
      savings: 0,
      needsPercentage: 0,
      wantsPercentage: 0,
      savingsPercentage: 0,
      status: 'needs_adjustment' as const,
    };
  }

  // Calculate actual percentages
  const needsPercentage = (essentialExpenses / totalExpenses) * 100;
  const wantsPercentage = (discretionaryExpenses / totalExpenses) * 100;
  const savingsPercentage = (savingsAmount / totalExpenses) * 100;

  // Calculate target amounts
  const needsTarget = totalExpenses * BUDGET_RULE.NEEDS_TARGET;
  const wantsTarget = totalExpenses * BUDGET_RULE.WANTS_TARGET;
  const savingsTarget = totalExpenses * BUDGET_RULE.SAVINGS_TARGET;

  // Calculate ratios (actual / target)
  const needsRatio = essentialExpenses / needsTarget;
  const wantsRatio = discretionaryExpenses / wantsTarget;
  const savingsRatio = savingsAmount / savingsTarget;

  // Determine status for each category
  const getStatus = (ratio: number) => {
    if (ratio >= 0.9 && ratio <= 1.1) return 'on_target' as const;
    if (ratio > 1.1) return 'over_target' as const;
    return 'under_target' as const;
  };

  const needsStatus = getStatus(needsRatio);
  const wantsStatus = getStatus(wantsRatio);
  const savingsStatus = getStatus(savingsRatio);

  // Determine overall status
  let overallStatus: 'healthy' | 'needs_adjustment' | 'needs_major_adjustment' = 'healthy';

  if (
    needsStatus === 'over_target' ||
    wantsStatus === 'over_target' ||
    savingsStatus === 'under_target'
  ) {
    overallStatus = 'needs_adjustment';
  }

  if (
    needsStatus === 'over_target' &&
    (wantsStatus === 'over_target' || savingsStatus === 'under_target')
  ) {
    overallStatus = 'needs_major_adjustment';
  }

  return {
    needs: essentialExpenses,
    wants: discretionaryExpenses,
    savings: savingsAmount,
    needsPercentage,
    wantsPercentage,
    savingsPercentage,
    status: overallStatus,
  };
};

/**
 * Calculate recommended budget allocation based on income
 *
 * @param afterTaxIncome - Annual after-tax income
 * @returns Recommended budget allocation
 */
export const getRecommendedBudget = (afterTaxIncome: number) => {
  return {
    needs: afterTaxIncome * BUDGET_RULE.NEEDS_TARGET,
    wants: afterTaxIncome * BUDGET_RULE.WANTS_TARGET,
    savings: afterTaxIncome * BUDGET_RULE.SAVINGS_TARGET,
    needsPercentage: BUDGET_RULE.NEEDS_TARGET * 100,
    wantsPercentage: BUDGET_RULE.WANTS_TARGET * 100,
    savingsPercentage: BUDGET_RULE.SAVINGS_TARGET * 100,
    status: 'healthy' as const,
  };
};

/**
 * Calculate monthly budget from annual amounts
 *
 * @param annualAmount - Annual amount
 * @returns Monthly amount
 */
export const toMonthly = (annualAmount: number) => {
  return annualAmount / 12;
};

/**
 * Calculate weekly budget from annual amounts
 *
 * @param annualAmount - Annual amount
 * @returns Weekly amount
 */
export const toWeekly = (annualAmount: number) => {
  return annualAmount / 52;
};

/**
 * Calculate bi-weekly budget from annual amounts
 *
 * @param annualAmount - Annual amount
 * @returns Bi-weekly amount
 */
export const toBiWeekly = (annualAmount: number) => {
  return annualAmount / 26;
};
