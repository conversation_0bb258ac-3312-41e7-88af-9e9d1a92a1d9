/**
 * Budget Calculator Utility Tests
 */

import {
  calculateBudgetHealth,
  getRecommendedBudget,
  toMonthly,
  toWeekly,
  toBiWeekly,
  BudgetAllocationStatus,
} from './budgetCalculator';

import { BUDGET_RULE } from './expenseCalculator'; // Assuming BUDGET_RULE is needed for testing targets

describe('Budget Calculator Utility', () => {
  describe('calculateBudgetHealth', () => {
    it('calculates budget allocation and status correctly for a healthy budget', () => {
      const essentialExpenses = 5000;
      const discretionaryExpenses = 3000;
      const savingsAmount = 2000;
      const totalExpenses = essentialExpenses + discretionaryExpenses + savingsAmount;

      const result = calculateBudgetHealth(
        essentialExpenses,
        discretionaryExpenses,
        savingsAmount,
        totalExpenses
      );

      expect(result.needs).toBe(essentialExpenses);
      expect(result.wants).toBe(discretionaryExpenses);
      expect(result.savings).toBe(savingsAmount);
      expect(result.needsPercentage).toBeCloseTo(50, 2);
      expect(result.wantsPercentage).toBeCloseTo(30, 2);
      expect(result.savingsPercentage).toBeCloseTo(20, 2);
      expect(result.status).toBe('healthy');
    });

    it('calculates budget allocation and status correctly for a budget needing adjustment (over wants)', () => {
      const essentialExpenses = 5000;
      const discretionaryExpenses = 4000; // Over target
      const savingsAmount = 1000; // Under target
      const totalExpenses = essentialExpenses + discretionaryExpenses + savingsAmount;

      const result = calculateBudgetHealth(
        essentialExpenses,
        discretionaryExpenses,
        savingsAmount,
        totalExpenses
      );

      expect(result.needs).toBe(essentialExpenses);
      expect(result.wants).toBe(discretionaryExpenses);
      expect(result.savings).toBe(savingsAmount);
      expect(result.needsPercentage).toBeCloseTo(50, 2);
      expect(result.wantsPercentage).toBeCloseTo(40, 2);
      expect(result.savingsPercentage).toBeCloseTo(10, 2);
      expect(result.status).toBe('needs_adjustment');
    });

    it('calculates budget allocation and status correctly for a budget needing major adjustment (over needs and under savings)', () => {
      const essentialExpenses = 6000; // Over target
      const discretionaryExpenses = 3000;
      const savingsAmount = 1000; // Under target
      const totalExpenses = essentialExpenses + discretionaryExpenses + savingsAmount;

      const result = calculateBudgetHealth(
        essentialExpenses,
        discretionaryExpenses,
        savingsAmount,
        totalExpenses
      );

      expect(result.needs).toBe(essentialExpenses);
      expect(result.wants).toBe(discretionaryExpenses);
      expect(result.savings).toBe(savingsAmount);
      expect(result.needsPercentage).toBeCloseTo(60, 2);
      expect(result.wantsPercentage).toBeCloseTo(30, 2);
      expect(result.savingsPercentage).toBeCloseTo(10, 2);
      expect(result.status).toBe('needs_major_adjustment');
    });

    it('handles zero total expenses correctly', () => {
      const essentialExpenses = 1000;
      const discretionaryExpenses = 500;
      const savingsAmount = 200;
      const totalExpenses = 0;

      const result = calculateBudgetHealth(
        essentialExpenses,
        discretionaryExpenses,
        savingsAmount,
        totalExpenses
      );

      expect(result.needs).toBe(0);
      expect(result.wants).toBe(0);
      expect(result.savings).toBe(0);
      expect(result.needsPercentage).toBe(0);
      expect(result.wantsPercentage).toBe(0);
      expect(result.savingsPercentage).toBe(0);
      expect(result.status).toBe('needs_adjustment');
    });

    it('handles zero values for all categories', () => {
      const essentialExpenses = 0;
      const discretionaryExpenses = 0;
      const savingsAmount = 0;
      const totalExpenses = 0;

      const result = calculateBudgetHealth(
        essentialExpenses,
        discretionaryExpenses,
        savingsAmount,
        totalExpenses
      );

      expect(result.needs).toBe(0);
      expect(result.wants).toBe(0);
      expect(result.savings).toBe(0);
      expect(result.needsPercentage).toBe(0);
      expect(result.wantsPercentage).toBe(0);
      expect(result.savingsPercentage).toBe(0);
      expect(result.status).toBe('needs_adjustment');
    });

    it('handles negative input values gracefully', () => {
      const essentialExpenses = -1000; // Negative input
      const discretionaryExpenses = 3000;
      const savingsAmount = 2000;
      const totalExpenses = essentialExpenses + discretionaryExpenses + savingsAmount; // Total will be 4000

      const result = calculateBudgetHealth(
        essentialExpenses,
        discretionaryExpenses,
        savingsAmount,
        totalExpenses
      );

      expect(result.needs).toBe(essentialExpenses);
      expect(result.wants).toBe(discretionaryExpenses);
      expect(result.savings).toBe(savingsAmount);
      // Percentages and status should be calculated based on the values, even if negative inputs lead to unexpected ratios
      expect(result.needsPercentage).toBeCloseTo(-25, 2);
      expect(result.wantsPercentage).toBeCloseTo(75, 2);
      expect(result.savingsPercentage).toBeCloseTo(50, 2);
      // Depending on the exact logic within calculateBudgetHealth for negative inputs, the status might vary.
      // Assuming the ratio calculation handles negative numbers, leading to 'under_target' or 'over_target' based on the sign and comparison with target.
      // In this case, needsRatio = -1000 / (4000 * 0.5) = -0.5, which is < 0.9, so 'under_target'.
      // wantsRatio = 3000 / (4000 * 0.3) = 2.5, which is > 1.1, so 'over_target'.
      // savingsRatio = 2000 / (4000 * 0.2) = 2.5, which is > 1.1, so 'over_target'.
      // Overall status will be 'needs_adjustment' due to wantsStatus and savingsStatus being 'over_target'.
      expect(result.status).toBe('needs_adjustment'); // Test based on expected behavior with negative input affecting ratios
    });
  });

  describe('getRecommendedBudget', () => {
    it('calculates recommended budget based on income', () => {
      const afterTaxIncome = 100000;
      const result = getRecommendedBudget(afterTaxIncome);

      expect(result.needs).toBe(afterTaxIncome * BUDGET_RULE.NEEDS_TARGET);
      expect(result.wants).toBe(afterTaxIncome * BUDGET_RULE.WANTS_TARGET);
      expect(result.savings).toBe(afterTaxIncome * BUDGET_RULE.SAVINGS_TARGET);
      expect(result.needsPercentage).toBeCloseTo(BUDGET_RULE.NEEDS_TARGET * 100, 2);
      expect(result.wantsPercentage).toBeCloseTo(BUDGET_RULE.WANTS_TARGET * 100, 2);
      expect(result.savingsPercentage).toBeCloseTo(BUDGET_RULE.SAVINGS_TARGET * 100, 2);
      expect(result.status).toBe('healthy'); // Recommended budget is always healthy by definition
    });

    it('handles zero income correctly', () => {
      const afterTaxIncome = 0;
      const result = getRecommendedBudget(afterTaxIncome);

      expect(result.needs).toBe(0);
      expect(result.wants).toBe(0);
      expect(result.savings).toBe(0);
      expect(result.needsPercentage).toBeCloseTo(BUDGET_RULE.NEEDS_TARGET * 100, 2);
      expect(result.wantsPercentage).toBeCloseTo(BUDGET_RULE.WANTS_TARGET * 100, 2);
      expect(result.savingsPercentage).toBeCloseTo(BUDGET_RULE.SAVINGS_TARGET * 100, 2);
      expect(result.status).toBe('healthy');
    });

    it('handles negative income gracefully', () => {
      const afterTaxIncome = -50000; // Negative income
      const result = getRecommendedBudget(afterTaxIncome);

      expect(result.needs).toBe(afterTaxIncome * BUDGET_RULE.NEEDS_TARGET);
      expect(result.wants).toBe(afterTaxIncome * BUDGET_RULE.WANTS_TARGET);
      expect(result.savings).toBe(afterTaxIncome * BUDGET_RULE.SAVINGS_TARGET);
      expect(result.needsPercentage).toBeCloseTo(BUDGET_RULE.NEEDS_TARGET * 100, 2);
      expect(result.wantsPercentage).toBeCloseTo(BUDGET_RULE.WANTS_TARGET * 100, 2);
      expect(result.savingsPercentage).toBeCloseTo(BUDGET_RULE.SAVINGS_TARGET * 100, 2);
      expect(result.status).toBe('healthy'); // Recommended budget is always healthy even with negative input
    });
  });

  describe('toMonthly', () => {
    it('converts annual amount to monthly correctly', () => {
      expect(toMonthly(12000)).toBe(1000);
    });

    it('handles zero input correctly', () => {
      expect(toMonthly(0)).toBe(0);
    });

    it('handles fractional annual amounts', () => {
      expect(toMonthly(100)).toBeCloseTo(8.33, 2);
    });
  });

  describe('toWeekly', () => {
    it('converts annual amount to weekly correctly', () => {
      expect(toWeekly(52000)).toBeCloseTo(1000, 0); // Approximately
    });

    it('handles zero input correctly', () => {
      expect(toWeekly(0)).toBe(0);
    });

    it('handles fractional annual amounts', () => {
      expect(toWeekly(100)).toBeCloseTo(1.92, 2);
    });
  });

  describe('toBiWeekly', () => {
    it('converts annual amount to bi-weekly correctly', () => {
      expect(toBiWeekly(52000)).toBeCloseTo(2000, 0); // Approximately
    });

    it('handles zero input correctly', () => {
      expect(toBiWeekly(0)).toBe(0);
    });

    it('handles fractional annual amounts', () => {
      expect(toBiWeekly(100)).toBeCloseTo(3.85, 2);
    });
  });
});
