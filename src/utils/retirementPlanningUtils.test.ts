/**
 * Retirement Planning Utility Tests
 */

import { generateRetirementPlan } from './retirementPlanningUtils';

describe('Retirement Planning Utility', () => {
  describe('generateRetirementPlan', () => {
    // Test case 1: Basic scenario with sufficient savings
    it('should generate a retirement plan for a basic scenario with sufficient savings', () => {
      const input = {
        currentAge: 40,
        retirementAge: 65,
        lifeExpectancy: 90,
        currentIncome: 100000,
        currentSavings: 500000,
        monthlyContribution: 1000,
        expectedReturnRate: 0.07,
        inflationRate: 0.025,
        socialSecurityEarnings: Array(35).fill(50000), // Simulate consistent earnings
        filingStatus: 'single' as 'single',
      };

      const plan = generateRetirementPlan(input);

      expect(plan.currentAge).toBe(input.currentAge);
      expect(plan.retirementAge).toBe(input.retirementAge);
      expect(plan.lifeExpectancy).toBe(input.lifeExpectancy);
      expect(plan.yearsUntilRetirement).toBe(25);
      expect(plan.retirementDuration).toBe(25);
      expect(plan.currentIncome).toBe(input.currentIncome);
      expect(plan.currentSavings).toBe(input.currentSavings);
      expect(plan.monthlyContribution).toBe(input.monthlyContribution);
      expect(plan.annualContribution).toBe(input.monthlyContribution * 12);
      expect(plan.expectedReturnRate).toBe(input.expectedReturnRate);
      expect(plan.inflationRate).toBe(input.inflationRate);

      // Basic checks for calculated values (more precise checks below or in dedicated tests)
      expect(plan.fullRetirementAge).toBeDefined();
      expect(plan.primaryInsuranceAmount).toBeGreaterThan(0);
      expect(plan.socialSecurityBenefit).toBeGreaterThan(0);
      expect(plan.projectedSavingsAtRetirement).toBeGreaterThan(input.currentSavings);
      expect(plan.requiredRetirementIncome).toBeCloseTo(input.currentIncome * 0.8, 2);
      expect(plan.requiredRetirementSavings).toBeGreaterThan(0);
      expect(plan.safeWithdrawalRate).toBe(0.04);
      expect(plan.annualSafeWithdrawal).toBeGreaterThan(0);
      expect(plan.monthlySafeWithdrawal).toBeGreaterThan(0);
      expect(plan.readinessScore).toBeGreaterThanOrEqual(0);
      expect(plan.yearsOfSavings).toBeGreaterThan(0);
      // Expecting a healthy plan with sufficient savings
      expect(plan.monthlyIncomeGap).toBeLessThan(0); // Projected income > required income
      expect(plan.incomeGapPercentage).toBeLessThanOrEqual(0); // Income gap is negative or zero
      expect(plan.recommendations.some((r) => r.title === 'Stay on Course')).toBe(true);
    });

    // Test case 2: Scenario with significant income gap
    it('should identify a significant income gap and recommend increasing savings', () => {
      const input = {
        currentAge: 40,
        retirementAge: 65,
        lifeExpectancy: 90,
        currentIncome: 100000,
        currentSavings: 50000,
        monthlyContribution: 100,
        expectedReturnRate: 0.05,
        inflationRate: 0.03,
        socialSecurityEarnings: Array(35).fill(20000), // Lower earnings
        filingStatus: 'single' as 'single',
      };

      const plan = generateRetirementPlan(input);

      expect(plan.monthlyIncomeGap).toBeGreaterThan(0); // Expecting an income gap
      expect(plan.incomeGapPercentage).toBeGreaterThan(10); // Expecting a significant gap percentage
      expect(plan.recommendations.some((r) => r.title === 'Close Your Retirement Income Gap')).toBe(
        true
      );
      expect(plan.recommendations.some((r) => r.action === 'increase_savings')).toBe(true);
    });

    // Test case 3: Scenario with low savings rate for age
    it('should recommend increasing savings rate if it is below recommended level', () => {
      const input = {
        currentAge: 45,
        retirementAge: 65,
        lifeExpectancy: 90,
        currentIncome: 120000,
        currentSavings: 100000,
        monthlyContribution: 500,
        expectedReturnRate: 0.06,
        inflationRate: 0.02,
        socialSecurityEarnings: Array(35).fill(60000),
        filingStatus: 'single' as 'single',
      };

      const plan = generateRetirementPlan(input);
      const annualContribution = input.monthlyContribution * 12;
      const savingsRate = (annualContribution / input.currentIncome) * 100;

      expect(savingsRate).toBeLessThan(15); // Confirming the low savings rate for this test case
      expect(plan.recommendations.some((r) => r.title === 'Increase Your Savings Rate')).toBe(true);
      expect(plan.recommendations.some((r) => r.action === 'increase_savings_rate')).toBe(true);
    });

    // Test case 4: Scenario claiming Social Security early
    it('should recommend considering delaying Social Security if claiming before FRA', () => {
      const input = {
        currentAge: 60,
        retirementAge: 62,
        lifeExpectancy: 90,
        currentIncome: 80000,
        currentSavings: 300000,
        monthlyContribution: 0,
        expectedReturnRate: 0.04,
        inflationRate: 0.02,
        socialSecurityEarnings: Array(35).fill(40000),
        filingStatus: 'single' as 'single',
      };

      const plan = generateRetirementPlan(input);

      // Assuming FRA for someone born in 1963 is 67
      expect(plan.retirementAge).toBeLessThan(67);
      expect(plan.recommendations.some((r) => r.title.includes('Delaying Social Security'))).toBe(
        true
      );
      expect(plan.recommendations.some((r) => r.action === 'delay_social_security')).toBe(true);
    });

    // Test case 5: Edge case - currentAge = retirementAge
    it('should handle currentAge equal to retirementAge correctly', () => {
      const input = {
        currentAge: 65,
        retirementAge: 65,
        lifeExpectancy: 90,
        currentIncome: 100000,
        currentSavings: 500000,
        monthlyContribution: 0,
        expectedReturnRate: 0.07,
        inflationRate: 0.025,
        socialSecurityEarnings: Array(35).fill(50000),
        filingStatus: 'single' as 'single',
      };

      const plan = generateRetirementPlan(input);

      expect(plan.yearsUntilRetirement).toBe(0);
      // Other assertions based on expected calculations at retirement age
    });

    // Test case 6: Edge case - currentSavings = 0
    it('should handle zero current savings correctly', () => {
      const input = {
        currentAge: 40,
        retirementAge: 65,
        lifeExpectancy: 90,
        currentIncome: 100000,
        currentSavings: 0,
        monthlyContribution: 1000,
        expectedReturnRate: 0.07,
        inflationRate: 0.025,
        socialSecurityEarnings: Array(35).fill(50000),
        filingStatus: 'single' as 'single',
      };

      const plan = generateRetirementPlan(input);

      expect(plan.currentSavings).toBe(0);
      expect(plan.projectedSavingsAtRetirement).toBeGreaterThan(0); // Should project based on contributions
    });

    // Test case 7: Edge case - monthlyContribution = 0
    it('should handle zero monthly contribution correctly', () => {
      const input = {
        currentAge: 40,
        retirementAge: 65,
        lifeExpectancy: 90,
        currentIncome: 100000,
        currentSavings: 500000,
        monthlyContribution: 0,
        expectedReturnRate: 0.07,
        inflationRate: 0.025,
        socialSecurityEarnings: Array(35).fill(50000),
        filingStatus: 'single' as 'single',
      };

      const plan = generateRetirementPlan(input);

      expect(plan.monthlyContribution).toBe(0);
      expect(plan.annualContribution).toBe(0);
      expect(plan.projectedSavingsAtRetirement).toBeCloseTo(
        input.currentSavings * Math.pow(1 + input.expectedReturnRate, plan.yearsUntilRetirement),
        2
      ); // Should project based only on current savings growth
    });

    // Test case 8: Edge case - expectedReturnRate = 0
    it('should handle zero expected return rate correctly', () => {
      const input = {
        currentAge: 40,
        retirementAge: 65,
        lifeExpectancy: 90,
        currentIncome: 100000,
        currentSavings: 500000,
        monthlyContribution: 1000,
        expectedReturnRate: 0,
        inflationRate: 0.025,
        socialSecurityEarnings: Array(35).fill(50000),
        filingStatus: 'single' as 'single',
      };

      const plan = generateRetirementPlan(input);

      expect(plan.expectedReturnRate).toBe(0);
      // Projected savings should be current savings + total contributions without growth
      expect(plan.projectedSavingsAtRetirement).toBeCloseTo(
        input.currentSavings + input.monthlyContribution * 12 * plan.yearsUntilRetirement,
        2
      );
      // Other calculations dependent on return rate should behave as expected with 0 rate.
    });

    // Test case 9: Edge case - inflationRate = 0
    it('should handle zero inflation rate correctly', () => {
      const input = {
        currentAge: 40,
        retirementAge: 65,
        lifeExpectancy: 90,
        currentIncome: 100000,
        currentSavings: 500000,
        monthlyContribution: 1000,
        expectedReturnRate: 0.07,
        inflationRate: 0,
        socialSecurityEarnings: Array(35).fill(50000),
        filingStatus: 'single' as 'single',
      };

      const plan = generateRetirementPlan(input);

      expect(plan.inflationRate).toBe(0);
      // Years of savings calculation should behave correctly with 0 inflation.
    });

    // Test case 10: Scenario with different filing status (married)
    it('should handle married filing status and spouse earnings correctly', () => {
      const input = {
        currentAge: 40,
        retirementAge: 65,
        lifeExpectancy: 90,
        currentIncome: 100000,
        currentSavings: 500000,
        monthlyContribution: 1000,
        expectedReturnRate: 0.07,
        inflationRate: 0.025,
        socialSecurityEarnings: Array(35).fill(50000),
        filingStatus: 'married' as 'married',
        spouseSocialSecurityEarnings: Array(35).fill(30000), // Simulate spouse earnings
      };

      const plan = generateRetirementPlan(input);

      expect(plan.filingStatus).toBe('married');
      // Additional checks would be needed here to verify spousal benefit calculation if implemented in generateRetirementPlan
      // Currently, spouseSocialSecurityEarnings is part of the input but not explicitly used in the provided generateRetirementPlan code snippet for spousal benefits.
      // A test for calculateSpousalBenefit in socialSecurityCalculator.test.ts would cover that logic.
    });

    // Test case 11: Scenario with retirement age before current age (edge case)
    it('should handle retirementAge before currentAge correctly', () => {
      const input = {
        currentAge: 70,
        retirementAge: 65,
        lifeExpectancy: 90,
        currentIncome: 50000,
        currentSavings: 800000,
        monthlyContribution: 0,
        expectedReturnRate: 0.04,
        inflationRate: 0.02,
        socialSecurityEarnings: Array(35).fill(40000),
        filingStatus: 'single' as 'single',
      };

      const plan = generateRetirementPlan(input);

      expect(plan.yearsUntilRetirement).toBe(0); // Years until retirement should be 0 or negative, capped at 0
      expect(plan.retirementDuration).toBe(20); // Life expectancy 90 - retirement age 65 = 25, but maybe life expectancy is from current age? Assuming life expectancy from birth.
      // Re-checking code: retirementDuration is lifeExpectancy - retirementAge. So 90 - 65 = 25.
      expect(plan.retirementDuration).toBe(25);
      // Calculations should reflect already being in retirement.
    });

    // Test case 12: Scenario with life expectancy less than retirement age (edge case)
    it('should handle life expectancy less than retirement age correctly', () => {
      const input = {
        currentAge: 40,
        retirementAge: 65,
        lifeExpectancy: 60,
        currentIncome: 100000,
        currentSavings: 500000,
        monthlyContribution: 1000,
        expectedReturnRate: 0.07,
        inflationRate: 0.025,
        socialSecurityEarnings: Array(35).fill(50000),
        filingStatus: 'single' as 'single',
      };

      const plan = generateRetirementPlan(input);

      expect(plan.retirementDuration).toBe(0); // Retirement duration should be 0 or negative, capped at 0
      // Calculations should reflect no retirement period.
      expect(plan.requiredRetirementIncome).toBe(0); // No retirement, no required income
      expect(plan.requiredRetirementSavings).toBe(0); // No retirement, no required savings
      expect(plan.monthlyIncomeGap).toBe(0); // No income gap
      expect(plan.incomeGapPercentage).toBe(0); // No income gap percentage
      expect(plan.yearsOfSavings).toBe(0); // No years of savings needed
    });

    // Test case 13: Scenario with zero current income
    it('should handle zero current income correctly', () => {
      const input = {
        currentAge: 40,
        retirementAge: 65,
        lifeExpectancy: 90,
        currentIncome: 0,
        currentSavings: 500000,
        monthlyContribution: 1000,
        expectedReturnRate: 0.07,
        inflationRate: 0.025,
        socialSecurityEarnings: Array(35).fill(50000),
        filingStatus: 'single' as 'single',
      };

      const plan = generateRetirementPlan(input);

      expect(plan.currentIncome).toBe(0);
      // Required retirement income should still be calculated (e.g., based on a minimum standard, or 80% of 0 = 0)
      // Assuming 80% of 0 is 0 for now based on the provided code snippet logic.
      expect(plan.requiredRetirementIncome).toBe(0);
      // Other calculations should handle zero income appropriately.
    });

    // Test case 14: Scenario with invalid or insufficient Social Security earnings data
    it('should handle invalid or insufficient social security earnings data gracefully', () => {
      const input = {
        currentAge: 40,
        retirementAge: 65,
        lifeExpectancy: 90,
        currentIncome: 100000,
        currentSavings: 500000,
        monthlyContribution: 1000,
        expectedReturnRate: 0.07,
        inflationRate: 0.025,
        socialSecurityEarnings: [], // Empty earnings array
        filingStatus: 'single' as 'single',
      };

      const plan = generateRetirementPlan(input);

      expect(plan.primaryInsuranceAmount).toBe(0); // PIA should be 0 with no earnings
      expect(plan.socialSecurityBenefit).toBe(0); // SS benefit should be 0
      // Calculations dependent on SS benefit should handle 0 correctly.
    });

    // Test case 15: Scenario with very high return rate (edge case)
    it('should handle very high expected return rate gracefully', () => {
      const input = {
        currentAge: 40,
        retirementAge: 65,
        lifeExpectancy: 90,
        currentIncome: 100000,
        currentSavings: 10000,
        monthlyContribution: 5000,
        expectedReturnRate: 0.2, // Unrealistic high return rate
        inflationRate: 0.025,
        socialSecurityEarnings: Array(35).fill(50000),
        filingStatus: 'single' as 'single',
      };

      const plan = generateRetirementPlan(input);

      // Projected savings should be very high, but calculations should not break.
      expect(plan.projectedSavingsAtRetirement).toBeGreaterThan(input.currentSavings);
      expect(plan.monthlyIncomeGap).toBeLessThanOrEqual(0); // Likely no income gap with high growth
      expect(plan.incomeGapPercentage).toBeLessThanOrEqual(0);
    });

    // Test case 16: Scenario with very high inflation rate (edge case)
    it('should handle very high inflation rate gracefully', () => {
      const input = {
        currentAge: 40,
        retirementAge: 65,
        lifeExpectancy: 90,
        currentIncome: 100000,
        currentSavings: 500000,
        monthlyContribution: 1000,
        expectedReturnRate: 0.07,
        inflationRate: 0.1, // Unrealistic high inflation rate
        socialSecurityEarnings: Array(35).fill(50000),
        filingStatus: 'single' as 'single',
      };

      const plan = generateRetirementPlan(input);

      expect(plan.inflationRate).toBe(0.1);
      // Calculations dependent on inflation (like yearsOfSavings) should reflect the high inflation.
      expect(plan.yearsOfSavings).toBeLessThan(input.lifeExpectancy - input.retirementAge); // High inflation reduces spending power quickly
    });

    // Test case 17: Scenario with required retirement income = 0 (e.g., no income needed in retirement)
    it('should handle required retirement income of zero correctly', () => {
      const input = {
        currentAge: 40,
        retirementAge: 65,
        lifeExpectancy: 90,
        currentIncome: 0, // Simulate scenario where no income needed (or already covered by other means)
        currentSavings: 500000,
        monthlyContribution: 1000,
        expectedReturnRate: 0.07,
        inflationRate: 0.025,
        socialSecurityEarnings: Array(35).fill(50000),
        filingStatus: 'single' as 'single',
      };

      const plan = generateRetirementPlan(input);

      expect(plan.requiredRetirementIncome).toBe(0);
      expect(plan.requiredRetirementSavings).toBe(0); // No required savings if no required income
      // Income gap and related calculations should also be zero.
      expect(plan.monthlyIncomeGap).toBeLessThanOrEqual(0);
      expect(plan.incomeGapPercentage).toBeLessThanOrEqual(0);
    });

    // Test case 18: Test recommendations for different scenarios
    it('should provide appropriate recommendations based on the plan analysis', () => {
      // Scenario needing high priority savings increase
      const input1 = {
        currentAge: 40,
        retirementAge: 65,
        lifeExpectancy: 90,
        currentIncome: 100000,
        currentSavings: 10000,
        monthlyContribution: 500,
        expectedReturnRate: 0.05,
        inflationRate: 0.03,
        socialSecurityEarnings: Array(35).fill(20000),
        filingStatus: 'single' as 'single',
      };
      const plan1 = generateRetirementPlan(input1);
      expect(
        plan1.recommendations.some(
          (r) => r.title === 'Close Your Retirement Income Gap' && r.priority === 'High'
        )
      ).toBe(true);
      expect(
        plan1.recommendations.some(
          (r) => r.title === 'Increase Your Savings Rate' && r.priority === 'High'
        )
      ).toBe(true);

      // Scenario needing medium priority savings increase (higher savings rate than above but still below 15%)
      const input2 = {
        currentAge: 45,
        retirementAge: 65,
        lifeExpectancy: 90,
        currentIncome: 120000,
        currentSavings: 100000,
        monthlyContribution: 1000, // Higher monthly contribution
        expectedReturnRate: 0.06,
        inflationRate: 0.02,
        socialSecurityEarnings: Array(35).fill(60000),
        filingStatus: 'single' as 'single',
      };
      const plan2 = generateRetirementPlan(input2);
      const savingsRate2 = ((input2.monthlyContribution * 12) / input2.currentIncome) * 100;
      expect(savingsRate2).toBeCloseTo(10, 2); // Confirm savings rate is around 10%
      expect(
        plan2.recommendations.some(
          (r) => r.title === 'Close Your Retirement Income Gap' && r.priority === 'Medium'
        )
      ).toBe(true); // Income gap likely medium priority now
      expect(
        plan2.recommendations.some(
          (r) => r.title === 'Increase Your Savings Rate' && r.priority === 'Medium'
        )
      ).toBe(true);

      // Scenario with no recommendations (stay on course)
      const input3 = {
        currentAge: 40,
        retirementAge: 65,
        lifeExpectancy: 90,
        currentIncome: 100000,
        currentSavings: 500000,
        monthlyContribution: 2000, // High savings rate
        expectedReturnRate: 0.08,
        inflationRate: 0.015,
        socialSecurityEarnings: Array(35).fill(70000),
        filingStatus: 'single' as 'single',
      };
      const plan3 = generateRetirementPlan(input3);
      expect(plan3.recommendations.some((r) => r.title === 'Stay on Course')).toBe(true);
      expect(plan3.recommendations.length).toBe(1); // Only one recommendation expected
    });
  });

  // Add more specific tests for individual utility functions used within generateRetirementPlan if needed
  // For example, testing calculateFutureValue, calculateRequiredRetirementIncome, etc. independently
});
