/**
 * Income Calculator Utility
 *
 * This utility provides comprehensive income calculations including conversions, validations,
 * and financial metrics for income analysis.
 */

import {
  calculateFederalIncomeTax,
  calculateStateTax,
  calculateFICATaxes,
  type FilingStatus,
} from './taxCalculator';
import { BUDGET_RULE } from './expenseCalculator';

/**
 * Constants for precise pay period calculations
 * Using more accurate values for pay periods per year
 */
export const PAY_PERIODS = {
  WEEKLY: 52.1429, // 365.25/7 (accounts for leap years)
  BIWEEKLY: 26.0714, // 365.25/14
  SEMI_MONTHLY: 24, // Twice per month (24 pay periods)
  MONTHLY: 12,
  QUARTERLY: 4,
  SEMI_ANNUALLY: 2,
  ANNUALLY: 1,
  ONE_TIME: 1, // Treated as annual for conversion purposes
};

/**
 * Convert income to annual amount
 *
 * @param amount - Income amount
 * @param frequency - Income frequency (weekly, biweekly, monthly, etc.)
 * @returns Annual income amount
 */
export const convertToAnnual = (amount: number, frequency: string): number => {
  if (isNaN(amount)) return 0;

  switch (frequency.toLowerCase()) {
    case 'weekly':
      return amount * PAY_PERIODS.WEEKLY;
    case 'biweekly':
      return amount * PAY_PERIODS.BIWEEKLY;
    case 'semi-monthly':
    case 'semi_monthly':
      return amount * PAY_PERIODS.SEMI_MONTHLY;
    case 'monthly':
      return amount * PAY_PERIODS.MONTHLY;
    case 'quarterly':
      return amount * PAY_PERIODS.QUARTERLY;
    case 'semi-annually':
    case 'semi_annually':
      return amount * PAY_PERIODS.SEMI_ANNUALLY;
    case 'annually':
    case 'annual':
      return amount;
    case 'one-time':
    case 'one_time':
      return amount; // One-time income is treated as annual for conversion
    default:
      return amount; // Default to assuming the amount is already annual
  }
};

/**
 * Convert income to monthly amount
 *
 * @param amount - Income amount
 * @param frequency - Income frequency (weekly, biweekly, monthly, etc.)
 * @returns Monthly income amount
 */
export const convertToMonthly = (amount: number, frequency: string): number => {
  const annual = convertToAnnual(amount, frequency);
  return annual / PAY_PERIODS.MONTHLY;
};

/**
 * Convert income to weekly amount
 *
 * @param amount - Income amount
 * @param frequency - Income frequency (weekly, biweekly, monthly, etc.)
 * @returns Weekly income amount
 */
export const convertToWeekly = (amount: number, frequency: string): number => {
  const annual = convertToAnnual(amount, frequency);
  return annual / PAY_PERIODS.WEEKLY;
};

/**
 * Convert income to biweekly amount
 *
 * @param amount - Income amount
 * @param frequency - Income frequency (weekly, biweekly, monthly, etc.)
 * @returns Biweekly income amount
 */
export const convertToBiweekly = (amount: number, frequency: string): number => {
  const annual = convertToAnnual(amount, frequency);
  return annual / PAY_PERIODS.BIWEEKLY;
};

/**
 * Income source interface
 */
export interface IncomeSource {
  amount: number;
  frequency: string;
  isTaxable?: boolean;
  isPreTax?: boolean;
  type?: string;
  description?: string;
}

/**
 * Income breakdown interface
 */
export interface IncomeBreakdown {
  grossIncome: number;
  taxableIncome: number;
  federalTax: number;
  stateTax: number;
  ficaTax: number;
  totalTax: number;
  afterTaxIncome: number;
  effectiveTaxRate: number;
  budgetAllocation: {
    needs: number;
    wants: number;
    savings: number;
    needsPercentage: number;
    wantsPercentage: number;
    savingsPercentage: number;
    status: 'healthy' | 'needs_adjustment' | 'needs_major_adjustment';
  };
}

/**
 * Calculate total annual income from multiple sources
 *
 * @param primaryIncome - Primary income amount
 * @param primaryIncomeFrequency - Primary income frequency
 * @param additionalSources - Array of additional income sources
 * @returns Total annual income
 */
export const calculateTotalAnnualIncome = (
  primaryIncome: number,
  primaryIncomeFrequency: string,
  additionalSources: IncomeSource[] = []
): number => {
  let total = convertToAnnual(primaryIncome, primaryIncomeFrequency);

  additionalSources.forEach((source) => {
    if (source.isTaxable !== false) {
      // Only include taxable income by default
      total += convertToAnnual(source.amount, source.frequency);
    }
  });

  return total;
};

/**
 * Calculate total taxable income from all sources
 *
 * @param primaryIncome - Primary income amount
 * @param primaryIncomeFrequency - Primary income frequency
 * @param additionalSources - Array of additional income sources
 * @param preTaxDeductions - Total pre-tax deductions
 * @returns Total taxable annual income
 */
export const calculateTaxableIncome = (
  primaryIncome: number,
  primaryIncomeFrequency: string,
  additionalSources: IncomeSource[] = [],
  preTaxDeductions: number = 0
): number => {
  let totalTaxable = 0;

  // Add primary income if taxable
  totalTaxable += convertToAnnual(primaryIncome, primaryIncomeFrequency);

  // Add additional taxable income sources
  additionalSources.forEach((source) => {
    if (source.isTaxable !== false) {
      totalTaxable += convertToAnnual(source.amount, source.frequency);
    }
  });

  // Subtract pre-tax deductions
  totalTaxable = Math.max(0, totalTaxable - preTaxDeductions);

  return totalTaxable;
};

/**
 * Calculate comprehensive income breakdown including taxes and budget allocation
 *
 * @param primaryIncome - Primary income amount
 * @param primaryIncomeFrequency - Primary income frequency
 * @param additionalSources - Array of additional income sources
 * @param preTaxDeductions - Total pre-tax deductions
 * @param postTaxDeductions - Total post-tax deductions
 * @param filingStatus - Tax filing status
 * @param state - State for state tax calculation
 * @returns Comprehensive income breakdown
 */
export const calculateIncomeBreakdown = (
  primaryIncome: number,
  primaryIncomeFrequency: string,
  additionalSources: IncomeSource[] = [],
  preTaxDeductions: number = 0,
  postTaxDeductions: number = 0,
  filingStatus: FilingStatus = 'SINGLE',
  state?: string
): IncomeBreakdown => {
  // Calculate gross income
  const grossIncome = calculateTotalAnnualIncome(
    primaryIncome,
    primaryIncomeFrequency,
    additionalSources
  );

  // Calculate taxable income
  const taxableIncome = calculateTaxableIncome(
    primaryIncome,
    primaryIncomeFrequency,
    additionalSources,
    preTaxDeductions
  );

  // Calculate taxes
  const federalTax = calculateFederalIncomeTax(taxableIncome, filingStatus);
  const stateTax = calculateStateTax(taxableIncome, state);
  const { total: ficaTax } = calculateFICATaxes(taxableIncome);
  const totalTax = federalTax + stateTax + ficaTax;

  // Calculate after-tax income
  let afterTaxIncome = grossIncome - totalTax - postTaxDeductions;
  afterTaxIncome = Math.max(0, afterTaxIncome); // Ensure non-negative

  // Calculate effective tax rate
  const effectiveTaxRate = grossIncome > 0 ? (totalTax / grossIncome) * 100 : 0;

  // Calculate budget allocation (50/30/20 rule)
  const needs = afterTaxIncome * BUDGET_RULE.NEEDS_TARGET;
  const wants = afterTaxIncome * BUDGET_RULE.WANTS_TARGET;
  const savings = afterTaxIncome * BUDGET_RULE.SAVINGS_TARGET;

  // Determine budget health status
  let budgetStatus: 'healthy' | 'needs_adjustment' | 'needs_major_adjustment' = 'healthy';
  const needsRatio = needs / afterTaxIncome || 0;
  const savingsRatio = savings / afterTaxIncome || 0;

  if (needsRatio > 0.6 || savingsRatio < 0.1) {
    budgetStatus = 'needs_major_adjustment';
  } else if (needsRatio > 0.5 || savingsRatio < 0.2) {
    budgetStatus = 'needs_adjustment';
  }

  return {
    grossIncome,
    taxableIncome,
    federalTax,
    stateTax,
    ficaTax,
    totalTax,
    afterTaxIncome,
    effectiveTaxRate,
    budgetAllocation: {
      needs,
      wants,
      savings,
      needsPercentage: BUDGET_RULE.NEEDS_TARGET * 100,
      wantsPercentage: BUDGET_RULE.WANTS_TARGET * 100,
      savingsPercentage: BUDGET_RULE.SAVINGS_TARGET * 100,
      status: budgetStatus,
    },
  };
};

/**
 * Calculate monthly income from annual income
 *
 * @param annualIncome - Annual income amount
 * @returns Monthly income amount
 */
export const calculateMonthlyFromAnnual = (annualIncome: number): number => {
  return annualIncome / PAY_PERIODS.MONTHLY;
};

/**
 * Calculate simple after-tax income using a flat tax rate
 *
 * @param annualIncome - Annual income amount
 * @param taxRate - Tax rate as a percentage (e.g., 25 for 25%)
 * @returns After-tax annual income
 */
export const calculateSimpleAfterTaxIncome = (annualIncome: number, taxRate: number): number => {
  if (taxRate < 0 || taxRate > 100) {
    throw new Error('Tax rate must be between 0 and 100');
  }

  return annualIncome * (1 - taxRate / 100);
};

/**
 * Calculate hourly rate equivalent from annual salary
 *
 * @param annualIncome - Annual income amount
 * @param hoursPerWeek - Number of hours worked per week (default: 40)
 * @param weeksPerYear - Number of weeks worked per year (default: 50)
 * @returns Hourly rate
 */
export const calculateHourlyRate = (
  annualIncome: number,
  hoursPerWeek: number = 40,
  weeksPerYear: number = 50
): number => {
  if (hoursPerWeek <= 0 || weeksPerYear <= 0) {
    throw new Error('Hours per week and weeks per year must be greater than 0');
  }

  return annualIncome / (hoursPerWeek * weeksPerYear);
};

/**
 * Calculate annual salary from hourly rate
 *
 * @param hourlyRate - Hourly rate
 * @param hoursPerWeek - Number of hours worked per week (default: 40)
 * @param weeksPerYear - Number of weeks worked per year (default: 50)
 * @returns Annual salary
 */
export const calculateAnnualFromHourly = (
  hourlyRate: number,
  hoursPerWeek: number = 40,
  weeksPerYear: number = 50
): number => {
  if (hoursPerWeek <= 0 || weeksPerYear <= 0) {
    throw new Error('Hours per week and weeks per year must be greater than 0');
  }

  return hourlyRate * hoursPerWeek * weeksPerYear;
};

/**
 * Calculate the percentage of income spent on a specific expense
 *
 * @param expenseAmount - Expense amount
 * @param incomeAmount - Income amount
 * @param frequency - Frequency of the expense ('monthly', 'annual', etc.)
 * @param incomeFrequency - Frequency of the income ('monthly', 'annual', etc.)
 * @returns Percentage of income spent on the expense
 */
export const calculateExpenseToIncomeRatio = (
  expenseAmount: number,
  incomeAmount: number,
  frequency: string = 'monthly',
  incomeFrequency: string = 'monthly'
): number => {
  if (incomeAmount <= 0) return 0;

  const annualExpense = convertToAnnual(expenseAmount, frequency);
  const annualIncome = convertToAnnual(incomeAmount, incomeFrequency);

  return (annualExpense / annualIncome) * 100;
};

/**
 * Calculate debt-to-income ratio
 *
 * @param totalDebtPayments - Total monthly debt payments
 * @param grossMonthlyIncome - Gross monthly income
 * @returns Debt-to-income ratio as a percentage
 */
export const calculateDebtToIncomeRatio = (
  totalDebtPayments: number,
  grossMonthlyIncome: number
): number => {
  if (grossMonthlyIncome <= 0) return 0;
  return (totalDebtPayments / grossMonthlyIncome) * 100;
};

/**
 * Calculates the total monthly income from a list of income sources.
 *
 * @param incomeSources - An array of income source objects.
 * @returns The total monthly income.
 */
export const calculateTotalMonthlyIncome = (sources: IncomeSource[]): number => {
  return sources.reduce((total, source) => {
    let monthlyAmount = source.amount;

    switch (source.frequency.toLowerCase()) {
      case 'monthly':
        monthlyAmount = source.amount;
        break;
      case 'weekly':
        monthlyAmount = source.amount * 52 / 12;
        break;
      case 'biweekly':
        monthlyAmount = source.amount * 26 / 12;
        break;
      case 'semimonthly':
        monthlyAmount = source.amount * 2;
        break;
      case 'annual':
        monthlyAmount = source.amount / 12;
        break;
      default:
        console.warn(`Unrecognized income frequency: ${source.frequency}. Assuming monthly if amount > 0.`);
        monthlyAmount = source.amount > 0 ? source.amount : 0;
    }

    return total + monthlyAmount;
  }, 0);
};
