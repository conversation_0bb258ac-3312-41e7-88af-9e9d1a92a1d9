import { z } from 'zod';
import { validationMessages } from '../schemas/base';

type ValidationResult = {
  isValid: boolean;
  errors: Record<string, string>;
};

export class ValidationUtils {
  /**
   * Validates form data against a Zod schema
   */
  static validateFormData<T>(data: T, schema: z.ZodSchema<T>): ValidationResult {
    try {
      schema.parse(data);
      return { isValid: true, errors: {} };
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors = error.errors.reduce<Record<string, string>>((acc, curr) => {
          const path = curr.path.join('.');
          acc[path] = curr.message;
          return acc;
        }, {});
        return { isValid: false, errors };
      }
      return { isValid: false, errors: { form: validationMessages.genericError } };
    }
  }

  /**
   * Validates a single field against a schema
   */
  static validateField<T>(
    fieldName: string,
    value: any,
    schema: z.ZodSchema<T>
  ): string | undefined {
    try {
      schema.parse(value);
      return undefined;
    } catch (error) {
      if (error instanceof z.ZodError) {
        return error.errors[0]?.message;
      }
      return validationMessages.genericError;
    }
  }

  /**
   * Creates a debounced validation function
   */
  static createDebouncedValidator<T>(
    validateFn: (value: T) => string | undefined,
    delay = 500
  ): (value: T) => Promise<string | undefined> {
    let timeoutId: NodeJS.Timeout;

    return (value: T) => {
      return new Promise((resolve) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          resolve(validateFn(value));
        }, delay);
      });
    };
  }

  /**
   * Validates required fields
   */
  static validateRequired(value: any, fieldName: string): string | undefined {
    if (value === undefined || value === null || value === '') {
      return `${fieldName} is required`;
    }
    return undefined;
  }

  /**
   * Validates email format
   */
  static validateEmail(email: string): string | undefined {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return validationMessages.invalidEmail;
    }
    return undefined;
  }

  /**
   * Validates phone number format
   */
  static validatePhone(phone: string): string | undefined {
    const phoneRegex = /^\+?[\d\s-()]{10,}$/;
    if (!phoneRegex.test(phone)) {
      return validationMessages.invalidPhone;
    }
    return undefined;
  }

  /**
   * Validates that a number is within a range
   */
  static validateNumberRange(
    value: number,
    min: number,
    max: number,
    fieldName: string
  ): string | undefined {
    if (value < min || value > max) {
      return `${fieldName} must be between ${min} and ${max}`;
    }
    return undefined;
  }

  /**
   * Validates that a string meets minimum and maximum length requirements
   */
  static validateStringLength(
    value: string,
    min: number,
    max: number,
    fieldName: string
  ): string | undefined {
    if (value.length < min) {
      return `${fieldName} must be at least ${min} characters`;
    }
    if (value.length > max) {
      return `${fieldName} cannot exceed ${max} characters`;
    }
    return undefined;
  }

  /**
   * Validates a date is in the past
   */
  static validatePastDate(date: Date, fieldName: string): string | undefined {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (date > today) {
      return `${fieldName} must be in the past`;
    }
    return undefined;
  }

  /**
   * Validates a date is in the future
   */
  static validateFutureDate(date: Date, fieldName: string): string | undefined {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (date < today) {
      return `${fieldName} must be in the future`;
    }
    return undefined;
  }
}

/**
 * Creates a validation function that combines multiple validators
 */
export const createValidator = <T>(...validators: ((value: T) => string | undefined)[]) => {
  return (value: T): string | undefined => {
    for (const validator of validators) {
      const error = validator(value);
      if (error) return error;
    }
    return undefined;
  };
};

/**
 * Creates a schema validator with custom error messages
 */
export const createSchemaValidator = <T>(
  schema: z.ZodSchema<T>,
  customMessages: Record<string, string> = {}
) => {
  return (data: T): ValidationResult => {
    try {
      schema.parse(data);
      return { isValid: true, errors: {} };
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors = error.errors.reduce<Record<string, string>>((acc, curr) => {
          const path = curr.path.join('.');
          acc[path] = customMessages[path] || curr.message;
          return acc;
        }, {});
        return { isValid: false, errors };
      }
      return { isValid: false, errors: { form: validationMessages.genericError } };
    }
  };
};
