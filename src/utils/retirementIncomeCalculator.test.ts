/**
 * Retirement Income Calculator Utility Tests
 */

import {
  convertToMonthly,
  convertToAnnual,
  calculateTotalMonthlyIncome,
  calculateIncomeReplacementRatio,
  calculateIncomeGap,
  applyInflationAdjustment,
  calculateIncomeDuration,
  IncomeFrequency,
} from './retirementIncomeCalculator';

describe('Retirement Income Calculator Utility', () => {
  describe('convertToMonthly', () => {
    it('converts monthly income correctly', () => {
      expect(convertToMonthly(1000, IncomeFrequency.MONTHLY)).toBe(1000);
    });

    it('converts annual income correctly', () => {
      expect(convertToMonthly(120000, IncomeFrequency.ANNUAL)).toBe(10000);
    });

    it('converts biweekly income correctly', () => {
      expect(convertToMonthly(2000, IncomeFrequency.BIWEEKLY)).toBeCloseTo(4333.33, 2);
    });

    it('converts weekly income correctly', () => {
      expect(convertToMonthly(1000, IncomeFrequency.WEEKLY)).toBeCloseTo(4333.33, 2);
    });

    it('handles one-time income correctly', () => {
      expect(convertToMonthly(10000, IncomeFrequency.ONE_TIME)).toBe(0);
    });

    it('handles string input correctly', () => {
      expect(convertToMonthly('1000', IncomeFrequency.MONTHLY)).toBe(1000);
    });

    it('handles invalid input gracefully', () => {
      expect(convertToMonthly(NaN, IncomeFrequency.MONTHLY)).toBe(0);
      expect(convertToMonthly('invalid', IncomeFrequency.MONTHLY)).toBe(0);
    });
  });

  describe('convertToAnnual', () => {
    it('converts monthly income correctly', () => {
      expect(convertToAnnual(1000, IncomeFrequency.MONTHLY)).toBe(12000);
    });

    it('converts annual income correctly', () => {
      expect(convertToAnnual(120000, IncomeFrequency.ANNUAL)).toBe(120000);
    });

    it('converts biweekly income correctly', () => {
      expect(convertToAnnual(2000, IncomeFrequency.BIWEEKLY)).toBe(52000);
    });

    it('converts weekly income correctly', () => {
      expect(convertToAnnual(1000, IncomeFrequency.WEEKLY)).toBe(52000);
    });

    it('handles one-time income correctly', () => {
      expect(convertToAnnual(10000, IncomeFrequency.ONE_TIME)).toBe(0);
    });

    it('handles string input correctly', () => {
      expect(convertToAnnual('1000', IncomeFrequency.MONTHLY)).toBe(12000);
    });

    it('handles invalid input gracefully', () => {
      expect(convertToAnnual(NaN, IncomeFrequency.MONTHLY)).toBe(0);
      expect(convertToAnnual('invalid', IncomeFrequency.MONTHLY)).toBe(0);
    });
  });

  describe('calculateTotalMonthlyIncome', () => {
    it('calculates total monthly income correctly with multiple sources', () => {
      const socialSecurity = {
        estimatedMonthlyBenefit: 2000,
        startAge: 67,
      };

      const pension = {
        hasEmployerPension: true,
        estimatedMonthlyBenefit: 1500,
        startAge: 65,
      };

      const incomeSources = [
        {
          id: '1',
          type: 'Part-time work',
          amount: 1000,
          frequency: IncomeFrequency.MONTHLY,
          startAge: 65,
          endAge: 70,
        },
        {
          id: '2',
          type: 'Rental income',
          amount: 2000,
          frequency: IncomeFrequency.MONTHLY,
          startAge: 67,
        },
      ];

      const result = calculateTotalMonthlyIncome(socialSecurity, pension, incomeSources, 60);

      // At age 67 (Social Security start age), all income sources are active
      expect(result.totalMonthly).toBe(6500); // 2000 (SS) + 1500 (pension) + 1000 (part-time) + 2000 (rental)

      // Check income by age
      expect(result.byAge[65]).toBe(2500); // 1500 (pension) + 1000 (part-time)
      expect(result.byAge[67]).toBe(6500); // 2000 (SS) + 1500 (pension) + 1000 (part-time) + 2000 (rental)
      expect(result.byAge[71]).toBe(5500); // 2000 (SS) + 1500 (pension) + 2000 (rental)

      // Check income breakdown
      expect(result.incomeBreakdown['Social Security']).toBe(2000);
      expect(result.incomeBreakdown['Pension']).toBe(1500);
      expect(result.incomeBreakdown['Other Income']).toBe(3000);
    });

    it('handles no pension correctly', () => {
      const socialSecurity = {
        estimatedMonthlyBenefit: 2000,
        startAge: 67,
      };

      const pension = {
        hasEmployerPension: false,
        estimatedMonthlyBenefit: 0,
        startAge: 65,
      };

      const incomeSources = [];

      const result = calculateTotalMonthlyIncome(socialSecurity, pension, incomeSources, 60);

      expect(result.totalMonthly).toBe(2000);
      expect(result.incomeBreakdown['Pension']).toBe(0);
    });

    it('handles invalid input gracefully', () => {
      const socialSecurity = {
        estimatedMonthlyBenefit: 'invalid',
        startAge: 'invalid',
      };

      const pension = {
        hasEmployerPension: true,
        estimatedMonthlyBenefit: 'invalid',
        startAge: 'invalid',
      };

      const incomeSources = [
        {
          id: '1',
          type: 'Invalid',
          amount: 'invalid',
          frequency: IncomeFrequency.MONTHLY,
          startAge: 'invalid',
          endAge: 'invalid',
        },
      ];

      const result = calculateTotalMonthlyIncome(socialSecurity, pension, incomeSources, 'invalid');

      expect(result.totalMonthly).toBe(0);
    });
  });

  describe('calculateIncomeReplacementRatio', () => {
    it('calculates income replacement ratio correctly', () => {
      expect(calculateIncomeReplacementRatio(6000, 10000)).toBe(60);
    });

    it('handles zero current income correctly', () => {
      expect(calculateIncomeReplacementRatio(6000, 0)).toBe(0);
    });

    it('handles invalid input gracefully', () => {
      expect(calculateIncomeReplacementRatio(NaN, 10000)).toBe(0);
      expect(calculateIncomeReplacementRatio(6000, NaN)).toBe(0);
    });
  });

  describe('calculateIncomeGap', () => {
    it('calculates income gap correctly when there is a shortfall', () => {
      expect(calculateIncomeGap(6000, 8000)).toBe(2000);
    });

    it('returns zero when there is no shortfall', () => {
      expect(calculateIncomeGap(8000, 6000)).toBe(0);
    });

    it('handles invalid input gracefully', () => {
      expect(calculateIncomeGap(NaN, 8000)).toBe(0);
      expect(calculateIncomeGap(6000, NaN)).toBe(0);
    });
  });

  describe('applyInflationAdjustment', () => {
    it('applies inflation adjustment correctly', () => {
      // $1,000 with 2.5% inflation for 10 years
      // Expected: $1,000 * (1.025)^10 ≈ $1,280.08
      expect(applyInflationAdjustment(1000, 10, 0.025)).toBeCloseTo(1280.08, 2);
    });

    it('handles zero years correctly', () => {
      expect(applyInflationAdjustment(1000, 0, 0.025)).toBe(1000);
    });

    it('handles invalid input gracefully', () => {
      expect(applyInflationAdjustment(NaN, 10, 0.025)).toBe(NaN);
      expect(applyInflationAdjustment(1000, NaN, 0.025)).toBe(1000);
      expect(applyInflationAdjustment(1000, 10, NaN)).toBe(1000);
    });
  });

  describe('calculateIncomeDuration', () => {
    it('calculates income duration correctly with positive real return', () => {
      // $1,000,000 savings, $4,000 monthly income, 4% return, 2.5% inflation
      // Expected: ~30 years
      expect(calculateIncomeDuration(1000000, 4000, 0.04, 0.025)).toBeCloseTo(30, 0);
    });

    it('calculates income duration correctly with zero real return', () => {
      // $1,000,000 savings, $4,000 monthly income, 2.5% return, 2.5% inflation
      // Expected: $1,000,000 / ($4,000 * 12) = ~20.83 years
      expect(calculateIncomeDuration(1000000, 4000, 0.025, 0.025)).toBeCloseTo(20.83, 2);
    });

    it('handles zero monthly income correctly', () => {
      expect(calculateIncomeDuration(1000000, 0)).toBe(0);
    });

    it('handles invalid input gracefully', () => {
      expect(calculateIncomeDuration(NaN, 4000)).toBe(0);
      expect(calculateIncomeDuration(1000000, NaN)).toBe(0);
    });
  });
});
