/**
 * Insurance Calculator Utility Tests
 */

import {
  calculateLifeInsuranceNeeds,
  calculateDisabilityInsuranceNeeds,
  calculateLongTermCareInsuranceNeeds,
  calculatePropertyInsuranceNeeds,
  calculateHealthInsuranceNeeds,
  calculateUmbrellaInsuranceNeeds,
} from './insuranceCalculator';

describe('Insurance Calculator Utility', () => {
  describe('calculateLifeInsuranceNeeds', () => {
    it('calculates life insurance needs correctly', () => {
      // Test case:
      // Annual income: $100,000
      // Years to replace: 10
      // Outstanding debt: $200,000
      // Education funds: $100,000
      // Final expenses: $20,000
      // Existing life insurance: $300,000
      // Liquid assets: $50,000
      // Expected result: $100,000 * 10 + $200,000 + $100,000 + $20,000 - $300,000 - $50,000 = $970,000
      const result = calculateLifeInsuranceNeeds(100000, 10, 200000, 100000, 20000, 300000, 50000);

      expect(result).toBe(970000);
    });

    it('returns zero when existing resources exceed needs', () => {
      // Test case where existing resources exceed needs
      const result = calculateLifeInsuranceNeeds(50000, 5, 100000, 0, 10000, 500000, 0);

      expect(result).toBe(0);
    });

    it('handles zero income correctly', () => {
      const result = calculateLifeInsuranceNeeds(0, 10, 200000, 100000, 20000, 300000, 50000);
      // Expected: 0 * 10 + 200000 + 100000 + 20000 - 300000 - 50000 = -30000, should be 0
      expect(result).toBe(0);
    });

    it('handles zero years to replace correctly', () => {
      const result = calculateLifeInsuranceNeeds(100000, 0, 200000, 100000, 20000, 300000, 50000);
      // Expected: 100000 * 0 + 200000 + 100000 + 20000 - 300000 - 50000 = -30000, should be 0
      expect(result).toBe(0);
    });

    it('handles zero debt, education, final expenses correctly', () => {
      const result = calculateLifeInsuranceNeeds(100000, 10, 0, 0, 0, 300000, 50000);
      // Expected: 100000 * 10 + 0 + 0 + 0 - 300000 - 50000 = 650000
      expect(result).toBe(650000);
    });

    it('handles zero existing life insurance and liquid assets correctly', () => {
      const result = calculateLifeInsuranceNeeds(100000, 10, 200000, 100000, 20000, 0, 0);
      // Expected: 100000 * 10 + 200000 + 100000 + 20000 - 0 - 0 = 1320000
      expect(result).toBe(1320000);
    });

    it('handles all zero inputs correctly', () => {
      const result = calculateLifeInsuranceNeeds(0, 0, 0, 0, 0, 0, 0);
      expect(result).toBe(0);
    });
  });

  describe('calculateDisabilityInsuranceNeeds', () => {
    it('calculates disability insurance needs correctly', () => {
      // Test case:
      // Monthly income: $8,000
      // Monthly expenses: $6,000
      // Existing disability insurance: $2,000
      // Other income sources: $1,000
      // Expected result: $6,000 - $2,000 - $1,000 = $3,000
      const result = calculateDisabilityInsuranceNeeds(8000, 6000, 2000, 1000);

      expect(result).toBe(3000);
    });

    it('returns zero when existing resources exceed needs', () => {
      // Test case where existing resources exceed needs
      const result = calculateDisabilityInsuranceNeeds(5000, 4000, 3000, 2000);

      expect(result).toBe(0);
    });

    it('caps the income gap at monthly income', () => {
      // Test case where expenses exceed income
      const result = calculateDisabilityInsuranceNeeds(5000, 7000, 1000, 0);

      // Should be capped at income: $5,000 - $1,000 - $0 = $4,000
      expect(result).toBe(4000);
    });

    it('handles zero monthly income correctly', () => {
      const result = calculateDisabilityInsuranceNeeds(0, 6000, 2000, 1000);
      // Expected: Math.max(0, Math.min(0, 6000) - 2000 - 1000) = Math.max(0, 0 - 2000 - 1000) = 0
      expect(result).toBe(0);
    });

    it('handles zero monthly expenses correctly', () => {
      const result = calculateDisabilityInsuranceNeeds(8000, 0, 2000, 1000);
      // Expected: Math.max(0, Math.min(8000, 0) - 2000 - 1000) = Math.max(0, 0 - 2000 - 1000) = 0
      expect(result).toBe(0);
    });

    it('handles zero existing disability insurance and other income correctly', () => {
      const result = calculateDisabilityInsuranceNeeds(8000, 6000, 0, 0);
      // Expected: Math.max(0, Math.min(8000, 6000) - 0 - 0) = Math.max(0, 6000) = 6000
      expect(result).toBe(6000);
    });

    it('handles all zero inputs correctly', () => {
      const result = calculateDisabilityInsuranceNeeds(0, 0, 0, 0);
      expect(result).toBe(0);
    });
  });

  describe('calculateLongTermCareInsuranceNeeds', () => {
    it('calculates long-term care insurance needs correctly', () => {
      // Test case from comments:
      // Monthly care expense: $6,000
      // Years of care: 3
      // Existing LTC insurance: $2,000/month
      // Monthly income during retirement: $3,000
      // Available assets: $100,000
      // Total care expenses: $6,000 * 3 * 12 = $216,000
      // Resources available: ($3,000 * 3 * 12) + $100,000 + ($2,000 * 3 * 12) = $108,000 + $100,000 + $72,000 = $280,000
      // Gap: $216,000 - $280,000 = -64,000, should be 0 since resources exceed expenses
      const result1 = calculateLongTermCareInsuranceNeeds(
        6000, // monthlyCareExpense
        3, // yearsOfCare
        2000, // existingLTCInsurance
        3000, // monthlyRetirementIncome
        100000 // availableAssets
      );
      expect(result1).toBe(0); // Resources exceed needs

      // Test case with a need:
      // Monthly care expense: $8,000
      // Years of care: 5
      // Existing LTC insurance: $1,000/month
      // Monthly income during retirement: $2,000
      // Available assets: $50,000
      // Total care expenses: $8,000 * 5 * 12 = $480,000
      // Resources available: ($2,000 * 5 * 12) + $50,000 + ($1,000 * 5 * 12) = $120,000 + $50,000 + $60,000 = $230,000
      // Gap: $480,000 - $230,000 = $250,000
      const result2 = calculateLongTermCareInsuranceNeeds(8000, 5, 1000, 2000, 50000);
      expect(result2).toBe(250000);
    });

    it('returns zero when existing resources exceed needs', () => {
      // Test case where existing resources exceed needs
      const result = calculateLongTermCareInsuranceNeeds(5000, 2, 3000, 2000, 100000);

      expect(result).toBe(0);
    });
  });

  describe('calculatePropertyInsuranceNeeds', () => {
    it('calculates property insurance needs correctly', () => {
      // Test case:
      // Property value: $400,000
      // Replacement cost: $350,000
      // Personal property value: $150,000
      // Liability risk: $300,000
      // Expected result:
      // Dwelling coverage: $350,000
      // Personal property coverage: $175,000 (50% of dwelling coverage, but at least personal property value)
      // Liability coverage: $300,000
      // Total coverage: $825,000
      const result = calculatePropertyInsuranceNeeds(400000, 350000, 150000, 300000);

      expect(result).toEqual({
        dwellingCoverage: 350000,
        personalPropertyCoverage: 175000,
        liabilityCoverage: 300000,
        totalCoverage: 825000,
      });
    });

    it('uses personal property value when it exceeds 50% of dwelling coverage', () => {
      // Test case where personal property value exceeds 50% of dwelling coverage
      const result = calculatePropertyInsuranceNeeds(300000, 250000, 200000, 300000);

      expect(result.personalPropertyCoverage).toBe(200000);
    });

    it('handles zero property value or replacement cost correctly', () => {
      const result1 = calculatePropertyInsuranceNeeds(0, 0, 150000, 300000);
      expect(result1).toEqual({
        dwellingCoverage: 0,
        personalPropertyCoverage: 150000, // Based on personal property value
        liabilityCoverage: 300000,
        totalCoverage: 450000,
      });
    });

    it('handles zero personal property value correctly', () => {
      const result = calculatePropertyInsuranceNeeds(400000, 350000, 0, 300000);
      expect(result.personalPropertyCoverage).toBeCloseTo(350000 * 0.5, 2); // 50% of dwelling coverage
    });

    it('handles zero liability risk correctly', () => {
      const result = calculatePropertyInsuranceNeeds(400000, 350000, 150000, 0);
      expect(result.liabilityCoverage).toBe(0);
    });

    it('handles all zero inputs correctly', () => {
      const result = calculatePropertyInsuranceNeeds(0, 0, 0, 0);
      expect(result).toEqual({
        dwellingCoverage: 0,
        personalPropertyCoverage: 0,
        liabilityCoverage: 0,
        totalCoverage: 0,
      });
    });
  });

  describe('calculateHealthInsuranceNeeds', () => {
    it('recommends Bronze plan for low medical expenses', () => {
      const result = calculateHealthInsuranceNeeds(1000, false, 1);

      expect(result.recommendedPlanType).toBe('Bronze');
    });

    it('recommends Silver plan for moderate medical expenses', () => {
      const result = calculateHealthInsuranceNeeds(3000, false, 1);

      expect(result.recommendedPlanType).toBe('Silver');
    });

    it('recommends Gold plan for high medical expenses', () => {
      const result = calculateHealthInsuranceNeeds(6000, false, 1);

      expect(result.recommendedPlanType).toBe('Gold');
    });

    it('recommends Gold plan for chronic conditions', () => {
      const result = calculateHealthInsuranceNeeds(2000, true, 1);

      expect(result.recommendedPlanType).toBe('Gold');
    });

    it('adjusts for family size', () => {
      const singleResult = calculateHealthInsuranceNeeds(2000, false, 1);

      const familyResult = calculateHealthInsuranceNeeds(2000, false, 3);

      // Family deductible should be higher
      expect(familyResult.recommendedDeductibleRange.min).toBeGreaterThan(
        singleResult.recommendedDeductibleRange.min
      );

      // Family premium should be higher
      expect(familyResult.estimatedMonthlyPremiumRange.min).toBeGreaterThan(
        singleResult.estimatedMonthlyPremiumRange.min
      );
    });

    it('handles zero medical expenses correctly', () => {
      const result = calculateHealthInsuranceNeeds(0, false, 1);
      expect(result.recommendedPlanType).toBe('Bronze');
    });

    it('handles large medical expenses correctly', () => {
      const result = calculateHealthInsuranceNeeds(100000, false, 1);
      expect(result.recommendedPlanType).toBe('Gold'); // Should still recommend Gold for very high expenses
    });

    it('handles zero family size correctly', () => {
      // Although family size should realistically be at least 1, testing 0 for robustness
      const result = calculateHealthInsuranceNeeds(2000, false, 0);
      expect(result.recommendedPlanType).toBe('Bronze'); // Defaulting to a basic plan
      expect(result.recommendedDeductibleRange.min).toBeGreaterThan(0);
    });
  });

  describe('calculateUmbrellaInsuranceNeeds', () => {
    it('calculates umbrella insurance needs based on net worth', () => {
      // Test case:
      // Net worth: $1,200,000
      // Annual income: $100,000
      // Risk factors: 1
      // Expected result: $1,200,000 + $500,000 = $1,700,000, rounded up to $2,000,000
      const result = calculateUmbrellaInsuranceNeeds(1200000, 100000, 1);

      expect(result).toBe(2000000);
    });

    it('calculates umbrella insurance needs based on income when higher', () => {
      // Test case where income-based calculation is higher
      // Net worth: $500,000
      // Annual income: $200,000
      // Risk factors: 1
      // Expected result: $200,000 * 5 + $500,000 = $1,500,000, rounded up to $2,000,000
      const result = calculateUmbrellaInsuranceNeeds(500000, 200000, 1);

      expect(result).toBe(2000000);
    });

    it('increases coverage for multiple risk factors', () => {
      // Test case with multiple risk factors
      const result1 = calculateUmbrellaInsuranceNeeds(1000000, 100000, 1);

      const result2 = calculateUmbrellaInsuranceNeeds(1000000, 100000, 3);

      expect(result2).toBeGreaterThan(result1);
    });

    it('returns at least $1,000,000', () => {
      // Test case with low net worth and income
      const result = calculateUmbrellaInsuranceNeeds(200000, 50000, 0);

      expect(result).toBe(1000000);
    });

    it('handles zero net worth and income correctly', () => {
      const result = calculateUmbrellaInsuranceNeeds(0, 0, 0);
      expect(result).toBe(1000000); // Should return minimum coverage
    });

    it('handles zero risk factors correctly', () => {
      const result = calculateUmbrellaInsuranceNeeds(1200000, 100000, 0);
      // Expected: Max(Net Worth + 0, Income * 5 + 0) rounded up to nearest million (1.2M vs 0.5M) = 1.2M, rounded to 2M, but minimum is 1M
      // Re-evaluating based on code: Max(1.2M + 0, 0.1M * 5 + 0) = Max(1.2M, 0.5M) = 1.2M -> 2M rounded. Min 1M. Result 2M
      expect(result).toBe(2000000); // Risk factors don't seem to directly impact calculation in the original code, just a parameter. Testing assumption based on no change for 0 risk.
    });
  });
});
