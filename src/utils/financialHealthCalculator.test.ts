/**
 * Financial Health Calculator Utility Tests
 */

import {
  calculateCashFlowHealthScore,
  calculateDebtManagementScore,
  calculateEmergencyFundScore,
  calculateNetWorthScore,
  calculateSimpleFinancialHealthScore,
  calculateDetailedFinancialHealthScore,
  FINANCIAL_HEALTH_WEIGHTS,
} from './financialHealthCalculator';

describe('Financial Health Calculator Utility', () => {
  describe('calculateCashFlowHealthScore', () => {
    it('calculates cash flow health score correctly for positive cash flow', () => {
      // Monthly income: 5000, Monthly cash flow: 1000 (20% savings rate)
      const monthlyIncome = 5000;
      const monthlyCashFlow = 1000;
      const expectedScore = 50 + 50; // 50 for savings rate >= 20%, 50 for cash flow ratio >= 20%
      expect(calculateCashFlowHealthScore(monthlyCashFlow, monthlyIncome)).toBe(expectedScore);
    });

    it('calculates cash flow health score correctly for zero cash flow', () => {
      const monthlyIncome = 5000;
      const monthlyCashFlow = 0;
      const expectedScore = 0 + 10; // 0 for savings rate 0%, 10 for cash flow ratio 0% (but income > 0)
      expect(calculateCashFlowHealthScore(monthlyCashFlow, monthlyIncome)).toBe(expectedScore);
    });

    it('calculates cash flow health score correctly for negative cash flow', () => {
      const monthlyIncome = 5000;
      const monthlyCashFlow = -500;
      const expectedScore = 0 + 0; // 0 for negative savings rate, 0 for negative cash flow
      expect(calculateCashFlowHealthScore(monthlyCashFlow, monthlyIncome)).toBe(expectedScore);
    });

    it('handles zero monthly income correctly', () => {
      const monthlyIncome = 0;
      const monthlyCashFlow = 1000; // Positive cash flow with zero income
      const expectedScore = 50; // Default moderate score
      expect(calculateCashFlowHealthScore(monthlyCashFlow, monthlyIncome)).toBe(expectedScore);
    });

    it('handles NaN inputs gracefully', () => {
      expect(calculateCashFlowHealthScore(NaN, 5000)).toBe(0); // monthlyCashFlow is NaN
      expect(calculateCashFlowHealthScore(1000, NaN)).toBe(0); // monthlyIncome is NaN
      expect(calculateCashFlowHealthScore(NaN, NaN)).toBe(0); // both are NaN
    });

    it('calculates score correctly for different savings rate tiers', () => {
      // Test savings rate tiers (0-50 points)
      expect(calculateCashFlowHealthScore(0, 5000)).toBe(10); // 0% savings rate (monthlyCashFlow 0, monthlyIncome 5000) -> savingsRateScore 0, cashFlowScore 10
      expect(calculateCashFlowHealthScore(200, 5000)).toBe(10 + 10); // 4% savings rate -> savingsRateScore 10
      expect(calculateCashFlowHealthScore(250, 5000)).toBe(20 + 10); // 5% savings rate -> savingsRateScore 20
      expect(calculateCashFlowHealthScore(400, 4000)).toBe(30 + 30); // 10% savings rate -> savingsRateScore 30, cashFlowRatio 10% -> cashFlowScore 30
      expect(calculateCashFlowHealthScore(750, 5000)).toBe(40 + 30); // 15% savings rate -> savingsRateScore 40, cashFlowRatio 15% -> cashFlowScore 30
      expect(calculateCashFlowHealthScore(1000, 5000)).toBe(50 + 50); // 20% savings rate -> savingsRateScore 50, cashFlowRatio 20% -> cashFlowScore 50
    });

    it('calculates score correctly for different cash flow ratio tiers', () => {
      // Test cash flow ratio tiers (0-50 points), assuming positive cash flow and income
      // savingsRateScore will be 0 if monthlyCashFlow <= 0 or monthlyIncome <= 0, covered by other tests.
      // For cashFlowScore, the logic is similar to savingsRateScore when monthlyIncome > 0.
      expect(calculateCashFlowHealthScore(50, 1000)).toBe(10 + 10); // 5% cash flow ratio
      expect(calculateCashFlowHealthScore(100, 1000)).toBe(20 + 20); // 10% cash flow ratio
      expect(calculateCashFlowHealthScore(150, 1000)).toBe(30 + 30); // 15% cash flow ratio
      expect(calculateCashFlowHealthScore(200, 1000)).toBe(40 + 40); // 20% cash flow ratio
      expect(calculateCashFlowHealthScore(250, 1000)).toBe(50 + 50); // 25% cash flow ratio
    });
  });

  describe('calculateDebtManagementScore', () => {
    it('calculates debt management score correctly with DTI provided', () => {
      // Total liabilities: 50000, Annual income: 100000, DTI: 50%
      // DTI score (<=50%): 20, Debt amount score (50000/100000=0.5 <= 0.5): 40. Total: 60
      const totalLiabilities = 50000;
      const annualIncome = 100000;
      const debtToIncomeRatio = 50; // as percentage
      const expectedScore = 20 + 40;
      expect(calculateDebtManagementScore(totalLiabilities, annualIncome, debtToIncomeRatio)).toBe(
        expectedScore
      );
    });

    it('calculates debt management score correctly without DTI provided', () => {
      // Total liabilities: 50000, Annual income: 100000
      // Calculated DTI: (50000 / 100000) * 100 = 50%
      // DTI score (<=50%): 20, Debt amount score (0.5 <= 0.5): 40. Total: 60
      const totalLiabilities = 50000;
      const annualIncome = 100000;
      const expectedScore = 20 + 40;
      expect(calculateDebtManagementScore(totalLiabilities, annualIncome)).toBe(expectedScore);
    });

    it('handles zero annual income correctly', () => {
      // If annual income is zero, DTI defaults to 100%, debtToIncomeMultiple defaults to 10
      // DTI score (>50%): 10, Debt amount score (>4): 5. Total: 15
      const totalLiabilities = 50000;
      const annualIncome = 0;
      const expectedScore = 10 + 5;
      expect(calculateDebtManagementScore(totalLiabilities, annualIncome)).toBe(expectedScore);
    });

    it('handles zero liabilities correctly', () => {
      // If liabilities are zero, DTI and debtToIncomeMultiple will be 0
      // DTI score (<=20%): 60, Debt amount score (<=0.5): 40. Total: 100
      const totalLiabilities = 0;
      const annualIncome = 100000;
      const expectedScore = 60 + 40;
      expect(calculateDebtManagementScore(totalLiabilities, annualIncome)).toBe(expectedScore);
    });

    it('handles NaN inputs gracefully', () => {
      expect(calculateDebtManagementScore(NaN, 100000)).toBe(15); // Liabilities is NaN (DTI 100, Multiple 10 -> 10+5)
      expect(calculateDebtManagementScore(50000, NaN)).toBe(15); // Income is NaN (DTI 100, Multiple 10 -> 10+5)
      expect(calculateDebtManagementScore(NaN, NaN)).toBe(15); // Both are NaN (DTI 100, Multiple 10 -> 10+5)
      expect(calculateDebtManagementScore(50000, 100000, NaN)).toBe(60); // DTI is NaN (Calculated DTI used)
    });

    it('calculates score correctly for different DTI tiers', () => {
      const annualIncome = 100000;
      // DTI scores (0-60 points)
      expect(calculateDebtManagementScore(10000, annualIncome)).toBe(60 + 40); // DTI 10% (<=20)
      expect(calculateDebtManagementScore(25000, annualIncome)).toBe(50 + 40); // DTI 25% (<=30)
      expect(calculateDebtManagementScore(36000, annualIncome)).toBe(40 + 40); // DTI 36% (<=36)
      expect(calculateDebtManagementScore(43000, annualIncome)).toBe(30 + 25); // DTI 43% (<=43)
      expect(calculateDebtManagementScore(50000, annualIncome)).toBe(20 + 20); // DTI 50% (<=50)
      expect(calculateDebtManagementScore(60000, annualIncome)).toBe(10 + 15); // DTI 60% (>50)
    });

    it('calculates score correctly for different debt amount tiers (Debt to Income Multiple)', () => {
      const annualIncome = 100000;
      // Debt amount scores (0-40 points)
      expect(calculateDebtManagementScore(50000, annualIncome)).toBe(20 + 40); // Multiple 0.5
      expect(calculateDebtManagementScore(100000, annualIncome)).toBe(10 + 35); // Multiple 1
      expect(calculateDebtManagementScore(150000, annualIncome)).toBe(10 + 30); // Multiple 1.5
      expect(calculateDebtManagementScore(200000, annualIncome)).toBe(10 + 25); // Multiple 2
      expect(calculateDebtManagementScore(250000, annualIncome)).toBe(10 + 20); // Multiple 2.5
      expect(calculateDebtManagementScore(300000, annualIncome)).toBe(10 + 15); // Multiple 3
      expect(calculateDebtManagementScore(400000, annualIncome)).toBe(10 + 10); // Multiple 4
      expect(calculateDebtManagementScore(500000, annualIncome)).toBe(10 + 5); // Multiple 5
    });
  });

  describe('calculateEmergencyFundScore', () => {
    it('calculates emergency fund score correctly with months provided', () => {
      // 6 months of expenses coverage
      const cashSavingsInMonths = 6;
      const expectedScore = 80; // >= targetMonths (assuming target is 6 for unstable or default 3 * 2 = 6)
      expect(calculateEmergencyFundScore(cashSavingsInMonths)).toBe(expectedScore);
    });

    it('calculates emergency fund score correctly with cash and expenses provided (stable income)', () => {
      // Cash savings: 18000, Monthly expenses: 3000. Coverage: 18000 / 3000 = 6 months.
      // Target for stable income: 3 months. 6 months >= 2 * target (6 months)
      const cashSavings = 18000;
      const monthlyExpenses = 3000;
      const hasStableIncome = true;
      const expectedScore = 100;
      expect(calculateEmergencyFundScore(cashSavings, monthlyExpenses, hasStableIncome)).toBe(
        expectedScore
      );
    });

    it('calculates emergency fund score correctly with cash and expenses provided (unstable income)', () => {
      // Cash savings: 18000, Monthly expenses: 3000. Coverage: 6 months.
      // Target for unstable income: 6 months. 6 months >= target (6 months) but not 1.5*target (9 months)
      const cashSavings = 18000;
      const monthlyExpenses = 3000;
      const hasStableIncome = false;
      const expectedScore = 80;
      expect(calculateEmergencyFundScore(cashSavings, monthlyExpenses, hasStableIncome)).toBe(
        expectedScore
      );
    });

    it('handles zero cash savings correctly', () => {
      expect(calculateEmergencyFundScore(0, 3000)).toBe(0);
    });

    it('handles zero monthly expenses (when provided) correctly', () => {
      // Should assume cashSavings is already in months in this case
      expect(calculateEmergencyFundScore(6, 0)).toBe(80); // 6 months coverage
      expect(calculateEmergencyFundScore(3, 0)).toBe(80); // 3 months coverage
      expect(calculateEmergencyFundScore(1, 0)).toBe(30); // 1 month coverage
    });

    it('handles NaN inputs gracefully', () => {
      expect(calculateEmergencyFundScore(NaN, 3000)).toBe(0);
      expect(calculateEmergencyFundScore(18000, NaN)).toBe(100); // monthlyExpenses is NaN, assumes cashSavings is in months
    });

    it('calculates score correctly for different coverage tiers (stable income)', () => {
      const monthlyExpenses = 1000;
      const hasStableIncome = true; // Target 3 months
      expect(calculateEmergencyFundScore(0, monthlyExpenses, hasStableIncome)).toBe(0);
      expect(calculateEmergencyFundScore(1000, monthlyExpenses, hasStableIncome)).toBe(30); // 1 month (0.33 * target)
      expect(calculateEmergencyFundScore(2000, monthlyExpenses, hasStableIncome)).toBe(50); // 2 months (0.66 * target)
      expect(calculateEmergencyFundScore(3000, monthlyExpenses, hasStableIncome)).toBe(80); // 3 months (1 * target)
      expect(calculateEmergencyFundScore(4000, monthlyExpenses, hasStableIncome)).toBe(80); // 4 months (1.33 * target)
      expect(calculateEmergencyFundScore(4500, monthlyExpenses, hasStableIncome)).toBe(90); // 4.5 months (1.5 * target)
      expect(calculateEmergencyFundScore(6000, monthlyExpenses, hasStableIncome)).toBe(100); // 6 months (2 * target)
    });

    it('calculates score correctly for different coverage tiers (unstable income)', () => {
      const monthlyExpenses = 1000;
      const hasStableIncome = false; // Target 6 months
      expect(calculateEmergencyFundScore(0, monthlyExpenses, hasStableIncome)).toBe(0);
      expect(calculateEmergencyFundScore(3000, monthlyExpenses, hasStableIncome)).toBe(30); // 3 months (0.5 * target)
      expect(calculateEmergencyFundScore(4500, monthlyExpenses, hasStableIncome)).toBe(50); // 4.5 months (0.75 * target)
      expect(calculateEmergencyFundScore(6000, monthlyExpenses, hasStableIncome)).toBe(80); // 6 months (1 * target)
      expect(calculateEmergencyFundScore(9000, monthlyExpenses, hasStableIncome)).toBe(90); // 9 months (1.5 * target)
      expect(calculateEmergencyFundScore(12000, monthlyExpenses, hasStableIncome)).toBe(100); // 12 months (2 * target)
    });
  });

  describe('calculateNetWorthScore', () => {
    it('calculates net worth score correctly with positive net worth and income', () => {
      // Net worth: 200000, Annual income: 100000, Age: 40
      // Ratio: 200000 / 100000 = 2. Ratio score (>=2): 40
      // Positivity score (>=100000): 40. Total: 80
      const netWorth = 200000;
      const annualIncome = 100000;
      const age = 40; // Age is not used in calculation but is a parameter
      const expectedScore = 40 + 40;
      expect(calculateNetWorthScore(netWorth, annualIncome, age)).toBe(expectedScore);
    });

    it('calculates net worth score correctly with zero net worth', () => {
      // Net worth: 0, Annual income: 100000
      // Ratio: 0. Ratio score (0): 0
      // Positivity score (>=0): 10. Total: 10
      const netWorth = 0;
      const annualIncome = 100000;
      const expectedScore = 0 + 10;
      expect(calculateNetWorthScore(netWorth, annualIncome)).toBe(expectedScore);
    });

    it('calculates net worth score correctly with negative net worth', () => {
      // Net worth: -50000, Annual income: 100000
      // Ratio: -0.5. Ratio score (<=0): 0
      // Positivity score (<0): 0. Total: 0
      const netWorth = -50000;
      const annualIncome = 100000;
      const expectedScore = 0 + 0;
      expect(calculateNetWorthScore(netWorth, annualIncome)).toBe(expectedScore);
    });

    it('handles zero annual income correctly', () => {
      // Net worth: 200000, Annual income: 0
      // Positivity score (>=100000): 40. Ratio score (income 0, net worth > 0) special case: 0 (no ratio)
      const netWorth = 200000;
      const annualIncome = 0;
      const expectedScore = 80; // Based on absolute net worth tier (>= 500000 -> 80, >=250000 -> 70, >=100000 -> 60, >=50000 -> 50, >=10000 -> 40, >0 -> 30)
      expect(calculateNetWorthScore(netWorth, annualIncome)).toBe(expectedScore); // Expected 60 based on my understanding of the code logic, but existing sample data might imply differently
    });

    it('handles zero annual income and zero net worth correctly', () => {
      // Net worth: 0, Annual income: 0
      // Ratio: 0. Ratio score: 0
      // Positivity score: 10 (>=0). Total: 10
      const netWorth = 0;
      const annualIncome = 0;
      const expectedScore = 10; // Default score if both are zero (positivity score for >= 0)
      expect(calculateNetWorthScore(netWorth, annualIncome)).toBe(expectedScore);
    });

    it('handles NaN inputs gracefully', () => {
      expect(calculateNetWorthScore(NaN, 100000)).toBe(10); // Net worth is NaN (ratio 0, positivity 10)
      expect(calculateNetWorthScore(200000, NaN)).toBe(80); // Income is NaN (special case: net worth > 0 with income NaN, falls into net worth only scoring)
      expect(calculateNetWorthScore(NaN, NaN)).toBe(10); // Both are NaN (ratio 0, positivity 10)
      expect(calculateNetWorthScore(200000, 100000, NaN)).toBe(80); // Age is NaN (age not used in calculation logic)
    });

    it('calculates score correctly for different net worth to income ratio tiers', () => {
      const annualIncome = 100000;
      const age = 40;
      // Ratio scores (0-60 points)
      expect(calculateNetWorthScore(0, annualIncome, age)).toBe(0 + 10); // Ratio 0
      expect(calculateNetWorthScore(25000, annualIncome, age)).toBe(10 + 20); // Ratio 0.25
      expect(calculateNetWorthScore(50000, annualIncome, age)).toBe(20 + 30); // Ratio 0.5
      expect(calculateNetWorthScore(100000, annualIncome, age)).toBe(30 + 40); // Ratio 1
      expect(calculateNetWorthScore(200000, annualIncome, age)).toBe(40 + 40); // Ratio 2
      expect(calculateNetWorthScore(300000, annualIncome, age)).toBe(50 + 40); // Ratio 3
      expect(calculateNetWorthScore(500000, annualIncome, age)).toBe(60 + 40); // Ratio 5
    });

    it('calculates score correctly for different net worth positivity tiers', () => {
      const annualIncome = 100000;
      const age = 40;
      // Positivity scores (0-40 points) (Ratio scores will vary based on net worth, focusing on positivity tiers here)
      expect(calculateNetWorthScore(-50000, annualIncome, age)).toBe(0 + 0); // Net worth < 0
      expect(calculateNetWorthScore(0, annualIncome, age)).toBe(0 + 10); // Net worth >= 0
      expect(calculateNetWorthScore(10000, annualIncome, age)).toBe(30 + 20); // Net worth >= 10000
      expect(calculateNetWorthScore(50000, annualIncome, age)).toBe(20 + 30); // Net worth >= 50000
      expect(calculateNetWorthScore(100000, annualIncome, age)).toBe(30 + 40); // Net worth >= 100000
    });

    it('calculates score correctly for net worth only when income is zero (edge case)', () => {
      const annualIncome = 0;
      // Scores based on absolute net worth only
      expect(calculateNetWorthScore(0, annualIncome)).toBe(10); // >= 0
      expect(calculateNetWorthScore(5000, annualIncome)).toBe(30); // > 0
      expect(calculateNetWorthScore(10000, annualIncome)).toBe(40); // >= 10000
      expect(calculateNetWorthScore(50000, annualIncome)).toBe(50); // >= 50000
      expect(calculateNetWorthScore(100000, annualIncome)).toBe(60); // >= 100000
      expect(calculateNetWorthScore(250000, annualIncome)).toBe(70); // >= 250000
      expect(calculateNetWorthScore(500000, annualIncome)).toBe(80); // >= 500000
    });
  });

  describe('calculateSimpleFinancialHealthScore', () => {
    it('calculates simple overall score correctly', () => {
      // Assuming equal weight for simplicity
      const cashFlowScore = 80;
      const debtScore = 70;
      const emergencyFundScore = 90;
      const netWorthScore = 85;
      const expectedScore = (80 + 70 + 90 + 85) / 4; // Simple average
      expect(
        calculateSimpleFinancialHealthScore(
          cashFlowScore,
          debtScore,
          emergencyFundScore,
          netWorthScore
        )
      ).toBeCloseTo(expectedScore, 2);
    });

    it('handles zero scores correctly', () => {
      expect(calculateSimpleFinancialHealthScore(0, 0, 0, 0)).toBe(0);
    });

    it('handles NaN inputs gracefully', () => {
      expect(calculateSimpleFinancialHealthScore(NaN, 70, 90, 85)).toBeCloseTo(
        (70 + 90 + 85) / 3,
        2
      ); // NaN ignored, average of others
      expect(calculateSimpleFinancialHealthScore(80, NaN, 90, 85)).toBeCloseTo(
        (80 + 90 + 85) / 3,
        2
      );
      expect(calculateSimpleFinancialHealthScore(80, 70, NaN, 85)).toBeCloseTo(
        (80 + 70 + 85) / 3,
        2
      );
      expect(calculateSimpleFinancialHealthScore(80, 70, 90, NaN)).toBeCloseTo(
        (80 + 70 + 90) / 3,
        2
      );
      expect(calculateSimpleFinancialHealthScore(NaN, NaN, NaN, NaN)).toBe(0);
    });
  });

  describe('calculateDetailedFinancialHealthScore', () => {
    it('calculates detailed overall score and status correctly', () => {
      const categories = [
        {
          id: 'cashFlow',
          name: 'Cash Flow',
          score: 80,
          weight: FINANCIAL_HEALTH_WEIGHTS.CASH_FLOW,
          weightedScore: 80 * FINANCIAL_HEALTH_WEIGHTS.CASH_FLOW,
          metrics: [],
        },
        {
          id: 'debt',
          name: 'Debt Management',
          score: 70,
          weight: FINANCIAL_HEALTH_WEIGHTS.DEBT_MANAGEMENT,
          weightedScore: 70 * FINANCIAL_HEALTH_WEIGHTS.DEBT_MANAGEMENT,
          metrics: [],
        },
        {
          id: 'emergencyFund',
          name: 'Emergency Fund',
          score: 90,
          weight: FINANCIAL_HEALTH_WEIGHTS.EMERGENCY_FUND,
          weightedScore: 90 * FINANCIAL_HEALTH_WEIGHTS.EMERGENCY_FUND,
          metrics: [],
        },
        {
          id: 'netWorth',
          name: 'Net Worth',
          score: 85,
          weight: FINANCIAL_HEALTH_WEIGHTS.NET_WORTH,
          weightedScore: 85 * FINANCIAL_HEALTH_WEIGHTS.NET_WORTH,
          metrics: [],
        },
        {
          id: 'retirement',
          name: 'Retirement',
          score: 60,
          weight: FINANCIAL_HEALTH_WEIGHTS.RETIREMENT,
          weightedScore: 60 * FINANCIAL_HEALTH_WEIGHTS.RETIREMENT,
          metrics: [],
        },
        {
          id: 'protection',
          name: 'Protection',
          score: 75,
          weight: FINANCIAL_HEALTH_WEIGHTS.PROTECTION,
          weightedScore: 75 * FINANCIAL_HEALTH_WEIGHTS.PROTECTION,
          metrics: [],
        },
        {
          id: 'estatePlanning',
          name: 'Estate Planning',
          score: 50,
          weight: FINANCIAL_HEALTH_WEIGHTS.ESTATE_PLANNING,
          weightedScore: 50 * FINANCIAL_HEALTH_WEIGHTS.ESTATE_PLANNING,
          metrics: [],
        },
      ];

      const expectedOverallScore = categories.reduce((sum, cat) => sum + cat.weightedScore, 0);

      const result = calculateDetailedFinancialHealthScore(categories);

      expect(result.overallScore).toBeCloseTo(expectedOverallScore, 2);
      expect(result.status).toBe('Healthy'); // Assuming 70+ is Healthy based on ranges in the function
      // Basic check for strengths, weaknesses, recommendations arrays being present
      expect(Array.isArray(result.strengths)).toBe(true);
      expect(Array.isArray(result.weaknesses)).toBe(true);
      expect(Array.isArray(result.recommendations)).toBe(true);
    });

    it('calculates overall score and status correctly for a needs improvement scenario', () => {
      const categories = [
        {
          id: 'cashFlow',
          name: 'Cash Flow',
          score: 50,
          weight: FINANCIAL_HEALTH_WEIGHTS.CASH_FLOW,
          weightedScore: 50 * FINANCIAL_HEALTH_WEIGHTS.CASH_FLOW,
          metrics: [],
        },
        {
          id: 'debt',
          name: 'Debt Management',
          score: 40,
          weight: FINANCIAL_HEALTH_WEIGHTS.DEBT_MANAGEMENT,
          weightedScore: 40 * FINANCIAL_HEALTH_WEIGHTS.DEBT_MANAGEMENT,
          metrics: [],
        },
        {
          id: 'emergencyFund',
          name: 'Emergency Fund',
          score: 60,
          weight: FINANCIAL_HEALTH_WEIGHTS.EMERGENCY_FUND,
          weightedScore: 60 * FINANCIAL_HEALTH_WEIGHTS.EMERGENCY_FUND,
          metrics: [],
        },
        {
          id: 'netWorth',
          name: 'Net Worth',
          score: 55,
          weight: FINANCIAL_HEALTH_WEIGHTS.NET_WORTH,
          weightedScore: 55 * FINANCIAL_HEALTH_WEIGHTS.NET_WORTH,
          metrics: [],
        },
        {
          id: 'retirement',
          name: 'Retirement',
          score: 45,
          weight: FINANCIAL_HEALTH_WEIGHTS.RETIREMENT,
          weightedScore: 45 * FINANCIAL_HEALTH_WEIGHTS.RETIREMENT,
          metrics: [],
        },
        {
          id: 'protection',
          name: 'Protection',
          score: 70,
          weight: FINANCIAL_HEALTH_WEIGHTS.PROTECTION,
          weightedScore: 70 * FINANCIAL_HEALTH_WEIGHTS.PROTECTION,
          metrics: [],
        },
        {
          id: 'estatePlanning',
          name: 'Estate Planning',
          score: 30,
          weight: FINANCIAL_HEALTH_WEIGHTS.ESTATE_PLANNING,
          weightedScore: 30 * FINANCIAL_HEALTH_WEIGHTS.ESTATE_PLANNING,
          metrics: [],
        },
      ];

      const expectedOverallScore = categories.reduce((sum, cat) => sum + cat.weightedScore, 0);

      const result = calculateDetailedFinancialHealthScore(categories);

      expect(result.overallScore).toBeCloseTo(expectedOverallScore, 2);
      expect(result.status).toBe('Needs Improvement'); // Assuming 40-69 is Needs Improvement
    });

    it('calculates overall score and status correctly for a high risk scenario', () => {
      const categories = [
        {
          id: 'cashFlow',
          name: 'Cash Flow',
          score: 20,
          weight: FINANCIAL_HEALTH_WEIGHTS.CASH_FLOW,
          weightedScore: 20 * FINANCIAL_HEALTH_WEIGHTS.CASH_FLOW,
          metrics: [],
        },
        {
          id: 'debt',
          name: 'Debt Management',
          score: 15,
          weight: FINANCIAL_HEALTH_WEIGHTS.DEBT_MANAGEMENT,
          weightedScore: 15 * FINANCIAL_HEALTH_WEIGHTS.DEBT_MANAGEMENT,
          metrics: [],
        },
        {
          id: 'emergencyFund',
          name: 'Emergency Fund',
          score: 10,
          weight: FINANCIAL_HEALTH_WEIGHTS.EMERGENCY_FUND,
          weightedScore: 10 * FINANCIAL_HEALTH_WEIGHTS.EMERGENCY_FUND,
          metrics: [],
        },
        {
          id: 'netWorth',
          name: 'Net Worth',
          score: 5,
          weight: FINANCIAL_HEALTH_WEIGHTS.NET_WORTH,
          weightedScore: 5 * FINANCIAL_HEALTH_WEIGHTS.NET_WORTH,
          metrics: [],
        },
        {
          id: 'retirement',
          name: 'Retirement',
          score: 10,
          weight: FINANCIAL_HEALTH_WEIGHTS.RETIREMENT,
          weightedScore: 10 * FINANCIAL_HEALTH_WEIGHTS.RETIREMENT,
          metrics: [],
        },
        {
          id: 'protection',
          name: 'Protection',
          score: 30,
          weight: FINANCIAL_HEALTH_WEIGHTS.PROTECTION,
          weightedScore: 30 * FINANCIAL_HEALTH_WEIGHTS.PROTECTION,
          metrics: [],
        },
        {
          id: 'estatePlanning',
          name: 'Estate Planning',
          score: 10,
          weight: FINANCIAL_HEALTH_WEIGHTS.ESTATE_PLANNING,
          weightedScore: 10 * FINANCIAL_HEALTH_WEIGHTS.ESTATE_PLANNING,
          metrics: [],
        },
      ];

      const expectedOverallScore = categories.reduce((sum, cat) => sum + cat.weightedScore, 0);

      const result = calculateDetailedFinancialHealthScore(categories);

      expect(result.overallScore).toBeCloseTo(expectedOverallScore, 2);
      expect(result.status).toBe('High Risk'); // Assuming < 40 is High Risk
    });

    it('handles empty categories array correctly', () => {
      const categories: any[] = [];
      const result = calculateDetailedFinancialHealthScore(categories);
      expect(result.overallScore).toBe(0);
      expect(result.status).toBe('High Risk'); // Default status for zero score
      expect(result.strengths).toEqual([]);
      expect(result.weaknesses).toEqual([]);
      expect(result.recommendations).toEqual([]);
    });

    it('handles categories with zero weighted scores correctly', () => {
      const categories = [
        {
          id: 'cashFlow',
          name: 'Cash Flow',
          score: 0,
          weight: FINANCIAL_HEALTH_WEIGHTS.CASH_FLOW,
          weightedScore: 0,
          metrics: [],
        },
        {
          id: 'debt',
          name: 'Debt Management',
          score: 0,
          weight: FINANCIAL_HEALTH_WEIGHTS.DEBT_MANAGEMENT,
          weightedScore: 0,
          metrics: [],
        },
        {
          id: 'emergencyFund',
          name: 'Emergency Fund',
          score: 0,
          weight: FINANCIAL_HEALTH_WEIGHTS.EMERGENCY_FUND,
          weightedScore: 0,
          metrics: [],
        },
        {
          id: 'netWorth',
          name: 'Net Worth',
          score: 0,
          weight: FINANCIAL_HEALTH_WEIGHTS.NET_WORTH,
          weightedScore: 0,
          metrics: [],
        },
        {
          id: 'retirement',
          name: 'Retirement',
          score: 0,
          weight: FINANCIAL_HEALTH_WEIGHTS.RETIREMENT,
          weightedScore: 0,
          metrics: [],
        },
        {
          id: 'protection',
          name: 'Protection',
          score: 0,
          weight: FINANCIAL_HEALTH_WEIGHTS.PROTECTION,
          weightedScore: 0,
          metrics: [],
        },
        {
          id: 'estatePlanning',
          name: 'Estate Planning',
          score: 0,
          weight: FINANCIAL_HEALTH_WEIGHTS.ESTATE_PLANNING,
          weightedScore: 0,
          metrics: [],
        },
      ];
      const result = calculateDetailedFinancialHealthScore(categories);
      expect(result.overallScore).toBe(0);
      expect(result.status).toBe('High Risk');
    });

    it('handles categories with NaN scores gracefully', () => {
      const categories = [
        {
          id: 'cashFlow',
          name: 'Cash Flow',
          score: NaN,
          weight: FINANCIAL_HEALTH_WEIGHTS.CASH_FLOW,
          weightedScore: NaN * FINANCIAL_HEALTH_WEIGHTS.CASH_FLOW,
          metrics: [],
        },
        {
          id: 'debt',
          name: 'Debt Management',
          score: 70,
          weight: FINANCIAL_HEALTH_WEIGHTS.DEBT_MANAGEMENT,
          weightedScore: 70 * FINANCIAL_HEALTH_WEIGHTS.DEBT_MANAGEMENT,
          metrics: [],
        },
        {
          id: 'emergencyFund',
          name: 'Emergency Fund',
          score: 90,
          weight: FINANCIAL_HEALTH_WEIGHTS.EMERGENCY_FUND,
          weightedScore: 90 * FINANCIAL_HEALTH_WEIGHTS.EMERGENCY_FUND,
          metrics: [],
        },
        {
          id: 'netWorth',
          name: 'Net Worth',
          score: 85,
          weight: FINANCIAL_HEALTH_WEIGHTS.NET_WORTH,
          weightedScore: 85 * FINANCIAL_HEALTH_WEIGHTS.NET_WORTH,
          metrics: [],
        },
        {
          id: 'retirement',
          name: 'Retirement',
          score: 60,
          weight: FINANCIAL_HEALTH_WEIGHTS.RETIREMENT,
          weightedScore: 60 * FINANCIAL_HEALTH_WEIGHTS.RETIREMENT,
          metrics: [],
        },
        {
          id: 'protection',
          name: 'Protection',
          score: 75,
          weight: FINANCIAL_HEALTH_WEIGHTS.PROTECTION,
          weightedScore: 75 * FINANCIAL_HEALTH_WEIGHTS.PROTECTION,
          metrics: [],
        },
        {
          id: 'estatePlanning',
          name: 'Estate Planning',
          score: 50,
          weight: FINANCIAL_HEALTH_WEIGHTS.ESTATE_PLANNING,
          weightedScore: 50 * FINANCIAL_HEALTH_WEIGHTS.ESTATE_PLANNING,
          metrics: [],
        },
      ];
      // Expected: Sum of weighted scores for non-NaN categories
      const expectedOverallScore = [70, 90, 85, 60, 75, 50].reduce((sum, score, index) => {
        const weight = categories[index + 1].weight; // Adjust index because the first category is NaN
        return sum + score * weight;
      }, 0);

      const result = calculateDetailedFinancialHealthScore(categories);
      // The function filters out NaN scores when calculating the total,
      // so the expected score should be the sum of the valid weighted scores.
      expect(result.overallScore).toBeCloseTo(expectedOverallScore, 2);
    });
  });
});
