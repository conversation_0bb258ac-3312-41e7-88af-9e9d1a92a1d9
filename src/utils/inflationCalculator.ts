/**
 * Inflation Calculator Utility
 *
 * This utility provides functions to adjust financial values for inflation
 * and project future expenses with inflation adjustments.
 */

/**
 * Calculate the future value of money considering inflation
 *
 * @param currentValue - Current monetary value
 * @param years - Number of years to project
 * @param inflationRate - Annual inflation rate as a decimal (default: 0.025 for 2.5%)
 * @returns Future value adjusted for inflation
 */
export const calculateFutureValueWithInflation = (
  currentValue: number,
  years: number,
  inflationRate: number = 0.025
): number => {
  if (years < 0 || inflationRate < -1) {
    throw new Error('Years must be non-negative and inflation rate must be >= -1');
  }

  return currentValue * Math.pow(1 + inflationRate, years);
};

/**
 * Calculate the present value of a future amount considering inflation
 *
 * @param futureValue - Future monetary value
 * @param years - Number of years in the future
 * @param inflationRate - Annual inflation rate as a decimal (default: 0.025)
 * @returns Present value adjusted for inflation
 */
export const calculatePresentValueWithInflation = (
  futureValue: number,
  years: number,
  inflationRate: number = 0.025
): number => {
  if (years < 0) {
    throw new Error('Years must be non-negative');
  }

  return futureValue / Math.pow(1 + inflationRate, years);
};

/**
 * Project retirement expenses with inflation adjustments
 *
 * @param currentAnnualExpenses - Current annual expenses
 * @param yearsToRetirement - Years until retirement
 * @param retirementDuration - Expected duration of retirement in years
 * @param inflationRate - Annual inflation rate as a decimal (default: 0.025)
 * @returns Object containing projected expenses for each year of retirement
 */
export const projectRetirementExpenses = (
  currentAnnualExpenses: number,
  yearsToRetirement: number,
  retirementDuration: number,
  inflationRate: number = 0.025
): {
  year: number;
  expense: number;
  cumulativeInflation: number;
}[] => {
  if (yearsToRetirement < 0 || retirementDuration <= 0) {
    throw new Error(
      'Years to retirement must be non-negative and retirement duration must be positive'
    );
  }

  const result = [];
  let currentExpense = currentAnnualExpenses;

  // Project expenses through retirement years
  for (let year = 1; year <= retirementDuration; year++) {
    const yearsFromNow = yearsToRetirement + year - 1;
    const inflatedExpense = calculateFutureValueWithInflation(
      currentAnnualExpenses,
      yearsFromNow,
      inflationRate
    );

    const cumulativeInflation = (inflatedExpense / currentAnnualExpenses - 1) * 100;

    result.push({
      year: yearsToRetirement + year,
      expense: inflatedExpense,
      cumulativeInflation: parseFloat(cumulativeInflation.toFixed(2)),
    });
  }

  return result;
};

/**
 * Calculate the total retirement corpus needed to fund retirement expenses
 *
 * @param currentAnnualExpenses - Current annual expenses
 * @param yearsToRetirement - Years until retirement
 * @param retirementDuration - Expected duration of retirement in years
 * @param inflationRate - Annual inflation rate as a decimal (default: 0.025)
 * @param investmentReturnRate - Expected annual return on investments during retirement (default: 0.05)
 * @returns Total corpus needed at retirement
 */
export const calculateRequiredRetirementCorpus = (
  currentAnnualExpenses: number,
  yearsToRetirement: number,
  retirementDuration: number,
  inflationRate: number = 0.025,
  investmentReturnRate: number = 0.05
): number => {
  if (yearsToRetirement < 0 || retirementDuration <= 0) {
    throw new Error('Invalid input parameters');
  }

  // Calculate the first year's retirement expenses in future dollars
  const firstYearExpense = calculateFutureValueWithInflation(
    currentAnnualExpenses,
    yearsToRetirement,
    inflationRate
  );

  // If inflation and return rates are equal, use simple multiplication
  if (Math.abs(inflationRate - investmentReturnRate) < 0.0001) {
    return firstYearExpense * retirementDuration;
  }

  // Calculate the real rate of return
  const realRate = (1 + investmentReturnRate) / (1 + inflationRate) - 1;

  // Calculate the present value of the growing annuity
  return (firstYearExpense * (1 - Math.pow(1 + realRate, -retirementDuration))) / realRate;
};

/**
 * Calculate the safe withdrawal amount from retirement savings
 *
 * @param retirementSavings - Total retirement savings
 * @param withdrawalRate - Annual withdrawal rate as a decimal (default: 0.04 for 4% rule)
 * @param inflationRate - Expected annual inflation rate (default: 0.025)
 * @returns Object containing initial and inflation-adjusted withdrawal amounts
 */
export const calculateSafeWithdrawal = (
  retirementSavings: number,
  withdrawalRate: number = 0.04,
  inflationRate: number = 0.025
): {
  initialWithdrawal: number;
  projectedWithdrawals: number[];
} => {
  if (withdrawalRate <= 0 || withdrawalRate > 1) {
    throw new Error('Withdrawal rate must be between 0 and 1');
  }

  const initialWithdrawal = retirementSavings * withdrawalRate;
  const projectedWithdrawals = [initialWithdrawal];

  // Project withdrawals for 30 years with inflation adjustments
  for (let year = 1; year < 30; year++) {
    const previousWithdrawal = projectedWithdrawals[year - 1];
    projectedWithdrawals.push(previousWithdrawal * (1 + inflationRate));
  }

  return {
    initialWithdrawal,
    projectedWithdrawals: projectedWithdrawals.map((amount) => parseFloat(amount.toFixed(2))),
  };
};

export default {
  calculateFutureValueWithInflation,
  calculatePresentValueWithInflation,
  projectRetirementExpenses,
  calculateRequiredRetirementCorpus,
  calculateSafeWithdrawal,
};
