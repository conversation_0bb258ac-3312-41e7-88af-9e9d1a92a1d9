/**
 * Form validation utilities for the Financial Compass application
 */

// Common validation patterns
export const patterns = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^\+?[\d\s-()]{10,}$/,
  ssn: /^\d{3}-\d{2}-\d{4}$/,
  zipCode: /^\d{5}(-\d{4})?$/,
  currency: /^\d+(\.\d{1,2})?$/,
  date: /^\d{4}-\d{2}-\d{2}$/,
  url: /^(https?:\/\/)?([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?$/,
  alphaNumeric: /^[a-zA-Z0-9 ]+$/,
};

// Types
type ValidationResult = {
  isValid: boolean;
  errors: Record<string, string>;
};

type ValidationMessage =
  | string
  | {
      [key: string]: string | ((value: any) => string);
    };

type ValidationRule = {
  required?: boolean;
  message?: ValidationMessage;
  pattern?: RegExp;
  min?: number;
  max?: number;
  minLength?: number;
  maxLength?: number;
  validate?: (value: any, formData?: any) => string | null | Promise<string | null>;
  when?: (formData: any) => boolean;
};

// Helper function to get error message
const getErrorMessage = (
  rule: ValidationRule,
  fieldName: string,
  value: any,
  formData: any = {}
): string | null => {
  const { message } = rule;

  if (!message) return null;

  if (typeof message === 'string') {
    return message;
  }

  if (typeof message === 'object') {
    for (const [key, msg] of Object.entries(message)) {
      if (
        key === 'required' &&
        rule.required &&
        (value === undefined || value === null || value === '')
      ) {
        return typeof msg === 'function' ? msg(rule) : msg;
      }
      if (
        key === 'min' &&
        typeof value === 'number' &&
        rule.min !== undefined &&
        value < rule.min
      ) {
        return typeof msg === 'function' ? msg(rule.min) : msg;
      }
      if (
        key === 'max' &&
        typeof value === 'number' &&
        rule.max !== undefined &&
        value > rule.max
      ) {
        return typeof msg === 'function' ? msg(rule.max) : msg;
      }
      if (
        key === 'minLength' &&
        typeof value === 'string' &&
        rule.minLength !== undefined &&
        value.length < rule.minLength
      ) {
        return typeof msg === 'function' ? msg(rule.minLength) : msg;
      }
      if (
        key === 'maxLength' &&
        typeof value === 'string' &&
        rule.maxLength !== undefined &&
        value.length > rule.maxLength
      ) {
        return typeof msg === 'function' ? msg(rule.maxLength) : msg;
      }
      if (
        key === 'pattern' &&
        rule.pattern &&
        typeof value === 'string' &&
        !rule.pattern.test(value)
      ) {
        return typeof msg === 'string' ? msg : 'Invalid format';
      }
    }
  }

  return null;
};

// Validate a single field
export const validateField = (
  value: any,
  rules: ValidationRule,
  fieldName: string,
  formData: any = {}
): string | null => {
  // Skip validation if when condition is false
  if (rules.when && !rules.when(formData)) {
    return null;
  }

  // Check required
  if (rules.required && (value === undefined || value === null || value === '')) {
    return getErrorMessage(rules, fieldName, value, formData) || `${fieldName} is required`;
  }

  // Skip further validation if value is empty and not required
  if (value === undefined || value === null || value === '') {
    return null;
  }

  // Check min/max for numbers
  if (typeof value === 'number') {
    if (rules.min !== undefined && value < rules.min) {
      return (
        getErrorMessage(rules, fieldName, value, formData) ||
        `${fieldName} must be at least ${rules.min}`
      );
    }
    if (rules.max !== undefined && value > rules.max) {
      return (
        getErrorMessage(rules, fieldName, value, formData) ||
        `${fieldName} must be at most ${rules.max}`
      );
    }
  }

  // Check minLength/maxLength for strings
  if (typeof value === 'string') {
    if (rules.minLength !== undefined && value.length < rules.minLength) {
      return (
        getErrorMessage(rules, fieldName, value, formData) ||
        `${fieldName} must be at least ${rules.minLength} characters`
      );
    }
    if (rules.maxLength !== undefined && value.length > rules.maxLength) {
      return (
        getErrorMessage(rules, fieldName, value, formData) ||
        `${fieldName} must be at most ${rules.maxLength} characters`
      );
    }
    if (rules.pattern && !rules.pattern.test(value)) {
      return (
        getErrorMessage(rules, fieldName, value, formData) || `Invalid format for ${fieldName}`
      );
    }
  }

  // Custom validation
  if (rules.validate) {
    const error = rules.validate(value, formData);
    if (error) {
      return typeof error === 'string' ? error : `Validation failed for ${fieldName}`;
    }
  }

  return null;
};

// Validate a form object
export const validateForm = <T extends Record<string, any>>(
  formData: T,
  validationSchema: Record<keyof T, ValidationRule>,
  parentField = ''
): ValidationResult => {
  const errors: Record<string, string> = {};
  let isValid = true;

  for (const [field, rules] of Object.entries(validationSchema)) {
    const fieldName = parentField ? `${parentField}.${field}` : field;
    const value = formData[field as keyof T];

    const error = validateField(value, rules as ValidationRule, field, formData);
    if (error) {
      errors[fieldName] = error;
      isValid = false;
    }
  }

  return { isValid, errors };
};

// Async version of validateField
export const validateFieldAsync = async (
  value: any,
  rules: ValidationRule,
  fieldName: string,
  formData: any = {}
): Promise<string | null> => {
  // Skip validation if when condition is false
  if (rules.when && !rules.when(formData)) {
    return null;
  }

  // Check required
  if (rules.required && (value === undefined || value === null || value === '')) {
    return getErrorMessage(rules, fieldName, value, formData) || `${fieldName} is required`;
  }

  // Skip further validation if value is empty and not required
  if (value === undefined || value === null || value === '') {
    return null;
  }

  // Check min/max for numbers
  if (typeof value === 'number') {
    if (rules.min !== undefined && value < rules.min) {
      return (
        getErrorMessage(rules, fieldName, value, formData) ||
        `${fieldName} must be at least ${rules.min}`
      );
    }
    if (rules.max !== undefined && value > rules.max) {
      return (
        getErrorMessage(rules, fieldName, value, formData) ||
        `${fieldName} must be at most ${rules.max}`
      );
    }
  }

  // Check minLength/maxLength for strings
  if (typeof value === 'string') {
    if (rules.minLength !== undefined && value.length < rules.minLength) {
      return (
        getErrorMessage(rules, fieldName, value, formData) ||
        `${fieldName} must be at least ${rules.minLength} characters`
      );
    }
    if (rules.maxLength !== undefined && value.length > rules.maxLength) {
      return (
        getErrorMessage(rules, fieldName, value, formData) ||
        `${fieldName} must be at most ${rules.maxLength} characters`
      );
    }
    if (rules.pattern && !rules.pattern.test(value)) {
      return (
        getErrorMessage(rules, fieldName, value, formData) || `Invalid format for ${fieldName}`
      );
    }
  }

  // Custom async validation
  if (rules.validate) {
    const error = await rules.validate(value, formData);
    if (error) {
      return typeof error === 'string' ? error : `Validation failed for ${fieldName}`;
    }
  }

  return null;
};

// Async version of validateForm
export const validateFormAsync = async <T extends Record<string, any>>(
  formData: T,
  validationSchema: Record<keyof T, ValidationRule>,
  parentField = ''
): Promise<ValidationResult> => {
  const errors: Record<string, string> = {};
  let isValid = true;

  for (const [field, rules] of Object.entries(validationSchema)) {
    const fieldName = parentField ? `${parentField}.${field}` : field;
    const value = formData[field as keyof T];

    const error = await validateFieldAsync(value, rules as ValidationRule, field, formData);
    if (error) {
      errors[fieldName] = error;
      isValid = false;
    }
  }

  return { isValid, errors };
};

// Create a validator function for a specific schema
export const createValidator =
  <T extends Record<string, any>>(schema: Record<keyof T, ValidationRule>) =>
  (formData: T) =>
    validateForm(formData, schema);

// Create an async validator function for a specific schema
export const createAsyncValidator =
  <T extends Record<string, any>>(schema: Record<keyof T, ValidationRule>) =>
  (formData: T) =>
    validateFormAsync(formData, schema);

// Common validation rules
export const validationRules = {
  required: { required: true, message: 'This field is required' },
  email: {
    required: true,
    pattern: patterns.email,
    message: 'Please enter a valid email address',
  },
  phone: {
    pattern: patterns.phone,
    message: 'Please enter a valid phone number',
  },
  ssn: {
    pattern: patterns.ssn,
    message: 'Please enter a valid SSN (XXX-XX-XXXX)',
  },
  zipCode: {
    pattern: patterns.zipCode,
    message: 'Please enter a valid ZIP code',
  },
  currency: {
    pattern: patterns.currency,
    message: 'Please enter a valid amount',
  },
  date: {
    pattern: patterns.date,
    message: 'Please enter a valid date (YYYY-MM-DD)',
  },
  url: {
    pattern: patterns.url,
    message: 'Please enter a valid URL',
  },
  alphaNumeric: {
    pattern: patterns.alphaNumeric,
    message: 'Only letters, numbers, and spaces are allowed',
  },
  password: {
    minLength: 8,
    message: 'Password must be at least 8 characters long',
  },
};
