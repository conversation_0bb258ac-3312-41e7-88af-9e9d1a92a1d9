/**
 * Retirement Calculator Utility Tests
 */

import {
  calculateFutureValue,
  calculateRequiredMonthlySavings,
  calculateRetirementIncomeNeeded,
  calculateRetirementSavingsNeeded,
  calculateRetirementReadiness,
  calculateRetirementIncomeDuration,
  calculateRetirementWithdrawal,
  calculateRetirementSavingsGap,
} from './retirementCalculator';

describe('Retirement Calculator Utility', () => {
  describe('calculateFutureValue', () => {
    it('calculates future value correctly with compound interest', () => {
      // Test case: $10,000 initial investment, $500 monthly contribution, 7% annual return, 30 years
      // Expected result: ~$1,000,000
      const result = calculateFutureValue(10000, 500, 0.07, 30);
      expect(result).toBeCloseTo(1000000, -3); // Allow a larger margin of error due to rounding
    });

    it('calculates future value correctly with zero interest rate', () => {
      // Test case: $10,000 initial investment, $500 monthly contribution, 0% annual return, 10 years
      // Expected result: $10,000 + ($500 * 12 * 10) = $70,000
      const result = calculateFutureValue(10000, 500, 0, 10);
      expect(result).toBeCloseTo(70000, 2);
    });

    it('handles zero monthly contribution correctly', () => {
      // Test case: $10,000 initial investment, $0 monthly contribution, 5% annual return, 10 years
      // Expected result: $10,000 * (1 + 0.05/12)^(10*12) ≈ $16,470
      const result = calculateFutureValue(10000, 0, 0.05, 10);
      expect(result).toBeCloseTo(16470, 0);
    });

    it('handles zero initial investment correctly', () => {
      // Test case: $0 initial investment, $500 monthly contribution, 5% annual return, 10 years
      // Expected result: $500 * ((1 + 0.05/12)^(10*12) - 1) / (0.05/12) ≈ $77,641
      const result = calculateFutureValue(0, 500, 0.05, 10);
      expect(result).toBeCloseTo(77641, 0);
    });

    it('handles negative years correctly', () => {
      expect(calculateFutureValue(10000, 500, 0.07, -5)).toBe(0);
    });

    it('handles invalid inputs gracefully', () => {
      expect(calculateFutureValue(NaN, 500, 0.07, 30)).toBe(0);
      expect(calculateFutureValue(10000, NaN, 0.07, 30)).toBe(0);
      expect(calculateFutureValue(10000, 500, NaN, 30)).toBe(0);
      expect(calculateFutureValue(10000, 500, 0.07, NaN)).toBe(0);
    });
  });

  describe('calculateRequiredMonthlySavings', () => {
    it('calculates required monthly savings correctly', () => {
      // Test case: $1,000,000 goal, $100,000 current savings, 7% annual return, 30 years
      // Expected result: ~$900
      const result = calculateRequiredMonthlySavings(1000000, 100000, 0.07, 30);
      expect(result).toBeCloseTo(900, -1); // Allow a larger margin of error due to rounding
    });

    it('calculates required monthly savings correctly with zero interest rate', () => {
      // Test case: $500,000 goal, $100,000 current savings, 0% annual return, 20 years
      // Expected result: ($500,000 - $100,000) / (20 * 12) = $1,666.67
      const result = calculateRequiredMonthlySavings(500000, 100000, 0, 20);
      expect(result).toBeCloseTo(1666.67, 2);
    });

    it('returns zero when current savings exceed the goal', () => {
      // Test case: $500,000 goal, $600,000 current savings
      const result = calculateRequiredMonthlySavings(500000, 600000, 0.07, 20);
      expect(result).toBe(0);
    });

    it('handles zero years correctly', () => {
      expect(calculateRequiredMonthlySavings(1000000, 100000, 0.07, 0)).toBe(0);
    });

    it('handles invalid inputs gracefully', () => {
      expect(calculateRequiredMonthlySavings(NaN, 100000, 0.07, 30)).toBe(0);
      expect(calculateRequiredMonthlySavings(1000000, NaN, 0.07, 30)).toBe(0);
      expect(calculateRequiredMonthlySavings(1000000, 100000, NaN, 30)).toBe(0);
      expect(calculateRequiredMonthlySavings(1000000, 100000, 0.07, NaN)).toBe(0);
    });
  });

  describe('calculateRetirementIncomeNeeded', () => {
    it('calculates retirement income needed correctly with default replacement ratio', () => {
      // Test case: $100,000 current income, default 80% replacement ratio
      // Expected result: $100,000 * 0.8 = $80,000
      const result = calculateRetirementIncomeNeeded(100000);
      expect(result).toBe(80000);
    });

    it('calculates retirement income needed correctly with custom replacement ratio', () => {
      // Test case: $100,000 current income, 70% replacement ratio
      // Expected result: $100,000 * 0.7 = $70,000
      const result = calculateRetirementIncomeNeeded(100000, 0.7);
      expect(result).toBe(70000);
    });

    it('handles zero income correctly', () => {
      expect(calculateRetirementIncomeNeeded(0)).toBe(0);
    });

    it('handles invalid inputs gracefully', () => {
      expect(calculateRetirementIncomeNeeded(NaN)).toBe(0);
      expect(calculateRetirementIncomeNeeded(100000, NaN)).toBe(0);
      expect(calculateRetirementIncomeNeeded(-100000)).toBe(0);
    });
  });

  describe('calculateRetirementSavingsNeeded', () => {
    it('calculates retirement savings needed correctly with default withdrawal rate', () => {
      // Test case: $80,000 annual income needed, default 4% withdrawal rate, no other income
      // Expected result: $80,000 / 0.04 = $2,000,000
      const result = calculateRetirementSavingsNeeded(80000);
      expect(result).toBe(2000000);
    });

    it('calculates retirement savings needed correctly with other income sources', () => {
      // Test case: $80,000 annual income needed, 4% withdrawal rate, $30,000 Social Security, $10,000 pension
      // Expected result: ($80,000 - $30,000 - $10,000) / 0.04 = $1,000,000
      const result = calculateRetirementSavingsNeeded(80000, 0.04, 30000, 10000);
      expect(result).toBe(1000000);
    });

    it('returns zero when other income exceeds needed income', () => {
      // Test case: $80,000 annual income needed, $90,000 in other income
      const result = calculateRetirementSavingsNeeded(80000, 0.04, 90000);
      expect(result).toBe(0);
    });

    it('handles zero withdrawal rate correctly', () => {
      expect(calculateRetirementSavingsNeeded(80000, 0)).toBe(0);
    });

    it('handles invalid inputs gracefully', () => {
      expect(calculateRetirementSavingsNeeded(NaN)).toBe(0);
      expect(calculateRetirementSavingsNeeded(80000, NaN)).toBe(0);
      expect(calculateRetirementSavingsNeeded(80000, 0.04, NaN)).toBe(0);
    });
  });

  describe('calculateRetirementReadiness', () => {
    it('calculates retirement readiness correctly', () => {
      // Test case: $200,000 current savings, $1,000 monthly contribution, 7% annual return,
      // 20 years until retirement, $1,500,000 savings needed
      // Expected future value: ~$1,000,000
      // Expected readiness: 1,000,000 / 1,500,000 * 100 = ~66.67%
      const result = calculateRetirementReadiness(200000, 1000, 0.07, 20, 1500000);
      expect(result).toBeCloseTo(66.67, 0);
    });

    it('caps readiness at 100%', () => {
      // Test case: Current savings already exceed needed amount
      const result = calculateRetirementReadiness(2000000, 1000, 0.07, 20, 1500000);
      expect(result).toBe(100);
    });

    it('handles zero savings needed correctly', () => {
      expect(calculateRetirementReadiness(200000, 1000, 0.07, 20, 0)).toBe(0);
    });

    it('handles invalid inputs gracefully', () => {
      expect(calculateRetirementReadiness(NaN, 1000, 0.07, 20, 1500000)).toBe(0);
      expect(calculateRetirementReadiness(200000, NaN, 0.07, 20, 1500000)).toBe(0);
      expect(calculateRetirementReadiness(200000, 1000, NaN, 20, 1500000)).toBe(0);
      expect(calculateRetirementReadiness(200000, 1000, 0.07, NaN, 1500000)).toBe(0);
      expect(calculateRetirementReadiness(200000, 1000, 0.07, 20, NaN)).toBe(0);
    });
  });

  describe('calculateRetirementIncomeDuration', () => {
    it('calculates retirement income duration correctly', () => {
      // Test case: $1,000,000 savings, $40,000 annual withdrawal, 4% annual return, 2.5% inflation
      // Expected result: ~30 years
      const result = calculateRetirementIncomeDuration(1000000, 40000);
      expect(result).toBeCloseTo(30, 0);
    });

    it('calculates retirement income duration correctly with zero real return', () => {
      // Test case: $1,000,000 savings, $40,000 annual withdrawal, 2.5% annual return, 2.5% inflation
      // Expected result: $1,000,000 / $40,000 = 25 years
      const result = calculateRetirementIncomeDuration(1000000, 40000, 0.025, 0.025);
      expect(result).toBeCloseTo(25, 0);
    });

    it('handles zero savings correctly', () => {
      expect(calculateRetirementIncomeDuration(0, 40000)).toBe(0);
    });

    it('handles zero withdrawal correctly', () => {
      expect(calculateRetirementIncomeDuration(1000000, 0)).toBe(0);
    });

    it('handles invalid inputs gracefully', () => {
      expect(calculateRetirementIncomeDuration(NaN, 40000)).toBe(0);
      expect(calculateRetirementIncomeDuration(1000000, NaN)).toBe(0);
      expect(calculateRetirementIncomeDuration(1000000, 40000, NaN)).toBe(0);
      expect(calculateRetirementIncomeDuration(1000000, 40000, 0.04, NaN)).toBe(0);
    });
  });

  describe('calculateRetirementWithdrawal', () => {
    it('calculates retirement withdrawal correctly with default rate', () => {
      // Test case: $1,000,000 savings, default 4% withdrawal rate
      // Expected result: $1,000,000 * 0.04 = $40,000
      const result = calculateRetirementWithdrawal(1000000);
      expect(result).toBe(40000);
    });

    it('calculates retirement withdrawal correctly with custom rate', () => {
      // Test case: $1,000,000 savings, 3.5% withdrawal rate
      // Expected result: $1,000,000 * 0.035 = $35,000
      const result = calculateRetirementWithdrawal(1000000, 0.035);
      expect(result).toBe(35000);
    });

    it('handles zero savings correctly', () => {
      expect(calculateRetirementWithdrawal(0)).toBe(0);
    });

    it('handles invalid inputs gracefully', () => {
      expect(calculateRetirementWithdrawal(NaN)).toBe(0);
      expect(calculateRetirementWithdrawal(1000000, NaN)).toBe(0);
      expect(calculateRetirementWithdrawal(-1000000)).toBe(0);
    });
  });

  describe('calculateRetirementSavingsGap', () => {
    it('calculates retirement savings gap correctly', () => {
      // Test case: $1,500,000 needed, $200,000 current savings, $1,000 monthly contribution,
      // 7% annual return, 20 years until retirement
      // Expected future value: ~$1,000,000
      // Expected gap: $1,500,000 - $1,000,000 = ~$500,000
      const result = calculateRetirementSavingsGap(1500000, 200000, 1000, 0.07, 20);
      expect(result).toBeCloseTo(500000, -3);
    });

    it('returns zero when projected savings exceed needed amount', () => {
      // Test case: Projected savings exceed needed amount
      const result = calculateRetirementSavingsGap(1000000, 500000, 2000, 0.07, 20);
      expect(result).toBe(0);
    });

    it('handles invalid inputs gracefully', () => {
      expect(calculateRetirementSavingsGap(NaN, 200000, 1000, 0.07, 20)).toBe(0);
      expect(calculateRetirementSavingsGap(1500000, NaN, 1000, 0.07, 20)).toBe(0);
      expect(calculateRetirementSavingsGap(1500000, 200000, NaN, 0.07, 20)).toBe(0);
      expect(calculateRetirementSavingsGap(1500000, 200000, 1000, NaN, 20)).toBe(0);
      expect(calculateRetirementSavingsGap(1500000, 200000, 1000, 0.07, NaN)).toBe(0);
    });
  });
});
