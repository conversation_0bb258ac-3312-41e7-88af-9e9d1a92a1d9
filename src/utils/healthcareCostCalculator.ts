/**
 * Healthcare Cost Calculator Utility
 *
 * This utility provides functions for projecting healthcare costs over time.
 */

import { HealthcareCostData } from '../types/southDirection';

export interface CostProjection {
  age: number;
  year: number;
  medicarePartB: number;
  medicarePartD: number;
  medicareSupplement: number;
  outOfPocket: number;
  longTermCare: number;
  total: number;
  cumulativeTotal: number;
}

/**
 * Projects healthcare costs over time based on user input.
 *
 * @param data - Healthcare cost input data.
 * @returns An array of cost projections per year.
 */
export const calculateHealthcareCostProjections = (data: HealthcareCostData): CostProjection[] => {
  const currentAge = parseInt(data.currentAge) || 45;
  const retirementAge = parseInt(data.retirementAge) || 65;
  const lifeExpectancy = parseInt(data.lifeExpectancy) || 90;
  const annualInflation = parseFloat(data.annualInflation) / 100 || 0.035;
  const currentAnnualHealthcareCost = parseFloat(data.currentAnnualHealthcareCost) || 5000;
  const medicareStartAge = parseInt(data.medicareStartAge) || 65;
  const medicarePartBPremium = parseFloat(data.medicarePartBPremium) * 12 || 170.1 * 12;
  const medicarePartDPremium = parseFloat(data.medicarePartDPremium) * 12 || 33 * 12;
  const medicareSupplementPremium = parseFloat(data.medicareSupplementPremium) * 12 || 150 * 12;
  const outOfPocketCosts = parseFloat(data.outOfPocketCosts) || 1500;
  const longTermCareStartAge = parseInt(data.longTermCareStartAge) || 80;
  const longTermCareCost = parseFloat(data.longTermCareCost) * 12 || 5000 * 12;
  const longTermCareDuration = parseInt(data.longTermCareDuration) || 3;

  const currentYear = new Date().getFullYear();
  const projections: CostProjection[] = [];
  let cumulativeTotal = 0;

  // Calculate projections for each year from current age to life expectancy
  for (let age = currentAge; age <= lifeExpectancy; age++) {
    const year = currentYear + (age - currentAge);
    const yearsSinceStart = age - currentAge;

    // Apply inflation to costs
    const inflationFactor = Math.pow(1 + annualInflation, yearsSinceStart);

    // Calculate Medicare costs (only after Medicare start age)
    const medicarePartB = age >= medicareStartAge ? medicarePartBPremium * inflationFactor : 0;
    const medicarePartD = age >= medicareStartAge ? medicarePartDPremium * inflationFactor : 0;
    const medicareSupplement =
      age >= medicareStartAge ? medicareSupplementPremium * inflationFactor : 0;

    // Calculate out-of-pocket costs
    let outOfPocket = 0;
    if (age < medicareStartAge) {
      // Before Medicare, use current healthcare cost with inflation
      outOfPocket = currentAnnualHealthcareCost * inflationFactor;
    } else {
      // After Medicare, use specified out-of-pocket costs with inflation
      outOfPocket = outOfPocketCosts * inflationFactor;
    }

    // Calculate long-term care costs (only during specified duration)
    const longTermCare =
      age >= longTermCareStartAge && age < longTermCareStartAge + longTermCareDuration
        ? longTermCareCost * inflationFactor
        : 0;

    // Calculate total annual cost
    const total = medicarePartB + medicarePartD + medicareSupplement + outOfPocket + longTermCare;

    // Update cumulative total
    cumulativeTotal += total;

    // Add projection for this year
    projections.push({
      age,
      year,
      medicarePartB,
      medicarePartD,
      medicareSupplement,
      outOfPocket,
      longTermCare,
      total,
      cumulativeTotal,
    });
  }

  return projections;
};
