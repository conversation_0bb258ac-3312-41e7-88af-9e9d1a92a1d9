/**
 * Auto Save Utility
 *
 * This utility provides functions for automatically saving form data with multiple
 * persistence mechanisms to prevent data loss.
 */

/**
 * Auto save configuration
 */
interface AutoSaveConfig {
  storageKey: string;
  backupKey?: string;
  debounceTime?: number;
  useLocalStorage?: boolean;
  useSessionStorage?: boolean;
  useIndexedDB?: boolean;
  onSave?: (data: any) => void;
  onError?: (error: Error) => void;
}

/**
 * Auto save service
 */
class AutoSaveService {
  private config: AutoSaveConfig;
  private saveTimeout: NodeJS.Timeout | null = null;
  private dbPromise: Promise<IDBDatabase> | null = null;

  /**
   * Create a new auto save service
   *
   * @param config Auto save configuration
   */
  constructor(config: AutoSaveConfig) {
    this.config = {
      debounceTime: 1000, // Default to 1 second
      useLocalStorage: true,
      useSessionStorage: true,
      useIndexedDB: true,
      ...config,
    };

    // Initialize IndexedDB if enabled
    if (this.config.useIndexedDB) {
      this.initIndexedDB();
    }
  }

  /**
   * Initialize IndexedDB
   */
  private initIndexedDB(): void {
    if (!window.indexedDB) {
      console.warn('IndexedDB is not supported in this browser');
      return;
    }

    this.dbPromise = new Promise((resolve, reject) => {
      const request = window.indexedDB.open('LifeCompassData', 1);

      request.onerror = (event) => {
        console.error('Error opening IndexedDB', event);
        reject(new Error('Failed to open IndexedDB'));
      };

      request.onsuccess = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        resolve(db);
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // Create object stores for each direction
        if (!db.objectStoreNames.contains('north')) {
          db.createObjectStore('north', { keyPath: 'id' });
        }

        if (!db.objectStoreNames.contains('east')) {
          db.createObjectStore('east', { keyPath: 'id' });
        }

        if (!db.objectStoreNames.contains('south')) {
          db.createObjectStore('south', { keyPath: 'id' });
        }

        if (!db.objectStoreNames.contains('west')) {
          db.createObjectStore('west', { keyPath: 'id' });
        }
      };
    });
  }

  /**
   * Save data with debounce
   *
   * @param data Data to save
   * @param id Optional ID for the data
   * @param direction Optional direction (north, east, south, west)
   */
  public saveWithDebounce(
    data: any,
    id?: string,
    direction?: 'north' | 'east' | 'south' | 'west'
  ): void {
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
    }

    this.saveTimeout = setTimeout(() => {
      this.save(data, id, direction);
    }, this.config.debounceTime);
  }

  /**
   * Save data immediately
   *
   * @param data Data to save
   * @param id Optional ID for the data
   * @param direction Optional direction (north, east, south, west)
   */
  public save(data: any, id?: string, direction?: 'north' | 'east' | 'south' | 'west'): void {
    try {
      // Add timestamp
      const dataWithTimestamp = {
        ...data,
        lastUpdated: new Date().toISOString(),
      };

      // Save to localStorage
      if (this.config.useLocalStorage) {
        this.saveToLocalStorage(dataWithTimestamp, id, direction);
      }

      // Save to sessionStorage
      if (this.config.useSessionStorage) {
        this.saveToSessionStorage(dataWithTimestamp, id, direction);
      }

      // Save to IndexedDB
      if (this.config.useIndexedDB) {
        this.saveToIndexedDB(dataWithTimestamp, id, direction);
      }

      // Call onSave callback
      if (this.config.onSave) {
        this.config.onSave(dataWithTimestamp);
      }
    } catch (error) {
      console.error('Error saving data', error);

      if (this.config.onError) {
        this.config.onError(error as Error);
      }
    }
  }

  /**
   * Save data to localStorage
   *
   * @param data Data to save
   * @param id Optional ID for the data
   * @param direction Optional direction (north, east, south, west)
   */
  private saveToLocalStorage(
    data: any,
    id?: string,
    direction?: 'north' | 'east' | 'south' | 'west'
  ): void {
    try {
      const storageKey = this.config.storageKey;

      // Get existing data
      const existingDataStr = localStorage.getItem(storageKey);
      let existingData = existingDataStr ? JSON.parse(existingDataStr) : {};

      // Update data
      if (direction && id) {
        // Save with direction and ID
        existingData = {
          ...existingData,
          [direction]: {
            ...existingData[direction],
            [id]: data,
          },
        };
      } else if (direction) {
        // Save with direction only
        existingData = {
          ...existingData,
          [direction]: data,
        };
      } else {
        // Save entire data object
        existingData = data;
      }

      // Save to localStorage
      localStorage.setItem(storageKey, JSON.stringify(existingData));
    } catch (error) {
      console.error('Error saving to localStorage', error);
      throw error;
    }
  }

  /**
   * Save data to sessionStorage
   *
   * @param data Data to save
   * @param id Optional ID for the data
   * @param direction Optional direction (north, east, south, west)
   */
  private saveToSessionStorage(
    data: any,
    id?: string,
    direction?: 'north' | 'east' | 'south' | 'west'
  ): void {
    try {
      const backupKey = this.config.backupKey || `${this.config.storageKey}_backup`;

      // Create backup key
      let backupStorageKey = backupKey;

      if (direction && id) {
        backupStorageKey = `${backupKey}_${direction}_${id}`;
      } else if (direction) {
        backupStorageKey = `${backupKey}_${direction}`;
      }

      // Save to sessionStorage
      sessionStorage.setItem(backupStorageKey, JSON.stringify(data));
    } catch (error) {
      console.error('Error saving to sessionStorage', error);
      // Don't throw error for backup storage
    }
  }

  /**
   * Save data to IndexedDB
   *
   * @param data Data to save
   * @param id Optional ID for the data
   * @param direction Optional direction (north, east, south, west)
   */
  private saveToIndexedDB(
    data: any,
    id?: string,
    direction?: 'north' | 'east' | 'south' | 'west'
  ): void {
    if (!this.dbPromise) {
      return;
    }

    this.dbPromise
      .then((db) => {
        try {
          // Default direction and ID if not provided
          const storeDirection = direction || 'north';
          const dataId = id || 'default';

          // Prepare data for storage
          const dataToStore = {
            id: dataId,
            data: data,
            timestamp: new Date().toISOString(),
          };

          // Start transaction
          const transaction = db.transaction([storeDirection], 'readwrite');
          const objectStore = transaction.objectStore(storeDirection);

          // Add or update data
          const request = objectStore.put(dataToStore);

          request.onerror = (event) => {
            console.error('Error saving to IndexedDB', event);
          };
        } catch (error) {
          console.error('Error in IndexedDB transaction', error);
          // Don't throw error for IndexedDB storage
        }
      })
      .catch((error) => {
        console.error('IndexedDB not available', error);
      });
  }

  /**
   * Load data from storage
   *
   * @param id Optional ID for the data
   * @param direction Optional direction (north, east, south, west)
   * @returns Promise resolving to the loaded data
   */
  public async load(id?: string, direction?: 'north' | 'east' | 'south' | 'west'): Promise<any> {
    try {
      // Try to load from localStorage first
      if (this.config.useLocalStorage) {
        const localData = this.loadFromLocalStorage(id, direction);
        if (localData) {
          return localData;
        }
      }

      // Try to load from IndexedDB next
      if (this.config.useIndexedDB) {
        try {
          const indexedDBData = await this.loadFromIndexedDB(id, direction);
          if (indexedDBData) {
            return indexedDBData;
          }
        } catch (error) {
          console.warn('Failed to load from IndexedDB, falling back to sessionStorage', error);
        }
      }

      // Finally, try sessionStorage
      if (this.config.useSessionStorage) {
        const sessionData = this.loadFromSessionStorage(id, direction);
        if (sessionData) {
          return sessionData;
        }
      }

      return null;
    } catch (error) {
      console.error('Error loading data', error);

      if (this.config.onError) {
        this.config.onError(error as Error);
      }

      return null;
    }
  }

  /**
   * Load data from localStorage
   *
   * @param id Optional ID for the data
   * @param direction Optional direction (north, east, south, west)
   * @returns Loaded data or null if not found
   */
  private loadFromLocalStorage(id?: string, direction?: 'north' | 'east' | 'south' | 'west'): any {
    try {
      const storageKey = this.config.storageKey;

      // Get existing data
      const existingDataStr = localStorage.getItem(storageKey);
      if (!existingDataStr) {
        return null;
      }

      const existingData = JSON.parse(existingDataStr);

      // Return data based on direction and ID
      if (direction && id) {
        return existingData?.[direction]?.[id] || null;
      } else if (direction) {
        return existingData?.[direction] || null;
      } else {
        return existingData;
      }
    } catch (error) {
      console.error('Error loading from localStorage', error);
      return null;
    }
  }

  /**
   * Load data from sessionStorage
   *
   * @param id Optional ID for the data
   * @param direction Optional direction (north, east, south, west)
   * @returns Loaded data or null if not found
   */
  private loadFromSessionStorage(
    id?: string,
    direction?: 'north' | 'east' | 'south' | 'west'
  ): any {
    try {
      const backupKey = this.config.backupKey || `${this.config.storageKey}_backup`;

      // Create backup key
      let backupStorageKey = backupKey;

      if (direction && id) {
        backupStorageKey = `${backupKey}_${direction}_${id}`;
      } else if (direction) {
        backupStorageKey = `${backupKey}_${direction}`;
      }

      // Get data from sessionStorage
      const dataStr = sessionStorage.getItem(backupStorageKey);
      if (!dataStr) {
        return null;
      }

      return JSON.parse(dataStr);
    } catch (error) {
      console.error('Error loading from sessionStorage', error);
      return null;
    }
  }

  /**
   * Load data from IndexedDB
   *
   * @param id Optional ID for the data
   * @param direction Optional direction (north, east, south, west)
   * @returns Promise resolving to the loaded data or null if not found
   */
  private async loadFromIndexedDB(
    id?: string,
    direction?: 'north' | 'east' | 'south' | 'west'
  ): Promise<any> {
    if (!this.dbPromise) {
      return null;
    }

    try {
      const db = await this.dbPromise;

      // Default direction and ID if not provided
      const storeDirection = direction || 'north';
      const dataId = id || 'default';

      // Start transaction
      const transaction = db.transaction([storeDirection], 'readonly');
      const objectStore = transaction.objectStore(storeDirection);

      // Get data
      return new Promise((resolve, reject) => {
        const request = objectStore.get(dataId);

        request.onsuccess = (event) => {
          const result = (event.target as IDBRequest).result;
          resolve(result ? result.data : null);
        };

        request.onerror = (event) => {
          console.error('Error loading from IndexedDB', event);
          reject(new Error('Failed to load from IndexedDB'));
        };
      });
    } catch (error) {
      console.error('Error in IndexedDB transaction', error);
      return null;
    }
  }
}

// Create default instance
const autoSave = new AutoSaveService({
  storageKey: 'lifecompass_financial_compass',
  backupKey: 'lifecompass_backup',
  debounceTime: 1000,
  useLocalStorage: true,
  useSessionStorage: true,
  useIndexedDB: true,
});

export default autoSave;
