/**
 * Income Calculator Utility Tests
 */

import {
  convertToAnnual,
  convertToMonthly,
  convertToWeekly,
  convertToBiweekly,
  calculateTotalAnnualIncome,
  calculateMonthlyFromAnnual,
  calculateSimpleAfterTaxIncome,
  PAY_PERIODS,
} from './incomeCalculator';

describe('Income Calculator Utility', () => {
  describe('convertToAnnual', () => {
    it('converts weekly income to annual correctly', () => {
      expect(convertToAnnual(1000, 'weekly')).toBeCloseTo(52142.9, 1);
    });

    it('converts biweekly income to annual correctly', () => {
      expect(convertToAnnual(2000, 'biweekly')).toBeCloseTo(52142.8, 1);
    });

    it('converts monthly income to annual correctly', () => {
      expect(convertToAnnual(5000, 'monthly')).toBe(60000);
    });

    it('keeps annual income as is', () => {
      expect(convertToAnnual(75000, 'annual')).toBe(75000);
    });

    it('handles one-time income correctly', () => {
      expect(convertToAnnual(10000, 'one-time')).toBe(10000);
    });

    it('handles invalid input gracefully', () => {
      expect(convertToAnnual(NaN, 'monthly')).toBe(0);
    });

    it('handles zero input correctly', () => {
      expect(convertToAnnual(0, 'weekly')).toBe(0);
      expect(convertToAnnual(0, 'monthly')).toBe(0);
      expect(convertToAnnual(0, 'annual')).toBe(0);
      expect(convertToAnnual(0, 'one-time')).toBe(0);
    });

    it('handles large input values', () => {
      expect(convertToAnnual(1000000, 'weekly')).toBeCloseTo(52142900, 0);
      expect(convertToAnnual(10000000, 'monthly')).toBe(120000000);
    });

    it('handles inputs that result in fractional annual values', () => {
      expect(convertToAnnual(1, 'weekly')).toBeCloseTo(52.14, 2);
    });
  });

  describe('convertToMonthly', () => {
    it('converts annual income to monthly correctly', () => {
      expect(convertToMonthly(60000, 'annual')).toBe(5000);
    });

    it('converts weekly income to monthly correctly', () => {
      expect(convertToMonthly(1000, 'weekly')).toBeCloseTo(4345.24, 2);
    });

    it('keeps monthly income as is', () => {
      expect(convertToMonthly(5000, 'monthly')).toBe(5000);
    });

    it('handles invalid input gracefully', () => {
      expect(convertToMonthly(NaN, 'annual')).toBe(0);
    });

    it('handles zero input correctly', () => {
      expect(convertToMonthly(0, 'annual')).toBe(0);
      expect(convertToMonthly(0, 'weekly')).toBe(0);
      expect(convertToMonthly(0, 'monthly')).toBe(0);
    });

    it('handles large input values', () => {
      expect(convertToMonthly(120000000, 'annual')).toBe(10000000);
      expect(convertToMonthly(43452400, 'weekly')).toBeCloseTo(188826666.7, 1); // Large number conversion
    });

    it('handles inputs that result in fractional monthly values', () => {
      expect(convertToMonthly(1, 'annual')).toBeCloseTo(0.083, 3);
    });
  });

  describe('convertToWeekly', () => {
    it('converts annual income to weekly correctly', () => {
      expect(convertToWeekly(52000, 'annual')).toBeCloseTo(997.45, 2);
    });

    it('converts monthly income to weekly correctly', () => {
      expect(convertToWeekly(4000, 'monthly')).toBeCloseTo(923.33, 2);
    });

    it('keeps weekly income as is', () => {
      expect(convertToWeekly(1000, 'weekly')).toBe(1000);
    });

    it('handles invalid input gracefully', () => {
      expect(convertToWeekly(NaN, 'annual')).toBe(0);
    });

    it('handles zero input correctly', () => {
      expect(convertToWeekly(0, 'annual')).toBe(0);
      expect(convertToWeekly(0, 'monthly')).toBe(0);
      expect(convertToWeekly(0, 'weekly')).toBe(0);
    });

    it('handles large input values', () => {
      expect(convertToWeekly(52000000, 'annual')).toBeCloseTo(997450, 0); // Large number conversion
      expect(convertToWeekly(4000000, 'monthly')).toBeCloseTo(923333, 0);
    });

    it('handles inputs that result in fractional weekly values', () => {
      expect(convertToWeekly(1, 'annual')).toBeCloseTo(0.019, 3);
    });
  });

  describe('convertToBiweekly', () => {
    it('converts annual income to biweekly correctly', () => {
      expect(convertToBiweekly(52000, 'annual')).toBeCloseTo(1994.9, 1);
    });

    it('converts monthly income to biweekly correctly', () => {
      expect(convertToBiweekly(4000, 'monthly')).toBeCloseTo(1846.66, 2);
    });

    it('keeps biweekly income as is', () => {
      expect(convertToBiweekly(2000, 'biweekly')).toBe(2000);
    });

    it('handles invalid input gracefully', () => {
      expect(convertToBiweekly(NaN, 'annual')).toBe(0);
    });

    it('handles zero input correctly', () => {
      expect(convertToBiweekly(0, 'annual')).toBe(0);
      expect(convertToBiweekly(0, 'monthly')).toBe(0);
      expect(convertToBiweekly(0, 'biweekly')).toBe(0);
    });

    it('handles large input values', () => {
      expect(convertToBiweekly(52000000, 'annual')).toBeCloseTo(1994900, 0); // Large number conversion
      expect(convertToBiweekly(4000000, 'monthly')).toBeCloseTo(1846667, 0);
    });

    it('handles inputs that result in fractional biweekly values', () => {
      expect(convertToBiweekly(1, 'annual')).toBeCloseTo(0.038, 3);
    });
  });

  describe('calculateTotalAnnualIncome', () => {
    it('calculates total annual income correctly with multiple sources', () => {
      const primaryIncome = 60000;
      const primaryIncomeFrequency = 'annual';
      const additionalSources = [
        { amount: 1000, frequency: 'monthly' },
        { amount: 500, frequency: 'biweekly' },
      ];

      // Expected: 60000 (primary) + 12000 (monthly) + 13035.7 (biweekly) = 85035.7
      expect(
        calculateTotalAnnualIncome(primaryIncome, primaryIncomeFrequency, additionalSources)
      ).toBeCloseTo(85035.7, 1);
    });

    it('handles invalid inputs gracefully', () => {
      const primaryIncome = 'invalid';
      const primaryIncomeFrequency = 'annual';
      const additionalSources = [
        { amount: 'invalid', frequency: 'monthly' },
        { amount: 500, frequency: 'biweekly' },
      ];

      // Expected: 0 (invalid primary) + 0 (invalid monthly) + 13035.7 (biweekly) = 13035.7
      expect(
        calculateTotalAnnualIncome(primaryIncome, primaryIncomeFrequency, additionalSources)
      ).toBeCloseTo(13035.7, 1);
    });

    it('calculates correctly with an empty additional sources array', () => {
      const primaryIncome = 70000;
      const primaryIncomeFrequency = 'annual';
      const additionalSources: { amount: number; frequency: PAY_PERIODS }[] = [];

      expect(
        calculateTotalAnnualIncome(primaryIncome, primaryIncomeFrequency, additionalSources)
      ).toBe(70000);
    });

    it('calculates correctly when primary income is zero', () => {
      const primaryIncome = 0;
      const primaryIncomeFrequency = 'annual';
      const additionalSources = [{ amount: 2000, frequency: 'monthly' }];

      expect(
        calculateTotalAnnualIncome(primaryIncome, primaryIncomeFrequency, additionalSources)
      ).toBe(24000);
    });

    it('calculates correctly with a mix of valid and invalid additional sources', () => {
      const primaryIncome = 50000;
      const primaryIncomeFrequency = 'annual';
      const additionalSources = [
        { amount: 1000, frequency: 'monthly' },
        { amount: NaN, frequency: 'weekly' }, // Invalid source
        { amount: 300, frequency: 'biweekly' },
      ];

      // Expected: 50000 (primary) + 12000 (monthly) + 7821.4 (biweekly) = 69821.4
      expect(
        calculateTotalAnnualIncome(primaryIncome, primaryIncomeFrequency, additionalSources)
      ).toBeCloseTo(69821.4, 1);
    });
  });

  describe('calculateMonthlyFromAnnual', () => {
    it('calculates monthly income from annual correctly', () => {
      expect(calculateMonthlyFromAnnual(60000)).toBe(5000);
    });

    it('handles zero input correctly', () => {
      expect(calculateMonthlyFromAnnual(0)).toBe(0);
    });
  });

  describe('calculateSimpleAfterTaxIncome', () => {
    it('calculates after-tax income correctly', () => {
      expect(calculateSimpleAfterTaxIncome(100000, 25)).toBe(75000);
    });

    it('handles zero tax rate correctly', () => {
      expect(calculateSimpleAfterTaxIncome(100000, 0)).toBe(100000);
    });

    it('handles invalid inputs gracefully', () => {
      expect(calculateSimpleAfterTaxIncome(NaN, 25)).toBe(0);
      expect(calculateSimpleAfterTaxIncome(100000, NaN)).toBe(0);
    });

    it('handles a 100% tax rate correctly', () => {
      expect(calculateSimpleAfterTaxIncome(100000, 100)).toBe(0);
    });

    it('handles income and tax rate as zero', () => {
      expect(calculateSimpleAfterTaxIncome(0, 0)).toBe(0);
    });

    it('handles zero income with non-zero tax rate', () => {
      expect(calculateSimpleAfterTaxIncome(0, 25)).toBe(0);
    });
  });
});
