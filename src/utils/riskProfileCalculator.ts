/**
 * Risk Profile Calculator Utility
 *
 * This utility provides functions to assess a user's risk tolerance and
 * provide appropriate investment recommendations based on their profile.
 */

// Types
export type RiskTolerance = 'conservative' | 'moderate' | 'balanced' | 'growth' | 'aggressive';
export type InvestmentHorizon = 'short' | 'medium' | 'long';
export type FinancialSituation = 'stable' | 'somewhat_stable' | 'volatile' | 'in_decline';

// Risk tolerance scoring thresholds
const RISK_SCORE_THRESHOLDS = {
  conservative: 20,
  moderate: 40,
  balanced: 60,
  growth: 80,
};

// Asset allocation models based on risk profile
const ASSET_ALLOCATION: Record<
  RiskTolerance,
  {
    stocks: number;
    bonds: number;
    cash: number;
    alternative: number;
  }
> = {
  conservative: { stocks: 20, bonds: 50, cash: 20, alternative: 10 },
  moderate: { stocks: 40, bonds: 40, cash: 10, alternative: 10 },
  balanced: { stocks: 60, bonds: 30, cash: 5, alternative: 5 },
  growth: { stocks: 80, bonds: 15, cash: 0, alternative: 5 },
  aggressive: { stocks: 90, bonds: 5, cash: 0, alternative: 5 },
};

// Recommended investment types based on risk profile
const INVESTMENT_TYPES: Record<RiskTolerance, string[]> = {
  conservative: [
    'High-quality bonds',
    'Money market funds',
    'Dividend-paying stocks',
    'Short-term CDs',
    'Treasury securities',
  ],
  moderate: [
    'Investment-grade corporate bonds',
    'Balanced mutual funds',
    'Blue-chip stocks',
    'REITs',
    'Municipal bonds',
  ],
  balanced: [
    'Large-cap stocks',
    'Corporate bonds',
    'Index funds',
    'Dividend stocks',
    'International stocks',
  ],
  growth: [
    'Growth stocks',
    'Sector-specific ETFs',
    'Small-cap stocks',
    'Emerging market funds',
    'Real estate funds',
  ],
  aggressive: [
    'Small-cap growth stocks',
    'Emerging market stocks',
    'Cryptocurrencies',
    'Venture capital',
    'Leveraged ETFs',
  ],
};

/**
 * Calculate risk tolerance score based on user responses
 *
 * @param answers - Array of answer scores (1-5)
 * @returns Risk tolerance score (0-100)
 */
export const calculateRiskScore = (answers: number[]): number => {
  if (!Array.isArray(answers) || answers.length === 0) return 0;

  const validScores = answers.filter(
    (score) => typeof score === 'number' && score >= 1 && score <= 5
  );

  if (validScores.length === 0) return 0;

  const sum = validScores.reduce((total, score) => total + score, 0);
  const average = sum / validScores.length;

  // Convert 1-5 scale to 0-100 scale
  return ((average - 1) / 4) * 100;
};

/**
 * Determine risk tolerance category based on score
 *
 * @param score - Risk tolerance score (0-100)
 * @returns Risk tolerance category
 */
export const determineRiskTolerance = (score: number): RiskTolerance => {
  if (score < RISK_SCORE_THRESHOLDS.conservative) return 'conservative';
  if (score < RISK_SCORE_THRESHOLDS.moderate) return 'moderate';
  if (score < RISK_SCORE_THRESHOLDS.balanced) return 'balanced';
  if (score < RISK_SCORE_THRESHOLDS.growth) return 'growth';
  return 'aggressive';
};

/**
 * Get recommended asset allocation based on risk tolerance
 *
 * @param riskTolerance - User's risk tolerance category
 * @returns Recommended asset allocation percentages
 */
export const getRecommendedAllocation = (riskTolerance: RiskTolerance) => {
  return ASSET_ALLOCATION[riskTolerance] || ASSET_ALLOCATION.balanced;
};

/**
 * Get recommended investment types based on risk tolerance
 *
 * @param riskTolerance - User's risk tolerance category
 * @returns Array of recommended investment types
 */
export const getRecommendedInvestments = (riskTolerance: RiskTolerance): string[] => {
  return INVESTMENT_TYPES[riskTolerance] || [];
};

/**
 * Adjust risk profile based on investment horizon and financial situation
 *
 * @param riskTolerance - Current risk tolerance
 * @param horizon - Investment horizon
 * @param situation - Current financial situation
 * @returns Adjusted risk tolerance
 */
export const adjustRiskProfile = (
  riskTolerance: RiskTolerance,
  horizon: InvestmentHorizon,
  situation: FinancialSituation
): RiskTolerance => {
  let adjustment = 0;

  // Adjust based on investment horizon
  if (horizon === 'short') adjustment -= 1;
  if (horizon === 'long') adjustment += 1;

  // Adjust based on financial situation
  if (situation === 'in_decline') adjustment -= 1;
  if (situation === 'stable') adjustment += 1;

  // Get current risk level index
  const riskLevels: RiskTolerance[] = [
    'conservative',
    'moderate',
    'balanced',
    'growth',
    'aggressive',
  ];
  let currentIndex = riskLevels.indexOf(riskTolerance);

  // Apply adjustment with bounds checking
  let newIndex = Math.max(0, Math.min(riskLevels.length - 1, currentIndex + adjustment));

  return riskLevels[newIndex];
};

/**
 * Calculate maximum recommended stock allocation based on age
 *
 * @param age - Current age
 * @param baseAllocation - Base allocation percentage (default: 110)
 * @returns Maximum recommended stock allocation percentage
 */
export const getAgeBasedAllocation = (age: number, baseAllocation: number = 110): number => {
  if (age < 20) return 90; // Cap at 90% for very young investors
  if (age > 70) return 30; // Minimum 30% for retirees
  return Math.max(30, Math.min(90, baseAllocation - age));
};

/**
 * Generate a comprehensive risk profile report
 *
 * @param riskTolerance - User's risk tolerance category
 * @param investmentHorizon - Investment horizon
 * @param financialSituation - Current financial situation
 * @returns Comprehensive risk profile report
 */
export const generateRiskProfileReport = (
  riskTolerance: RiskTolerance,
  investmentHorizon: InvestmentHorizon,
  financialSituation: FinancialSituation
) => {
  // Adjust risk tolerance if needed
  const adjustedRiskTolerance = adjustRiskProfile(
    riskTolerance,
    investmentHorizon,
    financialSituation
  );

  const allocation = getRecommendedAllocation(adjustedRiskTolerance);
  const investments = getRecommendedInvestments(adjustedRiskTolerance);

  return {
    riskTolerance: adjustedRiskTolerance,
    allocation,
    recommendedInvestments: investments,
    description: getRiskProfileDescription(adjustedRiskTolerance),
    considerations: getRiskConsiderations(
      adjustedRiskTolerance,
      investmentHorizon,
      financialSituation
    ),
  };
};

// Helper function to get risk profile description
const getRiskProfileDescription = (riskTolerance: RiskTolerance): string => {
  const descriptions = {
    conservative:
      'You prefer to preserve your capital and are willing to accept lower returns in exchange for lower risk.',
    moderate:
      'You seek a balance between risk and return, with a focus on capital preservation and steady growth.',
    balanced:
      'You are comfortable with moderate risk and seek a balance between income and growth in your portfolio.',
    growth:
      'You are willing to accept higher risk in pursuit of higher returns, with a focus on long-term growth.',
    aggressive:
      'You are comfortable with high levels of risk and seek maximum returns, understanding the potential for significant volatility.',
  };

  return descriptions[riskTolerance] || '';
};

// Helper function to get risk considerations
const getRiskConsiderations = (
  riskTolerance: RiskTolerance,
  horizon: InvestmentHorizon,
  situation: FinancialSituation
): string[] => {
  const considerations: string[] = [];

  // Risk tolerance considerations
  if (riskTolerance === 'conservative' || riskTolerance === 'moderate') {
    considerations.push(
      'Your portfolio is designed to minimize risk, which may result in lower returns over the long term.'
    );
  } else if (riskTolerance === 'growth' || riskTolerance === 'aggressive') {
    considerations.push(
      'Your portfolio includes higher-risk investments that may experience significant short-term volatility.'
    );
  }

  // Horizon considerations
  if (horizon === 'short') {
    considerations.push(
      'With a short investment horizon, your portfolio is more conservative to protect your principal.'
    );
  } else if (horizon === 'long') {
    considerations.push(
      'Your long investment horizon allows for more aggressive investments to potentially achieve higher returns.'
    );
  }

  // Situation considerations
  if (situation === 'in_decline') {
    considerations.push(
      'Given your current financial situation, we recommend a more conservative approach to preserve capital.'
    );
  } else if (situation === 'stable') {
    considerations.push(
      'Your stable financial situation allows for a balanced approach to risk and return.'
    );
  }

  return considerations;
};

export default {
  calculateRiskScore,
  determineRiskTolerance,
  getRecommendedAllocation,
  getRecommendedInvestments,
  adjustRiskProfile,
  getAgeBasedAllocation,
  generateRiskProfileReport,
};
