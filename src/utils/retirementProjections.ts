/**
 * Retirement Projections Utility
 *
 * This utility provides functions for retirement planning calculations and projections,
 * including Monte Carlo simulations for retirement success probability.
 */

/**
 * Retirement account type
 */
export type AccountType = 'traditional' | 'roth' | 'taxable';

/**
 * Retirement account interface
 */
export interface RetirementAccount {
  id: string;
  name: string;
  balance: number;
  type: AccountType;
  annualContribution: number;
  annualReturn: number;
  fees: number;
}

/**
 * Retirement income source interface
 */
export interface RetirementIncomeSource {
  id: string;
  name: string;
  monthlyAmount: number;
  startAge: number;
  endAge?: number;
  inflationAdjusted: boolean;
}

/**
 * Retirement expense interface
 */
export interface RetirementExpense {
  id: string;
  name: string;
  monthlyAmount: number;
  startAge: number;
  endAge?: number;
  isEssential: boolean;
  inflationAdjusted: boolean;
}

/**
 * Retirement projection options
 */
export interface RetirementProjectionOptions {
  currentAge: number;
  retirementAge: number;
  lifeExpectancy: number;
  accounts: RetirementAccount[];
  incomeSources: RetirementIncomeSource[];
  expenses: RetirementExpense[];
  inflationRate: number;
  taxRate: number;
  socialSecurityStartAge: number;
  socialSecurityMonthlyBenefit: number;
}

/**
 * Retirement projection result
 */
export interface RetirementProjectionResult {
  years: RetirementYearProjection[];
  finalBalance: number;
  successProbability: number;
  portfolioSurvivalAge: number;
  incomeReplacementRatio: number;
}

/**
 * Retirement year projection
 */
export interface RetirementYearProjection {
  age: number;
  year: number;
  isRetired: boolean;
  beginningBalance: number;
  contributions: number;
  withdrawals: number;
  returns: number;
  endingBalance: number;
  income: number;
  expenses: number;
  cashFlow: number;
  taxableWithdrawals: number;
  taxes: number;
  inflationFactor: number;
}

/**
 * Monte Carlo simulation result
 */
export interface MonteCarloSimulationResult {
  successProbability: number;
  medianEndingBalance: number;
  worstCaseEndingBalance: number;
  bestCaseEndingBalance: number;
  medianPortfolioSurvivalAge: number;
  simulations: RetirementProjectionResult[];
}

/**
 * Calculate retirement projection with deterministic returns
 *
 * @param options - Retirement projection options
 * @returns Retirement projection result
 */
export const calculateRetirementProjection = (
  options: RetirementProjectionOptions
): RetirementProjectionResult => {
  const {
    currentAge,
    retirementAge,
    lifeExpectancy,
    accounts,
    incomeSources,
    expenses,
    inflationRate,
    taxRate,
    socialSecurityStartAge,
    socialSecurityMonthlyBenefit,
  } = options;

  // Initialize projection years
  const years: RetirementYearProjection[] = [];
  const projectionYears = lifeExpectancy - currentAge + 1;

  // Calculate initial total balance
  let totalBalance = accounts.reduce((sum, account) => sum + account.balance, 0);

  // Calculate current annual expenses
  const currentAnnualExpenses = expenses.reduce((sum, expense) => {
    if (expense.startAge <= currentAge && (!expense.endAge || expense.endAge >= currentAge)) {
      return sum + expense.monthlyAmount * 12;
    }
    return sum;
  }, 0);

  // Calculate current annual income (excluding retirement accounts)
  const currentAnnualIncome = incomeSources.reduce((sum, source) => {
    if (source.startAge <= currentAge && (!source.endAge || source.endAge >= currentAge)) {
      return sum + source.monthlyAmount * 12;
    }
    return sum;
  }, 0);

  // Project each year
  for (let i = 0; i < projectionYears; i++) {
    const age = currentAge + i;
    const year = new Date().getFullYear() + i;
    const isRetired = age >= retirementAge;

    // Calculate inflation factor for this year
    const inflationFactor = Math.pow(1 + inflationRate, i);

    // Calculate income for this year
    let income = 0;

    // Add income from sources
    incomeSources.forEach((source) => {
      if (source.startAge <= age && (!source.endAge || source.endAge >= age)) {
        const baseAmount = source.monthlyAmount * 12;
        income += source.inflationAdjusted ? baseAmount * inflationFactor : baseAmount;
      }
    });

    // Add Social Security if eligible
    if (age >= socialSecurityStartAge) {
      income += socialSecurityMonthlyBenefit * 12 * inflationFactor;
    }

    // Calculate expenses for this year
    let annualExpenses = 0;

    expenses.forEach((expense) => {
      if (expense.startAge <= age && (!expense.endAge || expense.endAge >= age)) {
        const baseAmount = expense.monthlyAmount * 12;
        annualExpenses += expense.inflationAdjusted ? baseAmount * inflationFactor : baseAmount;
      }
    });

    // Calculate cash flow
    const cashFlow = income - annualExpenses;

    // Calculate contributions or withdrawals
    let contributions = 0;
    let withdrawals = 0;
    let taxableWithdrawals = 0;

    if (!isRetired) {
      // Working years - make contributions
      accounts.forEach((account) => {
        contributions += account.annualContribution;
      });
    } else if (cashFlow < 0) {
      // Retirement years with negative cash flow - make withdrawals
      withdrawals = Math.abs(cashFlow);

      // Determine taxable portion of withdrawals
      // Simplified: assume traditional accounts are withdrawn first
      const traditionalBalance = accounts
        .filter((account) => account.type === 'traditional')
        .reduce((sum, account) => sum + account.balance, 0);

      taxableWithdrawals = Math.min(withdrawals, traditionalBalance);
    }

    // Calculate taxes
    const taxes = taxableWithdrawals * taxRate;

    // Adjust withdrawals for taxes
    if (taxes > 0) {
      withdrawals += taxes;
    }

    // Calculate investment returns
    const beginningBalance = totalBalance;
    let returns = 0;

    accounts.forEach((account) => {
      // Simplified: assume equal distribution of balance across accounts
      const accountBalance = (account.balance / totalBalance) * beginningBalance;
      const accountReturn = accountBalance * (account.annualReturn - account.fees);
      returns += accountReturn;
    });

    // Calculate ending balance
    const endingBalance = Math.max(0, beginningBalance + contributions - withdrawals + returns);
    totalBalance = endingBalance;

    // Add year to projection
    years.push({
      age,
      year,
      isRetired,
      beginningBalance,
      contributions,
      withdrawals,
      returns,
      endingBalance,
      income,
      expenses: annualExpenses,
      cashFlow,
      taxableWithdrawals,
      taxes,
      inflationFactor,
    });

    // Stop if balance is depleted
    if (endingBalance <= 0 && isRetired) {
      break;
    }
  }

  // Calculate portfolio survival age
  const portfolioSurvivalAge = years.length > 0 ? years[years.length - 1].age : currentAge;

  // Calculate income replacement ratio
  const retirementIncome = years.find((year) => year.age === retirementAge)?.income || 0;
  const preRetirementIncome = currentAnnualIncome;
  const incomeReplacementRatio =
    preRetirementIncome > 0 ? (retirementIncome / preRetirementIncome) * 100 : 0;

  return {
    years,
    finalBalance: years.length > 0 ? years[years.length - 1].endingBalance : 0,
    successProbability: portfolioSurvivalAge >= lifeExpectancy ? 100 : 0,
    portfolioSurvivalAge,
    incomeReplacementRatio,
  };
};

/**
 * Run Monte Carlo simulation for retirement projection
 *
 * @param options - Retirement projection options
 * @param simulationCount - Number of simulations to run
 * @returns Monte Carlo simulation result
 */
export const runMonteCarloSimulation = (
  options: RetirementProjectionOptions,
  simulationCount: number = 1000
): MonteCarloSimulationResult => {
  const simulations: RetirementProjectionResult[] = [];

  // Historical stock market returns data (S&P 500 annual returns, 1928-2022)
  const historicalReturns = [
    43.81, 8.3, -25.12, -43.84, -8.64, 49.98, -1.19, 46.79, 31.94, -35.34, 29.28, -1.1, -10.67,
    -12.77, 19.17, 25.06, 19.03, 35.82, -8.43, 5.2, 5.7, 18.3, 30.81, 23.68, 18.15, -1.21, 52.56,
    32.6, 7.44, -10.46, 43.72, 12.06, 0.34, 26.64, -8.81, 22.61, 16.42, 12.4, -9.97, 23.92, -14.31,
    -25.9, 37.2, 23.83, -7.16, 6.51, 18.52, 32.15, -4.7, 21.41, 22.34, 6.15, 31.24, 18.49, 5.81,
    16.54, 31.48, -3.06, 30.23, 7.49, 9.97, 1.33, 37.2, 22.68, -9.03, -11.85, -22.27, 28.36, 10.74,
    4.83, 15.61, 5.48, -36.55, 25.94, 14.82, 2.1, 15.89, 32.15, 13.52, 1.38, 11.77, 21.61, -4.23,
    31.21, 18.02,
  ];

  // Run simulations
  for (let i = 0; i < simulationCount; i++) {
    // Create a copy of options for this simulation
    const simulationOptions = { ...options };

    // Modify account returns based on historical data
    simulationOptions.accounts = options.accounts.map((account) => {
      // For each account, use a random sequence of historical returns
      const simulatedReturns = Array(options.lifeExpectancy - options.currentAge + 1)
        .fill(0)
        .map(() => {
          const randomIndex = Math.floor(Math.random() * historicalReturns.length);
          return historicalReturns[randomIndex] / 100; // Convert to decimal
        });

      return {
        ...account,
        annualReturn: account.annualReturn, // Initial return
        simulatedReturns, // Store for use in projection
      };
    });

    // Run projection with modified options
    const projection = calculateRetirementProjection(simulationOptions);
    simulations.push(projection);
  }

  // Calculate success probability
  const successfulSimulations = simulations.filter(
    (sim) => sim.portfolioSurvivalAge >= options.lifeExpectancy
  );
  const successProbability = (successfulSimulations.length / simulationCount) * 100;

  // Calculate median ending balance
  const endingBalances = simulations.map((sim) => sim.finalBalance).sort((a, b) => a - b);
  const medianEndingBalance = endingBalances[Math.floor(endingBalances.length / 2)];

  // Calculate worst and best case scenarios
  const worstCaseEndingBalance = endingBalances[0];
  const bestCaseEndingBalance = endingBalances[endingBalances.length - 1];

  // Calculate median portfolio survival age
  const survivalAges = simulations.map((sim) => sim.portfolioSurvivalAge).sort((a, b) => a - b);
  const medianPortfolioSurvivalAge = survivalAges[Math.floor(survivalAges.length / 2)];

  return {
    successProbability,
    medianEndingBalance,
    worstCaseEndingBalance,
    bestCaseEndingBalance,
    medianPortfolioSurvivalAge,
    simulations,
  };
};
