/**
 * Utility functions for formatting values
 */

/**
 * Format a number as currency
 * @param value - The number to format
 * @param currency - The currency code (default: USD)
 * @param locale - The locale to use for formatting (default: en-US)
 * @returns Formatted currency string
 */
export const formatCurrency = (
  value: number,
  currency: string = 'USD',
  locale: string = 'en-US'
): string => {
  if (isNaN(value)) {
    return '$0.00';
  }
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);
};

/**
 * Format a number as a percentage
 * @param value - The number to format (e.g., 0.25 or "0.25" for 25%)
 * @param locale - The locale to use for formatting (default: en-US)
 * @returns Formatted percentage string
 */
export const formatPercentage = (value: number, locale: string = 'en-US'): string => {
  if (isNaN(value)) {
    return '0.0%';
  }
  // If the value is already in percentage form (e.g., 25 instead of 0.25), divide by 100
  const normalizedValue = value > 1 && value <= 100 ? value / 100 : value;
  return new Intl.NumberFormat(locale, {
    style: 'percent',
    minimumFractionDigits: 1,
    maximumFractionDigits: 1,
  }).format(normalizedValue);
};

/**
 * Format a date
 * @param date - The date to format (Date object or ISO string)
 * @param format - The format to use (default: 'short')
 * @param locale - The locale to use for formatting (default: en-US)
 * @returns Formatted date string
 */
export const formatDate = (
  date: Date | string,
  format: 'short' | 'medium' | 'long' | 'full' = 'short',
  locale: string = 'en-US'
): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  return new Intl.DateTimeFormat(locale, {
    dateStyle: format,
  }).format(dateObj);
};

/**
 * Format a number with commas
 * @param value - The number to format
 * @param locale - The locale to use for formatting (default: en-US)
 * @returns Formatted number string
 */
export const formatNumber = (value: number, locale: string = 'en-US'): string => {
  return new Intl.NumberFormat(locale).format(value);
};

/**
 * Format a phone number
 * @param phoneNumber - The phone number to format (e.g., "1234567890")
 * @returns Formatted phone number (e.g., "(*************")
 */
export const formatPhoneNumber = (phoneNumber: string): string => {
  // Remove all non-numeric characters
  const cleaned = phoneNumber.replace(/\D/g, '');

  // Check if the input is valid
  if (cleaned.length < 10) {
    return phoneNumber;
  }

  // Format the phone number
  const match = cleaned.match(/^(\d{1,3})?(\d{3})(\d{3})(\d{4})$/);

  if (match) {
    const countryCode = match[1] ? `+${match[1]} ` : '';
    return `${countryCode}(${match[2]}) ${match[3]}-${match[4]}`;
  }

  return phoneNumber;
};

/**
 * Format a social security number
 * @param ssn - The SSN to format (e.g., "123456789")
 * @returns Formatted SSN (e.g., "***********")
 */
export const formatSSN = (ssn: string): string => {
  // Remove all non-numeric characters
  const cleaned = ssn.replace(/\D/g, '');

  // Check if the input is valid
  if (cleaned.length !== 9) {
    return ssn;
  }

  // Format the SSN
  return `${cleaned.slice(0, 3)}-${cleaned.slice(3, 5)}-${cleaned.slice(5, 9)}`;
};

/**
 * Format a credit card number
 * @param cardNumber - The credit card number to format (e.g., "1234567890123456")
 * @returns Formatted credit card number (e.g., "1234 5678 9012 3456")
 */
export const formatCreditCard = (cardNumber: string): string => {
  // Remove all non-numeric characters
  const cleaned = cardNumber.replace(/\D/g, '');

  // Check if the input is valid
  if (cleaned.length < 12) {
    return cardNumber;
  }

  // Format the credit card number in groups of 4
  const groups = [];
  for (let i = 0; i < cleaned.length; i += 4) {
    groups.push(cleaned.slice(i, i + 4));
  }

  return groups.join(' ');
};

/**
 * Format a file size
 * @param bytes - The file size in bytes
 * @param decimals - The number of decimal places to show (default: 2)
 * @returns Formatted file size (e.g., "1.5 MB")
 */
export const formatFileSize = (bytes: number, decimals: number = 2): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

/**
 * Format a duration in milliseconds to a human-readable string
 * @param milliseconds - The duration in milliseconds
 * @returns Formatted duration string (e.g., "2h 30m")
 */
export const formatDuration = (milliseconds: number): string => {
  if (milliseconds < 0) return '0s';

  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) {
    return `${days}d ${hours % 24}h`;
  }

  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`;
  }

  if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  }

  return `${seconds}s`;
};

/**
 * Format a number as a compact number (e.g., 1.2K, 5.3M)
 * @param value - The number to format
 * @param locale - The locale to use for formatting (default: en-US)
 * @returns Formatted compact number string
 */
export const formatCompactNumber = (value: number, locale: string = 'en-US'): string => {
  return new Intl.NumberFormat(locale, {
    notation: 'compact',
    compactDisplay: 'short',
  }).format(value);
};

/**
 * Format a number to a specific number of decimal places
 * @param value - The number to format
 * @param decimals - The number of decimal places (default: 2)
 * @param locale - The locale to use for formatting (default: en-US)
 * @returns Formatted number string
 */
export const formatDecimal = (
  value: number,
  decimals: number = 2,
  locale: string = 'en-US'
): string => {
  return new Intl.NumberFormat(locale, {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value);
};
