/**
 * Social Security Calculator Utility
 *
 * This utility provides accurate Social Security benefit calculations using the
 * three-tier formula and proper early/delayed claiming adjustments.
 */

/**
 * Social Security bend points for 2023
 * These values are updated annually by the Social Security Administration
 */
export const BEND_POINTS_2023 = {
  FIRST: 1115,
  SECOND: 6721,
};

/**
 * Replacement rates for PIA calculation
 */
export const REPLACEMENT_RATES = {
  FIRST: 0.9, // 90% of AIME up to first bend point
  SECOND: 0.32, // 32% of AIME between first and second bend points
  THIRD: 0.15, // 15% of AIME above second bend point
};

/**
 * Full retirement age by birth year
 */
export const FULL_RETIREMENT_AGE = {
  // Birth year: [years, months]
  1937: [65, 0],
  1938: [65, 2],
  1939: [65, 4],
  1940: [65, 6],
  1941: [65, 8],
  1942: [65, 10],
  1943: [66, 0], // 1943-1954: 66 years
  1954: [66, 0],
  1955: [66, 2],
  1956: [66, 4],
  1957: [66, 6],
  1958: [66, 8],
  1959: [66, 10],
  1960: [67, 0], // 1960 and later: 67 years
};

/**
 * Calculate Primary Insurance Amount (PIA) using the three-tier formula
 *
 * @param aime - Average Indexed Monthly Earnings
 * @param bendPoints - Bend points to use for calculation (defaults to 2023 values)
 * @returns Primary Insurance Amount
 */
export const calculatePIA = (aime: number, bendPoints = BEND_POINTS_2023): number => {
  if (isNaN(aime) || aime < 0) return 0;

  // First tier: 90% of AIME up to first bend point
  const firstTier = Math.min(aime, bendPoints.FIRST) * REPLACEMENT_RATES.FIRST;

  // Second tier: 32% of AIME between first and second bend points
  const secondTier =
    Math.max(0, Math.min(aime - bendPoints.FIRST, bendPoints.SECOND - bendPoints.FIRST)) *
    REPLACEMENT_RATES.SECOND;

  // Third tier: 15% of AIME above second bend point
  const thirdTier = Math.max(0, aime - bendPoints.SECOND) * REPLACEMENT_RATES.THIRD;

  // Total PIA is the sum of the three tiers
  return Math.round(firstTier + secondTier + thirdTier);
};

/**
 * Calculate full retirement age based on birth year
 *
 * @param birthYear - Year of birth
 * @returns Full retirement age as an object with years and months
 */
export const calculateFullRetirementAge = (
  birthYear: number | string
): { years: number; months: number } => {
  const year = typeof birthYear === 'string' ? parseInt(birthYear) : birthYear;

  if (isNaN(year)) return { years: 67, months: 0 }; // Default to age 67

  // Handle years before 1937
  if (year <= 1937) return { years: 65, months: 0 };

  // Handle years after 1960
  if (year >= 1960) return { years: 67, months: 0 };

  // Look up the specific year
  for (let y = year; y >= 1937; y--) {
    if (FULL_RETIREMENT_AGE[y as keyof typeof FULL_RETIREMENT_AGE]) {
      const [years, months] = FULL_RETIREMENT_AGE[y as keyof typeof FULL_RETIREMENT_AGE];
      return { years, months };
    }
  }

  // Fallback (should not reach here)
  return { years: 67, months: 0 };
};

/**
 * Format full retirement age as a string
 *
 * @param fraObj - Full retirement age object with years and months
 * @returns Formatted string (e.g., "67" or "66 and 6 months")
 */
export const formatFullRetirementAge = (fraObj: { years: number; months: number }): string => {
  if (fraObj.months === 0) return `${fraObj.years}`;
  return `${fraObj.years} and ${fraObj.months} months`;
};

/**
 * Convert age string to months
 *
 * @param ageStr - Age string (e.g., "67" or "66 and 6 months")
 * @returns Age in months
 */
export const ageStringToMonths = (ageStr: string): number => {
  // Simple case: just a number
  if (/^\d+$/.test(ageStr)) {
    return parseInt(ageStr) * 12;
  }

  // Complex case: "X and Y months"
  const match = ageStr.match(/^(\d+)\s+and\s+(\d+)\s+months?$/);
  if (match) {
    const years = parseInt(match[1]);
    const months = parseInt(match[2]);
    return years * 12 + months;
  }

  // Default case
  return 67 * 12; // Default to age 67
};

/**
 * Calculate benefit adjustment for early or delayed claiming
 *
 * @param pia - Primary Insurance Amount
 * @param fullRetirementAge - Full retirement age in years and months (e.g., "67" or "66 and 6 months")
 * @param claimingAge - Claiming age in years (e.g., 62, 67, 70)
 * @returns Adjusted benefit amount
 */
export const calculateAdjustedBenefit = (
  pia: number,
  fullRetirementAge: string,
  claimingAge: number | string
): number => {
  if (isNaN(pia) || pia <= 0) return 0;

  // Convert claiming age to number if it's a string
  const claimingAgeNum = typeof claimingAge === 'string' ? parseInt(claimingAge) : claimingAge;

  if (isNaN(claimingAgeNum)) return pia;

  // Convert ages to months
  const fraMonths = ageStringToMonths(fullRetirementAge);
  const claimingAgeMonths = claimingAgeNum * 12;

  // Calculate difference in months
  const monthDifference = claimingAgeMonths - fraMonths;

  // Early claiming reduction (5/9% per month for first 36 months, 5/12% for additional months)
  if (monthDifference < 0) {
    const firstPeriodMonths = Math.min(36, Math.abs(monthDifference));
    const secondPeriodMonths = Math.max(0, Math.abs(monthDifference) - 36);

    const firstPeriodReduction = (firstPeriodMonths * (5 / 9)) / 100;
    const secondPeriodReduction = (secondPeriodMonths * (5 / 12)) / 100;

    return Math.round(pia * (1 - firstPeriodReduction - secondPeriodReduction));
  }

  // Delayed retirement credits (8% per year or 2/3% per month)
  if (monthDifference > 0) {
    const delayedCreditRate = (Math.min(monthDifference, 36) * (2 / 3)) / 100; // Max 3 years of delay (36 months)
    return Math.round(pia * (1 + delayedCreditRate));
  }

  // Claiming at exactly FRA
  return Math.round(pia);
};

/**
 * Calculate lifetime benefits based on life expectancy
 *
 * @param monthlyBenefit - Monthly benefit amount
 * @param claimingAge - Age at which benefits are claimed
 * @param lifeExpectancy - Expected age at death
 * @returns Total lifetime benefits
 */
export const calculateLifetimeBenefits = (
  monthlyBenefit: number,
  claimingAge: number | string,
  lifeExpectancy: number | string
): number => {
  if (isNaN(monthlyBenefit) || monthlyBenefit <= 0) return 0;

  // Convert ages to numbers if they're strings
  const claimingAgeNum = typeof claimingAge === 'string' ? parseInt(claimingAge) : claimingAge;
  const lifeExpectancyNum =
    typeof lifeExpectancy === 'string' ? parseInt(lifeExpectancy) : lifeExpectancy;

  if (isNaN(claimingAgeNum) || isNaN(lifeExpectancyNum)) return 0;

  // Calculate years receiving benefits
  const yearsReceivingBenefits = Math.max(0, lifeExpectancyNum - claimingAgeNum);

  // Calculate total lifetime benefits
  return monthlyBenefit * 12 * yearsReceivingBenefits;
};

/**
 * Calculate spousal benefit
 *
 * @param spousePIA - Spouse's Primary Insurance Amount
 * @param ownPIA - Own Primary Insurance Amount
 * @returns Spousal benefit amount
 */
export const calculateSpousalBenefit = (spousePIA: number, ownPIA: number): number => {
  if (isNaN(spousePIA) || isNaN(ownPIA) || spousePIA <= 0) return 0;

  // Spousal benefit is 50% of spouse's PIA
  const spousalBenefitAmount = spousePIA * 0.5;

  // If own benefit is higher, no spousal benefit is paid
  if (ownPIA >= spousalBenefitAmount) return 0;

  // Otherwise, spousal benefit is the difference
  return Math.round(spousalBenefitAmount - ownPIA);
};

/**
 * Calculate survivor benefit
 *
 * @param deceasedPIA - Deceased spouse's Primary Insurance Amount
 * @param ownPIA - Surviving spouse's Primary Insurance Amount
 * @returns Survivor benefit amount
 */
export const calculateSurvivorBenefit = (deceasedPIA: number, ownPIA: number): number => {
  if (isNaN(deceasedPIA) || isNaN(ownPIA) || deceasedPIA <= 0) return 0;

  // If own benefit is higher, no survivor benefit is paid
  if (ownPIA >= deceasedPIA) return 0;

  // Otherwise, survivor benefit is the difference
  return Math.round(deceasedPIA - ownPIA);
};
