/**
 * Tests for the Enhanced PDF Export Utility
 */

import { generateComprehensivePDF, downloadComprehensivePDF } from './enhancedPdfExport';

// Mock jsPDF
jest.mock('jspdf', () => {
  return jest.fn().mockImplementation(() => {
    return {
      setFontSize: jest.fn(),
      setTextColor: jest.fn(),
      text: jest.fn(),
      addImage: jest.fn(),
      line: jest.fn(),
      setLineWidth: jest.fn(),
      addPage: jest.fn(),
      save: jest.fn(),
      output: jest.fn().mockReturnValue('mock-pdf-output'),
      autoTable: jest.fn(),
      internal: {
        pageSize: { width: 210, height: 297 },
        getNumberOfPages: jest.fn().mockReturnValue(5),
      },
      setPage: jest.fn(),
    };
  });
});

// Mock sample data
const mockData = {
  north: {
    personalInfo: {
      firstName: 'John',
      lastName: 'Doe',
    },
    totalMonthlyIncome: 5000,
    totalMonthlyExpenses: 3000,
    totalAnnualIncome: 60000,
    totalAnnualExpenses: 36000,
    totalAssets: 250000,
    totalLiabilities: 150000,
  },
  east: {
    retirementGoals: {
      targetRetirementAge: 65,
      yearsUntilRetirement: 20,
      retirementDuration: 30,
      monthlyIncomeNeeded: 4000,
      totalSavingsNeeded: 1200000,
    },
    retirementIncome: {
      socialSecurity: {
        source: 'Social Security',
        monthlyAmount: 2000,
        startAge: 67,
      },
      pension: {
        source: 'Company Pension',
        monthlyAmount: 1000,
        startAge: 65,
      },
    },
  },
  south: {
    insuranceCoverage: {
      policies: [
        {
          type: 'Life',
          provider: 'State Farm',
          coverageAmount: '500000',
          premium: '50',
          frequency: 'monthly',
        },
        {
          type: 'Health',
          provider: 'Blue Cross',
          coverageAmount: '1000000',
          premium: '300',
          frequency: 'monthly',
        },
      ],
    },
    riskTolerance: {
      riskProfile: 'moderate',
      score: 3.2,
    },
  },
  west: {
    estatePlanning: {
      documents: [
        {
          type: 'Will',
          status: 'completed',
          location: 'Safe deposit box',
          lastUpdated: '2023-01-15',
        },
        {
          type: 'Power of Attorney',
          status: 'completed',
          location: 'Attorney office',
          lastUpdated: '2023-01-15',
        },
      ],
    },
    charitableGiving: {
      charities: [
        {
          name: 'Red Cross',
          category: 'Humanitarian',
          annualAmount: '1200',
          frequency: 'monthly',
        },
        {
          name: 'Local Food Bank',
          category: 'Community',
          annualAmount: '600',
          frequency: 'quarterly',
        },
      ],
    },
  },
};

describe('Enhanced PDF Export Utility', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('generateComprehensivePDF', () => {
    it('should generate a PDF with data from all four directions', () => {
      const result = generateComprehensivePDF(mockData, 'John Doe');

      // Verify the result is a string
      expect(typeof result).toBe('string');
      expect(result).toBe('mock-pdf-output');
    });

    it('should handle empty data gracefully', () => {
      const result = generateComprehensivePDF({}, 'John Doe');

      // Verify the result is a string
      expect(typeof result).toBe('string');
      expect(result).toBe('mock-pdf-output');
    });
  });

  describe('downloadComprehensivePDF', () => {
    it('should call doc.save with the correct filename', () => {
      // Mock Date to ensure consistent filename
      const mockDate = new Date('2024-06-10');
      const spy = jest.spyOn(global, 'Date').mockImplementation(() => mockDate);

      downloadComprehensivePDF(mockData, 'John Doe');

      // Verify doc.save was called with the expected filename
      const jsPDFInstance = require('jspdf')();
      expect(jsPDFInstance.save).toHaveBeenCalledWith('lifecompass_summary_2024-06-10.pdf');

      // Restore Date
      spy.mockRestore();
    });

    it('should handle errors gracefully', () => {
      // Mock console.error to prevent test output pollution
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();

      // Mock jsPDF to throw an error
      require('jspdf').mockImplementation(() => {
        throw new Error('Mock error');
      });

      // This should not throw
      expect(() => {
        downloadComprehensivePDF(mockData, 'John Doe');
      }).not.toThrow();

      // Verify error was logged
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Error downloading comprehensive PDF:',
        expect.any(Error)
      );

      // Restore console.error
      consoleErrorSpy.mockRestore();
    });
  });
});
