// Cross-form validation utility for Financial Compass

export interface CrossFormData {
  netWorth?: number;
  insuranceCoverage?: number;
  retirementSavings?: number;
  annualIncome?: number;
}

export interface CrossFormValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

export function validateCrossForm(data: CrossFormData): CrossFormValidationResult {
  const errors: Record<string, string> = {};

  // Insurance coverage should not exceed net worth
  if (
    typeof data.insuranceCoverage === 'number' &&
    typeof data.netWorth === 'number' &&
    data.insuranceCoverage > data.netWorth
  ) {
    errors.insuranceCoverage = 'Insurance coverage should not exceed your net worth.';
  }

  // Retirement savings should not exceed 10x annual income (example rule)
  if (
    typeof data.retirementSavings === 'number' &&
    typeof data.annualIncome === 'number' &&
    data.retirementSavings > data.annualIncome * 10
  ) {
    errors.retirementSavings = 'Retirement savings seem unusually high compared to your income.';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
}
