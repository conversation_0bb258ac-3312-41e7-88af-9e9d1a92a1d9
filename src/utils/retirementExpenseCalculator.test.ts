import {
  projectExpenses,
  calculateRetirementExpenses,
  calculatePresentValue,
  ExpenseCategory,
} from './retirementExpenseCalculator';

describe('Retirement Expense Calculator', () => {
  describe('projectExpenses', () => {
    it('should project expenses with inflation', () => {
      const result = projectExpenses(1000, 5, 0.03);

      expect(result).toHaveLength(5);
      expect(result[0].amount).toBeCloseTo(12000, 2);
      expect(result[1].amount).toBeCloseTo(12360, 2);
      expect(result[4].amount).toBeCloseTo(13509.27, 2);
    });

    it('should calculate present values correctly with a discount rate', () => {
      const result = projectExpenses(1000, 3, 0.03, 0.05);

      const expectedPV1 = 12000 / Math.pow(1.05, 0);
      const expectedPV2 = (1000 * 12 * Math.pow(1.03, 1)) / Math.pow(1.05, 1);
      const expectedPV3 = (1000 * 12 * Math.pow(1.03, 2)) / Math.pow(1.05, 2);

      expect(result[0].presentValue).toBeCloseTo(expectedPV1, 2);
      expect(result[1].presentValue).toBeCloseTo(expectedPV2, 2);
      expect(result[2].presentValue).toBeCloseTo(expectedPV3, 2);
    });

    it('should handle zero inflation', () => {
      const result = projectExpenses(1000, 3, 0);

      expect(result).toHaveLength(3);
      expect(result[0].amount).toBeCloseTo(12000, 2);
      expect(result[1].amount).toBeCloseTo(12000, 2);
      expect(result[2].amount).toBeCloseTo(12000, 2);
      expect(result[0].presentValue).toBeCloseTo(12000 / Math.pow(1.03, 0), 2);
      expect(result[1].presentValue).toBeCloseTo(12000 / Math.pow(1.03, 1), 2);
    });

    it('should handle zero discount rate', () => {
      const result = projectExpenses(1000, 3, 0.03, 0);

      expect(result[0].presentValue).toBeCloseTo(result[0].amount, 2);
      expect(result[1].presentValue).toBeCloseTo(result[1].amount, 2);
      expect(result[2].presentValue).toBeCloseTo(result[2].amount, 2);
    });

    it('should handle zero current monthly expense', () => {
      const result = projectExpenses(0, 5, 0.03);

      expect(result).toHaveLength(5);
      result.forEach((yearData) => {
        expect(yearData.amount).toBe(0);
        expect(yearData.presentValue).toBe(0);
      });
    });

    it('should handle zero years', () => {
      const result = projectExpenses(1000, 0, 0.03);

      expect(result).toHaveLength(0);
    });

    it('should handle negative inflation rate (deflation)', () => {
      const result = projectExpenses(1000, 3, -0.01);

      expect(result).toHaveLength(3);
      expect(result[0].amount).toBeCloseTo(12000, 2);
      expect(result[1].amount).toBeCloseTo(12000 * 0.99, 2);
      expect(result[2].amount).toBeCloseTo(12000 * Math.pow(0.99, 2), 2);
    });

    it('should handle negative discount rate', () => {
      const result = projectExpenses(1000, 3, 0.03, -0.02);

      expect(result).toHaveLength(3);
      expect(result[0].presentValue).toBeCloseTo(result[0].amount / Math.pow(0.98, 0), 2);
      expect(result[1].presentValue).toBeCloseTo(result[1].amount / Math.pow(0.98, 1), 2);
    });
  });

  describe('calculateRetirementExpenses', () => {
    const testCategories: ExpenseCategory[] = [
      {
        name: 'Housing',
        currentMonthly: 1500,
        inflationRate: 0.03,
        isEssential: true,
      },
      {
        name: 'Healthcare',
        currentMonthly: 500,
        inflationRate: 0.05,
        isEssential: true,
      },
      {
        name: 'Travel',
        currentMonthly: 300,
        inflationRate: 0.02,
        isEssential: false,
      },
    ];

    const testCategoriesZeroExpense: ExpenseCategory[] = [
      {
        name: 'Zero Expense',
        currentMonthly: 0,
        inflationRate: 0.03,
        isEssential: true,
      },
    ];

    it('should calculate expenses for multiple categories and years', () => {
      const result = calculateRetirementExpenses(testCategories, 5);

      expect(result.categoryProjections).toHaveLength(3);
      expect(result.totalProjections).toHaveLength(5);

      const year = 3;
      const housingYear3 = 1500 * 12 * Math.pow(1.03, year - 1);
      const healthcareYear3 = 500 * 12 * Math.pow(1.05, year - 1);
      const travelYear3 = 300 * 12 * Math.pow(1.02, year - 1);
      const expectedYear3Total = housingYear3 + healthcareYear3 + travelYear3;

      expect(result.totalProjections[year - 1].amount).toBeCloseTo(expectedYear3Total, 2);
    });

    it('should calculate total present value across all categories and years', () => {
      const discountRate = 0.04;
      const result = calculateRetirementExpenses(testCategories, 3, discountRate);

      let expectedTotalPV = 0;
      for (let i = 0; i < 3; i++) {
        expectedTotalPV += result.totalProjections[i].presentValue;
      }
      expect(result.totalPresentValue).toBeCloseTo(expectedTotalPV, 2);
    });

    it('should handle zero categories', () => {
      const result = calculateRetirementExpenses([], 5);
      expect(result.categoryProjections).toHaveLength(0);
      expect(result.totalProjections).toHaveLength(5);
      result.totalProjections.forEach((yearData) => {
        expect(yearData.amount).toBe(0);
        expect(yearData.presentValue).toBe(0);
      });
      expect(result.totalPresentValue).toBe(0);
    });

    it('should handle categories with zero current monthly expense', () => {
      const result = calculateRetirementExpenses(testCategoriesZeroExpense, 5);
      expect(result.categoryProjections).toHaveLength(1);
      expect(result.totalProjections).toHaveLength(5);
      result.totalProjections.forEach((yearData) => {
        expect(yearData.amount).toBe(0);
        expect(yearData.presentValue).toBe(0);
      });
      expect(result.totalPresentValue).toBe(0);
    });

    it('should handle zero years', () => {
      const result = calculateRetirementExpenses(testCategories, 0);
      expect(result.categoryProjections).toHaveLength(3);
      expect(result.totalProjections).toHaveLength(0);
      expect(result.totalPresentValue).toBe(0);
    });

    it('should handle negative discount rate', () => {
      const discountRate = -0.02;
      const result = calculateRetirementExpenses(testCategories, 3, discountRate);

      expect(result.totalProjections).toHaveLength(3);
      const totalFutureValue = result.totalProjections.reduce((sum, year) => sum + year.amount, 0);
      expect(result.totalPresentValue).toBeGreaterThan(totalFutureValue);
    });
  });

  describe('calculatePresentValue', () => {
    it('should calculate present value correctly', () => {
      const futureValue = 1000;
      const years = 5;
      const discountRate = 0.05;

      const result = calculatePresentValue(futureValue, years, discountRate);
      const expected = futureValue / Math.pow(1 + discountRate, years);

      expect(result).toBeCloseTo(expected, 2);
    });

    it('should return the same value for year 0', () => {
      const result = calculatePresentValue(1000, 0);
      expect(result).toBeCloseTo(1000, 2);
    });

    it('should handle zero discount rate', () => {
      const result = calculatePresentValue(1000, 5, 0);
      expect(result).toBeCloseTo(1000, 2);
    });

    it('should handle zero future value', () => {
      const result = calculatePresentValue(0, 5, 0.05);
      expect(result).toBe(0);
    });

    it('should handle negative future value', () => {
      const result = calculatePresentValue(-1000, 5, 0.05);
      const expected = -1000 / Math.pow(1.05, 5);
      expect(result).toBeCloseTo(expected, 2);
    });

    it('should handle negative years (although not typical)', () => {
      const result = calculatePresentValue(1000, -5, 0.05);
      const expected = 1000 / Math.pow(1.05, -5);
      expect(result).toBeCloseTo(expected, 2);
    });

    it('should handle negative discount rate', () => {
      const result = calculatePresentValue(1000, 5, -0.02);
      const expected = 1000 / Math.pow(0.98, 5);
      expect(result).toBeCloseTo(expected, 2);
    });
  });
});
