/**
 * Tax Optimization Utility
 *
 * This utility provides functions to suggest tax optimization strategies
 * based on the user's financial situation.
 */

import {
  calculateDetailedTaxLiability,
  calculateRetirementContributionSavings,
  calculateCapitalGainsTax,
} from './taxPlanning';

/**
 * Tax optimization strategy interface
 */
export // Define the shape of user financial data
interface UserFinancialData {
  income: number;
  filingStatus: 'single' | 'married' | 'headOfHousehold';
  age: number;
  retirementAccounts?: {
    traditionalIRA?: number;
    rothIRA?: number;
    employer401k?: number;
    employer401kMatch?: number;
  };
  investments?: {
    taxableBrokerage: number;
    capitalGains: number;
  };
  deductions: {
    itemized: number;
    standard: number;
  };
  dependents: number;
  hsaEligible: boolean;
  hsaContribution: number;
  year?: number;
}

// Define the optimization strategy interface
interface TaxOptimizationStrategy {
  id: string;
  title: string;
  description: string;
  estimatedSavings: number;
  implementation: string;
  impact: 'low' | 'medium' | 'high';
  eligibility: (data: UserFinancialData) => boolean;
  priority: number;
  category?: 'retirement' | 'investments' | 'deductions' | 'income' | 'estate' | 'education';
  timeHorizon?: 'immediate' | 'short-term' | 'long-term';
  complexity?: 'low' | 'medium' | 'high';
  risk?: 'low' | 'medium' | 'high';
  lastUpdated?: string;
  references?: string[];
}

/**
 * Get personalized tax optimization strategies
 *
 * @param userData - User's financial data
 * @returns Array of applicable tax optimization strategies
 */
export function getTaxOptimizationStrategies(userData: {
  income: number;
  filingStatus: 'single' | 'married' | 'headOfHousehold';
  age: number;
  retirementAccounts: {
    traditionalIRA?: number;
    rothIRA?: number;
    employer401k?: number;
    employer401kMatch?: number;
  };
  investments: {
    taxableBrokerage: number;
    capitalGains: number;
  };
  deductions: {
    itemized: number;
    standard: number;
  };
  dependents: number;
  hsaEligible: boolean;
  hsaContribution: number;
  year?: number;
}): TaxOptimizationStrategy[] {
  const year = userData.year || new Date().getFullYear();
  const strategies: TaxOptimizationStrategy[] = [];
  const {
    income,
    filingStatus,
    retirementAccounts,
    investments,
    deductions,
    hsaEligible,
    hsaContribution,
  } = userData;

  // 1. Maximize 401(k) contributions
  const max401k = userData.age >= 50 ? 30000 : 22500; // 2023 limit with catch-up
  const current401k = retirementAccounts.employer401k || 0;
  if (current401k < max401k) {
    const additionalContribution = Math.min(
      max401k - current401k,
      income * 0.25, // Don't recommend more than 25% of income
      (income - current401k) * 0.9 // Leave room for living expenses
    );

    if (additionalContribution > 1000) {
      // Only suggest if significant contribution possible
      const savings = calculateRetirementContributionSavings(
        income,
        additionalContribution,
        filingStatus
      );

      strategies.push({
        id: 'max-401k',
        title: 'Maximize 401(k) Contributions',
        description: `Contribute an additional $${additionalContribution.toLocaleString()} to your 401(k) to reduce taxable income.`,
        estimatedSavings: savings,
        implementation: 'Contact your HR department to increase your 401(k) contributions.',
        impact: 'high',
        eligibility: () => true,
        priority: 1,
      });
    }
  }

  // 2. IRA Contributions
  const maxIRA = userData.age >= 50 ? 7500 : 6500; // 2023 limit with catch-up
  const currentIRA = (retirementAccounts.traditionalIRA || 0) + (retirementAccounts.rothIRA || 0);

  // Traditional IRA phase-out ranges for 2023
  const traditionalIRAPhaseOut = {
    single: { min: 73000, max: 83000 },
    married: { min: 116000, max: 136000 },
    headOfHousehold: { min: 109000, max: 129000 },
  };

  // Roth IRA phase-out ranges for 2023
  const rothIRAPhaseOut = {
    single: { min: 138000, max: 153000 },
    married: { min: 218000, max: 228000 },
    headOfHousehold: { min: 138000, max: 153000 },
  };

  // Check eligibility for Traditional IRA deduction
  const isTraditionalIRAeligible = (income: number, status: string) => {
    if (!['single', 'married', 'headOfHousehold'].includes(status)) return false;
    const phaseOut = traditionalIRAPhaseOut[status as keyof typeof traditionalIRAPhaseOut];
    if (!phaseOut) return false;
    return income < phaseOut.max;
  };

  // Check eligibility for Roth IRA contribution
  const isRothIRAeligible = (income: number, status: string) => {
    if (!['single', 'married', 'headOfHousehold'].includes(status)) return false;
    const phaseOut = rothIRAPhaseOut[status as keyof typeof rothIRAPhaseOut];
    if (!phaseOut) return false;
    return income < phaseOut.max;
  };

  if (currentIRA < maxIRA) {
    const iraContribution = Math.min(maxIRA - currentIRA, 6000);
    const iraType = isTraditionalIRAeligible(income, filingStatus)
      ? 'Traditional'
      : isRothIRAeligible(income, filingStatus)
        ? 'Roth'
        : 'Backdoor Roth';

    const savings = calculateRetirementContributionSavings(income, iraContribution, filingStatus);

    strategies.push({
      id: 'max-ira',
      title: `Maximize ${iraType} IRA Contributions`,
      description: `Contribute $${iraContribution.toLocaleString()} to a ${iraType} IRA.`,
      estimatedSavings: iraType === 'Traditional' ? savings : savings * 0.7, // Reduced benefit for Roth
      implementation:
        iraType === 'Backdoor Roth'
          ? 'Open a Traditional IRA, contribute, then convert to Roth IRA (Backdoor Roth IRA).'
          : `Open or contribute to a ${iraType} IRA through a brokerage firm.`,
      impact: iraType === 'Traditional' ? 'high' : 'medium',
      eligibility: () => true,
      priority: 2,
    });
  }

  // 3. HSA Contributions
  const hsaLimit = userData.age >= 55 ? 4850 : 3850; // 2023 individual limit with catch-up
  if (hsaEligible && hsaContribution < hsaLimit) {
    const additionalHSA = hsaLimit - hsaContribution;
    const savings = additionalHSA * 0.22; // Estimate based on marginal rate

    strategies.push({
      id: 'max-hsa',
      title: 'Maximize HSA Contributions',
      description: `Contribute an additional $${additionalHSA.toLocaleString()} to your HSA for triple tax benefits.`,
      estimatedSavings: savings,
      implementation:
        'Contact your HSA provider to increase contributions. Consider investing HSA funds for long-term growth.',
      impact: 'high',
      eligibility: (data) => data.hsaEligible,
      priority: 3,
    });
  }

  // 4. Tax-Loss Harvesting
  const capitalGains = investments?.capitalGains || 0;
  const taxableBrokerage = investments?.taxableBrokerage || 0;

  if (capitalGains > 0 && taxableBrokerage > 10000) {
    const potentialLosses = Math.min(3000, capitalGains);
    const savings = calculateCapitalGainsTax(potentialLosses, income, filingStatus, year);

    strategies.push({
      id: 'tax-loss-harvesting',
      title: 'Tax-Loss Harvesting',
      description: `Offset capital gains by selling underperforming investments to realize up to $${potentialLosses.toLocaleString()} in losses.`,
      estimatedSavings: savings,
      implementation:
        'Review your investment portfolio and sell underperforming assets. Be mindful of wash sale rules.',
      impact: 'medium',
      eligibility: (data) => (data.investments?.capitalGains || 0) > 0,
      priority: 4,
      category: 'investments',
      timeHorizon: 'short-term',
      complexity: 'medium',
      risk: 'low',
      lastUpdated: new Date().toISOString(),
    });
  }

  // 5. Donor-Advised Fund (Bunching Charitable Contributions)
  const standardDeduction = filingStatus === 'single' ? 13850 : 27700; // 2023 amounts
  if (deductions.itemized < standardDeduction && income > 100000) {
    const bunchingAmount = Math.min(income * 0.1, 50000); // Up to 10% of income or $50k
    const itemizedTotal = deductions.itemized + bunchingAmount;

    if (itemizedTotal > standardDeduction) {
      const taxBenefit = (itemizedTotal - standardDeduction) * 0.24; // Estimate 24% bracket

      strategies.push({
        id: 'charitable-bunching',
        title: 'Bunch Charitable Contributions',
        description: `Consider bunching $${bunchingAmount.toLocaleString()} in charitable contributions into a single year to exceed the standard deduction.`,
        estimatedSavings: taxBenefit,
        implementation:
          'Open a Donor-Advised Fund to make a large contribution now and distribute to charities over time.',
        impact: 'high',
        eligibility: (data) =>
          data.income > 100000 && data.deductions.itemized < data.deductions.standard,
        priority: 5,
        category: 'deductions',
        timeHorizon: 'short-term',
        complexity: 'medium',
        risk: 'low',
        lastUpdated: new Date().toISOString(),
        references: [
          'https://www.fidelity.com/charitable-giving/charitable-giving/donor-advised-funds',
          'https://www.schwabcharitable.org/charitable-giving/charitable-bunching',
        ],
      });
    }
  }

  // 6. Mega Backdoor Roth (if available)
  const hasAfterTax401k = true; // This would come from user's 401k plan details
  const employer401k = retirementAccounts?.employer401k || 0;
  const maxAfterTaxContribution = Math.max(
    0,
    66000 - employer401k - (retirementAccounts?.employer401kMatch || 0)
  ); // 2023 total limit is $66,000

  if (hasAfterTax401k && maxAfterTaxContribution > 10000 && income > 150000) {
    const estimatedTaxSavings = maxAfterTaxContribution * 0.15; // Estimate 15% tax on growth

    strategies.push({
      id: 'mega-backdoor-roth',
      title: 'Mega Backdoor Roth',
      description: `Contribute up to $${maxAfterTaxContribution.toLocaleString()} to after-tax 401(k) and convert to Roth.`,
      estimatedSavings: estimatedTaxSavings,
      implementation:
        '1. Max out after-tax 401(k) contributions\n2. Convert to Roth 401(k) or Roth IRA\n3. Check plan rules for in-service distributions',
      impact: 'high',
      eligibility: (data) => data.income > 150000 && hasAfterTax401k,
      priority: 6,
      category: 'retirement',
      timeHorizon: 'long-term',
      complexity: 'high',
      risk: 'low',
      lastUpdated: new Date().toISOString(),
      references: [
        'https://www.madfientist.com/after-tax-contributions/',
        'https://www.nerdwallet.com/article/investing/mega-backdoor-roth-ira',
      ],
    });
  }

  // 7. Tax-Gain Harvesting (for low-income years)
  if (income < (filingStatus === 'single' ? 44625 : 89250)) {
    // 0% capital gains bracket threshold for 2023
    const remainingSpace = (filingStatus === 'single' ? 44625 : 89250) - income;
    const potentialGain = Math.min(remainingSpace, investments.taxableBrokerage * 0.5); // Up to 50% of portfolio

    if (potentialGain > 1000) {
      strategies.push({
        id: 'tax-gain-harvesting',
        title: 'Tax-Gain Harvesting',
        description: `Realize up to $${potentialGain.toLocaleString()} in capital gains at 0% tax rate.`,
        estimatedSavings: potentialGain * 0.15, // 15% is the typical long-term capital gains rate
        implementation:
          'Sell appreciated investments and immediately rebuy to step up your cost basis.',
        impact: 'medium',
        eligibility: () => true,
        priority: 7,
      });
    }
  }

  // 8. 529 College Savings Plans
  if (userData.dependents > 0 && income > 75000) {
    // Average state tax deduction for 529 contributions is about $2000 per beneficiary
    const stateTaxRate = 0.05; // Average state tax rate
    const potentialSavings = 2000 * stateTaxRate * userData.dependents;

    strategies.push({
      id: '529-contributions',
      title: '529 College Savings Plan',
      description: `Save for education expenses with tax-free growth and potential state tax deductions.`,
      estimatedSavings: potentialSavings,
      implementation:
        "Open a 529 plan for each child and contribute up to your state's deduction limit.",
      impact: 'medium',
      eligibility: (data) => data.dependents > 0 && data.income > 75000,
      priority: 8,
      category: 'education',
      timeHorizon: 'long-term',
    });
  }

  // 9. Health Insurance Premiums (Self-Employed)
  const isSelfEmployed = false; // This would come from user profile
  if (isSelfEmployed && hsaEligible) {
    const premiumDeduction = 5000; // Average annual premium
    const savings = premiumDeduction * 0.25; // Estimate based on marginal rate

    strategies.push({
      id: 'self-employed-health-insurance',
      title: 'Self-Employed Health Insurance Deduction',
      description: 'Deduct 100% of health insurance premiums from your self-employment income.',
      estimatedSavings: savings,
      implementation: 'Report health insurance premiums on Schedule 1 (Form 1040).',
      impact: 'high',
      eligibility: (data) => isSelfEmployed && data.hsaEligible,
      priority: 9,
      category: 'deductions',
      timeHorizon: 'immediate',
    });
  }

  // 10. Roth Conversion Ladder (for early retirees)
  const yearsToRetirement = 65 - userData.age; // Simplified calculation
  const traditionalIRABalance = retirementAccounts?.traditionalIRA || 0;

  if (yearsToRetirement > 10 && traditionalIRABalance > 100000) {
    const conversionAmount = Math.min(50000, traditionalIRABalance * 0.1);
    const taxSavings = conversionAmount * 0.12; // Estimate 12% tax rate in retirement

    strategies.push({
      id: 'roth-conversion-ladder',
      title: 'Roth Conversion Ladder',
      description: `Convert $${conversionAmount.toLocaleString()} from Traditional to Roth IRA to reduce future RMDs.`,
      estimatedSavings: taxSavings,
      implementation:
        'Convert a portion of your Traditional IRA to Roth IRA annually to fill lower tax brackets.',
      impact: 'high',
      eligibility: (data) => {
        const userYearsToRetirement = 65 - (data.age || 0);
        const userTraditionalIRA = data.retirementAccounts?.traditionalIRA || 0;
        return userYearsToRetirement > 10 && userTraditionalIRA > 100000;
      },
      priority: 10,
      category: 'retirement',
      timeHorizon: 'long-term',
      complexity: 'high',
      risk: 'medium',
      lastUpdated: new Date().toISOString(),
      references: [
        'https://www.irs.gov/retirement-plans/retirement-plans-faqs-regarding-iras',
        'https://www.investopedia.com/roth-ira-conversion-ladder-4770607',
      ],
    });
  }

  // 10. Charitable Contributions
  if (userData.deductions.itemized === 0 && userData.deductions.standard < userData.income * 0.1) {
    const potentialDonation = Math.min(userData.income * 0.03, 5000);
    const savings = potentialDonation * 0.24; // Estimate based on marginal rate

    strategies.push({
      id: 'charitable-donations',
      title: 'Charitable Donations',
      description: `Consider donating up to $${potentialDonation.toLocaleString()} to qualified charities.`,
      estimatedSavings: savings,
      implementation:
        'Donate to qualified 501(c)(3) organizations and keep receipts for tax filing.',
      impact: 'medium',
      eligibility: (data) => data.income > 50000,
      priority: 10,
      category: 'deductions',
      timeHorizon: 'immediate',
      complexity: 'low',
      risk: 'low',
      lastUpdated: new Date().toISOString(),
    });
  }

  // 11. Charitable Donations with Appreciated Securities
  const hasAppreciatedSecurities = (investments?.taxableBrokerage || 0) > 50000;
  const potentialDonation = Math.min(income * 0.05, 10000);

  if (hasAppreciatedSecurities && potentialDonation > 1000) {
    const taxRateOnGains = 0.2; // Long-term capital gains rate + NIIT
    const costBasis = potentialDonation * 0.6; // Assuming 40% appreciation
    const taxSavings = (potentialDonation - costBasis) * taxRateOnGains;

    strategies.push({
      id: 'charitable-securities',
      title: 'Donate Appreciated Securities',
      description: `Donate $${potentialDonation.toLocaleString()} in appreciated securities instead of cash to avoid capital gains tax.`,
      estimatedSavings: taxSavings,
      implementation:
        '1. Identify highly appreciated securities in your portfolio\n2. Donate them directly to a qualified charity\n3. Receive a tax deduction for the full market value',
      impact: 'high',
      eligibility: (data) =>
        (data.investments?.taxableBrokerage || 0) > 50000 && data.income > 50000,
      priority: 11,
      category: 'investments',
      timeHorizon: 'immediate',
      complexity: 'medium',
      risk: 'low',
      lastUpdated: new Date().toISOString(),
      references: [
        'https://www.fidelity.com/learning-center/smart-money/donating-appreciated-securities',
        'https://www.schwabcharitable.org/charitable-giving/give-appreciated-securities',
      ],
    });
  }

  // 12. Tax-Loss Harvesting for Fixed Income
  const fixedIncomeHoldings = investments?.taxableBrokerage
    ? investments.taxableBrokerage * 0.3
    : 0; // Assuming 30% in bonds
  if (fixedIncomeHoldings > 25000 && income > 100000) {
    const potentialTaxLoss = Math.min(3000, fixedIncomeHoldings * 0.1);
    const taxSavings = potentialTaxLoss * 0.32; // 32% bracket + state

    strategies.push({
      id: 'bond-tax-loss-harvesting',
      title: 'Tax-Loss Harvesting for Bonds',
      description: `Realize up to $${potentialTaxLoss.toLocaleString()} in bond losses to offset capital gains.`,
      estimatedSavings: taxSavings,
      implementation:
        'Sell bond funds/ETFs at a loss and replace with similar but not identical fixed income investments.',
      impact: 'medium',
      eligibility: (data) =>
        (data.investments?.taxableBrokerage || 0) > 25000 && data.income > 100000,
      priority: 12,
      category: 'investments',
      timeHorizon: 'short-term',
      complexity: 'medium',
      risk: 'low',
      lastUpdated: new Date().toISOString(),
    });
  }

  // 13. Tax-Exempt Municipal Bonds (State-Specific)
  const stateTaxRate = 0.05; // Average state tax rate
  const federalTaxRate = 0.32; // 32% federal bracket
  const combinedTaxRate = 1 - (1 - stateTaxRate) * (1 - federalTaxRate);
  const taxEquivalentYield = 0.03 / (1 - combinedTaxRate); // 3% tax-free yield

  if (income > 100000 && (investments?.taxableBrokerage || 0) > 50000) {
    const potentialInvestment = Math.min(20000, (investments?.taxableBrokerage || 0) * 0.3);
    const taxableEquivalentYield = taxEquivalentYield * potentialInvestment;
    const taxableYieldNeeded = taxableEquivalentYield * (1 - combinedTaxRate);
    const savings = taxableEquivalentYield - taxableYieldNeeded;

    strategies.push({
      id: 'municipal-bonds-advanced',
      title: 'State-Specific Municipal Bonds',
      description: `Consider investing $${potentialInvestment.toLocaleString()} in tax-exempt municipal bonds from your state.`,
      estimatedSavings: savings,
      implementation:
        "1. Research your state's general obligation bonds\n2. Consider a state-specific municipal bond fund\n3. Compare after-tax yields with taxable bonds",
      impact: 'high',
      eligibility: (data) =>
        data.income > 100000 && (data.investments?.taxableBrokerage || 0) > 50000,
      priority: 13,
      category: 'investments',
      timeHorizon: 'long-term',
      complexity: 'high',
      risk: 'low',
      lastUpdated: new Date().toISOString(),
      references: [
        'https://www.investopedia.com/terms/m/municipalbond.asp',
        'https://www.nerdwallet.com/article/investing/municipal-bonds',
      ],
    });
  }

  // 14. Roth Conversion Strategy with Tax Bracket Optimization
  const userYearsUntilRetirement = 65 - userData.age;
  const userTraditionalIRABalance = retirementAccounts?.traditionalIRA || 0;
  const userRothIRABalance = retirementAccounts?.rothIRA || 0;

  if (
    userYearsUntilRetirement > 5 &&
    userTraditionalIRABalance > 100000 &&
    userRothIRABalance < userTraditionalIRABalance * 0.3
  ) {
    const conversionAmount = Math.min(50000, userTraditionalIRABalance * 0.1);
    const currentTaxRate = 0.22; // Current tax rate (22% bracket)
    const projectedTaxRate = 0.24; // Projected future tax rate (24% bracket)
    const taxSavings = conversionAmount * projectedTaxRate - conversionAmount * currentTaxRate;

    strategies.push({
      id: 'roth-conversion-strategy',
      title: 'Roth Conversion Strategy',
      description: `Convert $${conversionAmount.toLocaleString()} from Traditional to Roth IRA to reduce future RMDs and lock in current tax rates.`,
      estimatedSavings: Math.abs(taxSavings),
      implementation:
        '1. Analyze your current and projected future tax brackets\n2. Convert amount that keeps you within current tax bracket\n3. Pay taxes now at current rates\n4. Enjoy tax-free growth and withdrawals in retirement\n5. Consider partial conversions over several years',
      impact: 'high',
      eligibility: (data) => {
        const yearsToRetirement = 65 - (data.age || 0);
        const tradIRA = data.retirementAccounts?.traditionalIRA || 0;
        const rothIRA = data.retirementAccounts?.rothIRA || 0;
        return yearsToRetirement > 5 && tradIRA > 100000 && rothIRA < tradIRA * 0.3;
      },
      priority: 14,
      category: 'retirement',
      timeHorizon: 'long-term',
      complexity: 'high',
      risk: 'medium',
      lastUpdated: new Date().toISOString(),
      references: [
        'https://www.investopedia.com/roth-ira-conversion-ladder-4770607',
        'https://www.madfientist.com/traditional-ira-vs-roth-ira/',
      ],
    });
  }

  // 15. Qualified Charitable Distributions (QCDs) for those over 70.5
  const userAge = userData.age || 0;
  const userTradIRABalance = retirementAccounts?.traditionalIRA || 0;

  if (userAge >= 70.5 && userTradIRABalance > 100000) {
    const qcdAmount = Math.min(100000, userTradIRABalance * 0.1);
    const rmds = userTradIRABalance * 0.04; // Estimated RMD percentage
    const taxSavings = Math.min(qcdAmount, rmds) * 0.24; // Estimated tax rate on RMDs

    strategies.push({
      id: 'qcd-strategy',
      title: 'Qualified Charitable Distributions',
      description: `Donate $${qcdAmount.toLocaleString()} directly from your IRA to charity to satisfy RMDs tax-free.`,
      estimatedSavings: taxSavings,
      implementation:
        '1. Contact your IRA custodian to set up QCDs\n2. Donate up to $100,000 annually directly to qualified charities\n3. Counts toward your RMD but not included in AGI\n4. Must be done before taking RMDs for the year',
      impact: 'high',
      eligibility: (data) =>
        (data.age || 0) >= 70.5 && (data.retirementAccounts?.traditionalIRA || 0) > 100000,
      priority: 15,
      category: 'retirement',
      timeHorizon: 'immediate',
      complexity: 'medium',
      risk: 'low',
      lastUpdated: new Date().toISOString(),
      references: [
        'https://www.irs.gov/retirement-plans/retirement-plans-faqs-regarding-required-minimum-distributions',
        'https://www.fidelity.com/learning-center/personal-finance/required-minimum-distributions',
      ],
    });
  }

  // 16. Tax-Gain Harvesting in Lower Income Years
  const hasTaxableInvestments = (investments?.taxableBrokerage || 0) > 0;
  const isLowIncomeYear = income < 50000; // Example threshold for lower income years

  if (hasTaxableInvestments && isLowIncomeYear) {
    const potentialGain = Math.min(50000, (investments?.taxableBrokerage || 0) * 0.2);
    const taxSavings = potentialGain * 0.15; // 0% or 15% capital gains rate

    strategies.push({
      id: 'tax-gain-harvesting',
      title: 'Tax-Gain Harvesting',
      description: `Realize up to $${potentialGain.toLocaleString()} in capital gains at 0% tax rate.`,
      estimatedSavings: taxSavings,
      implementation:
        '1. Sell appreciated securities with long-term gains\n2. Immediately repurchase to step up cost basis\n3. Pay 0% tax on gains if income is below threshold',
      impact: 'high',
      eligibility: (data) =>
        (data.income || 0) < 50000 && (data.investments?.taxableBrokerage || 0) > 0,
      priority: 16,
      category: 'investments',
      timeHorizon: 'short-term',
      complexity: 'medium',
      risk: 'low',
      lastUpdated: new Date().toISOString(),
      references: [
        'https://www.bogleheads.org/wiki/Tax_gain_harvesting',
        'https://www.nerdwallet.com/article/investing/tax-gain-harvesting',
      ],
    });
  }

  // Sort strategies by priority, then impact, then estimated savings
  return strategies
    .filter((strategy) => strategy.eligibility(userData))
    .sort((a, b) => {
      // First by priority (if specified)
      if (a.priority !== undefined && b.priority !== undefined && a.priority !== b.priority) {
        return a.priority - b.priority;
      }

      // Then by impact (high to low)
      const impactOrder = { high: 3, medium: 2, low: 1 };
      if (impactOrder[b.impact] !== impactOrder[a.impact]) {
        return impactOrder[b.impact] - impactOrder[a.impact];
      }

      // Finally by estimated savings (high to low)
      return b.estimatedSavings - a.estimatedSavings;
    });
}

/**
 * Calculate optimal retirement contribution amount
 *
 * @param income - Annual income
 * @param filingStatus - Filing status
 * @param currentContribution - Current retirement contribution
 * @param accountType - Type of retirement account (401k, IRA, etc.)
 * @returns Recommended contribution amount and estimated tax savings
 */
export function calculateOptimalRetirementContribution(
  income: number,
  filingStatus: 'single' | 'married' | 'headOfHousehold',
  currentContribution: number,
  accountType: '401k' | 'ira' | 'hsa' = '401k'
): { recommendedContribution: number; taxSavings: number } {
  const contributionLimits = {
    '401k': 22500, // 2023 limit
    ira: 6500, // 2023 limit
    hsa: 3850, // 2023 individual limit
  };

  const maxContribution = contributionLimits[accountType];
  const recommendedContribution = Math.min(
    maxContribution,
    Math.max(0, maxContribution - currentContribution),
    income * 0.25 // Don't recommend more than 25% of income
  );

  const taxSavings = calculateRetirementContributionSavings(
    income,
    recommendedContribution,
    filingStatus
  );

  return {
    recommendedContribution,
    taxSavings,
  };
}

/**
 * Compare tax scenarios
 *
 * @param scenarios - Array of tax scenarios to compare
 * @returns Comparison of tax scenarios with estimated tax liability
 */
export function compareTaxScenarios(
  scenarios: Array<{
    name: string;
    income: number;
    deductions: number;
    credits: number;
    filingStatus: 'single' | 'married' | 'headOfHousehold';
    year?: number;
  }>
): Array<{
  name: string;
  taxableIncome: number;
  taxLiability: number;
  effectiveTaxRate: number;
  afterTaxIncome: number;
}> {
  return scenarios.map((scenario) => {
    const { taxableIncome, federalTax } = calculateDetailedTaxLiability(
      scenario.income - scenario.deductions,
      scenario.filingStatus,
      { year: scenario.year }
    );

    const taxLiability = federalTax - scenario.credits;
    const afterTaxIncome = scenario.income - taxLiability;

    return {
      name: scenario.name,
      taxableIncome,
      taxLiability,
      effectiveTaxRate: taxLiability / scenario.income,
      afterTaxIncome,
    };
  });
}
