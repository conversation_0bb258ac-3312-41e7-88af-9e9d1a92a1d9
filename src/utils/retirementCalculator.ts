/**
 * Retirement Calculator Utility
 *
 * This utility provides accurate retirement savings calculations with compound interest.
 */

/**
 * Calculate future value with compound interest
 *
 * @param principal - Initial investment amount
 * @param monthlyContribution - Monthly contribution amount
 * @param annualRate - Annual interest rate (decimal)
 * @param years - Number of years
 * @returns Future value
 */
export const calculateFutureValue = (
  principal: number,
  monthlyContribution: number,
  annualRate: number,
  years: number
): number => {
  if (
    isNaN(principal) ||
    isNaN(monthlyContribution) ||
    isNaN(annualRate) ||
    isNaN(years) ||
    years < 0
  ) {
    return 0;
  }

  const monthlyRate = annualRate / 12;
  const months = years * 12;

  // Future value of initial principal
  const principalFV = principal * Math.pow(1 + monthlyRate, months);

  // Future value of monthly contributions
  let contributionFV = 0;
  if (monthlyRate > 0) {
    contributionFV = monthlyContribution * ((Math.pow(1 + monthlyRate, months) - 1) / monthlyRate);
  } else {
    contributionFV = monthlyContribution * months;
  }

  return principalFV + contributionFV;
};

/**
 * Calculate required monthly savings to reach a goal
 *
 * @param goal - Target amount
 * @param currentSavings - Current savings amount
 * @param annualRate - Annual interest rate (decimal)
 * @param years - Number of years
 * @returns Required monthly contribution
 */
export const calculateRequiredMonthlySavings = (
  goal: number,
  currentSavings: number,
  annualRate: number,
  years: number
): number => {
  if (isNaN(goal) || isNaN(currentSavings) || isNaN(annualRate) || isNaN(years) || years <= 0) {
    return 0;
  }

  const monthlyRate = annualRate / 12;
  const months = years * 12;

  // Future value of current savings
  const savingsFV = currentSavings * Math.pow(1 + monthlyRate, months);

  // Additional amount needed
  const additionalNeeded = goal - savingsFV;

  if (additionalNeeded <= 0) return 0;

  // Calculate required monthly contribution
  if (monthlyRate > 0) {
    return additionalNeeded / ((Math.pow(1 + monthlyRate, months) - 1) / monthlyRate);
  } else {
    return additionalNeeded / months;
  }
};

/**
 * Calculate retirement income needed
 *
 * @param currentIncome - Current annual income
 * @param replacementRatio - Income replacement ratio (decimal)
 * @returns Annual retirement income needed
 */
export const calculateRetirementIncomeNeeded = (
  currentIncome: number,
  replacementRatio: number = 0.8
): number => {
  if (
    isNaN(currentIncome) ||
    isNaN(replacementRatio) ||
    currentIncome < 0 ||
    replacementRatio < 0
  ) {
    return 0;
  }

  return currentIncome * replacementRatio;
};

/**
 * Calculate total retirement savings needed
 *
 * @param annualIncomeNeeded - Annual income needed in retirement
 * @param withdrawalRate - Safe withdrawal rate (decimal)
 * @param socialSecurityBenefit - Annual Social Security benefit
 * @param pensionBenefit - Annual pension benefit
 * @param otherIncome - Other annual income in retirement
 * @returns Total retirement savings needed
 */
export const calculateRetirementSavingsNeeded = (
  annualIncomeNeeded: number,
  withdrawalRate: number = 0.04,
  socialSecurityBenefit: number = 0,
  pensionBenefit: number = 0,
  otherIncome: number = 0
): number => {
  if (
    isNaN(annualIncomeNeeded) ||
    isNaN(withdrawalRate) ||
    isNaN(socialSecurityBenefit) ||
    isNaN(pensionBenefit) ||
    isNaN(otherIncome) ||
    withdrawalRate <= 0
  ) {
    return 0;
  }

  // Calculate income gap (amount that needs to come from savings)
  const incomeGap = Math.max(
    0,
    annualIncomeNeeded - socialSecurityBenefit - pensionBenefit - otherIncome
  );

  // Calculate savings needed to generate the income gap
  return incomeGap / withdrawalRate;
};

/**
 * Calculate retirement readiness
 *
 * @param currentSavings - Current retirement savings
 * @param monthlyContribution - Monthly contribution to retirement
 * @param annualRate - Annual interest rate (decimal)
 * @param yearsUntilRetirement - Years until retirement
 * @param savingsNeeded - Total savings needed for retirement
 * @returns Retirement readiness percentage
 */
export const calculateRetirementReadiness = (
  currentSavings: number,
  monthlyContribution: number,
  annualRate: number,
  yearsUntilRetirement: number,
  savingsNeeded: number
): number => {
  if (
    isNaN(currentSavings) ||
    isNaN(monthlyContribution) ||
    isNaN(annualRate) ||
    isNaN(yearsUntilRetirement) ||
    isNaN(savingsNeeded) ||
    savingsNeeded <= 0
  ) {
    return 0;
  }

  // Calculate projected savings at retirement
  const projectedSavings = calculateFutureValue(
    currentSavings,
    monthlyContribution,
    annualRate,
    yearsUntilRetirement
  );

  // Calculate readiness percentage
  return Math.min(100, (projectedSavings / savingsNeeded) * 100);
};

/**
 * Calculate retirement income duration
 *
 * @param savingsAmount - Total retirement savings
 * @param annualWithdrawal - Annual withdrawal amount
 * @param annualRate - Annual interest rate during retirement (decimal)
 * @param inflationRate - Annual inflation rate (decimal)
 * @returns Number of years the savings will last
 */
export const calculateRetirementIncomeDuration = (
  savingsAmount: number,
  annualWithdrawal: number,
  annualRate: number = 0.04,
  inflationRate: number = 0.025
): number => {
  if (
    isNaN(savingsAmount) ||
    isNaN(annualWithdrawal) ||
    isNaN(annualRate) ||
    isNaN(inflationRate) ||
    savingsAmount <= 0 ||
    annualWithdrawal <= 0
  ) {
    return 0;
  }

  // Calculate real rate of return (adjusted for inflation)
  const realRate = (1 + annualRate) / (1 + inflationRate) - 1;

  // If real rate is zero, use simple division
  if (Math.abs(realRate) < 0.0001) {
    return savingsAmount / annualWithdrawal;
  }

  // Calculate duration using the formula for present value of an annuity
  return (
    (Math.log(1 - (savingsAmount * realRate) / annualWithdrawal) / Math.log(1 + realRate)) * -1
  );
};

/**
 * Calculate retirement withdrawal amount
 *
 * @param savingsAmount - Total retirement savings
 * @param withdrawalRate - Safe withdrawal rate (decimal)
 * @returns Annual withdrawal amount
 */
export const calculateRetirementWithdrawal = (
  savingsAmount: number,
  withdrawalRate: number = 0.04
): number => {
  if (isNaN(savingsAmount) || isNaN(withdrawalRate) || savingsAmount < 0 || withdrawalRate < 0) {
    return 0;
  }

  return savingsAmount * withdrawalRate;
};

/**
 * Calculate retirement savings gap
 *
 * @param savingsNeeded - Total savings needed for retirement
 * @param currentSavings - Current retirement savings
 * @param monthlyContribution - Monthly contribution to retirement
 * @param annualRate - Annual interest rate (decimal)
 * @param yearsUntilRetirement - Years until retirement
 * @returns Retirement savings gap
 */
export const calculateRetirementSavingsGap = (
  savingsNeeded: number,
  currentSavings: number,
  monthlyContribution: number,
  annualRate: number,
  yearsUntilRetirement: number
): number => {
  if (
    isNaN(savingsNeeded) ||
    isNaN(currentSavings) ||
    isNaN(monthlyContribution) ||
    isNaN(annualRate) ||
    isNaN(yearsUntilRetirement)
  ) {
    return 0;
  }

  // Calculate projected savings at retirement
  const projectedSavings = calculateFutureValue(
    currentSavings,
    monthlyContribution,
    annualRate,
    yearsUntilRetirement
  );

  // Calculate gap
  return Math.max(0, savingsNeeded - projectedSavings);
};
