// AES encryption/decryption utility using Web Crypto API

/**
 * Encrypts a string using AES-GCM with a password
 * @param data - The plaintext string to encrypt
 * @param password - The password to use for encryption
 * @returns The encrypted data as a base64 string
 */
export async function encryptData(data: string, password: string): Promise<string> {
  const enc = new TextEncoder();
  const salt = crypto.getRandomValues(new Uint8Array(16));
  const iv = crypto.getRandomValues(new Uint8Array(12));
  const keyMaterial = await getKeyMaterial(password);
  const key = await deriveKey(keyMaterial, salt);
  const encrypted = await crypto.subtle.encrypt({ name: 'AES-GCM', iv }, key, enc.encode(data));
  // Combine salt, iv, and encrypted data
  const encryptedBytes = new Uint8Array([...salt, ...iv, ...new Uint8Array(encrypted)]);
  return btoa(String.fromCharCode(...encryptedBytes));
}

/**
 * Decrypts a string using AES-GCM with a password
 * @param encryptedData - The encrypted data as a base64 string
 * @param password - The password to use for decryption
 * @returns The decrypted plaintext string
 */
export async function decryptData(encryptedData: string, password: string): Promise<string> {
  const encryptedBytes = Uint8Array.from(atob(encryptedData), (c) => c.charCodeAt(0));
  const salt = encryptedBytes.slice(0, 16);
  const iv = encryptedBytes.slice(16, 28);
  const data = encryptedBytes.slice(28);
  const keyMaterial = await getKeyMaterial(password);
  const key = await deriveKey(keyMaterial, salt);
  const decrypted = await crypto.subtle.decrypt({ name: 'AES-GCM', iv }, key, data);
  return new TextDecoder().decode(decrypted);
}

async function getKeyMaterial(password: string): Promise<CryptoKey> {
  const enc = new TextEncoder();
  return crypto.subtle.importKey('raw', enc.encode(password), { name: 'PBKDF2' }, false, [
    'deriveKey',
  ]);
}

async function deriveKey(keyMaterial: CryptoKey, salt: Uint8Array): Promise<CryptoKey> {
  return crypto.subtle.deriveKey(
    {
      name: 'PBKDF2',
      salt,
      iterations: 100000,
      hash: 'SHA-256',
    },
    keyMaterial,
    { name: 'AES-GCM', length: 256 },
    false,
    ['encrypt', 'decrypt']
  );
}

/**
 * Hash a password using PBKDF2
 * @param password - The plaintext password
 * @returns The hash as a base64 string with salt
 */
export async function hashPassword(password: string): Promise<string> {
  const enc = new TextEncoder();
  const salt = crypto.getRandomValues(new Uint8Array(16));
  const key = await crypto.subtle.importKey(
    'raw',
    enc.encode(password),
    { name: 'PBKDF2' },
    false,
    ['deriveBits']
  );
  const hashBuffer = await crypto.subtle.deriveBits(
    {
      name: 'PBKDF2',
      salt,
      iterations: 100000,
      hash: 'SHA-256',
    },
    key,
    256
  );
  const hash = new Uint8Array(hashBuffer);
  // Store salt and hash together
  const combined = new Uint8Array([...salt, ...hash]);
  return btoa(String.fromCharCode(...combined));
}

/**
 * Verify a password against a stored hash
 * @param password - The plaintext password
 * @param storedHash - The stored hash (base64 string with salt)
 * @returns True if the password matches, false otherwise
 */
export async function verifyPassword(password: string, storedHash: string): Promise<boolean> {
  const combined = Uint8Array.from(atob(storedHash), (c) => c.charCodeAt(0));
  const salt = combined.slice(0, 16);
  const hash = combined.slice(16);
  const enc = new TextEncoder();
  const key = await crypto.subtle.importKey(
    'raw',
    enc.encode(password),
    { name: 'PBKDF2' },
    false,
    ['deriveBits']
  );
  const hashBuffer = await crypto.subtle.deriveBits(
    {
      name: 'PBKDF2',
      salt,
      iterations: 100000,
      hash: 'SHA-256',
    },
    key,
    256
  );
  const newHash = new Uint8Array(hashBuffer);
  // Constant-time comparison
  if (newHash.length !== hash.length) return false;
  let isValid = true;
  for (let i = 0; i < newHash.length; i++) {
    if (newHash[i] !== hash[i]) isValid = false;
  }
  return isValid;
}
