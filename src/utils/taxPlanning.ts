/**
 * Tax Planning Utility
 *
 * This utility provides functions for tax planning calculations and analysis.
 * Enhanced with more accurate tax calculations and additional features.
 */

/**
 * Tax bracket interface
 */
interface TaxBracket {
  threshold: number;
  rate: number;
}

/**
 * Standard deduction amounts by filing status
 */
interface StandardDeduction {
  single: number;
  married: number;
  headOfHousehold: number;
}

/**
 * Tax credit interface
 */
interface TaxCredit {
  name: string;
  amount: number;
  phaseOutStart?: number;
  phaseOutEnd?: number;
  isRefundable: boolean;
}

/**
 * Get standard deduction amount based on filing status and tax year
 *
 * @param filingStatus - Filing status (single, married, head of household)
 * @param year - Tax year
 * @returns Standard deduction amount
 */
export const getStandardDeduction = (
  filingStatus: 'single' | 'married' | 'headOfHousehold',
  year: number = new Date().getFullYear()
): number => {
  // Standard deduction amounts by year
  const standardDeductions: Record<number, StandardDeduction> = {
    2023: {
      single: 13850,
      married: 27700,
      headOfHousehold: 20800,
    },
    2024: {
      single: 14600,
      married: 29200,
      headOfHousehold: 21900,
    },
  };

  // Use the most recent year if the requested year is not available
  const deductionYear = standardDeductions[year]
    ? year
    : Math.max(...Object.keys(standardDeductions).map(Number));

  return standardDeductions[deductionYear][filingStatus];
};

/**
 * Get tax brackets for a specific year and filing status
 *
 * @param filingStatus - Filing status (single, married, head of household)
 * @param year - Tax year
 * @returns Array of tax brackets
 */
export const getTaxBrackets = (
  filingStatus: 'single' | 'married' | 'headOfHousehold',
  year: number = new Date().getFullYear()
): TaxBracket[] => {
  // Tax brackets by year and filing status
  const bracketsByYear: Record<number, Record<string, TaxBracket[]>> = {
    2023: {
      single: [
        { threshold: 0, rate: 0.1 },
        { threshold: 11000, rate: 0.12 },
        { threshold: 44725, rate: 0.22 },
        { threshold: 95375, rate: 0.24 },
        { threshold: 182100, rate: 0.32 },
        { threshold: 231250, rate: 0.35 },
        { threshold: 578125, rate: 0.37 },
      ],
      married: [
        { threshold: 0, rate: 0.1 },
        { threshold: 22000, rate: 0.12 },
        { threshold: 89450, rate: 0.22 },
        { threshold: 190750, rate: 0.24 },
        { threshold: 364200, rate: 0.32 },
        { threshold: 462500, rate: 0.35 },
        { threshold: 693750, rate: 0.37 },
      ],
      headOfHousehold: [
        { threshold: 0, rate: 0.1 },
        { threshold: 15700, rate: 0.12 },
        { threshold: 59850, rate: 0.22 },
        { threshold: 95350, rate: 0.24 },
        { threshold: 182100, rate: 0.32 },
        { threshold: 231250, rate: 0.35 },
        { threshold: 578100, rate: 0.37 },
      ],
    },
    2024: {
      single: [
        { threshold: 0, rate: 0.1 },
        { threshold: 11600, rate: 0.12 },
        { threshold: 47150, rate: 0.22 },
        { threshold: 100525, rate: 0.24 },
        { threshold: 191950, rate: 0.32 },
        { threshold: 243725, rate: 0.35 },
        { threshold: 609350, rate: 0.37 },
      ],
      married: [
        { threshold: 0, rate: 0.1 },
        { threshold: 23200, rate: 0.12 },
        { threshold: 94300, rate: 0.22 },
        { threshold: 201050, rate: 0.24 },
        { threshold: 383900, rate: 0.32 },
        { threshold: 487450, rate: 0.35 },
        { threshold: 731200, rate: 0.37 },
      ],
      headOfHousehold: [
        { threshold: 0, rate: 0.1 },
        { threshold: 16550, rate: 0.12 },
        { threshold: 63100, rate: 0.22 },
        { threshold: 100500, rate: 0.24 },
        { threshold: 191950, rate: 0.32 },
        { threshold: 243700, rate: 0.35 },
        { threshold: 609350, rate: 0.37 },
      ],
    },
  };

  // Use the most recent year if the requested year is not available
  const bracketsYear = bracketsByYear[year]
    ? year
    : Math.max(...Object.keys(bracketsByYear).map(Number));

  return bracketsByYear[bracketsYear][filingStatus];
};

/**
 * Calculate estimated tax liability based on income and filing status
 * with support for deductions and credits
 *
 * @param income - Total income
 * @param filingStatus - Filing status (single, married, head of household)
 * @param options - Additional options for tax calculation
 * @returns Detailed tax calculation results
 */
export const calculateDetailedTaxLiability = (
  income: number,
  filingStatus: 'single' | 'married' | 'headOfHousehold',
  options: {
    year?: number;
    itemizedDeductions?: number;
    adjustments?: number;
    credits?: TaxCredit[];
    dependents?: number;
    isBlind?: boolean;
    isOver65?: boolean;
    stateIncomeTaxRate?: number;
  } = {}
): {
  grossIncome: number;
  adjustedGrossIncome: number;
  taxableIncome: number;
  federalTax: number;
  stateTax: number;
  totalTax: number;
  effectiveTaxRate: number;
  marginalTaxRate: number;
  afterTaxIncome: number;
  taxByBracket: Array<{ bracket: TaxBracket; taxAmount: number }>;
  appliedCredits: Array<{ name: string; amount: number }>;
} => {
  const year = options.year || new Date().getFullYear();
  const adjustments = options.adjustments || 0;
  const dependents = options.dependents || 0;
  const stateIncomeTaxRate = options.stateIncomeTaxRate || 0;

  // Calculate Adjusted Gross Income (AGI)
  const adjustedGrossIncome = Math.max(0, income - adjustments);

  // Determine deduction (standard or itemized)
  const standardDeduction = getStandardDeduction(filingStatus, year);
  const itemizedDeductions = options.itemizedDeductions || 0;
  const deduction = Math.max(standardDeduction, itemizedDeductions);

  // Calculate taxable income
  const taxableIncome = Math.max(0, adjustedGrossIncome - deduction);

  // Get tax brackets
  const brackets = getTaxBrackets(filingStatus, year);

  // Calculate tax by bracket
  let federalTax = 0;
  const taxByBracket: Array<{ bracket: TaxBracket; taxAmount: number }> = [];
  let marginalTaxRate = 0;

  for (let i = 0; i < brackets.length; i++) {
    const currentBracket = brackets[i];
    const nextBracket = brackets[i + 1];

    if (taxableIncome > currentBracket.threshold) {
      marginalTaxRate = currentBracket.rate;

      let taxableAmountInBracket;
      if (!nextBracket) {
        // This is the highest bracket
        taxableAmountInBracket = taxableIncome - currentBracket.threshold;
      } else if (taxableIncome > nextBracket.threshold) {
        // Calculate tax for this bracket
        taxableAmountInBracket = nextBracket.threshold - currentBracket.threshold;
      } else {
        // This is the last applicable bracket
        taxableAmountInBracket = taxableIncome - currentBracket.threshold;
      }

      const taxForBracket = taxableAmountInBracket * currentBracket.rate;
      federalTax += taxForBracket;

      taxByBracket.push({
        bracket: currentBracket,
        taxAmount: taxForBracket,
      });
    }
  }

  // Apply tax credits
  const appliedCredits: Array<{ name: string; amount: number }> = [];
  let totalCredits = 0;

  if (options.credits && options.credits.length > 0) {
    options.credits.forEach((credit) => {
      let creditAmount = credit.amount;

      // Apply phase-out if applicable
      if (
        credit.phaseOutStart &&
        credit.phaseOutEnd &&
        adjustedGrossIncome > credit.phaseOutStart
      ) {
        if (adjustedGrossIncome >= credit.phaseOutEnd) {
          creditAmount = 0;
        } else {
          const phaseOutRange = credit.phaseOutEnd - credit.phaseOutStart;
          const phaseOutAmount =
            ((adjustedGrossIncome - credit.phaseOutStart) / phaseOutRange) * credit.amount;
          creditAmount = Math.max(0, credit.amount - phaseOutAmount);
        }
      }

      // For non-refundable credits, limit to tax liability
      if (!credit.isRefundable) {
        creditAmount = Math.min(creditAmount, federalTax - totalCredits);
      }

      if (creditAmount > 0) {
        appliedCredits.push({
          name: credit.name,
          amount: creditAmount,
        });

        totalCredits += creditAmount;
      }
    });
  }

  // Apply child tax credit if dependents are specified
  if (dependents > 0) {
    const childTaxCreditPerChild = 2000; // $2,000 per qualifying child
    const childTaxCreditPhaseOutStart = filingStatus === 'married' ? 400000 : 200000;
    const childTaxCreditPhaseOutRate = 0.05; // $50 per $1,000 above threshold

    let childTaxCredit = dependents * childTaxCreditPerChild;

    // Apply phase-out
    if (adjustedGrossIncome > childTaxCreditPhaseOutStart) {
      const phaseOutAmount =
        Math.floor((adjustedGrossIncome - childTaxCreditPhaseOutStart) / 1000) * 50 * dependents;
      childTaxCredit = Math.max(0, childTaxCredit - phaseOutAmount);
    }

    // Child tax credit is partially refundable
    const nonRefundablePortion = Math.min(childTaxCredit, federalTax - totalCredits);

    if (nonRefundablePortion > 0) {
      appliedCredits.push({
        name: 'Child Tax Credit (Non-refundable)',
        amount: nonRefundablePortion,
      });

      totalCredits += nonRefundablePortion;
    }

    // Additional Child Tax Credit (refundable portion)
    const refundablePortion = Math.min(
      childTaxCredit - nonRefundablePortion,
      Math.max(0, adjustedGrossIncome - 2500) * 0.15
    );

    if (refundablePortion > 0) {
      appliedCredits.push({
        name: 'Additional Child Tax Credit (Refundable)',
        amount: refundablePortion,
      });

      totalCredits += refundablePortion;
    }
  }

  // Apply credits to federal tax
  federalTax = Math.max(0, federalTax - totalCredits);

  // Calculate state income tax
  const stateTax = adjustedGrossIncome * stateIncomeTaxRate;

  // Calculate total tax
  const totalTax = federalTax + stateTax;

  // Calculate effective tax rate
  const effectiveTaxRate = income > 0 ? (totalTax / income) * 100 : 0;

  // Calculate after-tax income
  const afterTaxIncome = income - totalTax;

  return {
    grossIncome: income,
    adjustedGrossIncome,
    taxableIncome,
    federalTax,
    stateTax,
    totalTax,
    effectiveTaxRate,
    marginalTaxRate: marginalTaxRate * 100,
    afterTaxIncome,
    taxByBracket,
    appliedCredits,
  };
};

/**
 * Calculate estimated tax liability based on income and filing status
 * (Simplified version for backward compatibility)
 *
 * @param income - Total taxable income
 * @param filingStatus - Filing status (single, married, head of household)
 * @param year - Tax year (defaults to current year)
 * @returns Estimated tax liability
 */
export const calculateTaxLiability = (
  income: number,
  filingStatus: 'single' | 'married' | 'headOfHousehold',
  year: number = new Date().getFullYear()
): number => {
  const brackets = getTaxBrackets(filingStatus, year);
  let tax = 0;

  // Calculate tax for each bracket
  for (let i = 0; i < brackets.length; i++) {
    const currentBracket = brackets[i];
    const nextBracket = brackets[i + 1];

    if (income > currentBracket.threshold) {
      if (!nextBracket) {
        // This is the highest bracket
        tax += (income - currentBracket.threshold) * currentBracket.rate;
        break;
      } else if (income > nextBracket.threshold) {
        // Calculate tax for this bracket
        tax += (nextBracket.threshold - currentBracket.threshold) * currentBracket.rate;
      } else {
        // This is the last applicable bracket
        tax += (income - currentBracket.threshold) * currentBracket.rate;
        break;
      }
    }
  }

  return Math.max(0, Math.round(tax));
};

/**
 * Calculate potential tax savings from retirement contributions
 *
 * @param income - Total taxable income
 * @param contribution - Retirement contribution amount
 * @param filingStatus - Filing status (single, married, head of household)
 * @returns Estimated tax savings
 */
export const calculateRetirementContributionSavings = (
  income: number,
  contribution: number,
  filingStatus: 'single' | 'married' | 'headOfHousehold'
): number => {
  const taxWithoutContribution = calculateTaxLiability(income, filingStatus);
  const taxWithContribution = calculateTaxLiability(income - contribution, filingStatus);

  return taxWithoutContribution - taxWithContribution;
};

/**
 * Calculate required minimum distributions (RMDs) from retirement accounts
 *
 * @param accountBalance - Current retirement account balance
 * @param age - Current age
 * @returns Estimated RMD amount
 */
export const calculateRMD = (accountBalance: number, age: number): number => {
  // RMD factors based on IRS Uniform Lifetime Table (simplified)
  const rmdFactors: Record<number, number> = {
    72: 27.4,
    73: 26.5,
    74: 25.5,
    75: 24.6,
    76: 23.7,
    77: 22.9,
    78: 22.0,
    79: 21.1,
    80: 20.2,
    81: 19.4,
    82: 18.5,
    83: 17.7,
    84: 16.8,
    85: 16.0,
    86: 15.2,
    87: 14.4,
    88: 13.7,
    89: 12.9,
    90: 12.2,
    91: 11.5,
    92: 10.8,
    93: 10.1,
    94: 9.5,
    95: 8.9,
    96: 8.4,
    97: 7.8,
    98: 7.3,
    99: 6.8,
    100: 6.4,
  };

  // If age is less than 72, no RMD is required
  if (age < 72) {
    return 0;
  }

  // If age is greater than 100, use the factor for age 100
  const factor = rmdFactors[Math.min(age, 100)] || 6.4;

  return Math.round(accountBalance / factor);
};

/**
 * Capital gains tax bracket interface
 */
interface CapitalGainsBracket {
  threshold: number;
  rate: number;
}

/**
 * Get capital gains tax brackets for a specific year and filing status
 *
 * @param filingStatus - Filing status (single, married, head of household)
 * @param year - Tax year
 * @returns Array of capital gains tax brackets
 */
export const getCapitalGainsBrackets = (
  filingStatus: 'single' | 'married' | 'headOfHousehold',
  year: number = new Date().getFullYear()
): CapitalGainsBracket[] => {
  // Capital gains tax brackets by year and filing status
  const bracketsByYear: Record<number, Record<string, CapitalGainsBracket[]>> = {
    2023: {
      single: [
        { threshold: 0, rate: 0.0 },
        { threshold: 44625, rate: 0.15 },
        { threshold: 492300, rate: 0.2 },
      ],
      married: [
        { threshold: 0, rate: 0.0 },
        { threshold: 89250, rate: 0.15 },
        { threshold: 553850, rate: 0.2 },
      ],
      headOfHousehold: [
        { threshold: 0, rate: 0.0 },
        { threshold: 59750, rate: 0.15 },
        { threshold: 523050, rate: 0.2 },
      ],
    },
    2024: {
      single: [
        { threshold: 0, rate: 0.0 },
        { threshold: 47025, rate: 0.15 },
        { threshold: 518900, rate: 0.2 },
      ],
      married: [
        { threshold: 0, rate: 0.0 },
        { threshold: 94050, rate: 0.15 },
        { threshold: 583750, rate: 0.2 },
      ],
      headOfHousehold: [
        { threshold: 0, rate: 0.0 },
        { threshold: 63000, rate: 0.15 },
        { threshold: 551350, rate: 0.2 },
      ],
    },
  };

  // Use the most recent year if the requested year is not available
  const bracketsYear = bracketsByYear[year]
    ? year
    : Math.max(...Object.keys(bracketsByYear).map(Number));

  return bracketsByYear[bracketsYear][filingStatus];
};

/**
 * Calculate capital gains tax on investments with detailed breakdown
 *
 * @param gain - Capital gain amount
 * @param income - Total taxable income (excluding capital gains)
 * @param filingStatus - Filing status (single, married, head of household)
 * @param options - Additional options for capital gains tax calculation
 * @returns Detailed capital gains tax calculation results
 */
export const calculateDetailedCapitalGainsTax = (
  gain: number,
  income: number,
  filingStatus: 'single' | 'married' | 'headOfHousehold',
  options: {
    year?: number;
    isLongTerm?: boolean;
    isQualifiedDividend?: boolean;
    netInvestmentIncomeTax?: boolean;
  } = {}
): {
  gain: number;
  taxableIncome: number;
  totalTaxableIncome: number;
  capitalGainsTax: number;
  effectiveCapitalGainsRate: number;
  netInvestmentIncomeTax: number;
  totalTax: number;
  taxByBracket: Array<{ bracket: CapitalGainsBracket; taxAmount: number }>;
} => {
  const year = options.year || new Date().getFullYear();
  const isLongTerm = options.isLongTerm !== undefined ? options.isLongTerm : true;
  const isQualifiedDividend =
    options.isQualifiedDividend !== undefined ? options.isQualifiedDividend : false;
  const applyNIIT =
    options.netInvestmentIncomeTax !== undefined ? options.netInvestmentIncomeTax : true;

  // Short-term capital gains are taxed as ordinary income
  if (!isLongTerm && !isQualifiedDividend) {
    const taxLiability = calculateDetailedTaxLiability(income + gain, filingStatus, { year });
    const taxLiabilityWithoutGain = calculateDetailedTaxLiability(income, filingStatus, { year });

    return {
      gain,
      taxableIncome: income,
      totalTaxableIncome: income + gain,
      capitalGainsTax: taxLiability.federalTax - taxLiabilityWithoutGain.federalTax,
      effectiveCapitalGainsRate:
        gain > 0
          ? ((taxLiability.federalTax - taxLiabilityWithoutGain.federalTax) / gain) * 100
          : 0,
      netInvestmentIncomeTax: 0, // Included in ordinary income tax
      totalTax: taxLiability.federalTax - taxLiabilityWithoutGain.federalTax,
      taxByBracket: [],
    };
  }

  // Long-term capital gains and qualified dividends
  const brackets = getCapitalGainsBrackets(filingStatus, year);
  let capitalGainsTax = 0;
  const taxByBracket: Array<{ bracket: CapitalGainsBracket; taxAmount: number }> = [];

  // Calculate tax for each bracket
  let remainingGain = gain;
  for (let i = 0; i < brackets.length; i++) {
    const currentBracket = brackets[i];
    const nextBracket = brackets[i + 1];

    // Determine how much of the gain falls into this bracket
    let gainInBracket = 0;

    if (income < currentBracket.threshold) {
      // Income is below this bracket's threshold
      if (!nextBracket || income + remainingGain <= nextBracket.threshold) {
        // All remaining gain fits in this bracket
        gainInBracket = remainingGain;
      } else {
        // Gain spans multiple brackets
        gainInBracket = nextBracket.threshold - Math.max(currentBracket.threshold, income);
      }
    } else if (!nextBracket || income < nextBracket.threshold) {
      // Income is in this bracket
      if (!nextBracket || income + remainingGain <= nextBracket.threshold) {
        // All remaining gain fits in this bracket
        gainInBracket = remainingGain;
      } else {
        // Gain spans multiple brackets
        gainInBracket = nextBracket.threshold - income;
      }
    }

    if (gainInBracket > 0) {
      const taxForBracket = gainInBracket * currentBracket.rate;
      capitalGainsTax += taxForBracket;

      taxByBracket.push({
        bracket: currentBracket,
        taxAmount: taxForBracket,
      });

      remainingGain -= gainInBracket;
      income += gainInBracket;
    }

    if (remainingGain <= 0) break;
  }

  // Calculate Net Investment Income Tax (NIIT) - 3.8% on investment income for high earners
  let niit = 0;
  if (applyNIIT) {
    const niitThreshold =
      filingStatus === 'married' ? 250000 : filingStatus === 'headOfHousehold' ? 200000 : 200000;
    if (income > niitThreshold) {
      niit = gain * 0.038;
    }
  }

  // Calculate total tax and effective rate
  const totalTax = capitalGainsTax + niit;
  const effectiveRate = gain > 0 ? (totalTax / gain) * 100 : 0;

  return {
    gain,
    taxableIncome: income - gain,
    totalTaxableIncome: income,
    capitalGainsTax,
    effectiveCapitalGainsRate: gain > 0 ? (capitalGainsTax / gain) * 100 : 0,
    netInvestmentIncomeTax: niit,
    totalTax,
    taxByBracket,
  };
};

/**
 * Calculate capital gains tax on investments
 * (Simplified version for backward compatibility)
 *
 * @param gain - Capital gain amount
 * @param income - Total taxable income
 * @param filingStatus - Filing status (single, married, head of household)
 * @param year - Tax year (defaults to current year)
 * @returns Estimated capital gains tax
 */
export const calculateCapitalGainsTax = (
  gain: number,
  income: number,
  filingStatus: 'single' | 'married' | 'headOfHousehold',
  year: number = new Date().getFullYear()
): number => {
  const brackets = getCapitalGainsBrackets(filingStatus, year);
  const totalIncome = income + gain;

  // Find the applicable rate based on total income
  let rate = 0;
  for (let i = brackets.length - 1; i >= 0; i--) {
    if (totalIncome > brackets[i].threshold) {
      rate = brackets[i].rate;
      break;
    }
  }

  return Math.round(gain * rate);
};

/**
 * Estate tax bracket interface
 */
interface EstateTaxBracket {
  threshold: number;
  rate: number;
}

/**
 * Get estate tax exemption amount for a specific year
 *
 * @param year - Tax year
 * @returns Estate tax exemption amount
 */
export const getEstateTaxExemption = (year: number = new Date().getFullYear()): number => {
  // Estate tax exemption amounts by year
  const exemptionsByYear: Record<number, number> = {
    2021: 11700000,
    2022: 12060000,
    2023: 12920000,
    2024: 13610000,
  };

  // Use the most recent year if the requested year is not available
  const exemptionYear = exemptionsByYear[year]
    ? year
    : Math.max(...Object.keys(exemptionsByYear).map(Number));

  return exemptionsByYear[exemptionYear];
};

/**
 * Get estate tax brackets for a specific year
 *
 * @param year - Tax year
 * @returns Array of estate tax brackets
 */
export const getEstateTaxBrackets = (
  year: number = new Date().getFullYear()
): EstateTaxBracket[] => {
  // Estate tax brackets (same for all years, rates adjusted for inflation)
  return [
    { threshold: 0, rate: 0.18 },
    { threshold: 10000, rate: 0.2 },
    { threshold: 20000, rate: 0.22 },
    { threshold: 40000, rate: 0.24 },
    { threshold: 60000, rate: 0.26 },
    { threshold: 80000, rate: 0.28 },
    { threshold: 100000, rate: 0.3 },
    { threshold: 150000, rate: 0.32 },
    { threshold: 250000, rate: 0.34 },
    { threshold: 500000, rate: 0.37 },
    { threshold: 750000, rate: 0.39 },
    { threshold: 1000000, rate: 0.4 },
  ];
};

/**
 * Calculate estate tax liability with detailed breakdown
 *
 * @param estateValue - Total estate value
 * @param options - Additional options for estate tax calculation
 * @returns Detailed estate tax calculation results
 */
export const calculateDetailedEstateTax = (
  estateValue: number,
  options: {
    year?: number;
    stateEstateTax?: number;
    lifeTimeGifts?: number;
    deductions?: {
      charitableContributions?: number;
      maritalDeduction?: number;
      debts?: number;
      administrativeExpenses?: number;
      stateDeathTaxes?: number;
    };
    portableExemption?: number;
  } = {}
): {
  grossEstate: number;
  taxableEstate: number;
  exemptionAmount: number;
  estateTax: number;
  stateEstateTax: number;
  totalEstateTax: number;
  effectiveEstateTaxRate: number;
  netEstate: number;
  taxByBracket: Array<{ bracket: EstateTaxBracket; taxAmount: number }>;
} => {
  const year = options.year || new Date().getFullYear();
  const lifeTimeGifts = options.lifeTimeGifts || 0;
  const stateEstateTaxRate = options.stateEstateTax || 0;

  // Calculate deductions
  const charitableContributions = options.deductions?.charitableContributions || 0;
  const maritalDeduction = options.deductions?.maritalDeduction || 0;
  const debts = options.deductions?.debts || 0;
  const administrativeExpenses = options.deductions?.administrativeExpenses || 0;
  const stateDeathTaxes = options.deductions?.stateDeathTaxes || 0;

  const totalDeductions =
    charitableContributions + maritalDeduction + debts + administrativeExpenses + stateDeathTaxes;

  // Get exemption amount
  const baseExemption = getEstateTaxExemption(year);
  const portableExemption = options.portableExemption || 0;
  const totalExemption = baseExemption + portableExemption;

  // Calculate taxable estate
  const grossEstate = estateValue + lifeTimeGifts;
  const taxableEstate = Math.max(0, grossEstate - totalDeductions - totalExemption);

  // If taxable estate is zero, no tax is due
  if (taxableEstate <= 0) {
    return {
      grossEstate,
      taxableEstate: 0,
      exemptionAmount: totalExemption,
      estateTax: 0,
      stateEstateTax: 0,
      totalEstateTax: 0,
      effectiveEstateTaxRate: 0,
      netEstate: estateValue - totalDeductions,
      taxByBracket: [],
    };
  }

  // Get tax brackets
  const brackets = getEstateTaxBrackets(year);

  // Calculate tax by bracket
  let estateTax = 0;
  const taxByBracket: Array<{ bracket: EstateTaxBracket; taxAmount: number }> = [];

  for (let i = 0; i < brackets.length; i++) {
    const currentBracket = brackets[i];
    const nextBracket = brackets[i + 1];

    if (taxableEstate > currentBracket.threshold) {
      let taxableAmountInBracket;

      if (!nextBracket) {
        // This is the highest bracket
        taxableAmountInBracket = taxableEstate - currentBracket.threshold;
      } else if (taxableEstate > nextBracket.threshold) {
        // Calculate tax for this bracket
        taxableAmountInBracket = nextBracket.threshold - currentBracket.threshold;
      } else {
        // This is the last applicable bracket
        taxableAmountInBracket = taxableEstate - currentBracket.threshold;
      }

      const taxForBracket = taxableAmountInBracket * currentBracket.rate;
      estateTax += taxForBracket;

      taxByBracket.push({
        bracket: currentBracket,
        taxAmount: taxForBracket,
      });
    }
  }

  // Calculate state estate tax
  const stateEstateTax = taxableEstate * stateEstateTaxRate;

  // Calculate total estate tax
  const totalEstateTax = estateTax + stateEstateTax;

  // Calculate effective estate tax rate
  const effectiveEstateTaxRate = grossEstate > 0 ? (totalEstateTax / grossEstate) * 100 : 0;

  // Calculate net estate after taxes
  const netEstate = estateValue - totalDeductions - totalEstateTax;

  return {
    grossEstate,
    taxableEstate,
    exemptionAmount: totalExemption,
    estateTax,
    stateEstateTax,
    totalEstateTax,
    effectiveEstateTaxRate,
    netEstate,
    taxByBracket,
  };
};

/**
 * Calculate estate tax liability
 * (Simplified version for backward compatibility)
 *
 * @param estateValue - Total estate value
 * @param year - Tax year (defaults to current year)
 * @returns Estimated estate tax liability
 */
export const calculateEstateTax = (
  estateValue: number,
  year: number = new Date().getFullYear()
): number => {
  // Get exemption amount
  const exemption = getEstateTaxExemption(year);

  // If estate value is less than exemption, no tax is due
  if (estateValue <= exemption) {
    return 0;
  }

  // Calculate taxable estate
  const taxableEstate = estateValue - exemption;

  // Estate tax rate is 40% on amount over exemption (simplified)
  return Math.round(taxableEstate * 0.4);
};
