/**
 * Enhanced PDF Export Utility
 *
 * This utility provides functions for generating comprehensive PDF reports from financial data
 * across all four directions of the Financial Compass.
 *
 * It uses jsPDF and jspdf-autotable libraries.
 */

// Import dependencies
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import { FinancialCompassData } from '../types/compass'; // Import necessary types
import { formatCurrency } from './formatters';

// Define a type for PDF options if needed, though the tool output suggested removing JsPDFOptions
// interface JsPDFOptions {
//   orientation?: 'portrait' | 'landscape';
//   unit?: string;
//   format?: string | [number, number];
//   compress?: boolean;
//   precision?: number;
//   hotfixes?: string[];
//   putOnlyUsedFonts?: boolean;
//   userUnit?: number;
// }

interface InsuranceAnalysis {
  lifeInsurance?: {
    hasInsurance?: boolean;
    coverageAmount?: string;
  };
  healthInsurance?: {
    hasInsurance?: boolean;
  };
  disabilityInsurance?: {
    hasInsurance?: boolean;
    coverageAmount?: string;
  };
}

interface EmergencyFund {
  currentEmergencyFund?: string;
  targetMonths?: string; // Added targetMonths to EmergencyFund interface
}

interface EstatePlanning {
  hasWill?: boolean;
  hasTrust?: boolean;
  hasPowerOfAttorney?: boolean;
  hasHealthcareDirective?: boolean;
  hasEstateStrategy?: boolean;
  hasSuccessionPlan?: boolean;
  hasTaxStrategy?: boolean;
}

interface EstateDocuments {
  will?: {
    exists?: boolean;
  };
  trust?: {
    exists?: boolean;
  };
  powerOfAttorney?: {
    exists?: boolean;
  };
  healthcareDirective?: {
    exists?: boolean;
  };
}

interface LegacyPlanning {
  legacyStatement?: string; // Corrected type from boolean to string if it holds a statement
  beneficiaryDesignationsInPlace?: boolean;
  hasCharitableStrategy?: boolean;
}

interface FinancialCompassData {
  metadata: Record<string, unknown>;
  financialCompass: {
    north?: {
      expenseDetails?: {
        totalMonthlyExpenses?: string;
        expenseCategories?: Record<string, Record<string, string>>; // Added type for expenseCategories
      };
      incomeDetails?: {
        incomeSources?: { // Added type for incomeSources
          name: string;
          type: string;
          amount: string;
          frequency: string;
        }[];
      };
      assets?: Record<string, Record<string, string | number | { balance?: string | number }[]>>; // Added type for assets, including retirement accounts
      liabilities?: Record<string, Record<string, string | number>>; // Added type for liabilities
      personalInformation?: {
        age?: string;
      };
      netWorthDetails?: {
        totalAssets?: string;
        totalLiabilities?: string;
      };
      cashFlowAnalysis?: { // Added cashFlowAnalysis based on previous usage
        monthlyCashFlow?: string;
        savingsRate?: string;
      };
    };
    east?: { // Defined EastData partially based on usage
      retirementGoals?: {
        targetRetirementAge?: string;
        savingsGoal?: string;
        expectedReturnRate?: string;
        targetMonthlyIncome?: string;
        lifeExpectancy?: string; // Added based on usage in assessRetirementReadiness
      };
      retirementIncome?: { // Added type for retirementIncome
        incomeSources?: { // Added type for retirementIncome.incomeSources
          name: string;
          type: string;
          amount: string;
          frequency: string;
        }[];
        socialSecurity?: { // Added type for socialSecurity
          estimatedMonthlyBenefit?: string;
          startAge?: string; // Added based on usage in calculateTotalMonthlyIncome in FinancialCompassSummaryPage
        };
        pension?: { // Added type for pension
          estimatedMonthlyBenefit?: string;
          startAge?: string; // Added based on usage in calculateTotalMonthlyIncome in FinancialCompassSummaryPage
        };
      };
      retirementTimeline?: { // Added type for retirementTimeline
        currentSavings?: string;
        events?: { // Added type for retirementTimeline.events
          age: number;
          year: number;
          event: string;
          description: string;
        }[];
      };
      retirementExpenses?: { // Added type for retirementExpenses
        housingExpense?: string;
        healthcareExpense?: string;
        foodExpense?: string;
        transportationExpense?: string;
        travelExpense?: string;
        entertainmentExpense?: string;
        otherExpense?: string;
      };
    };
    south?: { // Defined SouthData based on usage
      insuranceAnalysis?: InsuranceAnalysis;
      emergencyFund?: EmergencyFund;
      debtManagement?: { // Added type for debtManagement
        monthlyDebtPayments?: string;
        debtItems?: { // Added type for debtItems
          type: string;
          balance: string;
          interestRate: string;
          actualPayment?: string;
          minimumPayment?: string;
        }[];
        debtToIncomeRatio?: string; // Added debtToIncomeRatio
      };
      protectionGap?: { // Added type for protectionGap based on usage in calculateDetailedFinancialHealthScore
        totalGap?: string;
      };
    };
    west?: { // Defined WestData based on usage
      estatePlanning?: EstatePlanning;
      estateDocuments?: EstateDocuments;
      legacyPlanning?: LegacyPlanning;
      charitableGiving?: { // Added type for charitableGiving
        favoriteOrganizations?: { // Added type for favoriteOrganizations
          name: string;
          category: string;
          annualDonation: string;
        }[];
      };
      valuesGoals?: { // Added type for valuesGoals
        personalMission?: string;
        coreValues?: { // Added type for coreValues
          value: string;
          importance: string;
          description: string;
        }[];
        lifeGoals?: { // Added type for lifeGoals
          goal: string;
          category: string;
          targetDate: string;
          status: string;
        }[];
      };
    };
  };
}

// Helper function to add header and footer
const addHeaderFooter = (doc: jsPDF, pageNumber: number, totalPages: number) => {
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();

  // Header
  doc.setFontSize(10);
  doc.setTextColor(100);
  doc.text('Financial Compass Report', pageWidth / 2, 15, {
    align: 'center',
  });

  // Footer
  doc.text(`Page ${pageNumber} of ${totalPages}`, pageWidth / 2, pageHeight - 10, {
    align: 'center',
  });
};

// Helper function to generate sections
const addSection = (
  doc: jsPDF,
  title: string,
  content: string | string[],
  yPos: number,
  options?: { title?: string; }
) => {
  doc.setFontSize(14);
  doc.setTextColor(0);
  doc.setFont(undefined, 'bold');
  doc.text(title, 15, yPos);
  doc.setFont(undefined, 'normal');
  doc.setFontSize(10);
  const textLines = Array.isArray(content)
    ? content
    : doc.splitTextToSize(content, doc.internal.pageSize.getWidth() - 30);
  doc.text(textLines, 15, yPos + 8);
  return yPos + 8 + textLines.length * 7 + 10; // Return new Y position
};

// Helper function to add a table
const addTable = (
  doc: jsPDF,
  columns: string[],
  data: (string | number)[][],
  yPos: number,
  title?: string,
  options?: { columnStyles?: Record<string, { cellWidth?: number; halign?: string }> }
) => {
  if (title) {
    doc.setFontSize(12);
    doc.setTextColor(0);
    doc.setFont(undefined, 'bold');
    doc.text(title, 15, yPos);
    yPos += 8;
    doc.setFont(undefined, 'normal');
  }

  (doc as any).autoTable({
    startY: yPos,
    head: [columns],
    body: data,
    theme: 'striped',
    headStyles: {
      fillColor: [220, 220, 220],
      textColor: 0,
      fontStyle: 'bold',
    },
    styles: {
      fontSize: 9,
      cellPadding: 5,
    },
    columnStyles: options?.columnStyles || {},
    margin: { top: 10, right: 15, bottom: 10, left: 15 },
    didDrawPage: (data: any) => {
      addHeaderFooter(doc, data.pageNumber, doc.internal.pages.length - 1);
    },
  });

  const finalY = (doc as any).autoTable.previous.finalY;
  return finalY + 10;
};

// Helper function to calculate total income (basic)
const calculateTotalIncome = (incomeSources: FinancialCompassData['financialCompass']['north']['incomeDetails']['incomeSources'] = []): number => {
  return incomeSources.reduce((total, source) => {
    const amount = parseFloat(source?.amount || '0') || 0;
    switch (source?.frequency) {
      case 'annual':
        return total + amount / 12;
      case 'monthly':
        return total + amount;
      case 'weekly':
        return total + (amount * 52) / 12;
      case 'bi-weekly':
        return total + (amount * 26) / 12;
      case 'one-time':
        return total; // One-time income is not added to recurring monthly income
      default:
        console.warn(`Unrecognized income frequency: ${source?.frequency}. Assuming monthly if amount > 0.`);
        return total + (amount > 0 ? amount : 0); // Assume monthly if amount > 0 for unknown frequency
    }
  }, 0);
};

// Helper function to calculate total expenses (basic)
const calculateTotalExpenses = (expenseCategories: FinancialCompassData['financialCompass']['north']['expenseDetails']['expenseCategories'] = {}): number => {
  let total = 0;
  for (const category in expenseCategories) {
    if (Object.prototype.hasOwnProperty.call(expenseCategories, category)) {
      const expenses = expenseCategories[category] || {};
      total += Object.values(expenses).reduce(
        (sum, value) => sum + (typeof value === 'string' ? parseFloat(value) || 0 : 0),
        0
      );
    }
  }
  return total;
};

// Helper function to calculate total assets (basic)
const calculateTotalAssets = (assets: FinancialCompassData['financialCompass']['north']['assets'] = {}): number => {
  let total = 0;
  for (const category in assets) {
    if (Object.prototype.hasOwnProperty.call(assets, category)) {
      const assetCategory = assets[category] || {};
      total += Object.values(assetCategory).reduce(
        (sum, value) => sum + (typeof value === 'string' ? parseFloat(value) || 0 : (typeof value === 'number' ? value : 0)),
        0
      );
    }
  }
  return total;
};

// Helper function to calculate total liabilities (basic)
const calculateTotalLiabilities = (liabilities: FinancialCompassData['financialCompass']['north']['liabilities'] = {}): number => {
  let total = 0;
  for (const category in liabilities) {
    if (Object.prototype.hasOwnProperty.call(liabilities, category)) {
      const liabilityCategory = liabilities[category] || {};
      total += Object.values(liabilityCategory).reduce(
        (sum, value) => sum + (typeof value === 'string' ? parseFloat(value) || 0 : (typeof value === 'number' ? value : 0)),
        0
      );
    }
  }
  return total;
};

// Helper function to calculate net worth
const calculateNetWorth = (totalAssets: number, totalLiabilities: number): number => {
  return totalAssets - totalLiabilities;
};

// Helper function for basic financial health assessment
const assessFinancialHealth = (data: FinancialCompassData) => {
  const northData = data.financialCompass?.north;

  // Ensure required data exists before accessing properties
  const incomeDetails = northData?.incomeDetails;
  const expenseDetails = northData?.expenseDetails;
  const assets = northData?.assets;
  const liabilities = northData?.liabilities;
  const emergencyFund = data.financialCompass?.south?.emergencyFund;
  const debtManagement = data.financialCompass?.south?.debtManagement;

  // Safely calculate metrics, defaulting to 0 if data is missing
  const totalMonthlyIncome = calculateTotalIncome(incomeDetails?.incomeSources);
  const totalMonthlyExpenses = calculateTotalExpenses(expenseDetails?.expenseCategories);
  const monthlyCashFlow = totalMonthlyIncome - totalMonthlyExpenses;
  const totalAssets = calculateTotalAssets(assets);
  const totalLiabilities = calculateTotalLiabilities(liabilities);
  const netWorth = calculateNetWorth(totalAssets, totalLiabilities);
  const currentEmergencyFund = parseFloat(emergencyFund?.currentEmergencyFund || '0') || 0;
  const targetEmergencyFundMonths = parseFloat(emergencyFund?.targetMonths || '3') || 3;
  const recommendedEmergencyFund = totalMonthlyExpenses * targetEmergencyFundMonths;
  const totalMonthlyDebtPayments = parseFloat(debtManagement?.monthlyDebtPayments || '0') || 0;
  const annualIncome = totalMonthlyIncome * 12;

  const debtToIncomeRatio =
    annualIncome > 0
      ? (totalMonthlyDebtPayments / annualIncome) * 100
      : 0;
  const savingsRate = totalMonthlyIncome > 0 ? ((totalMonthlyIncome - totalMonthlyExpenses - totalMonthlyDebtPayments) / totalMonthlyIncome) * 100 : 0;

  let cashFlowStatus = 'Neutral';
  if (monthlyCashFlow > 0) cashFlowStatus = 'Positive';
  if (monthlyCashFlow < 0) cashFlowStatus = 'Negative';

  let savingsRateStatus = 'Critical';
  if (savingsRate >= 20) savingsRateStatus = 'Excellent';
  else if (savingsRate >= 15) savingsRateStatus = 'Very Good';
  else if (savingsRate >= 10) savingsRateStatus = 'Good';
  else if (savingsRate >= 5) savingsRateStatus = 'Fair';
  else if (savingsRate > 0) savingsRateStatus = 'Needs Improvement';

  let debtToIncomeStatus = 'Excellent';
  if (debtToIncomeRatio > 43) debtToIncomeStatus = 'Concerning';
  else if (debtToIncomeRatio > 36) debtToIncomeStatus = 'Fair';

  let emergencyFundStatus = 'Critical';
  if (currentEmergencyFund >= recommendedEmergencyFund) emergencyFundStatus = 'Excellent';
  else if (currentEmergencyFund >= recommendedEmergencyFund * 0.75)
    emergencyFundStatus = 'Very Good'; // 75% funded
  else if (currentEmergencyFund >= recommendedEmergencyFund * 0.5)
    emergencyFundStatus = 'Good'; // 50% funded
  else if (currentEmergencyFund > 0) emergencyFundStatus = 'Fair';

  let netWorthStatus = 'Needs Attention';
  if (netWorth > 0) netWorthStatus = 'Positive';

  return {
    monthlyCashFlow,
    totalMonthlyIncome,
    totalMonthlyExpenses,
    cashFlowStatus,
    savingsRate,
    savingsRateStatus,
    debtToIncomeRatio,
    debtToIncomeStatus,
    currentEmergencyFund,
    targetEmergencyFundMonths,
    recommendedEmergencyFund,
    emergencyFundStatus,
    netWorth,
    totalAssets,
    totalLiabilities,
    netWorthStatus,
    totalMonthlyDebtPayments,
    annualIncome,
  };
};

// Helper function to assess retirement readiness
const assessRetirementReadiness = (data: FinancialCompassData) => {
  const northData = data.financialCompass?.north;
  const eastData = data.financialCompass?.east;

  const retirementGoals = eastData?.retirementGoals;
  const assets = northData?.assets;
  const personalInfo = northData?.personalInformation;
  const retirementTimeline = eastData?.retirementTimeline;

  // Safely access properties with default values
  const currentAge = parseInt(personalInfo?.age || '0') || 0;
  const retirementAge = parseInt(retirementGoals?.targetRetirementAge || '65') || 65;
  const yearsUntilRetirement = Math.max(0, retirementAge - currentAge);
  const savingsGoal = parseFloat(retirementGoals?.savingsGoal || '0') || 0;
  // Safely access retirement accounts and current savings
  const retirementAccounts = Array.isArray(assets?.retirement) ? assets.retirement : [];

  const currentRetirementSavings = parseFloat(
    retirementAccounts.reduce(
      (sum: number, account: { balance?: string | number }) => sum + (parseFloat(account.balance as string) || 0),
      0
    ) ||
    retirementTimeline?.currentSavings ||
    '0'
  ) || 0;

  const expectedReturnRate = parseFloat(retirementGoals?.expectedReturnRate || '0') / 100 || 0;

  // Calculate savings gap
  const savingsGap = Math.max(0, savingsGoal - currentRetirementSavings);

  const requiredMonthlySavings =
    savingsGap > 0 && yearsUntilRetirement > 0 ? savingsGap / (yearsUntilRetirement * 12) : 0;

  let savingsGapDescription = 'On track to meet your retirement savings goal.';
  if (savingsGap > 0) {
    savingsGapDescription = `You have a savings gap of ${formatCurrency(savingsGap)}. You need to save an additional ${formatCurrency(requiredMonthlySavings)} per month to reach your goal by retirement.`;
  }

  // Basic Income Replacement Ratio (Simplified) - Needs detailed income/expense analysis in retirement
  const estimatedRetirementIncome =
    parseFloat(eastData?.retirementIncome?.socialSecurity?.estimatedMonthlyBenefit || '0') || 0 +
    parseFloat(eastData?.retirementIncome?.pension?.estimatedMonthlyBenefit || '0') || 0;
  const estimatedRetirementExpenses =
    parseFloat(eastData?.retirementExpenses?.housingExpense || '0') || 0 +
    parseFloat(eastData?.retirementExpenses?.healthcareExpense || '0') || 0 +
    parseFloat(eastData?.retirementExpenses?.foodExpense || '0') || 0 +
    parseFloat(eastData?.retirementExpenses?.transportationExpense || '0') || 0 +
    parseFloat(eastData?.retirementExpenses?.travelExpense || '0') || 0 +
    parseFloat(eastData?.retirementExpenses?.entertainmentExpense || '0') || 0 +
    parseFloat(eastData?.retirementExpenses?.otherExpense || '0') || 0;

  const incomeReplacement =
    estimatedRetirementExpenses > 0
      ? (estimatedRetirementIncome / estimatedRetirementExpenses) * 100
      : 0;

  let replacementDescription = 'Estimated retirement income meets or exceeds estimated expenses.';
  if (incomeReplacement < 80 && estimatedRetirementExpenses > 0) {
    replacementDescription = `Your estimated retirement income (${formatCurrency(estimatedRetirementIncome)}/mo) is less than your estimated retirement expenses (${formatCurrency(estimatedRetirementExpenses)}/mo), resulting in an estimated ${incomeReplacement.toFixed(0)}% income replacement ratio. Consider increasing savings or reducing future expenses.`;
  } else if (estimatedRetirementExpenses === 0) {
    replacementDescription =
      'Could not estimate income replacement ratio due to missing retirement expense data.';
  }

  // Basic Investment Strategy Assessment (Needs detailed investment data and risk tolerance)
  const investmentStrategy = 'Review your investment strategy.';
  const strategyDescription =
    'Ensure your asset allocation aligns with your risk tolerance and time horizon.';

  return {
    currentAge,
    retirementAge,
    lifeExpectancy: parseInt(retirementGoals?.lifeExpectancy || '0') || 0,
    yearsUntilRetirement,
    retirementDuration: Math.max(0, (parseInt(retirementGoals?.lifeExpectancy || '0') || 0) - retirementAge),
    savingsGoal,
    currentRetirementSavings,
    savingsGap,
    requiredMonthlySavings,
    savingsGapDescription,
    estimatedRetirementIncome,
    estimatedRetirementExpenses,
    incomeReplacement: incomeReplacement.toFixed(0),
    replacementDescription,
    investmentStrategy,
    strategyDescription,
  };
};

// Helper function to assess protection planning
const assessProtectionPlanning = (data: FinancialCompassData) => {
  const southData = data.financialCompass?.south;
  const northData = data.financialCompass?.north;

  const insuranceCoverage = southData?.insuranceAnalysis;
  const emergencyFund = southData?.emergencyFund;

  // Check for essential insurance types safely
  const hasLifeInsurance = insuranceCoverage?.lifeInsurance?.hasInsurance === true;
  const hasHealthInsurance = insuranceCoverage?.healthInsurance?.hasInsurance === true;
  const hasDisabilityInsurance = insuranceCoverage?.disabilityInsurance?.hasInsurance === true;

  let insuranceStatus = 'Needs Review';
  let insuranceDescription = 'Review your insurance policies to ensure adequate coverage.';

  if (hasLifeInsurance && hasHealthInsurance && hasDisabilityInsurance) {
    insuranceStatus = 'Appears Adequate';
    insuranceDescription = 'Essential insurance coverage appears to be in place.';
  } else if (hasHealthInsurance && hasDisabilityInsurance) {
    insuranceStatus = 'Life Insurance Needed';
    insuranceDescription = 'Consider obtaining life insurance to protect your dependents.';
  } else if (hasHealthInsurance && hasLifeInsurance) {
    insuranceStatus = 'Disability Insurance Needed';
    insuranceDescription = 'Consider obtaining disability insurance to protect your income.';
  } else if (hasLifeInsurance && hasDisabilityInsurance) {
    insuranceStatus = 'Health Insurance Needed';
    insuranceDescription = 'Ensure you have adequate health insurance coverage.';
  } else if (hasHealthInsurance) {
    insuranceStatus = 'Life and Disability Insurance Needed';
    insuranceDescription = 'Consider obtaining life and disability insurance.';
  } else if (hasLifeInsurance) {
    insuranceStatus = 'Health and Disability Insurance Needed';
    insuranceDescription = 'Consider obtaining health and disability insurance.';
  } else if (hasDisabilityInsurance) {
    insuranceStatus = 'Life and Health Insurance Needed';
    insuranceDescription = 'Consider obtaining life and health insurance.';
  }

  // Emergency Fund Status safely
  const currentEmergencyFund = parseFloat(emergencyFund?.currentEmergencyFund || '0') || 0;
  const targetEmergencyFundMonths = parseFloat(emergencyFund?.targetMonths || '3') || 3;
  const monthlyExpenses = parseFloat(
    northData?.expenseDetails?.totalMonthlyExpenses || '0'
  ) || 0;
  const recommendedEmergencyFund = monthlyExpenses * targetEmergencyFundMonths;

  let emergencyFundStatus = 'Critical';
  let emergencyFundDescription = 'No emergency fund. This should be a top priority.';

  if (currentEmergencyFund >= recommendedEmergencyFund) {
    emergencyFundStatus = 'Excellent';
    emergencyFundDescription = `Your emergency fund of ${formatCurrency(currentEmergencyFund)} meets or exceeds the recommended ${targetEmergencyFundMonths} months of expenses (${formatCurrency(recommendedEmergencyFund)}).`;
  } else if (currentEmergencyFund > 0) {
    emergencyFundStatus = 'Needs Improvement';
    emergencyFundDescription = `Your emergency fund of ${formatCurrency(currentEmergencyFund)} is below the recommended ${targetEmergencyFundMonths} months of expenses (${formatCurrency(recommendedEmergencyFund)}). Work on building it up.`;
  }

  return {
    hasLifeInsurance,
    hasHealthInsurance,
    hasDisabilityInsurance,
    insuranceStatus,
    insuranceDescription,
    currentEmergencyFund,
    targetEmergencyFundMonths,
    recommendedEmergencyFund,
    emergencyFundStatus,
    emergencyFundDescription,
  };
};

// Helper function to assess estate planning
const assessEstatePlanning = (data: FinancialCompassData) => {
  const westData = data.financialCompass?.west;

  const estatePlanning = westData?.estatePlanning;
  const estateDocuments = westData?.estateDocuments;
  const legacyPlanning = westData?.legacyPlanning;

  // Check for essential documents safely
  const hasWill = estatePlanning?.hasWill === true || estateDocuments?.will?.exists === true;
  const hasTrust = estatePlanning?.hasTrust === true || estateDocuments?.trust?.exists === true;
  const hasPowerOfAttorney =
    estatePlanning?.hasPowerOfAttorney === true ||
    estateDocuments?.powerOfAttorney?.exists === true;
  const hasHealthcareDirective =
    estatePlanning?.hasHealthcareDirective === true ||
    estateDocuments?.healthcareDirective?.exists === true;
  const hasBeneficiaryDesignations = legacyPlanning?.beneficiaryDesignationsInPlace === true;
  const hasLegacyPlan = legacyPlanning ? Object.keys(legacyPlanning).length > 0 : false;

  let estatePlanStatus = 'Needs Attention';
  let estatePlanDescription = 'Review and establish essential estate planning documents.';

  if (hasWill && hasPowerOfAttorney && hasHealthcareDirective) {
    estatePlanStatus = 'Basic Plan in Place';
    estatePlanDescription =
      'Essential estate planning documents (Will, POA, Healthcare Directive) are in place.';
  } else if (hasWill || hasPowerOfAttorney || hasHealthcareDirective) {
    estatePlanStatus = 'Partially Complete';
    estatePlanDescription =
      'Some key estate planning documents are missing. Consider completing them.';
  }

  let beneficiaryStatus = 'Needs Review';
  let beneficiaryDescription = 'Review and update beneficiary designations on all accounts.';

  if (hasBeneficiaryDesignations) {
    beneficiaryStatus = 'Reviewed';
    beneficiaryDescription = 'Beneficiary designations appear to be in place.';
  }

  let legacyStatus = 'Not Defined';
  let legacyDescription = 'Consider defining your legacy goals and plan.';

  if (hasLegacyPlan) {
    legacyStatus = 'Defined';
    legacyDescription = typeof legacyPlanning?.legacyStatement === 'string' && legacyPlanning.legacyStatement.length > 0
      ? legacyPlanning.legacyStatement
      : 'Your legacy goals and plan have been defined.';
  }

  return {
    hasWill,
    hasTrust,
    hasPowerOfAttorney,
    hasHealthcareDirective,
    hasBeneficiaryDesignations,
    hasLegacyPlan,
    estatePlanStatus,
    estatePlanDescription,
    beneficiaryStatus,
    beneficiaryDescription,
    legacyStatus,
    legacyDescription,
  };
};

// Main function to generate the PDF
export const exportFinancialCompassPDF = async (data: FinancialCompassData): Promise<Blob> => {
  const doc = new jsPDF();
  let yPos = 15; // Starting Y position

  // Ensure financialCompass data exists
  const financialCompassData = data.financialCompass;
  if (!financialCompassData) {
    console.error('Financial Compass data is missing.');
    doc.setFontSize(16);
    doc.text('Financial Compass Comprehensive Report', doc.internal.pageSize.getWidth() / 2, 20, { align: 'center' });
    doc.setFontSize(12);
    doc.text('Data is missing or incomplete. Cannot generate report.', doc.internal.pageSize.getWidth() / 2, 40, { align: 'center' });
    return doc.output('blob');
  }

  const northData = financialCompassData.north;
  const eastData = financialCompassData.east;
  const southData = financialCompassData.south;
  const westData = financialCompassData.west;

  // Add Title Page (Optional)
  doc.setFontSize(24);
  doc.setTextColor(0);
  doc.setFont(undefined, 'bold');
  doc.text(
    'Financial Compass Comprehensive Report',
    doc.internal.pageSize.getWidth() / 2,
    yPos + 20,
    {
      align: 'center',
    }
  );
  doc.setFont(undefined, 'normal');
  doc.setFontSize(12);
  doc.text(
    `Report Date: ${new Date().toLocaleDateString()}`,
    doc.internal.pageSize.getWidth() / 2,
    yPos + 30,
    {
      align: 'center',
    }
  );
  yPos += 60;

  // Assess financial health and get recommendations using the utility
  const financialHealthResult: {
    overallScore: number;
    status: string;
    recommendations: { title: string; description: string; category: string; actionSteps?: string[]; icon?: string }[];
    overallDescription?: string;
  } = {
    overallScore: 0,
    status: 'Incomplete',
    recommendations: [],
  };

  try {
    const dummyCategories: any[] = [
      {
        id: 'cash_flow',
        name: 'Cash Flow',
        score: 0,
        weight: 0,
        weightedScore: 0,
        metrics: [],
      },
    ];
    const calculatedResult = calculateDetailedFinancialHealthScore(dummyCategories);
    financialHealthResult.overallScore = calculatedResult.overallScore;
    financialHealthResult.status = calculatedResult.status;
    financialHealthResult.recommendations = calculatedResult.recommendations as any;
    financialHealthResult.overallDescription = (calculatedResult as any).overallDescription;
  } catch (error) {
    console.error('Error calculating detailed financial health score:', error);
  }

  // Comprehensive Summary Section
  yPos = addSection(
    doc,
    'Overall Financial Summary',
    [
      `Your overall financial health score is ${financialHealthResult.overallScore}/100, categorized as ${financialHealthResult.status}.`,
      `This score reflects your performance across key areas: Cash Flow, Debt Management, Emergency Fund, Net Worth, Retirement Readiness, Protection Planning, and Estate Planning.`,
      'Recommendations:',
      ...(financialHealthResult.recommendations.length > 0
        ? financialHealthResult.recommendations.map((rec) => `- ${rec.title}: ${rec.description}`)
        : ['You are currently on track with your financial goals.']),
    ],
    yPos
  );

  // Detailed Sections

  // North Section: Current Position
  doc.addPage();
  yPos = 15; // Reset yPos for new page
  yPos = addSection(
    doc,
    'North: Current Financial Position',
    'Details about your income, expenses, assets, and liabilities.',
    yPos
  );

  // Income Details Table
  const incomeColumns = ['Source', 'Amount', 'Frequency'];
  const incomeData = (northData?.incomeDetails?.incomeSources || []).map((source) => [
    source.name || source.type,
    formatCurrency(parseFloat(source.amount) || 0),
    source.frequency,
  ]);
  yPos = addTable(doc, incomeColumns, incomeData, yPos, 'Income Sources');

  // Expense Details Table (simplified - needs structured expense categories)
  const expenseColumns = ['Category', 'Total Monthly'];
  const expenseData: (string | number)[][] = [];
  const expenseCategories = northData?.expenseDetails?.expenseCategories;
  if (expenseCategories) {
    for (const category in expenseCategories) {
      if (Object.prototype.hasOwnProperty.call(expenseCategories, category)) {
        const totalCategoryExpense = Object.values(expenseCategories[category] || {}).reduce(
          (sum, value) => sum + (typeof value === 'string' ? parseFloat(value) || 0 : 0),
          0
        );
        expenseData.push([category, formatCurrency(totalCategoryExpense)]);
      }
    }
  }
  yPos = addTable(doc, expenseColumns, expenseData, yPos, 'Expense Summary');

  // Assets Table
  const assetColumns = ['Category', 'Details', 'Value'];
  const assetData: (string | number)[][] = [];
  const assets = northData?.assets;
  if (assets) {
    for (const category in assets) {
      if (Object.prototype.hasOwnProperty.call(assets, category)) {
        const assetCategory = assets[category] || {};
        for (const item in assetCategory) {
          if (Object.prototype.hasOwnProperty.call(assetCategory, item)) {
            const value = assetCategory[item];
            if (Array.isArray(value)) {
              value.forEach(account => {
                assetData.push([category, account.name || 'Account', formatCurrency(parseFloat(account.balance as string) || 0)]);
              });
            } else {
              assetData.push([category, item, formatCurrency(parseFloat(value as string) || 0)]);
            }
          }
        }
      }
    }
  }
  yPos = addTable(doc, assetColumns, assetData, yPos, 'Assets');

  // Liabilities Table
  const liabilityColumns = ['Category', 'Details', 'Balance'];
  const liabilityData: (string | number)[][] = [];
  const liabilities = northData?.liabilities;
  if (liabilities) {
    for (const category in liabilities) {
      if (Object.prototype.hasOwnProperty.call(liabilities, category)) {
        const liabilityCategory = liabilities[category] || {};
        for (const item in liabilityCategory) {
          if (Object.prototype.hasOwnProperty.call(liabilityCategory, item)) {
            liabilityData.push([
              category,
              item,
              formatCurrency(parseFloat(liabilityCategory[item] as string) || 0),
            ]);
          }
        }
      }
    }
  }
  yPos = addTable(doc, liabilityColumns, liabilityData, yPos, 'Liabilities');

  // Net Worth Summary
  const financialHealthData = assessFinancialHealth(data);
  yPos = addSection(
    doc,
    'Net Worth',
    `Total Assets: ${formatCurrency(financialHealthData.totalAssets)}
Total Liabilities: ${formatCurrency(financialHealthData.totalLiabilities)}
Net Worth: ${formatCurrency(financialHealthData.netWorth)}`,
    yPos
  );

  // East Section: Retirement Vision
  doc.addPage();
  yPos = 15; // Reset yPos for new page
  yPos = addSection(
    doc,
    'East: Retirement Vision',
    'Details about your retirement goals, income, and expenses.',
    yPos
  );

  const retirementAssessment = assessRetirementReadiness(data);

  // Retirement Goals Summary
  yPos = addSection(
    doc,
    'Retirement Goals',
    [
      `Target Retirement Age: ${retirementAssessment.retirementAge}`,
      `Life Expectancy: ${retirementAssessment.lifeExpectancy}`,
      `Target Monthly Income: ${formatCurrency(parseFloat(eastData?.retirementGoals?.targetMonthlyIncome || '0') || 0)}`,
      `Retirement Savings Goal: ${formatCurrency(retirementAssessment.savingsGoal)}`,
    ],
    yPos
  );

  // Retirement Savings Progress
  yPos = addSection(
    doc,
    'Retirement Savings Progress',
    [
      `Current Retirement Savings: ${formatCurrency(retirementAssessment.currentRetirementSavings)}`,
      `Years Until Retirement: ${retirementAssessment.yearsUntilRetirement}`,
      retirementAssessment.savingsGapDescription,
      retirementAssessment.requiredMonthlySavings > 0
        ? `Required Additional Monthly Savings: ${formatCurrency(retirementAssessment.requiredMonthlySavings)}`
        : '',
    ].filter((line) => line !== ''),
    yPos
  );

  // Retirement Income Assessment
  yPos = addSection(
    doc,
    'Retirement Income Assessment',
    [
      `Estimated Monthly Retirement Income: ${formatCurrency(retirementAssessment.estimatedRetirementIncome)}`,
      `Estimated Monthly Retirement Expenses: ${formatCurrency(retirementAssessment.estimatedRetirementExpenses)}`,
      `Estimated Income Replacement Ratio: ${retirementAssessment.incomeReplacement}%`,
      retirementAssessment.replacementDescription,
    ].filter((line) => line !== ''),
    yPos
  );

  // Retirement Timeline (simplified)
  const timelineColumns = ['Age', 'Year', 'Event', 'Description'];
  const timelineData = (eastData?.retirementTimeline?.events || []).map((event) => [
    event.age,
    event.year,
    event.event,
    event.description,
  ]);
  yPos = addTable(doc, timelineColumns, timelineData, yPos, 'Key Retirement Timeline Events');

  // South Section: Protection & Risks
  doc.addPage();
  yPos = 15; // Reset yPos for new page
  yPos = addSection(
    doc,
    'South: Protection & Risks',
    'Details about your insurance coverage, emergency fund, and debt management.',
    yPos
  );

  const protectionAssessment = assessProtectionPlanning(data);

  // Insurance Coverage Summary
  yPos = addSection(
    doc,
    'Insurance Coverage',
    [
      `Life Insurance: ${protectionAssessment.hasLifeInsurance ? 'In Place' : 'Needed'}`,
      `Health Insurance: ${protectionAssessment.hasHealthInsurance ? 'In Place' : 'Needed'}`,
      `Disability Insurance: ${protectionAssessment.hasDisabilityInsurance ? 'In Place' : 'Needed'}`,
      protectionAssessment.insuranceDescription,
    ],
    yPos
  );

  // Emergency Fund Summary
  yPos = addSection(
    doc,
    'Emergency Fund',
    [
      `Current Emergency Fund: ${formatCurrency(protectionAssessment.currentEmergencyFund)}`,
      `Target Emergency Fund Months: ${protectionAssessment.targetEmergencyFundMonths}`,
      protectionAssessment.emergencyFundDescription,
    ],
    yPos
  );

  // Debt Management Summary
  const debtColumns = ['Type', 'Balance', 'Interest Rate', 'Monthly Payment'];
  const debtData = (southData?.debtManagement?.debtItems || []).map((item) => [
    item.type,
    formatCurrency(parseFloat(item.balance) || 0),
    `${item.interestRate}%`,
    formatCurrency(parseFloat(item.actualPayment || item.minimumPayment || '0') || 0),
  ]);
  yPos = addTable(doc, debtColumns, debtData, yPos, 'Debt Summary');

  // Debt Ratios
  const financialHealthDataForDebtRatio = assessFinancialHealth(data);
  yPos = addSection(
    doc,
    'Debt Ratios',
    [
      `Debt-to-Income Ratio: ${financialHealthDataForDebtRatio.debtToIncomeRatio.toFixed(2)}% (${financialHealthDataForDebtRatio.debtToIncomeStatus})`,
    ],
    yPos
  );

  // West Section: Legacy Planning
  doc.addPage();
  yPos = 15; // Reset yPos for new page
  yPos = addSection(
    doc,
    'West: Legacy Planning',
    'Details about your estate plan, charitable giving, and values.',
    yPos
  );

  const estatePlanningAssessment = assessEstatePlanning(data);

  // Estate Planning Summary
  yPos = addSection(
    doc,
    'Estate Planning',
    [
      `Will in Place: ${estatePlanningAssessment.hasWill ? 'Yes' : 'No'}`,
      `Trust in Place: ${estatePlanningAssessment.hasTrust ? 'Yes' : 'No'}`,
      `Power of Attorney in Place: ${estatePlanningAssessment.hasPowerOfAttorney ? 'Yes' : 'No'}`,
      `Healthcare Directive in Place: ${estatePlanningAssessment.hasHealthcareDirective ? 'Yes' : 'No'}`,
      estatePlanningAssessment.estatePlanDescription,
      `Beneficiary Designations: ${estatePlanningAssessment.hasBeneficiaryDesignations ? 'Reviewed' : 'Needs Review'}`,
      estatePlanningAssessment.beneficiaryDescription,
    ],
    yPos
  );

  // Charitable Giving Summary
  const givingColumns = ['Organization', 'Category', 'Annual Donation'];
  const givingData = (westData?.charitableGiving?.favoriteOrganizations || []).map((org) => [
    org.name,
    org.category,
    formatCurrency(parseFloat(org.annualDonation) || 0),
  ]);
  yPos = addTable(doc, givingColumns, givingData, yPos, 'Charitable Giving');

  // Values and Goals Summary (simplified)
  const valuesGoals = westData?.valuesGoals;
  if (valuesGoals) {
    yPos = addSection(
      doc,
      'Values and Goals',
      valuesGoals.personalMission || 'Personal mission not defined.',
      yPos
    );

    const valuesColumns = ['Value', 'Importance', 'Description'];
    const valuesData = (valuesGoals.coreValues || []).map((val) => [
      val.value,
      val.importance,
      val.description,
    ]);
    yPos = addTable(doc, valuesColumns, valuesData, yPos, 'Core Values');

    const goalsColumns = ['Goal', 'Category', 'Target Date', 'Status'];
    const goalsData = (valuesGoals.lifeGoals || []).map((goal) => [
      goal.goal,
      goal.category,
      goal.targetDate,
      goal.status,
    ]);
    yPos = addTable(doc, goalsColumns, goalsData, yPos, 'Life Goals');
  }

  // Legacy Planning Summary
  yPos = addSection(
    doc,
    'Legacy Planning',
    [
      `Legacy Plan Status: ${estatePlanningAssessment.legacyStatus}`,
      estatePlanningAssessment.legacyDescription,
    ],
    yPos
  );

  // Add a final page for Action Plan (Optional - could pull from action items)
  doc.addPage();
  yPos = 15;
  yPos = addSection(doc, 'Action Plan', 'Key steps to improve your financial health.', yPos);

  // Example Action Items Table (Needs actual action items data)
  const actionColumns = ['Priority', 'Title', 'Description', 'Status'];
  const actionData: (string | number)[][] = [];
  yPos = addTable(doc, actionColumns, actionData, yPos, 'Recommended Actions');

  // Add a disclaimer
  doc.setFontSize(9);
  doc.setTextColor(150);
  doc.text(
    'Disclaimer: This report is for informational purposes only and should not be considered financial advice. Consult with a qualified financial professional for personalized guidance.',
    15,
    doc.internal.pageSize.getHeight() - 20,
    { maxWidth: doc.internal.pageSize.getWidth() - 30 }
  );

  // Finalize PDF
  const pdfBlob = doc.output('blob');
  return pdfBlob;
};

// Helper function to identify strengths and weaknesses based on financial health assessment (simplified)
export const identifyStrengthsAndWeaknesses = (data: FinancialCompassData) => {
  const financialHealth = assessFinancialHealth(data);

  const strengths: { title: string; description: string; icon: string }[] = [];
  const weaknesses: { title: string; description: string; icon: string }[] = [];

  // Basic strength/weakness identification based on statuses
  if (financialHealth.cashFlowStatus === 'Positive') {
    strengths.push({
      title: 'Positive Cash Flow',
      description: `You have a positive monthly cash flow of ${formatCurrency(financialHealth.monthlyCashFlow)}.`,
      icon: '💰',
    });
  }
  if (
    financialHealth.savingsRateStatus === 'Excellent' ||
    financialHealth.savingsRateStatus === 'Very Good' ||
    financialHealth.savingsRateStatus === 'Good'
  ) {
    strengths.push({
      title: 'Healthy Savings Rate',
      description: `Your savings rate of ${financialHealth.savingsRate.toFixed(1)}% is strong.`,
      icon: '📈',
    });
  }
  if (
    financialHealth.debtToIncomeStatus === 'Excellent' ||
    financialHealth.debtToIncomeStatus === 'Good'
  ) {
    strengths.push({
      title: 'Manageable Debt Load',
      description: `Your debt-to-income ratio of ${financialHealth.debtToIncomeRatio.toFixed(1)}% is within healthy limits.`,
      icon: '⚖️',
    });
  }
  if (financialHealth.netWorthStatus === 'Positive') {
    strengths.push({
      title: 'Positive Net Worth',
      description: `Your net worth is ${formatCurrency(financialHealth.netWorth)}.`,
      icon: '💎',
    });
  }
  if (financialHealth.emergencyFundStatus === 'Excellent') {
    strengths.push({
      title: 'Robust Emergency Fund',
      description: `Your emergency fund of ${formatCurrency(financialHealth.currentEmergencyFund)} is well-funded.`,
      icon: '🛡️',
    });
  }

  // Assess protection and estate planning (simplified)
  const protection = assessProtectionPlanning(data);
  const estate = assessEstatePlanning(data);

  if (protection.insuranceStatus === 'Appears Adequate') {
    strengths.push({
      title: 'Adequate Insurance',
      description: protection.insuranceDescription,
      icon: '✅',
    });
  }
  if (estate.estatePlanStatus === 'Basic Plan in Place' || estate.estatePlanStatus === 'Partially Complete') {
    strengths.push({
      title: 'Estate Plan Started',
      description: estate.estatePlanDescription,
      icon: '📝',
    });
  }

  // Weaknesses
  if (financialHealth.cashFlowStatus === 'Negative') {
    weaknesses.push({
      title: 'Negative Cash Flow',
      description: `Your monthly cash flow is ${formatCurrency(financialHealth.monthlyCashFlow)}.`,
      icon: '📉',
    });
  }
  if (
    financialHealth.savingsRateStatus === 'Critical' ||
    financialHealth.savingsRateStatus === 'Needs Improvement' ||
    financialHealth.savingsRateStatus === 'Fair'
  ) {
    weaknesses.push({
      title: 'Low Savings Rate',
      description: `Your savings rate of ${financialHealth.savingsRate.toFixed(1)}% is below recommended levels.`,
      icon: '💸',
    });
  }
  if (
    financialHealth.debtToIncomeStatus === 'Concerning' ||
    financialHealth.debtToIncomeStatus === 'Fair'
  ) {
    weaknesses.push({
      title: 'High Debt Load',
      description: `Your debt-to-income ratio of ${financialHealth.debtToIncomeRatio.toFixed(1)}% is high.`,
      icon: '⚠️',
    });
  }
  if (financialHealth.netWorthStatus === 'Needs Attention') {
    weaknesses.push({
      title: 'Negative Net Worth',
      description: `Your net worth is ${formatCurrency(financialHealth.netWorth)}.`,
      icon: '🔻',
    });
  }
  if (
    financialHealth.emergencyFundStatus === 'Critical' ||
    financialHealth.emergencyFundStatus === 'Needs Improvement'
  ) {
    weaknesses.push({
      title: 'Inadequate Emergency Fund',
      description: protection.emergencyFundDescription,
      icon: '🚨',
    });
  }

  // Assess retirement readiness (simplified)
  const retirement = assessRetirementReadiness(data);

  if (retirement.savingsGap > 0) {
    weaknesses.push({
      title: 'Retirement Savings Gap',
      description: retirement.savingsGapDescription,
      icon: '🏦',
    });
  }

  if (protection.insuranceStatus !== 'Appears Adequate') {
    weaknesses.push({
      title: 'Insurance Gaps',
      description: protection.insuranceDescription,
      icon: '🔍',
    });
  }

  if (estate.estatePlanStatus === 'Needs Attention' || estate.estatePlanStatus === 'Partially Complete') {
    weaknesses.push({
      title: 'Incomplete Estate Plan',
      description: estate.estatePlanDescription,
      icon: '📝',
    });
  }

  return {
    strengths,
    weaknesses,
  };
};

// Helper function to generate action items based on weaknesses and recommendations
export const generateActionItems = (data: FinancialCompassData) => {
  const financialHealthResult: {
    overallScore: number;
    status: string;
    recommendations: { title: string; description: string; category: string; actionSteps?: string[]; icon?: string }[];
    overallDescription?: string;
  } = {
    overallScore: 0,
    status: 'Incomplete',
    recommendations: [],
  };

  try {
    const dummyCategories: any[] = [
      {
        id: 'cash_flow',
        name: 'Cash Flow',
        score: 0,
        weight: 0,
        weightedScore: 0,
        metrics: [],
      },
    ];
    const calculatedResult = calculateDetailedFinancialHealthScore(dummyCategories);
    financialHealthResult.overallScore = calculatedResult.overallScore;
    financialHealthResult.status = calculatedResult.status;
    financialHealthResult.recommendations = calculatedResult.recommendations as any;
    financialHealthResult.overallDescription = (calculatedResult as any).overallDescription;
  } catch (error) {
    console.error('Error calculating detailed financial health score:', error);
  }

  const actionItems: {
    title: string;
    description: string;
    priority: 'High' | 'Medium' | 'Low';
    category: string;
    actions?: string[];
  }[] = [];

  financialHealthResult.recommendations.forEach((rec) => {
    actionItems.push({
      title: rec.title,
      description: rec.description,
      priority: rec.priority as 'High' | 'Medium' | 'Low',
      category: rec.category,
      actions: rec.actionSteps,
    });
  });

  const protection = assessProtectionPlanning(data);
  if (
    protection.emergencyFundStatus === 'Critical' ||
    protection.emergencyFundStatus === 'Needs Improvement'
  ) {
    const existingRec = actionItems.find((item) =>
      item.title.toLowerCase().includes('emergency fund')
    );
    if (!existingRec) {
      actionItems.push({
        title: 'Build Emergency Fund',
        description: protection.emergencyFundDescription,
        priority: 'High',
        category: 'Emergency Fund',
      });
    }
  }

  const retirement = assessRetirementReadiness(data);
  if (retirement.savingsGap > 0) {
    const existingRec = actionItems.find(
      (item) =>
        item.title.toLowerCase().includes('retirement savings') ||
        item.title.toLowerCase().includes('savings gap')
    );
    if (!existingRec) {
      actionItems.push({
        title: 'Address Retirement Savings Gap',
        description: retirement.savingsGapDescription,
        priority: 'High',
        category: 'Retirement Readiness',
      });
    }
  }

  const estate = assessEstatePlanning(data);
  if (estate.estatePlanStatus !== 'Basic Plan in Place') {
    if (!estate.hasWill) {
      const existingRec = actionItems.find((item) => item.title.toLowerCase().includes('will'));
      if (!existingRec) {
        actionItems.push({
          title: 'Create a Will',
          description:
            'Establish a legal will to ensure your assets are distributed according to your wishes.',
          priority: 'High',
          category: 'Estate Planning',
        });
      }
    }
    if (!estate.hasPowerOfAttorney) {
      const existingRec = actionItems.find((item) =>
        item.title.toLowerCase().includes('power of attorney')
      );
      if (!existingRec) {
        actionItems.push({
          title: 'Establish Power of Attorney',
          description:
            'Designate someone to make financial and legal decisions on your behalf if you are unable.',
          priority: 'High',
          category: 'Estate Planning',
        });
      }
    }
    if (!estate.hasHealthcareDirective) {
      const existingRec = actionItems.find((item) =>
        item.title.toLowerCase().includes('healthcare directive')
      );
      if (!existingRec) {
        actionItems.push({
          title: 'Create Healthcare Directive',
          description:
            'Document your preferences for medical treatment and designate a healthcare proxy.',
          priority: 'High',
          category: 'Estate Planning',
        });
      }
    }
    if (!estate.hasBeneficiaryDesignations) {
      const existingRec = actionItems.find(item => item.title.toLowerCase().includes('beneficiary designations'));
      if (!existingRec) {
        actionItems.push({
          title: 'Review Beneficiary Designations',
          description: 'Ensure beneficiary designations on all your accounts and policies are up to date.',
          priority: 'Medium',
          category: 'Estate Planning',
        });
      }
    }
    if (!estate.hasLegacyPlan) {
      const existingRec = actionItems.find(item => item.title.toLowerCase().includes('legacy plan'));
      if (!existingRec) {
        actionItems.push({
          title: 'Define Your Legacy Plan',
          description: 'Consider your values and how you want to be remembered, and document your legacy goals.',
          priority: 'Low',
          category: 'Estate Planning',
        });
      }
    }
  }

  const uniqueActionItems = Array.from(new Set(actionItems.map((item) => item.title)))
    .map((title) => actionItems.find((item) => item.title === title))
    .filter((item): item is Exclude<typeof item, undefined> => item !== undefined);

  return uniqueActionItems;
};

// Helper function to generate guidance items
export const generateGuidanceItems = (data: FinancialCompassData) => {
  const financialHealthResult: {
    overallScore: number;
    status: string;
    recommendations: { title: string; description: string; category: string; actionSteps?: string[]; icon?: string }[];
    overallDescription?: string;
  } = {
    overallScore: 0,
    status: 'Incomplete',
    recommendations: [],
  };

  try {
    const dummyCategories: any[] = [
      {
        id: 'cash_flow',
        name: 'Cash Flow',
        score: 0,
        weight: 0,
        weightedScore: 0,
        metrics: [],
      },
    ];
    const calculatedResult = calculateDetailedFinancialHealthScore(dummyCategories);
    financialHealthResult.overallScore = calculatedResult.overallScore;
    financialHealthResult.status = calculatedResult.status;
    financialHealthResult.recommendations = calculatedResult.recommendations as any;
    financialHealthResult.overallDescription = (calculatedResult as any).overallDescription;
  } catch (error) {
    console.error('Error calculating detailed financial health score:', error);
  }

  const guidanceItems: { title: string; description: string; icon: string }[] = [];

  guidanceItems.push({
    title: `Your Financial Health is ${financialHealthResult.status}`,
    description: financialHealthResult.overallDescription || 'Review the details below.',
    icon: '📊',
  });

  if (financialHealthResult.recommendations.length > 0) {
    financialHealthResult.recommendations.slice(0, 3).forEach((rec) => {
      guidanceItems.push({
        title: rec.title,
        description: rec.description,
        icon: (rec as any).icon || '💡',
      });
    });
  }

  guidanceItems.push({
    title: 'Key Financial Principles',
    description:
      '• Pay yourself first - automate savings before spending on discretionary items.\n• Use the 50/30/20 rule - 50% needs, 30% wants, 20% savings and debt repayment.\n• Review your financial position quarterly to stay on track.\n• Adjust your strategy as life circumstances change.',
    icon: '📚',
  });

  guidanceItems.push({
    title: 'Next Steps',
    description:
      '1. Schedule a comprehensive financial review every 6 months.\n2. Update your financial data as significant changes occur.\n3. Implement the priority action items in order of importance.\n4. Consider consulting with financial professionals for specialized advice:\n   • Financial Advisor - For investment strategy and comprehensive planning\n   • Tax Professional - For tax optimization strategies\n   • Estate Attorney - For estate planning documents\n   • Insurance Agent - For insurance coverage review',
    icon: '➡️',
  });

  return guidanceItems;
};

export const generateTrends = () => {
  const trends: {
    title: string;
    value: string;
    change: string;
    direction: 'up' | 'down' | 'neutral';
  }[] = [];
  trends.push({ title: 'Net Worth', value: '$X', change: '+Y%', direction: 'up' });
  trends.push({ title: 'Savings Rate', value: 'Z%', change: '+A%', direction: 'up' });
  trends.push({ title: 'Debt', value: '$B', change: '-C%', direction: 'down' });
  return trends;
};

export const generateAlerts = () => {
  const alerts: { title: string; description: string; severity: 'high' | 'medium' | 'low' }[] = [];
  return alerts;
};

export const downloadComprehensivePDF = async (data: FinancialCompassData): Promise<void> => {
  try {
    const pdfBlob = await exportFinancialCompassPDF(data);
    const url = window.URL.createObjectURL(pdfBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'financial-compass-report.pdf';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error downloading PDF:', error);
    throw error;
  }
};

export const generatePDF = async (data: { sections: { title: string; content: string | string[] }[] }): Promise<Blob> => {
  try {
    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();

    doc.setFontSize(16);
    doc.setTextColor(0);
    doc.setFont(undefined, 'bold');
    doc.text('Report Section Summary', pageWidth / 2, 20, { align: 'center' });

    let yPos = 40;
    if (Array.isArray(data.sections)) {
      for (const section of data.sections) {
        yPos = addSection(doc, section.title, section.content, yPos);

        if (yPos > pageHeight - 40) {
          doc.addPage();
          yPos = 20;
        }
      }
    }

    doc.setFontSize(10);
    doc.setTextColor(100);
    doc.text('Generated on: ' + new Date().toLocaleDateString(), pageWidth / 2, pageHeight - 10, {
      align: 'center',
    });

    return doc.output('blob');
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw error;
  }
};
