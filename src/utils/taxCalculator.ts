/**
 * Tax Calculator Utility
 *
 * This utility provides accurate tax calculations using progressive brackets
 * for both federal and state taxes.
 */

/**
 * Filing status types
 */
export type FilingStatus = 'SINGLE' | 'MARRIED_JOINT' | 'HEAD_OF_HOUSEHOLD' | 'MARRIED_SEPARATE';

type TaxYear = 2023 | 2024;

/**
 * Tax bracket interface
 */
interface TaxBracket {
  rate: number;
  upTo: number;
}

/**
 * Federal Tax Brackets by Year
 */
type FederalTaxBrackets = Record<FilingStatus, TaxBracket[]>;

const FEDERAL_TAX_BRACKETS: Record<TaxYear, FederalTaxBrackets> = {
  2023: {
    SINGLE: [
      { rate: 0.1, upTo: 11000 },
      { rate: 0.12, upTo: 44725 },
      { rate: 0.22, upTo: 95375 },
      { rate: 0.24, upTo: 182100 },
      { rate: 0.32, upTo: 231250 },
      { rate: 0.35, upTo: 578125 },
      { rate: 0.37, upTo: Infinity },
    ],
    MARRIED_JOINT: [
      { rate: 0.1, upTo: 22000 },
      { rate: 0.12, upTo: 89450 },
      { rate: 0.22, upTo: 190750 },
      { rate: 0.24, upTo: 364200 },
      { rate: 0.32, upTo: 462500 },
      { rate: 0.35, upTo: 693750 },
      { rate: 0.37, upTo: Infinity },
    ],
    MARRIED_SEPARATE: [
      { rate: 0.1, upTo: 11000 },
      { rate: 0.12, upTo: 44725 },
      { rate: 0.22, upTo: 95375 },
      { rate: 0.24, upTo: 182100 },
      { rate: 0.32, upTo: 231250 },
      { rate: 0.35, upTo: 346875 },
      { rate: 0.37, upTo: Infinity },
    ],
    HEAD_OF_HOUSEHOLD: [
      { rate: 0.1, upTo: 15700 },
      { rate: 0.12, upTo: 59850 },
      { rate: 0.22, upTo: 95350 },
      { rate: 0.24, upTo: 182100 },
      { rate: 0.32, upTo: 231250 },
      { rate: 0.35, upTo: 578100 },
      { rate: 0.37, upTo: Infinity },
    ],
  },
  2024: {
    SINGLE: [
      { rate: 0.1, upTo: 11600 },
      { rate: 0.12, upTo: 47150 },
      { rate: 0.22, upTo: 100525 },
      { rate: 0.24, upTo: 191950 },
      { rate: 0.32, upTo: 243725 },
      { rate: 0.35, upTo: 609350 },
      { rate: 0.37, upTo: Infinity },
    ],
    MARRIED_JOINT: [
      { rate: 0.1, upTo: 23200 },
      { rate: 0.12, upTo: 94300 },
      { rate: 0.22, upTo: 201050 },
      { rate: 0.24, upTo: 383900 },
      { rate: 0.32, upTo: 487450 },
      { rate: 0.35, upTo: 731200 },
      { rate: 0.37, upTo: Infinity },
    ],
    MARRIED_SEPARATE: [
      { rate: 0.1, upTo: 11600 },
      { rate: 0.12, upTo: 47150 },
      { rate: 0.22, upTo: 100525 },
      { rate: 0.24, upTo: 191950 },
      { rate: 0.32, upTo: 243725 },
      { rate: 0.35, upTo: 365600 },
      { rate: 0.37, upTo: Infinity },
    ],
    HEAD_OF_HOUSEHOLD: [
      { rate: 0.1, upTo: 16550 },
      { rate: 0.12, upTo: 63100 },
      { rate: 0.22, upTo: 100500 },
      { rate: 0.24, upTo: 191950 },
      { rate: 0.32, upTo: 243700 },
      { rate: 0.35, upTo: 609350 },
      { rate: 0.37, upTo: Infinity },
    ],
  },
};

/**
 * Standard deductions by year and filing status
 */
type StandardDeductions = Record<FilingStatus, number>;

const STANDARD_DEDUCTIONS: Record<TaxYear, StandardDeductions> = {
  2023: {
    SINGLE: 13850,
    MARRIED_JOINT: 27700,
    MARRIED_SEPARATE: 13850,
    HEAD_OF_HOUSEHOLD: 20800,
  },
  2024: {
    SINGLE: 14600,
    MARRIED_JOINT: 29200,
    MARRIED_SEPARATE: 14600,
    HEAD_OF_HOUSEHOLD: 21900,
  },
};

/**
 * Get standard deduction for a given year and filing status
 */
const getStandardDeduction = (filingStatus: FilingStatus, year: TaxYear = 2024): number => {
  return STANDARD_DEDUCTIONS[year]?.[filingStatus] || 0;
};

/**
 * State tax rates (approximate average rates for estimation purposes)
 */
export const STATE_TAX_RATES: Record<string, number> = {
  AL: 0.05,
  AK: 0.0,
  AZ: 0.0459,
  AR: 0.055,
  CA: 0.093,
  CO: 0.0455,
  CT: 0.0699,
  DE: 0.066,
  FL: 0.0,
  GA: 0.0575,
  HI: 0.11,
  ID: 0.06,
  IL: 0.0495,
  IN: 0.0323,
  IA: 0.0625,
  KS: 0.057,
  KY: 0.05,
  LA: 0.0425,
  ME: 0.0715,
  MD: 0.0575,
  MA: 0.05,
  MI: 0.0425,
  MN: 0.0985,
  MS: 0.05,
  MO: 0.054,
  MT: 0.0675,
  NE: 0.0684,
  NV: 0.0,
  NH: 0.05,
  NJ: 0.1075,
  NM: 0.059,
  NY: 0.1023,
  NC: 0.0499,
  ND: 0.029,
  OH: 0.0399,
  OK: 0.0475,
  OR: 0.099,
  PA: 0.0307,
  RI: 0.0599,
  SC: 0.07,
  SD: 0.0,
  TN: 0.0,
  TX: 0.0,
  UT: 0.0495,
  VT: 0.0875,
  VA: 0.0575,
  WA: 0.0,
  WV: 0.065,
  WI: 0.0765,
  WY: 0.0,
  DC: 0.0895,
};

/**
 * Default state tax rate (used when state is not specified)
 */
export const DEFAULT_STATE_TAX_RATE = 0.05; // 5%

/**
 * Calculate federal income tax using progressive brackets
 *
 * @param taxableIncome - Taxable income amount
 * @param filingStatus - Filing status (SINGLE, MARRIED_JOINT, HEAD_OF_HOUSEHOLD)
 * @returns Federal income tax amount
 */
export const calculateFederalIncomeTax = (
  taxableIncome: number,
  filingStatus: FilingStatus = 'SINGLE',
  year: TaxYear = 2023
): number => {
  if (isNaN(taxableIncome) || taxableIncome <= 0) return 0;

  const brackets = FEDERAL_TAX_BRACKETS[year][filingStatus];
  let tax = 0;
  let remainingIncome = taxableIncome;
  let previousBracketLimit = 0;

  for (const bracket of brackets) {
    const bracketIncome = Math.min(remainingIncome, bracket.upTo - previousBracketLimit);
    tax += bracketIncome * bracket.rate;
    remainingIncome -= bracketIncome;
    previousBracketLimit = bracket.upTo;

    if (remainingIncome <= 0) break;
  }

  return tax;
};

/**
 * Calculate taxable income after standard deduction
 *
 * @param grossIncome - Gross income amount
 * @param filingStatus - Filing status
 * @param additionalDeductions - Additional deductions beyond standard deduction
 * @returns Taxable income amount
 */
export const calculateTaxableIncome = (
  grossIncome: number,
  filingStatus: FilingStatus = 'SINGLE',
  additionalDeductions: number = 0
): number => {
  if (isNaN(grossIncome) || grossIncome <= 0) return 0;
  if (isNaN(additionalDeductions)) additionalDeductions = 0;

  const year: TaxYear = 2023; // Default to 2023 for backward compatibility
  const standardDeduction = getStandardDeduction(filingStatus, year);
  return Math.max(0, grossIncome - standardDeduction - additionalDeductions);
};

/**
 * Calculate state income tax
 *
 * @param taxableIncome - Taxable income amount
 * @param stateCode - State code (e.g., 'CA', 'NY')
 * @returns State income tax amount
 */
export const calculateStateTax = (taxableIncome: number, stateCode?: string): number => {
  if (isNaN(taxableIncome) || taxableIncome <= 0) return 0;

  // Get state tax rate or use default
  const stateRate =
    stateCode && STATE_TAX_RATES[stateCode.toUpperCase()]
      ? STATE_TAX_RATES[stateCode.toUpperCase()]
      : DEFAULT_STATE_TAX_RATE;

  return taxableIncome * stateRate;
};

/**
 * Calculate FICA taxes (Social Security and Medicare)
 *
 * @param income - Gross income amount
 * @returns FICA taxes object with Social Security and Medicare tax amounts
 */
export const calculateFICATaxes = (
  income: number
): { socialSecurity: number; medicare: number; total: number } => {
  if (isNaN(income) || income <= 0) return { socialSecurity: 0, medicare: 0, total: 0 };

  // 2023 Social Security wage base limit
  const socialSecurityWageBase = 160200;

  // Calculate Social Security tax (6.2% up to wage base)
  const socialSecurityTax = Math.min(income, socialSecurityWageBase) * 0.062;

  // Calculate Medicare tax (1.45% on all earnings)
  const medicareTax = income * 0.0145;

  // Additional Medicare tax (0.9%) on income over $200,000 for single filers
  const additionalMedicareTax = income > 200000 ? (income - 200000) * 0.009 : 0;

  const totalMedicareTax = medicareTax + additionalMedicareTax;
  const totalFICATax = socialSecurityTax + totalMedicareTax;

  return {
    socialSecurity: socialSecurityTax,
    medicare: totalMedicareTax,
    total: totalFICATax,
  };
};

/**
 * Calculate detailed tax liability
 *
 * @param income - Gross income amount
 * @param filingStatus - Filing status
 * @param options - Additional options for tax calculation
 * @returns Detailed tax calculation results
 */
export const calculateDetailedTaxLiability = (
  income: number,
  filingStatus: 'single' | 'married' | 'headOfHousehold',
  options: {
    stateCode?: string;
    dependents?: number;
    additionalDeductions?: number;
    year?: number;
  } = {}
): {
  grossIncome: number;
  taxableIncome: number;
  federalTax: number;
  stateTax: number;
  ficaTax: number;
  totalTax: number;
  afterTaxIncome: number;
  effectiveTaxRate: number;
  marginalTaxRate: number;
} => {
  if (isNaN(income) || income <= 0) {
    return {
      grossIncome: 0,
      taxableIncome: 0,
      federalTax: 0,
      stateTax: 0,
      ficaTax: 0,
      totalTax: 0,
      afterTaxIncome: 0,
      effectiveTaxRate: 0,
      marginalTaxRate: 0,
    };
  }

  // Convert filing status to our internal format
  const internalFilingStatus: FilingStatus =
    filingStatus === 'married'
      ? 'MARRIED_JOINT'
      : filingStatus === 'headOfHousehold'
        ? 'HEAD_OF_HOUSEHOLD'
        : 'SINGLE';

  // Calculate additional deductions based on dependents
  const dependentDeduction = (options.dependents || 0) * 2000; // Approximate child tax credit as deduction
  const additionalDeductions = (options.additionalDeductions || 0) + dependentDeduction;

  // Calculate taxable income
  const taxableIncome = calculateTaxableIncome(income, internalFilingStatus, additionalDeductions);

  // Calculate federal income tax
  const federalTax = calculateFederalIncomeTax(taxableIncome, internalFilingStatus);

  // Calculate state tax
  const stateTax = calculateStateTax(taxableIncome, options.stateCode);

  // Calculate FICA taxes
  const ficaTaxes = calculateFICATaxes(income);

  // Calculate total tax
  const totalTax = federalTax + stateTax + ficaTaxes.total;

  // Calculate after-tax income
  const afterTaxIncome = income - totalTax;

  // Calculate effective tax rate
  const effectiveTaxRate = income > 0 ? (totalTax / income) * 100 : 0;

  // Calculate marginal tax rate (federal only)
  const marginalTaxRate = getMarginalTaxRate(taxableIncome, internalFilingStatus) * 100;

  return {
    grossIncome: income,
    taxableIncome,
    federalTax,
    stateTax,
    ficaTax: ficaTaxes.total,
    totalTax,
    afterTaxIncome,
    effectiveTaxRate,
    marginalTaxRate,
  };
};

/**
 * Get marginal tax rate based on taxable income and filing status
 *
 * @param taxableIncome - Taxable income amount
 * @param filingStatus - Filing status
 * @returns Marginal tax rate as a decimal
 */
export const getMarginalTaxRate = (
  taxableIncome: number,
  filingStatus: FilingStatus = 'SINGLE',
  year: TaxYear = 2023
): number => {
  if (isNaN(taxableIncome) || taxableIncome <= 0) return 0;

  const brackets = FEDERAL_TAX_BRACKETS[year][filingStatus];
  let previousBracketLimit = 0;

  for (const bracket of brackets) {
    if (taxableIncome <= bracket.upTo) {
      return bracket.rate;
    }
    previousBracketLimit = bracket.upTo;
  }

  // If we get here, use the highest bracket rate
  return brackets[brackets.length - 1].rate;
};
