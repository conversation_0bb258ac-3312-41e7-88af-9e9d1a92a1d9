import { z } from 'zod';
import {
  ValidationResult,
  validateFinancialCompassData,
  financialCompassSchema,
  FinancialCompassData,
  mergeWithExisting,
} from '../schemas';

/**
 * Validates form data against a Zod schema
 * @param schema The Zod schema to validate against
 * @param data The data to validate
 * @returns A validation result with either the validated data or error messages
 */
export function validateFormData<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): { success: boolean; data?: T; errors?: Record<string, string> } {
  const result = schema.safeParse(data);

  if (result.success) {
    return { success: true, data: result.data };
  }

  // Format Zod errors into a more user-friendly format
  const errors: Record<string, string> = {};

  result.error.errors.forEach((error) => {
    const path = error.path.join('.');
    errors[path] = error.message;
  });

  return { success: false, errors };
}

/**
 * Validates financial compass data
 * @param data The financial compass data to validate
 * @returns A validation result with either the validated data or error messages
 */
export function validateCompassData(
  data: unknown
): ReturnType<typeof validateFinancialCompassData> {
  return validateFinancialCompassData(data);
}

/**
 * Creates a form validator for a specific schema
 * @param schema The Zod schema to validate against
 * @returns A function that validates data against the schema
 */
export function createValidator<T>(schema: z.ZodSchema<T>) {
  return (data: unknown) => validateFormData(schema, data);
}

/**
 * Creates an async form validator for a specific schema
 * @param schema The Zod schema to validate against
 * @returns An async function that validates data against the schema
 */
export function createAsyncValidator<T>(schema: z.ZodSchema<T>) {
  return async (data: unknown) => validateFormData(schema, data);
}

/**
 * Merges partial updates with existing data
 * @param existing The existing data
 * @param updates The updates to apply
 * @returns The merged data
 */
export function mergeData<T extends object>(existing: T, updates: Partial<T>): T {
  return mergeWithExisting(existing, updates);
}

/**
 * Creates a type-safe form data object
 * @param schema The Zod schema to validate against
 * @param data The initial form data (optional)
 * @returns A form data object with validation methods
 */
export function createFormData<T>(schema: z.ZodSchema<T>, initialData: Partial<T> = {}) {
  let data: Partial<T> = { ...initialData };

  return {
    /**
     * Updates the form data
     * @param updates The updates to apply
     */
    update(updates: Partial<T>) {
      data = { ...data, ...updates };
    },

    /**
     * Validates the current form data
     * @returns A validation result
     */
    validate() {
      return validateFormData(schema, data);
    },

    /**
     * Gets the current form data
     */
    getData() {
      return data;
    },

    /**
     * Resets the form data to the initial state
     */
    reset() {
      data = { ...initialData };
    },

    /**
     * Sets the form data
     * @param newData The new form data
     */
    setData(newData: Partial<T>) {
      data = { ...newData };
    },
  };
}

// Re-export commonly used Zod types and utilities
export { z } from 'zod';
export type { ValidationResult, FinancialCompassData } from '../schemas';
