/**
 * Retirement Expense Calculator Utility
 *
 * This utility provides functions for projecting retirement expenses with inflation
 * and calculating present values of future expenses.
 */

type ExpenseCategory = {
  name: string;
  currentMonthly: number;
  inflationRate: number; // annual rate as decimal (e.g., 0.03 for 3%)
  isEssential: boolean;
};

type ExpenseProjection = {
  year: number;
  amount: number;
  presentValue: number;
}[];

/**
 * Project future expenses with inflation
 * @param currentMonthly - Current monthly expense amount
 * @param years - Number of years to project
 * @param inflationRate - Annual inflation rate as decimal (e.g., 0.03 for 3%)
 * @param discountRate - Annual discount rate for present value calculation (default: 0.03)
 * @returns Array of projected expenses by year with present values
 */
export const projectExpenses = (
  currentMonthly: number,
  years: number,
  inflationRate: number,
  discountRate: number = 0.03
): ExpenseProjection => {
  const result: ExpenseProjection = [];
  let annualAmount = currentMonthly * 12;

  for (let year = 1; year <= years; year++) {
    const inflatedAmount = annualAmount * Math.pow(1 + inflationRate, year - 1);
    const presentValue = inflatedAmount / Math.pow(1 + discountRate, year - 1);

    result.push({
      year,
      amount: parseFloat(inflatedAmount.toFixed(2)),
      presentValue: parseFloat(presentValue.toFixed(2)),
    });
  }

  return result;
};

/**
 * Calculate total retirement expenses across multiple categories
 * @param categories - Array of expense categories
 * @param years - Number of years to project
 * @param discountRate - Annual discount rate for present value calculation (default: 0.03)
 * @returns Object containing total projected expenses and breakdown by category
 */
export const calculateRetirementExpenses = (
  categories: ExpenseCategory[],
  years: number,
  discountRate: number = 0.03
) => {
  const categoryProjections = categories.map((category) => ({
    ...category,
    projections: projectExpenses(
      category.currentMonthly,
      years,
      category.inflationRate,
      discountRate
    ),
  }));

  // Calculate total for each year across all categories
  const totalProjections = Array(years)
    .fill(0)
    .map((_, index) => {
      const year = index + 1;
      const total = categoryProjections.reduce((sum, category) => {
        const yearData = category.projections.find((p) => p.year === year);
        return sum + (yearData?.amount || 0);
      }, 0);

      const totalPV = categoryProjections.reduce((sum, category) => {
        const yearData = category.projections.find((p) => p.year === year);
        return sum + (yearData?.presentValue || 0);
      }, 0);

      return {
        year,
        amount: parseFloat(total.toFixed(2)),
        presentValue: parseFloat(totalPV.toFixed(2)),
      };
    });

  return {
    totalProjections,
    categoryProjections,
    totalPresentValue: parseFloat(
      totalProjections.reduce((sum, year) => sum + year.presentValue, 0).toFixed(2)
    ),
  };
};

/**
 * Calculate the present value of a future expense
 * @param futureValue - Future expense amount
 * @param years - Number of years in the future
 * @param discountRate - Annual discount rate (default: 0.03)
 * @returns Present value of the future expense
 */
export const calculatePresentValue = (
  futureValue: number,
  years: number,
  discountRate: number = 0.03
): number => {
  return parseFloat((futureValue / Math.pow(1 + discountRate, years)).toFixed(2));
};
