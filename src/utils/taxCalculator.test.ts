/**
 * Tax Calculator Utility Tests
 */

import {
  calculateFederalIncomeTax,
  calculateTaxableIncome,
  calculateStateTax,
  calculateFICATaxes,
  calculateDetailedTaxLiability,
  getMarginalTaxRate,
  FEDERAL_TAX_BRACKETS_2023,
  STANDARD_DEDUCTION_2023,
} from './taxCalculator';

describe('Tax Calculator Utility', () => {
  describe('calculateFederalIncomeTax', () => {
    it('calculates federal income tax correctly for single filer', () => {
      // Test case: $50,000 taxable income, single filer
      // First $11,000 taxed at 10% = $1,100
      // Next $33,725 ($44,725 - $11,000) taxed at 12% = $4,047
      // Remaining $5,275 ($50,000 - $44,725) taxed at 22% = $1,160.50
      // Total tax = $1,100 + $4,047 + $1,160.50 = $6,307.50
      const result = calculateFederalIncomeTax(50000, 'SINGLE');
      expect(result).toBeCloseTo(6307.5, 2);
    });

    it('calculates federal income tax correctly for married filing jointly', () => {
      // Test case: $100,000 taxable income, married filing jointly
      // First $22,000 taxed at 10% = $2,200
      // Next $67,450 ($89,450 - $22,000) taxed at 12% = $8,094
      // Remaining $10,550 ($100,000 - $89,450) taxed at 22% = $2,321
      // Total tax = $2,200 + $8,094 + $2,321 = $12,615
      const result = calculateFederalIncomeTax(100000, 'MARRIED_JOINT');
      expect(result).toBeCloseTo(12615, 2);
    });

    it('calculates federal income tax correctly for head of household', () => {
      // Test case: $75,000 taxable income, head of household
      // First $15,700 taxed at 10% = $1,570
      // Next $44,150 ($59,850 - $15,700) taxed at 12% = $5,298
      // Remaining $15,150 ($75,000 - $59,850) taxed at 22% = $3,333
      // Total tax = $1,570 + $5,298 + $3,333 = $10,201
      const result = calculateFederalIncomeTax(75000, 'HEAD_OF_HOUSEHOLD');
      expect(result).toBeCloseTo(10201, 2);
    });

    it('handles zero income correctly', () => {
      expect(calculateFederalIncomeTax(0, 'SINGLE')).toBe(0);
    });

    it('handles negative income correctly', () => {
      expect(calculateFederalIncomeTax(-1000, 'SINGLE')).toBe(0);
    });

    it('handles NaN input correctly', () => {
      expect(calculateFederalIncomeTax(NaN, 'SINGLE')).toBe(0);
    });

    it('calculates federal income tax correctly at federal tax bracket boundaries (Single)', () => {
      expect(
        calculateFederalIncomeTax(FEDERAL_TAX_BRACKETS_2023.SINGLE[0].threshold, 'SINGLE')
      ).toBeCloseTo(
        FEDERAL_TAX_BRACKETS_2023.SINGLE[0].threshold * FEDERAL_TAX_BRACKETS_2023.SINGLE[0].rate,
        2
      ); // At 10% boundary
      expect(
        calculateFederalIncomeTax(FEDERAL_TAX_BRACKETS_2023.SINGLE[1].threshold - 1, 'SINGLE')
      ).toBeCloseTo(11000 * 0.1 + (44724 - 11000) * 0.12, 2); // Just below 22% bracket
      expect(
        calculateFederalIncomeTax(FEDERAL_TAX_BRACKETS_2023.SINGLE[1].threshold, 'SINGLE')
      ).toBeCloseTo(11000 * 0.1 + (44725 - 11000) * 0.12, 2); // At 22% boundary
    });

    it('calculates federal income tax correctly at federal tax bracket boundaries (Married Filing Jointly)', () => {
      expect(
        calculateFederalIncomeTax(
          FEDERAL_TAX_BRACKETS_2023.MARRIED_JOINT[0].threshold,
          'MARRIED_JOINT'
        )
      ).toBeCloseTo(
        FEDERAL_TAX_BRACKETS_2023.MARRIED_JOINT[0].threshold *
          FEDERAL_TAX_BRACKETS_2023.MARRIED_JOINT[0].rate,
        2
      ); // At 10% boundary
      expect(
        calculateFederalIncomeTax(
          FEDERAL_TAX_BRACKETS_2023.MARRIED_JOINT[1].threshold - 1,
          'MARRIED_JOINT'
        )
      ).toBeCloseTo(22000 * 0.1 + (89449 - 22000) * 0.12, 2); // Just below 22% bracket
      expect(
        calculateFederalIncomeTax(
          FEDERAL_TAX_BRACKETS_2023.MARRIED_JOINT[1].threshold,
          'MARRIED_JOINT'
        )
      ).toBeCloseTo(22000 * 0.1 + (89450 - 22000) * 0.12, 2); // At 22% boundary
    });

    it('calculates federal income tax correctly at federal tax bracket boundaries (Head of Household)', () => {
      expect(
        calculateFederalIncomeTax(
          FEDERAL_TAX_BRACKETS_2023.HEAD_OF_HOUSEHOLD[0].threshold,
          'HEAD_OF_HOUSEHOLD'
        )
      ).toBeCloseTo(
        FEDERAL_TAX_BRACKETS_2023.HEAD_OF_HOUSEHOLD[0].threshold *
          FEDERAL_TAX_BRACKETS_2023.HEAD_OF_HOUSEHOLD[0].rate,
        2
      ); // At 10% boundary
      expect(
        calculateFederalIncomeTax(
          FEDERAL_TAX_BRACKETS_2023.HEAD_OF_HOUSEHOLD[1].threshold - 1,
          'HEAD_OF_HOUSEHOLD'
        )
      ).toBeCloseTo(15700 * 0.1 + (59849 - 15700) * 0.12, 2); // Just below 22% bracket
      expect(
        calculateFederalIncomeTax(
          FEDERAL_TAX_BRACKETS_2023.HEAD_OF_HOUSEHOLD[1].threshold,
          'HEAD_OF_HOUSEHOLD'
        )
      ).toBeCloseTo(15700 * 0.1 + (59850 - 15700) * 0.12, 2); // At 22% boundary
    });
  });

  describe('calculateTaxableIncome', () => {
    it('calculates taxable income correctly with standard deduction', () => {
      // Test case: $75,000 gross income, single filer
      // Standard deduction for single filer: $13,850
      // Taxable income = $75,000 - $13,850 = $61,150
      const result = calculateTaxableIncome(75000, 'SINGLE');
      expect(result).toBe(75000 - STANDARD_DEDUCTION_2023.SINGLE);
    });

    it('calculates taxable income correctly with additional deductions', () => {
      // Test case: $100,000 gross income, married filing jointly, $10,000 additional deductions
      // Standard deduction for married filing jointly: $27,700
      // Taxable income = $100,000 - $27,700 - $10,000 = $62,300
      const result = calculateTaxableIncome(100000, 'MARRIED_JOINT', 10000);
      expect(result).toBe(100000 - STANDARD_DEDUCTION_2023.MARRIED_JOINT - 10000);
    });

    it('handles income less than standard deduction correctly', () => {
      // Test case: $10,000 gross income, single filer
      // Standard deduction for single filer: $13,850
      // Taxable income = $0 (since income is less than standard deduction)
      const result = calculateTaxableIncome(10000, 'SINGLE');
      expect(result).toBe(0);
    });

    it('handles zero income correctly', () => {
      expect(calculateTaxableIncome(0, 'SINGLE')).toBe(0);
    });

    it('handles negative income correctly', () => {
      expect(calculateTaxableIncome(-1000, 'SINGLE')).toBe(0);
    });

    it('handles NaN input correctly', () => {
      expect(calculateTaxableIncome(NaN, 'SINGLE')).toBe(0);
    });

    it('calculates taxable income correctly with zero additional deductions', () => {
      const result = calculateTaxableIncome(75000, 'SINGLE', 0);
      expect(result).toBe(75000 - STANDARD_DEDUCTION_2023.SINGLE);
    });
  });

  describe('calculateStateTax', () => {
    it('calculates state tax correctly for specified state', () => {
      // Test case: $50,000 taxable income, California (9.3% tax rate)
      const result = calculateStateTax(50000, 'CA');
      expect(result).toBeCloseTo(50000 * 0.093, 2);
    });

    it('uses default state tax rate when state is not specified', () => {
      // Test case: $50,000 taxable income, no state specified (5% default rate)
      const result = calculateStateTax(50000);
      expect(result).toBeCloseTo(50000 * 0.05, 2);
    });

    it('handles zero income correctly', () => {
      expect(calculateStateTax(0, 'NY')).toBe(0);
    });

    it('handles negative income correctly', () => {
      expect(calculateStateTax(-1000, 'NY')).toBe(0);
    });

    it('handles NaN input correctly', () => {
      expect(calculateStateTax(NaN, 'NY')).toBe(0);
    });

    // Note: State tax rates are simplified for this utility. Add more specific tests if needed.
    it('handles a state code not in the predefined list gracefully', () => {
      const result = calculateStateTax(50000, 'UNKNOWN');
      expect(result).toBeCloseTo(50000 * 0.05, 2); // Should use the default rate
    });
  });

  describe('calculateFICATaxes', () => {
    it('calculates FICA taxes correctly for income below Social Security wage base', () => {
      // Test case: $75,000 income
      // Social Security tax: 6.2% of $75,000 = $4,650
      // Medicare tax: 1.45% of $75,000 = $1,087.50
      // Total FICA tax: $4,650 + $1,087.50 = $5,737.50
      const result = calculateFICATaxes(75000);
      expect(result.socialSecurity).toBeCloseTo(75000 * 0.062, 2);
      expect(result.medicare).toBeCloseTo(75000 * 0.0145, 2);
      expect(result.total).toBeCloseTo(75000 * 0.062 + 75000 * 0.0145, 2);
    });

    it('calculates FICA taxes correctly for income above Social Security wage base', () => {
      // Test case: $200,000 income
      // Social Security tax: 6.2% of $160,200 (wage base) = $9,932.40
      // Medicare tax: 1.45% of $200,000 = $2,900
      // Additional Medicare tax: 0.9% of ($200,000 - $200,000) = $0
      // Total FICA tax: $9,932.40 + $2,900 = $12,832.40
      const result = calculateFICATaxes(200000);
      expect(result.socialSecurity).toBeCloseTo(160200 * 0.062, 2);
      expect(result.medicare).toBeCloseTo(200000 * 0.0145, 2);
      expect(result.total).toBeCloseTo(160200 * 0.062 + 200000 * 0.0145, 2);
    });

    it('calculates additional Medicare tax correctly for high income', () => {
      // Test case: $250,000 income
      // Social Security tax: 6.2% of $160,200 (wage base) = $9,932.40
      // Medicare tax: 1.45% of $250,000 = $3,625
      // Additional Medicare tax: 0.9% of ($250,000 - $200,000) = $450
      // Total Medicare tax: $3,625 + $450 = $4,075
      // Total FICA tax: $9,932.40 + $4,075 = $14,007.40
      const result = calculateFICATaxes(250000);
      expect(result.socialSecurity).toBeCloseTo(160200 * 0.062, 2);
      expect(result.medicare).toBeCloseTo(250000 * 0.0145 + 50000 * 0.009, 2);
      expect(result.total).toBeCloseTo(160200 * 0.062 + 250000 * 0.0145 + 50000 * 0.009, 2);
    });

    it('handles zero income correctly', () => {
      const result = calculateFICATaxes(0);
      expect(result.socialSecurity).toBe(0);
      expect(result.medicare).toBe(0);
      expect(result.total).toBe(0);
    });

    it('handles negative income correctly', () => {
      const result = calculateFICATaxes(-1000);
      expect(result.socialSecurity).toBe(0);
      expect(result.medicare).toBe(0);
      expect(result.total).toBe(0);
    });

    it('handles NaN input correctly', () => {
      const result = calculateFICATaxes(NaN);
      expect(result.socialSecurity).toBe(0);
      expect(result.medicare).toBe(0);
      expect(result.total).toBe(0);
    });

    it('calculates FICA taxes correctly exactly at the Social Security wage base', () => {
      const wageBase = 160200; // 2023 wage base
      const result = calculateFICATaxes(wageBase);
      expect(result.socialSecurity).toBeCloseTo(wageBase * 0.062, 2);
      expect(result.medicare).toBeCloseTo(wageBase * 0.0145, 2);
      expect(result.total).toBeCloseTo(wageBase * 0.062 + wageBase * 0.0145, 2);
    });

    it('calculates additional Medicare tax correctly exactly at the threshold', () => {
      const income = 200000; // Additional Medicare tax threshold
      const result = calculateFICATaxes(income);
      expect(result.socialSecurity).toBeCloseTo(160200 * 0.062, 2);
      expect(result.medicare).toBeCloseTo(income * 0.0145, 2); // No additional tax yet
      expect(result.total).toBeCloseTo(160200 * 0.062 + income * 0.0145, 2);
    });

    it('calculates additional Medicare tax correctly just above the threshold', () => {
      const income = 200001; // Just above the threshold
      const result = calculateFICATaxes(income);
      expect(result.socialSecurity).toBeCloseTo(160200 * 0.062, 2);
      expect(result.medicare).toBeCloseTo(200000 * 0.0145 + (200001 - 200000) * 0.009, 2);
      expect(result.total).toBeCloseTo(
        160200 * 0.062 + 200000 * 0.0145 + (200001 - 200000) * 0.009,
        2
      );
    });
  });

  describe('calculateDetailedTaxLiability', () => {
    it('calculates detailed tax liability correctly for single filer', () => {
      // Test case: $75,000 gross income, single filer
      const result = calculateDetailedTaxLiability(75000, 'single');

      // Verify gross income
      expect(result.grossIncome).toBe(75000);

      // Verify taxable income (gross income - standard deduction)
      expect(result.taxableIncome).toBeCloseTo(75000 - STANDARD_DEDUCTION_2023.SINGLE, 2);

      // Verify federal tax
      const expectedTaxableIncome = 75000 - STANDARD_DEDUCTION_2023.SINGLE;
      const expectedFederalTax = calculateFederalIncomeTax(expectedTaxableIncome, 'SINGLE');
      expect(result.federalTax).toBeCloseTo(expectedFederalTax, 2);

      // Verify state tax
      const expectedStateTax = calculateStateTax(expectedTaxableIncome);
      expect(result.stateTax).toBeCloseTo(expectedStateTax, 2);

      // Verify FICA tax
      const expectedFicaTax = calculateFICATaxes(75000).total;
      expect(result.ficaTax).toBeCloseTo(expectedFicaTax, 2);

      // Verify total tax
      const expectedTotalTax = expectedFederalTax + expectedStateTax + expectedFicaTax;
      expect(result.totalTax).toBeCloseTo(expectedTotalTax, 2);

      // Verify after-tax income
      expect(result.afterTaxIncome).toBeCloseTo(75000 - expectedTotalTax, 2);

      // Verify effective tax rate
      expect(result.effectiveTaxRate).toBeCloseTo((expectedTotalTax / 75000) * 100, 2);

      // Verify marginal tax rate
      const expectedMarginalRate = getMarginalTaxRate(expectedTaxableIncome, 'SINGLE') * 100;
      expect(result.marginalTaxRate).toBeCloseTo(expectedMarginalRate, 2);
    });

    it('handles zero income correctly', () => {
      const result = calculateDetailedTaxLiability(0, 'single');
      expect(result.grossIncome).toBe(0);
      expect(result.taxableIncome).toBe(0);
      expect(result.federalTax).toBe(0);
      expect(result.stateTax).toBe(0);
      expect(result.ficaTax).toBe(0);
      expect(result.totalTax).toBe(0);
      expect(result.afterTaxIncome).toBe(0);
      expect(result.effectiveTaxRate).toBe(0);
      expect(result.marginalTaxRate).toBe(0);
    });

    it('handles negative income correctly', () => {
      const result = calculateDetailedTaxLiability(-1000, 'single');
      expect(result.grossIncome).toBe(0);
      expect(result.taxableIncome).toBe(0);
      expect(result.federalTax).toBe(0);
      expect(result.stateTax).toBe(0);
      expect(result.ficaTax).toBe(0);
      expect(result.totalTax).toBe(0);
      expect(result.afterTaxIncome).toBe(0);
      expect(result.effectiveTaxRate).toBe(0);
      expect(result.marginalTaxRate).toBe(0);
    });

    it('handles NaN input correctly', () => {
      const result = calculateDetailedTaxLiability(NaN, 'single');
      expect(result.grossIncome).toBe(0);
      expect(result.taxableIncome).toBe(0);
      expect(result.federalTax).toBe(0);
      expect(result.stateTax).toBe(0);
      expect(result.ficaTax).toBe(0);
      expect(result.totalTax).toBe(0);
      expect(result.afterTaxIncome).toBe(0);
      expect(result.effectiveTaxRate).toBe(0);
      expect(result.marginalTaxRate).toBe(0);
    });

    it('calculates detailed tax liability correctly for married filing jointly', () => {
      // Test case: $150,000 gross income, married filing jointly, $5,000 additional deductions
      const result = calculateDetailedTaxLiability(150000, 'married_joint', 5000);

      // Verify gross income
      expect(result.grossIncome).toBe(150000);

      // Verify taxable income (gross income - standard deduction - additional deductions)
      const expectedTaxableIncome = 150000 - STANDARD_DEDUCTION_2023.MARRIED_JOINT - 5000;
      expect(result.taxableIncome).toBeCloseTo(expectedTaxableIncome, 2);

      // Verify federal tax
      const expectedFederalTax = calculateFederalIncomeTax(expectedTaxableIncome, 'MARRIED_JOINT');
      expect(result.federalTax).toBeCloseTo(expectedFederalTax, 2);

      // Verify state tax (uses default rate since no state specified)
      const expectedStateTax = calculateStateTax(expectedTaxableIncome);
      expect(result.stateTax).toBeCloseTo(expectedStateTax, 2);

      // Verify FICA tax
      const expectedFicaTax = calculateFICATaxes(150000).total;
      expect(result.ficaTax).toBeCloseTo(expectedFicaTax, 2);

      // Verify total tax
      const expectedTotalTax = expectedFederalTax + expectedStateTax + expectedFicaTax;
      expect(result.totalTax).toBeCloseTo(expectedTotalTax, 2);

      // Verify after-tax income
      expect(result.afterTaxIncome).toBeCloseTo(150000 - expectedTotalTax, 2);

      // Verify effective tax rate
      expect(result.effectiveTaxRate).toBeCloseTo((expectedTotalTax / 150000) * 100, 2);

      // Verify marginal tax rate
      const expectedMarginalRate = getMarginalTaxRate(expectedTaxableIncome, 'MARRIED_JOINT') * 100;
      expect(result.marginalTaxRate).toBeCloseTo(expectedMarginalRate, 2);
    });

    it('calculates detailed tax liability correctly for head of household', () => {
      // Test case: $100,000 gross income, head of household
      const result = calculateDetailedTaxLiability(100000, 'head_of_household');

      // Verify gross income
      expect(result.grossIncome).toBe(100000);

      // Verify taxable income (gross income - standard deduction)
      const expectedTaxableIncome = 100000 - STANDARD_DEDUCTION_2023.HEAD_OF_HOUSEHOLD;
      expect(result.taxableIncome).toBeCloseTo(expectedTaxableIncome, 2);

      // Verify federal tax
      const expectedFederalTax = calculateFederalIncomeTax(
        expectedTaxableIncome,
        'HEAD_OF_HOUSEHOLD'
      );
      expect(result.federalTax).toBeCloseTo(expectedFederalTax, 2);

      // Verify state tax (uses default rate since no state specified)
      const expectedStateTax = calculateStateTax(expectedTaxableIncome);
      expect(result.stateTax).toBeCloseTo(expectedStateTax, 2);

      // Verify FICA tax
      const expectedFicaTax = calculateFICATaxes(100000).total;
      expect(result.ficaTax).toBeCloseTo(expectedFicaTax, 2);

      // Verify total tax
      const expectedTotalTax = expectedFederalTax + expectedStateTax + expectedFicaTax;
      expect(result.totalTax).toBeCloseTo(expectedTotalTax, 2);

      // Verify after-tax income
      expect(result.afterTaxIncome).toBeCloseTo(100000 - expectedTotalTax, 2);

      // Verify effective tax rate
      expect(result.effectiveTaxRate).toBeCloseTo((expectedTotalTax / 100000) * 100, 2);

      // Verify marginal tax rate
      const expectedMarginalRate =
        getMarginalTaxRate(expectedTaxableIncome, 'HEAD_OF_HOUSEHOLD') * 100;
      expect(result.marginalTaxRate).toBeCloseTo(expectedMarginalRate, 2);
    });

    it('calculates detailed tax liability correctly with additional deductions and different filing status', () => {
      // Test case: $200,000 gross income, single filer, $20,000 additional deductions
      const result = calculateDetailedTaxLiability(200000, 'single', 20000);

      // Verify gross income
      expect(result.grossIncome).toBe(200000);

      // Verify taxable income
      const expectedTaxableIncome = 200000 - STANDARD_DEDUCTION_2023.SINGLE - 20000;
      expect(result.taxableIncome).toBeCloseTo(expectedTaxableIncome, 2);

      // Verify federal tax
      const expectedFederalTax = calculateFederalIncomeTax(expectedTaxableIncome, 'SINGLE');
      expect(result.federalTax).toBeCloseTo(expectedFederalTax, 2);

      // Verify state tax
      const expectedStateTax = calculateStateTax(expectedTaxableIncome);
      expect(result.stateTax).toBeCloseTo(expectedStateTax, 2);

      // Verify FICA tax
      const expectedFicaTax = calculateFICATaxes(200000).total;
      expect(result.ficaTax).toBeCloseTo(expectedFicaTax, 2);

      // Verify total tax
      const expectedTotalTax = expectedFederalTax + expectedStateTax + expectedFicaTax;
      expect(result.totalTax).toBeCloseTo(expectedTotalTax, 2);

      // Verify after-tax income
      expect(result.afterTaxIncome).toBeCloseTo(200000 - expectedTotalTax, 2);

      // Verify effective tax rate
      expect(result.effectiveTaxRate).toBeCloseTo((expectedTotalTax / 200000) * 100, 2);

      // Verify marginal tax rate
      const expectedMarginalRate = getMarginalTaxRate(expectedTaxableIncome, 'SINGLE') * 100;
      expect(result.marginalTaxRate).toBeCloseTo(expectedMarginalRate, 2);
    });
  });

  describe('getMarginalTaxRate', () => {
    it('returns correct marginal tax rate for different income levels', () => {
      // Test case: Single filer
      expect(getMarginalTaxRate(10000, 'SINGLE')).toBe(0.1); // 10% bracket
      expect(getMarginalTaxRate(30000, 'SINGLE')).toBe(0.12); // 12% bracket
      expect(getMarginalTaxRate(80000, 'SINGLE')).toBe(0.22); // 22% bracket
      expect(getMarginalTaxRate(200000, 'SINGLE')).toBe(0.32); // 32% bracket
      expect(getMarginalTaxRate(500000, 'SINGLE')).toBe(0.35); // 35% bracket
      expect(getMarginalTaxRate(600000, 'SINGLE')).toBe(0.37); // 37% bracket
    });

    it('handles zero income correctly', () => {
      expect(getMarginalTaxRate(0, 'SINGLE')).toBe(0);
    });

    it('handles negative income correctly', () => {
      expect(getMarginalTaxRate(-1000, 'SINGLE')).toBe(0);
    });

    it('handles NaN input correctly', () => {
      expect(getMarginalTaxRate(NaN, 'SINGLE')).toBe(0);
    });

    it('returns correct marginal tax rate for different income levels (Married Filing Jointly)', () => {
      expect(getMarginalTaxRate(20000, 'MARRIED_JOINT')).toBe(0.1);
      expect(getMarginalTaxRate(50000, 'MARRIED_JOINT')).toBe(0.12);
      expect(getMarginalTaxRate(150000, 'MARRIED_JOINT')).toBe(0.22);
      expect(getMarginalTaxRate(300000, 'MARRIED_JOINT')).toBe(0.24);
    });

    it('returns correct marginal tax rate for different income levels (Head of Household)', () => {
      expect(getMarginalTaxRate(10000, 'HEAD_OF_HOUSEHOLD')).toBe(0.1);
      expect(getMarginalTaxRate(40000, 'HEAD_OF_HOUSEHOLD')).toBe(0.12);
      expect(getMarginalTaxRate(90000, 'HEAD_OF_HOUSEHOLD')).toBe(0.22);
    });

    it('returns correct marginal tax rate at bracket boundaries (Single)', () => {
      expect(getMarginalTaxRate(FEDERAL_TAX_BRACKETS_2023.SINGLE[0].threshold - 1, 'SINGLE')).toBe(
        0.1
      );
      expect(getMarginalTaxRate(FEDERAL_TAX_BRACKETS_2023.SINGLE[0].threshold, 'SINGLE')).toBe(
        0.12
      );
      expect(getMarginalTaxRate(FEDERAL_TAX_BRACKETS_2023.SINGLE[1].threshold - 1, 'SINGLE')).toBe(
        0.12
      );
      expect(getMarginalTaxRate(FEDERAL_TAX_BRACKETS_2023.SINGLE[1].threshold, 'SINGLE')).toBe(
        0.22
      );
    });

    it('returns correct marginal tax rate at bracket boundaries (Married Filing Jointly)', () => {
      expect(
        getMarginalTaxRate(
          FEDERAL_TAX_BRACKETS_2023.MARRIED_JOINT[0].threshold - 1,
          'MARRIED_JOINT'
        )
      ).toBe(0.1);
      expect(
        getMarginalTaxRate(FEDERAL_TAX_BRACKETS_2023.MARRIED_JOINT[0].threshold, 'MARRIED_JOINT')
      ).toBe(0.12);
      expect(
        getMarginalTaxRate(
          FEDERAL_TAX_BRACKETS_2023.MARRIED_JOINT[1].threshold - 1,
          'MARRIED_JOINT'
        )
      ).toBe(0.12);
      expect(
        getMarginalTaxRate(FEDERAL_TAX_BRACKETS_2023.MARRIED_JOINT[1].threshold, 'MARRIED_JOINT')
      ).toBe(0.22);
    });

    it('returns correct marginal tax rate at bracket boundaries (Head of Household)', () => {
      expect(
        getMarginalTaxRate(
          FEDERAL_TAX_BRACKETS_2023.HEAD_OF_HOUSEHOLD[0].threshold - 1,
          'HEAD_OF_HOUSEHOLD'
        )
      ).toBe(0.1);
      expect(
        getMarginalTaxRate(
          FEDERAL_TAX_BRACKETS_2023.HEAD_OF_HOUSEHOLD[0].threshold,
          'HEAD_OF_HOUSEHOLD'
        )
      ).toBe(0.12);
      expect(
        getMarginalTaxRate(
          FEDERAL_TAX_BRACKETS_2023.HEAD_OF_HOUSEHOLD[1].threshold - 1,
          'HEAD_OF_HOUSEHOLD'
        )
      ).toBe(0.12);
      expect(
        getMarginalTaxRate(
          FEDERAL_TAX_BRACKETS_2023.HEAD_OF_HOUSEHOLD[1].threshold,
          'HEAD_OF_HOUSEHOLD'
        )
      ).toBe(0.22);
    });
  });
});
