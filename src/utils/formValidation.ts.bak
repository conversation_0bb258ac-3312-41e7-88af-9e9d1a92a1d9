/**
 * Form validation utilities for the Financial Compass application
 */

// Common validation patterns
export const patterns = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^\+?[\d\s-()]{10,}$/,
  ssn: /^\d{3}-\d{2}-\d{4}$/,
  zipCode: /^\d{5}(-\d{4})?$/,
  currency: /^\d+(\.\d{1,2})?$/,
  date: /^\d{4}-\d{2}-\d{2}$/,
  url: /^(https?:\/\/)?([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?$/,
  alphaNumeric: /^[a-zA-Z0-9 ]+$/
};

// Types
type ValidationResult = {
  isValid: boolean;
  errors: Record<string, string>;
};

type ValidationMessage = {
  required?: string;
  min?: string | ((min: number) => string);
  max?: string | ((max: number) => string);
  minLength?: string | ((min: number) => string);
  maxLength?: string | ((max: number) => string);
  pattern?: string;
  type?: string;
  custom?: string;
};

interface BaseValidationRule<T = any> {
  // Basic validation
  required?: boolean;
  requiredIf?: (formData: any) => boolean;
  
  // Type validation
  type?: 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object';
  
  // Number validation
  min?: number | ((value: T) => number);
  max?: number | ((value: T) => number);
  
  // String validation
  minLength?: number | ((value: T) => number);
  maxLength?: number | ((value: T) => number);
  pattern?: RegExp | ((value: T) => boolean);
  
  // Custom validation
  validate?: (value: T, formData?: any) => string | null | Promise<string | null>;
  custom?: (value: T, formData?: any) => string | null;
  
  // Error messages
  message?: string | ValidationMessage;

  // Conditional validation
  when?: (formData: any) => boolean;
}

interface NestedValidationRule<T = any> extends BaseValidationRule<T> {
  // Nested validation
  fields?: Record<string, ValidationRule>;
  items?: ValidationRule;
}

type ValidationRule<T = any> = NestedValidationRule<T>;

type ValidationResult = {
  isValid: boolean;
  errors: Record<string, string>;
};

// Common validation rules
export const validationRules = {
  // Basic rules
  required: { required: true },
  optional: { required: false },
  
  // Text rules
  email: {
    type: 'string',
    required: true,
    pattern: patterns.email,
    message: {
      required: 'Email is required',
      pattern: 'Please enter a valid email address'
    }
  },
  phone: {
    type: 'string',
    pattern: patterns.phone,
    message: 'Please enter a valid phone number'
  },
  ssn: {
    type: 'string',
    pattern: patterns.ssn,
    message: 'Please enter a valid SSN (XXX-XX-XXXX)'
  },
  zipCode: {
    type: 'string',
    pattern: patterns.zipCode,
    message: 'Please enter a valid ZIP code'
  },
  
  // Number rules
  positiveNumber: {
    type: 'number',
    required: true,
    min: 0,
    message: {
      required: 'This field is required',
      min: 'Must be a positive number'
    }
  },
  percentage: {
    type: 'number',
    min: 0,
    max: 100,
    message: 'Must be between 0 and 100'
  },
  currency: {
    type: 'number',
    min: 0,
    message: 'Must be a positive number'
  },
  
  // Date rules
  date: {
    type: 'date',
    message: 'Please enter a valid date'
  },
  futureDate: {
    type: 'date',
    validate: (value: string) => {
      const date = new Date(value);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      return date >= today ? null : 'Date must be in the future';
    }
  },
  
  // Composite rules
  password: {
    type: 'string',
    required: true,
    minLength: 8,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
    message: {
      required: 'Password is required',
      minLength: 'Password must be at least 8 characters',
      pattern: 'Password must contain at least one lowercase letter, one uppercase letter, and one number'
    }
  }
};

// Helper functions
const formatFieldName = (name: string): string => {
  return name
    .replace(/([A-Z])/g, ' $1')
    .replace(/([a-z])([A-Z])/g, '$1 $2')
    .replace(/\s+/g, ' ')
    .trim()
    .replace(/^\w/, (c) => c.toUpperCase());
};

// Common validation patterns
export const patterns = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,  // Basic email validation
  phone: /^\+?[\d\s-()]{10,}$/,  // International phone numbers with optional +
  ssn: /^\d{3}-\d{2}-\d{4}$/,  // US Social Security Number
  zipCode: /^\d{5}(-\d{4})?$/,  // US ZIP code with optional +4
  currency: /^\d+(\.\d{1,2})?$/,  // Basic currency format
  date: /^\d{4}-\d{2}-\d{2}$/,  // YYYY-MM-DD format
  url: /^(https?:\/\/)?([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?$/,  // Basic URL format
};

// Helper to get error message
const getErrorMessage = (
  errorType: keyof ValidationMessage,
  fieldName: string,
  ruleValue?: any,
  customMessage?: ValidationRule['message']
): string => {
  const formattedFieldName = formatFieldName(fieldName);
  
  // Handle string message
  if (typeof customMessage === 'string') {
    return customMessage;
  }
  // Handle custom message object
  if (customMessage && typeof customMessage === 'object') {
    if (errorType === 'min' && 'min' in customMessage) {
      const message = customMessage.min;
      if (typeof message === 'function') return (message as (v: any) => string)(ruleValue);
      if (message) return message as string;
    } else if (errorType === 'max' && 'max' in customMessage) {
      const message = customMessage.max;
      if (typeof message === 'function') return (message as (v: any) => string)(ruleValue);
      if (message) return message as string;
    } else if (errorType === 'required' && 'required' in customMessage) {
      const message = customMessage.required;
      if (message) return message as string;
    } else if (errorType === 'minLength' && 'minLength' in customMessage) {
      const message = customMessage.minLength;
      if (message) return message as string;
    } else if (errorType === 'maxLength' && 'maxLength' in customMessage) {
      const message = customMessage.maxLength;
      if (message) return message as string;
    } else if (errorType === 'pattern' && 'pattern' in customMessage) {
      const message = customMessage.pattern;
      if (message) return message as string;
    } else if (errorType === 'type' && 'type' in customMessage) {
      const message = customMessage.type;
      if (message) return message as string;
    } else if (errorType === 'custom' && 'custom' in customMessage) {
      const message = customMessage.custom;
      if (message) return message as string;
    }
  }

  // Default messages
  const messages: Record<keyof ValidationMessage, string | ((v: any) => string)> = {
    required: `${formattedFieldName} is required`,
    min: (min: number) => `${formattedFieldName} must be at least ${min}`,
    max: (max: number) => `${formattedFieldName} must be no more than ${max}`,
    minLength: (min: number) => `${formattedFieldName} must be at least ${min} characters`,
    maxLength: (max: number) => `${formattedFieldName} must be no more than ${max} characters`,
    pattern: `Invalid ${formattedFieldName} format`,
    type: (type: string) => `${formattedFieldName} must be a ${type}`,
    custom: `Invalid ${formattedFieldName}`,
  };

  const message = messages[errorType];
  return typeof message === 'function' ? message(ruleValue) : message as string;
};

/**
 * Asynchronously validates a single form field
 */
export const validateFieldAsync = async <T = any>(
  value: T,
  rules: ValidationRule<T>,
  fieldName: string,
  formData: any = {}
): Promise<string | null> => {
  // First run synchronous validations
  const syncError = validateField(value, rules, fieldName, formData);
  if (syncError) {
    return syncError;
  }

  // Then run async validations if they exist
  if (rules.validate) {
    try {
      const error = await rules.validate(value, formData);
      if (error) {
        return getErrorMessage('custom', fieldName, undefined, rules.message) || error;
      }
    } catch (e) {
      return 'Validation failed';
    }
  }

  return null;
};

/**
 * Validates a form field against the specified rules
 */
export const validateField = <T = any>(
  value: T,
  rules: ValidationRule<T>,
  fieldName: string,
  formData: any = {}
): string | null => {
  // Skip validation if when condition is not met
  if ('when' in rules && rules.when && !rules.when(formData)) {
    return null;
  }
  // Skip validation if value is not required and empty
  const isEmpty = value === '' || value === null || value === undefined;
  const isRequired = rules.required || (rules.requiredIf ? rules.requiredIf(formData) : false);
  
  if (isEmpty) {
    return isRequired ? getErrorMessage('required', fieldName, undefined, rules.message) : null;
  }

  // Type checking
  if (rules.type) {
    const type = typeof value;
    const typeValid = (
      (rules.type === 'array' && Array.isArray(value)) ||
      (rules.type === 'date' && !isNaN(Date.parse(value as any))) ||
      (rules.type === 'number' && !isNaN(Number(value))) ||
      type === rules.type
    );
    
    if (!typeValid) {
      return getErrorMessage('type', fieldName, rules.type, rules.message);
    }
  }

  // Number validations
  if (typeof value === 'number' || (typeof value === 'string' && !isNaN(Number(value)))) {
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    
    if (rules.min !== undefined) {
      const min = typeof rules.min === 'function' ? rules.min(value) : rules.min;
      if (numValue < min) {
        return getErrorMessage('min', fieldName, min, rules.message);
      }
    }
    
    if (rules.max !== undefined) {
      const max = typeof rules.max === 'function' ? rules.max(value) : rules.max;
      if (numValue > max) {
        return getErrorMessage('max', fieldName, max, rules.message);
      }
    }
  }

  // String validations
  if (typeof value === 'string') {
    if (rules.minLength !== undefined) {
      const minLength = typeof rules.minLength === 'function' ? rules.minLength(value) : rules.minLength;
      if (value.length < minLength) {
        return getErrorMessage('minLength', fieldName, minLength, rules.message);
      }
    }
    
    if (rules.maxLength !== undefined) {
      const maxLength = typeof rules.maxLength === 'function' ? rules.maxLength(value) : rules.maxLength;
      if (value.length > maxLength) {
        return getErrorMessage('maxLength', fieldName, maxLength, rules.message);
      }
    }
    
    if (rules.pattern) {
      const pattern = rules.pattern instanceof RegExp ? rules.pattern : rules.pattern(value);
      const testPattern = pattern instanceof RegExp ? pattern.test(value) : pattern;
      if (!testPattern) {
        return getErrorMessage('pattern', fieldName, undefined, rules.message);
      }
    }
  }
  
  // Nested object validation
  if (rules.fields && value && typeof value === 'object' && !Array.isArray(value)) {
    const nestedResult = validateForm(value, rules.fields);
    if (!nestedResult.isValid) {
      const firstError = Object.entries(nestedResult.errors)[0];
      return firstError ? `${fieldName}.${firstError[0]}: ${firstError[1]}` : `Invalid ${fieldName}`;
    }
  }
  
  // Array validation
  if (rules.items && Array.isArray(value)) {
    for (let i = 0; i < value.length; i++) {
      const error = validateField(value[i], rules.items, `${fieldName}[${i}]`, formData);
      if (error) return error;
    }
  }

  // Custom validation
  if (rules.validate) {
    const error = rules.validate(value, formData);
    if (error) return typeof error === 'string' ? error : getErrorMessage('custom', fieldName, undefined, rules.message);
  }
  
  if (rules.custom) {
    const error = rules.custom(value, formData);
    if (error) return error;
  }

  return null;

/**
 * Validates a form object against a validation schema
 */
// Type guard for validation rules
const isNestedRule = (rules: ValidationRule): rules is NestedValidationRule => {
  return !!(rules as NestedValidationRule).fields || !!(rules as NestedValidationRule).items;
};

/**
 * Validates a form object against a validation schema
 */
export function validateForm<T extends Record<string, any>>(
  formData: T,
  validationSchema: Record<string, ValidationRule>,
  parentField: string = ''
): ValidationResult {
  const errors: Record<string, string> = {};
  let isValid = true;

  for (const [field, rules] of Object.entries(validationSchema)) {
    const fieldPath = parentField ? `${parentField}.${field}` : field;
    const value = formData[field as keyof T];
    
    // Skip validation if when condition is not met
    if ('when' in rules && rules.when && !rules.when(formData)) {
      continue;
    }
    
    // Handle nested objects
    if (isNestedRule(rules) && rules.fields && value && typeof value === 'object' && !Array.isArray(value)) {
      const nestedResult = validateForm(
        value as Record<string, any>,
        rules.fields as Record<string, ValidationRule>,
        fieldPath
      );
      if (!nestedResult.isValid) {
        Object.assign(errors, nestedResult.errors);
        isValid = false;
      }
      continue;
    }
    
    // Handle arrays
    if (isNestedRule(rules) && rules.items && Array.isArray(value)) {
      for (let i = 0; i < value.length; i++) {
        const itemPath = `${fieldPath}[${i}]`;
        const itemError = validateField(value[i], rules.items, itemPath, formData);
        if (itemError) {
          errors[itemPath] = itemError;
          isValid = false;
        }
      }
      continue;
    }
    
    // Validate the field
    const error = validateField(value, rules, fieldPath, formData);
    if (error) {
      errors[fieldPath] = error;
      isValid = false;
    }
  }

  return { isValid, errors };
}

/**
 * Asynchronously validates a form object against a validation schema
 */
export async function validateFormAsync<T extends Record<string, any>>(
  formData: T,
  validationSchema: Record<string, ValidationRule>,
  parentField: string = ''
): Promise<ValidationResult> {
  const errors: Record<string, string> = {};
  let isValid = true;

  for (const [field, rules] of Object.entries(validationSchema)) {
    const fieldPath = parentField ? `${parentField}.${field}` : field;
    const value = formData[field as keyof T];
    
    // Skip validation if when condition is not met
    if ('when' in rules && rules.when && !rules.when(formData)) {
      continue;
    }
    
    // Handle nested objects
    if (rules.fields && value && typeof value === 'object' && !Array.isArray(value)) {
      const nestedResult = await validateFormAsync(value, rules.fields as any, fieldPath);
      if (!nestedResult.isValid) {
        Object.assign(errors, nestedResult.errors);
        isValid = false;
      }
      continue;
    }
    
    // Handle arrays
    if (rules.items && Array.isArray(value)) {
      for (let i = 0; i < value.length; i++) {
        const itemPath = `${fieldPath}[${i}]`;
        const itemError = await validateFieldAsync(value[i], rules.items, itemPath, formData);
        if (itemError) {
          errors[itemPath] = itemError;
          isValid = false;
        }
      }
      continue;
    }
    
    // Handle async validators
    if (rules.validate) {
      try {
        const error = await rules.validate(value, formData);
        if (error) {
          errors[fieldPath] = typeof error === 'string' ? error : getErrorMessage('custom', field, undefined, rules.message);
          isValid = false;
          continue;
        }
      } catch (e) {
        errors[fieldPath] = 'Validation failed';
        isValid = false;
        continue;
      }
    }
    
    // Handle sync validators
    const syncError = validateField(value, rules, fieldPath, formData);
    if (syncError) {
      errors[fieldPath] = syncError;
      isValid = false;
    }
  }

  return { isValid, errors };
}

/**
 * Creates a form validator function for a specific schema
 */
export function createValidator<T extends Record<string, any>>(
  schema: Record<string, ValidationRule>
): (formData: T) => ValidationResult {
  return (formData: T) => validateForm(formData, schema);
}

/**
 * Creates an async form validator function for a specific schema
 */
export function createAsyncValidator<T extends Record<string, any>>(
  schema: Record<string, ValidationRule>
): (formData: T) => Promise<ValidationResult> {
  return (formData: T) => validateFormAsync(formData, schema);
}
