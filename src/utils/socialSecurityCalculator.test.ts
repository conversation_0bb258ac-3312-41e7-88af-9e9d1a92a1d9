/**
 * Social Security Calculator Utility Tests
 */

import {
  calculatePIA,
  calculateFullRetirementAge,
  formatFullRetirementAge,
  ageStringToMonths,
  calculateAdjustedBenefit,
  calculateLifetimeBenefits,
  calculateSpousalBenefit,
  calculateSurvivorBenefit,
  BEND_POINTS_2023,
  REPLACEMENT_RATES,
} from './socialSecurityCalculator';

describe('Social Security Calculator Utility', () => {
  describe('calculatePIA', () => {
    it('calculates PIA correctly using the three-tier formula', () => {
      // Test case 1: AIME below first bend point
      // Expected: 90% of AIME
      const aime1 = 1000;
      const expected1 = Math.round(aime1 * REPLACEMENT_RATES.FIRST);
      expect(calculatePIA(aime1)).toBe(expected1);

      // Test case 2: AIME between first and second bend points
      // Expected: 90% of first bend point + 32% of the amount between bend points
      const aime2 = 3000;
      const expected2 = Math.round(
        BEND_POINTS_2023.FIRST * REPLACEMENT_RATES.FIRST +
          (aime2 - BEND_POINTS_2023.FIRST) * REPLACEMENT_RATES.SECOND
      );
      expect(calculatePIA(aime2)).toBe(expected2);

      // Test case 3: AIME above second bend point
      // Expected: 90% of first bend point + 32% of the amount between bend points + 15% of amount above second bend point
      const aime3 = 8000;
      const expected3 = Math.round(
        BEND_POINTS_2023.FIRST * REPLACEMENT_RATES.FIRST +
          (BEND_POINTS_2023.SECOND - BEND_POINTS_2023.FIRST) * REPLACEMENT_RATES.SECOND +
          (aime3 - BEND_POINTS_2023.SECOND) * REPLACEMENT_RATES.THIRD
      );
      expect(calculatePIA(aime3)).toBe(expected3);
    });

    it('handles zero and negative AIME correctly', () => {
      expect(calculatePIA(0)).toBe(0);
      expect(calculatePIA(-1000)).toBe(0);
    });

    it('handles NaN input gracefully', () => {
      expect(calculatePIA(NaN)).toBe(0);
    });

    it('calculates PIA correctly exactly at the first bend point', () => {
      const aime = BEND_POINTS_2023.FIRST;
      const expected = Math.round(aime * REPLACEMENT_RATES.FIRST);
      expect(calculatePIA(aime)).toBe(expected);
    });

    it('calculates PIA correctly exactly at the second bend point', () => {
      const aime = BEND_POINTS_2023.SECOND;
      const expected = Math.round(
        BEND_POINTS_2023.FIRST * REPLACEMENT_RATES.FIRST +
          (BEND_POINTS_2023.SECOND - BEND_POINTS_2023.FIRST) * REPLACEMENT_RATES.SECOND
      );
      expect(calculatePIA(aime)).toBe(expected);
    });
  });

  describe('calculateFullRetirementAge', () => {
    it('calculates full retirement age correctly for different birth years', () => {
      // Test case 1: Birth year 1937 or earlier
      expect(calculateFullRetirementAge(1937)).toEqual({ years: 65, months: 0 });
      expect(calculateFullRetirementAge(1936)).toEqual({ years: 65, months: 0 });

      // Test case 2: Birth year 1943-1954
      expect(calculateFullRetirementAge(1943)).toEqual({ years: 66, months: 0 });
      expect(calculateFullRetirementAge(1954)).toEqual({ years: 66, months: 0 });

      // Test case 3: Birth year 1960 or later
      expect(calculateFullRetirementAge(1960)).toEqual({ years: 67, months: 0 });
      expect(calculateFullRetirementAge(1970)).toEqual({ years: 67, months: 0 });

      // Test case 4: Birth years with partial months
      expect(calculateFullRetirementAge(1955)).toEqual({ years: 66, months: 2 });
      expect(calculateFullRetirementAge(1957)).toEqual({ years: 66, months: 6 });
      expect(calculateFullRetirementAge(1959)).toEqual({ years: 66, months: 10 });
    });

    it('handles string input correctly', () => {
      expect(calculateFullRetirementAge('1957')).toEqual({ years: 66, months: 6 });
    });

    it('handles invalid input gracefully', () => {
      expect(calculateFullRetirementAge(NaN)).toEqual({ years: 67, months: 0 });
      expect(calculateFullRetirementAge('invalid')).toEqual({ years: 67, months: 0 });
    });

    it('handles numerical input correctly', () => {
      expect(calculateFullRetirementAge(1957)).toEqual({ years: 66, months: 6 });
    });
  });

  describe('formatFullRetirementAge', () => {
    it('formats full retirement age correctly', () => {
      // Test case 1: No months
      expect(formatFullRetirementAge({ years: 67, months: 0 })).toBe('67');

      // Test case 2: With months
      expect(formatFullRetirementAge({ years: 66, months: 6 })).toBe('66 and 6 months');
    });
  });

  describe('ageStringToMonths', () => {
    it('converts age strings to months correctly', () => {
      // Test case 1: Simple age
      expect(ageStringToMonths('67')).toBe(67 * 12);

      // Test case 2: Age with months
      expect(ageStringToMonths('66 and 6 months')).toBe(66 * 12 + 6);
    });

    it('handles invalid input gracefully', () => {
      expect(ageStringToMonths('invalid')).toBe(67 * 12); // Default to age 67
    });
  });

  describe('calculateAdjustedBenefit', () => {
    it('calculates early claiming reduction correctly', () => {
      // Test case: Claiming at 62 with FRA of 67
      // Expected reduction: 30% (5/9% per month for 36 months + 5/12% per month for 24 months)
      const pia = 2000;
      const fra = '67';
      const claimingAge = 62;

      // Calculate expected reduction
      const monthsEarly = 60; // 5 years * 12 months
      const firstPeriodMonths = 36;
      const secondPeriodMonths = 24;
      const firstPeriodReduction = (firstPeriodMonths * (5 / 9)) / 100;
      const secondPeriodReduction = (secondPeriodMonths * (5 / 12)) / 100;
      const totalReduction = firstPeriodReduction + secondPeriodReduction;
      const expected = Math.round(pia * (1 - totalReduction));

      expect(calculateAdjustedBenefit(pia, fra, claimingAge)).toBe(expected);
    });

    it('calculates delayed retirement credits correctly', () => {
      // Test case: Claiming at 70 with FRA of 67
      // Expected increase: 24% (8% per year for 3 years)
      const pia = 2000;
      const fra = '67';
      const claimingAge = 70;

      // Calculate expected increase
      const monthsDelayed = 36; // 3 years * 12 months
      const delayedCreditRate = (monthsDelayed * (2 / 3)) / 100;
      const expected = Math.round(pia * (1 + delayedCreditRate));

      expect(calculateAdjustedBenefit(pia, fra, claimingAge)).toBe(expected);
    });

    it('returns PIA when claiming at FRA', () => {
      const pia = 2000;
      const fra = '67';
      const claimingAge = 67;

      expect(calculateAdjustedBenefit(pia, fra, claimingAge)).toBe(pia);
    });

    it('handles string claiming age correctly', () => {
      const pia = 2000;
      const fra = '67';
      const claimingAge = '70';

      // Calculate expected increase
      const monthsDelayed = 36; // 3 years * 12 months
      const delayedCreditRate = (monthsDelayed * (2 / 3)) / 100;
      const expected = Math.round(pia * (1 + delayedCreditRate));

      expect(calculateAdjustedBenefit(pia, fra, claimingAge)).toBe(expected);
    });

    it('handles invalid input gracefully', () => {
      expect(calculateAdjustedBenefit(0, '67', 70)).toBe(0);
      expect(calculateAdjustedBenefit(-1000, '67', 70)).toBe(0);
      expect(calculateAdjustedBenefit(NaN, '67', 70)).toBe(0);
      expect(calculateAdjustedBenefit(2000, '67', NaN)).toBe(2000);
    });

    it('calculates adjustment correctly for claiming exactly 36 months early', () => {
      const pia = 2000;
      const fra = '67';
      const claimingAge = 64; // 67 - 3 years = 64 (36 months early)
      const expected = Math.round(pia * (1 - (36 * (5 / 9)) / 100)); // Only first period reduction
      expect(calculateAdjustedBenefit(pia, fra, claimingAge)).toBe(expected);
    });

    it('calculates adjustment correctly for claiming exactly 36 months late', () => {
      const pia = 2000;
      const fra = '67';
      const claimingAge = 70; // 67 + 3 years = 70 (36 months late)
      const expected = Math.round(pia * (1 + (36 * (2 / 3)) / 100)); // Max delayed credits
      expect(calculateAdjustedBenefit(pia, fra, claimingAge)).toBe(expected);
    });
  });

  describe('calculateLifetimeBenefits', () => {
    it('calculates lifetime benefits correctly', () => {
      // Test case: $2,000 monthly benefit, claiming at 62, life expectancy 85
      const monthlyBenefit = 2000;
      const claimingAge = 62;
      const lifeExpectancy = 85;

      // Calculate expected lifetime benefits
      const yearsReceivingBenefits = lifeExpectancy - claimingAge;
      const expected = monthlyBenefit * 12 * yearsReceivingBenefits;

      expect(calculateLifetimeBenefits(monthlyBenefit, claimingAge, lifeExpectancy)).toBe(expected);
    });

    it('handles string input correctly', () => {
      const monthlyBenefit = 2000;
      const claimingAge = '62';
      const lifeExpectancy = '85';

      // Calculate expected lifetime benefits
      const yearsReceivingBenefits = 85 - 62;
      const expected = monthlyBenefit * 12 * yearsReceivingBenefits;

      expect(calculateLifetimeBenefits(monthlyBenefit, claimingAge, lifeExpectancy)).toBe(expected);
    });

    it('returns zero when life expectancy is less than claiming age', () => {
      expect(calculateLifetimeBenefits(2000, 70, 65)).toBe(0);
    });

    it('handles invalid input gracefully', () => {
      expect(calculateLifetimeBenefits(0, 62, 85)).toBe(0);
      expect(calculateLifetimeBenefits(-1000, 62, 85)).toBe(0);
      expect(calculateLifetimeBenefits(NaN, 62, 85)).toBe(0);
      expect(calculateLifetimeBenefits(2000, NaN, 85)).toBe(0);
      expect(calculateLifetimeBenefits(2000, 62, NaN)).toBe(0);
    });

    it('handles numerical input for claiming age and life expectancy', () => {
      const monthlyBenefit = 2000;
      const claimingAge = 62;
      const lifeExpectancy = 85;

      // Calculate expected lifetime benefits
      const yearsReceivingBenefits = lifeExpectancy - claimingAge;
      const expected = monthlyBenefit * 12 * yearsReceivingBenefits;

      expect(calculateLifetimeBenefits(monthlyBenefit, claimingAge, lifeExpectancy)).toBe(expected);
    });
  });

  describe('calculateSpousalBenefit', () => {
    it('calculates spousal benefit correctly', () => {
      // Test case 1: Spouse's PIA is higher, own PIA is lower
      // Expected: 50% of spouse's PIA - own PIA
      const spousePIA1 = 3000;
      const ownPIA1 = 1000;
      const expected1 = Math.round(spousePIA1 * 0.5 - ownPIA1);
      expect(calculateSpousalBenefit(spousePIA1, ownPIA1)).toBe(expected1);

      // Test case 2: Own PIA is higher than 50% of spouse's PIA
      // Expected: 0 (no spousal benefit)
      const spousePIA2 = 3000;
      const ownPIA2 = 2000;
      expect(calculateSpousalBenefit(spousePIA2, ownPIA2)).toBe(0);
    });

    it('handles invalid input gracefully', () => {
      expect(calculateSpousalBenefit(0, 1000)).toBe(0);
      expect(calculateSpousalBenefit(-1000, 1000)).toBe(0);
      expect(calculateSpousalBenefit(NaN, 1000)).toBe(0);
      expect(calculateSpousalBenefit(3000, NaN)).toBe(0);
    });
  });

  describe('calculateSurvivorBenefit', () => {
    it('calculates survivor benefit correctly', () => {
      // Test case 1: Deceased spouse's PIA is higher
      // Expected: Deceased spouse's PIA - own PIA
      const deceasedPIA1 = 3000;
      const ownPIA1 = 1000;
      const expected1 = Math.round(deceasedPIA1 - ownPIA1);
      expect(calculateSurvivorBenefit(deceasedPIA1, ownPIA1)).toBe(expected1);

      // Test case 2: Own PIA is higher
      // Expected: 0 (no survivor benefit)
      const deceasedPIA2 = 2000;
      const ownPIA2 = 3000;
      expect(calculateSurvivorBenefit(deceasedPIA2, ownPIA2)).toBe(0);
    });

    it('handles invalid input gracefully', () => {
      expect(calculateSurvivorBenefit(0, 1000)).toBe(0);
      expect(calculateSurvivorBenefit(-1000, 1000)).toBe(0);
      expect(calculateSurvivorBenefit(NaN, 1000)).toBe(0);
      expect(calculateSurvivorBenefit(3000, NaN)).toBe(0);
    });
  });
});
