import { z } from 'zod';
import { EastDirectionData } from '../schemas/east';
import { NorthDirectionData } from '../schemas/north';
import { SouthDirectionData } from '../schemas/south';
import { WestDirectionData } from '../schemas/west';

type DirectionData = {
  north?: NorthDirectionData;
  east?: EastDirectionData;
  south?: SouthDirectionData;
  west?: WestDirectionData;
};

export class ConsistencyChecker {
  private data: DirectionData;
  private errors: string[] = [];

  constructor(data: DirectionData) {
    this.data = data;
  }

  checkAll(): { isValid: boolean; errors: string[] } {
    this.errors = [];

    // Run all consistency checks
    this.checkRetirementSavingsVsIncome();
    this.checkInsuranceCoverageVsAssets();
    this.checkEmergencyFundVsExpenses();
    this.checkDebtToIncomeRatio();
    this.checkRetirementContributionVsAge();

    return {
      isValid: this.errors.length === 0,
      errors: this.errors,
    };
  }

  private checkRetirementSavingsVsIncome(): void {
    const { north, east } = this.data;

    if (!north?.income?.salary || !east?.retirementGoal) return;

    const annualIncome = north.income.salary;
    const retirementSavings = east.retirementGoal.currentSavings || 0;
    const recommendedSavings = annualIncome * 0.1 * 12; // 10% of annual income per year

    if (retirementSavings < recommendedSavings * 0.5) {
      this.errors.push(
        `Your retirement savings ($${retirementSavings.toLocaleString()}) is significantly lower than the recommended amount ` +
          `($${recommendedSavings.toLocaleString()}) based on your current income.`
      );
    }
  }

  private checkInsuranceCoverageVsAssets(): void {
    const { north, south } = this.data;
    if (!north?.assets || !south?.insurance) return;

    const totalAssets = Object.values(north.assets).reduce(
      (sum, value) => sum + (typeof value === 'number' ? value : 0),
      0
    );

    const lifeInsurance = south.insurance.life?.coverageAmount || 0;
    const propertyInsurance = south.insurance.property?.coverageAmount || 0;
    const totalCoverage = lifeInsurance + propertyInsurance;

    if (totalCoverage < totalAssets * 0.7) {
      this.errors.push(
        `Your total insurance coverage ($${totalCoverage.toLocaleString()}) may be insufficient ` +
          `for your total assets ($${totalAssets.toLocaleString()}). Consider reviewing your coverage.`
      );
    }
  }

  private checkEmergencyFundVsExpenses(): void {
    const { north } = this.data;
    if (!north?.expenses) return;

    const monthlyExpenses = Object.values(north.expenses).reduce(
      (sum, value) => sum + (typeof value === 'number' ? value : 0),
      0
    );

    const emergencyFund = north.assets?.savingsAccounts || 0;
    const recommendedFund = monthlyExpenses * 6; // 6 months of expenses

    if (emergencyFund < recommendedFund * 0.75) {
      this.errors.push(
        `Your emergency fund ($${emergencyFund.toLocaleString()}) is below the recommended ` +
          `6 months of expenses ($${recommendedFund.toLocaleString()}).`
      );
    }
  }

  private checkDebtToIncomeRatio(): void {
    const { north } = this.data;
    if (!north?.income?.salary || !north.liabilities) return;

    const annualIncome = north.income.salary;
    const monthlyIncome = annualIncome / 12;

    const monthlyDebtPayments = Object.entries(north.liabilities).reduce((sum, [key, value]) => {
      if (key === 'interestRates' || key === 'minimumPayments' || key === 'lastUpdated') {
        return sum;
      }
      return sum + (typeof value === 'number' ? value : 0);
    }, 0);

    const debtToIncomeRatio = (monthlyDebtPayments / monthlyIncome) * 100;

    if (debtToIncomeRatio > 36) {
      this.errors.push(
        `Your debt-to-income ratio (${debtToIncomeRatio.toFixed(1)}%) is above the recommended 36%. ` +
          `Consider paying down high-interest debts.`
      );
    }
  }

  private checkRetirementContributionVsAge(): void {
    const { north, east } = this.data;
    if (!north?.personalInfo?.age || !east?.retirementGoal) return;

    const age = north.personalInfo.age;
    const currentSavings = east.retirementGoal.currentSavings || 0;
    const annualContribution = (east.retirementGoal.monthlyContribution || 0) * 12;

    // Simple retirement savings benchmark by age (as multiple of annual income)
    const benchmarkMultipliers: Record<number, number> = {
      30: 1,
      40: 3,
      50: 6,
      60: 8,
      67: 10,
    };

    const benchmarkAge = Math.min(
      ...Object.keys(benchmarkMultipliers)
        .map(Number)
        .filter((benchmarkAge) => age <= benchmarkAge)
    );

    const benchmarkMultiplier = benchmarkMultipliers[benchmarkAge] || 10;
    const annualIncome = north.income?.salary || 0;
    const recommendedSavings = annualIncome * benchmarkMultiplier;

    if (currentSavings < recommendedSavings * 0.7) {
      this.errors.push(
        `At age ${age}, you should aim to have ${benchmarkMultiplier}x your annual income saved for retirement. ` +
          `Your current savings ($${currentSavings.toLocaleString()}) are below the recommended ` +
          `$${recommendedSavings.toLocaleString()} for your age group.`
      );
    }

    // Check contribution rate
    if (annualIncome > 0 && annualContribution / annualIncome < 0.1) {
      this.errors.push(
        `Your retirement contribution rate (${((annualContribution / annualIncome) * 100).toFixed(1)}%) ` +
          `is below the recommended 10-15% of your income.`
      );
    }
  }
}

// Helper function to validate cross-form consistency
export const validateCrossFormConsistency = (data: DirectionData) => {
  const checker = new ConsistencyChecker(data);
  return checker.checkAll();
};

// Zod schema for cross-form validation
export const crossFormConsistencySchema = z
  .object({
    north: z
      .object({
        income: z
          .object({
            salary: z.number().min(0),
          })
          .optional(),
        expenses: z.record(z.number()).optional(),
        assets: z.record(z.number()).optional(),
        liabilities: z.record(z.union([z.number(), z.record(z.any())])).optional(),
        personalInfo: z
          .object({
            age: z.number().min(0).optional(),
          })
          .optional(),
      })
      .optional(),
    east: z
      .object({
        retirementGoal: z
          .object({
            currentSavings: z.number().min(0).optional(),
            monthlyContribution: z.number().min(0).optional(),
          })
          .optional(),
      })
      .optional(),
    south: z
      .object({
        insurance: z
          .object({
            life: z
              .object({
                coverageAmount: z.number().min(0).optional(),
              })
              .optional(),
            property: z
              .object({
                coverageAmount: z.number().min(0).optional(),
              })
              .optional(),
          })
          .optional(),
      })
      .optional(),
  })
  .refine(() => true, {
    message: 'Cross-form validation failed',
    path: ['crossFormValidation'],
  });
