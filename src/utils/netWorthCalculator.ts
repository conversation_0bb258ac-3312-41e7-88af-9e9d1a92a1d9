/**
 * Net Worth Calculator Utility
 *
 * This utility provides standardized net worth calculation functions to ensure
 * proper synchronization between assets and liabilities.
 */

/**
 * Asset liquidity levels
 */
export enum AssetLiquidity {
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low',
}

/**
 * Asset risk levels
 */
export enum AssetRisk {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
}

/**
 * Asset allocation health status
 */
export type AllocationHealthStatus = 'good' | 'moderate' | 'poor';

/**
 * Asset allocation health assessment
 */
export interface AssetAllocationHealth {
  diversification: AllocationHealthStatus;
  liquidity: AllocationHealthStatus;
  risk: AllocationHealthStatus;
  overall: AllocationHealthStatus;
}

/**
 * Net worth history entry
 */
export interface NetWorthHistoryEntry {
  date: string;
  amount: number;
}

/**
 * Asset field definition
 */
export interface AssetField {
  id: string;
  label: string;
  liquidity: AssetLiquidity;
  risk: AssetRisk;
}

/**
 * Calculate total assets from asset categories
 *
 * @param assets - Asset categories with their fields and values
 * @param assetFields - Asset field definitions with liquidity and risk information
 * @returns Detailed asset calculation results
 */
export const calculateAssets = (
  assets: Record<string, Record<string, number>>,
  assetFields: Record<string, AssetField[]>
): {
  categoryTotals: Record<string, number>;
  categoryPercentages: Record<string, number>;
  totalAssets: number;
  liquidityTotals: Record<string, number>;
  liquidityPercentages: Record<string, number>;
  riskTotals: Record<string, number>;
  riskPercentages: Record<string, number>;
  liquidityRatio: number;
  assetAllocationHealth: AssetAllocationHealth;
} => {
  // Calculate total for each category
  const categoryTotals: Record<string, number> = {};
  let totalAssets = 0;

  // Track assets by liquidity and risk
  const liquidityTotals: Record<string, number> = {
    [AssetLiquidity.HIGH]: 0,
    [AssetLiquidity.MEDIUM]: 0,
    [AssetLiquidity.LOW]: 0,
  };

  const riskTotals: Record<string, number> = {
    [AssetRisk.LOW]: 0,
    [AssetRisk.MEDIUM]: 0,
    [AssetRisk.HIGH]: 0,
  };

  // Calculate category totals
  Object.entries(assets).forEach(([categoryId, fields]) => {
    let categoryTotal = 0;

    // Calculate field totals within category
    Object.entries(fields).forEach(([fieldId, value]) => {
      const amount = value || 0;
      categoryTotal += amount;

      // Find the field to check its liquidity and risk
      const categoryFields = assetFields[categoryId] || [];
      const field = categoryFields.find((f) => f.id === fieldId);

      if (field && amount > 0) {
        // Add to liquidity totals
        liquidityTotals[field.liquidity] += amount;

        // Add to risk totals
        riskTotals[field.risk] += amount;
      }
    });

    // Add to category total
    categoryTotals[categoryId] = categoryTotal;
    totalAssets += categoryTotal;
  });

  // Calculate percentages
  const categoryPercentages: Record<string, number> = {};
  Object.entries(categoryTotals).forEach(([categoryId, total]) => {
    categoryPercentages[categoryId] = totalAssets > 0 ? (total / totalAssets) * 100 : 0;
  });

  const liquidityPercentages = {
    [AssetLiquidity.HIGH]:
      totalAssets > 0 ? (liquidityTotals[AssetLiquidity.HIGH] / totalAssets) * 100 : 0,
    [AssetLiquidity.MEDIUM]:
      totalAssets > 0 ? (liquidityTotals[AssetLiquidity.MEDIUM] / totalAssets) * 100 : 0,
    [AssetLiquidity.LOW]:
      totalAssets > 0 ? (liquidityTotals[AssetLiquidity.LOW] / totalAssets) * 100 : 0,
  };

  const riskPercentages = {
    [AssetRisk.LOW]: totalAssets > 0 ? (riskTotals[AssetRisk.LOW] / totalAssets) * 100 : 0,
    [AssetRisk.MEDIUM]: totalAssets > 0 ? (riskTotals[AssetRisk.MEDIUM] / totalAssets) * 100 : 0,
    [AssetRisk.HIGH]: totalAssets > 0 ? (riskTotals[AssetRisk.HIGH] / totalAssets) * 100 : 0,
  };

  // Calculate liquidity ratio (high liquidity assets / total assets)
  const liquidityRatio = totalAssets > 0 ? liquidityTotals[AssetLiquidity.HIGH] / totalAssets : 0;

  // Determine asset allocation health
  const assetAllocationHealth: AssetAllocationHealth = {
    diversification: 'moderate',
    liquidity: liquidityRatio >= 0.2 ? 'good' : liquidityRatio >= 0.1 ? 'moderate' : 'poor',
    risk:
      riskPercentages[AssetRisk.HIGH] <= 30
        ? 'good'
        : riskPercentages[AssetRisk.HIGH] <= 50
          ? 'moderate'
          : 'poor',
    overall: 'moderate',
  };

  // Determine overall asset allocation health
  if (assetAllocationHealth.liquidity === 'good' && assetAllocationHealth.risk === 'good') {
    assetAllocationHealth.overall = 'good';
  } else if (assetAllocationHealth.liquidity === 'poor' || assetAllocationHealth.risk === 'poor') {
    assetAllocationHealth.overall = 'poor';
  }

  // Check diversification (no single category should be more than 50% except primary residence)
  const nonHomeCategories = Object.entries(categoryPercentages).filter(
    ([categoryId]) => categoryId !== 'realEstate'
  );

  const hasDiversification = !nonHomeCategories.some(([_, percentage]) => percentage > 50);

  if (hasDiversification) {
    assetAllocationHealth.diversification = 'good';
  } else {
    assetAllocationHealth.diversification = 'poor';
  }

  return {
    categoryTotals,
    categoryPercentages,
    totalAssets,
    liquidityTotals,
    liquidityPercentages,
    riskTotals,
    riskPercentages,
    liquidityRatio,
    assetAllocationHealth,
  };
};

/**
 * Calculate total liabilities from liability categories
 *
 * @param liabilities - Liability categories with their fields and values
 * @returns Detailed liability calculation results
 */
export const calculateLiabilities = (
  liabilities: Record<string, Record<string, number>>
): {
  categoryTotals: Record<string, number>;
  categoryPercentages: Record<string, number>;
  totalLiabilities: number;
} => {
  // Calculate total for each category
  const categoryTotals: Record<string, number> = {};
  let totalLiabilities = 0;

  Object.entries(liabilities).forEach(([categoryId, fields]) => {
    const categoryTotal = Object.values(fields).reduce((sum, value) => sum + (value || 0), 0);
    categoryTotals[categoryId] = categoryTotal;
    totalLiabilities += categoryTotal;
  });

  // Calculate percentages
  const categoryPercentages: Record<string, number> = {};
  Object.entries(categoryTotals).forEach(([categoryId, total]) => {
    categoryPercentages[categoryId] = totalLiabilities > 0 ? (total / totalLiabilities) * 100 : 0;
  });

  return {
    categoryTotals,
    categoryPercentages,
    totalLiabilities,
  };
};

/**
 * Calculate net worth and related metrics
 *
 * @param totalAssets - Total assets value
 * @param totalLiabilities - Total liabilities value
 * @param netWorthHistory - Previous net worth history entries
 * @returns Net worth calculation results
 */
export const calculateNetWorth = (
  totalAssets: number,
  totalLiabilities: number,
  netWorthHistory: NetWorthHistoryEntry[] = []
): {
  netWorth: number;
  debtToAssetRatio: number;
  debtToIncomeRatio?: number;
  netWorthToIncomeRatio?: number;
  updatedNetWorthHistory: NetWorthHistoryEntry[];
  netWorthTrend: 'increasing' | 'decreasing' | 'stable' | 'unknown';
  netWorthChange: number;
  netWorthChangePercentage: number;
} => {
  // Calculate net worth
  const netWorth = totalAssets - totalLiabilities;

  // Calculate debt-to-asset ratio
  const debtToAssetRatio = totalAssets > 0 ? totalLiabilities / totalAssets : 0;

  // Update net worth history
  const now = new Date();
  const newHistoryEntry = {
    date: now.toISOString(),
    amount: netWorth,
  };

  // Add to history if it's a new day or significant change
  let updatedHistory = [...netWorthHistory];
  const lastEntry = updatedHistory[updatedHistory.length - 1];

  if (
    !lastEntry ||
    new Date(lastEntry.date).toDateString() !== now.toDateString() ||
    Math.abs((lastEntry.amount - netWorth) / (lastEntry.amount || 1)) > 0.05
  ) {
    updatedHistory.push(newHistoryEntry);

    // Keep only the last 12 entries
    if (updatedHistory.length > 12) {
      updatedHistory = updatedHistory.slice(-12);
    }
  }

  // Calculate net worth trend
  let netWorthTrend: 'increasing' | 'decreasing' | 'stable' | 'unknown' = 'unknown';
  let netWorthChange = 0;
  let netWorthChangePercentage = 0;

  if (updatedHistory.length >= 2) {
    const oldestEntry = updatedHistory[0];
    const latestEntry = updatedHistory[updatedHistory.length - 1];

    netWorthChange = latestEntry.amount - oldestEntry.amount;
    netWorthChangePercentage =
      oldestEntry.amount !== 0 ? (netWorthChange / Math.abs(oldestEntry.amount)) * 100 : 0;

    if (netWorthChangePercentage > 5) {
      netWorthTrend = 'increasing';
    } else if (netWorthChangePercentage < -5) {
      netWorthTrend = 'decreasing';
    } else {
      netWorthTrend = 'stable';
    }
  }

  return {
    netWorth,
    debtToAssetRatio,
    updatedNetWorthHistory: updatedHistory,
    netWorthTrend,
    netWorthChange,
    netWorthChangePercentage,
  };
};
