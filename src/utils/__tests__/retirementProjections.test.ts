/**
 * Retirement Projections Utility Tests
 */

import {
  calculateRetirementProjection,
  runMonteCarloSimulation,
  RetirementProjectionOptions,
  AccountType,
} from '../retirementProjections';

describe('Retirement Projections Utility', () => {
  // Mock data for tests
  const mockAccounts = [
    {
      id: 'acc1',
      name: '401k',
      balance: 100000,
      type: 'traditional' as AccountType,
      annualContribution: 10000,
      annualReturn: 0.07,
      fees: 0.01,
    },
    {
      id: 'acc2',
      name: 'Roth IRA',
      balance: 50000,
      type: 'roth' as AccountType,
      annualContribution: 6000,
      annualReturn: 0.08,
      fees: 0.005,
    },
    {
      id: 'acc3',
      name: 'Taxable Brokerage',
      balance: 20000,
      type: 'taxable' as AccountType,
      annualContribution: 5000,
      annualReturn: 0.09,
      fees: 0.008,
    },
  ];

  const mockIncomeSources = [
    { id: 'inc1', name: 'Pension', monthlyAmount: 1000, startAge: 65, inflationAdjusted: true },
  ];

  const mockExpenses = [
    {
      id: 'exp1',
      name: 'Housing',
      monthlyAmount: 1500,
      startAge: 0,
      isEssential: true,
      inflationAdjusted: true,
    },
    {
      id: 'exp2',
      name: 'Travel',
      monthlyAmount: 500,
      startAge: 65,
      isEssential: false,
      inflationAdjusted: true,
    },
  ];

  const baseOptions: RetirementProjectionOptions = {
    currentAge: 40,
    retirementAge: 65,
    lifeExpectancy: 90,
    accounts: mockAccounts,
    incomeSources: mockIncomeSources,
    expenses: mockExpenses,
    inflationRate: 0.025,
    taxRate: 0.15,
    socialSecurityStartAge: 67,
    socialSecurityMonthlyBenefit: 2000,
  };

  describe('calculateRetirementProjection', () => {
    it('should calculate a basic retirement projection', () => {
      const result = calculateRetirementProjection(baseOptions);

      expect(result.years.length).toBe(baseOptions.lifeExpectancy - baseOptions.currentAge + 1);
      expect(result.finalBalance).toBeGreaterThanOrEqual(0); // Final balance should not be negative
      expect(result.portfolioSurvivalAge).toBeGreaterThanOrEqual(baseOptions.currentAge);
      expect(result.incomeReplacementRatio).toBeGreaterThanOrEqual(0);

      // Check a year before retirement
      const yearBeforeRetirement = result.years.find(
        (y) => y.age === baseOptions.retirementAge - 1
      );
      expect(yearBeforeRetirement).toBeDefined();
      expect(yearBeforeRetirement?.isRetired).toBe(false);
      expect(yearBeforeRetirement?.contributions).toBeGreaterThan(0);
      expect(yearBeforeRetirement?.withdrawals).toBe(0);

      // Check a year in retirement
      const yearInRetirement = result.years.find((y) => y.age === baseOptions.retirementAge + 1);
      expect(yearInRetirement).toBeDefined();
      expect(yearInRetirement?.isRetired).toBe(true);
      // Contributions should be 0 in retirement
      expect(yearInRetirement?.contributions).toBe(0);
      // Withdrawals should be positive if cash flow is negative
      if (yearInRetirement && yearInRetirement.cashFlow < 0) {
        expect(yearInRetirement.withdrawals).toBeGreaterThan(0);
      }
    });

    it('should handle zero accounts', () => {
      const options: RetirementProjectionOptions = {
        ...baseOptions,
        accounts: [],
      };
      const result = calculateRetirementProjection(options);

      expect(result.finalBalance).toBe(0); // No accounts, final balance should be 0
      expect(result.portfolioSurvivalAge).toBe(options.currentAge); // Portfolio doesn't survive
    });

    it('should handle zero income sources and expenses', () => {
      const options: RetirementProjectionOptions = {
        ...baseOptions,
        incomeSources: [],
        expenses: [],
      };
      const result = calculateRetirementProjection(options);

      // Income and expenses should be 0 throughout
      result.years.forEach((year) => {
        expect(year.income).toBe(0);
        expect(year.expenses).toBe(0);
        expect(year.cashFlow).toBe(0);
      });
    });

    it('should handle zero inflation rate', () => {
      const options: RetirementProjectionOptions = {
        ...baseOptions,
        inflationRate: 0,
      };
      const result = calculateRetirementProjection(options);

      // Inflation factor should be 1 throughout
      result.years.forEach((year) => {
        expect(year.inflationFactor).toBeCloseTo(1, 10);
      });
    });

    it('should handle zero tax rate', () => {
      const options: RetirementProjectionOptions = {
        ...baseOptions,
        taxRate: 0,
      };
      const result = calculateRetirementProjection(options);

      // Taxes and taxable withdrawals should be 0
      result.years.forEach((year) => {
        expect(year.taxableWithdrawals).toBe(0);
        expect(year.taxes).toBe(0);
      });
    });

    it('should handle life expectancy equal to retirement age', () => {
      const options: RetirementProjectionOptions = {
        ...baseOptions,
        lifeExpectancy: baseOptions.retirementAge,
      };
      const result = calculateRetirementProjection(options);

      expect(result.years.length).toBe(options.retirementAge - options.currentAge + 1);
      // The last year should be the retirement age year
      expect(result.years[result.years.length - 1].age).toBe(options.retirementAge);
    });

    it('should handle current age equal to retirement age', () => {
      const options: RetirementProjectionOptions = {
        ...baseOptions,
        currentAge: baseOptions.retirementAge,
      };
      const result = calculateRetirementProjection(options);

      expect(result.years.length).toBe(options.lifeExpectancy - options.retirementAge + 1);
      // All years should be retirement years
      result.years.forEach((year) => {
        expect(year.isRetired).toBe(true);
        expect(year.contributions).toBe(0);
      });
    });

    it('should handle current age greater than life expectancy (edge case)', () => {
      const options: RetirementProjectionOptions = {
        ...baseOptions,
        currentAge: baseOptions.lifeExpectancy + 1,
      };
      const result = calculateRetirementProjection(options);

      expect(result.years.length).toBe(0); // No years to project
      expect(result.finalBalance).toBe(0); // Or initial balance? The code initializes totalBalance before the loop.
      // Re-checking code: totalBalance is initialized with current account balances.
      const initialTotalBalance = options.accounts.reduce(
        (sum, account) => sum + account.balance,
        0
      );
      expect(result.finalBalance).toBe(initialTotalBalance); // Should return initial balance if no projection years
      expect(result.portfolioSurvivalAge).toBe(options.currentAge); // Or life expectancy? The code sets it after the loop.
      // Re-checking code: portfolioSurvivalAge is set to the age of the last projected year + 1 if finalBalance > 0, otherwise the last year's age.
      // If years is empty, the loop is skipped. portfolioSurvivalAge is initialized implicitly or based on the last year.
      // It seems it would default to currentAge or similar. Let's test for currentAge as a reasonable outcome.
      expect(result.portfolioSurvivalAge).toBe(options.currentAge);
      expect(result.incomeReplacementRatio).toBeNaN(); // Division by zero as current income will be 0
    });

    it('should handle negative balances or contributions (although input validation should prevent this)', () => {
      const options: RetirementProjectionOptions = {
        ...baseOptions,
        accounts: [
          {
            id: 'acc1',
            name: 'Negative Account',
            balance: -10000,
            type: 'taxable' as AccountType,
            annualContribution: -1000,
            annualReturn: 0.05,
            fees: 0,
          },
        ],
        expenses: [
          {
            id: 'exp1',
            name: 'Negative Expense',
            monthlyAmount: -100,
            startAge: 0,
            isEssential: true,
            inflationAdjusted: true,
          },
        ], // Negative expense should increase cash flow
      };
      const result = calculateRetirementProjection(options);

      expect(result.finalBalance).toBeGreaterThanOrEqual(0); // Final balance should still be non-negative after Math.max(0, ...)
      // Check if calculations handle negative values without errors
      result.years.forEach((year) => {
        expect(year.beginningBalance).not.toBeNaN();
        expect(year.endingBalance).not.toBeNaN();
      });
    });

    it('should handle zero social security benefit', () => {
      const options: RetirementProjectionOptions = {
        ...baseOptions,
        socialSecurityMonthlyBenefit: 0,
      };
      const result = calculateRetirementProjection(options);

      // Social Security income should be 0 throughout
      result.years.forEach((year) => {
        // Check income calculation in retirement years
        if (year.age >= options.retirementAge && year.age < options.socialSecurityStartAge) {
          // Income should only be from other sources before SS starts
          const expectedIncome = options.incomeSources.reduce((sum, source) => {
            if (source.startAge <= year.age && (!source.endAge || source.endAge >= year.age)) {
              const baseAmount = source.monthlyAmount * 12;
              return (
                sum + (source.inflationAdjusted ? baseAmount * year.inflationFactor : baseAmount)
              );
            }
            return sum;
          }, 0);
          expect(year.income).toBeCloseTo(expectedIncome, 2);
        } else if (year.age >= options.socialSecurityStartAge) {
          // Income should be from other sources + SS (which is 0 in this test)
          const expectedIncome = options.incomeSources.reduce((sum, source) => {
            if (source.startAge <= year.age && (!source.endAge || source.endAge >= year.age)) {
              const baseAmount = source.monthlyAmount * 12;
              return (
                sum + (source.inflationAdjusted ? baseAmount * year.inflationFactor : baseAmount)
              );
            }
            return sum;
          }, 0);
          expect(year.income).toBeCloseTo(expectedIncome, 2);
        }
      });
    });

    it('should handle social security starting before retirement age', () => {
      const options: RetirementProjectionOptions = {
        ...baseOptions,
        socialSecurityStartAge: baseOptions.retirementAge - 2,
      };
      const result = calculateRetirementProjection(options);

      // Social Security income should be included from the earlier age
      result.years.forEach((year) => {
        if (year.age >= options.socialSecurityStartAge && year.age < options.retirementAge) {
          // Income should include SS before retirement
          const expectedIncome =
            options.incomeSources.reduce((sum, source) => {
              if (source.startAge <= year.age && (!source.endAge || source.endAge >= year.age)) {
                const baseAmount = source.monthlyAmount * 12;
                return (
                  sum + (source.inflationAdjusted ? baseAmount * year.inflationFactor : baseAmount)
                );
              }
              return sum;
            }, 0) +
            options.socialSecurityMonthlyBenefit * 12 * year.inflationFactor;
          expect(year.income).toBeCloseTo(expectedIncome, 2);
        }
      });
    });

    it('should handle social security starting after life expectancy (effectively never starts)', () => {
      const options: RetirementProjectionOptions = {
        ...baseOptions,
        socialSecurityStartAge: baseOptions.lifeExpectancy + 1,
      };
      const result = calculateRetirementProjection(options);

      // Social Security income should never be included
      result.years.forEach((year) => {
        const expectedIncome = options.incomeSources.reduce((sum, source) => {
          if (source.startAge <= year.age && (!source.endAge || source.endAge >= year.age)) {
            const baseAmount = source.monthlyAmount * 12;
            return (
              sum + (source.inflationAdjusted ? baseAmount * year.inflationFactor : baseAmount)
            );
          }
          return sum;
        }, 0);
        expect(year.income).toBeCloseTo(expectedIncome, 2);
      });
    });

    it('should handle income sources with end ages', () => {
      const options: RetirementProjectionOptions = {
        ...baseOptions,
        incomeSources: [
          {
            id: 'inc1',
            name: 'Part-time Job',
            monthlyAmount: 500,
            startAge: 40,
            endAge: 60,
            inflationAdjusted: false,
          },
        ],
      };
      const result = calculateRetirementProjection(options);

      // Income should stop at the end age
      result.years.forEach((year) => {
        if (year.age <= 60) {
          expect(year.income).toBeCloseTo(500 * 12, 2); // Non-inflation adjusted income
        } else {
          expect(year.income).toBe(0);
        }
      });
    });

    it('should handle expenses with end ages', () => {
      const options: RetirementProjectionOptions = {
        ...baseOptions,
        expenses: [
          {
            id: 'exp1',
            name: 'Childcare',
            monthlyAmount: 800,
            startAge: 40,
            endAge: 50,
            isEssential: true,
            inflationAdjusted: true,
          },
        ],
      };
      const result = calculateRetirementProjection(options);

      // Expenses should stop at the end age
      result.years.forEach((year) => {
        if (year.age <= 50) {
          const expectedExpense = 800 * 12 * year.inflationFactor;
          expect(year.expenses).toBeCloseTo(expectedExpense, 2);
        } else {
          expect(year.expenses).toBe(0);
        }
      });
    });

    it('should correctly calculate income replacement ratio', () => {
      const options: RetirementProjectionOptions = {
        ...baseOptions,
        currentAge: 64, // One year before retirement
        retirementAge: 65,
        lifeExpectancy: 90,
        accounts: mockAccounts,
        incomeSources: [
          { id: 'inc1', name: 'Salary', monthlyAmount: 8000, startAge: 0, inflationAdjusted: true },
        ],
        expenses: mockExpenses,
        inflationRate: 0.025,
        taxRate: 0.15,
        socialSecurityStartAge: 67,
        socialSecurityMonthlyBenefit: 2000,
      };

      const result = calculateRetirementProjection(options);

      // Current annual income before retirement
      const currentAnnualIncome = options.incomeSources.reduce((sum, source) => {
        if (
          source.startAge <= options.currentAge &&
          (!source.endAge || source.endAge >= options.currentAge)
        ) {
          const baseAmount = source.monthlyAmount * 12;
          // Need inflation factor at current age? The code calculates it based on index i, starting from 0.
          // For currentAge, i is 0, so inflation factor is 1.
          return sum + baseAmount;
        }
        return sum;
      }, 0);
      expect(currentAnnualIncome).toBeCloseTo(8000 * 12, 2);

      // First year retirement income (age 65)
      const firstRetirementYear = result.years.find((y) => y.age === options.retirementAge);
      const firstRetirementIncome = firstRetirementYear ? firstRetirementYear.income : 0;

      // Income replacement ratio = First retirement income / Current annual income
      const expectedRatio =
        currentAnnualIncome > 0 ? firstRetirementIncome / currentAnnualIncome : NaN;
      expect(result.incomeReplacementRatio).toBeCloseTo(expectedRatio, 4);
    });
  });

  describe('runMonteCarloSimulation', () => {
    it('should run multiple simulations and provide results', () => {
      const simulationCount = 100;
      const result = runMonteCarloSimulation(baseOptions, simulationCount);

      expect(result.simulations.length).toBe(simulationCount);
      expect(result.successProbability).toBeGreaterThanOrEqual(0);
      expect(result.successProbability).toBeLessThanOrEqual(1);
      expect(result.medianEndingBalance).toBeGreaterThanOrEqual(0);
      expect(result.medianPortfolioSurvivalAge).toBeGreaterThanOrEqual(baseOptions.currentAge);
    });

    it('should handle zero simulation count', () => {
      const result = runMonteCarloSimulation(baseOptions, 0);
      expect(result.simulations.length).toBe(0);
      expect(result.successProbability).toBeNaN(); // No simulations, probability is NaN or 0 depending on implementation
      // Re-checking code: if simulationCount is 0, successCount will be 0. 0/0 is NaN.
      expect(result.successProbability).toBeNaN();
      expect(result.medianEndingBalance).toBeNaN();
      expect(result.medianPortfolioSurvivalAge).toBeNaN();
    });

    it('should handle options leading to guaranteed failure (e.g., no savings, high expenses)', () => {
      const options: RetirementProjectionOptions = {
        ...baseOptions,
        accounts: [],
        incomeSources: [],
        expenses: [
          {
            id: 'exp1',
            name: 'High Expenses',
            monthlyAmount: 10000,
            startAge: 65,
            isEssential: true,
            inflationAdjusted: true,
          },
        ],
        retirementAge: 65,
        lifeExpectancy: 70, // Short retirement duration
      };
      const simulationCount = 100;
      const result = runMonteCarloSimulation(options, simulationCount);

      expect(result.successProbability).toBe(0); // Should always fail
      expect(result.medianPortfolioSurvivalAge).toBeLessThan(options.lifeExpectancy); // Portfolio should not last
    });

    it('should handle options leading to guaranteed success (e.g., high savings, low expenses)', () => {
      const options: RetirementProjectionOptions = {
        ...baseOptions,
        accounts: [
          {
            id: 'acc1',
            name: 'Huge Savings',
            balance: 5000000,
            type: 'taxable' as AccountType,
            annualContribution: 0,
            annualReturn: 0.05,
            fees: 0,
          },
        ],
        incomeSources: [
          {
            id: 'inc1',
            name: 'Huge Pension',
            monthlyAmount: 20000,
            startAge: 65,
            inflationAdjusted: true,
          },
        ],
        expenses: [
          {
            id: 'exp1',
            name: 'Low Expenses',
            monthlyAmount: 1000,
            startAge: 65,
            isEssential: true,
            inflationAdjusted: true,
          },
        ],
        retirementAge: 65,
        lifeExpectancy: 100, // Very long life expectancy
      };
      const simulationCount = 100;
      const result = runMonteCarloSimulation(options, simulationCount);

      expect(result.successProbability).toBe(1); // Should always succeed
      expect(result.medianPortfolioSurvivalAge).toBeGreaterThanOrEqual(options.lifeExpectancy); // Portfolio should last
    });

    // Note: Testing the statistical accuracy of Monte Carlo requires a large number of simulations
    // and is beyond the scope of typical unit tests. The focus here is on ensuring the function runs
    // and provides outputs in the expected format and handles edge cases in input options.
  });
});
