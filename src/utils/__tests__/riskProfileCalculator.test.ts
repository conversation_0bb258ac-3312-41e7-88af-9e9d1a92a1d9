/**
 * Risk Profile Calculator Utility Tests
 */

import {
  calculateRiskScore,
  determineRiskTolerance,
  getRecommendedAllocation,
  getRecommendedInvestments,
  adjustRiskProfile,
  getAgeBasedAllocation,
  generateRiskProfileReport,
  RiskTolerance,
  InvestmentHorizon,
  FinancialSituation,
} from '../riskProfileCalculator';

describe('Risk Profile Calculator', () => {
  describe('calculateRiskScore', () => {
    it('should calculate a score for average responses', () => {
      const answers = [3, 3, 3, 3, 3]; // Average of 3
      const score = calculateRiskScore(answers);
      // ((3 - 1) / 4) * 100 = (2 / 4) * 100 = 0.5 * 100 = 50
      expect(score).toBeCloseTo(50, 2);
    });

    it('should calculate a score for all low responses', () => {
      const answers = [1, 1, 1, 1, 1]; // Average of 1
      const score = calculateRiskScore(answers);
      // ((1 - 1) / 4) * 100 = 0
      expect(score).toBe(0);
    });

    it('should calculate a score for all high responses', () => {
      const answers = [5, 5, 5, 5, 5]; // Average of 5
      const score = calculateRiskScore(answers);
      // ((5 - 1) / 4) * 100 = 100
      expect(score).toBe(100);
    });

    it('should handle mixed responses', () => {
      const answers = [1, 2, 3, 4, 5]; // Average of 3
      const score = calculateRiskScore(answers);
      // ((3 - 1) / 4) * 100 = 50
      expect(score).toBeCloseTo(50, 2);
    });

    it('should handle empty answers array', () => {
      const answers: number[] = [];
      const score = calculateRiskScore(answers);
      expect(score).toBe(0);
    });

    it('should handle non-numeric values in answers', () => {
      // @ts-ignore // Intentionally testing with invalid input
      const answers = [1, 2, '3', 4, 5];
      const score = calculateRiskScore(answers);
      // Valid scores are [1, 2, 4, 5]. Average is (1+2+4+5)/4 = 12/4 = 3
      expect(score).toBeCloseTo(50, 2);
    });

    it('should handle scores outside the 1-5 range', () => {
      const answers = [0, 1, 5, 6];
      const score = calculateRiskScore(answers);
      // Valid scores are [1, 5]. Average is (1+5)/2 = 3
      expect(score).toBeCloseTo(50, 2);
    });

    it('should handle array with only invalid scores', () => {
      // @ts-ignore // Intentionally testing with invalid input
      const answers = ['a', 0, 6];
      const score = calculateRiskScore(answers);
      expect(score).toBe(0);
    });
  });

  describe('determineRiskTolerance', () => {
    it('should return conservative for score < 20', () => {
      expect(determineRiskTolerance(19.99)).toBe('conservative');
    });

    it('should return moderate for score between 20 and 40', () => {
      expect(determineRiskTolerance(20)).toBe('moderate');
      expect(determineRiskTolerance(39.99)).toBe('moderate');
    });

    it('should return balanced for score between 40 and 60', () => {
      expect(determineRiskTolerance(40)).toBe('balanced');
      expect(determineRiskTolerance(59.99)).toBe('balanced');
    });

    it('should return growth for score between 60 and 80', () => {
      expect(determineRiskTolerance(60)).toBe('growth');
      expect(determineRiskTolerance(79.99)).toBe('growth');
    });

    it('should return aggressive for score >= 80', () => {
      expect(determineRiskTolerance(80)).toBe('aggressive');
      expect(determineRiskTolerance(100)).toBe('aggressive');
      expect(determineRiskTolerance(150)).toBe('aggressive'); // Handles scores > 100
    });

    it('should return conservative for negative scores', () => {
      expect(determineRiskTolerance(-10)).toBe('conservative');
    });
  });

  describe('getRecommendedAllocation', () => {
    it('should return correct allocation for conservative', () => {
      expect(getRecommendedAllocation('conservative')).toEqual({
        stocks: 20,
        bonds: 50,
        cash: 20,
        alternative: 10,
      });
    });

    it('should return correct allocation for aggressive', () => {
      expect(getRecommendedAllocation('aggressive')).toEqual({
        stocks: 90,
        bonds: 5,
        cash: 0,
        alternative: 5,
      });
    });

    it('should return balanced allocation for unknown risk tolerance', () => {
      // @ts-ignore // Intentionally testing with invalid input
      expect(getRecommendedAllocation('unknown')).toEqual({
        stocks: 60,
        bonds: 30,
        cash: 5,
        alternative: 5,
      });
    });
  });

  describe('getRecommendedInvestments', () => {
    it('should return correct investments for conservative', () => {
      const expected = [
        'High-quality bonds',
        'Money market funds',
        'Dividend-paying stocks',
        'Short-term CDs',
        'Treasury securities',
      ];
      expect(getRecommendedInvestments('conservative')).toEqual(expected);
    });

    it('should return correct investments for aggressive', () => {
      const expected = [
        'Small-cap growth stocks',
        'Emerging market stocks',
        'Cryptocurrencies',
        'Venture capital',
        'Leveraged ETFs',
      ];
      expect(getRecommendedInvestments('aggressive')).toEqual(expected);
    });

    it('should return empty array for unknown risk tolerance', () => {
      // @ts-ignore // Intentionally testing with invalid input
      expect(getRecommendedInvestments('unknown')).toEqual([]);
    });
  });

  describe('adjustRiskProfile', () => {
    // Base cases
    it('should not adjust for balanced horizon and situation', () => {
      expect(adjustRiskProfile('balanced', 'medium', 'somewhat_stable')).toBe('balanced');
    });

    // Horizon adjustments
    it('should decrease risk for short horizon', () => {
      expect(adjustRiskProfile('balanced', 'short', 'somewhat_stable')).toBe('moderate');
      expect(adjustRiskProfile('growth', 'short', 'somewhat_stable')).toBe('balanced');
      expect(adjustRiskProfile('conservative', 'short', 'somewhat_stable')).toBe('conservative'); // Lower bound
    });

    it('should increase risk for long horizon', () => {
      expect(adjustRiskProfile('balanced', 'long', 'somewhat_stable')).toBe('growth');
      expect(adjustRiskProfile('moderate', 'long', 'somewhat_stable')).toBe('balanced');
      expect(adjustRiskProfile('aggressive', 'long', 'somewhat_stable')).toBe('aggressive'); // Upper bound
    });

    // Financial situation adjustments
    it('should decrease risk for in_decline situation', () => {
      expect(adjustRiskProfile('balanced', 'medium', 'in_decline')).toBe('moderate');
      expect(adjustRiskProfile('growth', 'medium', 'in_decline')).toBe('balanced');
      expect(adjustRiskProfile('conservative', 'medium', 'in_decline')).toBe('conservative'); // Lower bound
    });

    it('should increase risk for stable situation', () => {
      expect(adjustRiskProfile('balanced', 'medium', 'stable')).toBe('growth');
      expect(adjustRiskProfile('moderate', 'medium', 'stable')).toBe('balanced');
      expect(adjustRiskProfile('aggressive', 'medium', 'stable')).toBe('aggressive'); // Upper bound
    });

    // Combined adjustments
    it('should decrease risk by two levels for short horizon and in_decline situation', () => {
      expect(adjustRiskProfile('balanced', 'short', 'in_decline')).toBe('conservative');
      expect(adjustRiskProfile('growth', 'short', 'in_decline')).toBe('moderate');
    });

    it('should increase risk by two levels for long horizon and stable situation', () => {
      expect(adjustRiskProfile('balanced', 'long', 'stable')).toBe('aggressive');
      expect(adjustRiskProfile('moderate', 'long', 'stable')).toBe('growth');
    });

    it('should balance out adjustments', () => {
      expect(adjustRiskProfile('balanced', 'short', 'stable')).toBe('balanced');
      expect(adjustRiskProfile('balanced', 'long', 'in_decline')).toBe('balanced');
    });
  });

  describe('getAgeBasedAllocation', () => {
    it('should return 90% for age 20 or less', () => {
      expect(getAgeBasedAllocation(20)).toBe(90);
      expect(getAgeBasedAllocation(10)).toBe(90);
    });

    it('should return 30% for age 70 or more', () => {
      expect(getAgeBasedAllocation(70)).toBe(30);
      expect(getAgeBasedAllocation(80)).toBe(30);
    });

    it('should calculate allocation for ages between 20 and 70 with default base', () => {
      expect(getAgeBasedAllocation(30)).toBe(80); // 110 - 30
      expect(getAgeBasedAllocation(50)).toBe(60); // 110 - 50
      expect(getAgeBasedAllocation(69)).toBe(41); // 110 - 69
    });

    it('should calculate allocation for ages between 20 and 70 with custom base', () => {
      expect(getAgeBasedAllocation(30, 100)).toBe(70); // 100 - 30
      expect(getAgeBasedAllocation(50, 120)).toBe(70); // 120 - 50
    });

    it('should cap allocation at 90% for younger ages even with high base', () => {
      expect(getAgeBasedAllocation(25, 120)).toBe(90); // 120 - 25 = 95, capped at 90
    });

    it('should cap allocation at 30% for older ages even with low base', () => {
      expect(getAgeBasedAllocation(60, 80)).toBe(30); // 80 - 60 = 20, capped at 30
    });
  });

  describe('generateRiskProfileReport', () => {
    it('should generate a report for a balanced profile', () => {
      const riskTolerance: RiskTolerance = 'balanced';
      const investmentHorizon: InvestmentHorizon = 'medium';
      const financialSituation: FinancialSituation = 'somewhat_stable';

      const report = generateRiskProfileReport(
        riskTolerance,
        investmentHorizon,
        financialSituation
      );

      expect(report.riskTolerance).toBe('balanced');
      expect(report.allocation).toEqual({ stocks: 60, bonds: 30, cash: 5, alternative: 5 });
      expect(report.recommendedInvestments.length).toBeGreaterThan(0);
      expect(report.description).toBeDefined();
      expect(report.considerations.length).toBeGreaterThan(0);
    });

    it('should generate a report for an adjusted profile (more conservative)', () => {
      const riskTolerance: RiskTolerance = 'growth';
      const investmentHorizon: InvestmentHorizon = 'short';
      const financialSituation: FinancialSituation = 'in_decline';

      const report = generateRiskProfileReport(
        riskTolerance,
        investmentHorizon,
        financialSituation
      );

      expect(report.riskTolerance).toBe('moderate'); // Adjusted from growth
      expect(report.allocation).toEqual({ stocks: 40, bonds: 40, cash: 10, alternative: 10 });
      expect(report.recommendedInvestments.length).toBeGreaterThan(0);
      expect(report.description).toBeDefined();
      expect(report.considerations.length).toBeGreaterThan(0);
      expect(report.considerations).toContain(
        'With a short investment horizon, your portfolio is more conservative to protect your principal.'
      );
      expect(report.considerations).toContain(
        'Given your current financial situation, we recommend a more conservative approach to preserve capital.'
      );
    });

    it('should generate a report for an adjusted profile (more aggressive)', () => {
      const riskTolerance: RiskTolerance = 'moderate';
      const investmentHorizon: InvestmentHorizon = 'long';
      const financialSituation: FinancialSituation = 'stable';

      const report = generateRiskProfileReport(
        riskTolerance,
        investmentHorizon,
        financialSituation
      );

      expect(report.riskTolerance).toBe('growth'); // Adjusted from moderate
      expect(report.allocation).toEqual({ stocks: 80, bonds: 15, cash: 0, alternative: 5 });
      expect(report.recommendedInvestments.length).toBeGreaterThan(0);
      expect(report.description).toBeDefined();
      expect(report.considerations.length).toBeGreaterThan(0);
      expect(report.considerations).toContain(
        'Your long investment horizon allows for more aggressive investments to potentially achieve higher returns.'
      );
      expect(report.considerations).toContain(
        'Given your stable financial situation, a more growth-oriented approach may be suitable.'
      );
    });
  });
});
