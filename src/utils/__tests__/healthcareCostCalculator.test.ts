import { calculateHealthcareCostProjections } from '../healthcareCostCalculator';
import { HealthcareCostData } from '../../types/southDirection';

describe('Healthcare Cost Calculator', () => {
  describe('calculateHealthcareCostProjections', () => {
    // Base test data
    const baseHealthcareData: HealthcareCostData = {
      currentAge: '45',
      retirementAge: '65',
      lifeExpectancy: '90',
      annualInflation: '3.5',
      currentAnnualHealthcareCost: '5000',
      medicareStartAge: '65',
      medicarePartBPremium: '170.10',
      medicarePartDPremium: '33',
      medicareSupplementPremium: '150',
      outOfPocketCosts: '1500',
      longTermCareStartAge: '80',
      longTermCareCost: '5000',
      longTermCareDuration: '3',
    };

    it('should project healthcare costs correctly over time', () => {
      const projections = calculateHealthcareCostProjections(baseHealthcareData);

      expect(projections.length).toBe(90 - 45 + 1); // From current age to life expectancy

      // Check initial year (before Medicare and LTC)
      const initialYear = projections[0]; // Age 45
      expect(initialYear.age).toBe(45);
      expect(initialYear.year).toBe(new Date().getFullYear());
      expect(initialYear.medicarePartB).toBe(0);
      expect(initialYear.medicarePartD).toBe(0);
      expect(initialYear.medicareSupplement).toBe(0);
      // Out-of-pocket should be current annual cost with inflation (inflation factor is 1 for year 0)
      expect(initialYear.outOfPocket).toBeCloseTo(
        parseFloat(baseHealthcareData.currentAnnualHealthcareCost) || 5000,
        2
      );
      expect(initialYear.longTermCare).toBe(0);
      expect(initialYear.total).toBeCloseTo(
        parseFloat(baseHealthcareData.currentAnnualHealthcareCost) || 5000,
        2
      );
      expect(initialYear.cumulativeTotal).toBeCloseTo(
        parseFloat(baseHealthcareData.currentAnnualHealthcareCost) || 5000,
        2
      );

      // Check a year in retirement before Medicare
      const yearBeforeMedicare = projections.find((p) => p.age === 64); // Age 64
      expect(yearBeforeMedicare).toBeDefined();
      expect(yearBeforeMedicare?.medicarePartB).toBe(0);
      expect(yearBeforeMedicare?.medicarePartD).toBe(0);
      expect(yearBeforeMedicare?.medicareSupplement).toBe(0);
      // Out-of-pocket should be current annual cost with inflation
      const yearsTo64 = 64 - 45;
      const inflationFactorTo64 = Math.pow(
        1 + (parseFloat(baseHealthcareData.annualInflation) / 100 || 0.035),
        yearsTo64
      );
      expect(yearBeforeMedicare?.outOfPocket).toBeCloseTo(
        (parseFloat(baseHealthcareData.currentAnnualHealthcareCost) || 5000) * inflationFactorTo64,
        2
      );
      expect(yearBeforeMedicare?.longTermCare).toBe(0);
      expect(yearBeforeMedicare?.total).toBeCloseTo(
        (parseFloat(baseHealthcareData.currentAnnualHealthcareCost) || 5000) * inflationFactorTo64,
        2
      );

      // Check a year after Medicare starts but before LTC
      const yearAfterMedicare = projections.find((p) => p.age === 70); // Age 70
      expect(yearAfterMedicare).toBeDefined();
      const yearsTo70 = 70 - 45;
      const inflationFactorTo70 = Math.pow(
        1 + (parseFloat(baseHealthcareData.annualInflation) / 100 || 0.035),
        yearsTo70
      );
      // Medicare costs should be present and inflated
      expect(yearAfterMedicare?.medicarePartB).toBeCloseTo(
        (parseFloat(baseHealthcareData.medicarePartBPremium) * 12 || 170.1 * 12) *
          inflationFactorTo70,
        2
      );
      expect(yearAfterMedicare?.medicarePartD).toBeCloseTo(
        (parseFloat(baseHealthcareData.medicarePartDPremium) * 12 || 33 * 12) * inflationFactorTo70,
        2
      );
      expect(yearAfterMedicare?.medicareSupplement).toBeCloseTo(
        (parseFloat(baseHealthcareData.medicareSupplementPremium) * 12 || 150 * 12) *
          inflationFactorTo70,
        2
      );
      // Out-of-pocket should be specified out-of-pocket cost with inflation
      expect(yearAfterMedicare?.outOfPocket).toBeCloseTo(
        (parseFloat(baseHealthcareData.outOfPocketCosts) || 1500) * inflationFactorTo70,
        2
      );
      expect(yearAfterMedicare?.longTermCare).toBe(0);
      expect(yearAfterMedicare?.total).toBeCloseTo(
        ((parseFloat(baseHealthcareData.medicarePartBPremium) * 12 || 170.1 * 12) +
          (parseFloat(baseHealthcareData.medicarePartDPremium) * 12 || 33 * 12) +
          (parseFloat(baseHealthcareData.medicareSupplementPremium) * 12 || 150 * 12) +
          (parseFloat(baseHealthcareData.outOfPocketCosts) || 1500)) *
          inflationFactorTo70,
        2
      );

      // Check a year during LTC
      const yearDuringLTC = projections.find((p) => p.age === 81); // Age 81 (within 80-83 duration)
      expect(yearDuringLTC).toBeDefined();
      const yearsTo81 = 81 - 45;
      const inflationFactorTo81 = Math.pow(
        1 + (parseFloat(baseHealthcareData.annualInflation) / 100 || 0.035),
        yearsTo81
      );
      // LTC cost should be present and inflated
      expect(yearDuringLTC?.longTermCare).toBeCloseTo(
        (parseFloat(baseHealthcareData.longTermCareCost) * 12 || 5000 * 12) * inflationFactorTo81,
        2
      );
      expect(yearDuringLTC?.total).toBeCloseTo(
        ((parseFloat(baseHealthcareData.medicarePartBPremium) * 12 || 170.1 * 12) +
          (parseFloat(baseHealthcareData.medicarePartDPremium) * 12 || 33 * 12) +
          (parseFloat(baseHealthcareData.medicareSupplementPremium) * 12 || 150 * 12) +
          (parseFloat(baseHealthcareData.outOfPocketCosts) || 1500) +
          (parseFloat(baseHealthcareData.longTermCareCost) * 12 || 5000 * 12)) *
          inflationFactorTo81,
        2
      );

      // Check cumulative total in the final year
      const finalYear = projections[projections.length - 1];
      expect(finalYear.cumulativeTotal).toBeGreaterThan(0);
      // The last cumulative total should be the sum of all annual totals
      const sumOfTotals = projections.reduce((sum, p) => sum + p.total, 0);
      expect(finalYear.cumulativeTotal).toBeCloseTo(sumOfTotals, 2);
    });

    it('should handle zero inflation', () => {
      const data: HealthcareCostData = { ...baseHealthcareData, annualInflation: '0' };
      const projections = calculateHealthcareCostProjections(data);

      // Costs should not increase over time
      const initialYearTotal = projections[0].total;
      projections.forEach((p) => {
        // Medicare and LTC costs should be constant (unless start age changes)
        if (p.age >= 65 && p.age < 80) {
          expect(p.total).toBeCloseTo(
            (parseFloat(data.medicarePartBPremium) * 12 || 170.1 * 12) +
              (parseFloat(data.medicarePartDPremium) * 12 || 33 * 12) +
              (parseFloat(data.medicareSupplementPremium) * 12 || 150 * 12) +
              (parseFloat(data.outOfPocketCosts) || 1500),
            2
          );
        } else if (p.age < 65) {
          expect(p.total).toBeCloseTo(parseFloat(data.currentAnnualHealthcareCost) || 5000, 2);
        } else if (p.age >= 80 && p.age < 80 + 3) {
          expect(p.total).toBeCloseTo(
            (parseFloat(data.medicarePartBPremium) * 12 || 170.1 * 12) +
              (parseFloat(data.medicarePartDPremium) * 12 || 33 * 12) +
              (parseFloat(data.medicareSupplementPremium) * 12 || 150 * 12) +
              (parseFloat(data.outOfPocketCosts) || 1500) +
              (parseFloat(data.longTermCareCost) * 12 || 5000 * 12),
            2
          );
        }
      });
    });

    it('should handle life expectancy equal to current age', () => {
      const data: HealthcareCostData = {
        ...baseHealthcareData,
        lifeExpectancy: baseHealthcareData.currentAge,
      };
      const projections = calculateHealthcareCostProjections(data);
      expect(projections.length).toBe(1); // Only one year projected
      expect(projections[0].age).toBe(parseInt(data.currentAge));
    });

    it('should handle current age greater than life expectancy', () => {
      const data: HealthcareCostData = {
        ...baseHealthcareData,
        currentAge: '95',
        lifeExpectancy: '90',
      };
      const projections = calculateHealthcareCostProjections(data);
      expect(projections.length).toBe(0); // No years to project
    });

    it('should handle Medicare starting before current age', () => {
      const data: HealthcareCostData = {
        ...baseHealthcareData,
        currentAge: '70',
        medicareStartAge: '65',
      };
      const projections = calculateHealthcareCostProjections(data);

      // All projected years should include Medicare costs
      projections.forEach((p) => {
        expect(p.age).toBeGreaterThanOrEqual(70);
        expect(p.medicarePartB).toBeGreaterThan(0);
        expect(p.medicarePartD).toBeGreaterThan(0);
        expect(p.medicareSupplement).toBeGreaterThan(0);
      });
    });

    it('should handle LTC starting before current age', () => {
      const data: HealthcareCostData = {
        ...baseHealthcareData,
        currentAge: '82',
        longTermCareStartAge: '80',
        longTermCareDuration: '5',
      };
      const projections = calculateHealthcareCostProjections(data);

      // Projected years within the LTC duration should include LTC costs
      projections.forEach((p) => {
        expect(p.age).toBeGreaterThanOrEqual(82);
        if (p.age < 80 + 5) {
          // Within original 80-85 duration
          expect(p.longTermCare).toBeGreaterThan(0);
        } else {
          expect(p.longTermCare).toBe(0);
        }
      });
    });

    it('should handle zero input values gracefully', () => {
      const data: HealthcareCostData = {
        currentAge: '0',
        retirementAge: '0',
        lifeExpectancy: '0',
        annualInflation: '0',
        currentAnnualHealthcareCost: '0',
        medicareStartAge: '0',
        medicarePartBPremium: '0',
        medicarePartDPremium: '0',
        medicareSupplementPremium: '0',
        outOfPocketCosts: '0',
        longTermCareStartAge: '0',
        longTermCareCost: '0',
        longTermCareDuration: '0',
      };
      const projections = calculateHealthcareCostProjections(data);

      // Should produce a result, but all costs should be zero
      expect(projections.length).toBeGreaterThan(0); // Still projects from default current age (45) to default life expectancy (90)
      projections.forEach((p) => {
        expect(p.medicarePartB).toBe(0);
        expect(p.medicarePartD).toBe(0);
        expect(p.medicareSupplement).toBe(0);
        expect(p.outOfPocket).toBe(0);
        expect(p.longTermCare).toBe(0);
        expect(p.total).toBe(0);
        expect(p.cumulativeTotal).toBe(0);
      });
    });

    it('should handle non-numeric string inputs by using defaults', () => {
      const data: HealthcareCostData = {
        currentAge: 'abc',
        retirementAge: 'def',
        lifeExpectancy: 'ghi',
        annualInflation: 'jkl',
        currentAnnualHealthcareCost: 'mno',
        medicareStartAge: 'pqr',
        medicarePartBPremium: 'stu',
        medicarePartDPremium: 'vwx',
        medicareSupplementPremium: 'yza',
        outOfPocketCosts: 'bcd',
        longTermCareStartAge: 'efg',
        longTermCareCost: 'hij',
        longTermCareDuration: 'klm',
      };
      const projections = calculateHealthcareCostProjections(data);

      // Should use default values and produce a projection
      expect(projections.length).toBe(90 - 45 + 1); // Default ages 45 to 90
      const initialYear = projections[0]; // Should use default currentAnnualHealthcareCost (5000)
      expect(initialYear.total).toBeCloseTo(5000, 2);
      // Medicare and LTC costs should be calculated based on defaults as well
      const yearAfterMedicare = projections.find((p) => p.age === 70); // Default medicareStartAge 65
      expect(yearAfterMedicare?.medicarePartB).toBeCloseTo(
        170.1 * 12 * Math.pow(1 + 0.035, 70 - 45),
        2
      );
    });

    it('should correctly calculate cumulative total', () => {
      const projections = calculateHealthcareCostProjections(baseHealthcareData);
      let runningTotal = 0;
      projections.forEach((p) => {
        runningTotal += p.total;
        expect(p.cumulativeTotal).toBeCloseTo(runningTotal, 2);
      });
    });

    it('should handle long term care duration of zero', () => {
      const data: HealthcareCostData = { ...baseHealthcareData, longTermCareDuration: '0' };
      const projections = calculateHealthcareCostProjections(data);
      const yearDuringLTCStart = projections.find(
        (p) => p.age === parseInt(data.longTermCareStartAge)
      );
      expect(yearDuringLTCStart?.longTermCare).toBe(0); // LTC cost should be 0 if duration is 0
    });

    it('should handle long term care starting after life expectancy', () => {
      const data: HealthcareCostData = {
        ...baseHealthcareData,
        longTermCareStartAge: '95',
        lifeExpectancy: '90',
      };
      const projections = calculateHealthcareCostProjections(data);
      projections.forEach((p) => {
        expect(p.longTermCare).toBe(0); // LTC should never start
      });
    });
  });
});
