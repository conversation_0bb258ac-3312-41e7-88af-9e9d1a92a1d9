import {
  calculateFutureValueWithInflation,
  calculatePresentValueWithInflation,
  projectRetirementExpenses,
  calculateRequiredRetirementCorpus,
  calculateSafeWithdrawal,
} from '../inflationCalculator';

describe('Inflation Calculator', () => {
  describe('calculateFutureValueWithInflation', () => {
    it('calculates future value with default inflation rate', () => {
      const result = calculateFutureValueWithInflation(1000, 10);
      expect(result).toBeCloseTo(1280.084544713706, 10);
    });

    it('handles zero inflation', () => {
      const result = calculateFutureValueWithInflation(1000, 10, 0);
      expect(result).toBe(1000);
    });

    it('throws error for negative years', () => {
      expect(() => calculateFutureValueWithInflation(1000, -1)).toThrow(
        'Years must be non-negative and inflation rate must be >= -1'
      );
    });

    it('handles zero current value', () => {
      expect(calculateFutureValueWithInflation(0, 10)).toBe(0);
    });

    it('handles negative current value', () => {
      expect(calculateFutureValueWithInflation(-1000, 10)).toBeCloseTo(-1280.084544713706, 10);
    });

    it('handles zero years', () => {
      expect(calculateFutureValueWithInflation(1000, 0)).toBe(1000);
    });

    it('handles large current value', () => {
      expect(calculateFutureValueWithInflation(1000000000, 10)).toBeCloseTo(1280084544.713706, 4);
    });

    it('handles fractional current value', () => {
      expect(calculateFutureValueWithInflation(100.5, 10)).toBeCloseTo(128.648557, 6);
    });

    it('handles inflation rate equal to -1 (total loss)', () => {
      expect(calculateFutureValueWithInflation(1000, 10, -1)).toBe(0);
    });

    it('throws error for inflation rate less than -1', () => {
      expect(() => calculateFutureValueWithInflation(1000, 10, -1.1)).toThrow(
        'Years must be non-negative and inflation rate must be >= -1'
      );
    });
  });

  describe('calculatePresentValueWithInflation', () => {
    it('calculates present value with default inflation rate', () => {
      const result = calculatePresentValueWithInflation(1280.084544713706, 10);
      expect(result).toBeCloseTo(1000, 10);
    });

    it('handles zero future value', () => {
      expect(calculatePresentValueWithInflation(0, 10)).toBe(0);
    });

    it('handles negative future value', () => {
      expect(calculatePresentValueWithInflation(-1280.084544713706, 10)).toBeCloseTo(-1000, 10);
    });

    it('handles zero years', () => {
      expect(calculatePresentValueWithInflation(1000, 0)).toBe(1000);
    });

    it('throws error for negative years', () => {
      expect(() => calculatePresentValueWithInflation(1000, -1)).toThrow(
        'Years must be non-negative'
      );
    });

    it('handles large future value', () => {
      expect(calculatePresentValueWithInflation(1280084544.713706, 10)).toBeCloseTo(1000000000, 4);
    });

    it('handles fractional future value', () => {
      expect(calculatePresentValueWithInflation(128.648557, 10)).toBeCloseTo(100.5, 6);
    });

    it('handles inflation rate equal to -1 (when years > 0)', () => {
      expect(calculatePresentValueWithInflation(1000, 10, -1)).toBe(Infinity);
    });

    it('throws error for inflation rate less than -1 (when years > 0)', () => {
      expect(() => calculatePresentValueWithInflation(1000, 10, -1.1)).not.toThrow();
    });
  });

  describe('projectRetirementExpenses', () => {
    it('projects expenses for retirement duration with default inflation', () => {
      const currentAnnualExpenses = 50000;
      const yearsToRetirement = 10;
      const retirementDuration = 5;
      const inflationRate = 0.025;

      const result = projectRetirementExpenses(
        currentAnnualExpenses,
        yearsToRetirement,
        retirementDuration
      );

      expect(result).toHaveLength(retirementDuration);
      expect(result[0].year).toBe(yearsToRetirement + 1);
      expect(result[0].expense).toBeCloseTo(
        currentAnnualExpenses * Math.pow(1 + inflationRate, yearsToRetirement),
        2
      );
      expect(result[4].year).toBe(yearsToRetirement + 5);
      expect(result[4].expense).toBeCloseTo(
        currentAnnualExpenses * Math.pow(1 + inflationRate, yearsToRetirement + 4),
        2
      );

      expect(result[0].cumulativeInflation).toBeCloseTo(
        (result[0].expense / currentAnnualExpenses - 1) * 100,
        2
      );
    });

    it('handles zero current annual expenses', () => {
      const result = projectRetirementExpenses(0, 10, 5);
      expect(result).toHaveLength(5);
      result.forEach((yearData) => {
        expect(yearData.expense).toBe(0);
        expect(yearData.cumulativeInflation).toBeNaN();
      });
    });

    it('handles zero years to retirement', () => {
      const currentAnnualExpenses = 50000;
      const result = projectRetirementExpenses(currentAnnualExpenses, 0, 5);
      expect(result).toHaveLength(5);
      expect(result[0].year).toBe(1);
      expect(result[0].expense).toBeCloseTo(currentAnnualExpenses * Math.pow(1 + 0.025, 0), 2);
    });

    it('throws error for zero retirement duration', () => {
      expect(() => projectRetirementExpenses(50000, 10, 0)).toThrow(
        'Years to retirement must be non-negative and retirement duration must be positive'
      );
    });

    it('throws error for negative years to retirement', () => {
      expect(() => projectRetirementExpenses(50000, -1, 5)).toThrow(
        'Years to retirement must be non-negative and retirement duration must be positive'
      );
    });

    it('throws error for negative retirement duration', () => {
      expect(() => projectRetirementExpenses(50000, 10, -5)).toThrow(
        'Years to retirement must be non-negative and retirement duration must be positive'
      );
    });

    it('handles zero inflation rate', () => {
      const currentAnnualExpenses = 50000;
      const result = projectRetirementExpenses(currentAnnualExpenses, 10, 5, 0);
      expect(result).toHaveLength(5);
      result.forEach((yearData) => {
        expect(yearData.expense).toBeCloseTo(
          currentAnnualExpenses * Math.pow(1 + 0, yearData.year - 1),
          2
        );
        expect(yearData.cumulativeInflation).toBe(0);
      });
    });

    it('handles negative inflation rate (deflation)', () => {
      const currentAnnualExpenses = 50000;
      const inflationRate = -0.01;
      const result = projectRetirementExpenses(currentAnnualExpenses, 10, 5, inflationRate);
      expect(result).toHaveLength(5);
      expect(result[0].expense).toBeCloseTo(
        currentAnnualExpenses * Math.pow(1 + inflationRate, 10),
        2
      );
      expect(result[4].expense).toBeLessThan(result[0].expense);
      expect(result[4].cumulativeInflation).toBeCloseTo(
        (result[4].expense / currentAnnualExpenses - 1) * 100,
        2
      );
    });
  });

  describe('calculateRequiredRetirementCorpus', () => {
    it('calculates required corpus with default rates', () => {
      const result = calculateRequiredRetirementCorpus(50000, 10, 30);
      expect(result).toBeCloseTo(
        (50000 * Math.pow(1.025, 10) * (1 - Math.pow((1 + 0.05) / (1 + 0.025), -30))) /
          ((1 + 0.05) / (1 + 0.025) - 1),
        2
      );
    });

    it('handles equal inflation and return rates', () => {
      const result = calculateRequiredRetirementCorpus(50000, 10, 30, 0.03, 0.03);
      const firstYearExpense = 50000 * Math.pow(1.03, 10);
      expect(result).toBeCloseTo(firstYearExpense * 30, 2);
    });

    it('handles zero current annual expenses', () => {
      expect(calculateRequiredRetirementCorpus(0, 10, 30)).toBe(0);
    });

    it('throws error for zero years to retirement', () => {
      expect(() => calculateRequiredRetirementCorpus(50000, 0, 30)).not.toThrow();
      const result = calculateRequiredRetirementCorpus(50000, 0, 30);
      expect(result).toBeCloseTo(
        (50000 * (1 - Math.pow((1 + 0.05) / (1 + 0.025), -30))) / ((1 + 0.05) / (1 + 0.025) - 1),
        2
      );
    });

    it('throws error for zero retirement duration', () => {
      expect(() => calculateRequiredRetirementCorpus(50000, 10, 0)).toThrow(
        'Invalid input parameters'
      );
    });

    it('throws error for negative years to retirement', () => {
      expect(() => calculateRequiredRetirementCorpus(50000, -1, 30)).toThrow(
        'Invalid input parameters'
      );
    });

    it('throws error for negative retirement duration', () => {
      expect(() => calculateRequiredRetirementCorpus(50000, 10, -5)).toThrow(
        'Invalid input parameters'
      );
    });

    it('handles zero inflation rate', () => {
      const result = calculateRequiredRetirementCorpus(50000, 10, 30, 0, 0.05);
      expect(result).toBeCloseTo(
        (50000 * Math.pow(1 + 0, 10) * (1 - Math.pow((1 + 0.05) / (1 + 0), -30))) /
          ((1 + 0.05) / (1 + 0) - 1),
        2
      );
    });

    it('handles zero investment return rate', () => {
      const result = calculateRequiredRetirementCorpus(50000, 10, 30, 0.025, 0);
      expect(result).toBeCloseTo(
        (50000 * Math.pow(1.025, 10) * (1 - Math.pow((1 + 0) / (1 + 0.025), -30))) /
          ((1 + 0) / (1 + 0.025) - 1),
        2
      );
    });

    it('handles negative inflation rate (deflation)', () => {
      const result = calculateRequiredRetirementCorpus(50000, 10, 30, -0.01, 0.05);
      expect(result).toBeCloseTo(
        (50000 * Math.pow(1 - 0.01, 10) * (1 - Math.pow((1 + 0.05) / (1 - 0.01), -30))) /
          ((1 + 0.05) / (1 - 0.01) - 1),
        2
      );
    });

    it('handles negative investment return rate', () => {
      const result = calculateRequiredRetirementCorpus(50000, 10, 30, 0.025, -0.01);
      expect(result).toBeCloseTo(
        (50000 * Math.pow(1.025, 10) * (1 - Math.pow((1 - 0.01) / (1 + 0.025), -30))) /
          ((1 - 0.01) / (1 + 0.025) - 1),
        2
      );
    });

    it('handles large input values', () => {
      const result = calculateRequiredRetirementCorpus(50000000, 30, 40, 0.03, 0.06);
      expect(result).toBeGreaterThan(0);
    });
  });

  describe('calculateSafeWithdrawal', () => {
    it('calculates safe withdrawal amounts with default rates', () => {
      const { initialWithdrawal, projectedWithdrawals } = calculateSafeWithdrawal(1000000);
      expect(initialWithdrawal).toBeCloseTo(40000, 2);
      expect(projectedWithdrawals).toHaveLength(30);
      expect(projectedWithdrawals[0]).toBeCloseTo(40000, 2);
      expect(projectedWithdrawals[1]).toBeCloseTo(40000 * (1 + 0.025), 2);
      expect(projectedWithdrawals[2]).toBeCloseTo(40000 * Math.pow(1 + 0.025, 2), 2);
    });

    it('handles zero retirement savings', () => {
      const { initialWithdrawal, projectedWithdrawals } = calculateSafeWithdrawal(0);
      expect(initialWithdrawal).toBe(0);
      expect(projectedWithdrawals).toHaveLength(30);
      projectedWithdrawals.forEach((amount) => expect(amount).toBe(0));
    });

    it('throws error for zero withdrawal rate', () => {
      expect(() => calculateSafeWithdrawal(1000000, 0)).toThrow(
        'Withdrawal rate must be between 0 and 1'
      );
    });

    it('throws error for withdrawal rate greater than 1', () => {
      expect(() => calculateSafeWithdrawal(1000000, 1.1)).toThrow(
        'Withdrawal rate must be between 0 and 1'
      );
    });

    it('handles negative retirement savings', () => {
      const { initialWithdrawal, projectedWithdrawals } = calculateSafeWithdrawal(-1000000);
      expect(initialWithdrawal).toBeCloseTo(-40000, 2);
      expect(projectedWithdrawals).toHaveLength(30);
      expect(projectedWithdrawals[1]).toBeCloseTo(-40000 * (1 + 0.025), 2);
    });

    it('handles zero inflation rate', () => {
      const { initialWithdrawal, projectedWithdrawals } = calculateSafeWithdrawal(1000000, 0.04, 0);
      expect(initialWithdrawal).toBe(40000);
      expect(projectedWithdrawals).toHaveLength(30);
      projectedWithdrawals.forEach((amount) => expect(amount).toBeCloseTo(40000, 2));
    });

    it('handles negative inflation rate (deflation)', () => {
      const { initialWithdrawal, projectedWithdrawals } = calculateSafeWithdrawal(
        1000000,
        0.04,
        -0.01
      );
      expect(initialWithdrawal).toBe(40000);
      expect(projectedWithdrawals).toHaveLength(30);
      expect(projectedWithdrawals[1]).toBeCloseTo(40000 * (1 - 0.01), 2);
      expect(projectedWithdrawals[29]).toBeLessThan(initialWithdrawal);
    });

    it('handles large retirement savings', () => {
      const { initialWithdrawal, projectedWithdrawals } = calculateSafeWithdrawal(1000000000);
      expect(initialWithdrawal).toBeCloseTo(40000000, 2);
      expect(projectedWithdrawals).toHaveLength(30);
      expect(projectedWithdrawals[1]).toBeCloseTo(40000000 * (1 + 0.025), 2);
    });
  });
});
