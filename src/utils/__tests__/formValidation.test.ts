import { validateField, validateForm, validationRules } from '../formValidation';

describe('Form Validation', () => {
  describe('validateField', () => {
    it('should validate required fields', () => {
      expect(validateField('', { required: true }, 'Name')).toBe('Name is required');
      expect(validateField(null, { required: true }, 'Name')).toBe('Name is required');
      expect(validateField(undefined, { required: true }, 'Name')).toBe('Name is required');
      expect(validateField('John', { required: true }, 'Name')).toBeNull();
    });

    it('should validate min value', () => {
      expect(validateField(5, { min: 10 }, 'Age')).toBe('Age must be at least 10');
      expect(validateField(10, { min: 10 }, 'Age')).toBeNull();
      expect(validateField(15, { min: 10 }, 'Age')).toBeNull();
    });

    it('should validate max value', () => {
      expect(validateField(150, { max: 100 }, 'Age')).toBe('Age must be no more than 100');
      expect(validateField(100, { max: 100 }, 'Age')).toBeNull();
      expect(validateField(50, { max: 100 }, 'Age')).toBeNull();
    });

    it('should validate with custom pattern', () => {
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      expect(validateField('invalid-email', { pattern: emailPattern }, 'Email')).toBe(
        'Invalid format for Email'
      );
      expect(validateField('<EMAIL>', { pattern: emailPattern }, 'Email')).toBeNull();
    });

    it('should validate with custom validator', () => {
      const customValidator = (value: number) => (value % 2 === 0 ? null : 'Must be even');
      expect(validateField(3, { custom: customValidator }, 'Number')).toBe('Must be even');
      expect(validateField(4, { custom: customValidator }, 'Number')).toBeNull();
    });
  });

  describe('validateForm', () => {
    const schema = {
      name: { required: true },
      age: { required: true, min: 18, max: 120 },
      email: { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ },
    };

    it('should validate a complete form', () => {
      const formData = {
        name: 'John Doe',
        age: 30,
        email: '<EMAIL>',
      };
      const result = validateForm(formData, schema);
      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual({});
    });

    it('should return errors for invalid form data', () => {
      const formData = {
        name: '',
        age: 15,
        email: 'invalid-email',
      };
      const result = validateForm(formData, schema);
      expect(result.isValid).toBe(false);
      expect(result.errors).toEqual({
        name: 'name is required',
        age: 'age must be at least 18',
        email: 'Invalid format for email',
      });
    });
  });

  describe('validationRules', () => {
    it('should include common validation rules', () => {
      expect(validationRules.required).toEqual({ required: true });
      expect(validationRules.email).toEqual({
        required: true,
        pattern: expect.any(RegExp),
      });
      expect(validationRules.positiveNumber).toEqual({
        required: true,
        custom: expect.any(Function),
      });
      expect(validationRules.percentage).toEqual({
        required: true,
        min: 0,
        max: 100,
      });
    });
  });
});
