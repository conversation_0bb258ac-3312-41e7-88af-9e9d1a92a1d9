/**
 * Expense Calculator Utility
 *
 * This utility provides standardized expense conversion and calculation functions.
 */

/**
 * Constants for expense frequency conversions
 */
export const EXPENSE_FREQUENCY = {
  DAILY: 365.25, // Days per year (accounting for leap years)
  WEEKLY: 52.1429, // Weeks per year (365.25/7)
  BIWEEKLY: 26.0714, // Biweekly periods per year (365.25/14)
  SEMI_MONTHLY: 24, // Semi-monthly periods per year
  MONTHLY: 12, // Months per year
  QUARTERLY: 4, // Quarters per year
  SEMI_ANNUALLY: 2, // Semi-annual periods per year
  ANNUALLY: 1, // Annual periods per year
};

/**
 * Budget rule targets (50/30/20 rule)
 */
export const BUDGET_RULE = {
  NEEDS_TARGET: 0.5, // 50% for needs (essential expenses)
  WANTS_TARGET: 0.3, // 30% for wants (discretionary expenses)
  SAVINGS_TARGET: 0.2, // 20% for savings
};

/**
 * Convert expense to annual amount
 *
 * @param amount - Expense amount
 * @param frequency - Expense frequency (daily, weekly, monthly, etc.)
 * @returns Annual expense amount
 */
export const convertToAnnual = (amount: number, frequency: string): number => {
  if (isNaN(amount)) return 0;

  switch (frequency.toLowerCase()) {
    case 'daily':
      return amount * EXPENSE_FREQUENCY.DAILY;
    case 'weekly':
      return amount * EXPENSE_FREQUENCY.WEEKLY;
    case 'biweekly':
      return amount * EXPENSE_FREQUENCY.BIWEEKLY;
    case 'semi-monthly':
    case 'semi_monthly':
      return amount * EXPENSE_FREQUENCY.SEMI_MONTHLY;
    case 'monthly':
      return amount * EXPENSE_FREQUENCY.MONTHLY;
    case 'quarterly':
      return amount * EXPENSE_FREQUENCY.QUARTERLY;
    case 'semi-annually':
    case 'semi_annually':
      return amount * EXPENSE_FREQUENCY.SEMI_ANNUALLY;
    case 'annually':
    case 'annual':
      return amount;
    default:
      return amount; // Default to assuming the amount is already annual
  }
};

/**
 * Convert expense to monthly amount
 *
 * @param amount - Expense amount
 * @param frequency - Expense frequency (daily, weekly, monthly, etc.)
 * @returns Monthly expense amount
 */
export const convertToMonthly = (amount: number, frequency: string): number => {
  const annual = convertToAnnual(amount, frequency);
  return annual / EXPENSE_FREQUENCY.MONTHLY;
};

/**
 * Convert expense to weekly amount
 *
 * @param amount - Expense amount
 * @param frequency - Expense frequency (daily, weekly, monthly, etc.)
 * @returns Weekly expense amount
 */
export const convertToWeekly = (amount: number, frequency: string): number => {
  const annual = convertToAnnual(amount, frequency);
  return annual / EXPENSE_FREQUENCY.WEEKLY;
};

/**
 * Convert expense to daily amount
 *
 * @param amount - Expense amount
 * @param frequency - Expense frequency (daily, weekly, monthly, etc.)
 * @returns Daily expense amount
 */
export const convertToDaily = (amount: number, frequency: string): number => {
  const annual = convertToAnnual(amount, frequency);
  return annual / EXPENSE_FREQUENCY.DAILY;
};

/**
 * Calculate total expenses from a collection of expense items
 *
 * @param expenses - Array of expense items with amount and frequency
 * @returns Total expenses in annual, monthly, and weekly amounts
 */
export const calculateTotalExpenses = (
  expenses: Array<{
    amount: number | string;
    frequency: string;
  }>
): {
  annual: number;
  monthly: number;
  weekly: number;
  daily: number;
} => {
  // Calculate total annual expenses
  const annualTotal = expenses.reduce((total, expense) => {
    const amount = typeof expense.amount === 'string' ? parseFloat(expense.amount) : expense.amount;

    if (isNaN(amount)) return total;

    return total + convertToAnnual(amount, expense.frequency);
  }, 0);

  // Calculate other frequency totals
  const monthlyTotal = annualTotal / EXPENSE_FREQUENCY.MONTHLY;
  const weeklyTotal = annualTotal / EXPENSE_FREQUENCY.WEEKLY;
  const dailyTotal = annualTotal / EXPENSE_FREQUENCY.DAILY;

  return {
    annual: annualTotal,
    monthly: monthlyTotal,
    weekly: weeklyTotal,
    daily: dailyTotal,
  };
};

/**
 * Calculate budget health based on the 50/30/20 rule
 *
 * @param essentialExpenses - Essential expenses (needs)
 * @param discretionaryExpenses - Discretionary expenses (wants)
 * @param savingsAmount - Savings amount
 * @param totalExpenses - Total expenses including savings
 * @returns Budget health assessment
 */
export const calculateBudgetHealth = (
  essentialExpenses: number,
  discretionaryExpenses: number,
  savingsAmount: number,
  totalExpenses: number
): {
  needsRatio: number;
  wantsRatio: number;
  savingsRatio: number;
  needsPercentage: number;
  wantsPercentage: number;
  savingsPercentage: number;
  needsTarget: number;
  wantsTarget: number;
  savingsTarget: number;
  needsStatus: 'under_target' | 'on_target' | 'over_target';
  wantsStatus: 'under_target' | 'on_target' | 'over_target';
  savingsStatus: 'under_target' | 'on_target' | 'over_target';
  overallStatus: 'healthy' | 'needs_adjustment' | 'needs_major_adjustment';
} => {
  if (totalExpenses <= 0) {
    return {
      needsRatio: 0,
      wantsRatio: 0,
      savingsRatio: 0,
      needsPercentage: 0,
      wantsPercentage: 0,
      savingsPercentage: 0,
      needsTarget: 0,
      wantsTarget: 0,
      savingsTarget: 0,
      needsStatus: 'on_target',
      wantsStatus: 'on_target',
      savingsStatus: 'on_target',
      overallStatus: 'healthy',
    };
  }

  // Calculate target amounts
  const needsTarget = totalExpenses * BUDGET_RULE.NEEDS_TARGET;
  const wantsTarget = totalExpenses * BUDGET_RULE.WANTS_TARGET;
  const savingsTarget = totalExpenses * BUDGET_RULE.SAVINGS_TARGET;

  // Calculate actual percentages
  const needsPercentage = (essentialExpenses / totalExpenses) * 100;
  const wantsPercentage = (discretionaryExpenses / totalExpenses) * 100;
  const savingsPercentage = (savingsAmount / totalExpenses) * 100;

  // Calculate ratios (actual / target)
  const needsRatio = essentialExpenses / needsTarget;
  const wantsRatio = discretionaryExpenses / wantsTarget;
  const savingsRatio = savingsAmount / savingsTarget;

  // Determine status for each category
  const needsStatus =
    needsRatio <= 1.05 ? 'on_target' : needsRatio <= 1.2 ? 'over_target' : 'over_target';

  const wantsStatus =
    wantsRatio <= 1.05 ? 'on_target' : wantsRatio <= 1.2 ? 'over_target' : 'over_target';

  const savingsStatus =
    savingsRatio >= 0.95 ? 'on_target' : savingsRatio >= 0.8 ? 'under_target' : 'under_target';

  // Determine overall budget health
  const overallStatus =
    needsStatus === 'on_target' && wantsStatus === 'on_target' && savingsStatus === 'on_target'
      ? 'healthy'
      : needsStatus === 'over_target' ||
          wantsStatus === 'over_target' ||
          savingsStatus === 'under_target'
        ? 'needs_major_adjustment'
        : 'needs_adjustment';

  return {
    needsRatio,
    wantsRatio,
    savingsRatio,
    needsPercentage,
    wantsPercentage,
    savingsPercentage,
    needsTarget,
    wantsTarget,
    savingsTarget,
    needsStatus,
    wantsStatus,
    savingsStatus,
    overallStatus,
  };
};

/**
 * Apply inflation to expenses
 *
 * @param amount - Expense amount
 * @param years - Number of years to project
 * @param inflationRate - Annual inflation rate (decimal)
 * @returns Inflation-adjusted expense amount
 */
export const applyInflation = (
  amount: number,
  years: number,
  inflationRate: number = 0.025 // Default 2.5% inflation
): number => {
  if (isNaN(amount) || isNaN(years) || isNaN(inflationRate)) return amount;

  return amount * Math.pow(1 + inflationRate, years);
};
