/**
 * Financial Health Calculator Utility
 *
 * This utility provides functions for calculating financial health scores
 * and related metrics with transparent methodology.
 */

/**
 * Financial health category weights
 */
export const FINANCIAL_HEALTH_WEIGHTS = {
  CASH_FLOW: 0.2, // 20% - Cash flow and savings rate
  DEBT_MANAGEMENT: 0.2, // 20% - Debt-to-income ratio and debt structure
  EMERGENCY_FUND: 0.15, // 15% - Emergency fund adequacy
  NET_WORTH: 0.15, // 15% - Net worth relative to income
  RETIREMENT: 0.15, // 15% - Retirement readiness
  PROTECTION: 0.1, // 10% - Insurance coverage
  ESTATE_PLANNING: 0.05, // 5%  - Estate planning documents
};

/**
 * Financial health score category
 */
export interface FinancialHealthCategory {
  id: string;
  name: string;
  score: number;
  weight: number;
  weightedScore: number;
  metrics: FinancialHealthMetric[];
}

/**
 * Financial health metric
 */
export interface FinancialHealthMetric {
  id: string;
  name: string;
  value: number | string;
  score: number;
  target: string;
  description: string;
}

/**
 * Financial health score result
 */
export interface FinancialHealthScoreResult {
  overallScore: number;
  categories: FinancialHealthCategory[];
  status: string;
  strengths: string[];
  weaknesses: string[];
  recommendations: string[];
}

/**
 * Calculate cash flow health score (0-100)
 *
 * @param monthlyCashFlow - Monthly cash flow amount (income - expenses)
 * @param monthlyIncome - Monthly income amount
 * @returns Cash flow health score (0-100)
 */
export const calculateCashFlowHealthScore = (
  monthlyCashFlow: number,
  monthlyIncome: number = 0
): number => {
  // Handle edge cases
  if (isNaN(monthlyCashFlow)) monthlyCashFlow = 0;
  if (isNaN(monthlyIncome)) monthlyIncome = 0;

  // If there's no income but positive cash flow (edge case in imported data)
  // Return a default moderate score
  if (monthlyIncome <= 0 && monthlyCashFlow > 0) {
    return 50; // Default moderate score
  }

  // Calculate savings rate if income is provided
  const savingsRate = monthlyIncome > 0 ? (monthlyCashFlow / monthlyIncome) * 100 : 0;

  // Savings rate score (0-50 points)
  let savingsRateScore = 0;
  if (savingsRate >= 20) savingsRateScore = 50;
  else if (savingsRate >= 15) savingsRateScore = 40;
  else if (savingsRate >= 10) savingsRateScore = 30;
  else if (savingsRate >= 5) savingsRateScore = 20;
  else if (savingsRate > 0) savingsRateScore = 10;

  // Cash flow score (0-50 points)
  let cashFlowScore = 0;
  if (monthlyCashFlow > 0) {
    const cashFlowRatio = monthlyIncome > 0 ? (monthlyCashFlow / monthlyIncome) * 100 : 0;

    if (cashFlowRatio >= 20) cashFlowScore = 50;
    else if (cashFlowRatio >= 15) cashFlowScore = 40;
    else if (cashFlowRatio >= 10) cashFlowScore = 30;
    else if (cashFlowRatio >= 5) cashFlowScore = 20;
    else cashFlowScore = 10;
  }

  // Calculate total score
  return savingsRateScore + cashFlowScore;
};

/**
 * Calculate debt management score (0-100)
 *
 * @param totalLiabilities - Total liabilities amount
 * @param annualIncome - Annual income amount
 * @param debtToIncomeRatio - Debt-to-income ratio as a percentage (optional)
 * @returns Debt management score (0-100)
 */
export const calculateDebtManagementScore = (
  totalLiabilities: number,
  annualIncome: number,
  debtToIncomeRatio?: number
): number => {
  // Calculate debt-to-income ratio if not provided
  const dti =
    debtToIncomeRatio !== undefined
      ? debtToIncomeRatio
      : annualIncome > 0
        ? (totalLiabilities / annualIncome) * 100
        : 100;

  // Debt-to-income ratio score (0-60 points)
  let dtiScore = 0;
  if (dti <= 20) dtiScore = 60;
  else if (dti <= 30) dtiScore = 50;
  else if (dti <= 36) dtiScore = 40;
  else if (dti <= 43) dtiScore = 30;
  else if (dti <= 50) dtiScore = 20;
  else dtiScore = 10;

  // Debt amount score (0-40 points)
  let debtAmountScore = 0;
  const debtToIncomeMultiple = annualIncome > 0 ? totalLiabilities / annualIncome : 10;

  if (debtToIncomeMultiple <= 0.5) debtAmountScore = 40;
  else if (debtToIncomeMultiple <= 1) debtAmountScore = 35;
  else if (debtToIncomeMultiple <= 1.5) debtAmountScore = 30;
  else if (debtToIncomeMultiple <= 2) debtAmountScore = 25;
  else if (debtToIncomeMultiple <= 2.5) debtAmountScore = 20;
  else if (debtToIncomeMultiple <= 3) debtAmountScore = 15;
  else if (debtToIncomeMultiple <= 4) debtAmountScore = 10;
  else debtAmountScore = 5;

  // Calculate total score
  return dtiScore + debtAmountScore;
};

/**
 * Calculate emergency fund score (0-100)
 *
 * @param cashSavings - Cash savings amount (checking + savings) or months of expenses covered
 * @param monthlyExpenses - Monthly expenses amount (optional if cashSavings is already in months)
 * @param hasStableIncome - Whether the person has stable income (optional)
 * @returns Emergency fund score (0-100)
 */
export const calculateEmergencyFundScore = (
  cashSavings: number,
  monthlyExpenses?: number,
  hasStableIncome: boolean = true
): number => {
  // Calculate months of expenses covered if monthlyExpenses is provided
  const emergencyFundMonths =
    monthlyExpenses && monthlyExpenses > 0 ? cashSavings / monthlyExpenses : cashSavings; // If monthlyExpenses not provided, assume cashSavings is already in months

  // Adjust target based on income stability
  const targetMonths = hasStableIncome ? 3 : 6;

  // Emergency fund coverage score (0-100 points)
  let coverageScore = 0;
  if (emergencyFundMonths >= 2 * targetMonths) coverageScore = 100;
  else if (emergencyFundMonths >= 1.5 * targetMonths) coverageScore = 90;
  else if (emergencyFundMonths >= targetMonths) coverageScore = 80;
  else if (emergencyFundMonths >= 0.75 * targetMonths) coverageScore = 70;
  else if (emergencyFundMonths >= 0.5 * targetMonths) coverageScore = 50;
  else if (emergencyFundMonths >= 0.25 * targetMonths) coverageScore = 30;
  else if (emergencyFundMonths > 0) coverageScore = 15;
  else coverageScore = 0;

  return coverageScore;
};

/**
 * Calculate net worth score (0-100)
 *
 * @param netWorth - Net worth amount
 * @param annualIncome - Annual income amount
 * @param age - Age of the person (optional)
 * @returns Net worth score (0-100)
 */
export const calculateNetWorthScore = (
  netWorth: number,
  annualIncome: number,
  age: number = 35
): number => {
  // Handle edge cases
  if (isNaN(netWorth)) netWorth = 0;
  if (isNaN(annualIncome)) annualIncome = 0;
  if (isNaN(age)) age = 35;

  // If there's no income but positive net worth (edge case in imported data)
  // Return a score based on the absolute net worth
  if (annualIncome <= 0 && netWorth > 0) {
    // Base score on absolute net worth only
    if (netWorth >= 500000) return 80;
    if (netWorth >= 250000) return 70;
    if (netWorth >= 100000) return 60;
    if (netWorth >= 50000) return 50;
    if (netWorth >= 10000) return 40;
    return 30;
  }

  // Net worth to income ratio score (0-60 points)
  const netWorthToIncomeRatio = annualIncome > 0 ? netWorth / annualIncome : 0;
  let ratioScore = 0;

  if (netWorthToIncomeRatio >= 5) ratioScore = 60;
  else if (netWorthToIncomeRatio >= 3) ratioScore = 50;
  else if (netWorthToIncomeRatio >= 2) ratioScore = 40;
  else if (netWorthToIncomeRatio >= 1) ratioScore = 30;
  else if (netWorthToIncomeRatio >= 0.5) ratioScore = 20;
  else if (netWorthToIncomeRatio > 0) ratioScore = 10;

  // Net worth positivity score (0-40 points)
  let positivityScore = 0;

  if (netWorth >= 100000) positivityScore = 40;
  else if (netWorth >= 50000) positivityScore = 30;
  else if (netWorth >= 10000) positivityScore = 20;
  else if (netWorth >= 0) positivityScore = 10;

  // Calculate total score
  return ratioScore + positivityScore;
};

/**
 * Calculate overall financial health score (simplified version)
 *
 * @param cashFlowScore - Cash flow health score (0-100)
 * @param debtScore - Debt management score (0-100)
 * @param emergencyFundScore - Emergency fund score (0-100)
 * @param netWorthScore - Net worth score (0-100)
 * @returns Overall financial health score (0-100)
 */
export const calculateSimpleFinancialHealthScore = (
  cashFlowScore: number,
  debtScore: number,
  emergencyFundScore: number,
  netWorthScore: number
): number => {
  // Define weights for each category
  const cashFlowWeight = 0.3; // 30%
  const debtWeight = 0.25; // 25%
  const emergencyFundWeight = 0.25; // 25%
  const netWorthWeight = 0.2; // 20%

  // Calculate weighted score
  const weightedScore =
    cashFlowScore * cashFlowWeight +
    debtScore * debtWeight +
    emergencyFundScore * emergencyFundWeight +
    netWorthScore * netWorthWeight;

  return weightedScore;
};

/**
 * Calculate overall financial health score (detailed version)
 *
 * @param categories - Array of financial health categories
 * @returns Financial health score result
 */
export const calculateDetailedFinancialHealthScore = (
  categories: FinancialHealthCategory[]
): FinancialHealthScoreResult => {
  // Calculate overall score with safeguards for NaN values
  const overallScore = Math.round(
    categories.reduce((sum, category) => {
      // Handle NaN or undefined weightedScore
      const weightedScore = category.weightedScore || 0;
      return isNaN(weightedScore) ? sum : sum + weightedScore;
    }, 0)
  );

  // Final safeguard against NaN
  const finalScore = isNaN(overallScore) ? 0 : overallScore;

  // Determine financial health status
  let status = '';
  if (finalScore >= 80) status = 'Excellent';
  else if (finalScore >= 70) status = 'Very Good';
  else if (finalScore >= 60) status = 'Good';
  else if (finalScore >= 50) status = 'Fair';
  else if (finalScore >= 40) status = 'Needs Attention';
  else status = 'Critical';

  // Identify strengths and weaknesses
  const strengths: string[] = [];
  const weaknesses: string[] = [];
  const recommendations: string[] = [];

  categories.forEach((category) => {
    if (category.score >= 80) {
      strengths.push(`Strong ${category.name.toLowerCase()}`);
    } else if (category.score <= 50) {
      weaknesses.push(`Weak ${category.name.toLowerCase()}`);

      // Add recommendations based on category
      switch (category.id) {
        case 'cash_flow':
          recommendations.push('Review your budget to reduce expenses and increase savings rate');
          break;
        case 'debt_management':
          recommendations.push(
            'Focus on paying down high-interest debt and reducing debt-to-income ratio'
          );
          break;
        case 'emergency_fund':
          recommendations.push('Build emergency fund to cover 3-6 months of expenses');
          break;
        case 'net_worth':
          recommendations.push('Increase savings and investments to build net worth');
          break;
        case 'retirement':
          recommendations.push('Increase retirement contributions and review investment strategy');
          break;
        case 'protection':
          recommendations.push('Review insurance coverage to ensure adequate protection');
          break;
        case 'estate_planning':
          recommendations.push('Create or update essential estate planning documents');
          break;
      }
    }
  });

  return {
    overallScore: finalScore,
    categories,
    status,
    strengths,
    weaknesses,
    recommendations,
  };
};

/**
 * Calculate protection planning score based on insurance coverage and emergency fund
 */
export const calculateProtectionPlanningScore = (data: {
  insuranceCoverage: any;
  emergencyFund: any;
  monthlyExpenses: number;
  annualIncome: number;
  calculatedTotalMonthlyIncome: number;
}): number => {
  let score = 0;

  // Life insurance coverage (0-30 points)
  const lifeInsurance = data.insuranceCoverage?.lifeInsurance;
  const lifeInsuranceNeeded = data.annualIncome * 10; // Example: 10x annual income
  const currentLifeCoverage = parseFloat(lifeInsurance?.coverageAmount || '0');
  const lifeInsuranceScore = Math.min(30, (currentLifeCoverage / lifeInsuranceNeeded) * 30);
  score += lifeInsuranceScore;

  // Disability insurance (0-20 points)
  const disabilityInsurance = data.insuranceCoverage?.disabilityInsurance;
  const disabilityInsuranceNeeded = data.calculatedTotalMonthlyIncome * 0.6; // 60% of monthly income
  const currentDisabilityCoverage = parseFloat(disabilityInsurance?.coverageAmount || '0');
  const disabilityInsuranceScore = Math.min(20, (currentDisabilityCoverage / disabilityInsuranceNeeded) * 20);
  score += disabilityInsuranceScore;

  // Emergency fund (0-30 points)
  const currentEmergencyFund = parseFloat(data.emergencyFund?.currentEmergencyFund || '0');
  const recommendedEmergencyFund = data.monthlyExpenses * 3; // 3 months of expenses
  const emergencyFundScore = Math.min(30, (currentEmergencyFund / recommendedEmergencyFund) * 30);
  score += emergencyFundScore;

  // Health insurance (0-20 points)
  const hasHealthInsurance = data.insuranceCoverage?.healthInsurance?.hasInsurance === true;
  score += hasHealthInsurance ? 20 : 0;

  return Math.min(100, score);
};

/**
 * Calculate estate planning score based on estate documents and legacy planning
 */
export const calculateEstatePlanningScore = (data: {
  estatePlanning: any;
  estateDocuments: any;
  legacyPlanning: any;
}): number => {
  let score = 0;

  // Essential documents (0-40 points)
  const hasWill = data.estatePlanning?.hasWill === true || data.estateDocuments?.will?.exists === true;
  const hasTrust = data.estatePlanning?.hasTrust === true || data.estateDocuments?.trust?.exists === true;
  const hasPowerOfAttorney = data.estatePlanning?.hasPowerOfAttorney === true || data.estateDocuments?.powerOfAttorney?.exists === true;
  const hasHealthcareDirective = data.estatePlanning?.hasHealthcareDirective === true || data.estateDocuments?.healthcareDirective?.exists === true;

  if (hasWill) score += 10;
  if (hasTrust) score += 10;
  if (hasPowerOfAttorney) score += 10;
  if (hasHealthcareDirective) score += 10;

  // Legacy planning (0-30 points)
  const hasLegacyStatement = data.legacyPlanning?.legacyStatement === true;
  const hasBeneficiaryDesignations = data.legacyPlanning?.beneficiaryDesignationsInPlace === true;
  const hasCharitableGiving = data.legacyPlanning?.hasCharitableStrategy === true;

  if (hasLegacyStatement) score += 10;
  if (hasBeneficiaryDesignations) score += 10;
  if (hasCharitableGiving) score += 10;

  // Estate strategy (0-30 points)
  const hasEstateStrategy = data.estatePlanning?.hasEstateStrategy === true;
  const hasSuccessionPlan = data.estatePlanning?.hasSuccessionPlan === true;
  const hasTaxStrategy = data.estatePlanning?.hasTaxStrategy === true;

  if (hasEstateStrategy) score += 10;
  if (hasSuccessionPlan) score += 10;
  if (hasTaxStrategy) score += 10;

  return Math.min(100, score);
};
