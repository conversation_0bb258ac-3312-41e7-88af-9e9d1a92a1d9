import React, { createContext, useContext, useState, ReactNode } from 'react';

// Define theme types
export type ThemeMode = 'light' | 'dark';

// Simple theme structure
export interface SimpleTheme {
  mode: ThemeMode;
  colors: {
    background: string;
    text: string;
    primary: string;
    secondary: string;
  };
}

// Create light and dark themes
const lightTheme: SimpleTheme = {
  mode: 'light',
  colors: {
    background: '#FAFAFA',
    text: '#212121',
    primary: '#4CAF50',
    secondary: '#8BC34A',
  },
};

const darkTheme: SimpleTheme = {
  mode: 'dark',
  colors: {
    background: '#121212',
    text: '#FFFFFF',
    primary: '#81C784',
    secondary: '#AED581',
  },
};

// Create theme context
interface ThemeContextValue {
  theme: SimpleTheme;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextValue | undefined>(undefined);

// Theme provider props
interface SimpleThemeProviderProps {
  children: ReactNode;
  initialMode?: ThemeMode;
}

// Simple Theme Provider component
export function SimpleThemeProviderMinimal({
  children,
  initialMode = 'light',
}: SimpleThemeProviderProps) {
  const [mode, setMode] = useState<ThemeMode>(initialMode);

  // Get current theme based on mode
  const theme = mode === 'light' ? lightTheme : darkTheme;

  // Toggle theme function
  const toggleTheme = () => {
    setMode((prevMode) => (prevMode === 'light' ? 'dark' : 'light'));
  };

  // Context value
  const contextValue: ThemeContextValue = {
    theme,
    toggleTheme,
  };

  return <ThemeContext.Provider value={contextValue}>{children}</ThemeContext.Provider>;
}

// Hook to use theme
export function useThemeMinimal(): ThemeContextValue {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useThemeMinimal must be used within a SimpleThemeProviderMinimal');
  }
  return context;
}
