import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { ThemeProvider as StyledThemeProvider } from 'styled-components';

// Define theme types
export type ThemeMode = 'light' | 'dark';
export type Season = 'spring' | 'summer' | 'autumn' | 'winter';

// Simple theme structure that works with styled-components
export interface SimpleTheme {
  mode: ThemeMode;
  season: Season;
  colors: {
    primary: {
      main: string;
      light: string;
      dark: string;
      contrastText: string;
    };
    secondary: {
      main: string;
      light: string;
      dark: string;
      contrastText: string;
    };
    text: {
      primary: string;
      secondary: string;
      disabled: string;
      hint: string;
      onPrimary?: string;
      tertiary?: string;
      placeholder?: string;
    };
    background: {
      default: string;
      paper: string;
      elevated: string;
      secondary?: string;
      tertiary?: string;
      main?: string;
      highlight?: string;
      hover?: string;
      input?: string;
    };
    divider: string;
    border: {
      main: string;
      light?: string;
      dark?: string;
    };
    error: {
      main: string;
      light: string;
      dark: string;
      contrastText: string;
    };
    warning: {
      main: string;
      light: string;
      dark: string;
      contrastText: string;
    };
    info: {
      main: string;
      light: string;
      dark: string;
      contrastText: string;
    };
    success: {
      main: string;
      light: string;
      dark: string;
      contrastText: string;
    };
    action: {
      active: string;
      hover: string;
      selected: string;
      disabled: string;
      disabledBackground: string;
      focus: string;
    };
    neutral: {
      main: string;
      light: string;
      dark: string;
      contrastText: string;
      background: string;
      surface: string;
      primaryText: string;
      secondaryText: string;
      divider: string;
      card: string;
    };
    seasons: {
      spring: {
        primary: string;
        secondary: string;
        accent: string;
        background: { light: string; dark: string };
      };
      summer: {
        primary: string;
        secondary: string;
        accent: string;
        background: { light: string; dark: string };
      };
      autumn: {
        primary: string;
        secondary: string;
        accent: string;
        background: { light: string; dark: string };
      };
      winter: {
        primary: string;
        secondary: string;
        accent: string;
        background: { light: string; dark: string };
      };
    };
  };
  typography: {
    fontFamily: {
      primary: string;
      secondary: string;
      tertiary: string;
      mono: string;
    };
    fontSize: {
      display: string;
      h1: string;
      h2: string;
      h3: string;
      h4: string;
      h5: string;
      heading2: string;
      heading3: string;
      bodyLarge: string;
      body: string;
      bodySmall: string;
      caption: string;
    };
    fontWeight: {
      light: number;
      regular: number;
      medium: number;
      bold: number;
    };
    lineHeight: {
      tight: number;
      normal: number;
      relaxed: number;
    };
  };
  spacing: (multiplier: number) => string;
  borderRadius: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    full: string;
    small: string;
    medium: string;
    large: string;
    circular: string;
  };
  shadows: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    subtle: string;
    medium: string;
    strong: string;
    dramatic: string;
    '2xl': string;
    inner: string;
    none: string;
    large: string;
    [key: number]: string;
  };
  transitions: {
    fast: string;
    normal: string;
    slow: string;
    easing: {
      easeInOut: string;
      easeOut: string;
      easeIn: string;
      sharp: string;
    };
    duration: {
      shortest: number;
      shorter: number;
      short: number;
      standard: number;
      complex: number;
      enteringScreen: number;
      leavingScreen: number;
    };
  };
  zIndex: {
    mobileStepper: number;
    appBar: number;
    drawer: number;
    modal: number;
    snackbar: number;
    tooltip: number;
  };
  shape: {
    borderRadius: number;
  };
  seasonal: {
    spring: {
      primary: string;
      secondary: string;
      background: string;
      surface: string;
      text: string;
      accent: string;
    };
    summer: {
      primary: string;
      secondary: string;
      background: string;
      surface: string;
      text: string;
      accent: string;
    };
    autumn: {
      primary: string;
      secondary: string;
      background: string;
      surface: string;
      text: string;
      accent: string;
    };
    winter: {
      primary: string;
      secondary: string;
      background: string;
      surface: string;
      text: string;
      accent: string;
    };
  };
}

// Seasonal color palettes
const seasonalColors = {
  spring: {
    primary: '#4CAF50',
    secondary: '#8BC34A',
    accent: '#7CB342',
    background: {
      light: '#F1F8E9',
      dark: '#2E3B24',
    },
  },
  summer: {
    primary: '#03A9F4',
    secondary: '#00BCD4',
    accent: '#039BE5',
    background: {
      light: '#E1F5FE',
      dark: '#1A3C4D',
    },
  },
  autumn: {
    primary: '#FF9800',
    secondary: '#FF5722',
    accent: '#F57C00',
    background: {
      light: '#FFF3E0',
      dark: '#4D3319',
    },
  },
  winter: {
    primary: '#9C27B0',
    secondary: '#673AB7',
    accent: '#8E24AA',
    background: {
      light: '#F3E5F5',
      dark: '#3A2A3D',
    },
  },
};

// Base colors for light and dark modes
const baseColors = {
  light: {
    text: {
      primary: '#212121',
      secondary: '#757575',
      disabled: '#9E9E9E',
      hint: '#9E9E9E',
    },
    background: {
      default: '#FAFAFA',
      paper: '#FFFFFF',
      elevated: '#F5F5F5',
    },
    divider: '#E0E0E0',
    error: '#D32F2F',
    warning: '#ED6C02',
    info: '#0288D1',
    success: '#2E7D32',
  },
  dark: {
    text: {
      primary: '#FFFFFF',
      secondary: '#B0BEC5',
      disabled: '#78909C',
      hint: '#78909C',
    },
    background: {
      default: '#121212',
      paper: '#1E1E1E',
      elevated: '#2C2C2C',
    },
    divider: '#2E2E2E',
    error: '#F44336',
    warning: '#FFA726',
    info: '#29B6F6',
    success: '#66BB6A',
  },
};

// Create a simple theme based on mode and season
function createSimpleTheme(mode: ThemeMode, season: Season): SimpleTheme {
  const colors = baseColors[mode];
  const seasonal = seasonalColors[season];

  return {
    mode,
    season,
    colors: {
      primary: {
        main: seasonal.primary,
        light: seasonal.primary + '33',
        dark: seasonal.primary + '99',
        contrastText: '#FFFFFF',
      },
      secondary: {
        main: seasonal.secondary,
        light: seasonal.secondary + '33',
        dark: seasonal.secondary + '99',
        contrastText: '#FFFFFF',
      },
      text: {
        ...colors.text,
        onPrimary: '#FFFFFF',
        tertiary: mode === 'light' ? '#757575' : '#9E9E9E',
        placeholder: mode === 'light' ? '#BDBDBD' : '#757575',
      },
      background: {
        ...colors.background,
        secondary: mode === 'light' ? '#F5F5F5' : '#2C2C2C',
        tertiary: mode === 'light' ? '#EEEEEE' : '#333333',
        main: mode === 'light' ? '#FFFFFF' : '#1E1E1E',
        highlight: mode === 'light' ? '#F5F5F5' : '#333333',
        hover: mode === 'light' ? '#F5F5F5' : '#333333',
        input: mode === 'light' ? '#FFFFFF' : '#2C2C2C',
      },
      divider: colors.divider,
      border: {
        main: colors.divider,
        light: mode === 'light' ? '#EEEEEE' : '#333333',
        dark: mode === 'light' ? '#BDBDBD' : '#616161',
      },
      error: {
        main: colors.error,
        light: colors.error + '33',
        dark: colors.error + '99',
        contrastText: '#FFFFFF',
      },
      warning: {
        main: colors.warning,
        light: colors.warning + '33',
        dark: colors.warning + '99',
        contrastText: '#FFFFFF',
      },
      info: {
        main: colors.info,
        light: colors.info + '33',
        dark: colors.info + '99',
        contrastText: '#FFFFFF',
      },
      success: {
        main: colors.success,
        light: colors.success + '33',
        dark: colors.success + '99',
        contrastText: '#FFFFFF',
      },
      action: {
        active: mode === 'light' ? 'rgba(0, 0, 0, 0.54)' : 'rgba(255, 255, 255, 0.7)',
        hover: mode === 'light' ? 'rgba(0, 0, 0, 0.04)' : 'rgba(255, 255, 255, 0.08)',
        selected: mode === 'light' ? 'rgba(0, 0, 0, 0.08)' : 'rgba(255, 255, 255, 0.16)',
        disabled: mode === 'light' ? 'rgba(0, 0, 0, 0.26)' : 'rgba(255, 255, 255, 0.3)',
        disabledBackground: mode === 'light' ? 'rgba(0, 0, 0, 0.12)' : 'rgba(255, 255, 255, 0.12)',
        focus: mode === 'light' ? 'rgba(0, 0, 0, 0.12)' : 'rgba(255, 255, 255, 0.12)',
      },
      neutral: {
        main: mode === 'light' ? '#757575' : '#9E9E9E',
        light: mode === 'light' ? '#BDBDBD' : '#757575',
        dark: mode === 'light' ? '#616161' : '#BDBDBD',
        contrastText: mode === 'light' ? '#FFFFFF' : '#000000',
        background: mode === 'light' ? '#F5F5F5' : '#2C2C2C',
        surface: mode === 'light' ? '#FFFFFF' : '#1E1E1E',
        primaryText: mode === 'light' ? '#212121' : '#FFFFFF',
        secondaryText: mode === 'light' ? '#757575' : '#B0BEC5',
        divider: mode === 'light' ? '#E0E0E0' : '#2E2E2E',
        card: mode === 'light' ? '#FFFFFF' : '#1E1E1E',
      },
      seasons: {
        spring: {
          primary: seasonalColors.spring.primary,
          secondary: seasonalColors.spring.secondary,
          accent: seasonalColors.spring.accent,
          background: seasonalColors.spring.background,
        },
        summer: {
          primary: seasonalColors.summer.primary,
          secondary: seasonalColors.summer.secondary,
          accent: seasonalColors.summer.accent,
          background: seasonalColors.summer.background,
        },
        autumn: {
          primary: seasonalColors.autumn.primary,
          secondary: seasonalColors.autumn.secondary,
          accent: seasonalColors.autumn.accent,
          background: seasonalColors.autumn.background,
        },
        winter: {
          primary: seasonalColors.winter.primary,
          secondary: seasonalColors.winter.secondary,
          accent: seasonalColors.winter.accent,
          background: seasonalColors.winter.background,
        },
      },
    },
    typography: {
      fontFamily: {
        primary:
          "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif",
        secondary:
          "'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif",
        tertiary: "'Roboto Mono', monospace",
        mono: "'Roboto Mono', monospace",
      },
      fontSize: {
        display: '3rem',
        h1: '2.5rem',
        h2: '2rem',
        h3: '1.75rem',
        h4: '1.5rem',
        h5: '1.25rem',
        heading2: '1.5rem',
        heading3: '1.25rem',
        bodyLarge: '1.125rem',
        body: '1rem',
        bodySmall: '0.875rem',
        caption: '0.75rem',
      },
      fontWeight: {
        light: 300,
        regular: 400,
        medium: 500,
        bold: 700,
      },
      lineHeight: {
        tight: 1.25,
        normal: 1.5,
        relaxed: 1.75,
      },
    },
    spacing: (multiplier: number) => `${0.25 * multiplier}rem`,
    borderRadius: {
      xs: '0.125rem',
      sm: '0.25rem',
      md: '0.5rem',
      lg: '1rem',
      xl: '2rem',
      full: '9999px',
      small: '0.25rem',
      medium: '0.5rem',
      large: '1rem',
      circular: '9999px',
    },
    shadows: {
      xs: mode === 'dark' ? '0 1px 3px rgba(0, 0, 0, 0.5)' : '0 1px 2px rgba(0, 0, 0, 0.05)',
      sm:
        mode === 'dark'
          ? '0 2px 6px rgba(0, 0, 0, 0.5)'
          : '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.08)',
      md:
        mode === 'dark'
          ? '0 3px 10px rgba(0, 0, 0, 0.5)'
          : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      lg:
        mode === 'dark'
          ? '0 5px 15px rgba(0, 0, 0, 0.5)'
          : '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      xl:
        mode === 'dark'
          ? '0 8px 30px rgba(0, 0, 0, 0.5)'
          : '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
      subtle: mode === 'dark' ? '0 1px 3px rgba(0, 0, 0, 0.3)' : '0 1px 2px rgba(0, 0, 0, 0.03)',
      medium: mode === 'dark' ? '0 3px 8px rgba(0, 0, 0, 0.4)' : '0 3px 6px rgba(0, 0, 0, 0.08)',
      strong: mode === 'dark' ? '0 5px 12px rgba(0, 0, 0, 0.5)' : '0 6px 12px rgba(0, 0, 0, 0.12)',
      dramatic:
        mode === 'dark' ? '0 8px 20px rgba(0, 0, 0, 0.6)' : '0 12px 24px rgba(0, 0, 0, 0.18)',
      '2xl':
        mode === 'dark'
          ? '0 10px 40px rgba(0, 0, 0, 0.6)'
          : '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
      inner:
        mode === 'dark'
          ? 'inset 0 2px 4px rgba(0, 0, 0, 0.5)'
          : 'inset 0 2px 4px rgba(0, 0, 0, 0.06)',
      none: 'none',
      large: mode === 'dark' ? '0 6px 18px rgba(0, 0, 0, 0.5)' : '0 12px 18px rgba(0, 0, 0, 0.15)',
      0: 'none',
      1: mode === 'dark' ? '0 1px 2px rgba(0, 0, 0, 0.4)' : '0 1px 2px rgba(0, 0, 0, 0.04)',
      2: mode === 'dark' ? '0 2px 4px rgba(0, 0, 0, 0.45)' : '0 2px 4px rgba(0, 0, 0, 0.06)',
      3: mode === 'dark' ? '0 3px 6px rgba(0, 0, 0, 0.5)' : '0 3px 6px rgba(0, 0, 0, 0.08)',
      4: mode === 'dark' ? '0 4px 8px rgba(0, 0, 0, 0.55)' : '0 4px 8px rgba(0, 0, 0, 0.1)',
      5: mode === 'dark' ? '0 6px 12px rgba(0, 0, 0, 0.6)' : '0 6px 12px rgba(0, 0, 0, 0.12)',
    },
    transitions: {
      fast: '150ms',
      normal: '300ms',
      slow: '500ms',
      easing: {
        easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
        easeOut: 'cubic-bezier(0.0, 0, 0.2, 1)',
        easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
        sharp: 'cubic-bezier(0.4, 0, 0.6, 1)',
      },
      duration: {
        shortest: 150,
        shorter: 200,
        short: 250,
        standard: 300,
        complex: 375,
        enteringScreen: 225,
        leavingScreen: 195,
      },
    },
    zIndex: {
      mobileStepper: 1000,
      appBar: 1100,
      drawer: 1200,
      modal: 1300,
      snackbar: 1400,
      tooltip: 1500,
    },
    shape: {
      borderRadius: 4,
    },
    seasonal: {
      spring: {
        primary: seasonalColors.spring.primary,
        secondary: seasonalColors.spring.secondary,
        background:
          mode === 'light'
            ? seasonalColors.spring.background.light
            : seasonalColors.spring.background.dark,
        surface: mode === 'light' ? '#FFFFFF' : '#1E1E1E',
        text: mode === 'light' ? '#212121' : '#FFFFFF',
        accent: seasonalColors.spring.accent,
      },
      summer: {
        primary: seasonalColors.summer.primary,
        secondary: seasonalColors.summer.secondary,
        background:
          mode === 'light'
            ? seasonalColors.summer.background.light
            : seasonalColors.summer.background.dark,
        surface: mode === 'light' ? '#FFFFFF' : '#1E1E1E',
        text: mode === 'light' ? '#212121' : '#FFFFFF',
        accent: seasonalColors.summer.accent,
      },
      autumn: {
        primary: seasonalColors.autumn.primary,
        secondary: seasonalColors.autumn.secondary,
        background:
          mode === 'light'
            ? seasonalColors.autumn.background.light
            : seasonalColors.autumn.background.dark,
        surface: mode === 'light' ? '#FFFFFF' : '#1E1E1E',
        text: mode === 'light' ? '#212121' : '#FFFFFF',
        accent: seasonalColors.autumn.accent,
      },
      winter: {
        primary: seasonalColors.winter.primary,
        secondary: seasonalColors.winter.secondary,
        background:
          mode === 'light'
            ? seasonalColors.winter.background.light
            : seasonalColors.winter.background.dark,
        surface: mode === 'light' ? '#FFFFFF' : '#1E1E1E',
        text: mode === 'light' ? '#212121' : '#FFFFFF',
        accent: seasonalColors.winter.accent,
      },
    },
  };
}

// Get initial theme from localStorage or system preference
function getInitialTheme(): { mode: ThemeMode; season: Season } {
  try {
    const storedMode = localStorage.getItem('themeMode') as ThemeMode;
    const storedSeason = localStorage.getItem('themeSeason') as Season;

    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const defaultMode = prefersDark ? 'dark' : 'light';

    // Get current month to determine default season
    const month = new Date().getMonth();
    let defaultSeason: Season = 'spring';

    if (month >= 2 && month <= 4) defaultSeason = 'spring';
    else if (month >= 5 && month <= 7) defaultSeason = 'summer';
    else if (month >= 8 && month <= 10) defaultSeason = 'autumn';
    else defaultSeason = 'winter';

    return {
      mode: storedMode || defaultMode,
      season: storedSeason || defaultSeason,
    };
  } catch (error) {
    console.error('Error accessing localStorage:', error);
    return { mode: 'light', season: 'spring' };
  }
}

// Create theme context
interface ThemeContextValue {
  theme: SimpleTheme;
  setMode: (mode: ThemeMode) => void;
  setSeason: (season: Season) => void;
}

const ThemeContext = createContext<ThemeContextValue | undefined>(undefined);

// Theme provider props
interface SimpleThemeProviderProps {
  children: ReactNode;
  initialMode?: ThemeMode;
  initialSeason?: Season;
}

// Simple Theme Provider component
export function SimpleThemeProvider({
  children,
  initialMode,
  initialSeason,
}: SimpleThemeProviderProps) {
  // Get initial theme values
  const initialTheme =
    typeof window !== 'undefined'
      ? getInitialTheme()
      : { mode: initialMode || 'light', season: initialSeason || 'spring' };

  const [mode, setMode] = useState<ThemeMode>(initialMode || initialTheme.mode);
  const [season, setSeason] = useState<Season>(initialSeason || initialTheme.season);

  // Create theme object
  const theme = createSimpleTheme(mode, season);

  // Update localStorage when theme changes
  useEffect(() => {
    try {
      localStorage.setItem('themeMode', mode);
      localStorage.setItem('themeSeason', season);
    } catch (error) {
      console.error('Error saving theme to localStorage:', error);
    }
  }, [mode, season]);

  // Context value
  const contextValue: ThemeContextValue = {
    theme,
    setMode,
    setSeason,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      <StyledThemeProvider theme={theme}>{children}</StyledThemeProvider>
    </ThemeContext.Provider>
  );
}

// Hook to use theme
export function useTheme(): ThemeContextValue {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a SimpleThemeProvider');
  }
  return context;
}

// Export default theme for reference
export const defaultTheme = createSimpleTheme('light', 'spring');
