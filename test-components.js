// Test script for LifeCompass components
console.log('Testing LifeCompass components...');

// Function to navigate to a specific component
async function navigateToComponent(componentName) {
  console.log(`Navigating to ${componentName}...`);

  // First, navigate to the main page
  if (window.location.pathname !== '/') {
    window.location.href = '/';
    await new Promise((resolve) => setTimeout(resolve, 1000));
  }

  // Click on Financial Compass
  const financialCompassButton = document.querySelector(
    'button[data-testid="financial-compass-button"]'
  );
  if (financialCompassButton) {
    financialCompassButton.click();
    await new Promise((resolve) => setTimeout(resolve, 500));
  }

  // Navigate based on component name
  switch (componentName) {
    case 'PersonalInformation':
      // Click on North direction
      const northButton = document.querySelector('button[data-testid="north-button"]');
      if (northButton) {
        northButton.click();
        await new Promise((resolve) => setTimeout(resolve, 500));
      }

      // Click on Personal Information
      const personalInfoButton = document.querySelector(
        'button[data-testid="personal-information-button"]'
      );
      if (personalInfoButton) {
        personalInfoButton.click();
        await new Promise((resolve) => setTimeout(resolve, 500));
      }
      break;

    case 'Assets':
      // Click on North direction
      const northAssetsButton = document.querySelector('button[data-testid="north-button"]');
      if (northAssetsButton) {
        northAssetsButton.click();
        await new Promise((resolve) => setTimeout(resolve, 500));
      }

      // Click on Assets
      const assetsButton = document.querySelector('button[data-testid="assets-button"]');
      if (assetsButton) {
        assetsButton.click();
        await new Promise((resolve) => setTimeout(resolve, 500));
      }
      break;

    case 'ExpenseDetails':
      // Click on North direction
      const northExpensesButton = document.querySelector('button[data-testid="north-button"]');
      if (northExpensesButton) {
        northExpensesButton.click();
        await new Promise((resolve) => setTimeout(resolve, 500));
      }

      // Click on Expenses
      const expensesButton = document.querySelector('button[data-testid="expenses-button"]');
      if (expensesButton) {
        expensesButton.click();
        await new Promise((resolve) => setTimeout(resolve, 500));
      }
      break;

    case 'IncomeDetails':
      // Click on North direction
      const northIncomeButton = document.querySelector('button[data-testid="north-button"]');
      if (northIncomeButton) {
        northIncomeButton.click();
        await new Promise((resolve) => setTimeout(resolve, 500));
      }

      // Click on Income
      const incomeButton = document.querySelector('button[data-testid="income-button"]');
      if (incomeButton) {
        incomeButton.click();
        await new Promise((resolve) => setTimeout(resolve, 500));
      }
      break;

    case 'RetirementGoals':
      // Click on East direction
      const eastButton = document.querySelector('button[data-testid="east-button"]');
      if (eastButton) {
        eastButton.click();
        await new Promise((resolve) => setTimeout(resolve, 500));
      }

      // Click on Retirement Goals
      const retirementButton = document.querySelector(
        'button[data-testid="retirement-goals-button"]'
      );
      if (retirementButton) {
        retirementButton.click();
        await new Promise((resolve) => setTimeout(resolve, 500));
      }
      break;
  }

  console.log(`Navigation to ${componentName} complete.`);
}

// Function to test the PersonalInformation component
async function testPersonalInformation() {
  await navigateToComponent('PersonalInformation');

  // Fill in some test data
  const firstNameInput = document.querySelector('#firstName');
  const lastNameInput = document.querySelector('#lastName');
  const dobInput = document.querySelector('#dateOfBirth');

  if (firstNameInput && lastNameInput && dobInput) {
    firstNameInput.value = 'Test';
    lastNameInput.value = 'User';
    dobInput.value = '1980-01-01';

    // Trigger change events
    firstNameInput.dispatchEvent(new Event('change', { bubbles: true }));
    lastNameInput.dispatchEvent(new Event('change', { bubbles: true }));
    dobInput.dispatchEvent(new Event('change', { bubbles: true }));

    // Check if age is calculated
    await new Promise((resolve) => setTimeout(resolve, 1000));
    console.log('PersonalInformation test complete.');
  } else {
    console.error('Could not find PersonalInformation form fields.');
  }
}

// Function to test the Assets component
async function testAssets() {
  await navigateToComponent('Assets');

  // Check if the component loaded
  const assetsTitle = document.querySelector('h2');
  if (assetsTitle && assetsTitle.textContent.includes('Assets')) {
    console.log('Assets component loaded successfully.');

    // Add some test data
    const inputs = document.querySelectorAll('input[type="number"]');
    if (inputs.length > 0) {
      inputs[0].value = '10000';
      inputs[0].dispatchEvent(new Event('change', { bubbles: true }));

      // Wait for auto-save
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Check if asset allocation analysis button appears
      const analysisButton = document.querySelector('button');
      if (analysisButton && analysisButton.textContent.includes('Asset Allocation')) {
        console.log('Asset allocation analysis button found.');
        analysisButton.click();
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Check if analysis is displayed
        const analysis = document.querySelector('div[data-testid="asset-allocation-analysis"]');
        if (analysis) {
          console.log('Asset allocation analysis displayed successfully.');
        } else {
          console.log('Asset allocation analysis not found.');
        }
      } else {
        console.log('Asset allocation analysis button not found.');
      }
    } else {
      console.error('Could not find asset input fields.');
    }
  } else {
    console.error('Assets component did not load correctly.');
  }

  console.log('Assets test complete.');
}

// Function to test the ExpenseDetails component
async function testExpenseDetails() {
  await navigateToComponent('ExpenseDetails');

  // Check if the component loaded
  const expensesTitle = document.querySelector('h2');
  if (expensesTitle && expensesTitle.textContent.includes('Expenses')) {
    console.log('ExpenseDetails component loaded successfully.');

    // Add some test data
    const inputs = document.querySelectorAll('input[type="number"]');
    if (inputs.length > 0) {
      inputs[0].value = '1000';
      inputs[0].dispatchEvent(new Event('change', { bubbles: true }));

      // Wait for auto-save
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Check if budget analysis button appears
      const analysisButton = document.querySelector('button');
      if (analysisButton && analysisButton.textContent.includes('Budget Analysis')) {
        console.log('Budget analysis button found.');
        analysisButton.click();
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Check if analysis is displayed
        const analysis = document.querySelector('div[data-testid="budget-analysis"]');
        if (analysis) {
          console.log('Budget analysis displayed successfully.');
        } else {
          console.log('Budget analysis not found.');
        }
      } else {
        console.log('Budget analysis button not found.');
      }
    } else {
      console.error('Could not find expense input fields.');
    }
  } else {
    console.error('ExpenseDetails component did not load correctly.');
  }

  console.log('ExpenseDetails test complete.');
}

// Run tests
async function runTests() {
  try {
    await testPersonalInformation();
    await testAssets();
    await testExpenseDetails();
    console.log('All tests completed.');
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run tests when the page is fully loaded
window.addEventListener('load', () => {
  setTimeout(runTests, 2000);
});
